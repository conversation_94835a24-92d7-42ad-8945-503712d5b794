<resources xmlns:tools="http://schemas.android.com/tools">

    <style name="AppTheme.NoActionBar" parent="Theme.AppCompat.Light">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <!--    横屏内容显示到状态栏    -->
        <item name="android:windowLayoutInDisplayCutoutMode" tools:targetApi="27">shortEdges</item>
    </style>

    <style name="SplashTheme" parent="AppTheme.NoActionBar">
        <item name="android:windowBackground">@drawable/splash_bg</item>
    </style>

    <style name="BlackWindowTheme" parent="AppTheme.NoActionBar">
        <item name="android:windowBackground">@color/black</item>
    </style>

    <!--    CPBaseDialog    -->
    <style name="Theme.NoTitle.Dialog" parent="android:style/Theme.Dialog">
        <item name="android:windowAnimationStyle">@style/anim_center_in_out</item>
        <!-- 设置是否透明 -->
        <item name="android:windowIsTranslucent">true</item>
        <!-- 无标题 -->
        <item name="android:windowNoTitle">true</item>
        <!-- 背景 -->
        <!--        <item name="android:background">@null</item>-->
        <!-- 窗口背景 -->
        <item name="android:windowBackground">@android:color/transparent</item>
        <!-- 是否变暗 -->
        <!--   <item name="android:backgroundDimEnabled">false</item>-->
    </style>

    <style name="anim_center_in_out">
        <item name="android:windowEnterAnimation">@anim/dialog_center_in</item>
        <item name="android:windowExitAnimation">@anim/dialog_center_out</item>
    </style>
</resources>