<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="20dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/iv_bg"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="45dp"
        android:background="@drawable/bg_buy_tip_dialog"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/iv_fore">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="80dp"
            android:gravity="center"
            android:maxWidth="286dp"
            android:text="@string/current_content_requires_purchase"
            android:textColor="@color/color_FFF0F0"
            android:textSize="22sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/iv_bg" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_choose"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:gravity="center"
            android:maxWidth="320dp"
            android:text="@string/choose_your_plan"
            android:textColor="@color/color_D8BAC5"
            android:textSize="16sp"
            app:layout_constraintEnd_toEndOf="@id/iv_bg"
            app:layout_constraintStart_toStartOf="@id/iv_bg"
            app:layout_constraintTop_toBottomOf="@id/tv_title" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_tip1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="50dp"
            android:layout_marginTop="19dp"
            android:drawablePadding="10dp"
            android:text="@string/unlimited_text_messages"
            android:textColor="@color/color_D8BAC5"
            android:textSize="14sp"
            app:drawableStartCompat="@drawable/ic_buy_tip_right"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_choose" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_tip2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="6dp"
            android:drawablePadding="10dp"
            android:text="@string/unlimited_voice_listening"
            android:textColor="@color/color_D8BAC5"
            android:textSize="14sp"
            app:drawableStartCompat="@drawable/ic_buy_tip_right"
            app:layout_constraintStart_toStartOf="@id/tv_tip1"
            app:layout_constraintTop_toBottomOf="@id/tv_tip1" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_tip3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="6dp"
            android:drawablePadding="10dp"
            android:text="@string/unlimited_preview_videos"
            android:textColor="@color/color_D8BAC5"
            android:textSize="14sp"
            app:drawableStartCompat="@drawable/ic_buy_tip_right"
            app:layout_constraintStart_toStartOf="@id/tv_tip1"
            app:layout_constraintTop_toBottomOf="@id/tv_tip2" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_tip4"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="6dp"
            android:drawablePadding="10dp"
            android:text="@string/unlimited_prompt_association"
            android:textColor="@color/color_D8BAC5"
            android:textSize="14sp"
            app:drawableStartCompat="@drawable/ic_buy_tip_right"
            app:layout_constraintStart_toStartOf="@id/tv_tip1"
            app:layout_constraintTop_toBottomOf="@id/tv_tip3" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_buy"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="30dp"
            android:layout_marginBottom="34dp"
            android:background="@drawable/shape_buy_dialog_btn"
            android:gravity="center"
            android:minWidth="236dp"
            android:minHeight="44dp"
            android:text="@string/buy_now"
            android:textColor="@color/white"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_tip4" />

        <ImageView
            android:layout_width="79dp"
            android:layout_height="96dp"
            android:layout_marginTop="-10dp"
            android:layout_marginEnd="4dp"
            android:src="@drawable/ic_crystal"
            app:layout_constraintBottom_toTopOf="@id/tv_buy"
            app:layout_constraintEnd_toEndOf="@id/iv_bg" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <ImageView
        android:id="@+id/iv_close"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginBottom="20dp"
        android:src="@drawable/ic_buy_close"
        app:layout_constraintBottom_toTopOf="@id/iv_bg"
        app:layout_constraintEnd_toEndOf="parent" />

    <ImageView
        android:id="@+id/iv_fore"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:adjustViewBounds="true"
        android:src="@drawable/ic_buy_fore"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>