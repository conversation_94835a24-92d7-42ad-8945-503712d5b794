<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="300dp"
    android:layout_height="wrap_content"
    android:background="@drawable/shape_16_white"
    android:orientation="vertical"
    android:paddingTop="24dp"
    android:paddingBottom="24dp">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:gravity="center"
        android:text="@string/are_you_sure_you_want_to_log_out"
        android:textColor="@color/black"
        android:textColorHighlight="@color/transparent"
        android:textFontWeight="500"
        android:textSize="16sp"
        tools:targetApi="28" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="18dp"
        android:layout_marginTop="24dp"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/btn_yes"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_weight="1"
            android:background="@drawable/shape_100_white"
            android:gravity="center"
            android:text="@string/yes"
            android:textColor="@color/color_924EFF"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/btn_cancel"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_marginStart="16dp"
            android:layout_weight="1"
            android:background="@drawable/selector_bg_btn_submit"
            android:gravity="center"
            android:text="@string/cancel"
            android:textColor="@color/white"
            android:textSize="14sp" />
    </LinearLayout>
</LinearLayout>