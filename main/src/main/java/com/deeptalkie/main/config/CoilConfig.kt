package com.deeptalkie.main.config

import coil3.ImageLoader
import coil3.PlatformContext
import coil3.disk.DiskCache
import coil3.memory.MemoryCache
import coil3.network.okhttp.OkHttpNetworkFetcherFactory
import coil3.request.allowRgb565
import coil3.request.crossfade
import com.clevguard.utils.BuildConfig
import com.clevguard.utils.ext.logv
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import okio.Path.Companion.toOkioPath

private fun coilOkHttpClient(): OkHttpClient {
    return OkHttpClient.Builder()
        .apply {
            if (!BuildConfig.DEBUG) return@apply

            val logging = HttpLoggingInterceptor(object : HttpLoggingInterceptor.Logger {
                override fun log(message: String) {
                    logv(message, "coil_logging", showLogFrom = false)
                }
            })
            logging.level = HttpLoggingInterceptor.Level.BASIC
            addInterceptor(logging)
        }
        .build()
}

fun coilConfig(context: PlatformContext): ImageLoader {
    return ImageLoader.Builder(context)
        .components {
            add(OkHttpNetworkFetcherFactory(callFactory = { coilOkHttpClient() }))
        }
        .memoryCache {
            MemoryCache.Builder()
                .maxSizePercent(context, 0.3)
                .build()
        }
        .diskCache {
            DiskCache.Builder()
                .directory(context.cacheDir.resolve("image_cache").toOkioPath())
                .maxSizePercent(0.03)
                .build()
        }
        .crossfade(true)
        .allowRgb565(true)
        .build()
}