package com.deeptalkie.main.config

import coil3.imageLoader
import com.deeptalkie.main.App
import com.deeptalkie.main.ext.imageRequest
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.withContext

fun List<String>.preloadImages() {
    val context = App.getInstance()
    App.launch(Dispatchers.IO) {
        forEach { url ->
            context.imageLoader
                .enqueue(url.imageRequest(context))
        }
    }
}

suspend fun List<String>.loadImages() = withContext(Dispatchers.IO) {
    map { url ->
        async {
            url.loadImage()
        }
    }.forEach { it.await() }
}

suspend fun String.loadImage() = withContext(Dispatchers.IO) {
    val context = App.getInstance()
    context.imageLoader
        .execute(imageRequest(context))
}