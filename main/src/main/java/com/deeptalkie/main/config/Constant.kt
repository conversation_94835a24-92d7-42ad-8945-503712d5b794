package com.deeptalkie.main.config

import android.content.Context
import android.util.Log
import com.deeptalkie.main.App
import com.deeptalkie.main.utils.Utils
import com.alibaba.fastjson.JSONObject
import java.util.Locale


/**
 *
 * @Description: java类作用描述
 * @Author: Lee<PERSON>iuuu
 * @CreateDate: 2022/7/26 13:46
 */
object Constant {
    var provider = "google"
    val google_login = "google"
    val accountCom = "account.deeptalkie.com"
    val accountNet = "account.deeptalkie.net"
    val umKey = "67e1044548ac1b4f87f53689"

    val EN: String = Locale.ENGLISH.language  //英语
    val JA: String = Locale.JAPANESE.language //日语
    val KO: String = Locale.KOREAN.language  // 韩语
    val TH: String = Locale.forLanguageTag("th").toLanguageTag()  //泰语

    //    v1.3.0
    val ES: String = Locale.forLanguageTag("es").toLanguageTag()  //西班牙语
    val DE: String = Locale.GERMAN.language //德语
    val PT: String = Locale.forLanguageTag("pt").toLanguageTag() //葡萄牙语
    val AR: String = Locale.forLanguageTag("ar").toLanguageTag() //阿拉伯语
    val FR: String = Locale.FRENCH.language  //法语
    val ZH: String = Locale.TRADITIONAL_CHINESE.language  //繁体中文

    //2.1.0
    val CN: String = Locale.SIMPLIFIED_CHINESE.language  //简体中文
    val IT: String = Locale.ITALIAN.language   //意大利
    val NL: String = Locale.forLanguageTag("nl").toLanguageTag()   //荷兰
    val MS: String = Locale.forLanguageTag("ms").toLanguageTag()   //马来
    val SV: String = Locale.forLanguageTag("sv").toLanguageTag()   //瑞典
    val ID: String = Locale.forLanguageTag("id").toLanguageTag() //印尼

    const val INFORMATION_SOURCES = "200553"
    val language: String
        get() {
            return try {
                val preferences =
                    App.getInstance().getSharedPreferences("imyfone_track", Context.MODE_PRIVATE)
                val str = preferences.getString(
                    "referrer",
                    "{\"utm_source\":\"not set\",\"utm_medium\":\"organic\"}"
                )
                val parseObject = JSONObject.parseObject(str)
                parseObject.getString("lang") ?: "EN"
            } catch (e: Exception) {
                "EN"
            }
        }

    val fromSite: String
        get() {
            return try {
                val preferences =
                    App.getInstance()
                        .getSharedPreferences("imyfone_track", Context.MODE_PRIVATE)
                val str = preferences.getString(
                    "referrer",
                    "{\"utm_source\":\"not set\",\"utm_medium\":\"organic\"}"
                )
                Log.e("缓存的渠道追踪", "getCacheTrackInfo:$str")
                val parseObject = JSONObject.parseObject(str)
                val source = parseObject.getString("utm_source") ?: "not set"
                if (source == "google-play") {
                    "not set"
                } else {
                    source
                }
            } catch (e: Exception) {
                "not set"
            }
        }


    val webParams = "?pid=$INFORMATION_SOURCES&custom=$fromSite"
    var AUTO_RENEWAL = "https://www.deeptalkie.com/company/auto-renewal.html"

    // multi language
    var terms = Utils.getTerms()
    var eula = Utils.getEula()
    var privacy = Utils.getPrivacy()
    var contactSupport = Utils.getContactSupport()

    const val GoogleChannel = "google play"
    const val Top_Banner_clicks = "Top_Banner_clicks"
    const val Search_box = "Search_box"
    const val Purchase_pop_up = "Purchase_pop_up"
    const val Purchase_page = "Purchase_page"
}