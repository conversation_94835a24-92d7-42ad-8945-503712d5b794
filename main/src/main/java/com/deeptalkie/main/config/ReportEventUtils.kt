package com.deeptalkie.main.config

import android.os.Bundle
import androidx.compose.runtime.Composable
import androidx.compose.runtime.SideEffect
import com.deeptalkie.main.App
import com.google.firebase.analytics.ktx.analytics
import com.google.firebase.ktx.Firebase
import com.umeng.analytics.MobclickAgent


/**
 *creater:l<PERSON><PERSON><PERSON> on 2025/6/9 17:42
 */
object ReportEventUtils {
    fun onEvent(event: String) {
        try {
            val ctx = App.getInstance()
            MobclickAgent.onEvent(ctx, event)
            //firebase数据上报
            Firebase.analytics.logEvent(event, null)
        } catch (e: Throwable) {
            e.printStackTrace()
        }
    }

    fun onEvent(event: String, map: Map<String, String>) {
        try {
            val ctx = App.getInstance()
            MobclickAgent.onEvent(ctx, event, map)
            val bundle = Bundle()
            for ((key, value) in map) {
                bundle.putString(key, value)
            }
            Firebase.analytics.logEvent(event, bundle)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @Composable
    fun OnEvent(event: String) {
        SideEffect {
            try {
                val ctx = App.getInstance()
                MobclickAgent.onEvent(ctx, event)
                //firebase数据上报
                Firebase.analytics.logEvent(event, null)
            } catch (e: Throwable) {
                e.printStackTrace()
            }
        }
    }

    @Composable
    fun OnEvent(event: String, map: Map<String, String>) {
        SideEffect {
            try {
                val ctx = App.getInstance()
                MobclickAgent.onEvent(ctx, event, map)
                val bundle = Bundle()
                for ((key, value) in map) {
                    bundle.putString(key, value)
                }
                Firebase.analytics.logEvent(event, bundle)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

}