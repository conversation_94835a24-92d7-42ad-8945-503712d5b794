package com.deeptalkie.main.proto

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.core.Serializer
import androidx.datastore.dataStore
import java.io.InputStream
import java.io.OutputStream

object AppPrefsSerializer : Serializer<AppPrefs> {
    override val defaultValue: AppPrefs = AppPrefs.getDefaultInstance()

    override suspend fun readFrom(input: InputStream): AppPrefs {
        return AppPrefs.parseFrom(input)
    }

    override suspend fun writeTo(t: AppPrefs, output: OutputStream) {
        t.writeTo(output)
    }
}

val Context.appDataStore: DataStore<AppPrefs> by dataStore(
    fileName = "app_prefs.pb",
    serializer = AppPrefsSerializer
)

fun AppPrefs?.update(
    block: AppPrefs.Builder.() -> Unit
): AppPrefs {
    return this?.toBuilder()?.apply(block)?.build() ?: AppPrefs.newBuilder().apply(block).build()
}

fun AppPrefs?.updateAppSettings(
    block: AppSettings.Builder.() -> Unit
): AppPrefs {
    return this.update {
        setAppSettings(
            appSettings.update(block)
        )
    }
}

fun AppPrefs?.updateVipInfo(
    block: VipInfo.Builder.() -> Unit
): AppPrefs {
    return this.update {
        setVipInfo(
            vipInfo.update(block)
        )
    }
}


fun AppSettings?.update(
    block: AppSettings.Builder.() -> Unit
): AppSettings {
    return this?.toBuilder()?.apply(block)?.build() ?: AppSettings.newBuilder().apply(block).build()
}

fun VipInfo?.update(
    block: VipInfo.Builder.() -> Unit
): VipInfo {
    return this?.toBuilder()?.apply(block)?.build() ?: VipInfo.newBuilder().apply(block).build()
}
