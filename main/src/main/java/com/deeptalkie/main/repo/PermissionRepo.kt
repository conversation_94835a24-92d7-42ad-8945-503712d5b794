package com.deeptalkie.main.repo

import com.deeptalkie.main.Membership
import com.deeptalkie.main.bean.UserManager
import com.imyfone.membership.api.bean.PermissionBean
import com.mfccgroup.android.httpclient.adapter.API

/**
 *creater:lin<PERSON><PERSON> on 2025/6/26 10:49
 */
class PermissionRepo : BaseDeepTalkieRepo() {
    suspend fun refreshVipState() =
        Membership.account.getPermissions(status = PermissionBean.STATUS_VALID).also {
            if (it.isSuccess) {
                updateVipState(it)
                updateVipFailureTimeText(it)
            }
        }


    private fun updateVipState(response: API<List<PermissionBean>>) {
        val permissions =
            response.getDataOrNull() ?: return UserManager.updateVipState(false)
        if (permissions.isNotEmpty()) {
            val licenseIds = permissions.map { it.licenseId ?: "" }
            // licenseId: 1 月，2 年，3 终身，4 季度，5 天，6 周
            val vipLicenseList = listOf<String>("1", "2", "4", "5", "6")
            var isVIP = false
            for (i in licenseIds) {
                if (vipLicenseList.contains(i)) {
                    isVIP = true
                    break
                }
            }
            UserManager.updateVipState(isVIP)

        } else {
            UserManager.updateVipState(false)
        }
    }

    private fun updateVipFailureTimeText(response: API<List<PermissionBean>>) {
        val permissions =
            response.getDataOrNull() ?: return UserManager.updateVipFailureTimeText("")
        if (permissions.isNotEmpty()) {
            val list = permissions.filter {
                it.licenseId != null && it.licenseId != "3"
            }
            val temp = list.minByOrNull {
                it.failureTime
            }
            UserManager.updateVipFailureTimeText(temp?.failureTimeText ?: "")
        } else {
            UserManager.updateVipFailureTimeText("")
        }
    }
}
