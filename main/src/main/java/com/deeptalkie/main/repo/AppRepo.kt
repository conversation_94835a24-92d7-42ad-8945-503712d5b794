package com.deeptalkie.main.repo

import com.deeptalkie.kidsguard.net.runHttp
import com.deeptalkie.main.App
import com.deeptalkie.main.Membership
import com.deeptalkie.main.R
import com.deeptalkie.main.bean.VersionBean
import com.deeptalkie.main.config.Constant
import com.deeptalkie.main.utils.LanguageUtil
import com.mfccgroup.android.httpclient.adapter.API

class AppRepo : BaseDeepTalkieRepo() {
    suspend fun getVersionInfo(): VersionBean? {
        return runHttp {
            val pid = Constant.INFORMATION_SOURCES
            val lang = LanguageUtil.getLanguageParam()

            dtApi.getVersionInfo(pid, lang).data
        }
    }

    suspend fun submitFeedback(email: String, title: String, content: String): API<String>? {
        return runHttp {
            Membership.membershipClient.cbs.postFeedback(
                email,
                title,
                content,
                null,
                "${App.getInstance().getString(R.string.app_name)}android",
                "feedback"
            )
        }
    }
}