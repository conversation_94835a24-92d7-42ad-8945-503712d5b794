package com.deeptalkie.main.repo

import com.deeptalkie.kidsguard.net.client.newMoshiRetrofitApi
import com.deeptalkie.main.BuildConfig
import com.deeptalkie.main.api.CommonApi
import com.deeptalkie.main.api.deepTalkieApi
import com.deeptalkie.main.api.deepTalkieApiFastJson
import com.deeptalkie.main.db.DTDatabase
import com.deeptalkie.main.db.dao.AIRoleDao
import com.deeptalkie.main.db.dao.AIRoleSessionDao
import com.deeptalkie.main.db.dao.AIRoleTagDao
import com.deeptalkie.main.db.dao.AIRoleTagRelationDao
import com.deeptalkie.main.db.dao.MsgRecordDao
import com.deeptalkie.main.db.dao.UserAIRoleDao
import com.deeptalkie.main.db.dao.VoiceDao

abstract class BaseDeepTalkieRepo {
    protected val commonApi = newMoshiRetrofitApi<CommonApi>(BuildConfig.CommonApi)
    protected val remoteApi = deepTalkieApi
    protected val dtApi = deepTalkieApiFastJson
    protected val aiRoleDao: AIRoleDao
        get() = DTDatabase.instance.aiRoleDao()
    protected val msgRecordDao: MsgRecordDao
        get() = DTDatabase.instance.msgRecordDao()
    protected val userAIRoleDao: UserAIRoleDao
        get() = DTDatabase.instance.userAIRoleDao()
    protected val aiRoleSessionDao: AIRoleSessionDao
        get() = DTDatabase.instance.aiRoleSessionDao()
    protected val voiceDao: VoiceDao
        get() = DTDatabase.instance.voiceDao()
    protected val aiRoleTagDao: AIRoleTagDao
        get() = DTDatabase.instance.aiRoleTagDao()
    protected val aiRoleTagRelationDao: AIRoleTagRelationDao
        get() = DTDatabase.instance.aiRoleTagRelationDao()
}