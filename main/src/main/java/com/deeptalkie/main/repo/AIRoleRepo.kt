package com.deeptalkie.main.repo

import androidx.room.Transaction
import com.clevguard.utils.ext.toJsonOrNull
import com.deeptalkie.kidsguard.net.getSuccessData
import com.deeptalkie.kidsguard.net.runHttp
import com.deeptalkie.main.Membership
import com.deeptalkie.main.db.table.AIRoleTagRelation
import com.deeptalkie.main.db.table.UserAIRole

class AIRoleRepo : BaseDeepTalkieRepo() {
    fun userAiRoleFlow(userId: String?, roleId: Long) =
        userAIRoleDao.queryUserAIRoleFlow(userId, roleId)

    suspend fun requestIsFavorite(userId: String, roleId: Long) {
        val data = runHttp { remoteApi.isFavorite(roleId) }?.getSuccessData() ?: return
        userAIRoleDao.upsertOne(UserAIRole(userId, roleId, isFavorite = data.isFavorite == 1))
    }

    @Transaction
    suspend fun loadAIRoleDetails(roleId: Long) {
        val response = runHttp { dtApi.getRoleDetail(roleId) } ?: return
        if (response.isSuccess) {
            val roleDetailBean = response.getDataOrNull() ?: return
            aiRoleDao.upsertOne(roleDetailBean.toAIRole())
            Membership.getUserId()?.let { userId ->
                userAIRoleDao.upsertOne(
                    UserAIRole(userId, roleId, isFavorite = roleDetailBean.isFavorite == 1)
                )
            }
            aiRoleTagDao.insertOrIgnoreAll(roleDetailBean.tags.map { it.toAIRoleTag() })
            aiRoleTagRelationDao.insertOrIgnoreAll(roleDetailBean.tags.map { tag ->
                AIRoleTagRelation(roleId, tag.id)
            })
        }
    }

    fun getAIRoleWithTagsFlow(roleId: Long) = aiRoleTagDao.getAIRoleWithTagsFlow(roleId)

    suspend fun favorite(userId: String, roleId: Long, isFavorite: Boolean) {
        runHttp {
            userAIRoleDao.upsertOne(UserAIRole(userId, roleId, isFavorite = !isFavorite))
            remoteApi.favorite(roleId, if (isFavorite) 0 else 1)
        }
    }

    suspend fun requestTalkSuggestion(roleId: Long) {
        runHttp {
            val resp = dtApi.getTalkSuggestion(roleId)
            if (resp.isSuccess) {
                val talkSuggestion = resp.getDataOrNull()?.list
                aiRoleDao.updateTalkSuggestion(roleId, talkSuggestion?.toJsonOrNull())
                return@runHttp talkSuggestion
            }
        }
    }
}