package com.deeptalkie.main.db.table

import androidx.compose.runtime.Stable
import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.deeptalkie.main.config.loadImage

@Entity(tableName = "msg_record")
@Stable
data class MsgRecord(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    @ColumnInfo("msg_id")
    val msgId: Long? = null,
    @ColumnInfo("role_id")
    val roleId: Long,
    @ColumnInfo("speaker_type")
    val speakerType: Int,
    @ColumnInfo("type")
    val type: Int,
    @ColumnInfo("content")
    val content: String,
    @ColumnInfo("created_at")
    val createdAt: Long,
    @ColumnInfo(name = "user_id")
    val userId: String,
    @ColumnInfo(name = "is_read")
    val isRead: Boolean,
    @ColumnInfo(name = "reply_id")
    val replyId: Long,
    @ColumnInfo(name = "is_lock")
    val isLock: Int,
    @ColumnInfo(name = "reply")
    val reply: String?,
    @ColumnInfo(name = "voice_path")
    val voicePath: String? = null,//语音文件存储路径
    @ColumnInfo(name = "video_time", defaultValue = "")
    val videoTime: String,
    @ColumnInfo(name = "prompt", defaultValue = "")
    val prompt: String = "",
    @ColumnInfo(name = "send_status", defaultValue = "Success")
    val sendStatus: MsgSendStatus,
    @ColumnInfo(name = "msg_send_type", defaultValue = "NULL")
    val msgSendType: Int? = null, // 1-普通文字消息，2-请求图片
) {
    val fromSelf: Boolean
        get() = speakerType == 2

    val msgType: MsgType?
        get() = when (type) {
            1 -> MsgType.Text
            2 -> MsgType.Image
            3 -> MsgType.Video
            4 -> MsgType.Voice
            else -> null
        }

    val isTextMsg: Boolean
        get() = msgType == MsgType.Text

    val isImageOrVideoMsg: Boolean
        get() = msgType == MsgType.Image || msgType == MsgType.Video

    suspend fun loadImage() {
        if (isImageOrVideoMsg) {
            content.loadImage()
        }
    }
}

enum class MsgType {
    Text, Image, Video, Voice
}

enum class MsgSendStatus {
    Sending, Success, Failed
}

fun newMyTextMsg(
    roleId: Long,
    text: String,
    userId: String,
    replyId: Long,
    reply: String?,
    msgSendType: Int,
): MsgRecord {
    return MsgRecord(
        msgId = null,
        roleId = roleId,
        speakerType = 2,
        type = 1,
        content = text,
        createdAt = System.currentTimeMillis() / 1000,
        userId = userId,
        isRead = true,
        replyId = replyId,
        isLock = 0,
        reply = reply,
        videoTime = "",
        sendStatus = MsgSendStatus.Sending,
        msgSendType = msgSendType
    )
}

fun MsgRecord?.isFailed(): Boolean {
    return this?.sendStatus == MsgSendStatus.Failed
}
