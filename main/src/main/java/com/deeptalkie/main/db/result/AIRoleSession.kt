package com.deeptalkie.main.db.result

import androidx.compose.runtime.Stable
import androidx.room.ColumnInfo
import androidx.room.Embedded
import androidx.room.Relation
import com.deeptalkie.main.db.table.AIRole
import com.deeptalkie.main.db.table.UserAIRole

@Stable
data class AIRoleSessionInfo(
    @Embedded
    val userAIRole: UserAIRole,
    @Relation(
        parentColumn = "role_id",
        entityColumn = "id",
        entity = AIRole::class
    )
    val aiRole: AIRole,
    @ColumnInfo(name = "unread_count")
    val unreadCount: Int
)