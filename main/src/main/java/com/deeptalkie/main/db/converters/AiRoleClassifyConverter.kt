package com.deeptalkie.main.db.converters

import androidx.room.TypeConverter

class AiRoleClassifyConverter {
    @TypeConverter
    fun fromAiRoleClassify(aiRoleClassify: AiRoleClassify): String {
        return aiRoleClassify.name
    }

    @TypeConverter
    fun fromString(value: String): AiRoleClassify {
        return AiRoleClassify.valueOf(value)
    }
}

enum class AiRoleClassify {
    DAILY, USER_CREATED,
}