package com.deeptalkie.main.db.converters

import androidx.room.TypeConverter
import kotlinx.serialization.json.Json

class StringListConverter {
    @TypeConverter
    fun fromStringList(list: List<String>?): String {
        val fixedList = list ?: listOf()
        return Json.encodeToString(fixedList)
    }

    @TypeConverter
    fun fromString(value: String?): List<String> {
        value ?: return emptyList()
        return Json.decodeFromString(value)
    }
}