package com.deeptalkie.main.db.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Transaction
import com.deeptalkie.main.db.result.MsgWithReply
import com.deeptalkie.main.db.table.MsgRecord
import com.deeptalkie.main.db.table.MsgSendStatus
import com.deeptalkie.main.db.table.MsgType
import com.deeptalkie.main.db.update.merge
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.withContext

@Dao
interface MsgRecordDao {
    @Query("select * from msg_record where user_id=:userId and role_id=:roleId order by created_at desc")
    fun queryCurrentSessionMsg(userId: String, roleId: Long): Flow<List<MsgWithReply>>

    @Query("select * from msg_record where user_id=:userId and role_id=:roleId order by created_at desc limit 1")
    suspend fun getLatestMsgRecord(userId: String, roleId: Long): MsgRecord?

    @Query("select * from msg_record where msg_id=:msgId")
    suspend fun queryMsgRecordAsync(msgId: Long): MsgRecord?

    @Insert(onConflict = OnConflictStrategy.IGNORE)
    suspend fun insertOrIgnore(msgRecord: MsgRecord): Long

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertOrReplaceOne(msgRecord: MsgRecord)

    @Transaction
    suspend fun upsertOne(msgRecord: MsgRecord) {
        val merge = tryInsert(msgRecord)
        if (merge != null) {
            insertOrReplaceOne(merge)
        }
        updateAIRoleMsg(msgRecord.userId, msgRecord.roleId)
    }

    suspend fun tryInsertAll(msgRecords: List<MsgRecord>): List<MsgRecord> {
        return msgRecords.mapNotNull { msg ->
            tryInsert(msg)
        }
    }

    private suspend fun tryInsert(msgRecord: MsgRecord): MsgRecord? {
        if (msgRecord.msgId == null) {
            insertOrIgnore(msgRecord)
            return null
        }
        val local = queryMsgRecordAsync(msgRecord.msgId)
        if (local == null) {
            insertOrIgnore(msgRecord)
            return null
        }
        return local.merge(msgRecord)
    }

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertOrReplaceAll(msgRecords: List<MsgRecord>)

    @Transaction
    suspend fun upsertAll(msgRecords: List<MsgRecord>) = withContext(Dispatchers.IO) {
        if (msgRecords.isEmpty()) return@withContext

        val merges = tryInsertAll(msgRecords)
        insertOrReplaceAll(merges)

        val lastMsg = msgRecords.last()
        updateAIRoleMsg(lastMsg.userId, lastMsg.roleId)
    }

    suspend fun updateAIRoleMsg(userId: String, roleId: Long) {
        val lastMsg = getLatestMsgRecord(userId, roleId) ?: return

        val content = when (lastMsg.msgType) {
            MsgType.Text -> lastMsg.content
            MsgType.Image -> "[image]"
            MsgType.Video -> "[video]"
            MsgType.Voice -> "[voice]"
            null -> lastMsg.content
        }

        updateAIRoleMsgInfo(userId, lastMsg.roleId, content, lastMsg.createdAt)
    }

    @Insert(onConflict = OnConflictStrategy.IGNORE)
    suspend fun insertIgnoreAll(msgRecords: List<MsgRecord>)

    @Query("update user_ai_role set last_msg=:lastMsg, last_message_at=:lastMsgAt where user_id=:userId and role_id=:roleId")
    suspend fun updateAIRoleMsgInfo(userId: String, roleId: Long, lastMsg: String, lastMsgAt: Long)

    @Query("select count(*) from msg_record where is_read = 0 and user_id=:userId and role_id=:roleId")
    suspend fun getUnreadMsgCount(userId: String, roleId: Long): Long

    @Query("update msg_record set is_read=1 where user_id=:userId and role_id=:roleId")
    suspend fun setRoleMsgIsRead(userId: String, roleId: Long)

    /**
     * 删除当前角色下的所有对话消息
     */
    @Query("delete from msg_record where user_id = :userId and role_id = :roleId")
    suspend fun deleteSession(userId: String, roleId: Long)

    @Query("delete from msg_record where user_id = :userId and role_id in (:roleIds)")
    suspend fun deleteRolesMsg(userId: String, roleIds: List<Long>)

    @Query("delete from msg_record where user_id = :userId and role_id = :roleId and msg_id = :msgId")
    suspend fun delMsg(userId: String, roleId: Long, msgId: Long)

    @Query("update msg_record set voice_path = :path where id = :id")
    suspend fun setMsgVoicePath(id: Long, path: String)

    @Query("update msg_record set is_lock = 0 where user_id = :userId")
    suspend fun unlockAllMsg(userId: String)

    @Query("select count(*) = 0 from msg_record where user_id=:userId and role_id=:roleId")
    suspend fun isNoMsgRecord(userId: String, roleId: Long): Boolean

    @Query("update msg_record set send_status=:sendStatus where id=:localId")
    suspend fun updateMsgStatus(sendStatus: MsgSendStatus, localId: Long)

    @Query("update msg_record set msg_id=:msgId, send_status=:sendStatus where id=:localId")
    suspend fun updateMsgIdAndStatus(msgId: Long, sendStatus: MsgSendStatus, localId: Long)
}