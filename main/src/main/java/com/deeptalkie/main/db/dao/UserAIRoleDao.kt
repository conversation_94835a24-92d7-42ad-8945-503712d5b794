package com.deeptalkie.main.db.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Transaction
import com.deeptalkie.main.db.table.UserAIRole
import com.deeptalkie.main.db.update.merge
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.withContext

@Dao
interface UserAIRoleDao {

    @Insert(onConflict = OnConflictStrategy.IGNORE)
    suspend fun insertOrIgnore(userAIRole: UserAIRole): Long

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertOrReplace(userAIRole: UserAIRole)

    @Query("select * from user_ai_role where user_id=:userId and role_id=:roleId")
    suspend fun getByUserAndRoleId(userId: String, roleId: Long): UserAIRole?

    @Query("select * from user_ai_role where user_id=:userId and role_id=:roleId")
    fun queryUserAIRoleFlow(userId: String?, roleId: Long): Flow<UserAIRole?>

    @Transaction
    suspend fun upsertOne(userAIRole: UserAIRole) {
        val res = insertOrIgnore(userAIRole)
        if (res == -1L) {
            val localUserAIRole = getByUserAndRoleId(userAIRole.userId, userAIRole.roleId)

            insertOrReplace(localUserAIRole.merge(userAIRole))
        }
    }

    @Insert(onConflict = OnConflictStrategy.IGNORE)
    suspend fun insertOrIgnoreAll(userAIRoleList: List<UserAIRole>): List<Long>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertOrReplaceAll(userAIRoleList: List<UserAIRole>)

    @Query("select * from user_ai_role where user_id=:userId and session_id not in (:sessionIds)")
    suspend fun getNotExistSession(userId: String, sessionIds: List<Long>): List<UserAIRole>

    @Query("update user_ai_role set session_id = null, last_msg = null, last_message_at = null, session_top_up_at = 0 where user_id=:userId and session_id in (:sessionIds)")
    suspend fun deleteNotExistSessionInfo(userId: String, sessionIds: List<Long>)

    @Transaction
    suspend fun upsertAll(userAIRoleList: List<UserAIRole>) = withContext(Dispatchers.IO) {
        if (userAIRoleList.isEmpty()) return@withContext

        val res = insertOrIgnoreAll(userAIRoleList)
        val updates = buildList {
            res.forEachIndexed { index, r ->
                if (r == -1L) {
                    add(userAIRoleList[index])
                }
            }
        }
        if (updates.isNotEmpty()) {
            val merges = updates.map { userAIRole ->
                async {
                    val localUserAIRole = getByUserAndRoleId(userAIRole.userId, userAIRole.roleId)
                    localUserAIRole.merge(userAIRole)
                }
            }.map { it.await() }

            insertOrReplaceAll(merges)
        }
    }

    @Query("select session_id from user_ai_role where user_id=:userId and role_id=:roleId")
    suspend fun getSessionId(userId: String, roleId: Long): Long?

    @Query("update user_ai_role set session_id = null, last_msg = null, last_message_at = null, session_top_up_at = 0 where user_id=:userId and role_id=:roleId")
    suspend fun deleteSession(userId: String, roleId: Long)

    @Query("select count(*) from user_ai_role where user_id=:userId and session_id is not null")
    suspend fun getSessionsCount(userId: String): Int
}