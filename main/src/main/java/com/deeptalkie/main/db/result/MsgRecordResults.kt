package com.deeptalkie.main.db.result

import androidx.room.Embedded
import androidx.room.Relation
import com.deeptalkie.main.db.table.MsgRecord

data class MsgWithReply(
    @Embedded
    val msg: MsgRecord, // 使用 @Embedded 将 MsgRecord 嵌入
    @Relation(
        parentColumn = "reply_id", // MsgRecord 表中用于关联的列 (reply_id)
        entityColumn = "msg_id"         // 被关联的 MsgRecord 表中对应的列 (id)
    )
    val reply: MsgRecord?           // 回复的 MsgRecord，可能为 null
)