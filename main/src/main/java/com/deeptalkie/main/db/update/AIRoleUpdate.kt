package com.deeptalkie.main.db.update

import com.deeptalkie.main.db.table.AIRole

fun AIRole?.merge(new: AIR<PERSON>): AIRole {
    this ?: return new
    return AIRole(
        id = new.id,
        name = new.name.ifEmpty { name },
        description = new.description.ifEmpty { description },
        images = new.images.ifEmpty { images },
        messageCount = if (new.messageCount > 0) new.messageCount else messageCount,
        classify = new.classify ?: classify,
        voiceId = if (new.voiceId != -1) new.voiceId else voiceId,
        width = if (new.width > 0) new.width else width,
        height = if (new.height > 0) new.height else height,
        isHot = if (new.isHot != -1) new.isHot else isHot,
        createdAt = new.createdAt.ifEmpty { createdAt },
        updatedAt = new.updatedAt.ifEmpty { updatedAt },
        approvalStatus = new.approvalStatus ?: approvalStatus,
        isShowModel = new.isShowModel ?: isShowModel,
        modelName = new.modelName ?: modelName,
        type = new.type ?: type,
        talkSuggestion = new.talkSuggestion ?: talkSuggestion,
    )
}