package com.deeptalkie.main.db.update

import com.deeptalkie.main.db.table.UserAIRole

fun UserAIRole?.merge(new: UserAIRole): UserAIRole {
    this ?: return new
    return UserAIRole(
        userId = new.userId,
        roleId = new.roleId,
        sessionId = new.sessionId ?: sessionId,
        lastMsg = lastMsg ?: new.lastMsg,
        lastMsgAt = lastMsgAt ?: new.lastMsgAt,
        sessionTopUpAt = new.sessionTopUpAt ?: sessionTopUpAt,
        isFavorite = new.isFavorite ?: isFavorite
    )
}