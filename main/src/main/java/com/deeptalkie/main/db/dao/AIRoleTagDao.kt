package com.deeptalkie.main.db.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Transaction
import androidx.room.Upsert
import com.deeptalkie.main.db.result.AIRoleWithTags
import com.deeptalkie.main.db.table.AIRoleTag
import com.deeptalkie.main.db.table.AIRoleTagRelation
import kotlinx.coroutines.flow.Flow

@Dao
interface AIRoleTagDao {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertOrReplaceAll(tags: List<AIRoleTag>)

    @Insert(onConflict = OnConflictStrategy.IGNORE)
    suspend fun insertOrIgnoreAll(tags: List<AIRoleTag>)

    @Insert(onConflict = OnConflictStrategy.IGNORE)
    suspend fun insertOrIgnore(tags: AIRoleTag)

    @Upsert
    suspend fun upsertAll(tags: List<AIRoleTag>)

    @Query("select * from ai_role_tag")
    suspend fun getAllTags(): List<AIRoleTag>

    @Query("select * from ai_role_tag")
    fun getAllTagsFlow(): Flow<List<AIRoleTag>>

    @Transaction
    @Query("select * from ai_role where id = :roleId")
    fun getAIRoleWithTagsFlow(roleId: Long): Flow<AIRoleWithTags?>

    @Query("delete from ai_role_tag_relation where tag_id not in (:tagIds)")
    suspend fun deleteByTagIds(tagIds: List<Long>)

    @Query("delete from ai_role_tag where id not in (:tagIds)")
    suspend fun deleteNotInRemoteTags(tagIds: List<Long>)

    @Transaction
    suspend fun deleteNotExistTags(tags: List<AIRoleTag>) {
        deleteByTagIds(tags.map { it.id })
        deleteNotInRemoteTags(tags.map { it.id })
    }
}

@Dao
interface AIRoleTagRelationDao {
    @Insert(onConflict = OnConflictStrategy.IGNORE)
    suspend fun insertOrIgnoreAll(tagRelations: List<AIRoleTagRelation>)
}