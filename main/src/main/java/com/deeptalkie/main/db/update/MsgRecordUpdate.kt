package com.deeptalkie.main.db.update

import com.deeptalkie.main.db.table.MsgRecord

fun MsgRecord?.merge(new: MsgRecord): MsgRecord {
    this ?: return new
    return MsgRecord(
        id = id,
        msgId = new.msgId,
        roleId = new.roleId,
        speakerType = new.speakerType,
        type = new.type,
        content = new.content,
        createdAt = new.createdAt,
        userId = new.userId,
        isRead = isRead,
        replyId = new.replyId,
        isLock = new.isLock,
        reply = new.reply,
        voicePath = voicePath,
        videoTime = new.videoTime.ifEmpty { videoTime },
        prompt = new.prompt,
        sendStatus = new.sendStatus,
        msgSendType = msgSendType
    )
}