package com.deeptalkie.main.db.dao

import androidx.room.Dao
import androidx.room.Query
import androidx.room.Upsert
import com.deeptalkie.main.db.table.Voice
import kotlinx.coroutines.flow.Flow

@Dao
interface VoiceDao {
    @Upsert
    suspend fun upsertAll(voices: List<Voice>)

    @Query("select * from voice where recommended = 1")
    fun getRecommendedVoiceFlow(): Flow<List<Voice>>

    @Query("select * from voice where sex = 2")
    fun getFemaleVoiceFlow(): Flow<List<Voice>>

    @Query("select * from voice where sex = 1")
    fun getMaleVoiceFlow(): Flow<List<Voice>>

    @Query("delete from voice where id not in (:ids)")
    suspend fun deleteNotInIds(ids: List<Long>)

    @Query("delete from voice")
    suspend fun deleteAll()
}