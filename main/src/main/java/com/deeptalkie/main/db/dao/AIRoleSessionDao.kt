package com.deeptalkie.main.db.dao

import androidx.room.Dao
import androidx.room.Query
import androidx.room.Transaction
import com.deeptalkie.main.db.result.AIRoleSessionInfo
import kotlinx.coroutines.flow.Flow

@Dao
interface AIRoleSessionDao {
    @Transaction
    @Query(
        """
        SELECT 
            u.*,
            r.*,
            (SELECT COUNT(*) FROM msg_record m WHERE m.role_id = u.role_id AND m.is_read = 0 AND m.user_id = u.user_id) as unread_count
        FROM user_ai_role u
        JOIN ai_role r ON u.role_id = r.id
        WHERE u.user_id = :userId 
        AND u.role_id = :roleId 
        """
    )
    fun querySessionByIds(userId: String, roleId: Long): Flow<AIRoleSessionInfo?>

    @Transaction
    @Query(
        """
        SELECT 
            u.*,
            r.*,
            (SELECT COUNT(*) FROM msg_record m WHERE m.role_id = u.role_id AND m.is_read = 0 AND m.user_id = u.user_id) as unread_count
        FROM user_ai_role u
        JOIN ai_role r ON u.role_id = r.id
        WHERE u.user_id = :userId AND u.session_id IS NOT NULL
        ORDER BY
            CASE WHEN u.session_top_up_at > 0 THEN 1 ELSE 0 END DESC,
            u.session_top_up_at DESC, 
            u.last_message_at DESC
        """
    )
    fun querySessionsWithInfo(userId: String?): Flow<List<AIRoleSessionInfo>>
}