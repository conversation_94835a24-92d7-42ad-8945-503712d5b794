package com.deeptalkie.main.db

import androidx.room.AutoMigration
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import com.clevguard.utils.ext.logv
import com.deeptalkie.main.App
import com.deeptalkie.main.db.converters.StringListConverter
import com.deeptalkie.main.db.dao.AIRoleDao
import com.deeptalkie.main.db.dao.AIRoleSessionDao
import com.deeptalkie.main.db.dao.AIRoleTagDao
import com.deeptalkie.main.db.dao.AIRoleTagRelationDao
import com.deeptalkie.main.db.dao.MsgRecordDao
import com.deeptalkie.main.db.dao.UserAIRoleDao
import com.deeptalkie.main.db.dao.VoiceDao
import com.deeptalkie.main.db.table.AIRole
import com.deeptalkie.main.db.table.AIRoleTag
import com.deeptalkie.main.db.table.AIRoleTagRelation
import com.deeptalkie.main.db.table.MsgRecord
import com.deeptalkie.main.db.table.UserAIRole
import com.deeptalkie.main.db.table.Voice
import java.util.concurrent.Executors

@Database(
    entities = [
        AIRole::class,
        MsgRecord::class,
        UserAIRole::class,
        Voice::class,
        AIRoleTag::class,
        AIRoleTagRelation::class,
    ],
    version = 7,
    autoMigrations = [
        AutoMigration(from = 1, to = 2),
        AutoMigration(from = 2, to = 3),
        AutoMigration(from = 3, to = 4),
        AutoMigration(from = 4, to = 5),
        AutoMigration(from = 5, to = 6),
        AutoMigration(from = 6, to = 7),
    ]
)
@TypeConverters(StringListConverter::class)
abstract class DTDatabase : RoomDatabase() {
    abstract fun aiRoleDao(): AIRoleDao
    abstract fun msgRecordDao(): MsgRecordDao
    abstract fun userAIRoleDao(): UserAIRoleDao
    abstract fun aiRoleSessionDao(): AIRoleSessionDao
    abstract fun voiceDao(): VoiceDao
    abstract fun aiRoleTagDao(): AIRoleTagDao
    abstract fun aiRoleTagRelationDao(): AIRoleTagRelationDao

    companion object {
        val instance by lazy {
            Room.databaseBuilder(App.getInstance(), DTDatabase::class.java, "db_deeptalkie")
                .apply {
                    if (App.showLog) {
                        setJournalMode(JournalMode.TRUNCATE)
                        setQueryCallback(showSqlLog, Executors.newSingleThreadExecutor())
                    }
                }
                .build()
        }
    }
}

private val showSqlLog = { sql: String, args: List<Any?> ->
    val formattedArgs = args.map { arg -> "$arg" }
    val sqlParts = sql.split("\\?".toRegex())
    val replacedQuery = buildString {
        for (i in sqlParts.indices) {
            append(sqlParts[i].lowercase())
            if (i < formattedArgs.size) {
                append(formattedArgs[i])
            }
        }
    }
    logv(replacedQuery, "sql_logging", false)
}