package com.deeptalkie.main.db.result

import androidx.room.Embedded
import androidx.room.Junction
import androidx.room.Relation
import com.deeptalkie.main.db.table.AIRole
import com.deeptalkie.main.db.table.AIRoleTag
import com.deeptalkie.main.db.table.AIRoleTagRelation

data class AIRoleImagesResult(
    val images: List<String>,
)

data class AIRoleWithTags(
    @Embedded
    val aiRole: AIRole,
    @Relation(
        parentColumn = "id", // AIRole.id (父实体的列名)
        entity = AIRoleTag::class, // 关联的实体类
        entityColumn = "id", // AIRoleTag.id (关联实体的列名)
        associateBy = Junction(
            value = AIRoleTagRelation::class, // 中间表实体类
            parentColumn = "role_id", // 中间表中与父实体主键关联的列名
            entityColumn = "tag_id"   // 中间表中与关联实体主键关联的列名
        )
    )
    val tags: List<AIRoleTag>
)