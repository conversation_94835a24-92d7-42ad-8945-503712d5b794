package com.deeptalkie.main.db.table

import androidx.compose.runtime.Stable
import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.clevguard.utils.ext.jsonAsOrNull
import com.deeptalkie.main.R
import com.deeptalkie.main.db.converters.AiRoleClassify
import com.deeptalkie.main.db.converters.AiRoleClassifyConverter
import com.deeptalkie.main.utils.getString
import kotlinx.serialization.Serializable

@Entity(tableName = "ai_role")
@TypeConverters(AiRoleClassifyConverter::class)
@Stable
@Serializable
data class AIRole(
    @PrimaryKey
    @ColumnInfo(name = "id")
    val id: Long,
    @ColumnInfo(name = "name")
    val name: String,
    @ColumnInfo(name = "description")
    val description: String,
    @ColumnInfo(name = "images")
    val images: List<String>,
    @ColumnInfo(name = "message_count")
    val messageCount: Long,
    @ColumnInfo(name = "classify")
    val classify: AiRoleClassify? = null,
    @ColumnInfo(name = "voice_id")
    val voiceId: Int,
    @ColumnInfo(name = "width", defaultValue = "0")
    val width: Int,
    @ColumnInfo(name = "height", defaultValue = "0")
    val height: Int,
    @ColumnInfo(name = "is_hot", defaultValue = "0")
    val isHot: Int,
    @ColumnInfo(name = "created_at", defaultValue = "")
    val createdAt: String,
    @ColumnInfo(name = "updated_at", defaultValue = "")
    val updatedAt: String,
    @ColumnInfo(name = "approval_status")
    val approvalStatus: Int? = null, //  0-待审核，1-审核通过，2-审核未通过
    @ColumnInfo(name = "is_show_model")
    val isShowModel: Int? = null,
    @ColumnInfo(name = "model_name")
    val modelName: String? = null,
    @ColumnInfo(name = "type")
    val type: Int? = null, // 1-系统创建角色，2-用户创建角色
    @ColumnInfo(name = "talk_suggestion")
    val talkSuggestion: String? = null,
) {
    val avatar: String get() = images.firstOrNull().orEmpty()

    fun ratio(): Float {
        return width / height.toFloat()
    }

    fun isHot(): Boolean {
        return isHot == 1
    }

    val approvalStatusText: String?
        get() = when (approvalStatus) {
            0 -> getString(R.string.under_review)
            1 -> null
            2 -> getString(R.string.review_failed)
            else -> null
        }

    val talkSuggestionList: List<String>
        get() = talkSuggestion?.jsonAsOrNull<List<String>>().orEmpty()
}

fun AIRole?.isShowModel(): Boolean {
    return this?.isShowModel == 1
}

fun AIRole?.isUserCreated(): Boolean {
    return this?.type == 2
}

fun AIRole?.isHot(): Boolean {
    return this?.isHot == 1
}
