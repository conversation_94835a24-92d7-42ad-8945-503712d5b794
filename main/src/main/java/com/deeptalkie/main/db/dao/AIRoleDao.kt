package com.deeptalkie.main.db.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Transaction
import com.deeptalkie.main.db.result.AIRoleImagesResult
import com.deeptalkie.main.db.table.AIRole
import com.deeptalkie.main.db.update.merge
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.withContext

@Dao
interface AIRoleDao {
    @Query("select * from ai_role where classify='DAILY'")
    fun queryDailyAiRoles(): Flow<List<AIRole>>

    @Query("select * from ai_role where classify='USER_CREATED'")
    fun queryUserCreatedAiRoles(): Flow<List<AIRole>>

    @Query("select images from ai_role order by classify asc")
    suspend fun queryHomePageImages(): List<AIRoleImagesResult>

    @Insert(onConflict = OnConflictStrategy.IGNORE)
    suspend fun insertOrIgnore(aiRole: AIRole): Long

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertOrReplaceOne(aiRole: AIRole)

    @Transaction
    suspend fun upsertOne(aiRole: AIRole) {
        val res = insertOrIgnore(aiRole)
        if (res == -1L) {
            val localAIRole = queryAIRoleAsync(aiRole.id)

            insertOrReplaceOne(localAIRole.merge(aiRole))
        }
    }

    @Insert(onConflict = OnConflictStrategy.IGNORE)
    suspend fun insertOrIgnoreAll(aiRoles: List<AIRole>): List<Long>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertOrReplaceAll(aiRoles: List<AIRole>)

    @Query("update ai_role set classify = null where id not in (:ids)")
    suspend fun resetClassifyWhereNotInRemote(ids: List<Long>)

    @Transaction
    suspend fun upsertAll(aiRoles: List<AIRole>) = withContext(Dispatchers.IO) {
        if (aiRoles.isEmpty()) return@withContext

        val res = insertOrIgnoreAll(aiRoles)
        val updates = buildList {
            res.forEachIndexed { index, r ->
                if (r == -1L) {
                    add(aiRoles[index])
                }
            }
        }

        if (updates.isNotEmpty()) {
            val merges = updates.map { newRole ->
                async {
                    val localAIRole = queryAIRoleAsync(newRole.id)
                    localAIRole.merge(newRole)
                }
            }.map { it.await() }

            insertOrReplaceAll(merges)
        }
    }

    @Query("select * from ai_role where id=:roleId")
    fun queryAIRole(roleId: Long): Flow<AIRole>

    @Query("select * from ai_role where id=:roleId")
    suspend fun queryAIRoleAsync(roleId: Long): AIRole?

    @Query("update ai_role set talk_suggestion = :talkSuggestion where id = :roleId")
    suspend fun updateTalkSuggestion(roleId: Long, talkSuggestion: String?)
}