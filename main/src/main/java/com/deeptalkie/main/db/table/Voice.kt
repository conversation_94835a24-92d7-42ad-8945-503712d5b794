package com.deeptalkie.main.db.table

import android.os.Parcelable
import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import kotlinx.parcelize.Parcelize
import kotlinx.serialization.Serializable

@Parcelize
@Serializable
@Entity(tableName = "voice")
data class Voice(
    @PrimaryKey
    @ColumnInfo(name = "id")
    val id: Long,
    @ColumnInfo(name = "type")
    val type: Int,
    @ColumnInfo(name = "voice")
    val voice: String,
    @ColumnInfo(name = "show_name")
    val showName: String,
    @ColumnInfo(name = "avatar")
    val avatar: String,
    @ColumnInfo(name = "lang_id")
    val langId: Long,
    @ColumnInfo(name = "cate_id")
    val cateId: Long,
    @ColumnInfo(name = "sex")
    val sex: Int,
    @ColumnInfo(name = "advanced")
    val advanced: Int,
    @ColumnInfo(name = "recommended")
    val recommended: Int,
    @ColumnInfo(name = "category_name")
    val categoryName: String,
    @ColumnInfo(name = "default")
    val default: Int,
    @ColumnInfo(name = "age")
    val age: Int,
) : Parcelable