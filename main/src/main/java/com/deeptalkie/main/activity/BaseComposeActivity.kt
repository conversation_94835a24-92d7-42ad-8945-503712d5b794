package com.deeptalkie.main.activity

import android.content.ComponentCallbacks
import android.content.Context
import android.content.res.Configuration
import android.content.res.Resources
import android.os.Bundle
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.runtime.Composable
import androidx.fragment.app.FragmentActivity
import com.clevguard.utils.ext.configSystemBar
import com.clevguard.utils.ext.logv
import com.clevguard.utils.ext.setDensity
import com.deeptalkie.main.bean.UserManager
import com.deeptalkie.main.compose.theme.DTTheme
import com.deeptalkie.main.utils.ContextUtil
import kotlin.properties.Delegates

abstract class BaseComposeActivity : FragmentActivity() {
    private var currentOrientation by Delegates.observable(-1) { _, old, new ->
        if (old != new) {
            customResources = setDensity()
        }
    }

    private val callback = object : ComponentCallbacks {
        override fun onConfigurationChanged(newConfig: Configuration) {
            currentOrientation = newConfig.orientation
        }

        override fun onLowMemory() {

        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        application.registerComponentCallbacks(callback)

        enableEdgeToEdge()

        configSystemBar {
            statusBarBlackFont = false
            navigationBarBlackIcon = false
        }

        setContent {
            DTTheme {
                ComposeContent()
            }
        }
    }

    @Composable
    abstract fun ComposeContent()

    override fun attachBaseContext(newBase: Context) {
        val language = UserManager.getLanguage()
        super.attachBaseContext(
            ContextUtil.attachBaseContext(
                newBase,
                language.locale
            )
        )
        logv("attachBaseContext:$language", "lang")
    }

    private var customResources: Resources? = null

    override fun getResources(): Resources {
        return customResources ?: super.getResources().run {
            setDensity()
            createConfigurationContext(configuration).resources.also {
                customResources = it
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        application.unregisterComponentCallbacks(callback)
    }
}