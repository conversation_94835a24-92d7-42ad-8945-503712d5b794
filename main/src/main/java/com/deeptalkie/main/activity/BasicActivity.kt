package com.deeptalkie.main.activity

import android.content.Context
import android.os.Bundle
import android.view.View
import android.widget.ImageView
import androidx.activity.enableEdgeToEdge
import androidx.appcompat.app.AppCompatActivity
import androidx.viewbinding.ViewBinding
import com.clevguard.utils.ext.configSystemBar
import com.deeptalkie.main.R
import com.deeptalkie.main.bean.UserManager
import com.deeptalkie.main.utils.ContextUtil

/**
 *
 * @Description: java类作用描述
 * @Author: LeeQiuuu
 * @CreateDate: 2022/3/31 14:13
 */
abstract class BasicActivity<VB : ViewBinding> : AppCompatActivity() {
    lateinit var mBinding: VB

    override fun onCreate(savedInstanceState: Bundle?) {
        enableEdgeToEdge()
        window.decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR
        super.onCreate(savedInstanceState)
        configSystemBar {
            gestureNavigationTransparent = true
        }

        getViewBinding().also { mBinding = it }
        setContentView(mBinding.root)

        val ivBack = findViewById<ImageView>(R.id.iv_back)
        ivBack?.setOnClickListener {
            finish()
        }
        initView()
    }

    protected abstract fun getViewBinding(): VB

    abstract fun initView()

    /**
     * app内手动切换语言的时候需要调用，多语言时，不需要，只需要value有对应的语言即可、
     */
    override fun attachBaseContext(newBase: Context) {
        super.attachBaseContext(
            ContextUtil.attachBaseContext(
                newBase,
                UserManager.getLanguage().locale // 获取我们存储的语言环境 比如 "en","zh",等等
            )
        )
    }
}