package com.deeptalkie.main.activity

import android.Manifest
import android.os.Build
import android.os.Bundle
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.compose.runtime.Composable
import com.clevguard.utils.ext.configSystemBar
import com.clevguard.utils.ext.logv
import com.deeptalkie.main.rtc.Live2DRtcViewModel

class Live2DRtcActivity : BaseComposeActivity() {
    private val viewModel by viewModels<Live2DRtcViewModel>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        configSystemBar {
            statusBarBlackFont = true
            navigationBarBlackIcon = true
        }

        val permissionRequest = registerForActivityResult(
            ActivityResultContracts.RequestMultiplePermissions()
        ) { permissions ->
            logv("权限申请结果:$permissions")
            val allGranted = permissions.values.all { it }
            if (allGranted) {
                viewModel.start()
            }
        }

        val permissions = if (Build.VERSION.SDK_INT >= 31) {
            arrayOf(
                Manifest.permission.RECORD_AUDIO,
                Manifest.permission.READ_PHONE_STATE,
                Manifest.permission.BLUETOOTH_CONNECT
            )
        } else {
            arrayOf(Manifest.permission.RECORD_AUDIO)
        }

        permissionRequest.launch(permissions)
    }

    @Composable
    override fun ComposeContent() {

    }
}