package com.deeptalkie.main.activity

import android.os.Bundle
import androidx.activity.compose.BackHandler
import androidx.activity.compose.LocalActivity
import androidx.activity.viewModels
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.platform.LocalContext
import androidx.lifecycle.lifecycleScope
import androidx.navigation.compose.rememberNavController
import com.azhon.appupdate.listener.OnDownloadListener
import com.azhon.appupdate.manager.DownloadManager
import com.deeptalkie.kidsguard.net.error
import com.deeptalkie.kidsguard.net.successFun
import com.deeptalkie.main.R
import com.deeptalkie.main.api.deepTalkieApi
import com.deeptalkie.main.compose.navigation.MainNavigation
import com.deeptalkie.main.compose.ui.components.RequestPermission
import com.deeptalkie.main.compose.ui.dialog.DTSingleBtnWarnDialog
import com.deeptalkie.main.compose.ui.dialog.RequestNotifyDialog
import com.deeptalkie.main.compose.ui.page.about.UpdateDialog
import com.deeptalkie.main.compose.utils.NotifyPermissionUtil
import com.deeptalkie.main.config.TimberUtil
import com.deeptalkie.main.ext.showToast
import com.deeptalkie.main.utils.NetWorkManager
import com.deeptalkie.main.utils.TrackUtil
import com.deeptalkie.main.view.ConfigDialog
import com.deeptalkie.main.viewmodel.MainViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.io.File

class MainActivity : BaseComposeActivity() {
    companion object {
        private const val TAG = "MainActivity"
    }

    private var exitApp = false

    private val mainVm by viewModels<MainViewModel>()

    private val configDialog by lazy {
        ConfigDialog(this)
    }

    private var showAreaNotAvailableDialog by mutableStateOf(false)

    private fun checkConfig() {
        lifecycleScope.launch {
            deepTalkieApi.activity().successFun {
                TimberUtil.d(TAG, "it = $it")
                val config = it ?: return@successFun
                configDialog.setTextAndShow(config)
            }.error { i, s ->
                TimberUtil.d(TAG, "i = $i,s = $s")
            }
        }
    }

    private fun checkUpdate() {
        if (!NetWorkManager.isNetworkConnectedSystem()) {
            showToast(this.getString(R.string.network_error))
            return
        }
        mainVm.getNewVersionInfo(this) { version ->
            mainVm.versionBean = version
        }
    }

    private fun checkIpInfo() {
        lifecycleScope.launch {
            try {
                deepTalkieApi.getIpInfo()
                    .successFun { inChineseMainland ->
                        if (inChineseMainland?.inChineseMainland == true) {
                            // 显示toast提示应用不可用
                            showAreaNotAvailableDialog = true
                        }
                    }
            } catch (_: Exception) {
                // 出现异常，不做处理继续使用应用
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        TrackUtil.initTrack(this)
        mainVm.connectWebSocket()
        checkUpdate()
        checkConfig()
    }

    @Composable
    override fun ComposeContent() {
        val navController = rememberNavController()
        var notifyShow by remember { mutableStateOf(false) }
        var startRequestPermission by remember { mutableStateOf(false) }
        val context = LocalContext.current
        val activity = LocalActivity.current

        BackHandler {
            if (navController.previousBackStackEntry != null) {
                navController.popBackStack()
                return@BackHandler
            }
            if (exitApp) {
                finish()
            } else {
                exitApp = true
                showToast(getString(R.string.click_again_exit))
                lifecycleScope.launch {
                    delay(2000)
                    exitApp = false
                }
            }
        }
        MainNavigation()
        mainVm.versionBean?.let {
            UpdateDialog(
                versionBean = it,
                onUpdate = {
                    if (!NotifyPermissionUtil.checkNotificationPermission(context)) {
                        startRequestPermission = true
                    }
                    if (mainVm.isDownloading) {
                        showToast(context.getString(R.string.new_version_is_downloading))
                        return@UpdateDialog mainVm.showUpdateDialog(null)
                    } else {
                        val url = it.link ?: ""
                        if (url.isEmpty()) return@UpdateDialog mainVm.showUpdateDialog(null)
                        val apkName = "${context.getString(R.string.app_name)}.apk"
                        if (activity == null) return@UpdateDialog mainVm.showUpdateDialog(null)
                        val manager = DownloadManager.Builder(activity).run {
                            apkUrl(url)
                            apkName(apkName)
                            smallIcon(R.drawable.ic_launcher)
                            onDownloadListener(object : OnDownloadListener {
                                override fun cancel() {
                                    mainVm.isDownloading = false
                                }

                                override fun done(apk: File) {
                                    mainVm.isDownloading = false
                                }

                                override fun downloading(max: Int, progress: Int) {
                                }

                                override fun error(e: Throwable) {
                                    mainVm.isDownloading = false
                                }

                                override fun start() {
                                    mainVm.isDownloading = true
                                }

                            })
                            build()
                        }
                        manager.download()
                        mainVm.showUpdateDialog(null)
                    }
                },
                onDismiss = {
                    mainVm.showUpdateDialog(null)
                }
            )
        }
        RequestNotifyDialog(
            notifyShow,
            onConfirm = {
                NotifyPermissionUtil.requestNotificationPermission(context)
            },
            onDismissRequest = {
                notifyShow = false
            },
        )

        if (showAreaNotAvailableDialog) {
            DTSingleBtnWarnDialog(
                title = null,
                content = R.string.ip_not_available,
                confirmText = R.string.ok,
                onCancel = {},
                onConfirm = {
                    finish()
                    android.os.Process.killProcess(android.os.Process.myPid())
                }
            )
        }
        RequestPermission(startRequestPermission = startRequestPermission) {
            notifyShow = true
        }
    }

    override fun onResume() {
        super.onResume()
        checkIpInfo()
    }
}