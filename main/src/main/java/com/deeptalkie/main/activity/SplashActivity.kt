package com.deeptalkie.main.activity

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.deeptalkie.main.R
import com.deeptalkie.main.bean.UserManager
import com.deeptalkie.main.compose.ui.components.DTVerticalSpacer
import com.deeptalkie.main.compose.ui.page.main.home.HomeRepo
import kotlinx.coroutines.delay

@SuppressLint("CustomSplashScreen")
class SplashActivity : BaseComposeActivity() {
    private val homeRepo = HomeRepo()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        homeRepo.preloadHomeData()
    }

    @Composable
    override fun ComposeContent() {
        LaunchedEffect(Unit) {
            delay(2000)
            launchApp()
        }

        Column(
            Modifier
                .fillMaxSize()
                .padding(bottom = 90.dp)
                .navigationBarsPadding(),
            verticalArrangement = Arrangement.Bottom,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Image(
                painterResource(R.drawable.splash_logo),
                null,
                Modifier
                    .width(239.dp)
                    .height(76.dp)

            )
            DTVerticalSpacer(4.dp)
            Text(
                stringResource(R.string.splash_page_tips),
                Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 37.dp),
                color = MaterialTheme.colorScheme.onSecondary,
                fontSize = 14.sp,
                fontWeight = FontWeight.W400,
                textAlign = TextAlign.Center,
            )
        }
    }

    // 让函数参数只执行一次
    private var isLaunched = false

    @Synchronized
    private fun singleLaunch(invoke: () -> Unit) {
        if (isLaunched) {
            return
        } else {
            isLaunched = true
            invoke()
        }
    }

    private fun launchApp() {
        val isFirstTimeLaunchApp = UserManager.isFirstTimeLaunchApp()
        val target = if (isFirstTimeLaunchApp) NewUserInfoSelectActivity::class.java
        else MainActivity::class.java
        singleLaunch {
            startActivity(Intent(this, target))
            finish()
        }
    }
}