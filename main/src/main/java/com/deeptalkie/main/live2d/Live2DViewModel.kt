package com.deeptalkie.main.live2d

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.media3.common.Player
import androidx.media3.exoplayer.ExoPlayer
import com.clevguard.utils.ext.logv
import com.deeptalkie.kidsguard.net.runHttp
import com.deeptalkie.main.App
import com.deeptalkie.main.api.deepTalkieApiFastJson
import com.deeptalkie.main.bean.AskResp
import com.deeptalkie.main.compose.ui.page.main.video.addHexAudio
import com.imyfone.live2d.LAppDefine
import com.imyfone.live2d.LAppDelegate
import com.imyfone.live2d.LAppLive2DManager
import kotlinx.coroutines.launch

class Live2DViewModel : ViewModel() {
    private val streamMsgRepo = StreamMsgRepo()
    private var currentMotionIndex = -1
    var saying by mutableStateOf(false)
        private set
    var showBg1 by mutableStateOf(true)
        private set

    var inputText by mutableStateOf("")
        private set
    var chatMsgs = mutableStateListOf<AskResp>()

    // 用于跟踪已播放的音频数据，避免重复播放
    private val playedAudioHashes = mutableSetOf<String>()

    private val voicePlayer by lazy {
        ExoPlayer.Builder(App.getInstance()).build()
    }

    init {
        viewModelScope.launch {
            voicePlayer.addListener(object : Player.Listener {
                override fun onIsPlayingChanged(isPlaying: Boolean) {
                    super.onIsPlayingChanged(isPlaying)
                    saying = isPlaying
                    LAppLive2DManager.instance.getModel()?.isSay = isPlaying
                }

                override fun onPlayerError(error: androidx.media3.common.PlaybackException) {
                    super.onPlayerError(error)
                    logv("语音播放错误: ${error.message}")
                    saying = false
                    LAppLive2DManager.instance.getModel()?.isSay = false
                    // 尝试清理并重新准备播放器
                    try {
                        voicePlayer.stop()
                        voicePlayer.clearMediaItems()
                        voicePlayer.prepare()
                    } catch (e: Exception) {
                        logv("重置播放器失败: ${e.message}")
                    }
                }

                override fun onPlaybackStateChanged(playbackState: Int) {
                    super.onPlaybackStateChanged(playbackState)
                    logv("播放器状态变化: $playbackState, 媒体项数量: ${voicePlayer.mediaItemCount}, 当前索引: ${voicePlayer.currentMediaItemIndex}")
                    when (playbackState) {
                        Player.STATE_ENDED -> {
                            logv("播放完成，媒体项数量: ${voicePlayer.mediaItemCount}")
                            // 播放完成后，检查是否还有更多媒体项
                            if (voicePlayer.mediaItemCount > 1) {
                                logv("还有更多媒体项，移除当前项并继续播放")
                                // 移除第一个已播放完成的项目
                                voicePlayer.removeMediaItem(0)
                                // 重新准备并播放下一个
                                voicePlayer.prepare()
                                voicePlayer.playWhenReady = true
                                voicePlayer.play()
                            } else if (voicePlayer.mediaItemCount == 1) {
                                logv("这是最后一个媒体项，播放完成")
                                voicePlayer.clearMediaItems()
                                saying = false
                                LAppLive2DManager.instance.getModel()?.isSay = false
                            } else {
                                logv("没有媒体项，播放完成")
                                saying = false
                                LAppLive2DManager.instance.getModel()?.isSay = false
                            }
                        }

                        Player.STATE_IDLE -> {
                            logv("播放器空闲状态")
                            saying = false
                            LAppLive2DManager.instance.getModel()?.isSay = false
                        }

                        Player.STATE_BUFFERING -> {
                            logv("播放器缓冲中")
                        }

                        Player.STATE_READY -> {
                            logv("播放器准备就绪")
                        }
                    }
                }
            })
        }
    }

    fun toggleSay() {
        saying = !saying
        LAppLive2DManager.instance.getModel()?.isSay = saying
    }

    fun execMotion() {
        val model = LAppLive2DManager.instance.getModel() ?: return
        if (currentMotionIndex >= model.tapBodyMotionCount - 1) {
            currentMotionIndex = 0
        } else {
            currentMotionIndex++
        }
        model.startMotion(
            LAppDefine.MotionGroup.TAP_BODY.id,
            currentMotionIndex,
            LAppDefine.Priority.FORCE.priority
        )
    }

    fun toggleBg() {
        showBg1 = !showBg1
    }

    fun switchNextModel() {
        saying = false
        LAppLive2DManager.instance.getModel()?.isSay = false
        LAppDelegate.instance.view?.isChangedModel = true
    }

    fun onTextInput(text: String) {
        inputText = text
    }

    fun sendMsg() {
        App.launch {
            runHttp {
                val ask = AskResp(System.currentTimeMillis(), inputText, inputText, 0, 0, true)
                onTextInput("")
                chatMsgs.add(ask)
                val resp = deepTalkieApiFastJson.ask(ask.content)
                logv("消息响应: ${resp.data?.content}")
                val msg = resp.getDataOrNull() ?: return@runHttp
                chatMsgs.add(msg)
                playHexMp3(msg.id, msg.audio)
            }
        }
    }

    fun sendMsgStream() {
        App.launch {
            runHttp {
                val ask = AskResp(System.currentTimeMillis(), inputText, inputText, 0, 0, true)
                onTextInput("")
                chatMsgs.add(ask)
                streamMsgRepo.sendMsgStream(ask.content) onEvent@{ event ->
                    when (event) {
                        is SSEEvent.Message -> {
                            val msg = event.msg
                            if (msg == null) {
                                logv("消息响应为空")
                                return@onEvent
                            }
                            logv("消息响应: ${msg.content}")

                            val existingMsg = chatMsgs.find { it.id == msg.id }
                            if (existingMsg != null) {
                                // 更新现有消息的音频数据（流式更新）
                                if (msg.audio.isNotBlank() && msg.audio != existingMsg.audio) {
                                    val audioHash = "${msg.id}_${msg.audio.hashCode()}"
                                    if (!playedAudioHashes.contains(audioHash)) {
                                        playedAudioHashes.add(audioHash)
                                        playHexMp3(msg.id, msg.audio)
                                        logv("播放新的音频片段: $audioHash")
                                    } else {
                                        logv("跳过重复的音频片段: $audioHash")
                                    }
                                }
                                return@onEvent
                            }

                            // 新消息
                            chatMsgs.add(msg)
                            if (msg.audio.isNotBlank()) {
                                val audioHash = "${msg.id}_${msg.audio.hashCode()}"
                                if (!playedAudioHashes.contains(audioHash)) {
                                    playedAudioHashes.add(audioHash)
                                    playHexMp3(msg.id, msg.audio)
                                    logv("播放新消息音频: $audioHash")
                                } else {
                                    logv("跳过重复的新消息音频: $audioHash")
                                }
                            }
                        }

                        is SSEEvent.Error -> {
                            logv("消息响应错误: ${event.t.stackTraceToString()}")
                        }

                        is SSEEvent.Close -> {
                            logv("消息响应关闭")
                        }

                        is SSEEvent.Open -> {
                            logv("消息响应打开")
                        }
                    }
                }
            }
        }
    }

    // 2. 播放函数
    fun playHexMp3(msgId: Long, hex: String) {
        viewModelScope.launch {
            try {
                // 定期清理旧的音频哈希记录
                cleanupOldAudioHashes()

                // 验证hex字符串
                if (hex.isBlank() || hex.length % 2 != 0) {
                    logv("无效的hex音频数据：$msgId")
                    return@launch
                }

                LAppLive2DManager.instance.getModel()?.startRandomMotion(
                    LAppDefine.MotionGroup.TAP_BODY.id,
                    LAppDefine.Priority.FORCE.priority
                )

                logv("添加语音播放：$msgId, hex长度: ${hex.length}")

                // 添加音频到播放队列
                val success = voicePlayer.addHexAudio(hex, "${msgId}_${System.currentTimeMillis()}")
                if (!success) {
                    logv("添加音频失败：$msgId")
                    return@launch
                }

                logv("当前播放状态: isPlaying=${voicePlayer.isPlaying}, playbackState=${voicePlayer.playbackState}, mediaItemCount=${voicePlayer.mediaItemCount}")

                // 如果播放器未在播放，启动播放
                if (!voicePlayer.isPlaying) {
                    when (voicePlayer.playbackState) {
                        Player.STATE_IDLE -> {
                            logv("播放器空闲，开始播放")
                            voicePlayer.prepare()
                            voicePlayer.playWhenReady = true
                            voicePlayer.play()
                        }

                        Player.STATE_ENDED -> {
                            logv("播放器已结束，重新开始播放")
                            voicePlayer.prepare()
                            voicePlayer.playWhenReady = true
                            voicePlayer.play()
                        }

                        Player.STATE_READY -> {
                            logv("播放器就绪，开始播放")
                            voicePlayer.playWhenReady = true
                            voicePlayer.play()
                        }

                        else -> {
                            logv("播放器状态: ${voicePlayer.playbackState}, 等待状态变化")
                        }
                    }
                } else {
                    logv("播放器正在播放，音频已添加到队列")
                }
            } catch (e: Exception) {
                logv("播放语音异常：${e.message}")
                saying = false
                LAppLive2DManager.instance.getModel()?.isSay = false
            }
        }
    }

    // 清理播放队列中已完成的媒体项
    private fun cleanupPlayedItems() {
        try {
            if (voicePlayer.mediaItemCount > 5) { // 保持队列不超过5个项目
                val currentIndex = voicePlayer.currentMediaItemIndex
                if (currentIndex > 0) {
                    for (i in 0 until currentIndex) {
                        voicePlayer.removeMediaItem(0)
                    }
                }
            }
        } catch (e: Exception) {
            logv("清理播放队列失败: ${e.message}")
        }
    }

    // 停止当前播放并清理
    private fun stopVoicePlayback() {
        try {
            voicePlayer.pause()
            voicePlayer.stop()
            voicePlayer.clearMediaItems()
            playedAudioHashes.clear()
            saying = false
            LAppLive2DManager.instance.getModel()?.isSay = false
            logv("播放器已停止并清理")
        } catch (e: Exception) {
            logv("停止播放失败: ${e.message}")
        }
    }

    // 清理旧的音频哈希记录，避免内存泄漏
    private fun cleanupOldAudioHashes() {
        if (playedAudioHashes.size > 100) {
            playedAudioHashes.clear()
            logv("清理音频哈希记录")
        }
    }

    override fun onCleared() {
        super.onCleared()
        stopVoicePlayback()
        voicePlayer.release()
    }
}