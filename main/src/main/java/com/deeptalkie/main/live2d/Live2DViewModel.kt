package com.deeptalkie.main.live2d

import android.content.Context
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.media3.common.Player
import androidx.media3.exoplayer.ExoPlayer
import com.clevguard.utils.ext.logv
import com.deeptalkie.kidsguard.net.runHttp
import com.deeptalkie.main.App
import com.deeptalkie.main.api.deepTalkieApiFastJson
import com.deeptalkie.main.bean.AskResp
import com.deeptalkie.main.compose.ui.page.main.video.addHexAudio
import com.imyfone.live2d.LAppDefine
import com.imyfone.live2d.LAppDelegate
import com.imyfone.live2d.LAppLive2DManager
import kotlinx.coroutines.launch

class Live2DViewModel : ViewModel() {
    private val streamMsgRepo = StreamMsgRepo()
    private var currentMotionIndex = -1
    var saying by mutableStateOf(false)
        private set
    var showBg1 by mutableStateOf(true)
        private set

    var inputText by mutableStateOf("")
        private set
    var chatMsgs = mutableStateListOf<AskResp>()

    private val voicePlayer by lazy {
        ExoPlayer.Builder(App.getInstance()).build()
    }

    init {
        viewModelScope.launch {
            voicePlayer.addListener(object : Player.Listener {
                override fun onIsPlayingChanged(isPlaying: Boolean) {
                    super.onIsPlayingChanged(isPlaying)
                    saying = isPlaying
                    LAppLive2DManager.instance.getModel()?.isSay = isPlaying
                }
            })
        }
    }

    fun toggleSay() {
        saying = !saying
        LAppLive2DManager.instance.getModel()?.isSay = saying
    }

    fun execMotion() {
        val model = LAppLive2DManager.instance.getModel() ?: return
        if (currentMotionIndex >= model.tapBodyMotionCount - 1) {
            currentMotionIndex = 0
        } else {
            currentMotionIndex++
        }
        model.startMotion(
            LAppDefine.MotionGroup.TAP_BODY.id,
            currentMotionIndex,
            LAppDefine.Priority.FORCE.priority
        )
    }

    fun toggleBg() {
        showBg1 = !showBg1
    }

    fun switchNextModel() {
        saying = false
        LAppLive2DManager.instance.getModel()?.isSay = false
        LAppDelegate.instance.view?.isChangedModel = true
    }

    fun onTextInput(text: String) {
        inputText = text
    }

    fun sendMsg() {
        App.launch {
            runHttp {
                val ask = AskResp(System.currentTimeMillis(), inputText, inputText, 0, 0, true)
                onTextInput("")
                chatMsgs.add(ask)
                val resp = deepTalkieApiFastJson.ask(ask.content)
                logv("消息响应: ${resp.data?.content}")
                val msg = resp.getDataOrNull() ?: return@runHttp
                chatMsgs.add(msg)
                playHexMp3(msg.id, msg.audio)
            }
        }
    }

    fun sendMsgStream(context: Context) {
        App.launch {
            runHttp {
                val ask = AskResp(System.currentTimeMillis(), inputText, inputText, 0, 0, true)
                onTextInput("")
                chatMsgs.add(ask)
                streamMsgRepo.sendMsgStream(ask.content) onEvent@{ event ->
                    when (event) {
                        is SSEEvent.Message -> {
                            val msg = event.msg
                            if (msg == null) {
                                logv("消息响应为空")
                                return@onEvent
                            }
                            logv("消息响应: ${msg.content}")
                            if (chatMsgs.any { it.id == msg.id }) {
                                // 处理流式音频
                                playHexMp3(msg.id, msg.audio)
                                return@onEvent
                            }
                            chatMsgs.add(msg)
                            playHexMp3(msg.id, msg.audio)
                        }

                        is SSEEvent.Error -> {
                            logv("消息响应错误: ${event.t.stackTraceToString()}")
                        }

                        is SSEEvent.Close -> {
                            logv("消息响应关闭")
                        }

                        is SSEEvent.Open -> {
                            logv("消息响应打开")
                        }
                    }
                }
            }
        }
    }

    // 2. 播放函数
    fun playHexMp3(msgId: Long, hex: String) {
        viewModelScope.launch {
            LAppLive2DManager.instance.getModel()?.startRandomMotion(
                LAppDefine.MotionGroup.TAP_BODY.id,
                LAppDefine.Priority.FORCE.priority
            )
            logv("添加语音播放：$msgId")
            voicePlayer.addHexAudio(hex, "${msgId}_${System.currentTimeMillis()}")
            if (!voicePlayer.isPlaying) {
                logv("开始播放语音")
                voicePlayer.prepare()
                voicePlayer.playWhenReady = true
                voicePlayer.play()
            }
        }
    }

    override fun onCleared() {
        super.onCleared()
        voicePlayer.pause()
        voicePlayer.stop()
        voicePlayer.clearMediaItems()
        voicePlayer.release()
    }
}