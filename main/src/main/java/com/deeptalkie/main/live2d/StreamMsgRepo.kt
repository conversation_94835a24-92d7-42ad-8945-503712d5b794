package com.deeptalkie.main.live2d

import com.clevguard.utils.BaseApplication
import com.clevguard.utils.ext.jsonAsOrNull
import com.deeptalkie.kidsguard.net.client.newOkHttpSSEClient
import com.deeptalkie.main.BuildConfig
import com.deeptalkie.main.bean.AskResp
import okhttp3.HttpUrl.Companion.toHttpUrlOrNull
import okhttp3.Request
import okhttp3.Response
import okhttp3.sse.EventSource
import okhttp3.sse.EventSourceListener
import okhttp3.sse.EventSources

class StreamMsgRepo {
    private val client = newOkHttpSSEClient(BaseApplication.getInstance())

    fun sendMsgStream(msg: String, onEvent: (SSEEvent) -> Unit) {
        val httpUrl = "${BuildConfig.BaseUrlDeepTalkie}ask".toHttpUrlOrNull()
        if (httpUrl == null) {
            return onEvent(SSEEvent.Error(IllegalArgumentException("httpUrl is null")))
        }
        val httpBuilder = httpUrl.newBuilder()
            .addQueryParameter("content", msg)
            .addQueryParameter("stream", "1")
            .addQueryParameter("time", "${System.currentTimeMillis() / 1000}")
            .addQueryParameter("reply_id", "0")
            .addQueryParameter("type", "1")
            .build()

        val request = Request.Builder()
            .url(httpBuilder)
            .build()

        EventSources.createFactory(client)
            .newEventSource(request, object : EventSourceListener() {
                override fun onOpen(eventSource: EventSource, response: Response) {
                    onEvent(SSEEvent.Open)
                }

                override fun onEvent(
                    eventSource: EventSource, id: String?, type: String?, data: String
                ) {
                    onEvent(SSEEvent.Message(id, type, data.jsonAsOrNull()))
                }

                override fun onClosed(eventSource: EventSource) {
                    onEvent(SSEEvent.Close)
                }

                override fun onFailure(
                    eventSource: EventSource, t: Throwable?, response: Response?
                ) {
                    onEvent(SSEEvent.Error(t ?: Exception("unknown error")))
                }
            })
    }
}

sealed interface SSEEvent {
    data object Open : SSEEvent
    data class Message(val id: String?, val type: String?, val msg: AskResp?) : SSEEvent
    data object Close : SSEEvent
    data class Error(val t: Throwable) : SSEEvent
}
