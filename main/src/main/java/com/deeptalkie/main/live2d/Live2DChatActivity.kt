package com.deeptalkie.main.live2d

import android.content.Context
import android.content.Intent
import androidx.activity.viewModels
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.IconButtonDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import com.clevguard.utils.ext.dpi
import com.clevguard.utils.ext.dpn
import com.deeptalkie.main.activity.BaseComposeActivity
import com.deeptalkie.main.compose.theme.Black50
import com.deeptalkie.main.compose.ui.components.DTHorizontalSpacer
import com.deeptalkie.main.compose.ui.components.DTPage
import com.deeptalkie.main.compose.ui.components.DTTextFieldNoBorder
import com.imyfone.live2d.Live2dView
import com.imyfone.live2d.R

class Live2DChatActivity : BaseComposeActivity() {
    companion object {
        fun start(context: Context) {
            context.startActivity(Intent(context, Live2DChatActivity::class.java))
        }
    }

    private val viewModel by viewModels<Live2DViewModel>()

    @Composable
    override fun ComposeContent() {
        DTPage {
            Column(Modifier.fillMaxSize()) {
                Box(
                    Modifier
                        .weight(1f)
                        .fillMaxWidth()
                ) {
                    ComposeLive2DView(viewModel.showBg1, Modifier.fillMaxSize())
                    LazyColumn(
                        Modifier
                            .height(200.dp)
                            .fillMaxWidth()
                            .background(Black50)
                            .align(Alignment.BottomCenter),
                        reverseLayout = true
                    ) {
                        items(viewModel.chatMsgs.reversed(), key = { it.id }) { msg ->
                            Text(msg.content, color = Color.White)
                        }
                    }
                }
                Row(
                    Modifier
                        .fillMaxWidth()
                        .background(Color(0xFF1B1926))
                        .padding(end = 24.dp)
                        .align(Alignment.End),
                    horizontalArrangement = Arrangement.End
                ) {
                    IconBtn(if (viewModel.saying) R.drawable.outline_stop_circle_24 else R.drawable.outline_play_circle_24) {
                        viewModel.toggleSay()
                    }
                    IconBtn(R.drawable.outline_directions_run_24) {
                        viewModel.execMotion()
                    }
                    IconBtn(R.drawable.outline_replace_image_24) {
                        viewModel.toggleBg()
                    }
                    IconBtn(R.drawable.outline_face_right_24) {
                        viewModel.switchNextModel()
                    }
                }
                Row(
                    Modifier
                        .fillMaxWidth()
                        .background(Color(0xFF1B1926))
                        .padding(horizontal = 16.dp, vertical = 10.dp)
                        .navigationBarsPadding(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    DTTextFieldNoBorder(
                        value = viewModel.inputText,
                        onValueChange = viewModel::onTextInput,
                        modifier = Modifier
                            .weight(1f)
                            .height(40.dp)
                            .background(Color(0xFF312F3A), RoundedCornerShape(16.dp))
                    )
                    DTHorizontalSpacer(10.dp)
                    IconBtn(com.deeptalkie.main.R.drawable.ic_send_msg) {
                        viewModel.sendMsgStream()
                    }
                }
            }
        }
    }

    @Composable
    private fun IconBtn(icon: Int, onClick: () -> Unit) {
        IconButton(
            onClick,
            colors = IconButtonDefaults.iconButtonColors(contentColor = Color.White)
        ) {
            Icon(
                painterResource(icon),
                null
            )
        }
    }
}

@Composable
fun ComposeLive2DView(showBg1: Boolean, modifier: Modifier = Modifier) {
    AndroidView(
        factory = { context ->
            Live2dView(context).apply {
                // 设置模型高度，宽度自适应保持比例
                setModelHeight(500.dpi)
                // 设置初始背景
                setBackgroundImage(R.drawable.bg1)
                // 设置模型位置在底部
                setModelVerticalOffset((-120).dpn)
            }
        },
        modifier,
        update = { live2dView ->
            // 根据状态切换背景
            val backgroundRes = if (showBg1) R.drawable.bg1 else R.drawable.bg2
            live2dView.setBackgroundImage(backgroundRes)
        }
    )
}