package com.deeptalkie.main.bean;

/**
 * creater:l<PERSON><PERSON><PERSON> on 2023/2/1 14:38
 */
public class VersionBean {
    private String ver;
    private String info;
    private String link;
    private String exinfo;
    private String plink;
    private boolean force_update;

    public VersionBean() {
    }

    public VersionBean(String ver, String info, String link, String exinfo, String plink, boolean force_update) {
        this.ver = ver;
        this.info = info;
        this.link = link;
        this.exinfo = exinfo;
        this.plink = plink;
        this.force_update = force_update;
    }

    public String getVer() {
        return ver;
    }

    public void setVer(String ver) {
        this.ver = ver;
    }

    public String getInfo() {
        return info;
    }

    public void setInfo(String info) {
        this.info = info;
    }

    public String getLink() {
        return link;
    }

    public void setLink(String link) {
        this.link = link;
    }

    public String getExinfo() {
        return exinfo;
    }

    public void setExinfo(String exinfo) {
        this.exinfo = exinfo;
    }

    public String getPlink() {
        return plink;
    }

    public void setPlink(String plink) {
        this.plink = plink;
    }

    public boolean isForce_update() {
        return force_update;
    }

    public void setForce_update(boolean force_update) {
        this.force_update = force_update;
    }

    @Override
    public String toString() {
        return "VersionBean{" +
                "ver='" + ver + '\'' +
                ", info='" + info + '\'' +
                ", link='" + link + '\'' +
                ", exinfo='" + exinfo + '\'' +
                ", plink='" + plink + '\'' +
                ", force_update=" + force_update +
                '}';
    }
}

