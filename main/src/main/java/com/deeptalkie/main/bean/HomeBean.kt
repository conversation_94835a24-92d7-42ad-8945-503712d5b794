package com.deeptalkie.main.bean

import com.deeptalkie.main.db.converters.AiRoleClassify
import com.deeptalkie.main.db.table.AIRole
import com.squareup.moshi.Json

data class HomeBean(
    @Json(name = "daily_recommend")
    val dailyRecommend: List<RoleInfo>?,
    @<PERSON><PERSON>(name = "user_created")
    val userCreated: List<RoleInfo>?,
)

data class RoleInfo(
    val id: Long,
    val name: String,
    val images: List<String>,
    val description: String,
    @<PERSON><PERSON>(name = "message_count")
    val messageCount: Long,
    @<PERSON><PERSON>(name = "created_at")
    val createdAt: String,
    @<PERSON><PERSON>(name = "updated_at")
    val updatedAt: String,
    val width: Int,
    val height: Int,
    @<PERSON><PERSON>(name = "voice_id")
    val voiceId: Int,
    @<PERSON><PERSON>(name = "is_hot")
    val isHot: Int,
    @<PERSON><PERSON>(name = "is_favorite")
    val isFavorite: Int,
) {
    fun toAIRole(classify: AiRoleClassify): AIRole {
        return AIRole(
            id = id,
            name = name,
            description = description,
            images = images,
            messageCount = messageCount,
            classify = classify,
            voiceId = voiceId,
            width = width,
            height = height,
            isHot = isHot,
            createdAt = createdAt,
            updatedAt = updatedAt,
        )
    }
}