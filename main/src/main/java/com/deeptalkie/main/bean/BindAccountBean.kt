package com.deeptalkie.main.bean

import androidx.annotation.Keep
import kotlinx.serialization.Serializable

/**
 *@decription:第三方账号绑定会员邮箱
 *@param:avatarUrl:第三方登录的用户头像 url，thirdEmail:第三方的账号,account:用户的邮箱，provider:登录的方式，google or facebook登录,displayName:展示的呢成
 *@return:
 *@author:l<PERSON><PERSON><PERSON>
 *@time:2025/5/23 9:47
 */
@Keep
@Serializable
data class BindAccountBean(
    val avatar: String,
    val thirdEmail: String,
    val account: String = "",
    val displayName: String,
    val provider: String,
    val state: String,
)