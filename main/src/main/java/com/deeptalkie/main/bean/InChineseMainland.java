package com.deeptalkie.main.bean;

public class InChineseMainland {
    private Boolean isInChineseMainland;

    @Override
    public String toString() {
        return "InChineseMainland{" +
                "isInChineseMainland=" + isInChineseMainland +
                '}';
    }

    public Boolean getInChineseMainland() {
        return isInChineseMainland;
    }

    public void setInChineseMainland(Boolean inChineseMainland) {
        isInChineseMainland = inChineseMainland;
    }

    public InChineseMainland() {
    }

    public InChineseMainland(Boolean isInChineseMainland) {
        this.isInChineseMainland = isInChineseMainland;
    }
}