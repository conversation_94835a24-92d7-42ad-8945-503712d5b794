package com.deeptalkie.main.bean

import androidx.compose.runtime.Stable
import com.deeptalkie.main.db.table.AIRole
import com.squareup.moshi.Json

@Stable
data class SearchResultBean(
    val id: Long,
    val name: String?,
    val images: List<String>,
    val description: String,
    @<PERSON><PERSON>(name = "message_count")
    val messageCount: Long,
    @<PERSON><PERSON>(name = "created_at")
    val createAt: String,
    @<PERSON><PERSON>(name = "updated_at")
    val updateAt: String,
    val width: Int,
    val height: Int,
    @<PERSON><PERSON>(name = "voice_id")
    val voiceId: Int,
    @<PERSON><PERSON>(name = "is_hot")
    val isHot: Int,
    @<PERSON><PERSON>(name = "is_favorite")
    val isFavorite: Int,
) {
    fun toAIRole(): AIRole {
        return AIRole(
            id = id,
            name = name ?: "",
            images = images,
            description = description,
            messageCount = messageCount,
            voiceId = voiceId,
            width = width,
            height = height,
            isHot = isHot,
            createdAt = createAt,
            updatedAt = updateAt,
        )
    }
}

fun emptySearchResultBean(name: String): SearchResultBean {
    return SearchResultBean(
        id = -1,
        name = name,
        images = emptyList(),
        description = "",
        messageCount = 0,
        createAt = "",
        updateAt = "",
        width = 0,
        height = 0,
        voiceId = 0,
        isHot = 0,
        isFavorite = 0,
    )
}