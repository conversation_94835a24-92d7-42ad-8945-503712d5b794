package com.deeptalkie.main.bean;

public class TextModerationReason {
    //内容语言
    private String detectedLanguage;
    //翻译后内容
    private String translatedContent;
    //命中等级：medium、high、low，没命中则没此字段
    private String riskLevel;
    //细分标签，没命中则没此字段
    private String riskTips;
    //命中风险内容，没命中则没此字段
    private String riskWords;

    public TextModerationReason() {
    }

    public TextModerationReason(String detectedLanguage, String translatedContent, String riskWords, String riskTips, String riskLevel) {
        this.detectedLanguage = detectedLanguage;
        this.translatedContent = translatedContent;
        this.riskWords = riskWords;
        this.riskTips = riskTips;
        this.riskLevel = riskLevel;
    }

    public String getDetectedLanguage() {
        return detectedLanguage;
    }

    public void setDetectedLanguage(String detectedLanguage) {
        this.detectedLanguage = detectedLanguage;
    }

    public String getRiskLevel() {
        return riskLevel;
    }

    public void setRiskLevel(String riskLevel) {
        this.riskLevel = riskLevel;
    }

    public String getRiskTips() {
        return riskTips;
    }

    public void setRiskTips(String riskTips) {
        this.riskTips = riskTips;
    }

    public String getRiskWords() {
        return riskWords;
    }

    public void setRiskWords(String riskWords) {
        this.riskWords = riskWords;
    }

    public String getTranslatedContent() {
        return translatedContent;
    }

    public void setTranslatedContent(String translatedContent) {
        this.translatedContent = translatedContent;
    }

    @Override
    public String toString() {
        return "TextModerationReason{" +
                "detectedLanguage='" + detectedLanguage + '\'' +
                ", translatedContent='" + translatedContent + '\'' +
                ", riskLevel='" + riskLevel + '\'' +
                ", riskTips='" + riskTips + '\'' +
                ", riskWords='" + riskWords + '\'' +
                '}';
    }
}
