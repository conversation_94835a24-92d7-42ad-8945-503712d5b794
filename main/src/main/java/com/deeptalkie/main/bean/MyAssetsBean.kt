package com.deeptalkie.main.bean

import androidx.annotation.Keep
import com.alibaba.fastjson.annotation.JSONField
import com.deeptalkie.main.R
import com.deeptalkie.main.utils.getString

/**
 *creater:l<PERSON><PERSON><PERSON> on 2025/6/19 09:59
 */
@Keep
data class AssetsBean(val list: List<MyAssetsBean>)

@Keep
data class MyAssetsBean(
    @JSONField(name = "id") val id: Long,
    @JSONField(name = "name") val name: String,
    @JSONField(name = "images") val images: List<String>,
    @JSONField(name = "description") val description: String,
    @JSONField(name = "message_count") val messageCount: Int,
    @JSONField(name = "created_at") val createdAt: String,
    @JSONField(name = "updated_at") val updatedAt: String,
    @JSONField(name = "width") val width: Int,
    @JSONField(name = "height") val height: Int,
    @JSONField(name = "voice_id") val voiceId: Int,
    @JSONField(name = "base_message_count") val baseMessageCount: Int,
    @JSONField(name = "approval_status") val approvalStatus: Int, //  0-待审核，1-审核通过，2-审核未通过
    @JSONField(name = "is_public") val isPublic: Int,
    @JSONField(name = "is_favorite") val isFavorite: Int
) {
    fun getRatio(): Float {
        if (height == 0) return 768 / 1344f
        val value = (width / height).toFloat()
        return if (value == 0f) {
            768 / 1344f
        } else {
            value
        }
    }

    val approvalStatusText: String?
        get() = when (approvalStatus) {
            0 -> getString(R.string.under_review)
            1 -> getString(R.string.review_passed)
            2 -> getString(R.string.review_failed)
            else -> null
        }
}