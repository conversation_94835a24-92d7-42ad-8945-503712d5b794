package com.deeptalkie.main.bean

import androidx.annotation.Keep
import com.alibaba.fastjson.annotation.JSONField
import com.deeptalkie.main.db.table.AIRole
import com.deeptalkie.main.db.table.AIRoleTag

/**
 *creater:l<PERSON><PERSON><PERSON> on 2025/6/18 15:52
 */
@Keep
data class RoleDetailBean(
    @JSONField(name = "id") val id: Long,
    @JSONField(name = "name") val name: String,
    @JSONField(name = "description") val description: String,
    @JSONField(name = "images") val images: List<String>,
    @JSONField(name = "is_favorite") val isFavorite: Int,
    @J<PERSON>NField(name = "videos") val videos: List<String>,
    @JSONField(name = "body") val body: String,
    @JSONField(name = "age") val age: Int,
    @JSONField(name = "language") val language: String,
    @JSONField(name = "occupation") val occupation: String,
    @JSONField(name = "message_count") val messageCount: Long,
    @JSONField(name = "created_at") val createdAt: String,
    @J<PERSON><PERSON>ield(name = "updated_at") val updatedAt: String,
    @J<PERSON>NField(name = "voice_id") val voiceId: Int,
    @JSONField(name = "is_hot") val isHot: Int,
    @JSONField(name = "is_public") val isPublic: Int,
    @JSONField(name = "approval_status") val approvalStatus: Int, //  0-待审核，1-审核通过，2-审核未通过
    @JSONField(name = "tags") val tags: List<TagBean>
) {
    fun toAIRole(): AIRole {
        return AIRole(
            id = id,
            name = name,
            description = description,
            images = images,
            messageCount = messageCount,
            voiceId = voiceId,
            width = -1,
            height = -1,
            isHot = isHot,
            createdAt = createdAt,
            updatedAt = updatedAt,
            approvalStatus = approvalStatus,
        )
    }
}

@Keep
data class TagBean(
    @JSONField(name = "id") val id: Long,
    @JSONField(name = "name") val name: String
) {
    fun toAIRoleTag(): AIRoleTag {
        return AIRoleTag(
            id = id,
            name = name,
            status = 1,
            createdAt = "",
            updatedAt = ""
        )
    }
}