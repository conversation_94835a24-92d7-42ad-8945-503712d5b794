package com.deeptalkie.main.bean;

import android.os.Parcel;
import android.os.Parcelable;

public class ConfirmOrderBean implements Parcelable  {
    public int status;
    public String order_no;
    public String currency_code;
    public String sku_name;
    public String order_price;
    public int is_subscribe;//是否订阅  0=否 1=是

    protected ConfirmOrderBean(Parcel in) {
        status = in.readInt();
        order_no = in.readString();
        currency_code = in.readString();
        sku_name = in.readString();
        order_price = in.readString();
        is_subscribe = in.readInt();
    }

    public static final Creator<ConfirmOrderBean> CREATOR = new Creator<ConfirmOrderBean>() {
        @Override
        public ConfirmOrderBean createFromParcel(Parcel in) {
            return new ConfirmOrderBean(in);
        }

        @Override
        public ConfirmOrderBean[] newArray(int size) {
            return new ConfirmOrderBean[size];
        }
    };

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(status);
        dest.writeString(order_no);
        dest.writeString(currency_code);
        dest.writeString(sku_name);
        dest.writeString(order_price);
        dest.writeInt(is_subscribe);
    }
}
