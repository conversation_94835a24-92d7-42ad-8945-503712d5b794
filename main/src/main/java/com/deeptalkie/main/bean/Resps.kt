package com.deeptalkie.main.bean

import com.alibaba.fastjson.annotation.JSONField
import com.deeptalkie.main.db.result.AIRoleWithTags
import com.deeptalkie.main.db.table.AIRole
import com.deeptalkie.main.db.table.AIRoleTag
import com.deeptalkie.main.db.table.MsgRecord
import com.deeptalkie.main.db.table.MsgSendStatus
import com.deeptalkie.main.db.table.UserAIRole
import com.deeptalkie.main.db.table.Voice
import com.squareup.moshi.Json

data class SessionBean(
    @<PERSON><PERSON>(name = "role_id")
    val roleId: Long,
    @<PERSON><PERSON>(name = "session_id")
    val sessionId: Long,
    @<PERSON><PERSON>(name = "role_name")
    val roleName: String?,
    val images: List<String>,
    @<PERSON>son(name = "last_message_at")
    val lastMessageAt: Long,
    @J<PERSON>(name = "last_message")
    val lastMessage: String,
    @<PERSON><PERSON>(name = "last_message_id")
    val lastMessageId: Long,
    @<PERSON><PERSON>(name = "pin_to_top_time")
    val sessionTopUpAt: Long? = null,
    @<PERSON><PERSON>(name = "voice_id")
    val voiceId: Int,
    @<PERSON><PERSON>(name = "approval_status") //  0-待审核，1-审核通过，2-审核未通过
    val approvalStatus: Int,
) {
    fun toUserAIRole(userId: String): UserAIRole {
        return UserAIRole(
            userId = userId,
            roleId = roleId,
            sessionId = sessionId,
            lastMsg = lastMessage,
            lastMsgAt = lastMessageAt,
            sessionTopUpAt = sessionTopUpAt,
        )
    }

    fun toAIRole(): AIRole {
        return AIRole(
            id = roleId,
            name = roleName ?: "",
            description = "",
            images = images,
            messageCount = 0,
            voiceId = voiceId,
            width = -1,
            height = -1,
            isHot = -1,
            createdAt = "",
            updatedAt = "",
            approvalStatus = approvalStatus,
        )
    }
}

data class RequestChatResult(
    @Json(name = "session_id")
    val sessionId: Long,
    @Json(name = "role_name")
    val roleName: String?,
    val images: List<String>,
)

data class ChatMessagesResult(
    @Json(name = "id")
    val msgId: Long,
    @Json(name = "speaker_type")
    val speakerType: Int,
    val type: Int,
    val content: String,
    @Json(name = "created_at")
    val createdAt: Long,
    @Json(name = "reply_id")
    val replyId: Long,
    @Json(name = "is_lock")
    val isLock: Int,
    val reply: ChatMessagesResult?,
    val prompt: String? = null,
) {
    fun toMsgRecord(userId: String, roleId: Long, videoTime: String): MsgRecord {
        return MsgRecord(
            msgId = msgId,
            roleId = roleId,
            speakerType = speakerType,
            type = type,
            content = content,
            createdAt = createdAt,
            userId = userId,
            isRead = true,
            replyId = replyId,
            isLock = isLock,
            reply = reply?.content,
            videoTime = videoTime,
            prompt = prompt.orEmpty(),
            sendStatus = MsgSendStatus.Success
        )
    }
}

data class ExploreRoleBean(
    val id: Long,
    val name: String,
    val images: List<String>,
    val description: String,
    @Json(name = "message_count")
    val messageCount: Long,
    val width: Int,
    val height: Int,
    @Json(name = "created_at")
    val createdAt: String,
    @Json(name = "updated_at")
    val updatedAt: String,
    @Json(name = "voice_id")
    val voiceId: Int,
    @Json(name = "is_hot")
    val isHot: Int,
    @Json(name = "is_favorite")
    val isFavorite: Int,
) {
    fun toAIRole(): AIRole {
        return AIRole(
            id = id,
            name = name,
            description = description,
            images = images,
            messageCount = messageCount,
            voiceId = voiceId,
            width = width,
            height = height,
            isHot = isHot,
            createdAt = createdAt,
            updatedAt = updatedAt,
        )
    }
}

data class AIReplyResp(
    val messages: List<String>?
)

data class RandomChatData(
    @Json(name = "role_id")
    val roleId: Long,
    @Json(name = "session_id")
    val sessionId: Long,
    @Json(name = "role_name")
    val roleName: String,
    val images: List<String>,
    @Json(name = "voice_id")
    val voiceId: Int,
    @Json(name = "is_favorite")
    val isFavorite: Int,
) {
    fun toAIRole(): AIRole {
        return AIRole(
            id = roleId,
            name = roleName,
            description = "",
            images = images,
            messageCount = 0,
            voiceId = voiceId,
            width = -1,
            height = -1,
            isHot = -1,
            createdAt = "",
            updatedAt = ""
        )
    }
}

data class FavoriteRes(
    @Json(name = "is_favorite")
    val isFavorite: Int
)

data class SendMsgResp(
    val id: Long
)

data class AwsConfigResp(
    val data: String
)

data class AIRoleDescResp(
    val description: String
)

/**
 * {
 * 		"id": 0,
 * 		"type": 0, // 分类
 * 		"voice": "", // 接口配音名称
 * 		"show_name": "", //显示名称
 * 		"avatar": "", // 头像
 * 		"lang_id": 0, // 语言标识
 * 		"cate_id": 0, // 分类标识
 * 		"sex": 1, // 性别：1-男，2-女
 * 		"advanced": 1, // 是否高级配音：1-是，2-否
 * 		"recommended": 0, // 是否推荐配音：1-是，2-否
 * 		"category_name": "", // 分类名称
 * 		"default": 0, // 是否默认配音：1-是，2-否
 * 		"age": 0 // 年龄：1-儿童，2-普通，3-年长
 * 	}
 */
data class VoiceBean(
    val id: Long,
    val type: Int,
    val voice: String,
    @Json(name = "show_name")
    val showName: String,
    val avatar: String,
    @Json(name = "lang_id")
    val langId: Long,
    @Json(name = "cate_id")
    val cateId: Long,
    val sex: Int,
    val advanced: Int,
    val recommended: Int,
    @Json(name = "category_name")
    val categoryName: String,
    val default: Int,
    val age: Int,
) {
    fun toVoice(): Voice {
        return Voice(
            id = id,
            type = type,
            voice = voice,
            showName = showName,
            avatar = avatar,
            langId = langId,
            cateId = cateId,
            sex = sex,
            advanced = advanced,
            recommended = recommended,
            categoryName = categoryName,
            default = default,
            age = age,
        )
    }
}

data class VoiceLink(
    @Json(name = "audio_link")
    val audioLink: String
)

data class AIRoleTagsResp(
    val list: List<AIRoleTagItem>
)

data class CreateAIRoleResp(
    val id: Long
)

data class AIRoleTagItem(
    val id: Long,
    val name: String,
    val status: Int,
    @Json(name = "created_at")
    val createdAt: String,
    @Json(name = "updated_at")
    val updatedAt: String,
) {
    fun toAIRoleTag(): AIRoleTag {
        return AIRoleTag(
            id = id,
            name = name,
            status = status,
            createdAt = createdAt,
            updatedAt = updatedAt,
        )
    }
}

data class CreateTagResp(
    val id: Long
)

data class AIImageStyleListResp(
    val list: List<AIImageStyle>
)

data class AIRoleWithTagsResp(
    val id: Long,
    val name: String,
    val images: List<String>,
    val description: String,
    @Json(name = "message_count")
    val messageCount: Long,
    @Json(name = "is_favorite")
    val isFavorite: Int,
    @Json(name = "voice_id")
    val voiceId: Int,
    val width: Int,
    val height: Int,
    @Json(name = "is_hot")
    val isHot: Int,
    @Json(name = "is_show_model")
    val isShowModel: Int,
    @Json(name = "modelName")
    val modelName: String,
    @Json(name = "type")
    val type: Int, // 1-系统创建角色，2-用户创建消息
    @Json(name = "created_at")
    val createdAt: String,
    @Json(name = "updated_at")
    val updatedAt: String,
    @Json(name = "tags")
    val tags: List<SimpleAIRoleTagItem>,
) {
    fun toAIRole(): AIRole {
        return AIRole(
            id = id,
            name = name,
            description = description,
            images = images,
            messageCount = messageCount,
            voiceId = voiceId,
            width = width,
            height = height,
            isHot = isHot,
            createdAt = createdAt,
            updatedAt = updatedAt,
            isShowModel = isShowModel,
            modelName = modelName,
            type = type,
        )
    }

    fun toAIRoleWithTags(): AIRoleWithTags {
        return AIRoleWithTags(
            aiRole = toAIRole(),
            tags = tags.map { it.toAIRoleTag() }
        )
    }
}

data class SimpleAIRoleTagItem(
    val id: Long,
    val name: String,
) {
    fun toAIRoleTag(): AIRoleTag {
        return AIRoleTag(
            id = id,
            name = name,
            status = 1,
            createdAt = "",
            updatedAt = "",
        )
    }
}

/**
 * {
 *   "id": 1,
 *   "name": "Gibli",
 *   "path": "https://files.deeptalkie.com/deeptalkie/imagestyle/89223d5a1ada9c08f3b7412c9ab9e4ff.jpg",
 *   "prompt": "Gibli",
 *   "sort": 500,
 *   "created_at": "2025-06-05 15:15:38",
 *   "updated_at": "2025-06-09 19:01:24",
 *   "girl_path": "https://files.deeptalkie.com/deeptalkie/imagestyle/4208aa14f1c55772b2810e7f1c196b4c.jpg",
 *   "lang_name": ""
 * }
 */
data class AIImageStyle(
    val id: Long,
    val name: String,
    val path: String,
    val prompt: String,
    val sort: Int,
    @Json(name = "created_at")
    val createdAt: String,
    @Json(name = "updated_at")
    val updatedAt: String,
    @Json(name = "girl_path")
    val girlPath: String,
    @Json(name = "lang_name")
    val langName: String,
)

/**
 * {
 *    "id": 5,
 *    "status": 2,
 *    "result": [
 *      "https://p2-kling.klingai.com/bs2/upload-ylab-stunt/22412fd94a3eecbe9e1279936694506d.png?x-kcdn-pid=112452",
 *      "https://p2-kling.klingai.com/bs2/upload-ylab-stunt/9d4fb9b6b8e593d7d3c4b22084c157e4.png?x-kcdn-pid=112452",
 *      "https://p2-kling.klingai.com/bs2/upload-ylab-stunt/ed5764e6359a92f9db6236c44ec30af1.png?x-kcdn-pid=112452",
 *      "https://p2-kling.klingai.com/bs2/upload-ylab-stunt/e68d0600a314d3747a3542ba3a1e8ec3.png?x-kcdn-pid=112452"
 *    ],
 *    "reason": "",
 *    "error_code": 0,
 *    "created_at": "2025-06-04T11:11:57.000000Z",
 *    "updated_at": "2025-06-04T11:43:48.000000Z"
 * }
 */
data class AIImageResult(
    val id: Long,
    // status：0 等待处理 1 处理中 2 成功 3 失败
    val status: Int,
    val result: List<String>?,
    val reason: String,
    // error_code: 1 未通过风险控制，52001 超时
    @Json(name = "error_code")
    val errorCode: Int,
    @Json(name = "created_at")
    val createdAt: String,
    @Json(name = "updated_at")
    val updatedAt: String,
)

data class ListData<T>(
    val list: List<T>,
)

data class SwData(
    val token: String,
    @JSONField(name = "app_id")
    val appId: String,
    val name: String
)

data class SwAgentData(
    @JSONField(name = "agent_id")
    val agentId: String,
    @JSONField(name = "create_ts")
    val createTs: Long,
    val status: String
)

data class AskResp(
    val id: Long,
    val content: String,
    val audio: String,
    val time: Long,
    val type: Int,
    val isSelf: Boolean = false,
)