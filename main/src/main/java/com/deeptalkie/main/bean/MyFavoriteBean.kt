package com.deeptalkie.main.bean

import androidx.annotation.Keep
import com.alibaba.fastjson.annotation.JSONField

/**
 *creater:<PERSON><PERSON><PERSON><PERSON> on 2025/6/19 10:06
 */
@Keep
data class FavoriteBean(@J<PERSON><PERSON>ield(name = "list") val list: List<MyFavoriteBean>)

@Keep
data class MyFavoriteBean(
    @JSONField(name = "id") val id: Long,
    @JSONField(name = "name") val name: String,
    @JSONField(name = "images") val images: List<String>,
    @JSONField(name = "description") val description: String,
    @J<PERSON>NField(name = "message_count") val messageCount: Int,
    @<PERSON><PERSON><PERSON>ield(name = "created_at") val createdAt: String,
    @JSONField(name = "updated_at") val updatedAt: String,
    @JSONField(name = "width") val width: Int,
    @<PERSON><PERSON><PERSON>ield(name = "height") val height: Int,
    @JSONField(name = "voice_id") val voiceId: Int,
    @JSONField(name = "is_favorite") val isFavorite: Int
) {
    fun getRatio(): Float {
        if (height == 0) return 768 / 1344f
        val value = (width / height).toFloat()
        return if (value == 0f) {
            768 / 1344f
        } else {
            value
        }
    }
}