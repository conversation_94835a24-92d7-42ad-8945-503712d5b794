package com.deeptalkie.main.compose.ui.page.main.video

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import com.deeptalkie.main.R
import com.deeptalkie.main.compose.navigation.MainRoute
import com.deeptalkie.main.compose.ui.components.DTLoading
import com.deeptalkie.main.compose.ui.components.DTSlider
import com.deeptalkie.main.compose.ui.page.main.chats.ComposeExoPlayer

@Composable
fun VideoPlayPage(
    route: MainRoute.VideoPlay,
    onBack: () -> Unit,
) {
    val playerController = rememberExoPlayerController(route.url)

    LaunchedEffect(Unit) {
        playerController.play()
    }

    ConstraintLayout(Modifier.fillMaxSize()) {
        val (videoRef, backRef, playRef, progressRef) = createRefs()

        ComposeExoPlayer(
            playerController,
            Modifier
                .fillMaxWidth()
                .aspectRatio(route.width / route.height.toFloat())
                .constrainAs(videoRef) {
                    centerVerticallyTo(parent)
                    centerHorizontallyTo(parent)
                }
        )

        Image(
            painterResource(R.drawable.ic_back2),
            null,
            Modifier
                .statusBarsPadding()
                .padding(top = 10.dp)
                .size(36.dp)
                .constrainAs(backRef) {
                    start.linkTo(parent.start, 16.dp)
                    top.linkTo(parent.top)
                }
                .clip(CircleShape)
                .clickable(onClick = onBack),
        )

        DTLoading(playerController.playState.isLoading()) { }

        Icon(
            if (playerController.playState.isPlaying()) painterResource(R.drawable.ic_pause)
            else painterResource(R.drawable.ic_play),
            null,
            Modifier
                .size(44.dp)
                .clip(CircleShape)
                .clickable {
                    if (playerController.playState.isPlaying()) {
                        playerController.pause()
                    } else {
                        playerController.play()
                    }
                }
                .padding(10.dp)
                .constrainAs(playRef) {
                    start.linkTo(parent.start)
                    bottom.linkTo(parent.bottom, 34.dp)
                },
            tint = MaterialTheme.colorScheme.onPrimary
        )

        var draggedValue by remember { mutableFloatStateOf(playerController.progress) }

        DTSlider(
            value = playerController.progress,
            draggedValue = draggedValue,
            onValueChange = { value ->
                draggedValue = value
            },
            onValueChangeFinished = {
                playerController.seekToPercent(draggedValue)
            },
            modifier = Modifier
                .constrainAs(progressRef) {
                    start.linkTo(playRef.end)
                    end.linkTo(parent.end, 24.dp)
                    width = Dimension.fillToConstraints
                    centerVerticallyTo(playRef)
                }
        )
    }
}