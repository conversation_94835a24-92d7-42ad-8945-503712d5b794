package com.deeptalkie.main.compose.ui.page.chat

import androidx.room.Transaction
import com.clevguard.utils.ext.logv
import com.deeptalkie.kidsguard.net.Response
import com.deeptalkie.kidsguard.net.getSuccessData
import com.deeptalkie.kidsguard.net.runHttp
import com.deeptalkie.kidsguard.net.ws.WsData
import com.deeptalkie.main.Membership
import com.deeptalkie.main.R
import com.deeptalkie.main.bean.AIReplyResp
import com.deeptalkie.main.db.result.MsgWithReply
import com.deeptalkie.main.db.table.MsgRecord
import com.deeptalkie.main.db.table.MsgSendStatus
import com.deeptalkie.main.db.table.newMyTextMsg
import com.deeptalkie.main.ext.showToast
import com.deeptalkie.main.ext.toMsgRecord
import com.deeptalkie.main.repo.BaseDeepTalkieRepo
import com.deeptalkie.main.utils.PromptUtils
import com.deeptalkie.main.utils.getString
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.withContext

class MsgRecordRepo : BaseDeepTalkieRepo() {
    fun queryAIRoleSession(userId: String, roleId: Long) =
        aiRoleSessionDao.querySessionByIds(userId, roleId)

    fun queryCurrentSessionMsg(roleId: Long): Flow<List<MsgWithReply>> {
        val userId = Membership.getUserId() ?: ""
        return msgRecordDao.queryCurrentSessionMsg(userId, roleId)
    }

    suspend fun sendTextMsg(
        userId: String,
        roleId: Long,
        sessionId: Long,
        text: String,
        replyId: Long = 0,
        reply: String? = null,
        type: Int, // 1-普通文字消息，2-请求图片
    ) = coroutineScope {
        val msgRecord = newMyTextMsg(
            roleId = roleId,
            text = text,
            userId = userId,
            replyId = replyId,
            reply = reply,
            msgSendType = type
        )

        val localId = insertMyMsg(msgRecord)

        sendMsgRemote(sessionId, msgRecord.copy(id = localId), type)
    }

    suspend fun onRetrySendMsg(sessionId: Long, msgRecord: MsgRecord) {
        val sendType = msgRecord.msgSendType ?: return
        msgRecordDao.updateMsgStatus(MsgSendStatus.Sending, msgRecord.id)
        sendMsgRemote(sessionId, msgRecord, sendType)
    }

    private suspend fun sendMsgRemote(
        sessionId: Long,
        msgRecord: MsgRecord,
        type: Int, // 1-普通文字消息，2-请求图片
    ) {
        val checkSensitiveRes = PromptUtils.checkSensitive(msgRecord.content)
        if (checkSensitiveRes?.labels == "regional") {
            val string = getString(R.string.content_sensitive)
            withContext(Dispatchers.Main) { showToast(string) }
            onSendMsgFail(msgRecord.id)
            return
        }
        val remoteId = remoteApi.sendMsg(
            sessionId, msgRecord.content, msgRecord.replyId, type
        ).getSuccessData()?.id
        if (remoteId == null) {
            onSendMsgFail(msgRecord.id)
            return
        }
        onSendMsgSuccess(msgRecord.userId, msgRecord.roleId, msgRecord.id, remoteId)
    }

    private suspend fun onSendMsgFail(localId: Long) {
        msgRecordDao.updateMsgStatus(MsgSendStatus.Failed, localId)
    }

    @Transaction
    private suspend fun onSendMsgSuccess(
        userId: String, roleId: Long, localId: Long, remoteId: Long
    ) {
        logv("发送消息成功，msgId=$remoteId")
        msgRecordDao.updateMsgIdAndStatus(remoteId, MsgSendStatus.Success, localId)
        msgRecordDao.updateAIRoleMsg(userId, roleId)
    }

    @Transaction
    private suspend fun insertMyMsg(msgRecord: MsgRecord): Long {
        val localId = msgRecordDao.insertOrIgnore(msgRecord)
        msgRecordDao.updateAIRoleMsg(msgRecord.userId, msgRecord.roleId)
        return localId
    }

    suspend fun saveWebSocketMsg(wsData: WsData) {
        val userId = Membership.getUserId() ?: return
        val videoTime = wsData.loadVideoTime()
        msgRecordDao.upsertOne(wsData.toMsgRecord(userId, videoTime).apply {
            loadImage()
        })
    }

    suspend fun setRoleMsgIsRead(userId: String, roleId: Long) =
        msgRecordDao.setRoleMsgIsRead(userId, roleId)

    suspend fun deleteMsg(userId: String, roleId: Long, msgId: Long) {
        msgRecordDao.delMsg(userId, roleId, msgId)
        msgRecordDao.updateAIRoleMsg(userId, roleId)

        runHttp { remoteApi.deleteMsg(msgId) }
    }

    suspend fun getInspirations(sessionId: Long, content: String): Response<AIReplyResp>? {
        return runHttp { remoteApi.inspiration(sessionId, content) }
    }

    suspend fun unlockAllMsg(userId: String) {
        msgRecordDao.unlockAllMsg(userId)
    }
}