package com.deeptalkie.main.compose.ui.dialog

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import com.deeptalkie.main.R
import com.deeptalkie.main.compose.theme.Black
import com.deeptalkie.main.compose.theme.SocialBlue
import com.deeptalkie.main.compose.theme.White
import com.deeptalkie.main.compose.ui.components.DTButton

/**
 *creater:linjinhao on 2025/6/20 15:30
 */
@Composable
fun RequestNotifyDialog(show: Boolean, onConfirm: () -> Unit, onDismissRequest: () -> Unit) {
    if (show) {
        Dialog(onDismissRequest = { onDismissRequest() }) {
            Column(
                modifier = Modifier
                    .background(White, shape = RoundedCornerShape(22.dp))
                    .fillMaxWidth(),
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                Spacer(modifier = Modifier.height(32.dp))
                Text(
                    stringResource(R.string.request_notify),
                    style = TextStyle(
                        textAlign = TextAlign.Center,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.W500,
                        color = Black
                    ),
                    modifier = Modifier
                        .padding(horizontal = 16.dp)
                        .fillMaxWidth(),
                )

                Spacer(modifier = Modifier.height(32.dp))

                Row(
                    modifier = Modifier
                        .fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly,
                ) {
                    DTButton(
                        text = R.string.yes,
                        modifier = Modifier.size(120.dp, 42.dp),
                        textStyle = TextStyle(fontSize = 14.sp, fontWeight = FontWeight.W600),
                        shape = RoundedCornerShape(100.dp),
                        contentColor = SocialBlue,
                        border = BorderStroke(
                            width = 2.dp,
                            color = SocialBlue
                        )
                    ) {
                        onConfirm()
                        onDismissRequest()
                    }
                    DTButton(
                        text = R.string.cancel,
                        modifier = Modifier.size(120.dp, 42.dp),
                        textStyle = TextStyle(fontSize = 14.sp, fontWeight = FontWeight.W600),
                        shape = RoundedCornerShape(100.dp),
                        contentColor = White,
                        containerColor = SocialBlue
                    ) { onDismissRequest() }
                }
                Spacer(modifier = Modifier.height(24.dp))
            }
        }
    }
}