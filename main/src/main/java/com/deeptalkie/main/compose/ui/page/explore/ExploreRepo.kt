package com.deeptalkie.main.compose.ui.page.explore

import androidx.room.Transaction
import com.deeptalkie.kidsguard.net.runHttp
import com.deeptalkie.main.config.loadImages
import com.deeptalkie.main.db.table.AIRoleTagRelation
import com.deeptalkie.main.repo.BaseDeepTalkieRepo
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope

class ExploreRepo : BaseDeepTalkieRepo() {
    @Transaction
    suspend fun loadAIRolesForTag(tagId: Long, module: String, page: Int, pageSize: Int) =
        coroutineScope {
            val resp = runHttp { dtApi.getRolesForTag(tagId, module, page, pageSize) }
                ?: return@coroutineScope null
            val roles = resp.getDataOrNull() ?: return@coroutineScope null

            val insetDataJob = async {
                aiRoleDao.upsertAll(roles.map { it.toAIRole() })
                aiRoleTagRelationDao.insertOrIgnoreAll(roles.map {
                    AIRoleTagRelation(roleId = it.id, tagId = tagId)
                })
            }

            val loadImageJob = async {
                roles.mapNotNull { it.images.firstOrNull() }.loadImages()
            }

            insetDataJob.await()
            loadImageJob.await()

            roles.map { it.toAIRoleWithTags() }
        }
}