package com.deeptalkie.main.compose.ui.page.main.createrole

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Tab
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.deeptalkie.main.R
import com.deeptalkie.main.compose.theme.White6
import com.deeptalkie.main.compose.theme.White60
import com.deeptalkie.main.compose.ui.components.BasicVoiceLoading
import com.deeptalkie.main.compose.ui.components.DTButton
import com.deeptalkie.main.compose.ui.components.DTHorizontalSpacer
import com.deeptalkie.main.compose.ui.components.DTPage
import com.deeptalkie.main.compose.ui.components.DTScrollableTabRow
import com.deeptalkie.main.compose.ui.components.DTVerticalSpacer
import com.deeptalkie.main.db.table.Voice
import kotlinx.coroutines.launch

const val SELECT_VOICE_KEY = "SELECT_VOICE_KEY"

@Composable
fun SelectVoicePage(
    onBack: () -> Unit,
    onConfirm: (Voice) -> Unit,
    viewModel: SelectVoiceViewModel
) {
    val pagerState = rememberPagerState { viewModel.tabVoicesMap.size }
    val scope = rememberCoroutineScope()
    val tabs = remember { viewModel.tabVoicesMap.keys.toList() }

    DTPage {
        Column(
            Modifier
                .fillMaxSize()
                .background(Color(0xFF1B1B20))
                .statusBarsPadding()
                .navigationBarsPadding()
        ) {
            TitleBar(onBack)
            DTVerticalSpacer(8.dp)
            Tabs(
                tabs = tabs,
                index = pagerState.currentPage,
                onTabClick = { i, tab ->
                    scope.launch {
                        pagerState.animateScrollToPage(i)
                    }
                }
            )
            DTVerticalSpacer(18.dp)
            HorizontalPager(
                state = pagerState,
                Modifier
                    .fillMaxWidth()
                    .weight(1f)
            ) { index ->
                val voices by viewModel.tabVoicesMap[tabs[index]]!!.collectAsStateWithLifecycle()
                VoiceList(
                    viewModel.selectedVoice,
                    viewModel.playingVoiceId,
                    voices,
                    viewModel::onVoiceSelected
                )
            }
            DTVerticalSpacer(16.dp)
            ConfirmBtn(viewModel.selectedVoice != null) {
                onConfirm(viewModel.selectedVoice!!)
            }
            DTVerticalSpacer(16.dp)
        }
    }
}

@Composable
private fun TitleBar(onBack: () -> Unit) {
    Row(
        Modifier
            .padding(horizontal = 16.dp)
            .fillMaxWidth()
            .height(56.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Image(
            painterResource(R.drawable.ic_create_ai_role_back),
            null,
            Modifier
                .size(36.dp)
                .clip(CircleShape)
                .clickable(onClick = onBack)
        )
        DTHorizontalSpacer(10.dp)
        Text(
            stringResource(R.string.select_voice_page_title),
            modifier = Modifier.weight(1f),
            style = MaterialTheme.typography.headlineMedium.copy(
                color = MaterialTheme.colorScheme.onPrimary,
                lineHeight = 24.sp,
                textAlign = TextAlign.Center
            )
        )
        DTHorizontalSpacer(46.dp)
    }
}

@Composable
private fun Tabs(
    tabs: List<String>,
    index: Int,
    onTabClick: (Int, String) -> Unit
) {
    DTScrollableTabRow(index, edgePadding = 22.dp) {
        tabs.forEachIndexed { i, tab ->
            val isEnd = i == tabs.lastIndex
            val isSelected = index == i
            Tab(
                isSelected,
                onClick = { onTabClick(i, tab) },
                Modifier.padding(end = if (isEnd) 0.dp else 20.dp),
                selectedContentColor = Color.Transparent
            ) {
                Text(
                    tab,
                    style = MaterialTheme.typography.bodyMedium.copy(
                        color = if (isSelected) MaterialTheme.colorScheme.onPrimary else White60,
                        fontSize = if (isSelected) 15.sp else 13.sp,
                        lineHeight = if (isSelected) 22.5.sp else 19.5.sp,
                        fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal,
                    )
                )
            }
        }
    }
}

@Composable
private fun VoiceList(
    selectedVoice: Voice?,
    playingVoiceId: String?,
    voices: List<Voice>,
    onVoiceSelected: (Voice) -> Unit
) {
    LazyColumn(
        Modifier.fillMaxSize(),
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        items(voices, key = { it.id }) { voice ->
            VoiceItem(voice, voice == selectedVoice, voice.id == playingVoiceId?.toLongOrNull()) {
                onVoiceSelected(voice)
            }
        }
    }
}

@Composable
private fun VoiceItem(voice: Voice, isSelected: Boolean, isPlaying: Boolean, onClick: () -> Unit) {
    Row(
        Modifier
            .padding(horizontal = 16.dp)
            .fillMaxWidth()
            .height(56.dp)
            .clip(RoundedCornerShape(8.dp))
            .clickable(onClick = onClick)
            .background(White6)
            .padding(start = 12.dp, end = 14.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Column(
            Modifier
                .weight(1f)
                .fillMaxHeight(),
            verticalArrangement = Arrangement.Center
        ) {
            Row(Modifier.heightIn(16.dp), verticalAlignment = Alignment.Bottom) {
                Text(
                    voice.showName,
                    modifier = Modifier.weight(1f, fill = false),
                    style = MaterialTheme.typography.bodyMedium.copy(
                        color = MaterialTheme.colorScheme.onPrimary,
                        fontSize = 14.sp
                    ),
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
                if (isSelected) {
                    DTHorizontalSpacer(10.dp)
                    BasicVoiceLoading(
                        isPlaying,
                        Modifier
                            .size(38.dp, 16.dp)
                            .clip(RoundedCornerShape(9.dp, 10.dp, 10.dp, 1.dp))
                            .background(
                                Brush.horizontalGradient(
                                    listOf(
                                        Color(0xFFF4A0FF),
                                        Color(0xFFD64FFF),
                                        Color(0xFF684EFF),
                                    )
                                )
                            )
                    )
                }
            }
            DTVerticalSpacer(2.dp)
            Text(
                voice.info,
                style = MaterialTheme.typography.labelMedium.copy(
                    color = MaterialTheme.colorScheme.onTertiary,
                    fontSize = 11.sp
                )
            )
        }
        DTHorizontalSpacer(10.dp)
        Icon(
            if (isSelected) painterResource(R.drawable.ic_voice_selected)
            else painterResource(R.drawable.ic_voice_unselected),
            null,
            Modifier.size(20.dp),
            tint = Color.Unspecified
        )
    }
}

@Composable
private fun ConfirmBtn(enable: Boolean, onClick: () -> Unit) {
    DTButton(
        R.string.confirm,
        modifier = Modifier
            .padding(horizontal = 16.dp)
            .fillMaxWidth()
            .height(46.dp),
        enable = enable,
        containerColor = MaterialTheme.colorScheme.primary,
        disabledContainerColor = Color(0xFF636364),
        onClick = onClick
    )
}
