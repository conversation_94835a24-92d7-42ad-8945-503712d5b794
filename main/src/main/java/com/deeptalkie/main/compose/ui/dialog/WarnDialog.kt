package com.deeptalkie.main.compose.ui.dialog

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.deeptalkie.main.R
import com.deeptalkie.main.compose.theme.Black100
import com.deeptalkie.main.compose.ui.components.DTButton
import com.deeptalkie.main.compose.ui.components.DTButtonTextStyle
import com.deeptalkie.main.compose.ui.components.DTHorizontalSpacer
import com.deeptalkie.main.compose.ui.components.DTVerticalSpacer

@Composable
fun DTWarnDialog(
    @StringRes title: Int,
    @StringRes content: Int,
    @StringRes cancelText: Int,
    @StringRes confirmText: Int,
    onCancel: () -> Unit,
    onConfirm: () -> Unit,
) {
    DTDialog(onCancel) {
        Box(Modifier.width(300.dp)) {
            Column(
                Modifier
                    .padding(top = 35.dp)
                    .fillMaxWidth()
                    .background(
                        MaterialTheme.colorScheme.onPrimary,
                        RoundedCornerShape(16.dp)
                    )
            ) {
                DTVerticalSpacer(45.dp)
                Title(title)
                DTVerticalSpacer(6.dp)
                ContentText(content)
                DTVerticalSpacer(25.dp)
                DialogButtons(cancelText, confirmText, onCancel, onConfirm)
                DTVerticalSpacer(25.dp)
            }
            DialogIcon()
        }
    }
}

@Composable
private fun BoxScope.DialogIcon(@DrawableRes icon: Int = R.drawable.ic_del_chat_dialog) {
    Image(
        painterResource(icon),
        null,
        Modifier
            .height(90.dp)
            .align(Alignment.TopCenter)
    )
}

@Composable
private fun ColumnScope.Title(@StringRes text: Int) {
    Text(
        stringResource(text),
        Modifier.align(Alignment.CenterHorizontally),
        style = MaterialTheme.typography.headlineLarge.copy(
            color = Black100,
            fontSize = 16.sp
        )
    )
}

@Composable
private fun ContentText(@StringRes text: Int) {
    Text(
        stringResource(text),
        Modifier
            .padding(horizontal = 13.dp)
            .fillMaxWidth(),
        style = MaterialTheme.typography.bodySmall.copy(
            color = Black100,
            lineHeight = 20.sp,
            textAlign = TextAlign.Center
        )
    )
}

@Composable
private fun DialogButtons(
    @StringRes cancelText: Int,
    @StringRes confirmText: Int,
    onCancel: () -> Unit,
    onConfirm: () -> Unit,
) {
    Row(
        Modifier
            .padding(horizontal = 18.dp)
            .fillMaxWidth()
    ) {
        DTButton(
            stringResource(cancelText),
            Modifier
                .weight(1f)
                .height(40.dp),
            border = BorderStroke(2.dp, Color(0xFF9568F7)),
            textStyle = DTButtonTextStyle.copy(color = Color(0xFF9568F7)),
            onClick = onCancel
        )
        DTHorizontalSpacer(16.dp)
        DTButton(
            stringResource(confirmText),
            Modifier
                .weight(1f)
                .height(40.dp),
            background = Color(0xFF9568F7),
            onClick = onConfirm
        )
    }
}

@Composable
fun DTSingleBtnWarnDialog(
    @StringRes title: Int?,
    @StringRes content: Int,
    @StringRes confirmText: Int,
    @DrawableRes icon: Int = R.drawable.ic_del_chat_dialog,
    onCancel: () -> Unit,
    onConfirm: () -> Unit,
) {
    DTDialog(onCancel) {
        Box(Modifier.width(300.dp)) {
            Column(
                Modifier
                    .padding(top = 35.dp)
                    .fillMaxWidth()
                    .background(
                        MaterialTheme.colorScheme.onPrimary,
                        RoundedCornerShape(16.dp)
                    )
            ) {
                DTVerticalSpacer(45.dp)
                if (title != null) {
                    Title(title)
                }
                DTVerticalSpacer(6.dp)
                ContentText(content)
                DTVerticalSpacer(25.dp)
                ConfirmBtn(confirmText, onConfirm)
                DTVerticalSpacer(25.dp)
            }
            DialogIcon(icon)
        }
    }
}

@Composable
private fun ConfirmBtn(
    @StringRes text: Int,
    onClick: () -> Unit,
) {
    DTButton(
        stringResource(text),
        Modifier
            .padding(horizontal = 40.dp)
            .fillMaxWidth()
            .height(40.dp),
        background = Color(0xFF9568F7),
        onClick = onClick
    )
}