package com.deeptalkie.main.compose.ui.page.main.mine

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.clevguard.utils.ext.logv
import com.deeptalkie.kidsguard.net.runHttp
import com.deeptalkie.kidsguard.net.util.EncryptedUtil
import com.deeptalkie.main.Membership
import com.deeptalkie.main.R
import com.deeptalkie.main.compose.utils.ILoadingState
import com.deeptalkie.main.compose.utils.loadingState
import com.deeptalkie.main.config.PASSWORD_REGEX
import com.deeptalkie.main.ext.showToast
import com.deeptalkie.main.utils.NetWorkManager
import com.deeptalkie.main.utils.getString
import com.imyfone.membership.repository.VerificationCode
import kotlinx.coroutines.launch

class ForgetPasswordViewModel : ViewModel(), ILoadingState by loadingState() {
    var email by mutableStateOf("")
        private set
    var code by mutableStateOf("")
        private set
    var password by mutableStateOf("")
        private set
    var confirmPassword by mutableStateOf("")
        private set
    var emailTip by mutableStateOf<String?>(null)
        private set
    var codeTip by mutableStateOf<String?>(null)
        private set
    var passwordTip by mutableStateOf<String?>(null)
        private set
    var confirmPasswordTip by mutableStateOf<String?>(null)
        private set
    var lastGetCodeTime by mutableLongStateOf(0L)
        private set

    fun onEmailChanged(newEmail: String) {
        email = newEmail
        emailTip = when {
            email.isEmpty() -> getString(R.string.enter_email)
            !isEmailFormat(email) -> getString(R.string.login_email_valid)
            else -> ""
        }
    }

    fun onCodeChanged(newCode: String) {
        code = newCode
        codeTip = if (code.isEmpty()) getString(R.string.empty_code) else ""
    }

    fun onPasswordChanged(newPassword: String) {
        password = newPassword

        passwordTip = when {
            password.isEmpty() -> getString(R.string.enter_psw)
            password.length < 6 || password.length > 16 -> getString(R.string.login_pwd_valid)
            !PASSWORD_REGEX.matches(password) -> getString(R.string.login_psw_valid)
            else -> ""
        }
        logv(passwordTip.orEmpty())
    }

    fun onConfirmPasswordChanged(newConfirmPassword: String) {
        confirmPassword = newConfirmPassword
        confirmPasswordTip = when {
            confirmPassword.isEmpty() -> getString(R.string.enter_psw)
            confirmPassword != password -> getString(R.string.login_pwd_confirm_valid)
            else -> ""
        }
    }

    fun getCode() {
        if (!NetWorkManager.isNetworkConnectedSystem()) {
            showToast(getString(R.string.network_error))
            return
        }

        viewModelScope.launch {
            showLoading(true)
            val response = runHttp {
                Membership.membershipClient.account.sendVerificationCode(
                    sendEmail = email,
                    code = VerificationCode.FORGET_PASSWORD,
                    email = email,
                )
            }
            if (response?.isSuccess == true) {
                showToast(getString(R.string.code_will_send))
                lastGetCodeTime = System.currentTimeMillis()
            } else {
                showToastByCode(response?.code)
            }
            showLoading(false)
        }
    }

    fun canSubmit() =
        emailTip == "" && codeTip == "" && passwordTip == "" && confirmPasswordTip == ""

    fun submit(onSuccess: () -> Unit) {
        if (!NetWorkManager.isNetworkConnectedSystem()) {
            showToast(getString(R.string.network_error))
            return
        }
        viewModelScope.launch {
            showLoading(true)
            val response = runHttp {
                Membership.membershipClient.account.updatePassword(
                    email = email,
                    code = code,
                    password = EncryptedUtil.md5(password)
                )
            }
            if (response?.isSuccess == true) {
                showToast(getString(R.string.your_password_has_been_changed_please_login_again))
                onSuccess()
            } else {
                showToastByCode(response?.code)
            }
            showLoading(false)
        }
    }

    private fun showToastByCode(code: Int?) {
        when (code) {
            410, 411 -> {
                showToast(getString(R.string.login_email_no_exist))
            }

            412 -> {
                showToast(getString(R.string.login_email_valid))
            }

            421 -> {
                showToast(getString(R.string.error_code))
            }

            415 -> {
                showToast(getString(R.string.login_psw_valid))
            }

            414 -> {
                showToast(getString(R.string.login_pwd_valid))
            }

            else -> {
                showToast(getString(R.string.network_error))
            }
        }
    }
}