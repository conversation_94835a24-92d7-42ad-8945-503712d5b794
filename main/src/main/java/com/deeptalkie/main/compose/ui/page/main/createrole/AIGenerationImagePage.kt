package com.deeptalkie.main.compose.ui.page.main.createrole

import androidx.annotation.StringRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredWidth
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.GridItemSpan
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil3.compose.AsyncImage
import com.deeptalkie.main.Membership
import com.deeptalkie.main.R
import com.deeptalkie.main.bean.AIImageStyle
import com.deeptalkie.main.compose.theme.Black20
import com.deeptalkie.main.compose.theme.White6
import com.deeptalkie.main.compose.ui.components.DTButton
import com.deeptalkie.main.compose.ui.components.DTButtonDefaults
import com.deeptalkie.main.compose.ui.components.DTHorizontalSpacer
import com.deeptalkie.main.compose.ui.components.DTIconButton
import com.deeptalkie.main.compose.ui.components.DTPage
import com.deeptalkie.main.compose.ui.components.DTTextFieldNoBorder
import com.deeptalkie.main.compose.ui.components.DTVerticalSpacer
import com.deeptalkie.main.compose.ui.dialog.AIRoleSex
import com.deeptalkie.main.compose.ui.dialog.DTSingleBtnWarnDialog
import com.deeptalkie.main.compose.ui.modifier.click
import com.deeptalkie.main.compose.ui.page.main.createrole.CreateAIRoleViewModel.VerifiedState
import com.deeptalkie.main.compose.utils.InfringingWordVisualTransformation
import com.deeptalkie.main.config.ReportEventUtils
import com.deeptalkie.main.config.UmConstant
import com.deeptalkie.main.ext.toDp
import com.deeptalkie.main.view.RequirePurchaseDialog
import com.eygraber.compose.placeholder.PlaceholderHighlight
import com.eygraber.compose.placeholder.material3.shimmer
import com.eygraber.compose.placeholder.placeholder
import kotlinx.coroutines.launch

@Composable
fun AIGenerationImagePage(
    onBack: () -> Unit,
    onGotoPay: () -> Unit,
    onGenerate: (prompt: String, aiRoleSex: AIRoleSex, stylePrompt: String) -> Unit,
    viewModel: AIGenerationImageViewModel
) {
    val context = LocalContext.current
    val focusManager = LocalFocusManager.current
    val keyboardController = LocalSoftwareKeyboardController.current
    val navigationHeight = WindowInsets.navigationBars.getBottom(LocalDensity.current).toDp
    val scope = rememberCoroutineScope()
    val requirePurchaseDialog = remember {
        RequirePurchaseDialog(context, RequirePurchaseDialog.TYPE_CREATE_AI_ROLE, onGotoPay)
    }

    DTPage {
        Column(
            Modifier
                .fillMaxSize()
                .background(Color(0xFF0D1116))
                .click {
                    focusManager.clearFocus()
                    keyboardController?.hide()
                }
                .statusBarsPadding()
        ) {
            CreateAIRoleTitleBar(
                R.string.create_ai_role_page_cell_character_image,
                onBack,
                onGotoPay
            )
            BoxWithConstraints(
                Modifier
                    .weight(1f)
                    .fillMaxWidth()
            ) {
                LazyVerticalGrid(
                    columns = GridCells.Fixed(3),
                    Modifier
                        .fillMaxSize(),
                    horizontalArrangement = Arrangement.spacedBy(14.dp),
                    contentPadding = PaddingValues(
                        start = 16.dp,
                        top = 8.dp,
                        end = 16.dp,
                        bottom = 60.dp + navigationHeight
                    )
                ) {
                    item(span = { GridItemSpan(maxLineSpan) }, key = "description") {
                        Column(Modifier.requiredWidth(<EMAIL>)) {
                            DescriptionCell(
                                viewModel.imageDesc,
                                viewModel.aiImageDescReqFailState,
                                viewModel.descInfringingWord,
                                R.string.ai_generation_image_page_image_description_placeholder,
                                viewModel.aiImageDescLoading,
                                onDescriptionChange = viewModel::onImageDescInput,
                                onAiGeneration = block@{
                                    ReportEventUtils.onEvent(
                                        UmConstant.CREATE_IMAGE,
                                        mapOf(UmConstant.CREATE_IMAGE to "AI_inspiration_botton")
                                    )
                                    if (!Membership.isVip()) {
                                        if (!requirePurchaseDialog.isShowing) {
                                            requirePurchaseDialog.show()
                                        }
                                        return@block
                                    }
                                    viewModel.requestImageDesc()
                                },
                                onAiOptimize = block@{
                                    ReportEventUtils.onEvent(
                                        UmConstant.CREATE_IMAGE,
                                        mapOf(UmConstant.CREATE_IMAGE to "Optimize_botton")
                                    )
                                    if (!Membership.isVip()) {
                                        if (!requirePurchaseDialog.isShowing) {
                                            requirePurchaseDialog.show()
                                        }
                                        return@block
                                    }
                                    viewModel.requestImageDescOptimize()
                                },
                                onRetry = viewModel::onRetryGenDesc
                            )
                            DTVerticalSpacer(20.dp)
                        }
                    }

                    item(span = { GridItemSpan(maxLineSpan) }, key = "image_style") {
                        Column(Modifier.requiredWidth(<EMAIL>)) {
                            CreateAIRoleCellTitle(
                                R.string.ai_generation_image_page_choose_image_style,
                                required = true,
                                verifiedState = if (viewModel.imageStyle == null) VerifiedState.NotVerified
                                else VerifiedState.Verified,
                                canAiGeneration = false
                            )
                            DTVerticalSpacer(8.dp)
                        }
                    }

                    items(viewModel.aiImageStyleList, key = { it.id }) { imageStyle ->
                        ImageStyleItem(
                            imageStyle,
                            viewModel.aiRoleSex,
                            viewModel.imageStyle == imageStyle,
                            viewModel::onImageStyleChange
                        )
                    }
                }

                ConfirmBtn(viewModel.canGenerate()) {
                    scope.launch {
                        ReportEventUtils.onEvent(
                            UmConstant.CREATE_IMAGE,
                            mapOf(UmConstant.CREATE_IMAGE to "Generate_image_botton")
                        )
                        if (!Membership.isVip()) {
                            if (!requirePurchaseDialog.isShowing) {
                                requirePurchaseDialog.show()
                            }
                            return@launch
                        }
                        if (!viewModel.checkCoinEnough) {
                            viewModel.showCoinNotEnoughDialog(true)
                            return@launch
                        }
                        if (!viewModel.verifyImageDesc()) {
                            return@launch
                        }
                        onGenerate(
                            viewModel.imageDesc,
                            viewModel.aiRoleSex,
                            viewModel.imageStyle?.prompt.orEmpty()
                        )
                    }
                }
            }
        }
    }

    if (viewModel.showCoinNotEnoughDialog) {
        DTSingleBtnWarnDialog(
            title = R.string.reminder,
            content = R.string.ai_generation_image_page_coin_not_enough_dialog_tips,
            confirmText = R.string.check,
            icon = R.drawable.ic_coin_not_enough,
            onCancel = {
                viewModel.showCoinNotEnoughDialog(false)
            },
            onConfirm = {
                viewModel.showCoinNotEnoughDialog(false)
                onGotoPay()
            }
        )
    }
}

@Composable
private fun DescriptionCell(
    description: String,
    aiImageDescReqFailState: AIDescReqFailState,
    descInfringingWords: List<String>,
    @StringRes placeholder: Int,
    aiDescriptionLoading: Boolean,
    onDescriptionChange: (String) -> Unit,
    onAiGeneration: () -> Unit,
    onAiOptimize: () -> Unit,
    onRetry: () -> Unit
) {
    CreateAIRoleCellTitle(
        R.string.ai_generation_image_page_image_description,
        required = true,
        verifiedState = when {
            description.length > 1000 -> VerifiedState.VerifyFail
            description.isNotEmpty() -> VerifiedState.Verified
            else -> VerifiedState.NotVerified
        },
        canAiGeneration = true,
        aiGenText = R.string.create_ai_role_page_btn_ai_inspiration,
        onAiGenerationClick = onAiGeneration
    )
    DTVerticalSpacer(8.dp)
    Column(
        Modifier
            .padding(horizontal = 16.dp)
            .fillMaxWidth()
            .heightIn(160.dp)
            .background(White6, RoundedCornerShape(8.dp))
    ) {
        if (aiImageDescReqFailState != AIDescReqFailState.NotFail) {
            AIDescFailedContent(onRetry)
        } else {
            if (aiDescriptionLoading) {
                DescPlaceholder()
            } else {
                DTTextFieldNoBorder(
                    value = description,
                    onValueChange = onDescriptionChange,
                    Modifier
                        .fillMaxWidth()
                        .height(127.dp),
                    placeholder = stringResource(placeholder),
                    textStyle = MaterialTheme.typography.bodyMedium.copy(
                        color = MaterialTheme.colorScheme.onPrimary,
                        fontSize = 12.sp
                    ),
                    singleLine = false,
                    visualTransformation = remember(description, descInfringingWords) {
                        InfringingWordVisualTransformation(description, descInfringingWords)
                    },
                    verticalAlignment = Alignment.Top,
                    contentPadding = PaddingValues(horizontal = 10.dp, vertical = 12.dp),
                )
            }
            Row(
                Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 10.dp),
                verticalAlignment = Alignment.Bottom,
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                DTIconButton(
                    onClick = onAiOptimize,
                    modifier = Modifier
                        .height(23.dp),
                    enabled = description.isNotEmpty(),
                    colors = DTButtonDefaults.buttonColors(
                        containerColor = Black20,
                        disabledContainerColor = Color(0xFF4E4E4E),
                    )
                ) {
                    Icon(
                        painterResource(R.drawable.ic_optimize),
                        null,
                        Modifier.size(14.dp),
                        tint = if (description.isNotEmpty()) Color.Unspecified else Color(0xFF9E9E9E)
                    )
                    DTHorizontalSpacer(5.dp)
                    Text(
                        stringResource(R.string.create_ai_role_page_cell_desc_optimize),
                        style = MaterialTheme.typography.bodyMedium.copy(
                            brush = Brush.horizontalGradient(
                                if (description.isNotEmpty())
                                    listOf(Color(0xFF995BFF), Color(0xFF5258F6))
                                else listOf(Color(0xFF9E9E9E), Color(0xFF9E9E9E))
                            ),
                            fontSize = 10.sp,
                            lineHeight = 14.sp
                        )
                    )
                }
                Text(
                    buildAnnotatedString {
                        withStyle(
                            SpanStyle(
                                color = if (description.length > 1000) Color(0xFFF74040)
                                else MaterialTheme.colorScheme.onTertiary,
                            )
                        ) {
                            append("${description.length}")
                        }
                        append("/1000")
                    },
                    style = MaterialTheme.typography.labelMedium.copy(
                        color = MaterialTheme.colorScheme.onTertiary,
                        fontSize = 11.sp,
                    )
                )
            }
        }
    }
}

@Composable
private fun DescPlaceholder() {
    Column(
        Modifier
            .padding(top = 12.dp)
            .fillMaxWidth()
            .height(115.dp),
        verticalArrangement = Arrangement.spacedBy(5.dp)
    ) {
        repeat(2) {
            Box(
                Modifier
                    .padding(horizontal = 10.dp)
                    .fillMaxWidth()
                    .height(20.dp)
                    .placeholder(
                        true,
                        color = Color(0xFF3A3A3E),
                        shape = RoundedCornerShape(4.dp),
                        highlight = PlaceholderHighlight.shimmer()
                    )
            )
        }
        Box(
            Modifier
                .padding(start = 10.dp)
                .width(194.dp)
                .height(20.dp)
                .placeholder(
                    true,
                    color = Color(0xFF3A3A3E),
                    shape = RoundedCornerShape(4.dp),
                    highlight = PlaceholderHighlight.shimmer()
                )
        )
    }
}

@Composable
fun AIDescFailedContent(
    onRetry: () -> Unit
) {
    Column(
        Modifier
            .padding(top = 8.dp)
            .fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Image(
            painterResource(R.drawable.ic_ai_gen_img_fail),
            null,
            Modifier.height(70.dp)
        )
        DTVerticalSpacer(4.dp)
        Text(
            stringResource(R.string.ai_generation_image_page_ai_desc_failed_tips),
            Modifier
                .padding(horizontal = 20.dp)
                .fillMaxWidth(),
            style = MaterialTheme.typography.labelSmall.copy(
                color = MaterialTheme.colorScheme.onPrimary,
                lineHeight = 15.sp,
                textAlign = TextAlign.Center
            )
        )
        DTVerticalSpacer(8.dp)
        DTButton(
            stringResource(R.string.try_again),
            modifier = Modifier
                .width(104.dp)
                .height(28.dp),
            background = MaterialTheme.colorScheme.primary,
            onClick = onRetry,
            textStyle = MaterialTheme.typography.headlineMedium.copy(
                color = MaterialTheme.colorScheme.onPrimary,
                fontSize = 12.sp
            )
        )
    }
}

@Composable
private fun ImageStyleItem(
    imageStyle: AIImageStyle,
    aiRoleSex: AIRoleSex,
    isSelected: Boolean,
    onImageStyleChange: (AIImageStyle) -> Unit
) {
    Column(
        Modifier
            .fillMaxWidth()
            .height(170.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        AsyncImage(
            if (aiRoleSex == AIRoleSex.Girls) imageStyle.girlPath else imageStyle.path,
            null,
            Modifier
                .fillMaxWidth()
                .height(134.dp)
                .clip(RoundedCornerShape(6.dp))
                .run {
                    if (isSelected) {
                        border(2.dp, MaterialTheme.colorScheme.primary, RoundedCornerShape(6.dp))
                    } else this
                }
                .clickable(onClick = { onImageStyleChange(imageStyle) }),
            placeholder = painterResource(R.drawable.img_ai_role_placeholder),
            contentScale = ContentScale.Crop
        )
        DTVerticalSpacer(6.dp)
        Text(
            imageStyle.name,
            style = MaterialTheme.typography.bodyMedium.copy(
                color = MaterialTheme.colorScheme.onPrimary,
                fontSize = 14.sp
            )
        )
    }
}

@Composable
private fun BoxScope.ConfirmBtn(
    enable: Boolean,
    onClick: () -> Unit
) {
    DTButton(
        R.string.ai_generation_image_page_btn_generate,
        modifier = Modifier
            .padding(start = 16.dp, end = 16.dp, bottom = 16.dp)
            .navigationBarsPadding()
            .fillMaxWidth()
            .height(44.dp)
            .align(Alignment.BottomCenter),
        enable = enable,
        containerColor = MaterialTheme.colorScheme.primary,
        disabledContainerColor = Color(0xFF636364),
        onClick = onClick
    )
}