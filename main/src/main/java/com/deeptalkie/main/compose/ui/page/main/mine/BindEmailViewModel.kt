package com.deeptalkie.main.compose.ui.page.main.mine

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.deeptalkie.main.Membership
import com.imyfone.membership.api.bean.OAUTH_GOOGLE_LOGIN
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 *creater:lin<PERSON><PERSON> on 2025/5/22 17:12
 */
class BindEmailViewModel : ViewModel() {
    val account = Membership.membershipClient.account

    private val _bindEmailState = MutableStateFlow(false)
    val bindEmailState = _bindEmailState.asStateFlow()
    private val _bindEmailEvent = MutableSharedFlow<BindEmailEvent>()
    val bindEmailEvent = _bindEmailEvent.asSharedFlow()


    fun checkEmailRegister(email: String) {
        viewModelScope.launch {
            _bindEmailState.emit(true)
            val response =
                account.checkEmailRegister(email = email, provider = OAUTH_GOOGLE_LOGIN)
            val data = response.data
            _bindEmailState.emit(false)
            when (response.code) {
                406 -> { // 账号已注册
                    if(data?.has_bind_provider==true){  //跳转到绑定其他账号页面
                        _bindEmailEvent.emit(BindEmailEvent.EmailHasRegisterAndBind)
                    }else{  //到输入密码的界面，验证账号的密码
                        _bindEmailEvent.emit(BindEmailEvent.EmailHasRegisterUnBind)
                    }
                }

                410 -> { //邮箱不存在 ,跳转到
                    _bindEmailEvent.emit(BindEmailEvent.NeedCreateAccount)
                }

                412 -> {
                    _bindEmailEvent.emit(BindEmailEvent.EmailFormatError)
                }

                else -> {
                    _bindEmailEvent.emit(BindEmailEvent.CommonError)
                }
            }
        }
    }
}

sealed interface BindEmailEvent {
    data object EmailHasRegisterUnBind : BindEmailEvent
    data object EmailHasRegisterAndBind : BindEmailEvent
    data object NeedCreateAccount : BindEmailEvent
    data object EmailFormatError : BindEmailEvent
    data object CommonError : BindEmailEvent
}