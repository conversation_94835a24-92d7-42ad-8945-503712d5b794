package com.deeptalkie.main.compose.ui.dialog

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.deeptalkie.main.R
import com.deeptalkie.main.compose.ui.components.DTHorizontalSpacer
import com.deeptalkie.main.compose.ui.components.DTVerticalSpacer

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CreateAIRoleBottomSheet(
    onDismiss: () -> Unit,
    onSexSelected: (sex: AIRoleSex) -> Unit
) {
    DTBottomSheet(onDismiss = onDismiss, containerColor = Color(0xFF28282D)) {
        Column(
            Modifier
                .fillMaxWidth()
                .navigationBarsPadding(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            DTVerticalSpacer(6.dp)
            Title()
            DTVerticalSpacer(2.dp)
            Subtitle()
            DTVerticalSpacer(16.dp)
            AIRoleSexSelector(onSexSelected = onSexSelected)
            DTVerticalSpacer(24.dp)
        }
    }
}

@Composable
private fun Title() {
    Text(
        stringResource(R.string.create_ai_role_bottom_sheet_title),
        style = MaterialTheme.typography.headlineMedium.copy(
            color = MaterialTheme.colorScheme.onPrimary,
            lineHeight = 24.sp
        )
    )
}

@Composable
fun Subtitle() {
    Text(
        stringResource(R.string.create_ai_role_bottom_sheet_subtitle),
        Modifier
            .padding(horizontal = 20.dp)
            .fillMaxWidth(),
        style = MaterialTheme.typography.labelMedium.copy(
            color = MaterialTheme.colorScheme.onTertiary,
            lineHeight = 22.sp,
            textAlign = TextAlign.Center
        )
    )
}

@Composable
private fun AIRoleSexSelector(
    onSexSelected: (sex: AIRoleSex) -> Unit
) {
    Row {
        AIRoleSexSelectorItem(AIRoleSex.Girls) {
            onSexSelected(AIRoleSex.Girls)
        }
        DTHorizontalSpacer(40.dp)
        AIRoleSexSelectorItem(AIRoleSex.Guys) {
            onSexSelected(AIRoleSex.Guys)
        }
    }
}

@Composable
private fun AIRoleSexSelectorItem(sex: AIRoleSex, onClick: () -> Unit) {
    val image = when (sex) {
        AIRoleSex.Girls -> R.drawable.ai_role_girls
        AIRoleSex.Guys -> R.drawable.ai_role_guys
    }
    val text = when (sex) {
        AIRoleSex.Girls -> R.string.girls
        AIRoleSex.Guys -> R.string.guys
    }
    Column(horizontalAlignment = Alignment.CenterHorizontally) {
        Image(
            painterResource(image),
            null,
            Modifier
                .size(110.dp, 140.dp)
                .clip(RoundedCornerShape(8.dp))
                .clickable(onClick = onClick),
            contentScale = ContentScale.Crop
        )
        DTVerticalSpacer(10.dp)
        Text(
            stringResource(text),
            style = MaterialTheme.typography.bodyMedium.copy(
                color = MaterialTheme.colorScheme.onPrimary,
                fontSize = 14.sp
            )
        )
    }
}

enum class AIRoleSex {
    Girls,
    Guys;

    fun eventValue(): String {
        return when (this) {
            Girls -> "Girls"
            Guys -> "Guy"
        }
    }
}