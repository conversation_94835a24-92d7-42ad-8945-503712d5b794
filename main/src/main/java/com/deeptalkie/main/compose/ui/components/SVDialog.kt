package com.deeptalkie.main.compose.ui.components

import androidx.annotation.StringRes
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.deeptalkie.main.compose.theme.Black100
import com.deeptalkie.main.compose.theme.SocialBlue

// 通用的提示+双按钮弹窗
@Composable
fun SVDialog(
    @StringRes title: Int,
    @StringRes cancelText: Int,
    @StringRes confirmText: Int,
    onConfirm: () -> Unit,
    onCancel: () -> Unit,
) {
    Dialog(
        onDismissRequest = onCancel,
        DialogProperties(usePlatformDefaultWidth = false)
    ) {
        Column(
            Modifier
                .padding(horizontal = 34.dp)
                .fillMaxWidth()
                .background(MaterialTheme.colorScheme.onPrimary, RoundedCornerShape(16.dp))
        ) {
            DTVerticalSpacer(32.dp)
            Text(
                stringResource(title),
                Modifier
                    .padding(horizontal = 16.dp)
                    .fillMaxWidth(),
                style = MaterialTheme.typography.headlineMedium.copy(
                    color = Black100,
                    lineHeight = 20.sp,
                    textAlign = TextAlign.Center
                )
            )
            DTVerticalSpacer(32.dp)
            Row(
                Modifier
                    .padding(horizontal = 30.dp)
                    .fillMaxWidth()
            ) {
                DTButton(
                    stringResource(cancelText),
                    Modifier
                        .weight(1f)
                        .height(40.dp),
                    border = BorderStroke(2.dp, SocialBlue),
                    textStyle = DTButtonTextStyle.copy(color = SocialBlue),
                    onClick = onCancel
                )
                DTHorizontalSpacer(16.dp)
                DTButton(
                    stringResource(confirmText),
                    Modifier
                        .weight(1f)
                        .height(40.dp),
                    background = SocialBlue,
                    onClick = onConfirm
                )
            }
            DTVerticalSpacer(24.dp)
        }
    }
}