package com.deeptalkie.main.compose.ui.components

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsDraggedAsState
import androidx.compose.foundation.interaction.collectIsPressedAsState
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import com.deeptalkie.main.compose.theme.White40
import kotlinx.coroutines.delay

data class DTBannerItem(
    val image: Int,
)

@Composable
fun DTBanner(
    data: List<DTBannerItem>,
    modifier: Modifier = Modifier,
    onClick: (Int, DTBannerItem) -> Unit
) {
    val pagerState =
        rememberPagerState(initialPage = (Int.MAX_VALUE / 2) - 1, pageCount = { Int.MAX_VALUE })

    val isDragged by pagerState.interactionSource.collectIsDraggedAsState()
    val pageInteractionSource = remember { MutableInteractionSource() }
    val pageIsPressed by pageInteractionSource.collectIsPressedAsState()

    val autoAdvance = !isDragged && !pageIsPressed

    if (autoAdvance) {
        LaunchedEffect(pagerState, pageInteractionSource) {
            while (true) {
//                val time = if (pagerState.currentPageOffsetFraction != 0f) 0L else 3000L
                delay(3_000)
                val nextPage = pagerState.currentPage + 1
                pagerState.animateScrollToPage(nextPage, animationSpec = tween(600))
            }
        }
    }

    Box(modifier.fillMaxWidth()) {
        HorizontalPager(state = pagerState) { index ->
            val position = index % data.size
            val item = data[position]
            Image(
                painterResource(item.image),
                null,
                Modifier
                    .padding(horizontal = 16.dp)
                    .fillMaxWidth()
                    .aspectRatio(1029 / 663f)
                    .clip(RoundedCornerShape(15.dp))
                    .clickable(
                        interactionSource = pageInteractionSource,
                        indication = null
                    ) {
                        onClick(position, item)
                    }
            )
        }

        PagerIndicator(
            data.size,
            pagerState.currentPage % data.size,
            Modifier
                .padding(bottom = 6.dp)
                .align(Alignment.BottomCenter)
        )
    }
}

@Composable
fun PagerIndicator(pageCount: Int, currentPageIndex: Int, modifier: Modifier = Modifier) {
    Row(modifier) {
        repeat(pageCount) { index ->
            val selected = currentPageIndex == index
            val color by animateColorAsState(if (selected) MaterialTheme.colorScheme.onPrimary else White40)

            val width by animateDpAsState(if (selected) 16.dp else 6.dp)

            Box(
                modifier = modifier
                    .width(width)
                    .height(6.dp)
                    .clip(RoundedCornerShape(6.dp))
                    .background(color)
            )
            if (index < pageCount - 1) {
                DTHorizontalSpacer(8.dp)
            }
        }
    }
}
