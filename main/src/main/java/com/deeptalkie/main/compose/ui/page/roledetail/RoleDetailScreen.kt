package com.deeptalkie.main.compose.ui.page.roledetail

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import coil3.compose.AsyncImage
import com.deeptalkie.main.Membership
import com.deeptalkie.main.R
import com.deeptalkie.main.compose.theme.White
import com.deeptalkie.main.compose.theme.White10
import com.deeptalkie.main.compose.ui.components.DTButton
import com.deeptalkie.main.compose.ui.components.DTHorizontalSpacer
import com.deeptalkie.main.compose.ui.components.DTPage
import com.deeptalkie.main.compose.ui.components.DTVerticalSpacer
import com.deeptalkie.main.db.result.AIRoleWithTags
import com.deeptalkie.main.db.table.AIRole
import com.deeptalkie.main.ext.imageRequest
import com.deeptalkie.main.view.RequirePurchaseDialog
import com.skydoves.landscapist.coil3.CoilImage
import com.skydoves.landscapist.components.rememberImageComponent
import com.skydoves.landscapist.transformation.blur.BlurTransformationPlugin
import kotlinx.coroutines.launch

@Composable
fun RoleDetailScreen(
    onBack: () -> Unit,
    onChatClick: ((roleId: Long, sessionId: Long) -> Unit),
    onLogin: () -> Unit,
    onBuy: () -> Unit,
    onReport: (String) -> Unit,
    viewModel: DTRoleDetailViewModel = viewModel(),
) {
    val context = LocalContext.current
    val scope = rememberCoroutineScope()

    val aiRoleWithTags by viewModel.aiRoleWithTagsFlow.collectAsStateWithLifecycle()
    val userAIRole by viewModel.userAIRoleFlow.collectAsStateWithLifecycle()
    val isVip by Membership.vipStateFlow.collectAsStateWithLifecycle()

    DTPage(
        loading = viewModel.loading,
        onDismissLoading = {
            viewModel.showLoading(false)
        }
    ) {
        Box {
            Column(
                Modifier
                    .fillMaxSize()
                    .background(Color(0xFF0D1116))
                    .verticalScroll(rememberScrollState())
                    .padding(bottom = 66.dp)
                    .navigationBarsPadding()
            ) {
                NameAndImages(
                    aiRoleWithTags?.aiRole,
                    isVip,
                    viewModel.getCurrentImage(),
                    fn@{ index, isLocked ->
                        if (isLocked) {
                            return@fn RequirePurchaseDialog(
                                context,
                                RequirePurchaseDialog.TYPE_PICTURE,
                                onBuy
                            ).show()
                        }
                        viewModel.onImageSelected(index)
                    }
                )
                DTVerticalSpacer(10.dp)
                RoleInfo(aiRoleWithTags)
            }
            DetailsTopBar(
                onBack = onBack,
                isFavorite = userAIRole?.isFavorite == true,
                onFavorite = fn@{
                    if (!Membership.isLogin()) {
                        return@fn onLogin()
                    }
                    viewModel.favorite()
                },
                onReport = {
                    onReport(aiRoleWithTags?.aiRole?.name.orEmpty())
                }
            )
            ChatButton(Modifier.align(Alignment.BottomCenter)) {
                scope.launch {
                    if (!Membership.isLogin()) {
                        return@launch onLogin()
                    }
                    val sessionId = viewModel.startChat(
                        context,
                        gotoLogin = onLogin,
                        gotoBuy = onBuy
                    ) ?: return@launch
                    onChatClick(viewModel.route.roleId, sessionId)
                }
            }
        }
    }
}

@Composable
private fun DetailsTopBar(
    onBack: () -> Unit,
    isFavorite: Boolean,
    onFavorite: () -> Unit,
    onReport: () -> Unit
) {
    Row(
        Modifier
            .statusBarsPadding()
            .fillMaxWidth()
            .height(56.dp)
            .padding(horizontal = 16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            painter = painterResource(R.drawable.ic_back2),
            contentDescription = "back",
            modifier = Modifier
                .size(36.dp)
                .clip(CircleShape)
                .clickable(onClick = onBack),
            tint = Color.Unspecified
        )
        Spacer(Modifier.weight(1f))
        Icon(
            painter = painterResource(if (isFavorite) R.drawable.ic_collected else R.drawable.ic_uncollected),
            contentDescription = null,
            modifier = Modifier
                .size(36.dp)
                .clip(CircleShape)
                .clickable(onClick = onFavorite),
            tint = Color.Unspecified
        )
        DTHorizontalSpacer(10.dp)
        Icon(
            painter = painterResource(R.drawable.ic_report),
            contentDescription = "report",
            modifier = Modifier
                .size(36.dp)
                .clip(CircleShape)
                .clickable(onClick = onReport),
            tint = Color.Unspecified
        )
    }
}

@Composable
private fun NameAndImages(
    aiRole: AIRole?,
    isVip: Boolean,
    currentImage: String?,
    onImageSelected: (Int, Boolean) -> Unit
) {
    aiRole ?: return
    val isUserCreated = aiRole.images.size == 1
    Box(
        Modifier
            .fillMaxWidth()
            .height(if (isUserCreated) 700.dp else 500.dp)
    ) {
        AsyncImage(
            currentImage,
            contentDescription = "img",
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.Crop
        )
        Box(
            Modifier
                .fillMaxWidth()
                .height(140.dp)
                .background(
                    Brush.verticalGradient(
                        listOf(
                            Color(0x00000000),
                            Color(0xFF0D1116),
                        )
                    )
                )
                .align(Alignment.BottomCenter)
        )
        Column(Modifier.align(Alignment.BottomCenter)) {
            if (!isUserCreated) {
                ImageRow(
                    aiRole.images,
                    isVip = isVip,
                    onImageSelected = onImageSelected
                )
                DTVerticalSpacer(16.dp)
            }
            Text(
                aiRole.name,
                modifier = Modifier
                    .padding(horizontal = 20.dp)
                    .fillMaxWidth(),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                style = MaterialTheme.typography.headlineLarge.copy(
                    color = White,
                    fontSize = 20.sp,
                )
            )
        }
    }
}

@Composable
private fun ImageRow(
    images: List<String>,
    isVip: Boolean,
    onImageSelected: (Int, Boolean) -> Unit
) {
    val context = LocalContext.current
    LazyRow(
        Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(12.dp, Alignment.CenterHorizontally)
    ) {
        itemsIndexed(images, key = { _, url -> url }) { index, url ->
            val isLocked = !isVip && index != 0
            Box(
                Modifier
                    .size(50.dp)
                    .clip(RoundedCornerShape(8.dp))
                    .clickable {
                        onImageSelected(index, isLocked)
                    },
                contentAlignment = Alignment.Center
            ) {
                CoilImage(
                    imageRequest = { url.imageRequest(context) },
                    Modifier.fillMaxSize(),
                    component = if (isVip || index == 0) {
                        rememberImageComponent {}
                    } else {
                        rememberImageComponent { +BlurTransformationPlugin(radius = 80) }
                    }
                )
                if (isLocked) {
                    Image(
                        painterResource(R.drawable.ic_locked),
                        contentDescription = "lockIcon",
                        modifier = Modifier
                            .size(24.dp),
                        contentScale = ContentScale.Crop
                    )
                }
            }
        }
    }
}

@Composable
private fun RoleInfo(
    aiRoleWithTags: AIRoleWithTags?
) {
    val (aiRole, tags) = aiRoleWithTags ?: return
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 20.dp)
    ) {
        if (aiRole.description.isNotBlank()) {
            Text(
                aiRole.description,
                modifier = Modifier.fillMaxWidth(),
                style = MaterialTheme.typography.bodySmall.copy(
                    color = Color(0xffECEBED),
                    fontSize = 13.sp,
                )
            )
            DTVerticalSpacer(14.dp)
        }
        if (tags.isNotEmpty()) {
            HorizontalDivider(
                modifier = Modifier.fillMaxWidth(),
                thickness = 0.5.dp,
                color = Color(0xff323436)
            )
            DTVerticalSpacer(14.dp)
            FlowRow(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(14.dp),
                verticalArrangement = Arrangement.spacedBy(12.dp),
            ) {
                tags.forEach { tag ->
                    Box(
                        Modifier
                            .clip(RoundedCornerShape(50))
                            .background(White10)
                            .padding(14.dp, 5.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            tag.name,
                            style = MaterialTheme.typography.bodyMedium.copy(
                                color = MaterialTheme.colorScheme.onPrimary,
                                fontSize = 14.sp,
                                lineHeight = 19.6.sp
                            )
                        )
                    }
                }
            }
            DTVerticalSpacer(20.dp)
        }
    }
}

@Composable
private fun ChatButton(
    modifier: Modifier = Modifier,
    onChatClick: () -> Unit,
) {
    Box(
        modifier
            .fillMaxWidth()
            .background(Color(0xFF0D1116))
            .navigationBarsPadding()
            .padding(start = 30.dp, end = 30.dp, bottom = 20.dp)
    ) {
        DTButton(
            R.string.chat,
            modifier = Modifier
                .fillMaxWidth()
                .height(46.dp),
            contentColor = White,
            containerColor = MaterialTheme.colorScheme.primary,
            onClick = onChatClick
        )
    }
}