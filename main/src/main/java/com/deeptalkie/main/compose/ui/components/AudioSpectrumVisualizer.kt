package com.deeptalkie.main.compose.ui.components

import androidx.compose.foundation.Canvas
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.Fill
import androidx.compose.ui.unit.dp
import com.deeptalkie.main.ext.toPx
import kotlin.math.min

@Composable
fun AudioSpectrumVisualizer(
    audioSpectralData: FloatArray,
    modifier: Modifier = Modifier,
    barColor: Color = MaterialTheme.colorScheme.onPrimary,
) {
    val barWidthPx = 3.dp.toPx
    val spacingPx = 2.dp.toPx

    Canvas(modifier) {
        val maxBarHeight = size.height

        audioSpectralData.forEachIndexed { index, animatable ->
            val barH = min(animatable * maxBarHeight, maxBarHeight)
            val x = index * (barWidthPx + spacingPx)
            val y = (maxBarHeight - barH) / 2

            drawRoundRect(
                color = barColor,
                topLeft = Offset(x = x, y = y),
                size = Size(width = barWidthPx, height = barH),
                cornerRadius = CornerRadius(5f, 5f),
                style = Fill
            )
        }
    }
}