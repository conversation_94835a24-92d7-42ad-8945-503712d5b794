package com.deeptalkie.main.compose.ui.page.main.chats

import android.content.Context
import androidx.room.Transaction
import com.deeptalkie.kidsguard.net.asyncSuccess
import com.deeptalkie.kidsguard.net.getSuccessData
import com.deeptalkie.kidsguard.net.runHttp
import com.deeptalkie.main.App
import com.deeptalkie.main.Membership
import com.deeptalkie.main.bean.RandomChatData
import com.deeptalkie.main.compose.ui.page.chat.loadVideoTime
import com.deeptalkie.main.db.result.AIRoleSessionInfo
import com.deeptalkie.main.db.table.UserAIRole
import com.deeptalkie.main.repo.BaseDeepTalkieRepo
import com.deeptalkie.main.view.RequirePurchaseDialog
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class ChatsRepo : BaseDeepTalkieRepo() {
    fun queryAllSession(userId: String?) = aiRoleSessionDao.querySessionsWithInfo(userId)

    suspend fun requestSessionList(userId: String) = withContext(Dispatchers.IO) {
        runHttp { remoteApi.getSessionList() }?.asyncSuccess { sessions ->
            launch {
                aiRoleDao.upsertAll(sessions.map { it.toAIRole() })
            }
            launch @Transaction {
                // 如果有从别的手机上删除掉的会话，需要删除掉本地的会话
                val notExistRemoteSession =
                    userAIRoleDao.getNotExistSession(userId, sessions.map { it.sessionId })
                if (notExistRemoteSession.isNotEmpty()) {
                    userAIRoleDao.deleteNotExistSessionInfo(
                        userId,
                        notExistRemoteSession.mapNotNull { it.sessionId })
                    msgRecordDao.deleteRolesMsg(userId, notExistRemoteSession.map { it.roleId })
                }
                userAIRoleDao.upsertAll(sessions.map { it.toUserAIRole(userId) })
            }
        }
    }

    suspend fun getSessionsCount(): Int {
        val userId = Membership.getUserId() ?: return 5
        return userAIRoleDao.getSessionsCount(userId)
    }

    suspend fun requestChat(
        id: Long,
        context: Context,
        gotoLogin: () -> Unit,
        gotoBuy: () -> Unit
    ): Long? {
        if (!chatCountCheck(context, gotoLogin, gotoBuy)) return null

        val userId = Membership.getUserId() ?: return null
        val localSessionId = userAIRoleDao.getSessionId(userId, id)
        if (localSessionId != null) return localSessionId
        val resp = runHttp { remoteApi.requestChat(id) } ?: return null
        val sessionId = resp.getSuccessData()?.sessionId

        val userAIRole =
            UserAIRole(userId = userId, roleId = id, sessionId = sessionId, sessionTopUpAt = 0)
        userAIRoleDao.upsertOne(userAIRole)

        return sessionId
    }

    private suspend fun chatCountCheck(
        context: Context,
        gotoLogin: () -> Unit,
        gotoBuy: () -> Unit
    ): Boolean {
        if (!Membership.isLogin()) {
            gotoLogin()
            return false
        }
        if (!Membership.isVip()) {
            val sessionsCount = getSessionsCount()

            if (sessionsCount >= 5) {
                RequirePurchaseDialog(
                    context,
                    RequirePurchaseDialog.NUMBER_OF_CHATS,
                    gotoBuy
                ).show()
                return false
            }
        }
        return true
    }

    suspend fun randomChat(
        context: Context,
        gotoLogin: () -> Unit,
        gotoBuy: () -> Unit
    ): Pair<Long, Long>? {
        if (!chatCountCheck(context, gotoLogin, gotoBuy)) return null

        val userId = Membership.getUserId() ?: return null
        val resp = runHttp { remoteApi.randomChat() } ?: return null
        val data = resp.getSuccessData() ?: return null
        insertAIRoleAndSession(userId, data)
        return data.roleId to data.sessionId
    }

    @Transaction
    suspend fun insertAIRoleAndSession(userId: String, randomChatData: RandomChatData) {
        aiRoleDao.upsertOne(randomChatData.toAIRole())
        userAIRoleDao.upsertOne(
            UserAIRole(
                userId = userId,
                roleId = randomChatData.roleId,
                sessionId = randomChatData.sessionId,
                sessionTopUpAt = 0
            )
        )
    }

    suspend fun requestChatMessages(
        userId: String,
        roleId: Long,
        sessionId: Long,
        laseMsgId: Long? = null,
        pageSize: Int = 100,
    ): Boolean = coroutineScope {
        val messages = runHttp { remoteApi.requestChatMessages(sessionId, laseMsgId, pageSize) }
            ?.getSuccessData() ?: return@coroutineScope true
        launch {
            msgRecordDao.upsertAll(messages.map {
                async {
                    val videoTime = loadVideoTime(it.type, it.content)
                    it.toMsgRecord(userId, roleId, videoTime).apply {
                        loadImage()
                    }
                }
            }.map { it.await() })
        }
        messages.size >= pageSize
    }

    suspend fun favoriteFun(userId: String, roleId: Long, state: Int) =
        withContext(Dispatchers.IO) {
            launch {
                userAIRoleDao.upsertOne(UserAIRole(userId, roleId, isFavorite = state == 1))
            }
            launch {
                runHttp { remoteApi.favorite(roleId, state) }
            }
        }

    suspend fun deleteSession(userId: String, roleId: Long, sessionId: Long) =
        withContext(Dispatchers.IO) {
            launch {
                msgRecordDao.deleteSession(userId, roleId)
                userAIRoleDao.deleteSession(userId, roleId)
            }
            launch {
                runHttp { remoteApi.deleteSession(sessionId) }
            }
        }

    /**
     * 置顶方法
     */
    suspend fun pipToTop(userId: String, roleId: Long, sessionId: Long, pipToTop: Int) =
        withContext(Dispatchers.IO) {
            launch {
                val sessionTopUpAt = if (pipToTop == 0) 0 else System.currentTimeMillis()
                userAIRoleDao.upsertOne(UserAIRole(userId, roleId, sessionTopUpAt = sessionTopUpAt))
            }
            launch {
                runHttp { remoteApi.pipToTop(sessionId, pipToTop) }
            }
        }

    /**
     * 更新本地音频的路径方法
     */
    suspend fun setVoicePath(id: Long, path: String) {
        msgRecordDao.setMsgVoicePath(id, path)
    }

    fun preloadMsgRecord(sessions: List<AIRoleSessionInfo>) {
        App.launch {
            sessions.forEach { (userAIRole, _, _) ->
                App.launch {
                    if (userAIRole.sessionId != null) {
                        val isNoMsgRecord =
                            msgRecordDao.isNoMsgRecord(userAIRole.userId, userAIRole.roleId)
                        if (isNoMsgRecord) {
                            requestChatMessages(
                                userAIRole.userId,
                                userAIRole.roleId,
                                userAIRole.sessionId
                            )
                        }
                    }
                }
            }
        }
    }
}