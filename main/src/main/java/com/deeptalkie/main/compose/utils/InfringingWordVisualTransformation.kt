package com.deeptalkie.main.compose.utils

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.input.OffsetMapping
import androidx.compose.ui.text.input.TransformedText
import androidx.compose.ui.text.input.VisualTransformation

/**
 * 自定义的VisualTransformation，用于将不合规的文字标记为红色。
 * @param fullText TextField的完整文本内容。
 * @param infringingWords 从API返回的不合规关键词。
 */
class InfringingWordVisualTransformation(
    private val fullText: String,
    private val infringingWords: List<String>
) : VisualTransformation {
    override fun filter(text: AnnotatedString): TransformedText {
        // 如果没有不合规的词，或者列表为空，则不做任何转换
        if (infringingWords.isEmpty()) {
            return TransformedText(text, OffsetMapping.Identity)
        }

        // 创建AnnotatedString.Builder来构建带有样式的文本
        val builder = AnnotatedString.Builder(fullText)

        // 遍历所有不合规的关键词
        for (word in infringingWords) {
            // 查找关键词在完整文本中的所有出现位置
            var startIndex = fullText.indexOf(word, ignoreCase = true)
            while (startIndex != -1) {
                val endIndex = startIndex + word.length
                // 对不合规的词应用红色样式
                builder.addStyle(
                    SpanStyle(color = Color(0xFFF74040)),
                    startIndex,
                    endIndex
                )
                // 继续查找下一个出现的位置
                startIndex = fullText.indexOf(word, startIndex + word.length, ignoreCase = true)
            }
        }

        // 返回转换后的文本，OffsetMapping.Identity表示字符位置不变
        return TransformedText(builder.toAnnotatedString(), OffsetMapping.Identity)
    }
}