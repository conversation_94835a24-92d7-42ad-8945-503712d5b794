package com.deeptalkie.main.compose.ui.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.PagerScope
import androidx.compose.foundation.pager.PagerState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.Stable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.runtime.staticCompositionLocalOf
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import com.deeptalkie.main.compose.ui.modifier.click
import kotlinx.coroutines.launch

@Stable
data class DTNavigationItemData(
    val id: Long,
    val icon: SelectableDrawable,
)

val LocalDTNavigationBarHeight = staticCompositionLocalOf { 0.dp }

@Composable
fun DTNavigationBarScaffold(
    pagerState: PagerState,
    navigationData: List<DTNavigationItemData>,
    centerItem: DTNavigationItemData? = null,
    onCenterClick: () -> Unit = {},
    pageContent: @Composable PagerScope.(Int) -> Unit,
) {
    val scope = rememberCoroutineScope()

    val navigationHeightDp = with(LocalDensity.current) {
        val navigationHeight by rememberUpdatedState(WindowInsets.navigationBars.getBottom(this))
        navigationHeight.toDp()
    }

    CompositionLocalProvider(
        LocalDTNavigationBarHeight provides (51.dp + navigationHeightDp)
    ) {
        Box(Modifier.fillMaxSize()) {
            HorizontalPager(
                pagerState,
                userScrollEnabled = false,
                key = { navigationData[it].id },
                pageContent = pageContent,
            )
            DTNavigationBar(
                pagerState.currentPage,
                navigationData,
                centerItem,
                Modifier.align(Alignment.BottomCenter),
                onCenterClick = onCenterClick,
                onSelected = {
                    scope.launch {
                        pagerState.scrollToPage(it)
                    }
                }
            )
        }
    }
}

@Composable
fun DTNavigationBar(
    index: Int,
    data: List<DTNavigationItemData>,
    centerItem: DTNavigationItemData?,
    modifier: Modifier = Modifier,
    onCenterClick: () -> Unit,
    onSelected: (index: Int) -> Unit,
) {
    val navData = remember(data) {
        if (centerItem != null) {
            data.toMutableList().apply {
                add(data.size / 2, centerItem)
            }
        } else {
            data
        }
    }
    Row(
        modifier
            .fillMaxWidth()
            .height(LocalDTNavigationBarHeight.current)
            .background(Color(0xFF1B1926))
            .click {}
            .border(
                1.dp,
                brush = Brush.verticalGradient(listOf(Color(0x1AFFFFFF), Color(0x00FFFFFF))),
                RoundedCornerShape(0.dp)
            )
            .navigationBarsPadding()
    ) {
        navData.forEachIndexed { i, item ->
            val centerIndex = data.size / 2
            if (centerItem != null && i == centerIndex) {
                DTCenterNavigationItem(item, Modifier.weight(1f), onCenterClick)
            } else {
                val realIndex = if (centerItem != null && i > centerIndex) i - 1 else i
                DTNavigationItem(
                    item,
                    realIndex == index,
                    Modifier.weight(1f),
                ) {
                    onSelected(realIndex)
                }
            }
        }
    }
}

@Composable
fun DTCenterNavigationItem(
    item: DTNavigationItemData,
    modifier: Modifier = Modifier,
    onClick: () -> Unit
) {
    Box(
        modifier
            .fillMaxHeight()
            .clickable(onClick = onClick),
        contentAlignment = Alignment.Center
    ) {
        Image(
            painterResource(item.icon.enableDrawable),
            null,
            Modifier.size(51.dp),
        )
    }
}

@Composable
fun DTNavigationItem(
    data: DTNavigationItemData,
    selected: Boolean,
    modifier: Modifier = Modifier,
    onClick: () -> Unit
) {
    val icon = if (selected) data.icon.enableDrawable else data.icon.disableDrawable

    Box(
        modifier
            .fillMaxHeight()
            .clickable(onClick = onClick),
        contentAlignment = Alignment.Center
    ) {
        Image(
            painterResource(icon),
            null,
            Modifier.size(26.dp),
        )
    }
}