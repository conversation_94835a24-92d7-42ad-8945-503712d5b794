package com.deeptalkie.main.compose.ui.dialog

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.deeptalkie.main.R
import com.deeptalkie.main.compose.theme.White10
import com.deeptalkie.main.compose.ui.components.DTButton
import com.deeptalkie.main.compose.ui.components.DTVerticalSpacer
import com.deeptalkie.main.db.table.AIRoleTag

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SelectTagBottomSheet(
    tags: List<AIRoleTag>,
    selectedTags: List<AIRoleTag>,
    onTagClick: (AIRoleTag, Boolean) -> Unit,
    onClickCreateTag: () -> Unit,
    onDismiss: () -> Unit
) {
    DTBottomSheet(
        onDismiss = onDismiss,
        containerColor = Color(0xFF28282D)
    ) {
        Column(
            Modifier
                .fillMaxWidth()
                .navigationBarsPadding(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            DTVerticalSpacer(6.dp)
            Title()
            DTVerticalSpacer(20.dp)
            TagList(tags, selectedTags, onClickCreateTag, onTagClick)
            DTVerticalSpacer(20.dp)
            ConfirmBtn(selectedTags.isNotEmpty(), onConfirm = onDismiss)
            DTVerticalSpacer(20.dp)
        }
    }
}

@Composable
private fun Title() {
    Text(
        stringResource(R.string.create_ai_role_page_select_tag_bottom_sheet_title),
        style = MaterialTheme.typography.headlineMedium.copy(
            color = MaterialTheme.colorScheme.onPrimary,
            lineHeight = 24.sp
        )
    )
}

@Composable
private fun TagList(
    tags: List<AIRoleTag>,
    selectedTags: List<AIRoleTag>,
    onAddTag: () -> Unit,
    onTagClick: (AIRoleTag, Boolean) -> Unit,
) {
    FlowRow(
        Modifier
            .padding(horizontal = 16.dp)
            .fillMaxWidth()
            .heightIn(max = 240.dp)
            .verticalScroll(rememberScrollState()),
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        tags.forEach { tag ->
            val selectedTag = selectedTags.any { it.id == tag.id }
            Box(
                Modifier
                    .height(30.dp)
                    .clip(RoundedCornerShape(50))
                    .clickable {
                        onTagClick(tag, !selectedTag)
                    }
                    .border(
                        1.dp,
                        if (selectedTag) MaterialTheme.colorScheme.primary else Color.Transparent,
                        RoundedCornerShape(50)
                    )
                    .background(White10)
                    .padding(horizontal = 14.dp),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    tag.name,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    style = MaterialTheme.typography.bodyMedium.copy(
                        color = MaterialTheme.colorScheme.onPrimary,
                        fontSize = 14.sp
                    )
                )
            }
        }
        Box(
            Modifier
                .size(40.dp, 30.dp)
                .clip(RoundedCornerShape(50))
                .clickable(onClick = onAddTag)
                .background(White10),
            contentAlignment = Alignment.Center
        ) {
            Icon(
                painterResource(R.drawable.ic_add_tag),
                null,
                Modifier.size(12.dp),
                Color.Unspecified
            )
        }
    }
}

@Composable
private fun ConfirmBtn(enable: Boolean, onConfirm: () -> Unit) {
    DTButton(
        R.string.confirm,
        modifier = Modifier
            .padding(horizontal = 16.dp)
            .fillMaxWidth()
            .height(44.dp),
        enable = enable,
        containerColor = MaterialTheme.colorScheme.primary,
        disabledContainerColor = Color(0xFF636364),
        onClick = onConfirm
    )
}