package com.deeptalkie.main.compose.ui.page.main.mine

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.deeptalkie.main.R
import com.deeptalkie.main.compose.theme.SocialBlue
import com.deeptalkie.main.compose.theme.White
import com.deeptalkie.main.compose.ui.components.DTLoading
import com.deeptalkie.main.compose.ui.modifier.click
import com.deeptalkie.main.ext.showToast
import com.imyfone.membership.api.bean.MemberBean
import kotlinx.coroutines.flow.SharedFlow

/**
 *creater:linjinhao on 2025/5/28 17:04
 */

private val Type_First_Name = "First_Name"
private val Type_Last_Name = "Last_Name"

@Composable
fun ChangeUserInfoRoute(
    tag: String,
    viewModel: SvChangeUserInfoViewModel = viewModel(),
    onBack: () -> Unit
) {
    ChangeUserInfoScreen(tag = tag, viewModel = viewModel, onBack = { onBack() })
    HandleEvent(viewModel.event) {
        onBack()
    }
}

@Composable
private fun ChangeUserInfoScreen(
    tag: String,
    viewModel: SvChangeUserInfoViewModel = viewModel(),
    onBack: () -> Unit
) {
    val focusManager = LocalFocusManager.current
    val softwareKeyboardController = LocalSoftwareKeyboardController.current

    Box(
        modifier = Modifier
            .click {
                softwareKeyboardController?.hide()
                focusManager.clearFocus()
            }
            .background(color = colorResource(R.color.color_13111B))
            .fillMaxSize()
    ) {
        val state by viewModel.state.collectAsState()
        Column(
            modifier = Modifier
                .statusBarsPadding()
                .fillMaxSize()
        ) {
            val member by viewModel.member.collectAsState(null)
            var value by rememberValue(type = tag, member)
            val title by rememberTitle(type = tag)
            Spacer(modifier = Modifier.height(10.dp))
            TitleTopBar(title = title, onBack = { onBack() }, onChangeValue = {
                focusManager.clearFocus()
                softwareKeyboardController?.hide()
                when (tag) {
                    Type_First_Name -> {
                        viewModel.changeFirstName(value = value)
                    }

                    Type_Last_Name -> {
                        viewModel.changeLastName(value = value)
                    }
                }
            })
            Spacer(modifier = Modifier.height(30.dp))
            UserInfoTextField(
                value = value,
                onChangeValue = { value = it },
                onClear = { value = "" })
        }
        DTLoading(loading = state) { }
    }
}

@Composable
private fun TitleTopBar(title: String, onBack: () -> Unit, onChangeValue: () -> Unit) {
    Row(
        modifier = Modifier
            .padding(horizontal = 18.dp)
            .fillMaxWidth(),
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Image(
            painterResource(R.drawable.ic_top_bar_back),
            contentDescription = "BackIcon",
            modifier = Modifier
                .click { onBack() }
                .size(24.dp)
        )
        Spacer(modifier = Modifier.width(5.dp))
        Text(
            text = title,
            style = TextStyle(
                textAlign = TextAlign.Center,
                fontSize = 16.sp,
                fontWeight = FontWeight.W600,
                color = White
            ),
            modifier = Modifier.weight(1f, true)
        )
        Spacer(modifier = Modifier.width(5.dp))
        Text(
            stringResource(R.string.save),
            style = TextStyle(
                textAlign = TextAlign.Center,
                fontSize = 14.sp,
                fontWeight = FontWeight.W500,
                color = White
            ), modifier = Modifier
                .clickable(enabled = true) {
                    onChangeValue()
                }
                .background(SocialBlue, shape = RoundedCornerShape(8.dp))
                .wrapContentSize()
                .padding(horizontal = 12.dp, vertical = 6.dp)
        )
    }
}

@Composable
private fun UserInfoTextField(value: String, onChangeValue: (String) -> Unit, onClear: () -> Unit) {
    Box(
        Modifier
            .padding(horizontal = 18.dp)
            .background(colorResource(R.color.color_2B2B30), RoundedCornerShape(12.dp))
            .fillMaxWidth()
    ) {
        BasicTextField(
            value = value,
            onValueChange = {
                onChangeValue(it)
            },
            singleLine = true,
            textStyle = TextStyle(fontSize = 15.sp, color = White),
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Text),
            modifier = Modifier
                .height(52.dp)
                .fillMaxWidth()
                .padding(horizontal = 16.dp),
            decorationBox = { innerTextField ->
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.padding(horizontal = 10.dp)
                ) {
                    Box(
                        modifier = Modifier.weight(1f),
                        contentAlignment = Alignment.CenterStart
                    ) {
                        innerTextField()
                    }
                    if (value.isNotEmpty()) {
                        IconButton(
                            onClick = { onClear() },
                        ) {
                            Icon(
                                painterResource(R.drawable.ic_close1),
                                modifier = Modifier.size(24.dp),
                                contentDescription = "clearIcon",
                                tint = White
                            )
                        }
                    }
                }
            })
    }

}

@Composable
private fun rememberValue(type: String, memberBean: MemberBean?): MutableState<String> {
    return remember(type, memberBean) {
        val string = when (type) {
            Type_First_Name -> memberBean?.firstName ?: ""
            else -> memberBean?.lastName ?: ""
        }
        println("rememberValueState = ${string}")
        mutableStateOf(string)
    }
}

@Composable
private fun rememberTitle(type: String): MutableState<String> {
    val context = LocalContext.current
    return remember(type) {
        val string = when (type) {
            Type_First_Name -> context.getString(R.string.first_name)
            else -> context.getString(R.string.last_name)
        }
        println("rememberValueState = ${string}")
        mutableStateOf(string)
    }
}


@Composable
private fun HandleEvent(event: SharedFlow<ChangeValueEvent>, onChangeSuccess: () -> Unit) {
    val context = LocalContext.current
    LaunchedEffect(event) {
        event.collect {
            when (it) {
                ChangeValueEvent.ChangeFail -> {
                    showToast(context.getString(R.string.network_error))
                }

                ChangeValueEvent.ChangeSuccess -> {
                    onChangeSuccess()
                }
            }
        }
    }
}