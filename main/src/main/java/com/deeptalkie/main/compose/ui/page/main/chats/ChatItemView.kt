package com.deeptalkie.main.compose.ui.page.main.chats

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.constraintlayout.compose.ChainStyle
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import androidx.constraintlayout.compose.Visibility
import coil3.compose.AsyncImage
import com.deeptalkie.main.R
import com.deeptalkie.main.compose.theme.LightGray
import com.deeptalkie.main.compose.theme.White8
import com.deeptalkie.main.db.result.AIRoleSessionInfo
import com.deeptalkie.main.ext.imageRequest

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun ChatItemView(chat: AIRoleSessionInfo, onClick: () -> Unit, onLongClick: () -> Unit) {
    Box(
        Modifier
            .fillMaxWidth()
            .height(82.dp)
    ) {
        ConstraintLayout(
            Modifier
                .padding(horizontal = 8.dp)
                .fillMaxSize()
                .clip(RoundedCornerShape(15.dp))
                .combinedClickable(
                    onClick = onClick,
                    onLongClick = onLongClick
                )
        ) {
            if (chat.userAIRole.isTopUp) {
                Image(
                    painterResource(R.drawable.image_chat_item_top_up_bg),
                    null,
                    Modifier.fillMaxSize(),
                    contentScale = ContentScale.FillBounds
                )
            }

            val (avatarRef, nameRef, msgRef, unreadNumRef, dateRef) = createRefs()
            createVerticalChain(
                nameRef,
                msgRef.withChainParams(topMargin = 4.dp),
                chainStyle = ChainStyle.Packed
            )

            AsyncImage(
                chat.aiRole.avatar.imageRequest(LocalContext.current),
                chat.aiRole.name,
                Modifier
                    .size(46.dp)
                    .clip(CircleShape)
                    .constrainAs(avatarRef) {
                        start.linkTo(parent.start, 16.dp)
                        top.linkTo(parent.top)
                        bottom.linkTo(parent.bottom)
                    },
                contentScale = ContentScale.Crop
            )
            Text(
                chat.aiRole.name,
                Modifier.constrainAs(nameRef) {
                    width = Dimension.fillToConstraints
                    start.linkTo(avatarRef.end, 12.dp)
                    top.linkTo(parent.top)
                    end.linkTo(unreadNumRef.start, 12.dp, 16.dp)
                    bottom.linkTo(msgRef.top)
                },
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                style = MaterialTheme.typography.headlineLarge.copy(
                    color = MaterialTheme.colorScheme.onPrimary,
                    fontSize = 14.sp,
                    lineHeight = 20.sp
                )
            )
            Text(
                chat.userAIRole.lastMsg.orEmpty(),
                Modifier.constrainAs(msgRef) {
                    width = Dimension.fillToConstraints
                    start.linkTo(nameRef.start)
                    top.linkTo(nameRef.bottom)
                    end.linkTo(dateRef.start, 12.dp)
                    bottom.linkTo(parent.bottom)
                },
                color = MaterialTheme.colorScheme.onSecondary,
                fontSize = 13.sp,
                fontWeight = FontWeight.W400,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
            Box(
                Modifier
                    .size(18.dp)
                    .background(Color(0xFFFF4D6A), CircleShape)
                    .constrainAs(unreadNumRef) {
                        if (chat.unreadCount < 1) {
                            visibility = Visibility.Gone
                        }
                        end.linkTo(parent.end, 16.dp)
                        top.linkTo(nameRef.top)
                        bottom.linkTo(nameRef.bottom)
                    },
                contentAlignment = Alignment.Center
            ) {
                Text(
                    "${chat.unreadCount}",
                    color = MaterialTheme.colorScheme.onPrimary,
                    fontSize = 12.sp,
                    fontWeight = FontWeight.W400,
                    textAlign = TextAlign.Center,
                    lineHeight = 16.sp
                )
            }
            Text(
                chat.userAIRole.lastMsgDatetime,
                Modifier.constrainAs(dateRef) {
                    end.linkTo(parent.end, 16.dp)
                    top.linkTo(msgRef.top)
                    bottom.linkTo(msgRef.bottom)
                },
                color = LightGray,
                fontSize = 11.sp,
                fontWeight = FontWeight.W400,
            )
        }

        if (!chat.userAIRole.isTopUp) {
            HorizontalDivider(
                Modifier
                    .fillMaxWidth()
                    .align(Alignment.BottomCenter),
                0.5.dp,
                color = White8
            )
        }
    }
}