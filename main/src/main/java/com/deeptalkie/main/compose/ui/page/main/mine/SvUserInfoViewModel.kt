package com.deeptalkie.main.compose.ui.page.main.mine

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.deeptalkie.main.Membership
import com.deeptalkie.main.Membership.clearVipStatInfo
import com.deeptalkie.main.bean.UserManager
import com.imyfone.membership.repository.VerificationCode
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 *creater:linjinhao on 2025/5/27 11:40
 */
class SvUserInfoViewModel : ViewModel() {
    companion object {
        var clickLogout = false
            private set

        fun resetClickLogout() {
            clickLogout = false
        }
    }

    private val client = Membership.membershipClient
    private val account = Membership.membershipClient.account
    val member = account.memberFlow
    val vipStateFlow = UserManager.vipState

    private val _state = MutableStateFlow(false)
    val state = _state.asStateFlow()

    private val _userInformationEvent = MutableSharedFlow<UserInformationEvent>()
    val userInformationEvent = _userInformationEvent.asSharedFlow()

    private val _logoutEvent = MutableSharedFlow<LogoutEvent>()
    val logoutEvent = _logoutEvent.asSharedFlow()

    val user = account.userFlow
    val setSendVerificationCodeTime = UserManager.sendVerificationCodeTime
    fun logout() {
        viewModelScope.launch {
            clickLogout = true
            withContext(Dispatchers.IO) {
                clearVipStatInfo()
                client.clearData()
            }
            _logoutEvent.emit(LogoutEvent.Logout)
        }
    }

    fun cancelAccount(code: String) {
        viewModelScope.launch {
            _state.emit(true)
            val response = account.cancelAccount(code = code)
            _state.emit(false)
            if (response.isSuccess) {
                clearVipStatInfo()
                _userInformationEvent.emit(UserInformationEvent.CancelAccountSuccess)
            } else if (response.code == 421) {
                _userInformationEvent.emit(UserInformationEvent.CodeInputInvalid)
            } else {
                _userInformationEvent.emit(UserInformationEvent.CancelAccountFail)
            }

        }
    }

    fun getCode(sendEmail: String, email: String) {
        viewModelScope.launch {
            _state.emit(true)
            val response = account.sendVerificationCode(
                sendEmail = sendEmail,
                email = email,
                code = VerificationCode.CANCEL_ACCOUNT
            )
            _state.emit(false)
            if (response.isSuccess) {
                UserManager.setSendVerificationCodeTime(System.currentTimeMillis())
                val noticeEmail = response.data?.noticeEmail ?: ""
                _userInformationEvent.emit(UserInformationEvent.GetCodeSuccess(email = noticeEmail))
            } else if (response.code == 400) {
                _userInformationEvent.emit(UserInformationEvent.GetCodeToMany(response.msg ?: ""))
            } else {
                _userInformationEvent.emit(UserInformationEvent.GetCodeError)
            }
        }
    }

    fun checkPermissionStatus() {
        viewModelScope.launch {
            _state.emit(true)
            val response = account.checkPermissionStatus()
            _state.emit(false)
            val data = response.data
            if (response.isSuccess && data != null) {
                when {
                    data.subscribeStatus -> { //有订阅权益
                        _userInformationEvent.emit(UserInformationEvent.AccountExitsSubscriptPermission)
                    }

                    data.permissionStatus -> { //有权益
                        _userInformationEvent.emit(UserInformationEvent.AccountExitsPermission)
                    }

                    else -> {
                        _userInformationEvent.emit(UserInformationEvent.AccountExitNoPermission)
                    }
                }
            } else {
                _userInformationEvent.emit(UserInformationEvent.CommonError)
            }
        }
    }
}

sealed interface LogoutEvent {
    data object Logout : LogoutEvent
}

sealed interface UserInformationEvent {
    data object AccountExitsPermission : UserInformationEvent  //有非订阅的权益
    data object AccountExitsSubscriptPermission : UserInformationEvent //有订阅的权益
    data object AccountExitNoPermission : UserInformationEvent
    data object CommonError : UserInformationEvent

    /**
     * 验证码输入无效
     */
    object CodeInputInvalid : UserInformationEvent

    /**
     * 取消账号成功
     */
    object CancelAccountSuccess : UserInformationEvent

    /**
     * 取消账号失败
     */
    object CancelAccountFail : UserInformationEvent

    /**
     * 获取验证码成功
     */
    class GetCodeSuccess(val email: String) : UserInformationEvent

    /**
     * 获取验证码过于频繁
     */
    class GetCodeToMany(val msg: String) : UserInformationEvent

    /**
     * 获取验证码失败
     */
    object GetCodeError : UserInformationEvent

}