package com.deeptalkie.main.compose.ui.components

import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

@Composable
fun DTBlurTag(
    icon: Painter,
    text: String,
    iconColor: Color,
    modifier: Modifier = Modifier
) {
    Row(modifier, verticalAlignment = Alignment.CenterVertically) {
        DTHorizontalSpacer(6.dp)
        Icon(
            icon,
            null,
            Modifier.size(10.dp),
            tint = iconColor
        )
        DTHorizontalSpacer(2.dp)
        Text(
            text,
            style = MaterialTheme.typography.headlineLarge.copy(
                color = MaterialTheme.colorScheme.onPrimary,
                fontSize = 10.sp,
                lineHeight = 13.sp
            ),
        )
        DTHorizontalSpacer(6.dp)
    }
}