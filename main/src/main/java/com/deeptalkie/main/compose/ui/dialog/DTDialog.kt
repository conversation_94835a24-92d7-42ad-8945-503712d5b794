package com.deeptalkie.main.compose.ui.dialog

import android.app.Activity
import android.content.Context
import android.content.ContextWrapper
import android.view.Window
import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.deeptalkie.main.compose.theme.DTTheme

@Composable
fun DTDialog(
    onDismiss: () -> Unit,
    properties: DialogProperties = DialogProperties(
        usePlatformDefaultWidth = false
    ),
    content: @Composable (Window?) -> Unit
) {
    Dialog(onDismiss, properties) {
        DTTheme {
            content(findWindow(LocalView.current.context))
        }
    }
}

fun findWindow(context: Context): Window? {
    if (context is Activity) {
        return context.window
    }
    if (context is ContextWrapper) {
        return findWindow(context.baseContext)
    }
    return null
}