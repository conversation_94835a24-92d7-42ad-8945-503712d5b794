package com.deeptalkie.main.compose.utils

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.imyfone.membership.api.bean.Page
import com.mfccgroup.android.httpclient.adapter.API

/**
 *creater:lin<PERSON><PERSON> on 2025/6/25 14:18
 */

fun <Item : Any> createIntSourceFactory(fetchList: suspend (Int) -> API<Page<Item>>): PagingSource<Int, Item> {
    return IntSourceFactory(fetchList)
}


internal class IntSourceFactory<Item : Any>(val fetchList: suspend (Int) -> API<Page<Item>>) :
    PagingSource<Int, Item>() {
    override fun getRefreshKey(state: PagingState<Int, Item>): Int? {
        return state.anchorPosition?.let { anchorPosition ->
            val anchorPage = state.closestPageToPosition(anchorPosition)
            anchorPage?.prevKey?.plus(1) ?: anchorPage?.nextKey?.minus(1)
        }
    }

    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, Item> {
        val page = params.key ?: 1
        val response = fetchList(page)
        return if (response.isSuccess) {
            val prevKey = if (page == 1) null else page - 1
            val dataList = response.data?.data
            val nextKey = if (dataList.isNullOrEmpty()) null else page + 1
            LoadResult.Page(data = dataList ?: emptyList(), prevKey = prevKey, nextKey = nextKey)
        } else {
            LoadResult.Error(response.originThrowable ?: RuntimeException(response.msg ?: ""))
        }
    }

}
