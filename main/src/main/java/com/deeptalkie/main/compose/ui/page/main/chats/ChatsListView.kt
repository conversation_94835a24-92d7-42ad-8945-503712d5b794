package com.deeptalkie.main.compose.ui.page.main.chats

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.deeptalkie.main.compose.navigation.MainRoute
import com.deeptalkie.main.compose.ui.components.DTVerticalSpacer
import com.deeptalkie.main.compose.ui.components.LocalDTNavigationBarHeight
import kotlinx.coroutines.launch

@Composable
fun ChatsListView(
    viewModel: ChatsViewModel = viewModel(),
    onClickExplore: () -> Unit,
    navigate: (MainRoute) -> Unit
) {
    val scope = rememberCoroutineScope()
    val context = LocalContext.current

    val chats by viewModel.chatsStateFlow.collectAsStateWithLifecycle()

    LaunchedEffect(Unit) {
        viewModel.requestChats()
    }

    Box(Modifier.fillMaxSize()) {
        if (chats.isEmpty()) {
            EmptyChatsPage(onClickRandomMatch = {
                scope.launch {
                    viewModel.showLoading(true)
                    val res = viewModel.randomChat(
                        context, gotoLogin = {
                            navigate(MainRoute.Login)
                        },
                        gotoBuy = {
                            navigate(MainRoute.Product)
                        }
                    )
                    viewModel.showLoading(false)
                    if (res == null) return@launch
                    val (roleId, sessionId) = res
                    navigate(MainRoute.Chat(roleId, sessionId))
                }
            }, onClickExplore)
        } else {
            LazyColumn(Modifier.fillMaxSize()) {
                items(chats, key = { it.aiRole.id }, contentType = { "ChatItemView" }) { chat ->
                    ChatItemView(
                        chat = chat,
                        onClick = {
                            if (chat.userAIRole.sessionId != null) {
                                navigate(MainRoute.Chat(chat.aiRole.id, chat.userAIRole.sessionId))
                            }
                        },
                        onLongClick = {
                            viewModel.showChatDialog(chat)
                        }
                    )
                }

                item(key = "NAVIGATION_HEIGHT_SPACER") {
                    DTVerticalSpacer(LocalDTNavigationBarHeight.current)
                }
            }
        }
    }
    viewModel.longClickingChat?.let { chat ->
        ChatDialog(
            chat.aiRole.name,
            chat.userAIRole.isTopUp,
            onDismiss = {
                viewModel.showChatDialog(null)
            },
            onItemClick = { type ->
                when (type) {
                    ChatDialogItemType.Del -> {
                        val deletingChat = viewModel.longClickingChat
                        viewModel.showChatDialog(null)
                        viewModel.showDelChatDialog(deletingChat)
                    }

                    ChatDialogItemType.TopUp -> {
                        val pipTop = if (chat.userAIRole.isTopUp) 0 else 1
                        viewModel.pipToTop(
                            chat.aiRole.id,
                            chat.userAIRole.sessionId ?: 0,
                            pipTop
                        )
                        viewModel.showChatDialog(null)
                    }
                }
            }
        )
    }

    viewModel.deletingChat?.let { chat ->
        DeleteChatDialog(
            onDismiss = {
                viewModel.showDelChatDialog(null)
            }, onDelete = {
                viewModel.deleteSession(
                    chat.aiRole.id,
                    chat.userAIRole.sessionId ?: 0
                )
                viewModel.showDelChatDialog(null)
            }
        )
    }
}