package com.deeptalkie.main.compose.ui.page.chat

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.deeptalkie.main.R
import com.deeptalkie.main.compose.theme.Black
import com.deeptalkie.main.compose.theme.White90
import com.deeptalkie.main.compose.ui.components.DTHorizontalSpacer
import com.deeptalkie.main.compose.ui.components.DTPopup

@Composable
fun DeletePopup(
    modifier: Modifier = Modifier,
    onDismiss: () -> Unit = {},
    onClick: () -> Unit,
) {
    DTPopup(
        modifier,
        alignment = Alignment.TopStart,
        offsetY = 4.dp,
        onDismissRequest = onDismiss
    ) {
        Row(
            Modifier
                .width(142.dp)
                .height(44.dp)
                .clip(RoundedCornerShape(10.dp))
                .clickable(onClick = onClick)
                .background(White90),
            verticalAlignment = Alignment.CenterVertically
        ) {
            DTHorizontalSpacer(12.dp)
            Icon(
                painterResource(R.drawable.ic_msg_del),
                stringResource(R.string.Delete),
                Modifier.size(24.dp),
                tint = Black
            )
            DTHorizontalSpacer(6.dp)
            Text(
                stringResource(R.string.Delete),
                color = Black,
                fontSize = 14.sp,
                fontWeight = FontWeight.W500,
                lineHeight = 20.sp,
            )
        }
    }
}