package com.deeptalkie.main.compose.ui.page.main.chats

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.deeptalkie.main.R
import com.deeptalkie.main.compose.theme.White
import com.deeptalkie.main.compose.ui.components.DTButtonTextStyle
import com.deeptalkie.main.compose.ui.components.DTIconButton
import com.deeptalkie.main.compose.ui.components.DTVerticalSpacer
import com.deeptalkie.main.compose.ui.components.LocalDTNavigationBarHeight
import com.deeptalkie.main.compose.ui.components.SelectableDrawable

@Composable
fun EmptyChatsPage(
    onClickRandomMatch: () -> Unit,
    onClickExplore: () -> Unit,
) {
    val configuration = LocalConfiguration.current
    val screenHeight = configuration.screenHeightDp
    // 小于等于667dp，则认为是小屏
    val isSmallScreen = remember(screenHeight) { screenHeight <= 667 }

    Column(
        Modifier
            .fillMaxSize()
            .navigationBarsPadding(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Spacer(Modifier.weight(1f))
        Image(
            painterResource(R.drawable.image_empty_chats),
            null,
            Modifier
                .width(245.dp)
                .height(95.dp)
        )
        DTVerticalSpacer(
            if (isSmallScreen) 30.dp
            else 34.dp
        )
        Text(
            stringResource(R.string.chats_page_empty_chats_content),
            color = MaterialTheme.colorScheme.onPrimary,
            fontSize = 16.sp,
            fontWeight = FontWeight.W400,
        )
        DTVerticalSpacer(
            if (isSmallScreen) 66.dp
            else 100.dp
        )
        DTIconButton(
            stringResource(R.string.chats_page_random_match_btn_text),
            SelectableDrawable(R.drawable.image_random_match_btn_bg),
            Modifier
                .padding(horizontal = 38.dp)
                .fillMaxWidth()
                .height(50.dp),
            painterResource(R.drawable.ic_chats_page_random_match),
            onClick = onClickRandomMatch
        )
        DTVerticalSpacer(24.dp)
        DTIconButton(
            stringResource(R.string.chats_page_explore_btn_text),
            SelectableDrawable(R.drawable.image_explore_btn_bg),
            Modifier
                .padding(horizontal = 38.dp)
                .fillMaxWidth()
                .height(50.dp),
            painterResource(R.drawable.ic_chats_page_btn_explore),
            textStyle = DTButtonTextStyle.copy(
                brush = Brush.horizontalGradient(listOf(White, Color(0xFF8873FF)))
            ),
            onClick = onClickExplore
        )

        DTVerticalSpacer(
            if (isSmallScreen) 69.dp else 100.dp + LocalDTNavigationBarHeight.current
        )
    }
}