package com.deeptalkie.main.compose.ui.page.order

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.paging.Pager
import androidx.paging.PagingConfig
import androidx.paging.cachedIn
import com.deeptalkie.main.Membership
import com.deeptalkie.main.compose.utils.ILoadingState
import com.deeptalkie.main.compose.utils.createIntSourceFactory
import com.deeptalkie.main.compose.utils.loadingState
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update

/**
 *creater:lin<PERSON><PERSON> on 2025/6/25 15:59
 */
class OrderViewModel : ViewModel(), ILoadingState by loadingState() {
    private val account get() = Membership.membershipClient.account

    companion object {
        const val PAGE_SIZE = 20
    }

    // 状态
    private var isLoadedSuccess: Boolean = false
    private val _state = MutableStateFlow(OrderUIState())
    val state = _state.asStateFlow()

    private val orderPager = Pager(PagingConfig(PAGE_SIZE)) {
        createIntSourceFactory(fetchList = ::getOrder)
    }
    val orderFlow = orderPager.flow.cachedIn(viewModelScope)

    private suspend fun getOrder(curPage: Int) = account.getOrders(
        curPage = curPage,
        pageNum = PAGE_SIZE
    ).also {
        if (curPage == 1) {
            when {
                // 当前接口获取成功
                it.isSuccess -> {
                    isLoadedSuccess = true
                    _state.update { state ->
                        state.copy(
                            isShowErrorPage = false,
                            isLoadedSuccess = true
                        )
                    }
                }
                // 接口获取失败且未成功过, 显示错误页
                !isLoadedSuccess -> {
                    _state.update { state -> state.copy(isShowErrorPage = true) }
                }
            }
        }
    }
}

/**
 * 订单状态
 */
data class OrderUIState(
    val isShowErrorPage: Boolean = false,
    val isLoadedSuccess: Boolean = false,
)
