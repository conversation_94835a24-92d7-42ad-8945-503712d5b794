package com.deeptalkie.main.compose.ui.page.main.chats

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.deeptalkie.main.R
import com.deeptalkie.main.compose.theme.Black
import com.deeptalkie.main.compose.ui.components.DTHorizontalSpacer

sealed interface ChatDialogItemType {
    data object TopUp : ChatDialogItemType
    data object Del : ChatDialogItemType
}

@Composable
fun ChatDialog(
    name: String,
    isPinTop: Boolean,
    onDismiss: () -> Unit,
    onItemClick: (ChatDialogItemType) -> Unit,
) {
    Dialog(
        onDismissRequest = onDismiss,
        DialogProperties(usePlatformDefaultWidth = false)
    ) {
        Column(
            Modifier
                .width(220.dp)
                .clip(RoundedCornerShape(16.dp))
                .background(MaterialTheme.colorScheme.onPrimary)
        ) {
            Box(
                Modifier
                    .fillMaxWidth()
                    .height(42.dp),
                contentAlignment = Alignment.CenterStart
            ) {
                Image(
                    painter = painterResource(R.drawable.chat_dialog_title_bg),
                    null,
                    Modifier.fillMaxSize(),
                    contentScale = ContentScale.FillBounds
                )

                Text(
                    name,
                    Modifier
                        .padding(start = 26.dp, end = 16.dp)
                        .fillMaxWidth(),
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    style = MaterialTheme.typography.headlineMedium.copy(
                        color = Black,
                        lineHeight = 16.sp
                    ),
                )
            }
            ChatDialogItem(
                painterResource(if (isPinTop) R.drawable.ic_chats_cancel_top_up else R.drawable.ic_chat_top_up),
                stringResource(if (isPinTop) R.string.chats_page_chat_dialog_cancel_top_up else R.string.chats_page_chat_dialog_top_up)
            ) {
                onItemClick(ChatDialogItemType.TopUp)
            }
            ChatDialogItem(
                painterResource(R.drawable.ic_chats_del),
                stringResource(R.string.Delete)
            ) {
                onItemClick(ChatDialogItemType.Del)
            }
        }
    }
}

@Composable
fun ChatDialogItem(icon: Painter, title: String, onClick: () -> Unit) {
    Row(
        Modifier
            .fillMaxWidth()
            .height(56.dp)
            .clickable(onClick = onClick),
        verticalAlignment = Alignment.CenterVertically
    ) {
        DTHorizontalSpacer(26.dp)
        Icon(
            icon,
            title,
            Modifier.size(24.dp),
            tint = Black
        )
        DTHorizontalSpacer(10.dp)
        Text(
            title,
            color = Black,
            fontSize = 14.sp,
            fontWeight = FontWeight.W500,
        )
    }
}