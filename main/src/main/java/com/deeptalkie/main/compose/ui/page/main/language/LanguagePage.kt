package com.deeptalkie.main.compose.ui.page.main.language

import android.content.Intent
import androidx.activity.compose.LocalActivity
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.deeptalkie.main.R
import com.deeptalkie.main.activity.MainActivity
import com.deeptalkie.main.bean.UserManager
import com.deeptalkie.main.compose.theme.SocialBlue
import com.deeptalkie.main.compose.theme.White
import com.deeptalkie.main.compose.theme.White8
import com.deeptalkie.main.compose.ui.components.DTHorizontalSpacer
import com.deeptalkie.main.compose.ui.components.DTPage
import com.deeptalkie.main.compose.ui.components.SVTopBar


@Composable
fun LanguagePage(onBack: () -> Unit) {
    val activity = LocalActivity.current
    var localLanguage by remember { mutableStateOf(UserManager.getLanguage()) }

    val languages = AppSupportedLanguage.entries

    DTPage(background = R.drawable.bg_mine) {
        Column(
            Modifier
                .statusBarsPadding()
                .fillMaxSize()
        ) {
            SVTopBar(R.string.language, onBack)
            LazyColumn(
                Modifier
                    .fillMaxWidth()
                    .weight(1f),
            ) {
                itemsIndexed(languages, key = { _, it -> it.id }) { i, language ->
                    val checked = localLanguage == language
                    LanguageItem(language, checked, i < languages.lastIndex) {
                        if (checked) {
                            return@LanguageItem onBack()
                        }
                        localLanguage = language
                        UserManager.setLanguageId(language.id)
                        val intent = Intent(activity, MainActivity::class.java).apply {
                            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
                        }
                        activity?.startActivity(intent)
                    }
                }
            }
        }
    }
}

@Composable
private fun LanguageItem(
    language: AppSupportedLanguage,
    checked: Boolean,
    showDivider: Boolean,
    onClick: () -> Unit
) {
    Box(
        Modifier
            .fillMaxWidth()
            .height(52.dp)
            .clickable(onClick = onClick)
            .padding(horizontal = 24.dp)
    ) {
        Row(
            Modifier.fillMaxSize(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                language.title,
                Modifier.weight(1f),
                style = MaterialTheme.typography.headlineMedium.copy(
                    color = if (checked) SocialBlue else White,
                    fontWeight = if (checked) FontWeight.Medium else FontWeight.Normal,
                )
            )
            DTHorizontalSpacer(18.dp)
            if (checked) {
                Image(painterResource(R.drawable.ic_language_check), null, Modifier.size(24.dp))
            }
        }
        if (showDivider) {
            HorizontalDivider(
                Modifier
                    .fillMaxWidth()
                    .align(Alignment.BottomCenter), 0.5.dp, White8
            )
        }
    }
}