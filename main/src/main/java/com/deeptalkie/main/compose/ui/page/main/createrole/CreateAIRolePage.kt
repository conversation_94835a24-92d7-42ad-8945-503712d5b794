package com.deeptalkie.main.compose.ui.page.main.createrole

import android.net.Uri
import androidx.activity.compose.BackHandler
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.PickVisualMediaRequest
import androidx.activity.result.contract.ActivityResultContracts.PickVisualMedia
import androidx.annotation.StringRes
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.expandVertically
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.ime
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import coil3.compose.AsyncImage
import com.clevguard.utils.ext.logv
import com.deeptalkie.main.Membership
import com.deeptalkie.main.R
import com.deeptalkie.main.bean.UserManager
import com.deeptalkie.main.compose.navigation.MainRoute
import com.deeptalkie.main.compose.theme.Black20
import com.deeptalkie.main.compose.theme.Black40
import com.deeptalkie.main.compose.theme.White10
import com.deeptalkie.main.compose.theme.White6
import com.deeptalkie.main.compose.theme.White70
import com.deeptalkie.main.compose.ui.components.DTButton
import com.deeptalkie.main.compose.ui.components.DTButtonDefaults
import com.deeptalkie.main.compose.ui.components.DTHorizontalSpacer
import com.deeptalkie.main.compose.ui.components.DTIconButton
import com.deeptalkie.main.compose.ui.components.DTPage
import com.deeptalkie.main.compose.ui.components.DTPlaceholder
import com.deeptalkie.main.compose.ui.components.DTTextFieldColumnGroup
import com.deeptalkie.main.compose.ui.components.DTTextFieldRowGroup
import com.deeptalkie.main.compose.ui.components.DTVerticalSpacer
import com.deeptalkie.main.compose.ui.components.ThreeDotsLoading
import com.deeptalkie.main.compose.ui.dialog.BottomInputDialog
import com.deeptalkie.main.compose.ui.dialog.CreateRoleBottomSheet
import com.deeptalkie.main.compose.ui.dialog.DTWarnDialog
import com.deeptalkie.main.compose.ui.dialog.SelectTagBottomSheet
import com.deeptalkie.main.compose.ui.modifier.click
import com.deeptalkie.main.compose.ui.page.main.createrole.CreateAIRoleViewModel.VerifiedState
import com.deeptalkie.main.compose.utils.InfringingWordVisualTransformation
import com.deeptalkie.main.config.ReportEventUtils
import com.deeptalkie.main.config.UmConstant
import com.deeptalkie.main.db.table.AIRoleTag
import com.deeptalkie.main.db.table.Voice
import com.deeptalkie.main.ext.showToast
import com.deeptalkie.main.ext.toDp
import com.deeptalkie.main.s3.AwsS3
import com.deeptalkie.main.utils.getString
import com.deeptalkie.main.view.RequirePurchaseDialog
import com.eygraber.compose.placeholder.PlaceholderHighlight
import com.eygraber.compose.placeholder.material3.shimmer
import com.eygraber.compose.placeholder.placeholder
import kotlinx.coroutines.launch
import kotlinx.serialization.json.Json

@Composable
fun CreateAIRolePage(
    onBack: () -> Unit,
    navigate: (MainRoute) -> Unit,
    onCreateComplete: (Long) -> Unit,
    viewModel: CreateAIRoleViewModel
) {
    val context = LocalContext.current
    val focusManager = LocalFocusManager.current
    val keyboardController = LocalSoftwareKeyboardController.current
    val pickImageLauncher = rememberLauncherForActivityResult(PickVisualMedia()) { uri ->
        uri ?: return@rememberLauncherForActivityResult
        val ext = AwsS3.getUriExtension(uri)
        logv("选择图片类型:$ext")
        if (ext.equals("gif", ignoreCase = true)) {
            showToast(getString(R.string.unsupported_image_format))
            return@rememberLauncherForActivityResult
        }
        viewModel.onSelectedImage(uri)
    }
    val voiceState = viewModel.voiceStateFlow.collectAsStateWithLifecycle()
    val allTags by viewModel.allTagsFlow.collectAsStateWithLifecycle()
    val scope = rememberCoroutineScope()

    val backHandler = remember {
        {
            if (viewModel.pageContentChanged) {
                viewModel.showRemainDialog(true)
            } else {
                onBack()
            }
        }
    }

    BackHandler {
        backHandler()
    }

    DTPage(
        loading = viewModel.loading,
        onDismissLoading = onDismiss@{
            if (viewModel.creatingRole) {
                return@onDismiss
            }
            viewModel.showLoading(false)
        }
    ) {
        Column(
            Modifier
                .fillMaxSize()
                .background(Color(0xFF0D1116))
                .click {
                    focusManager.clearFocus()
                    keyboardController?.hide()
                }
                .statusBarsPadding()
        ) {
            CreateAIRoleTitleBar(R.string.create_ai_role_page_title, backHandler) {
                navigate(MainRoute.Product)
            }
            DTVerticalSpacer(8.dp)
            Column(
                Modifier
                    .weight(1f)
                    .fillMaxWidth()
                    .verticalScroll(rememberScrollState())
            ) {
                UploadImageCell(
                    viewModel.selectedImageUri,
                    viewModel.imageVerifiedState,
                    viewModel::onRemoveImage,
                    onUploadImage = {
                        ReportEventUtils.onEvent(
                            UmConstant.UPLOAD_IMAGE,
                            mapOf(UmConstant.UPLOAD_IMAGE to "Upload")
                        )
                        pickImageLauncher.launch(PickVisualMediaRequest(PickVisualMedia.ImageOnly))
                    },
                    onAiGeneration = {
                        navigate(MainRoute.AIGenerationImage(viewModel.route.aiRoleSex))
                    }
                )
                DTVerticalSpacer(14.dp)
                NameCell(viewModel.name, viewModel.nameInfringingWord, viewModel::onNameChange)
                DTVerticalSpacer(14.dp)
                DescriptionCell(
                    viewModel.description,
                    viewModel.aiDescReqFailState,
                    viewModel.descriptionInfringingWord,
                    viewModel.aiDescriptionLoading,
                    viewModel::onDescriptionChange,
                    onAiGeneration = block@{
                        if (!Membership.isVip()) {
                            RequirePurchaseDialog(
                                context, RequirePurchaseDialog.TYPE_CREATE_AI_ROLE
                            ) {
                                navigate(MainRoute.Product)
                            }.show()
                            return@block
                        }
                        viewModel.requestAIDescription()
                    },
                    onAiOptimize = block@{
                        if (!Membership.isVip()) {
                            RequirePurchaseDialog(
                                context, RequirePurchaseDialog.TYPE_CREATE_AI_ROLE
                            ) {
                                navigate(MainRoute.Product)
                            }.show()
                            return@block
                        }
                        viewModel.requestAIDescriptionOptimize()
                    },
                    onRetry = viewModel::onRetryGenDesc
                )
                DTVerticalSpacer(14.dp)
                VoiceCell(voiceState.value) {
                    navigate(
                        MainRoute.SelectVoice(
                            if (voiceState.value == null) null
                            else Json.encodeToString(voiceState.value)
                        )
                    )
                }
                DTVerticalSpacer(14.dp)
                AnimatedVisibility(
                    viewModel.showMoreSetting,
                    Modifier.fillMaxWidth(),
                    enter = fadeIn() + expandVertically(expandFrom = Alignment.Top),
                    exit = fadeOut() + shrinkVertically(shrinkTowards = Alignment.Top),
                ) {
                    Column(Modifier.fillMaxWidth()) {
                        PersonalityCell(
                            viewModel.selectedTags,
                            onShowTagSelectBottomSheet = {
                                viewModel.showSelectTagBottomSheet(true)
                            },
                            onUnselectTag = {
                                viewModel.onSelectTag(it, false)
                            }
                        )
                        DTVerticalSpacer(14.dp)
                        OpeningRemarksCell(
                            viewModel.openingRemarks,
                            viewModel.openingRemarksInfringingWord,
                            viewModel::onOpeningRemarksChange
                        )
                        DTVerticalSpacer(14.dp)
                        CharacterIntroductionCell(
                            viewModel.characterIntroduction,
                            viewModel.characterIntroductionInfringingWord,
                            viewModel::onCharacterIntroductionChange,
                        )
                        DTVerticalSpacer(35.dp)
                    }
                }
                if (!viewModel.showMoreSetting) {
                    MoreSetting(viewModel::showMoreSetting)
                }
            }
            Box(contentAlignment = Alignment.BottomCenter) {
                val imeHeight = WindowInsets.ime.getBottom(LocalDensity.current).toDp
                DTVerticalSpacer(imeHeight)
                CreateBtn(viewModel.canCreate()) {
                    ReportEventUtils.onEvent(
                        UmConstant.CREATE_CHARACTER,
                        mapOf(UmConstant.CREATE_CHARACTER to "Create_character_button")
                    )
                    if (!UserManager.canCreateAIRole()) {
                        RequirePurchaseDialog(context, RequirePurchaseDialog.TYPE_CREATE_AI_ROLE) {
                            navigate(MainRoute.Product)
                        }.show()
                        return@CreateBtn
                    }
                    scope.launch {
                        if (viewModel.checkContent()) {
                            viewModel.showCreateRoleBottomSheet(true)
                        }
                    }
                }
            }
        }
    }

    if (viewModel.showRemainDialog) {
        DTWarnDialog(
            R.string.reminder,
            R.string.create_ai_role_page_remain_dialog_content,
            R.string.cancel,
            R.string.exit,
            onCancel = {
                viewModel.showRemainDialog(false)
            },
            onConfirm = {
                viewModel.showRemainDialog(false)
                onBack()
            }
        )
    }
    if (viewModel.showSelectTagBottomSheet) {
        SelectTagBottomSheet(
            allTags,
            viewModel.selectedTags,
            onTagClick = viewModel::onSelectTag,
            onClickCreateTag = {
                viewModel.showCreateTagDialog(true)
            },
            onDismiss = {
                viewModel.showSelectTagBottomSheet(false)
            }
        )
    }
    if (viewModel.showCreateTagDialog) {
        BottomInputDialog(
            onDismiss = {
                viewModel.showCreateTagDialog(false)
            },
            onSubmit = {
                scope.launch {
                    if (viewModel.createTag(it)) {
                        viewModel.showCreateTagDialog(false)
                    }
                }
            }
        )
    }
    if (viewModel.showCreateRoleBottomSheet) {
        CreateRoleBottomSheet(
            onDismiss = {
                viewModel.showCreateRoleBottomSheet(false)
            },
            onCreate = { isPublic ->
                scope.launch {
                    ReportEventUtils.onEvent(
                        UmConstant.CREATE_CHARACTER,
                        mapOf(UmConstant.CREATE_CHARACTER to if (isPublic) "Public" else "Only oneself")
                    )
                    if (!Membership.isLogin()) return@launch navigate(MainRoute.Login)
                    viewModel.showCreateRoleBottomSheet(false)
                    val roleId = viewModel.createRole(isPublic)
                    if (roleId == null) return@launch
                    onCreateComplete(roleId)
                }
            }
        )
    }
}

@Composable
fun CreateAIRoleTitleBar(
    @StringRes titleId: Int,
    onBack: () -> Unit,
    onCart: () -> Unit
) {
    Row(
        Modifier
            .padding(horizontal = 16.dp)
            .fillMaxWidth()
            .height(56.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Image(
            painterResource(R.drawable.ic_create_ai_role_back),
            null,
            Modifier
                .size(36.dp)
                .clip(CircleShape)
                .clickable(onClick = onBack)
        )
        DTHorizontalSpacer(10.dp)
        Text(
            stringResource(titleId),
            modifier = Modifier.weight(1f),
            style = MaterialTheme.typography.headlineMedium.copy(
                color = MaterialTheme.colorScheme.onPrimary,
                lineHeight = 24.sp,
                textAlign = TextAlign.Center
            )
        )
        DTHorizontalSpacer(10.dp)
        Image(
            painterResource(R.drawable.ic_create_ai_role_cart),
            null,
            Modifier
                .size(36.dp)
                .clip(CircleShape)
                .clickable(onClick = onCart)
                .padding(6.dp)
        )
    }
}

@Composable
private fun UploadImageCell(
    selectedImage: Uri? = null,
    verifiedState: VerifiedState,
    onRemoveImage: () -> Unit,
    onUploadImage: () -> Unit,
    onAiGeneration: () -> Unit
) {
    CreateAIRoleCellTitle(
        R.string.create_ai_role_page_cell_character_image,
        required = true,
        verifiedState = verifiedState,
        canAiGeneration = false
    )
    DTVerticalSpacer(8.dp)
    UploadImageCard(selectedImage, onRemoveImage, onUploadImage, onAiGeneration)
}

@Composable
private fun UploadImageCard(
    selectedImage: Uri?,
    onRemoveImage: () -> Unit,
    onUploadImage: () -> Unit,
    onAiGeneration: () -> Unit
) {
    ConstraintLayout(
        Modifier
            .padding(horizontal = 16.dp)
            .fillMaxWidth()
            .height(110.dp)
            .background(White6, RoundedCornerShape(8.dp))
    ) {
        val (imageContainerRef, removeImageRef, optionsRef) = createRefs()
        Box(
            Modifier
                .size(90.dp)
                .clip(RoundedCornerShape(8.dp))
                .clickable(selectedImage == null, onClick = onUploadImage)
                .background(Black40)
                .constrainAs(imageContainerRef) {
                    centerVerticallyTo(parent)
                    start.linkTo(parent.start, 10.dp)
                },
            contentAlignment = Alignment.Center
        ) {
            if (selectedImage != null) {
                AsyncImage(
                    selectedImage,
                    null,
                    Modifier.fillMaxSize(),
                    contentScale = ContentScale.Crop
                )
            } else {
                Image(
                    painterResource(R.drawable.ic_upload_image),
                    null,
                    Modifier.size(24.dp)
                )
            }
        }
        if (selectedImage != null) {
            Image(
                painterResource(R.drawable.ic_ai_role_image_remove),
                null,
                Modifier
                    .size(17.dp)
                    .offset((-5).dp, 5.dp)
                    .constrainAs(removeImageRef) {
                        start.linkTo(imageContainerRef.end)
                        top.linkTo(imageContainerRef.top)
                        end.linkTo(imageContainerRef.end)
                        bottom.linkTo(imageContainerRef.top)
                    }
                    .clip(CircleShape)
                    .clickable(onClick = onRemoveImage)
            )
        }
        Column(Modifier.constrainAs(optionsRef) {
            width = Dimension.fillToConstraints
            height = Dimension.fillToConstraints
            start.linkTo(imageContainerRef.end, 12.dp)
            top.linkTo(parent.top, 14.dp)
            end.linkTo(parent.end, 10.dp)
            bottom.linkTo(parent.bottom, 14.dp)
        }) {
            Text(
                stringResource(R.string.create_ai_role_page_cell_character_image_tips),
                Modifier
                    .padding(horizontal = 4.dp)
                    .fillMaxWidth()
                    .weight(1f),
                style = MaterialTheme.typography.bodyMedium.copy(
                    color = MaterialTheme.colorScheme.onTertiary,
                    fontSize = 12.sp,
                )
            )
            Row(Modifier.fillMaxWidth()) {
                DTButton(
                    stringResource(R.string.create_ai_role_page_btn_upload),
                    modifier = Modifier
                        .weight(1f)
                        .height(30.dp),
                    background = MaterialTheme.colorScheme.primary,
                    onClick = onUploadImage,
                    textStyle = MaterialTheme.typography.headlineMedium.copy(
                        color = MaterialTheme.colorScheme.onPrimary,
                        fontSize = 12.sp
                    )
                )
                DTHorizontalSpacer(13.dp)
                DTButton(
                    stringResource(R.string.create_ai_role_page_btn_ai_generation),
                    modifier = Modifier
                        .weight(1f)
                        .height(30.dp),
                    background = MaterialTheme.colorScheme.primary,
                    onClick = onAiGeneration,
                    textStyle = MaterialTheme.typography.headlineMedium.copy(
                        color = MaterialTheme.colorScheme.onPrimary,
                        fontSize = 12.sp
                    )
                )
            }
        }
    }
}

@Composable
private fun NameCell(
    name: String,
    nameInfringingWords: List<String>,
    onNameChange: (String) -> Unit
) {
    CreateAIRoleCellTitle(
        R.string.create_ai_role_page_cell_name,
        required = true,
        verifiedState = when {
            name.length > 40 -> VerifiedState.VerifyFail
            name.isNotEmpty() -> VerifiedState.Verified
            else -> VerifiedState.NotVerified
        },
        canAiGeneration = false
    )
    DTVerticalSpacer(8.dp)
    DTTextFieldRowGroup(
        value = name,
        onValueChange = onNameChange,
        Modifier
            .padding(horizontal = 16.dp)
            .fillMaxWidth()
            .height(42.dp)
            .background(White6, RoundedCornerShape(8.dp))
            .padding(horizontal = 10.dp),
        textStyle = MaterialTheme.typography.bodyMedium.copy(
            color = MaterialTheme.colorScheme.onPrimary,
            fontSize = 12.sp
        ),
        singleLine = true,
        textPadding = PaddingValues(end = 16.dp),
        visualTransformation = remember(name, nameInfringingWords) {
            InfringingWordVisualTransformation(name, nameInfringingWords)
        },
        placeholder = {
            DTPlaceholder(
                stringResource(R.string.create_ai_role_page_cell_name_placeholder),
                maxLines = 1,
                style = MaterialTheme.typography.bodyMedium.copy(
                    color = MaterialTheme.colorScheme.onTertiary,
                    fontSize = 12.sp
                )
            )
        },
        endContent = {
            Text(
                buildAnnotatedString {
                    withStyle(
                        SpanStyle(
                            color = if (name.length > 40) Color(0xFFF74040)
                            else MaterialTheme.colorScheme.onTertiary,
                        )
                    ) {
                        append("${name.length}")
                    }
                    append("/40")
                },
                style = MaterialTheme.typography.labelMedium.copy(
                    color = MaterialTheme.colorScheme.onTertiary,
                    fontSize = 11.sp,
                )
            )
        }
    )
}

@Composable
private fun DescriptionCell(
    description: String,
    aiDescReqFailState: AIDescReqFailState,
    descriptionInfringingWords: List<String>,
    aiDescriptionLoading: Boolean,
    onDescriptionChange: (String) -> Unit,
    onAiGeneration: () -> Unit,
    onAiOptimize: () -> Unit,
    onRetry: () -> Unit
) {
    CreateAIRoleCellTitle(
        R.string.create_ai_role_page_cell_desc,
        required = true,
        verifiedState = if (description.isNotEmpty()) VerifiedState.Verified else VerifiedState.NotVerified,
        canAiGeneration = true,
        aiGenText = R.string.create_ai_role_page_btn_ai_inspiration,
        onAiGenerationClick = onAiGeneration
    )
    DTVerticalSpacer(8.dp)
    Column(
        Modifier
            .padding(horizontal = 16.dp)
            .fillMaxWidth()
            .background(White6, RoundedCornerShape(8.dp))
            .padding(bottom = 10.dp)
    ) {
        if (aiDescReqFailState != AIDescReqFailState.NotFail) {
            AIDescFailedContent(onRetry)
        } else {
            if (aiDescriptionLoading) {
                DescPlaceholder()
            } else {
                DTTextFieldRowGroup(
                    value = description,
                    onValueChange = onDescriptionChange,
                    Modifier
                        .fillMaxWidth()
                        .heightIn(117.dp, 234.dp),
                    textStyle = MaterialTheme.typography.bodyMedium.copy(
                        color = MaterialTheme.colorScheme.onPrimary,
                        fontSize = 12.sp
                    ),
                    visualTransformation = remember(description, descriptionInfringingWords) {
                        InfringingWordVisualTransformation(description, descriptionInfringingWords)
                    },
                    verticalAlignment = Alignment.Top,
                    textPadding = PaddingValues(horizontal = 10.dp, vertical = 12.dp),
                    placeholder = {
                        DTPlaceholder(
                            stringResource(R.string.create_ai_role_page_cell_desc_placeholder),
                            maxLines = Int.MAX_VALUE,
                            style = MaterialTheme.typography.bodyMedium.copy(
                                color = MaterialTheme.colorScheme.onTertiary,
                                fontSize = 12.sp
                            )
                        )
                    }
                )
            }
            DTIconButton(
                onClick = onAiOptimize,
                modifier = Modifier
                    .padding(start = 10.dp)
                    .height(23.dp),
                enabled = description.isNotEmpty(),
                colors = DTButtonDefaults.buttonColors(
                    containerColor = Black20,
                    disabledContainerColor = Color(0xFF4E4E4E),
                )
            ) {
                Icon(
                    painterResource(R.drawable.ic_optimize),
                    null,
                    Modifier.size(14.dp),
                    tint = if (description.isNotEmpty()) Color.Unspecified else Color(0xFF9E9E9E)
                )
                DTHorizontalSpacer(5.dp)
                Text(
                    stringResource(R.string.create_ai_role_page_cell_desc_optimize),
                    style = MaterialTheme.typography.bodyMedium.copy(
                        brush = Brush.horizontalGradient(
                            if (description.isNotEmpty())
                                listOf(Color(0xFF995BFF), Color(0xFF5258F6))
                            else listOf(Color(0xFF9E9E9E), Color(0xFF9E9E9E))
                        ),
                        fontSize = 10.sp,
                        lineHeight = 14.sp
                    )
                )
            }
        }
    }
}

@Composable
private fun DescPlaceholder() {
    Column(
        Modifier
            .padding(top = 12.dp)
            .fillMaxWidth()
            .height(105.dp),
        verticalArrangement = Arrangement.spacedBy(5.dp)
    ) {
        repeat(2) {
            Box(
                Modifier
                    .padding(horizontal = 10.dp)
                    .fillMaxWidth()
                    .height(20.dp)
                    .placeholder(
                        true,
                        color = Color(0xFF3A3A3E),
                        shape = RoundedCornerShape(4.dp),
                        highlight = PlaceholderHighlight.shimmer()
                    )
            )
        }
        Box(
            Modifier
                .padding(start = 10.dp)
                .width(194.dp)
                .height(20.dp)
                .placeholder(
                    true,
                    color = Color(0xFF3A3A3E),
                    shape = RoundedCornerShape(4.dp),
                    highlight = PlaceholderHighlight.shimmer()
                )
        )
    }
}

@Composable
private fun VoiceCell(voice: Voice?, onSelectVoice: () -> Unit) {
    CreateAIRoleCellTitle(
        R.string.create_ai_role_page_cell_voice,
        required = true,
        verifiedState = if (voice != null) VerifiedState.Verified else VerifiedState.NotVerified,
        canAiGeneration = false
    )
    DTVerticalSpacer(8.dp)
    Row(
        Modifier
            .padding(horizontal = 16.dp)
            .fillMaxWidth()
            .height(42.dp)
            .clip(RoundedCornerShape(8.dp))
            .clickable(onClick = onSelectVoice)
            .background(White6),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            voice?.showName ?: stringResource(R.string.create_ai_role_page_cell_voice),
            Modifier
                .padding(horizontal = 10.dp)
                .weight(1f),
            style = MaterialTheme.typography.bodyMedium.copy(
                color = if (voice == null) MaterialTheme.colorScheme.onTertiary
                else MaterialTheme.colorScheme.onPrimary,
                fontSize = 12.sp
            )
        )
        Icon(
            painterResource(R.drawable.ic_config_voice),
            null,
            Modifier.size(22.dp),
            White70
        )
        DTHorizontalSpacer(12.dp)
    }
}

@Composable
private fun PersonalityCell(
    selectedTags: List<AIRoleTag>,
    onShowTagSelectBottomSheet: () -> Unit,
    onUnselectTag: (AIRoleTag) -> Unit
) {
    CreateAIRoleCellTitle(
        R.string.create_ai_role_page_cell_personality,
        required = false,
        verifiedState = if (selectedTags.isNotEmpty()) VerifiedState.Verified else VerifiedState.NotVerified,
        canAiGeneration = false
    )
    DTVerticalSpacer(8.dp)
    Row(
        Modifier
            .padding(horizontal = 16.dp)
            .fillMaxWidth()
            .height(42.dp)
            .clip(RoundedCornerShape(8.dp))
            .background(White6),
        verticalAlignment = Alignment.CenterVertically
    ) {
        DTHorizontalSpacer(10.dp)
        if (selectedTags.isEmpty()) {
            Text(
                stringResource(R.string.create_ai_role_page_cell_personality_placeholder),
                Modifier.weight(1f),
                style = MaterialTheme.typography.bodyMedium.copy(
                    color = MaterialTheme.colorScheme.onTertiary,
                    fontSize = 12.sp
                )
            )
        } else {
            LazyRow(Modifier.weight(1f), horizontalArrangement = Arrangement.spacedBy(8.dp)) {
                items(selectedTags, key = { it.id }) { tag ->
                    Row(
                        modifier = Modifier
                            .height(27.dp)
                            .background(White10, RoundedCornerShape(50))
                            .padding(horizontal = 10.dp),
                        verticalAlignment = Alignment.CenterVertically,
                    ) {
                        Text(
                            tag.name,
                            style = MaterialTheme.typography.labelMedium.copy(
                                color = MaterialTheme.colorScheme.onPrimary,
                                fontWeight = FontWeight.Medium,
                            )
                        )
                        DTHorizontalSpacer(5.dp)
                        Icon(
                            painterResource(R.drawable.ic_remove_ai_role_tag),
                            null,
                            Modifier
                                .size(17.dp)
                                .clip(CircleShape)
                                .clickable {
                                    onUnselectTag(tag)
                                },
                            Color.Unspecified
                        )
                    }
                }
            }
        }
        DTHorizontalSpacer(5.dp)
        Icon(
            painterResource(R.drawable.ic_add_ai_role_tag),
            null,
            Modifier
                .size(34.dp)
                .clip(CircleShape)
                .clickable(onClick = onShowTagSelectBottomSheet)
                .padding(5.dp),
            White70
        )
        DTHorizontalSpacer(7.dp)
    }
}

@Composable
private fun OpeningRemarksCell(
    remarks: String,
    remarksInfringingWords: List<String>,
    onRemarksChange: (String) -> Unit
) {
    CreateAIRoleCellTitle(
        R.string.create_ai_role_page_cell_opening_remarks,
        required = false,
        verifiedState = when {
            remarks.length > 600 -> VerifiedState.VerifyFail
            remarks.isEmpty() -> VerifiedState.NotVerified
            else -> VerifiedState.Verified
        },
        canAiGeneration = false
    )
    DTVerticalSpacer(8.dp)
    DTTextFieldColumnGroup(
        value = remarks,
        onValueChange = onRemarksChange,
        Modifier
            .padding(horizontal = 16.dp)
            .background(White6, RoundedCornerShape(8.dp)),
        textGroupModifier = Modifier
            .fillMaxWidth()
            .heightIn(80.dp, 160.dp)
            .padding(horizontal = 10.dp, vertical = 12.dp),
        textStyle = MaterialTheme.typography.bodyMedium.copy(
            color = MaterialTheme.colorScheme.onPrimary,
            fontSize = 12.sp
        ),
        visualTransformation = remember(remarks, remarksInfringingWords) {
            InfringingWordVisualTransformation(remarks, remarksInfringingWords)
        },
        placeholder = {
            DTPlaceholder(
                stringResource(R.string.create_ai_role_page_cell_opening_remarks_placeholder),
                maxLines = Int.MAX_VALUE,
                style = MaterialTheme.typography.bodyMedium.copy(
                    color = MaterialTheme.colorScheme.onTertiary,
                    fontSize = 12.sp
                )
            )
        },
        bottomContent = {
            Text(
                buildAnnotatedString {
                    withStyle(
                        SpanStyle(
                            color = if (remarks.length > 600) Color(0xFFF74040)
                            else MaterialTheme.colorScheme.onTertiary,
                        )
                    ) {
                        append("${remarks.length}")
                    }
                    append("/600")
                },
                modifier = Modifier
                    .padding(end = 10.dp, bottom = 12.dp)
                    .align(Alignment.End),
                style = MaterialTheme.typography.labelMedium.copy(
                    color = MaterialTheme.colorScheme.onTertiary,
                    fontSize = 11.sp,
                )
            )
        },
    )
}

@Composable
private fun CharacterIntroductionCell(
    introduction: String,
    introductionInfringingWords: List<String>,
    onIntroductionChange: (String) -> Unit
) {
    CreateAIRoleCellTitle(
        R.string.create_ai_role_page_cell_character_introduction,
        required = false,
        verifiedState = when {
            introduction.length > 600 -> VerifiedState.VerifyFail
            introduction.isEmpty() -> VerifiedState.NotVerified
            else -> VerifiedState.Verified
        },
        canAiGeneration = false
    )
    DTVerticalSpacer(8.dp)
    DTTextFieldColumnGroup(
        value = introduction,
        onValueChange = onIntroductionChange,
        Modifier
            .padding(horizontal = 16.dp)
            .background(White6, RoundedCornerShape(8.dp)),
        textGroupModifier = Modifier
            .fillMaxWidth()
            .heightIn(80.dp, 160.dp)
            .padding(horizontal = 10.dp, vertical = 12.dp),
        textStyle = MaterialTheme.typography.bodyMedium.copy(
            color = MaterialTheme.colorScheme.onPrimary,
            fontSize = 12.sp
        ),
        visualTransformation = remember(introduction, introductionInfringingWords) {
            InfringingWordVisualTransformation(introduction, introductionInfringingWords)
        },
        placeholder = {
            DTPlaceholder(
                stringResource(R.string.create_ai_role_page_cell_character_introduction_placeholder),
                maxLines = Int.MAX_VALUE,
                style = MaterialTheme.typography.bodyMedium.copy(
                    color = MaterialTheme.colorScheme.onTertiary,
                    fontSize = 12.sp
                )
            )
        },
        bottomContent = {
            Text(
                buildAnnotatedString {
                    withStyle(
                        SpanStyle(
                            color = if (introduction.length > 600) Color(0xFFF74040)
                            else MaterialTheme.colorScheme.onTertiary,
                        )
                    ) {
                        append("${introduction.length}")
                    }
                    append("/600")
                },
                modifier = Modifier
                    .padding(end = 10.dp, bottom = 12.dp)
                    .align(Alignment.End),
                style = MaterialTheme.typography.labelMedium.copy(
                    color = MaterialTheme.colorScheme.onTertiary,
                    fontSize = 11.sp,
                )
            )
        },
    )
}

@Composable
private fun ColumnScope.MoreSetting(onClick: () -> Unit) {
    Text(
        stringResource(R.string.create_ai_role_page_btn_more_setting),
        modifier = Modifier
            .align(Alignment.CenterHorizontally)
            .click(onClick),
        style = MaterialTheme.typography.bodyMedium.copy(
            color = White70,
            fontSize = 14.sp
        )
    )
}

@Composable
private fun CreateBtn(canCreate: Boolean, onClick: () -> Unit) {
    Column(Modifier.navigationBarsPadding()) {
        DTVerticalSpacer(14.dp)
        DTButton(
            R.string.create_ai_role_page_btn_create,
            modifier = Modifier
                .padding(horizontal = 16.dp)
                .fillMaxWidth()
                .height(46.dp),
            enable = canCreate,
            containerColor = MaterialTheme.colorScheme.primary,
            disabledContainerColor = Color(0xFF636364),
            onClick = onClick
        )
        DTVerticalSpacer(14.dp)
    }
}

@Composable
fun CreateAIRoleCellTitle(
    @StringRes textId: Int,
    required: Boolean,
    verifiedState: VerifiedState,
    canAiGeneration: Boolean,
    @StringRes aiGenText: Int = R.string.create_ai_role_page_btn_ai_generation,
    onAiGenerationClick: () -> Unit = {}
) {
    Row(
        Modifier
            .padding(horizontal = 16.dp)
            .fillMaxWidth()
            .heightIn(18.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        CellText(textId, required, verifiedState)
        when (verifiedState) {
            VerifiedState.NotVerified -> {}
            VerifiedState.Verified -> {
                DTHorizontalSpacer(6.dp)
                Image(
                    painterResource(R.drawable.ic_create_ai_role_verified),
                    null,
                    Modifier.size(18.dp)
                )
            }

            VerifiedState.VerifyFail -> {
                DTHorizontalSpacer(6.dp)
                Image(
                    painterResource(R.drawable.ic_create_ai_role_verified_fail),
                    null,
                    Modifier.size(18.dp)
                )
            }

            VerifiedState.Verifying -> {
                ThreeDotsLoading(Modifier.size(18.dp))
            }
        }
        Spacer(Modifier.weight(1f))
        if (canAiGeneration) {
            DTButton(
                stringResource(aiGenText),
                modifier = Modifier
                    .width(104.dp)
                    .height(30.dp),
                background = MaterialTheme.colorScheme.primary,
                onClick = onAiGenerationClick,
                textStyle = MaterialTheme.typography.headlineMedium.copy(
                    color = MaterialTheme.colorScheme.onPrimary,
                    fontSize = 12.sp
                )
            )
        }
    }
}

@Composable
private fun CellText(
    @StringRes textId: Int,
    required: Boolean,
    verifiedState: VerifiedState,
    modifier: Modifier = Modifier
) {
    Text(
        "${if (required) "* " else ""}${stringResource(textId)}",
        modifier,
        style = MaterialTheme.typography.bodyMedium.copy(
            color = if (verifiedState is VerifiedState.VerifyFail) Color(0xFFF74040)
            else MaterialTheme.colorScheme.onPrimary,
            fontSize = 14.sp
        )
    )
}