package com.deeptalkie.main.compose.ui.page.main.createrole

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.toRoute
import com.clevguard.utils.ext.logv
import com.deeptalkie.main.bean.UserManager
import com.deeptalkie.main.compose.navigation.MainRoute
import com.deeptalkie.main.config.ReportEventUtils
import com.deeptalkie.main.config.UmConstant
import com.deeptalkie.main.config.loadImages
import com.deeptalkie.main.ext.showToast
import com.deeptalkie.main.ext.stateInViewModelDefault
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlin.math.min

class AIImageResultViewModel(savedStateHandle: SavedStateHandle) : ViewModel() {
    val route = savedStateHandle.toRoute<MainRoute.AIGenerationImageResult>()
    private val createAIRoleRepo = CreateAIRoleRepo()

    var prompt by mutableStateOf(route.prompt)
        private set

    var imageResult: ImageResult by mutableStateOf(ImageResult.Loading(0))
        private set

    private var imageResultId: Long? = null

    var showEditPromptDialog by mutableStateOf(false)
        private set

    var selectedImageIndex by mutableIntStateOf(0)
        private set

    var showFailDialog by mutableStateOf(false)
        private set

    var showReminderDialog by mutableStateOf(false)
        private set

    var showCoinNotEnoughDialog by mutableStateOf(false)
        private set

    private val coins = UserManager.userCoinsFlow.stateInViewModelDefault(0)

    init {
        generateAIImage()
    }

    private fun generateAIImage() {
        viewModelScope.launch {
            plusLoadingProgress(2)
            ReportEventUtils.onEvent(
                UmConstant.IMAGE_STYLE,
                mapOf(UmConstant.IMAGE_STYLE to route.stylePrompt)
            )
            val id = createAIRoleRepo.generateAIImage(
                prompt, route.aiRoleSex, route.stylePrompt
            )
            if (id == null) {
                imageResult = ImageResult.Fail
                showFailDialog(true)
                return@launch
            }
            imageResultId = id
            logv("生成图片记录id：$id")
            plusLoadingProgress(10)
            queryImageResult()
        }
    }

    private fun plusLoadingProgress(progress: Int) {
        if (imageResult !is ImageResult.Loading) {
            imageResult = ImageResult.Loading(0)
        } else {
            val loading = imageResult as ImageResult.Loading
            imageResult = ImageResult.Loading(min(loading.progress + progress, 99))
        }
    }

    fun queryImageResult() {
        viewModelScope.launch {
            plusLoadingProgress(2)
            logv("开始查询图片结果")
            while (imageResult is ImageResult.Loading) {
                delay(1000)
                logv("查询图片结果中, progress=${(imageResult as ImageResult.Loading).progress}")
                val result = createAIRoleRepo.queryImageResult(imageResultId!!)
                logv("查询图片结果：$result")
                when {
                    result == null -> {
                        imageResult = ImageResult.Fail
                        showFailDialog(true)
                    }

                    result.status == 2 -> {
                        val images = result.result.orEmpty()
                        selectedImageIndex = 0
                        // 先加载出来再显示结果
                        images.loadImages()
                        ReportEventUtils.onEvent(
                            UmConstant.CREATE_IMAGE,
                            mapOf(UmConstant.CREATE_IMAGE to "Generation_succeed")
                        )
                        ReportEventUtils.onEvent(
                            UmConstant.IMAGE_PROMPTS,
                            mapOf(UmConstant.IMAGE_PROMPTS to "View_image_page")
                        )
                        imageResult = ImageResult.Success(images)
                    }

                    result.status == 3 -> {
                        if (result.reason.isNotEmpty()) {
                            showToast(result.reason)
                        }
                        imageResult = ImageResult.Fail
                        showFailDialog(true)
                    }

                    else -> {
                        plusLoadingProgress(2)
                    }
                }
            }
        }
    }

    fun showFailDialog(show: Boolean) {
        if (show) {
            ReportEventUtils.onEvent(
                UmConstant.CREATE_IMAGE,
                mapOf(UmConstant.CREATE_IMAGE to "Image_generation_failed")
            )
        }
        showFailDialog = show
    }

    fun onRetry() {
        showFailDialog(false)
        generateAIImage()
    }

    fun showReminderDialog(show: Boolean) {
        showReminderDialog = show
    }

    fun showEditPromptDialog(show: Boolean) {
        if (show) {
            ReportEventUtils.onEvent(
                UmConstant.IMAGE_PROMPTS,
                mapOf(UmConstant.IMAGE_PROMPTS to "Modify the prompts")
            )
        }
        showEditPromptDialog = show
    }

    fun showCoinNotEnoughDialog(show: Boolean) {
        showCoinNotEnoughDialog = show
    }

    fun onEditPrompt(newPrompt: String) {
        ReportEventUtils.onEvent(
            UmConstant.IMAGE_PROMPTS,
            mapOf(UmConstant.IMAGE_PROMPTS to "Regenerate")
        )
        if (coins.value < 10) {
            showCoinNotEnoughDialog(true)
            return
        }
        prompt = newPrompt
        showEditPromptDialog(false)
        generateAIImage()
    }

    fun onImageSelected(index: Int) {
        selectedImageIndex = index
    }
}

sealed interface ImageResult {
    data class Success(val images: List<String>) : ImageResult
    data class Loading(val progress: Int) : ImageResult
    data object Fail : ImageResult
}