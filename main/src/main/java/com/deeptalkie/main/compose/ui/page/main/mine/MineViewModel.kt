package com.deeptalkie.main.compose.ui.page.main.mine

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.deeptalkie.main.Membership
import com.deeptalkie.main.bean.UserManager
import com.deeptalkie.main.ext.stateInViewModel
import com.deeptalkie.main.ext.stateInViewModelDefault
import com.deeptalkie.main.repo.UserCoinRepo
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch

/**
 *creater:linjinhao on 2025/5/14 14:37
 */
class MineViewModel : ViewModel() {
    private val userCoinRepo = UserCoinRepo()
    private var refreshJob: Job? = null
    private var refreshAccountJob: Job? = null
    private val account = Membership.membershipClient.account
    val userFlow = Membership.userStateFlow
    val memberFlow = Membership.memberStateFlow
    var coins by mutableIntStateOf(0)
    val vipStateFlow = UserManager.vipState.stateInViewModelDefault(false)
    val vipFailureTimeTextFlow= UserManager.vipFailureTimeTextFlow.stateInViewModel()

    init {
        collectCoins()
    }


    fun refreshInfo() {
        refreshUserInfo()
        refreshAccountInfo()
    }

    private fun collectCoins() {
        viewModelScope.launch {
            UserManager.userCoinsFlow.collect {
                coins = it
            }
        }
    }

    private fun refreshUserInfo() {
        refreshJob?.cancel()
        refreshJob = viewModelScope.launch {
            account.refreshMemberInfo()
        }
    }

    private fun refreshAccountInfo() {
        refreshAccountJob?.cancel()
        refreshAccountJob = viewModelScope.launch {
            userCoinRepo.refreshUserCoin()
        }
    }
}