package com.deeptalkie.main.compose.ui.dialog

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.deeptalkie.main.R
import com.deeptalkie.main.compose.theme.White6
import com.deeptalkie.main.compose.theme.White90
import com.deeptalkie.main.compose.ui.components.DTVerticalSpacer
import com.deeptalkie.main.utils.getString

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CharacterImageBottomSheet(
    onDismiss: () -> Unit,
    onQuestionClick: () -> Unit,
    onSelectionClick: (String) -> Unit
) {
    DTBottomSheet(onDismiss, containerColor = Color(0xFF28282D)) {
        Column(Modifier.navigationBarsPadding()) {
            DTVerticalSpacer(6.dp)
            Title(onQuestionClick)
            DTVerticalSpacer(4.dp)
            CharacterImageSubtitle()
            DTVerticalSpacer(12.dp)
            CharacterImageSelection(stringResource(R.string.chat_page_generate_image_dialog_selection_1)) {
                onSelectionClick(getString(R.string.chat_page_generate_image_dialog_selection_1_value))
            }
            DTVerticalSpacer(8.dp)
            CharacterImageSelection(stringResource(R.string.chat_page_generate_image_dialog_selection_2)) {
                onSelectionClick(getString(R.string.chat_page_generate_image_dialog_selection_2_value))
            }
            DTVerticalSpacer(8.dp)
            CharacterImageSelection(stringResource(R.string.chat_page_generate_image_dialog_selection_3)) {
                onSelectionClick(getString(R.string.chat_page_generate_image_dialog_selection_3_value))
            }
            DTVerticalSpacer(8.dp)
            CharacterImageSelection(stringResource(R.string.chat_page_generate_image_dialog_selection_4)) {
                onSelectionClick("")
            }
            DTVerticalSpacer(20.dp)
        }
    }
}

@Composable
private fun Title(
    onQuestionClick: () -> Unit
) {
    Box(Modifier.fillMaxWidth()) {
        Text(
            stringResource(R.string.chat_page_generate_image_dialog_title),
            Modifier
                .padding(horizontal = 52.dp)
                .fillMaxWidth()
                .align(Alignment.Center),
            style = MaterialTheme.typography.headlineMedium.copy(
                color = MaterialTheme.colorScheme.primary,
                fontSize = 16.sp,
                lineHeight = 20.sp,
                textAlign = TextAlign.Center
            )
        )
        Icon(
            painterResource(R.drawable.ic_question_circle),
            null,
            Modifier
                .padding(end = 20.dp)
                .size(20.dp)
                .clip(CircleShape)
                .clickable(onClick = onQuestionClick)
                .align(Alignment.CenterEnd),
            tint = Color.Unspecified
        )
    }
}

@Composable
private fun CharacterImageSubtitle() {
    Text(
        stringResource(R.string.chat_page_generate_image_dialog_subtitle),
        Modifier
            .padding(horizontal = 20.dp)
            .fillMaxWidth(),
        style = MaterialTheme.typography.labelMedium.copy(
            color = MaterialTheme.colorScheme.onTertiary,
            lineHeight = 18.sp,
            textAlign = TextAlign.Center
        )
    )
}

@Composable
private fun CharacterImageSelection(text: String, onClick: () -> Unit) {
    Text(
        text,
        Modifier
            .padding(horizontal = 16.dp)
            .fillMaxWidth()
            .clip(RoundedCornerShape(10.dp))
            .background(White6)
            .clickable(onClick = onClick)
            .padding(10.dp, 13.dp),
        style = MaterialTheme.typography.labelMedium.copy(
            color = White90,
            lineHeight = 18.sp,
        )
    )
}