package com.deeptalkie.main.compose.ui.page.assets

import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.clevguard.utils.ext.logd
import com.clevguard.utils.ext.loge
import com.deeptalkie.main.api.deepTalkieApiFastJson
import com.deeptalkie.main.bean.MyAssetsBean
import com.deeptalkie.main.bean.MyFavoriteBean
import com.deeptalkie.main.compose.utils.ILoadingState
import com.deeptalkie.main.compose.utils.loadingState
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

/**
 *creater:linjinhao on 2025/6/19 10:55
 */
class MineAssetsViewModel : ViewModel(), ILoadingState by loadingState() {
    companion object {
        const val MODULE_ASSETS = "assets"
        const val MODULE_FAVORITE = "favorite"
        const val PAGE_SIZE = 8
    }

    //管理 assets 和 favorite的分页
    private val pagingState = mutableMapOf<String, MutableState<PagingState>>()
    private val _myAssetsStateFlow = MutableStateFlow<List<MyAssetsBean>>(emptyList())
    val myAssetsStateFlow = _myAssetsStateFlow.asStateFlow()
    private val _myFavoriteStateFlow = MutableStateFlow<List<MyFavoriteBean>>(emptyList())
    val myFavoriteStateFlow = _myFavoriteStateFlow.asStateFlow()

    fun hasMore(tag: String): Boolean {
        val value = getPagingState(tag).value.hasMore
        loge("tag:$tag,more:${value}")
        return value
    }

    fun isRefreshing(tag: String): Boolean {
        return getPagingState(tag).value.isRefreshing
    }

    suspend fun onRefresh(tag: String) {
        if (tag == MODULE_ASSETS) {
            refreshMyAssets()
        } else {
            refreshFavorite()
        }
    }

    fun onLoadMore(tag: String) {
        if (tag == MODULE_ASSETS) {
            loadMoreAssets()
        } else {
            loadMoreFavorite()
        }
    }

    fun initData(tag: String) {
        viewModelScope.launch {
            val updatePagingState = updatePagingState(tag) {
                it.value = it.value.copy(page = 1, hasMore = true, isLoadMore = false)
            }
            showLoading(true)
            if (tag == MODULE_ASSETS) {
                val response =
                    getMyAssets(updatePagingState.value.page, updatePagingState.value.pageSize)
                val data = response.data
                if (response.isSuccess && data != null) {
                    _myAssetsStateFlow.update { currentAssets ->
                        val currentMap =
                            currentAssets.orEmpty().associateBy { it.id }.toMutableMap()
                        // 用新数据更新或添加资产
                        data.list.forEach { newAsset ->
                            currentMap[newAsset.id] = newAsset // 更新或添加
                        }

                        // 返回合并后的列表
                        currentMap.values.toList()
                    }
                } else {
                    onError(response.code, response.msg)
                }

            } else {
                val response =
                    getFavorites(updatePagingState.value.page, updatePagingState.value.pageSize)
                val data = response.data
                if (response.isSuccess && data != null) {
                    _myFavoriteStateFlow.update { list ->
                        val currentMap = list.orEmpty().associateBy { it.id }.toMutableMap()
                        data.list.forEach { new ->
                            currentMap[new.id] = new
                        }
                        // 返回合并后的列表
                        currentMap.values.toList()
                    }
                } else {
                    onError(response.code, response.msg)
                }
            }
            showLoading(false)
        }
    }

    suspend fun refreshMyAssets() {
        val updatePagingState = updatePagingState(MODULE_ASSETS) {
            it.value =
                it.value.copy(page = 1, hasMore = true, isLoadMore = false, isRefreshing = true)
        }
        val response = getMyAssets(updatePagingState.value.page, updatePagingState.value.pageSize)
        val data = response.data
        if (response.isSuccess && data != null) {
            _myAssetsStateFlow.update { currentAssets ->
                val currentMap = currentAssets.orEmpty().associateBy { it.id }.toMutableMap()
                // 用新数据更新或添加资产
                data.list.forEach { newAsset ->
                    currentMap[newAsset.id] = newAsset // 更新或添加
                }

                // 返回合并后的列表
                currentMap.values.toList()
            }
        } else {
            onError(response.code, response.msg)
        }
    }

    suspend fun refreshFavorite() {
        val updatePagingState = updatePagingState(MODULE_FAVORITE) {
            it.value =
                it.value.copy(page = 1, hasMore = true, isLoadMore = false, isRefreshing = true)
        }
        val response = getFavorites(updatePagingState.value.page, updatePagingState.value.pageSize)
        val data = response.data
        if (response.isSuccess && data != null) {
            _myFavoriteStateFlow.update { list ->
                val currentMap = list.orEmpty().associateBy { it.id }.toMutableMap()
                data.list.forEach { new ->
                    currentMap[new.id] = new
                }
                // 返回合并后的列表
                currentMap.values.toList()
            }
        } else {
            onError(response.code, response.msg)
        }
    }


    fun loadMoreAssets() {
        viewModelScope.launch {
            val pagingState = getPagingState(MODULE_ASSETS)
            if (pagingState.value.isLoadMore || pagingState.value.isRefreshing) return@launch
            val updatePagingState = updatePagingState(MODULE_ASSETS) {
                it.value = it.value.copy(isLoadMore = true)
            }
            val response =
                getMyAssets(updatePagingState.value.page, updatePagingState.value.pageSize)
            val data = response.data
            if (response.isSuccess && data != null) {
                updatePagingState(MODULE_ASSETS) {
                    it.value = it.value.copy(
                        page = it.value.page + 1,
                        isLoadMore = false,
                        hasMore = data.list.size >= updatePagingState.value.pageSize
                    )
                }
                _myAssetsStateFlow.update { currentAssets ->
                    val currentMap = currentAssets.orEmpty().associateBy { it.id }.toMutableMap()
                    // 用新数据更新或添加资产
                    data.list.forEach { newAsset ->
                        currentMap[newAsset.id] = newAsset // 更新或添加
                    }

                    // 返回合并后的列表
                    currentMap.values.toList()
                }
            } else {
                updatePagingState(MODULE_ASSETS) {
                    it.value = it.value.copy(
                        isLoadMore = false
                    )
                }
                onError(response.code, response.msg)
            }
        }
    }

    fun loadMoreFavorite() {
        viewModelScope.launch {
            val pagingState = getPagingState(MODULE_FAVORITE)
            if (pagingState.value.isLoadMore) return@launch

            val updatePagingState = updatePagingState(MODULE_FAVORITE) {
                it.value = it.value.copy(isLoadMore = true)
            }
            val response =
                getFavorites(updatePagingState.value.page + 1, updatePagingState.value.pageSize)

            val data = response.data
            if (response.isSuccess && data != null) {
                updatePagingState(MODULE_FAVORITE) {
                    it.value = it.value.copy(
                        page = it.value.page + 1,
                        isLoadMore = false,
                        hasMore = data.list.size >= updatePagingState.value.pageSize
                    )
                }
                loge("pagestate success:${getPagingState(MODULE_FAVORITE)}")
                _myFavoriteStateFlow.update { list ->
                    val currentMap = list.associateBy { it.id }.toMutableMap()
                    data.list.forEach { new ->
                        currentMap[new.id] = new
                    }
                    // 返回合并后的列表
                    currentMap.values.toList()
                }
            } else {
                updatePagingState(MODULE_FAVORITE) {
                    it.value = it.value.copy(
                        isLoadMore = false
                    )
                }
                onError(response.code, response.msg)
            }
        }
    }

    suspend fun getMyAssets(page: Int, pageSize: Int) =
        deepTalkieApiFastJson.getMyAssets(page, pageSize)

    suspend fun getFavorites(page: Int, pageSize: Int) =
        deepTalkieApiFastJson.getMyFavorite(page, pageSize)


    fun getPagingState(tag: String): MutableState<PagingState> {
        return pagingState.getOrPut(tag) { mutableStateOf(PagingState()) }
    }

    fun updatePagingState(
        tag: String,
        block: (MutableState<PagingState>) -> Unit
    ): MutableState<PagingState> {
        val state = getPagingState(tag)
        block(getPagingState(tag))
        loge("pagestate:${state.value}")
        return state
    }

    private fun onError(code: Int, msg: String?) {
        logd("code:$code,msg:$msg")
    }
}


data class PagingState(
    val page: Int = 1,
    val pageSize: Int = 8,
    val hasMore: Boolean = true,
    val isLoadMore: Boolean = false,
    val isRefreshing: Boolean = false
)