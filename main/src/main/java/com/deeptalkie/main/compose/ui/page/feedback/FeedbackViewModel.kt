package com.deeptalkie.main.compose.ui.page.feedback

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.deeptalkie.main.Membership
import com.deeptalkie.main.R
import com.deeptalkie.main.compose.utils.ILoadingState
import com.deeptalkie.main.compose.utils.loadingState
import com.deeptalkie.main.ext.showToast
import com.deeptalkie.main.repo.AppRepo
import com.deeptalkie.main.utils.NetWorkManager
import com.deeptalkie.main.utils.getString
import kotlinx.coroutines.launch

class FeedbackViewModel : ViewModel(),
    ILoadingState by loadingState() {
    private val appRepo = AppRepo()

    var showBackDialog by mutableStateOf(false)
        private set

    var feedback by mutableStateOf("")
        private set
    var email by mutableStateOf("")
        private set

    init {
        viewModelScope.launch {
            email = Membership.getEmail().orEmpty()
        }
    }

    fun showBackDialog(show: Boolean) {
        showBackDialog = show
    }

    fun onInput(content: String) {
        feedback = content
    }

    fun onInputEmail(content: String) {
        email = content
    }

    fun submit(onResp: (Int?) -> Unit) {
        if (!NetWorkManager.isNetworkConnectedSystem()) {
            showToast(getString(R.string.report_failed))
            return
        }
        viewModelScope.launch {
            showLoading(true)
            val resp = appRepo.submitFeedback(email, "Feedback", feedback)
            onResp(resp?.code)
            showLoading(false)
        }
    }
}