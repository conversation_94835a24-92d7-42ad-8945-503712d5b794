package com.deeptalkie.main.compose.ui.page.explore

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.staggeredgrid.LazyVerticalStaggeredGrid
import androidx.compose.foundation.lazy.staggeredgrid.StaggeredGridCells
import androidx.compose.foundation.lazy.staggeredgrid.StaggeredGridItemSpan
import androidx.compose.foundation.lazy.staggeredgrid.itemsIndexed
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Tab
import androidx.compose.material3.Text
import androidx.compose.material3.pulltorefresh.PullToRefreshBox
import androidx.compose.material3.pulltorefresh.PullToRefreshDefaults.Indicator
import androidx.compose.material3.pulltorefresh.rememberPullToRefreshState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import coil3.compose.AsyncImage
import com.clevguard.utils.ext.logv
import com.deeptalkie.main.R
import com.deeptalkie.main.compose.theme.White30
import com.deeptalkie.main.compose.theme.White60
import com.deeptalkie.main.compose.ui.components.BasicLoadMore
import com.deeptalkie.main.compose.ui.components.DTHorizontalSpacer
import com.deeptalkie.main.compose.ui.components.DTScrollableTabRow
import com.deeptalkie.main.compose.ui.components.DTVerticalSpacer
import com.deeptalkie.main.compose.ui.components.LocalDTNavigationBarHeight
import com.deeptalkie.main.db.result.AIRoleWithTags
import com.deeptalkie.main.db.table.AIRoleTag
import com.deeptalkie.main.db.table.isShowModel
import com.deeptalkie.main.db.table.isUserCreated
import com.deeptalkie.main.ext.imageRequest
import com.deeptalkie.main.ext.viewModelFactory
import kotlinx.coroutines.launch

/**
 * 角色列表
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ExploreRoleScreen(
    module: ExploreModule,
    onRoleClick: (Long) -> Unit,
    viewModel: ExploreViewModel = viewModel(
        key = module.name,
        factory = viewModelFactory {
            ExploreViewModel(module)
        }
    )
) {
    val roleTags by viewModel.roleTagsFlow.collectAsStateWithLifecycle()
    val refreshState = rememberPullToRefreshState()
    val pagerState = rememberPagerState { roleTags.size }
    val scope = rememberCoroutineScope()

    // 首次加载TAG数据
    LaunchedEffect(Unit) {
        if (roleTags.isEmpty()) {
            viewModel.refreshTags(pagerState.currentPage)
        }
    }

    PullToRefreshBox(
        isRefreshing = viewModel.loading,
        onRefresh = {
            viewModel.refreshTags(pagerState.currentPage)
        },
        modifier = Modifier
            .fillMaxSize(),
        state = refreshState,
        indicator = {
            Indicator(
                modifier = Modifier.align(Alignment.TopCenter),
                isRefreshing = viewModel.loading,
                state = refreshState,
                color = MaterialTheme.colorScheme.primary
            )
        },
    ) {
        Column(modifier = Modifier.fillMaxSize()) {
            Tabs(
                roleTags,
                pagerState.currentPage
            ) { i, tab ->
                scope.launch {
                    pagerState.animateScrollToPage(i)
                }
            }
            DTVerticalSpacer(10.dp)
            HorizontalPager(
                state = pagerState,
                Modifier
                    .fillMaxWidth()
                    .weight(1f),
                key = { roleTags[it].id }
            ) { index ->
                val tag = remember(index, roleTags) { roleTags[index] }
                val aiRoleWithTags by viewModel.getTagRolesFlow(tag).collectAsStateWithLifecycle()

                // 首次加载TAG角色数据
                LaunchedEffect(Unit) {
                    if (aiRoleWithTags.isEmpty()) {
                        logv("加载角色数据:$tag")
                        viewModel.refresh(tag)
                    }
                }

                RoleList(
                    aiRoleWithTags = aiRoleWithTags,
                    hasMore = viewModel.canLoadMore(tag),
                    onLoadMore = {
                        viewModel.loadMoreRoles(tag)
                    },
                    onRoleClick = onRoleClick
                )
            }
        }
    }
}

@Composable
private fun Tabs(
    tabs: List<AIRoleTag>,
    index: Int,
    onTabClick: (Int, AIRoleTag) -> Unit
) {
    DTScrollableTabRow(
        index,
        containerColor = Color.Transparent,
        edgePadding = 16.dp,
        indicator = {},
        divider = {}
    ) {
        tabs.forEachIndexed { i, tab ->
            val isEnd = i == tabs.lastIndex
            Tab(
                index == i,
                onClick = { onTabClick(i, tab) },
                Modifier.padding(end = if (isEnd) 0.dp else 20.dp),
                selectedContentColor = Color.Transparent
            ) {
                Text(
                    tab.name,
                    style = MaterialTheme.typography.bodySmall.copy(
                        color = if (index == i) MaterialTheme.colorScheme.onPrimary else White60,
                        fontSize = 13.sp,
                        fontWeight = if (index == i) FontWeight.Bold else FontWeight.Normal,
                    )
                )
            }
        }
    }
}

@Composable
private fun RoleList(
    aiRoleWithTags: List<AIRoleWithTags>,
    hasMore: Boolean,
    onLoadMore: () -> Unit,
    onRoleClick: (Long) -> Unit,
) {
    LazyVerticalStaggeredGrid(
        columns = StaggeredGridCells.Fixed(2),
        modifier = Modifier
            .fillMaxSize(),
        contentPadding = PaddingValues(
            start = 16.dp, end = 16.dp,
            bottom = 5.dp + LocalDTNavigationBarHeight.current
        ),
        verticalItemSpacing = 7.dp,
        horizontalArrangement = Arrangement.spacedBy(7.dp),
    ) {
        itemsIndexed(aiRoleWithTags, key = { _, it -> it.aiRole.id }) { _, item ->
            ExploreRoleItem(
                aiRoleWithTag = item,
                false,
                onRoleClick = { onRoleClick(it) }
            )
        }
        item(span = StaggeredGridItemSpan.FullLine) {
            if (hasMore) {
                LaunchedEffect(Unit) {
                    onLoadMore()
                }
                Column(
                    modifier = Modifier.fillMaxWidth(),
                    verticalArrangement = Arrangement.Center,
                    horizontalAlignment = Alignment.CenterHorizontally,
                ) {
                    DTVerticalSpacer(3.dp)
                    BasicLoadMore(Modifier.size(55.dp))
                    Text(
                        text = stringResource(R.string.assets_load_more_tip),
                        style = MaterialTheme.typography.labelMedium.copy(
                            color = White30,
                            lineHeight = 12.sp
                        )
                    )
                    DTVerticalSpacer(16.dp)
                }
            } else {
                DTVerticalSpacer(5.dp)
            }
        }
    }
}

@Composable
private fun ExploreRoleItem(
    aiRoleWithTag: AIRoleWithTags,
    isNew: Boolean,
    onRoleClick: (Long) -> Unit
) {
    val (aiRole, _) = aiRoleWithTag
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(10.dp))
            .clickable { onRoleClick(aiRole.id) }
    ) {
        // 角色图片
        AsyncImage(
            model = aiRole.images.firstOrNull()?.imageRequest(LocalContext.current) ?: "",
            contentDescription = aiRole.name,
            contentScale = ContentScale.Crop,
            modifier = Modifier
                .fillMaxWidth()
                .aspectRatio(aiRole.ratio())
        )

        // 底部渐变遮罩层
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(66.dp)
                .align(Alignment.BottomCenter)
                .background(
                    brush = Brush.verticalGradient(
                        colors = listOf(
                            Color.Transparent,
                            Color(0x99000000),
                            Color(0xCC000000)
                        )
                    )
                )
                .clip(RoundedCornerShape(bottomStart = 16.dp, bottomEnd = 16.dp))
        )

        if (aiRole.isShowModel()) {
            Row(
                modifier = Modifier
                    .align(Alignment.TopStart)
                    .padding(start = 4.dp, top = 4.dp)
                    .height(16.dp)
                    .clip(RoundedCornerShape(4.dp))
                    .background(
                        Brush.horizontalGradient(
                            listOf(
                                Color(0xFFEF0A57),
                                Color(0xFF7863FF),
                            )
                        )
                    )
                    .padding(horizontal = 4.dp),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Icon(
                    painterResource(R.drawable.ic_ai_model),
                    null,
                    Modifier.size(10.dp),
                    tint = Color.Unspecified
                )
                DTHorizontalSpacer(2.dp)
                Text(
                    aiRole.modelName.orEmpty(),
                    style = MaterialTheme.typography.bodyMedium.copy(
                        color = MaterialTheme.colorScheme.onPrimary,
                        fontSize = 9.sp,
                        lineHeight = 12.sp
                    )
                )
            }
        }

        if (aiRole.isHot() || isNew) {
            Box(
                Modifier
                    .padding(top = 4.dp, end = 4.dp)
                    .align(Alignment.TopEnd)
                    .clip(RoundedCornerShape(50, 50, 50, 0))
                    .background(
                        Brush.horizontalGradient(
                            if (aiRole.isHot()) {
                                listOf(
                                    Color(0xFFFA2875),
                                    Color(0xFFFB5B5B),
                                    Color(0xFFFB8D3A),
                                )
                            } else {
                                listOf(
                                    Color(0xFF40C365),
                                    Color(0xFF2CCFC3),
                                )
                            }
                        )
                    )
                    .padding(7.dp, 2.dp)
            ) {
                Text(
                    if (aiRole.isHot()) stringResource(R.string.explore_tag_hot)
                    else stringResource(R.string.explore_tag_new),
                    style = MaterialTheme.typography.headlineLarge.copy(
                        color = MaterialTheme.colorScheme.onPrimary,
                        fontSize = 10.sp,
                        lineHeight = 12.sp,
                        fontStyle = FontStyle.Italic
                    )
                )
            }
        }

        Column(
            Modifier
                .padding(start = 10.dp, end = 10.dp, bottom = 10.dp)
                .fillMaxWidth()
                .align(Alignment.BottomStart)
        ) {
            Row(Modifier.fillMaxWidth(), verticalAlignment = Alignment.CenterVertically) {
                if (aiRole.isUserCreated()) {
                    Icon(
                        painterResource(R.drawable.ic_user_create),
                        null,
                        Modifier.size(14.dp),
                        tint = Color.Unspecified
                    )
                    DTHorizontalSpacer(2.dp)
                }
                Text(
                    text = aiRole.name,
                    modifier = Modifier.weight(1f, false),
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    style = MaterialTheme.typography.bodyMedium.copy(
                        color = MaterialTheme.colorScheme.onPrimary,
                        fontSize = 14.sp,
                        lineHeight = 14.sp
                    )
                )
            }
            DTVerticalSpacer(5.dp)
            Text(
                text = aiRole.description,
                modifier = Modifier.fillMaxWidth(),
                maxLines = 2,
                overflow = TextOverflow.Ellipsis,
                style = MaterialTheme.typography.labelMedium.copy(
                    color = Color(0xFF8A8C91),
                    fontSize = 11.sp,
                    lineHeight = 15.sp,
                )
            )
        }
    }
}
