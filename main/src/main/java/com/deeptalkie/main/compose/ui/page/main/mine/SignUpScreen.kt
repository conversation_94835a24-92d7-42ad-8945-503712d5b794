package com.deeptalkie.main.compose.ui.page.main.mine

import androidx.compose.animation.animateColorAsState
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.lifecycle.viewmodel.compose.viewModel
import com.deeptalkie.main.R
import com.deeptalkie.main.activity.WebActivity
import com.deeptalkie.main.compose.theme.SecondaryColor
import com.deeptalkie.main.compose.theme.SocialBlue
import com.deeptalkie.main.compose.theme.White
import com.deeptalkie.main.compose.theme.White12
import com.deeptalkie.main.compose.theme.White30
import com.deeptalkie.main.compose.ui.components.DTButton
import com.deeptalkie.main.compose.ui.components.DTLoading
import com.deeptalkie.main.compose.ui.modifier.click
import com.deeptalkie.main.config.Constant
import com.deeptalkie.main.config.PASSWORD_REGEX
import com.deeptalkie.main.ext.showToast
import kotlinx.coroutines.flow.SharedFlow

/**
 *creater:linjinhao on 2025/5/21 16:44
 */
@Composable
fun SignUpRoute(
    viewModel: SvSignupViewModel = viewModel(),
    onBack: () -> Unit,
    onLogin: () -> Unit,
    onSignUpSuccess: () -> Unit,
) {
    var emailTip by remember { mutableStateOf("") }
    var pwdTip by remember { mutableStateOf("") }
    var confirmPwTip by remember { mutableStateOf("") }
    val context = LocalContext.current

    SignUpScreen(
        emailTip = emailTip,
        pwdTip = pwdTip,
        confirmPwTip = confirmPwTip,
        onBack = onBack,
        onchangePwdTip = { pwdTip = it },
        onChangeEmailTip = { emailTip = it },
        onChangeConfirmPwdTip = { confirmPwTip = it },
        onLogin = onLogin,
        onPolicy = { WebActivity.startBrowser(context, Constant.privacy + Constant.webParams) },
        onTerms = { WebActivity.startBrowser(context, (Constant.terms) + Constant.webParams) },
        onAgreement = { WebActivity.startBrowser(context, (Constant.eula) + Constant.webParams) }
    )

    HandleSignUpEvent(
        viewModel.signUpEvent,
        onSignUpSuccess = onSignUpSuccess,
        onChangeEmailTip = { emailTip = it })
}

@Composable
fun SignUpScreen(
    viewModel: SvSignupViewModel = viewModel(),
    emailTip: String,
    pwdTip: String,
    confirmPwTip: String,
    onBack: () -> Unit,
    onLogin: () -> Unit,
    onchangePwdTip: (String) -> Unit,
    onChangeEmailTip: (String) -> Unit,
    onChangeConfirmPwdTip: (String) -> Unit,
    onPolicy: () -> Unit,
    onTerms: () -> Unit,
    onAgreement: () -> Unit
) {
    val state = rememberScrollState()
    val softwareKeyboardController = LocalSoftwareKeyboardController.current
    val localFocusManager = LocalFocusManager.current
    var isShowPopup by remember { mutableStateOf(false) }
    Box(
        Modifier
            .fillMaxSize()
            .click {
                localFocusManager.clearFocus()
                isShowPopup = false
                softwareKeyboardController?.hide()
            }) {
        Image(
            painterResource(R.drawable.bg_common_bg_dt),
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.FillBounds,
            contentDescription = "bg"
        )
        Column(
            modifier = Modifier
                .statusBarsPadding()
                .fillMaxSize()
                .navigationBarsPadding()
                .verticalScroll(state)
        ) {
            ConstraintLayout(Modifier.padding(start = 30.dp, end = 30.dp)) {
                val (ivBack, title, tvEmail, tvPwd, confirmPw, btn, emailSuffix) = createRefs()

                var email by remember { mutableStateOf(TextFieldValue("")) }
                var password by remember { mutableStateOf("") }
                var confirmPwd by remember { mutableStateOf("") }
                Image(
                    painter = painterResource(R.drawable.ic_back2),
                    contentDescription = "backIcon",
                    modifier = Modifier
                        .constrainAs(ivBack) {
                            top.linkTo(parent.top, 10.dp)
                            start.linkTo(parent.start)
                        }
                        .click(onBack)
                        .size(36.dp)
                        .offset((-10).dp)
                )
                Title(
                    Modifier.constrainAs(title) {
                        top.linkTo(ivBack.bottom, 30.dp)
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                    },
                    title = R.string.sign_up,
                    des = R.string.signup_note
                )
                val current = LocalContext.current
                LoginEmail(
                    modifier = Modifier.constrainAs(tvEmail) {
                        top.linkTo(title.bottom, 30.dp)
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                    },
                    email = email, emailTip = emailTip,
                    onEmailChanged = { newEmail ->
                        email = if (newEmail.text.length > 50) {
                            val substring = newEmail.text.substring(0, 50)
                            TextFieldValue(substring, selection = TextRange(substring.length))
                        } else {
                            newEmail
                        }
                        if (email.text.isEmpty()) {
                            onChangeEmailTip(current.getString(R.string.enter_email))
                        } else {
                            onChangeEmailTip("")
                        }
                        isShowPopup = email.text.isNotEmpty()
                    }, onChangeEmailTip = { onChangeEmailTip(it) })
                LoginPwd(
                    modifier = Modifier.constrainAs(tvPwd) {
                        top.linkTo(tvEmail.bottom, 22.dp)
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                    },
                    pwd = password,
                    pwdTip = pwdTip,
                    onchangePwdTip = { onchangePwdTip(it) },
                    onPasswordChanged = { newPwd ->
                        password = newPwd
                        when {
                            // 未填写密码，失去焦点时，输入框下方提示“Please enter a password.”
                            password.isEmpty() -> {
                                onchangePwdTip(current.getString(R.string.enter_psw))
                            }
                            // 密码小于6个字符，失去焦点时，输入框下方提示“Password length must be 6-16 characters.”
                            password.length < 6 || password.length > 16 -> {
                                onchangePwdTip(current.getString(R.string.login_pwd_valid))
                            }
                            // 仅允许输入字母、数字、和特殊字符（见注册页的符号限制），当输入了不被允许的其他字符时，失去焦点，输入框下方提示“Password only contains alphabets, numbers and special characters.”
                            !PASSWORD_REGEX.matches(password) -> {
                                onchangePwdTip(current.getString(R.string.login_psw_valid))
                            }
                            // 密码符合情况，则清空提示
                            else -> {
                                onchangePwdTip("")
                            }
                        }
                    })
                val context = LocalContext.current
                ConfirmPwd(
                    modifier = Modifier.constrainAs(confirmPw) {
                        top.linkTo(tvPwd.bottom, 22.dp)
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                    },
                    confirmPwdTip = confirmPwTip,
                    pwd = password,
                    confirmPwd = confirmPwd,
                    onChangeConfirmPwdTip = {
                        onChangeConfirmPwdTip(it)
                    },
                    onConfirmPasswordChanged = { newConfirmPwd ->
                        confirmPwd = newConfirmPwd
                        when {
                            confirmPwd.isEmpty() -> {
                                onChangeConfirmPwdTip(context.getString(R.string.enter_psw))
                            }

                            confirmPwd.length < 6 || confirmPwd.length > 16 -> {
                                onChangeConfirmPwdTip(context.getString(R.string.login_pwd_valid))
                            }

                            !PASSWORD_REGEX.matches(confirmPwd) -> {
                                onChangeConfirmPwdTip(context.getString(R.string.login_psw_valid))
                            }

                            confirmPwd != password -> {
                                onChangeConfirmPwdTip(context.getString(R.string.login_pwd_confirm_valid))
                            }

                            else -> {
                                onChangeConfirmPwdTip("")
                            }
                        }

                    }
                )

                DTButton(
                    R.string.sign_up,
                    modifier = Modifier
                        .constrainAs(btn) {
                            top.linkTo(confirmPw.bottom, 50.dp)
                            start.linkTo(parent.start)
                            end.linkTo(parent.end)
                        }
                        .fillMaxSize()
                        .height(46.dp),
                    enable = checkAccountPassword(password) && checkEmail(email.text) && confirmPwd == password,
                    containerColor = SocialBlue,
                    disabledContainerColor = colorResource(R.color.color_4b4b51),
                    contentColor = White,
                    disabledContentColor = colorResource(R.color.color_8A8C91)
                ) {
                    localFocusManager.clearFocus()
                    viewModel.signUp(email = email.text, pwd = password)
                }
                EmailSuffix(
                    modifier = Modifier
                        .constrainAs(emailSuffix) {
                            top.linkTo(tvEmail.bottom)
                            start.linkTo(parent.start)
                            end.linkTo(parent.end)
                        }
                        .offset(0.dp, (-10).dp),
                    isShowPopup = isShowPopup,
                    email = email,
                    onItemEmailClick = { lastedEmail ->
                        email =
                            TextFieldValue(lastedEmail, selection = TextRange(lastedEmail.length))
                        isShowPopup = false
                    })
            }
            Spacer(modifier = Modifier.height(14.dp))
            SignUpHelp(onSignUp = onLogin)
            Spacer(modifier = Modifier.weight(1f))
            UserPolicy(onPolicy = onPolicy, onTerms = onTerms, onAgreement = onAgreement)
        }
        val loading by viewModel.loadingState.collectAsState()
        DTLoading(loading = loading.loading) {}
    }
}


@Composable
private fun SignUpHelp(onSignUp: () -> Unit) {
    Row(
        modifier = Modifier
            .padding(start = 16.dp, end = 16.dp)
            .fillMaxWidth(), horizontalArrangement = Arrangement.Center
    ) {
        Text(
            stringResource(R.string.login_already_account),
            style = TextStyle(
                fontSize = 14.sp,
                color = SecondaryColor, textAlign = TextAlign.Center
            ),
            modifier = Modifier
                .weight(1f)
                .click {
                    onSignUp()
                }
        )
    }
}

@Composable
fun ConfirmPwd(
    modifier: Modifier = Modifier,
    pwd: String,
    confirmPwd: String,
    confirmPwdTip: String,
    onConfirmPasswordChanged: (String) -> Unit,
    onChangeConfirmPwdTip: (String) -> Unit
) {
    var isPasswordVisible by remember { mutableStateOf(false) }

    Column(modifier = modifier.fillMaxWidth()) {
        val context = LocalContext.current
        BasicTextField(
            value = confirmPwd,
            onValueChange = {
                onConfirmPasswordChanged(it)
            },
            singleLine = true,
            textStyle = TextStyle(fontSize = 15.sp, color = White),
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Password),
            visualTransformation = if (isPasswordVisible) VisualTransformation.None else PasswordVisualTransformation(),
            modifier = Modifier
                .onFocusChanged {
                    if (it.hasFocus) {
                        when {
                            confirmPwd.isEmpty() -> {
                                onChangeConfirmPwdTip(context.getString(R.string.enter_psw))
                            }

                            confirmPwd.length < 6 -> {
                                onChangeConfirmPwdTip(context.getString(R.string.login_pwd_valid))
                            }

                            !PASSWORD_REGEX.matches(pwd) -> {
                                onChangeConfirmPwdTip(context.getString(R.string.login_psw_valid))
                            }

                            confirmPwd != pwd -> {
                                onChangeConfirmPwdTip(context.getString(R.string.login_pwd_confirm_valid))
                            }

                            else -> {
                                onChangeConfirmPwdTip("")
                            }
                        }
                    }
                }
                .background(color = White12, RoundedCornerShape(100.dp))
                .padding(start = 16.dp)
                .height(48.dp)
                .fillMaxWidth(),
            decorationBox = { innerTextField ->
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.padding(horizontal = 10.dp)
                ) {
                    Box(
                        modifier = Modifier.weight(1f),
                        contentAlignment = Alignment.CenterStart
                    ) {
                        if (confirmPwd.isEmpty()) {
                            Text(
                                stringResource(R.string.confirm_password),
                                style = TextStyle(fontSize = 15.sp, color = White30)
                            )
                        }
                        innerTextField()
                    }
                    // 眼睛图标
                    IconButton(
                        onClick = { isPasswordVisible = !isPasswordVisible },
                    ) {
                        if (isPasswordVisible) {
                            Icon(
                                painterResource(R.drawable.ic_login_pwd_show),
                                null,
                                tint = White30
                            )
                        } else {
                            Icon(
                                painterResource(R.drawable.ic_login_pwd_hide),
                                null,
                                tint = White30
                            )
                        }
                    }
                }
            }
        )
        val backgroundColor by animateColorAsState(
            targetValue = if (confirmPwdTip.isNotEmpty()) Color.Red else Color.Transparent
        )
        Spacer(modifier = Modifier.height(5.dp))
        Text(
            confirmPwdTip,
            fontSize = 10.sp,
            color = backgroundColor,
            modifier = Modifier
                .padding(start = 16.dp, end = 16.dp)
                .fillMaxWidth()
        )
    }
}

@Composable
fun HandleSignUpEvent(
    event: SharedFlow<SignupEvent>,
    onSignUpSuccess: () -> Unit,
    onChangeEmailTip: (String) -> Unit
) {
    val context = LocalContext.current
    LaunchedEffect(event) {
        event.collect {
            when (it) {
                SignupEvent.AccountHasBeenRegistered -> {
                    onChangeEmailTip(context.getString(R.string.login_email_exists))
                }

                SignupEvent.CommonError -> {
                    showToast(context.getString(R.string.network_error))
                }

                SignupEvent.SignupSuccess -> {
                    onSignUpSuccess()
                }
            }
        }
    }
}