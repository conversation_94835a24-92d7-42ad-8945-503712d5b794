package com.deeptalkie.main.compose.ui.dialog

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.deeptalkie.main.R
import com.deeptalkie.main.compose.theme.White40
import com.deeptalkie.main.compose.theme.White6
import com.deeptalkie.main.compose.ui.components.DTButton
import com.deeptalkie.main.compose.ui.components.DTHorizontalSpacer
import com.deeptalkie.main.compose.ui.components.DTVerticalSpacer

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CreateRoleBottomSheet(
    onDismiss: () -> Unit,
    onCreate: (isPublic: Boolean) -> Unit
) {
    var isPublic by remember { mutableStateOf(true) }
    DTBottomSheet(
        onDismiss = onDismiss,
        containerColor = Color(0xFF28282D)
    ) {
        Column(
            Modifier
                .fillMaxWidth()
                .navigationBarsPadding(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            DTVerticalSpacer(6.dp)
            Title()
            DTVerticalSpacer(12.dp)
            Column(
                Modifier
                    .padding(horizontal = 16.dp)
                    .fillMaxWidth()
                    .clip(RoundedCornerShape(12.dp))
                    .background(White6)
            ) {
                PermissionsCell(
                    R.drawable.ic_create_ai_role_public,
                    R.string.create_ai_role_permission_bottom_sheet_selection_public,
                    selected = isPublic,
                    onClick = { isPublic = true }
                )
                HorizontalDivider(
                    Modifier
                        .padding(horizontal = 14.dp)
                        .fillMaxWidth()
                        .height(1.dp),
                    color = White6
                )
                PermissionsCell(
                    R.drawable.ic_create_ai_role_private,
                    R.string.create_ai_role_permission_bottom_sheet_selection_private,
                    selected = !isPublic,
                    onClick = { isPublic = false }
                )
            }
            DTVerticalSpacer(8.dp)
            Tip()
            DTVerticalSpacer(20.dp)
            ConfirmBtn {
                onCreate(isPublic)
            }
            DTVerticalSpacer(20.dp)
        }
    }
}

@Composable
private fun Title() {
    Text(
        stringResource(R.string.create_ai_role_permission_bottom_sheet_title),
        style = MaterialTheme.typography.headlineMedium.copy(
            color = MaterialTheme.colorScheme.onPrimary,
            lineHeight = 24.sp
        )
    )
}

@Composable
private fun PermissionsCell(
    @DrawableRes icon: Int,
    @StringRes text: Int,
    selected: Boolean,
    onClick: () -> Unit
) {
    Row(
        Modifier
            .fillMaxWidth()
            .height(48.dp)
            .clickable(onClick = onClick)
            .padding(start = 16.dp, end = 20.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            painterResource(icon),
            null,
            Modifier.size(16.dp),
            tint = Color.Unspecified
        )
        DTHorizontalSpacer(10.dp)
        Text(
            stringResource(text),
            Modifier.weight(1f),
            style = MaterialTheme.typography.labelMedium.copy(
                color = MaterialTheme.colorScheme.onPrimary,
                fontSize = 15.sp,
                lineHeight = 24.sp
            )
        )
        if (selected) {
            Icon(
                painterResource(R.drawable.ic_voice_selected),
                null,
                Modifier.size(20.dp),
                tint = Color.Unspecified
            )
        }
    }
}

@Composable
private fun Tip() {
    Text(
        stringResource(R.string.create_ai_role_permission_bottom_sheet_tips),
        Modifier
            .padding(horizontal = 16.dp)
            .fillMaxWidth(),
        style = MaterialTheme.typography.labelSmall.copy(
            color = White40
        )
    )
}

@Composable
private fun ConfirmBtn(onConfirm: () -> Unit) {
    DTButton(
        R.string.confirm,
        modifier = Modifier
            .padding(horizontal = 16.dp)
            .fillMaxWidth()
            .height(44.dp),
        containerColor = MaterialTheme.colorScheme.primary,
        onClick = onConfirm
    )
}