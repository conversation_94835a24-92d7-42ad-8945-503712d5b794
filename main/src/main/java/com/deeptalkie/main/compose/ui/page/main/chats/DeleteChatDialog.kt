package com.deeptalkie.main.compose.ui.page.main.chats

import androidx.compose.runtime.Composable
import com.deeptalkie.main.R
import com.deeptalkie.main.compose.ui.dialog.DTWarnDialog

@Composable
fun DeleteChatDialog(
    onDismiss: () -> Unit,
    onDelete: () -> Unit
) {
    DTWarnDialog(
        title = R.string.chats_page_del_chat_dialog_del_tip,
        content = R.string.chats_page_del_chat_dialog_del_tip_content,
        cancelText = R.string.cancel,
        confirmText = R.string.Delete,
        onCancel = onDismiss,
        onConfirm = onDelete
    )
}