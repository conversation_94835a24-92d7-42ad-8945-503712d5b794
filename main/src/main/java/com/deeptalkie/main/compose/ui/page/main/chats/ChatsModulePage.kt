package com.deeptalkie.main.compose.ui.page.main.chats

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.deeptalkie.main.R
import com.deeptalkie.main.compose.navigation.MainRoute
import com.deeptalkie.main.compose.ui.components.DTPage

/**
 * 聊天列表页
 */
@Composable
fun ChatsModulePage(
    onClickExplore: () -> Unit,
    navigate: (MainRoute) -> Unit,
    viewModel: ChatsViewModel = viewModel(),
) {
    DTPage(
        background = R.drawable.bg_mine,
        loading = viewModel.loading,
        onDismissLoading = { viewModel.showLoading(false) }
    ) {
        Column(
            Modifier
                .statusBarsPadding()
                .fillMaxSize()
        ) {
            Box(
                Modifier
                    .fillMaxWidth()
                    .height(56.dp),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    stringResource(R.string.chats_page_title),
                    style = MaterialTheme.typography.headlineLarge.copy(
                        color = MaterialTheme.colorScheme.onPrimary,
                        fontSize = 16.sp,
                    )
                )
            }
            ChatsListView(onClickExplore = onClickExplore, navigate = navigate)
        }
    }
}