package com.deeptalkie.main.compose.ui.page.main.video

import android.content.Context
import androidx.media3.common.util.UnstableApi
import androidx.media3.database.StandaloneDatabaseProvider
import androidx.media3.datasource.DataSource
import androidx.media3.datasource.DefaultDataSource
import androidx.media3.datasource.cache.CacheDataSink
import androidx.media3.datasource.cache.CacheDataSource
import androidx.media3.datasource.cache.LeastRecentlyUsedCacheEvictor
import androidx.media3.datasource.cache.SimpleCache
import androidx.media3.exoplayer.source.DefaultMediaSourceFactory
import com.deeptalkie.main.App
import com.deeptalkie.main.config.TimberUtil
import java.io.File

@UnstableApi
object VideoCacheManager {
    private const val TAG = "VideoCacheManager"
    private const val CACHE_DIR = "exo_video_cache"
    private const val MAX_CACHE_SIZE = 512 * 1024 * 1024L // 512MB

    private val cache by lazy {
        createCache()
    }

    fun newMediaSourceFactory(context: Context): DefaultMediaSourceFactory {
        return DefaultMediaSourceFactory(context)
            .setDataSourceFactory(newCachedDataSourceFactory(context))
    }

    fun newCachedDataSourceFactory(context: Context): DataSource.Factory {
        return CacheDataSource.Factory()
            .setCache(cache)
            .setUpstreamDataSourceFactory(DefaultDataSource.Factory(context))
            .setFlags(CacheDataSource.FLAG_IGNORE_CACHE_ON_ERROR)
            .setCacheWriteDataSinkFactory(
                CacheDataSink.Factory()
                    .setCache(cache)
                    .setFragmentSize(CacheDataSink.DEFAULT_FRAGMENT_SIZE)
                    .setBufferSize(CacheDataSink.DEFAULT_BUFFER_SIZE)
            )
            .setEventListener(object : CacheDataSource.EventListener {
                override fun onCachedBytesRead(cacheSizeBytes: Long, cachedBytesRead: Long) {
                    TimberUtil.d(TAG, "Read $cachedBytesRead bytes from cache")
                }

                override fun onCacheIgnored(reason: Int) {
                    TimberUtil.d(TAG, "Cache ignored, reason: $reason")
                }
            })
    }

    private fun createCache(): SimpleCache {
        val context = App.Companion.getInstance()
        val cacheDir = File(context.cacheDir, CACHE_DIR).apply { mkdirs() }
        val databaseProvider = StandaloneDatabaseProvider(context)
        return SimpleCache(
            cacheDir,
            LeastRecentlyUsedCacheEvictor(MAX_CACHE_SIZE),
            databaseProvider
        )
    }

    fun clearCache() {
        cache.release()
    }
}