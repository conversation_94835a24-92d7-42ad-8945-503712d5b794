package com.deeptalkie.main.compose.ui.page.assets

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.staggeredgrid.LazyVerticalStaggeredGrid
import androidx.compose.foundation.lazy.staggeredgrid.StaggeredGridCells
import androidx.compose.foundation.lazy.staggeredgrid.StaggeredGridItemSpan
import androidx.compose.foundation.lazy.staggeredgrid.itemsIndexed
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.pulltorefresh.PullToRefreshBox
import androidx.compose.material3.pulltorefresh.PullToRefreshDefaults.Indicator
import androidx.compose.material3.pulltorefresh.rememberPullToRefreshState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import coil3.compose.AsyncImage
import com.deeptalkie.main.R
import com.deeptalkie.main.bean.MyAssetsBean
import com.deeptalkie.main.bean.MyFavoriteBean
import com.deeptalkie.main.compose.navigation.MainRoute
import com.deeptalkie.main.compose.theme.SocialBlue
import com.deeptalkie.main.compose.theme.White
import com.deeptalkie.main.compose.theme.White30
import com.deeptalkie.main.compose.theme.White60
import com.deeptalkie.main.compose.theme.White90
import com.deeptalkie.main.compose.ui.components.BasicLoadMore
import com.deeptalkie.main.compose.ui.components.DTButton
import com.deeptalkie.main.compose.ui.components.DTHorizontalSpacer
import com.deeptalkie.main.compose.ui.components.DTPage
import com.deeptalkie.main.compose.ui.components.DTVerticalSpacer
import com.deeptalkie.main.compose.ui.dialog.CreateAIRoleBottomSheet
import com.deeptalkie.main.compose.ui.modifier.click
import com.deeptalkie.main.ext.imageRequest
import kotlinx.coroutines.launch

/**
 *creater:linjinhao on 2025/6/19 10:23
 */
@Composable
fun MineAssetRoute(onClickRole: (Long) -> Unit, onBack: () -> Unit, navigate: (MainRoute) -> Unit) {
    MineAssetsScreen(
        onBack = onBack,
        onClickRole = {
            onClickRole(it)
        },
        navigate = {
            navigate(it)
        }
    )
}


@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun MineAssetsScreen(
    onBack: () -> Unit,
    viewModel: MineAssetsViewModel = viewModel(),
    onClickRole: (Long) -> Unit,
    navigate: (MainRoute) -> Unit
) {
    val brush = Brush.verticalGradient(listOf(Color(0xff1B1B20), Color(0xff0D1116)))
    val pagerState = rememberPagerState { 2 }
    val scope = rememberCoroutineScope()
    var refreshing by remember { mutableStateOf(false) }
    val refreshState = rememberPullToRefreshState()
    val tag by remember {
        derivedStateOf {
            when (pagerState.currentPage) {
                0 -> MineAssetsViewModel.MODULE_ASSETS
                else -> MineAssetsViewModel.MODULE_FAVORITE
            }
        }
    }

    var isShowCreateAIRoleBottomSheet by remember { mutableStateOf(false) }

    DTPage(background = brush, loading = viewModel.loading) {
        Column(
            modifier = Modifier
                .statusBarsPadding()
                .navigationBarsPadding()
                .fillMaxSize()
        ) {

            TopBar(
                onBack = onBack,
                currentPage = pagerState.currentPage,
                onSelectPage = {
                    scope.launch {
                        pagerState.scrollToPage(it)
                    }
                }
            )
            PullToRefreshBox(
                isRefreshing = refreshing,
                onRefresh = {
                    refreshing = true
                    scope.launch {
                        viewModel.onRefresh(tag)
                        refreshing = false
                    }
                },
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f),
                state = refreshState,
                indicator = {
                    Indicator(
                        modifier = Modifier.align(Alignment.TopCenter),
                        isRefreshing = refreshing,
                        state = refreshState,
                        color = SocialBlue
                    )
                },
            ) {
                HorizontalPager(
                    state = pagerState,
                    modifier = Modifier
                        .fillMaxSize()
                ) {
                    if (it == 0) {
                        AssetsList(
                            viewModel = viewModel,
                            onCreate = {
                                isShowCreateAIRoleBottomSheet = true
                            },
                            hasMore = { viewModel.hasMore(tag) },
                            onLoadMore = {
                                viewModel.onLoadMore(tag)
                            },
                            onClickRole = {
                                onClickRole(it)
                            },
                            isRefresh = viewModel.isRefreshing(tag)
                        )
                    } else {
                        FavoriteList(
                            viewModel = viewModel,
                            hasMore = { viewModel.hasMore(tag) },
                            onLoadMore = {
                                viewModel.onLoadMore(tag)
                            },
                            onClickRole = {
                                onClickRole(it)
                            },
                            isRefresh = viewModel.isRefreshing(tag)
                        )
                    }
                }
            }
        }
        CreateRoleBottomSheet(
            isShowCreateAIRoleBottomSheet = isShowCreateAIRoleBottomSheet,
            onDismiss = { isShowCreateAIRoleBottomSheet = it },
            navigate = { navigate(it) }
        )
    }
}

@Composable
private fun TopBar(onBack: () -> Unit, currentPage: Int, onSelectPage: (page: Int) -> Unit) {
    Row(
        modifier = Modifier
            .padding(horizontal = 16.dp)
            .fillMaxWidth(),
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Image(
            painter = painterResource(R.drawable.ic_back2),
            contentDescription = "backIcon",
            modifier = Modifier
                .click(onBack)
                .size(36.dp)
        )
        Row(
            modifier = Modifier
                .click {
                    onSelectPage(0)
                }
                .weight(1f), horizontalArrangement = Arrangement.End,
            verticalAlignment = Alignment.CenterVertically) {
            Column(
                modifier = Modifier
                    .click {
                        onSelectPage(0)
                    },
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                Text(
                    text = stringResource(R.string.my_assets),
                    style = TextStyle(
                        fontSize = 16.sp,
                        lineHeight = 24.sp,
                        color = if (currentPage == 0) White else White60,
                        fontWeight = if (currentPage == 0) FontWeight.W600 else FontWeight.W400
                    )
                )
                DTVerticalSpacer(3.dp)
                if (currentPage == 0) {
                    HorizontalDivider(
                        modifier = Modifier
                            .width(42.dp)
                            .clip(shape = RoundedCornerShape(10.dp)),
                        thickness = 2.dp,
                        color = White
                    )
                }
            }
        }

        DTHorizontalSpacer(24.dp)
        Row(
            modifier = Modifier
                .click {
                    onSelectPage(1)
                }
                .weight(1f),
            horizontalArrangement = Arrangement.Start,
            verticalAlignment = Alignment.CenterVertically) {
            Column(
                modifier = Modifier
                    .click {
                        onSelectPage(1)
                    },
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                Text(
                    text = stringResource(R.string.favorite),
                    style = TextStyle(
                        fontSize = 16.sp,
                        lineHeight = 24.sp,
                        color = if (currentPage == 1) White else White60,
                        fontWeight = if (currentPage == 1) FontWeight.W600 else FontWeight.W400
                    )
                )
                DTVerticalSpacer(3.dp)
                if (currentPage == 1) {
                    HorizontalDivider(
                        modifier = Modifier
                            .width(42.dp)
                            .clip(shape = RoundedCornerShape(10.dp)),
                        thickness = 2.dp,
                        color = White
                    )
                }
            }
        }

        DTHorizontalSpacer(36.dp)
    }
}

@Composable
private fun AssetsList(
    viewModel: MineAssetsViewModel = viewModel(),
    isRefresh: Boolean,
    onCreate: () -> Unit,
    hasMore: () -> Boolean,
    onLoadMore: suspend () -> Unit,
    onClickRole: (id: Long) -> Unit
) {
    val assetsList by viewModel.myAssetsStateFlow.collectAsState()
    LaunchedEffect(Unit) {
        if (assetsList.isEmpty()) {
            viewModel.initData(MineAssetsViewModel.MODULE_ASSETS)
        }
    }


    if (assetsList.isNotEmpty()) {
        LazyVerticalStaggeredGrid(
            columns = StaggeredGridCells.Fixed(2),
            contentPadding = PaddingValues(horizontal = 12.dp),
            modifier = Modifier.fillMaxSize(),
            verticalItemSpacing = 3.5.dp
        ) {
            itemsIndexed(assetsList, key = { _, item -> item.id }) { index, item ->
                ItemAsset(data = item, onClickRole = { onClickRole(it) })
            }

            item(span = StaggeredGridItemSpan.FullLine) {
                val hasMore by remember(assetsList) {
                    derivedStateOf { hasMore() && assetsList.size >= MineAssetsViewModel.PAGE_SIZE }
                }
                if (hasMore && !isRefresh) {
                    LaunchedEffect(Unit) {
                        onLoadMore()
                    }
                    Column(
                        modifier = Modifier.fillMaxWidth(),
                        verticalArrangement = Arrangement.Center,
                        horizontalAlignment = Alignment.CenterHorizontally,
                    ) {
                        DTVerticalSpacer(3.dp)
                        BasicLoadMore(Modifier.size(55.dp))
                        Text(
                            text = stringResource(R.string.assets_load_more_tip),
                            style = MaterialTheme.typography.labelMedium.copy(
                                color = White30,
                                lineHeight = 12.sp
                            )
                        )
                        DTVerticalSpacer(16.dp)
                    }
                } else {
                    DTVerticalSpacer(5.dp)
                }
            }
        }
    } else {
        if (!viewModel.loading) {
            EmptyAssetView(onCreate)
        }
    }

}

@Composable
private fun ItemAsset(data: MyAssetsBean, onClickRole: (id: Long) -> Unit) {
    Box(
        modifier = Modifier
            .padding(horizontal = 5.dp, vertical = 6.dp)
            .fillMaxWidth()
            .clip(RoundedCornerShape(8.dp))
            .clickable { onClickRole(data.id) }
    ) {
        // 角色图片
        AsyncImage(
            model = data.images.firstOrNull()?.imageRequest(LocalContext.current) ?: "",
            contentDescription = data.name,
            contentScale = ContentScale.Crop,
            modifier = Modifier
                .fillMaxWidth()
                .aspectRatio(data.getRatio())
        )

        // 底部渐变遮罩层
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(66.dp)
                .align(Alignment.BottomCenter)
                .background(
                    brush = Brush.verticalGradient(
                        colors = listOf(
                            Color.Transparent,
                            Color(0x99000000),
                            Color(0xCC000000)
                        )
                    )
                )
                .clip(RoundedCornerShape(bottomStart = 8.dp, bottomEnd = 8.dp))
        )
        // 角色名称
        Text(
            text = data.name,
            color = Color.White,
            fontSize = 14.sp,
            fontWeight = FontWeight.Bold,
            textAlign = TextAlign.Start,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
            modifier = Modifier
                .align(Alignment.BottomStart)
                .padding(start = 10.dp, bottom = 40.dp)
        )
        // 角色描述
        Text(
            text = data.description,
            color = colorResource(R.color.color_8A8C91),
            fontSize = 11.sp,
            maxLines = 2,
            lineHeight = 13.sp,
            overflow = TextOverflow.Ellipsis,
            modifier = Modifier
                .align(Alignment.BottomStart)
                .padding(start = 10.dp, end = 10.dp, bottom = 9.dp)
                .fillMaxWidth()
        )
        Box(
            modifier = Modifier
                .height(18.dp)
                .align(alignment = Alignment.TopEnd)
                .approvalStatusTagBg(data.approvalStatus)
                .padding(horizontal = 8.dp),
            contentAlignment = Alignment.Center,
        ) {
            Text(
                text = data.approvalStatusText.orEmpty(),
                style = MaterialTheme.typography.bodySmall.copy(
                    color = White90,
                    fontSize = 11.sp
                ),
            )
        }
    }
}

fun Modifier.approvalStatusTagBg(approvalStatus: Int): Modifier {
    return background(
        Brush.horizontalGradient(
            colors = when (approvalStatus) {
                0 -> listOf(Color(0x00575DFF), Color(0x73575DFF), Color(0xFF575DFF))
                1 -> listOf(Color(0x0015D98D), Color(0x7315D98D), Color(0xFF15D98D))
                2 -> listOf(Color(0x00DF2033), Color(0x73DF2033), Color(0xFFDF2033))
                else -> listOf(Color.Transparent, Color.Transparent)
            }
        )
    )
}

@Composable
private fun FavoriteList(
    viewModel: MineAssetsViewModel = viewModel(),
    hasMore: () -> Boolean,
    isRefresh: Boolean,
    onLoadMore: suspend () -> Unit,
    onClickRole: (id: Long) -> Unit
) {
    val favoriteList by viewModel.myFavoriteStateFlow.collectAsState()
    LaunchedEffect(Unit) {
        if (favoriteList.isEmpty()) {
            viewModel.initData(MineAssetsViewModel.MODULE_FAVORITE)
        }
    }

    if (favoriteList.isNotEmpty()) {
        LazyVerticalStaggeredGrid(
            columns = StaggeredGridCells.Fixed(2),
            modifier = Modifier.fillMaxSize(),
            contentPadding = PaddingValues(horizontal = 12.dp),
            verticalItemSpacing = 3.5.dp
        ) {
            itemsIndexed(favoriteList, key = { _, item -> item.id }) { index, item ->
                ItemFavorite(data = item, onClickRole = { onClickRole(it) })
            }

            item(span = StaggeredGridItemSpan.FullLine) {
                val hasMore by remember(favoriteList) {
                    derivedStateOf {
                        hasMore() && (favoriteList.size >= MineAssetsViewModel.PAGE_SIZE)
                    }
                }
                if (hasMore && !isRefresh) {
                    LaunchedEffect(Unit) {
                        onLoadMore()
                    }
                    Column(
                        modifier = Modifier.fillMaxWidth(),
                        verticalArrangement = Arrangement.Center,
                        horizontalAlignment = Alignment.CenterHorizontally,
                    ) {
                        DTVerticalSpacer(3.dp)
                        BasicLoadMore(Modifier.size(55.dp))
                        Text(
                            text = stringResource(R.string.assets_load_more_tip),
                            style = MaterialTheme.typography.labelMedium.copy(
                                color = White30,
                                lineHeight = 12.sp
                            )
                        )
                        DTVerticalSpacer(16.dp)
                    }
                } else {
                    DTVerticalSpacer(5.dp)
                }
            }
        }
    } else {
        if (!viewModel.loading) {
            EmptyFavoriteView()
        }
    }

}

@Composable
private fun ItemFavorite(data: MyFavoriteBean, onClickRole: (id: Long) -> Unit) {
    Box(
        modifier = Modifier
            .padding(horizontal = 5.dp, vertical = 6.dp)
            .fillMaxWidth()
            .clip(RoundedCornerShape(10.dp))
            .clickable { onClickRole(data.id) }
    ) {
        // 角色图片
        AsyncImage(
            model = data.images.firstOrNull()?.imageRequest(LocalContext.current) ?: "",
            contentDescription = data.name,
            contentScale = ContentScale.Crop,
            modifier = Modifier
                .fillMaxWidth()
                .aspectRatio(data.getRatio())
        )

        // 底部渐变遮罩层
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(66.dp)
                .align(Alignment.BottomCenter)
                .background(
                    brush = Brush.verticalGradient(
                        colors = listOf(
                            Color.Transparent,
                            Color(0x99000000),
                            Color(0xCC000000)
                        )
                    )
                )
                .clip(RoundedCornerShape(bottomStart = 16.dp, bottomEnd = 16.dp))
        )
        // 角色名称
        Text(
            text = data.name,
            color = Color.White,
            fontSize = 14.sp,
            fontWeight = FontWeight.Bold,
            textAlign = TextAlign.Start,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
            modifier = Modifier
                .align(Alignment.BottomStart)
                .padding(start = 10.dp, bottom = 40.dp)
        )

        // 角色描述
        Text(
            text = data.description,
            color = colorResource(R.color.color_8A8C91),
            fontSize = 11.sp,
            maxLines = 2,
            lineHeight = 13.sp,
            overflow = TextOverflow.Ellipsis,
            modifier = Modifier
                .align(Alignment.BottomStart)
                .padding(start = 10.dp, end = 10.dp, bottom = 9.dp)
                .fillMaxWidth()
        )
    }
}

@Composable
private fun EmptyAssetView(onCreate: () -> Unit) {
    Column(
        modifier = Modifier.fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {

        Image(
            painter = painterResource(R.drawable.empty_asset),
            contentDescription = "emptyImage",
            modifier = Modifier.size(208.dp)
        )
        Text(
            text = stringResource(R.string.no_character_is_currently_created),
            style = TextStyle(fontSize = 15.sp, color = White, lineHeight = 24.sp)
        )

        DTVerticalSpacer(186.dp)
        DTButton(
            R.string.create_character,
            contentColor = White,
            containerColor = SocialBlue,
            textStyle = TextStyle(fontSize = 16.sp, fontWeight = FontWeight.W500),
            modifier = Modifier.size(325.dp, 46.dp)
        ) {
            onCreate()
        }
    }
}

@Composable
private fun EmptyFavoriteView() {
    Column(
        modifier = Modifier.fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {

        Image(
            painter = painterResource(R.drawable.empty_asset),
            contentDescription = "emptyImage",
            modifier = Modifier.size(208.dp)
        )
        Text(
            text = stringResource(R.string.no_favorite_characters),
            style = TextStyle(fontSize = 15.sp, color = White, lineHeight = 24.sp)
        )
    }
}

@Composable
private fun CreateRoleBottomSheet(
    isShowCreateAIRoleBottomSheet: Boolean,
    onDismiss: (Boolean) -> Unit,
    navigate: (MainRoute) -> Unit
) {
    if (isShowCreateAIRoleBottomSheet) {
        CreateAIRoleBottomSheet(
            onDismiss = {
                onDismiss(false)
            },
            onSexSelected = {
                onDismiss(false)
                navigate(MainRoute.CreateAIRole(it))
            }
        )
    }
}