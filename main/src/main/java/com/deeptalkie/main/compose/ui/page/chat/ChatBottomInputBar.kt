package com.deeptalkie.main.compose.ui.page.chat

import androidx.activity.compose.BackHandler
import androidx.compose.animation.AnimatedContent
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.togetherWith
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.ime
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.windowInsetsBottomHeight
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActionScope
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.max
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import androidx.lifecycle.viewmodel.compose.viewModel
import com.deeptalkie.main.R
import com.deeptalkie.main.compose.ui.components.DTHorizontalSpacer
import com.deeptalkie.main.compose.ui.components.DTTextFieldRowGroup
import com.deeptalkie.main.compose.ui.components.DTTextFieldTextStyle
import com.deeptalkie.main.compose.ui.components.DTVerticalSpacer
import com.deeptalkie.main.ext.toDp
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

private fun Modifier.clipChatBottomBar(clip: Boolean, size: Dp): Modifier {
    if (!clip) return this
    val shape = RoundedCornerShape(topStart = size, topEnd = size)
    return then(
        Modifier
            .clip(shape)
            .border(
                1.dp,
                Brush.verticalGradient(
                    listOf(Color(0x1AFFFFFF), Color(0x00FFFFFF))
                ),
                shape
            )
    )
}

@Composable
fun ChatBottomBar(
    focusRequester: FocusRequester,
    modifier: Modifier = Modifier,
    viewModel: ChatViewModel = viewModel(),
    gotoBuy: () -> Unit,
) {
    val imeHeight = WindowInsets.ime.getBottom(LocalDensity.current).toDp
    val navigationHeight = WindowInsets.navigationBars.getBottom(LocalDensity.current).toDp

    if (viewModel.aiReplyState.enabled) {
        BackHandler {
            viewModel.foldAiReply()
        }
    }
    ConstraintLayout(
        modifier
            .clipChatBottomBar(viewModel.selectedReplyMsg == null, 25.dp)
            .background(Brush.verticalGradient(listOf(Color(0xBF1B1926), Color(0xFF1B1926)))),
    ) {
        val (bgRef) = createRefs()
        AnimatedVisibility(
            viewModel.aiReplyState.enabled,
            Modifier
                .fillMaxWidth()
                .constrainAs(bgRef) {
                    top.linkTo(parent.top)
                    bottom.linkTo(parent.bottom)
                    height = Dimension.fillToConstraints
                }
        ) {
            Image(
                painter = painterResource(R.drawable.bg_chat_input_bar),
                null,
                Modifier.fillMaxSize(),
                contentScale = ContentScale.FillBounds
            )
        }
        Column {
            ChatBottomInputBar(focusRequester, gotoBuy, Modifier.padding(vertical = 16.dp))
            AnimatedContent(
                viewModel.aiReplyState.enabled,
                Modifier.fillMaxWidth(),
                transitionSpec = {
                    fadeIn(animationSpec = tween(300))
                        .togetherWith(fadeOut(animationSpec = tween(300)))
                }
            ) { enabled ->
                if (enabled) {
                    Column(Modifier.heightIn(imeHeight)) {
                        DTVerticalSpacer(2.dp)
                        AIReplyView(
                            onClickKeyboard = {
                                focusRequester.requestFocus()
                            },
                            gotoBuy = gotoBuy
                        )
                        DTVerticalSpacer(10.dp)
                        Spacer(Modifier.windowInsetsBottomHeight(WindowInsets.navigationBars))
                    }
                } else {
                    DTVerticalSpacer(max(imeHeight, navigationHeight))
                }
            }
        }
    }
}

@Composable
fun ChatBottomInputBar(
    focusRequester: FocusRequester,
    gotoBuy: () -> Unit,
    modifier: Modifier = Modifier,
    viewModel: ChatViewModel = viewModel(),
) {
    val context = LocalContext.current
    Row(modifier.fillMaxWidth(), verticalAlignment = Alignment.CenterVertically) {
        val aiReplyEnabled = viewModel.aiReplyState.enabled
        val keyboardController = LocalSoftwareKeyboardController.current
        val focusManager = LocalFocusManager.current
        val scope = rememberCoroutineScope()

        DTHorizontalSpacer(16.dp)
        MsgInput(
            value = viewModel.inputText,
            onValueChange = viewModel::onTextInput,
            aiReplyEnabled = aiReplyEnabled,
            onPhotoIconClick = {
                viewModel.showCharacterImage(true)
            },
            onAITipsIconClick = {
                focusManager.clearFocus()
                keyboardController?.hide()
                viewModel.requestAITips(context, gotoBuy)
            },
            onKeyboardSend = {
                if (viewModel.inputText.isNotBlank()) {
                    viewModel.sendTextMsg(context, gotoBuy = gotoBuy)
                }
            },
            modifier = Modifier
                .weight(1f)
                .heightIn(44.dp)
                .focusRequester(focusRequester)
                .onFocusChanged { focusState ->
                    if (focusState.isFocused) {
                        scope.launch {
                            if (aiReplyEnabled) {
                                delay(360)
                            }
                            viewModel.foldAiReply()
                        }
                    }
                }
                .background(Color(0xFF312F3A), RoundedCornerShape(50)),
        )
        DTHorizontalSpacer(8.dp)
        Image(
            painterResource(R.drawable.ic_send_msg),
            null,
            Modifier
                .size(44.dp)
                .clip(CircleShape)
                .clickable(enabled = viewModel.inputText.isNotBlank(), onClick = {
                    viewModel.sendTextMsg(context, gotoBuy = gotoBuy)
                }),
            contentScale = ContentScale.Crop
        )
        DTHorizontalSpacer(16.dp)
    }
}

@Composable
private fun MsgInput(
    value: String,
    onValueChange: (String) -> Unit,
    aiReplyEnabled: Boolean,
    onPhotoIconClick: () -> Unit,
    onAITipsIconClick: () -> Unit,
    onKeyboardSend: KeyboardActionScope.() -> Unit,
    modifier: Modifier = Modifier,
) {
    DTTextFieldRowGroup(
        value = value,
        onValueChange = onValueChange,
        modifier = modifier,
        keyboardOptions = KeyboardOptions(imeAction = ImeAction.Send),
        keyboardActions = KeyboardActions(onSend = onKeyboardSend),
        maxLines = 4,
        textPadding = PaddingValues(start = 16.dp, top = 12.dp, bottom = 12.dp),
        placeholder = {
            Text(
                stringResource(R.string.chat_page_input_placeholder),
                Modifier.fillMaxWidth(),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                style = DTTextFieldTextStyle.copy(color = MaterialTheme.colorScheme.onTertiary),
            )
        },
        endContent = {
            DTHorizontalSpacer(2.dp)
            Icon(
                painterResource(R.drawable.ic_req_chat_img),
                null,
                modifier = Modifier
                    .size(40.dp)
                    .clip(CircleShape)
                    .clickable(onClick = onPhotoIconClick)
                    .padding(10.dp),
                tint = Color.Unspecified
            )
            Icon(
                painterResource(if (aiReplyEnabled) R.drawable.ic_refresh else R.drawable.ic_ai_tips),
                null,
                modifier = Modifier
                    .size(40.dp)
                    .clip(CircleShape)
                    .clickable(onClick = onAITipsIconClick)
                    .padding(10.dp),
                tint = Color.Unspecified
            )
            DTHorizontalSpacer(2.dp)
        }
    )
}