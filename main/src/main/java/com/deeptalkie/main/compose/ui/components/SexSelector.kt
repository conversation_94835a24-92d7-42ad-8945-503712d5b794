package com.deeptalkie.main.compose.ui.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.deeptalkie.main.R

@Composable
fun SexSelector(index: Int, onSelected: (index: Int) -> Unit) {
    Row(Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.Center) {
        SexSelectBox(
            index == 0,
            painterResource(R.drawable.ic_female),
            stringResource(R.string.new_user_info_select_page_male)
        ) {
            onSelected(0)
        }
        DTHorizontalSpacer(22.dp)
        SexSelectBox(
            index == 1,
            painterResource(R.drawable.ic_male),
            stringResource(R.string.new_user_info_select_page_female)
        ) {
            onSelected(1)
        }
        DTHorizontalSpacer(22.dp)
        SexSelectBox(
            index == 2,
            painterResource(R.drawable.ic_non_binary),
            stringResource(R.string.new_user_info_select_page_non_binary)
        ) {
            onSelected(2)
        }
    }
}

@Composable
fun SexSelectBox(selected: Boolean, icon: Painter, text: String, onSelected: () -> Unit) {
    val bg = if (selected) R.drawable.sex_selected_bg else R.drawable.sex_un_selected_bg

    Column(
        Modifier.clickable(
            onClick = onSelected,
            indication = null,
            interactionSource = remember { MutableInteractionSource() }),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Box(contentAlignment = Alignment.Center) {
            Image(
                painterResource(bg),
                null,
                Modifier.size(95.dp)
            )
            Icon(
                icon,
                null,
                Modifier.size(27.dp),
                tint = MaterialTheme.colorScheme.onPrimary
            )
        }
        Text(
            text = text,
            color = MaterialTheme.colorScheme.onTertiary,
            fontSize = 13.sp,
            fontWeight = FontWeight.W400,
            textAlign = TextAlign.Center,
        )
    }
}

@Preview
@Composable
fun SexSelectorPrev() {
    SexSelector(0) {}
}