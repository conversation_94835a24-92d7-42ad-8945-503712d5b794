package com.deeptalkie.main.compose.ui.page.chat

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.constraintlayout.compose.ConstraintLayout
import com.deeptalkie.main.R
import com.deeptalkie.main.compose.theme.White70
import com.deeptalkie.main.compose.ui.components.DTVerticalSpacer

@Composable
fun ChatTopBar(
    name: String,
    approvalStatusText: String?,
    isFavorites: Boolean,
    onBackClick: () -> Unit,
    onFavoritesClick: () -> Unit,
    onReportClick: () -> Unit
) {
    ConstraintLayout(
        Modifier
            .fillMaxWidth()
            .statusBarsPadding()
            .height(62.dp)
    ) {
        val (backRef, nameRef, collectRef, reportRef, lineRef) = createRefs()

        Image(
            painterResource(R.drawable.ic_back2),
            null,
            Modifier
                .size(36.dp)
                .constrainAs(backRef) {
                    start.linkTo(parent.start, 16.dp)
                    centerVerticallyTo(parent)
                }
                .clip(CircleShape)
                .clickable(onClick = onBackClick),
        )
        Column(
            Modifier
                .padding(horizontal = 110.dp)
                .fillMaxSize()
                .constrainAs(nameRef) {
                    centerHorizontallyTo(parent)
                    centerVerticallyTo(parent)
                },
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                name,
                Modifier.fillMaxWidth(),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                style = MaterialTheme.typography.headlineLarge.copy(
                    color = MaterialTheme.colorScheme.onPrimary,
                    fontSize = 16.sp,
                    lineHeight = 16.sp,
                    textAlign = TextAlign.Center
                )
            )
            if (approvalStatusText != null) {
                DTVerticalSpacer(4.dp)
                Text(
                    "(${approvalStatusText})",
                    Modifier.fillMaxWidth(),
                    style = MaterialTheme.typography.bodySmall.copy(
                        color = White70,
                        fontSize = 11.sp,
                        textAlign = TextAlign.Center
                    )
                )
            }
        }
        Image(
            painterResource(if (isFavorites) R.drawable.ic_collected else R.drawable.ic_uncollected),
            null,
            Modifier
                .size(36.dp)
                .constrainAs(collectRef) {
                    end.linkTo(reportRef.start, 10.dp)
                    centerVerticallyTo(parent)
                }
                .clip(CircleShape)
                .clickable(onClick = onFavoritesClick)
        )
        Image(
            painterResource(R.drawable.ic_report),
            null,
            Modifier
                .size(36.dp)
                .constrainAs(reportRef) {
                    end.linkTo(parent.end, 16.dp)
                    centerVerticallyTo(parent)
                }
                .clip(CircleShape)
                .clickable(onClick = onReportClick)
        )
        HorizontalDivider(
            Modifier
                .fillMaxWidth()
                .constrainAs(lineRef) {
                    bottom.linkTo(parent.bottom)
                },
            0.5.dp,
            Color(0xFF323436)
        )
    }
}