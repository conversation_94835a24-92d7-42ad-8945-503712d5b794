package com.deeptalkie.main.compose.ui.components

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import coil3.compose.AsyncImage

@Composable
fun DTPage(
    modifier: Modifier = Modifier,
    background: Any? = null,
    loading: Boolean = false,
    onDismissLoading: () -> Unit = {},
    content: @Composable BoxScope.() -> Unit,
) {
    Box(modifier.fillMaxSize()) {
        if (background != null) {
            AsyncImage(background, null, Modifier.fillMaxSize(), contentScale = ContentScale.Crop)
        }
        content()
    }
    DTLoading(loading, onDismissLoading)
}