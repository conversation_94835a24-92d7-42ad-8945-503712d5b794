package com.deeptalkie.main.compose.ui.page.main

import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.platform.LocalContext
import com.deeptalkie.main.LoginInvalidEvent
import com.deeptalkie.main.Membership
import com.deeptalkie.main.R
import com.deeptalkie.main.compose.navigation.MainRoute
import com.deeptalkie.main.compose.ui.components.DTNavigationBarScaffold
import com.deeptalkie.main.compose.ui.components.DTNavigationItemData
import com.deeptalkie.main.compose.ui.components.SelectableDrawable
import com.deeptalkie.main.compose.ui.dialog.CreateAIRoleBottomSheet
import com.deeptalkie.main.compose.ui.page.explore.ExploreModuleScreen
import com.deeptalkie.main.compose.ui.page.main.chats.ChatsModulePage
import com.deeptalkie.main.compose.ui.page.main.home.HomeModulePage
import com.deeptalkie.main.compose.ui.page.main.home.PrivacyAgreementDialog
import com.deeptalkie.main.compose.ui.page.main.mine.MineRoute
import com.deeptalkie.main.config.ReportEventUtils
import com.deeptalkie.main.config.UmConstant
import com.deeptalkie.main.ext.showToast
import com.deeptalkie.main.live2d.Live2DChatActivity
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.launch

@Composable
fun MainPage(navigate: (MainRoute) -> Unit) {
    val context = LocalContext.current
    val navigationData = remember { getMainPageNavigationData() }
    val pagerState = rememberPagerState { navigationData.size }
    val scope = rememberCoroutineScope()
    val centerNavItem = remember {
        DTNavigationItemData(
            4,
            SelectableDrawable(R.drawable.ic_add_role, R.drawable.ic_add_role),
        )
    }
    var isShowCreateAIRoleBottomSheet by remember { mutableStateOf(false) }
    val showCreateRoleBottomSheet = remember(context, navigate) {
        click@{
            if (!Membership.isLogin()) {
                navigate(MainRoute.Login)
                return@click
            }
            Live2DChatActivity.start(context)
            /*if (!UserManager.canCreateAIRole()) {
                RequirePurchaseDialog(context, RequirePurchaseDialog.TYPE_CREATE_AI_ROLE) {
                    navigate(MainRoute.Product)
                }.show()
                return@click
            }
            isShowCreateAIRoleBottomSheet = true*/
        }
    }

    DTNavigationBarScaffold(
        pagerState,
        navigationData,
        centerNavItem,
        onCenterClick = showCreateRoleBottomSheet
    ) { index ->
        when (index) {
            0 -> HomeModulePage(
                navigate = navigate,
                onJumpMine = {
                    scope.launch {
                        pagerState.scrollToPage(3)
                    }
                },
                showCreateRoleBottomSheet
            )

            1 -> ChatsModulePage(
                onClickExplore = {
                    scope.launch {
                        pagerState.scrollToPage(2)
                    }
                },
                navigate = navigate
            )

            2 -> ExploreModuleScreen(
                onRoleClick = { roleId ->
                    navigate(MainRoute.RoleDetail(roleId))
                }
            )

            3 -> MineRoute(
                onEnterProduct = {
                    navigate(MainRoute.Product)
                },
                onEnterUserInfo = { navigate(MainRoute.UserInfo) },
                onSignUp = { navigate(MainRoute.SignUp) },
                onLogin = { navigate(MainRoute.Login) },
                onNavigate = navigate
            )
        }
    }
    PrivacyAgreementDialog()
    if (isShowCreateAIRoleBottomSheet) {
        CreateAIRoleBottomSheet(
            onDismiss = {
                isShowCreateAIRoleBottomSheet = false
            },
            onSexSelected = {
                isShowCreateAIRoleBottomSheet = false
                ReportEventUtils.onEvent(
                    UmConstant.CREATE_ROLE,
                    mapOf(UmConstant.CREATE_ROLE to it.eventValue())
                )
                navigate(MainRoute.CreateAIRole(it))
            }
        )
    }

    HandleEvent(
        Membership.loginInvalidEvent,
        onLoginInvalid = {
            navigate(MainRoute.Login)
        },
        onLoginOutByOther = { navigate(MainRoute.Login) },
        onLoginOutByChangePassword = { navigate(MainRoute.Login) })
}

fun getMainPageNavigationData(): List<DTNavigationItemData> {
    return listOf(
        DTNavigationItemData(
            0,
            SelectableDrawable(R.drawable.ic_home_selected, R.drawable.ic_home_unselected),
        ),
        DTNavigationItemData(
            1,
            SelectableDrawable(R.drawable.ic_chats_selected, R.drawable.ic_chats_unselected),
        ),
        DTNavigationItemData(
            2,
            SelectableDrawable(R.drawable.ic_explore_selected, R.drawable.ic_explore_unselected),
        ),
        DTNavigationItemData(
            3,
            SelectableDrawable(R.drawable.ic_my_selected, R.drawable.ic_my_unselected),
        )
    )
}

@Composable
private fun HandleEvent(
    event: SharedFlow<LoginInvalidEvent>,
    onLoginInvalid: () -> Unit,
    onLoginOutByChangePassword: () -> Unit,
    onLoginOutByOther: () -> Unit
) {
    val context = LocalContext.current
    LaunchedEffect(event) {
        Membership.loginInvalidEvent.collect {
            when (it) {
                LoginInvalidEvent.LoginInvalid -> {
                    onLoginInvalid()
                    showToast(context.getString(R.string.logout_out_toast))
                }

                LoginInvalidEvent.LoginOutByChangePassword -> {
                    onLoginOutByChangePassword()
                    showToast(context.getString(R.string.logout_out_toast))
                }

                LoginInvalidEvent.LoginOutByOther -> {
                    onLoginOutByOther()
                    showToast(context.getString(R.string.logout_out_toast))
                }
            }
        }
    }
}