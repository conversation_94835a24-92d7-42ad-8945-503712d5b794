package com.deeptalkie.main.compose.ui.page.main.createrole

import androidx.room.Transaction
import com.deeptalkie.kidsguard.net.getSuccessData
import com.deeptalkie.kidsguard.net.runHttp
import com.deeptalkie.main.R
import com.deeptalkie.main.bean.AIImageResult
import com.deeptalkie.main.bean.AIImageStyle
import com.deeptalkie.main.bean.AIRoleDescResp
import com.deeptalkie.main.compose.ui.dialog.AIRoleSex
import com.deeptalkie.main.db.table.AIRoleTag
import com.deeptalkie.main.ext.showToast
import com.deeptalkie.main.repo.BaseDeepTalkieRepo
import com.deeptalkie.main.utils.getString

class CreateAIRoleRepo : BaseDeepTalkieRepo() {
    fun getAllTagsFlow() = aiRoleTagDao.getAllTagsFlow()

    suspend fun requestAIRoleDesc(aiRoleSex: AIRoleSex): AIRoleDescResp? {
        return runHttp {
            remoteApi.requestAIRoleDesc(aiRoleSex.toGender()).getSuccessData()
        }
    }

    suspend fun requestAIRoleDescOptimize(prompt: String, aiRoleSex: AIRoleSex): AIRoleDescResp? {
        return runHttp {
            remoteApi.requestAIRoleDescOptimize(prompt, aiRoleSex.toGender()).getSuccessData()
        }
    }

    suspend fun requestAIRoleImageDesc(aiRoleSex: AIRoleSex): AIRoleDescResp? {
        return runHttp {
            remoteApi.requestAIRoleImageDesc(aiRoleSex.toGender()).getSuccessData()
        }
    }

    suspend fun requestAIRoleImageDescOptimize(
        prompt: String,
        aiRoleSex: AIRoleSex
    ): AIRoleDescResp? {
        return runHttp {
            remoteApi.requestAIRoleImageDescOptimize(prompt, aiRoleSex.toGender()).getSuccessData()
        }
    }

    suspend fun requestAIImageStyleList(): List<AIImageStyle>? {
        return runHttp {
            dtApi.getAIImageStyleList().getDataOrNull()?.list
        }
    }

    suspend fun generateAIImage(
        prompt: String,
        aiRoleSex: AIRoleSex,
        stylePrompt: String
    ): Long? {
        return runHttp {
            dtApi.generateAIImage(prompt, aiRoleSex.toGender(), stylePrompt).getDataOrNull()?.id
        }
    }

    suspend fun queryImageResult(id: Long): AIImageResult? {
        return runHttp {
            remoteApi.queryImageResult(id).getSuccessData()
        }
    }

    suspend fun createRole(
        name: String,
        description: String,
        prompt: String,
        helloWord: String,
        image: String,
        voiceId: Long,
        isPublic: Int,
        tagIds: String,
    ): Long? {
        return runHttp {
            val resp = remoteApi.createRole(
                name,
                description,
                prompt,
                helloWord,
                image,
                voiceId,
                isPublic,
                tagIds
            )
            if (!resp.isSuccess()) {
                showToast(resp.msg ?: getString(R.string.network_error))
                return@runHttp null
            }
            resp.getSuccessData()?.id
        }
    }

    @Transaction
    suspend fun loadAllTagsToDb(module: String? = null): List<AIRoleTag>? {
        val tags = loadAllTags(module) ?: return null
        // 删除云端不存在的标签
        if (module == null) {
            aiRoleTagDao.deleteNotExistTags(tags)
        }
        aiRoleTagDao.upsertAll(tags)
        return tags
    }

    private suspend fun loadAllTags(module: String? = null): List<AIRoleTag>? {
        val resp = runHttp { dtApi.getTags(module = module) } ?: return null
        val tags = resp.getDataOrNull()?.list ?: return null
        return tags.map { it.toAIRoleTag() }
    }

    suspend fun createTag(name: String): AIRoleTag? {
        return runHttp {
            val resp = dtApi.createTag(name)
            if (!resp.isSuccess) return@runHttp null
            val tagId = resp.getDataOrNull()?.id ?: return@runHttp null
            val tag = AIRoleTag(tagId, name, 1, "", "")
            aiRoleTagDao.insertOrIgnore(tag)
            tag
        }
    }
}

fun AIRoleSex.toGender(): Int {
    return when (this) {
        AIRoleSex.Guys -> 1
        AIRoleSex.Girls -> 2
    }
}