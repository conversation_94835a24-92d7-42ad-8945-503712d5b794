package com.deeptalkie.main.compose.ui.dialog

import androidx.annotation.StringRes
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalClipboardManager
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.style.LineBreak
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.deeptalkie.main.R
import com.deeptalkie.main.compose.theme.Black100
import com.deeptalkie.main.compose.theme.Black70
import com.deeptalkie.main.compose.ui.components.DTButton
import com.deeptalkie.main.compose.ui.components.DTVerticalSpacer
import com.deeptalkie.main.compose.ui.modifier.click
import com.deeptalkie.main.ext.showToast
import com.deeptalkie.main.utils.getString

@Composable
fun GuestPaySuccessDialog(
    expirationTimeText: String,
    account: String,
    password: String,
    onDismiss: () -> Unit
) {
    DTDialog(onDismiss = {}) {
        Column(
            Modifier
                .width(300.dp)
                .background(
                    MaterialTheme.colorScheme.onPrimary,
                    RoundedCornerShape(16.dp)
                )
                .padding(13.dp, 24.dp)
        ) {
            Title(R.string.guest_pay_success)
            DTVerticalSpacer(10.dp)
            ContentCell(R.string.guest_order_info, expirationTimeText, false)
            ContentCell(R.string.account, account)
            ContentCell(R.string.password, password)
            DTVerticalSpacer(10.dp)
            ContentTips()
            DTVerticalSpacer(20.dp)
            ConfirmBtn(onDismiss)
        }
    }
}

@Composable
private fun ColumnScope.Title(@StringRes text: Int) {
    Text(
        stringResource(text),
        Modifier.align(Alignment.CenterHorizontally),
        style = MaterialTheme.typography.headlineLarge.copy(
            color = Black100,
            fontSize = 16.sp
        )
    )
}

@Composable
private fun ContentCell(@StringRes title: Int, text: String, canCopy: Boolean = true) {
    val clipboardManager = LocalClipboardManager.current
    Text(
        buildAnnotatedString {
            append(stringResource(title))
            append("：")
            withStyle(SpanStyle(color = Black70)) {
                append(text)
            }
        },
        Modifier
            .fillMaxWidth()
            .click {
                if (!canCopy) return@click
                clipboardManager.setText(AnnotatedString(text))
                showToast(getString(R.string.guest_pay_success_copy_toast))
            },
        style = MaterialTheme.typography.bodySmall.copy(
            color = Black100,
            lineBreak = LineBreak.Paragraph
        )
    )
}

@Composable
private fun ContentTips() {
    Text(
        stringResource(R.string.guest_pay_success_tips),
        Modifier.fillMaxWidth(),
        style = MaterialTheme.typography.labelMedium.copy(
            color = Color(0xFFFF2424)
        )
    )
}

@Composable
private fun ConfirmBtn(onClick: () -> Unit) {
    DTButton(
        stringResource(R.string.confirm),
        modifier = Modifier
            .padding(horizontal = 14.dp)
            .fillMaxWidth()
            .height(40.dp),
        background = MaterialTheme.colorScheme.primary,
        onClick = onClick
    )
}