package com.deeptalkie.main.compose.ui.page.product

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicText
import androidx.compose.foundation.text.ClickableText
import androidx.compose.foundation.text.InlineTextContent
import androidx.compose.foundation.text.TextAutoSize
import androidx.compose.foundation.text.appendInlineContent
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.Placeholder
import androidx.compose.ui.text.PlaceholderVerticalAlign
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.deeptalkie.main.Membership
import com.deeptalkie.main.R
import com.deeptalkie.main.activity.WebActivity
import com.deeptalkie.main.compose.theme.Black
import com.deeptalkie.main.compose.theme.Transparent
import com.deeptalkie.main.compose.theme.White
import com.deeptalkie.main.compose.theme.White12
import com.deeptalkie.main.compose.theme.White50
import com.deeptalkie.main.compose.theme.White70
import com.deeptalkie.main.compose.theme.White80
import com.deeptalkie.main.compose.ui.components.DTButton
import com.deeptalkie.main.compose.ui.components.DTHorizontalSpacer
import com.deeptalkie.main.compose.ui.components.DTLoading
import com.deeptalkie.main.compose.ui.components.DTVerticalSpacer
import com.deeptalkie.main.compose.ui.components.SelectableDrawable
import com.deeptalkie.main.compose.ui.dialog.GuestPaySuccessDialog
import com.deeptalkie.main.compose.ui.modifier.click
import com.deeptalkie.main.compose.utils.ChildDesign
import com.deeptalkie.main.compose.utils.toAnnotatedStringParameters
import com.deeptalkie.main.config.Constant
import com.deeptalkie.main.config.ReportEventUtils
import com.deeptalkie.main.config.UmConstant
import com.deeptalkie.main.ext.showToast
import com.imyfone.membership.api.bean.ConfirmResultBean
import com.imyfone.membership.api.bean.SKUBean

private const val TAG_PRIVACY = "TAG_PRIVACY"
private const val TAG_TERMS = "TAG_TERMS"
private const val TAG_EULA = "TAG_EULA"

/**
 *creater:linjinhao on 2025/5/19 15:02
 */
@Composable
fun ProductRoute(
    onBack: () -> Unit,
    onGooglePurchaseSuccess: (email: String, bean: ConfirmResultBean?) -> Unit,
    onLogin: () -> Unit
) {
    val context = LocalContext.current
    val viewModel: ProductViewModel = viewModel()
    val state by viewModel.state.collectAsState()
    ReportEventUtils.OnEvent(UmConstant.PURCHASE_PAGE)
    ProductScreen(
        onBack = onBack,
        viewModel = viewModel,
        onPrivacy = {
            WebActivity.startBrowser(
                context,
                Constant.privacy + Constant.webParams
            )
        },
        onTerm = { WebActivity.startBrowser(context, Constant.terms + Constant.webParams) },
        onEula = { WebActivity.startBrowser(context, Constant.eula + Constant.webParams) },
        onCancelSubscript = {
            Membership.toGoogleSubscription(context)
        }
    )
    DTLoading(loading = state.skuState is SkuUIState.Loading || state.purchaseLoading) {}

    LaunchedEffect(viewModel.state) {
        when (state.skuState) {
            SkuUIState.Fail -> {
                showToast(context.getString(R.string.network_error))
            }

            SkuUIState.Init, SkuUIState.Loading -> {

            }

            is SkuUIState.Success -> {
            }
        }
    }
    LaunchedEffect(viewModel.event) {
        viewModel.event.collect {
            when (it) {
                is ProductEvent.GooglePurchaseSuccess -> {
                    onGooglePurchaseSuccess(viewModel.userBean?.email ?: "", it.bean)
                }

                is ProductEvent.LoginTip -> {
                    showToast(context.getString(R.string.payment_successful))
                }

                ProductEvent.NeedLogin -> onLogin()

                ProductEvent.CancelGooglePurchase -> showToast(context.getString(R.string.pay_cancel))

                is ProductEvent.GooglePurchaseFail -> {
                    showToast(context.getString(R.string.pay_cancel))
                }

                ProductEvent.ICartPurchaseFinish -> {
                    showToast(context.getString(R.string.payment_successful))
                }

                ProductEvent.GoogleConfirmOrderFail -> {
                    showToast(context.getString(R.string.pay_cancel))
                }
            }
        }
    }
}

@Composable
private fun ProductScreen(
    onBack: () -> Unit,
    viewModel: ProductViewModel,
    onPrivacy: () -> Unit,
    onEula: () -> Unit,
    onTerm: () -> Unit,
    onCancelSubscript: () -> Unit
) {
    val scrollState = rememberLazyListState()
    var alpha by remember { mutableFloatStateOf(0f) }

    // 监听滚动状态
    LaunchedEffect(scrollState) {
        snapshotFlow { scrollState.firstVisibleItemScrollOffset }
            .collect { offset ->
                alpha = if (offset > 0) {
                    1f
                } else {
                    0f
                }
            }
    }

    Box(
        modifier = Modifier
            .background(colorResource(R.color.color_121118))
            .fillMaxSize()
    ) {
        ConstraintLayout(
            modifier = Modifier
                .navigationBarsPadding()
                .fillMaxSize()
        ) {
            val (center, btm) = createRefs()
            LazyColumn(
                modifier = Modifier
                    .constrainAs(center) {
                        top.linkTo(parent.top)
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                        bottom.linkTo(btm.top)
                        height = Dimension.fillToConstraints
                    }
                    .fillMaxWidth(),
                state = scrollState
            ) {
                item {
                    Box(modifier = Modifier.fillMaxWidth()) {
                        Image(
                            painterResource(R.drawable.ic_bg_buy),
                            contentDescription = "image",
                            modifier = Modifier
                                .fillMaxWidth()
                                .aspectRatio(375f / 340f)
                        )
                        Column(
                            modifier = Modifier
                                .statusBarsPadding()
                                .fillMaxWidth()
                        ) {
                            DTVerticalSpacer(240.dp)
                            PlanDescText()
                        }
                    }
                }
                if (viewModel.isVip) {
                    item {
                        SwitchSku()
                    }
                }

                itemsIndexed(viewModel.skus, key = { _, it -> it.skuID }) { index, item ->
                    if (viewModel.isSelectSubPlan) {
                        ProductItem(
                            item,
                            index == 0,
                            selected = viewModel.selectIndex == index,
                            onSelect = { viewModel.selectIndex = index })
                    } else {
                        SkuCoinItem(
                            item, selected = viewModel.selectIndex == index,
                            onSelect = { viewModel.selectIndex = index })
                    }
                    DTVerticalSpacer(15.dp)
                }
                if (viewModel.isSelectSubPlan) {
                    item {
                        DTVerticalSpacer(5.dp)
                        BenefitCompareTableHeader()
                    }
                    itemsIndexed(
                        viewModel.benefits,
                        key = { _, it -> it.benefit },
                    ) { index, item ->
                        ItemBenefitCompare(
                            benefitCompareBean = item,
                            needRadius = viewModel.benefits.lastIndex == index
                        )
                    }
                }

                item {
                    DTVerticalSpacer(16.dp)
                }
            }
            LayBottom(
                modifier = Modifier.constrainAs(btm) {
                    top.linkTo(center.bottom)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                    bottom.linkTo(parent.bottom)
                },
                viewModel = viewModel,
                onBuyProduct = {
                    viewModel.purchase(skuBean = it)

                },
                onPrivacy = onPrivacy,
                onEula = onEula,
                onTerm = onTerm,
                onCancelSubscript = onCancelSubscript
            )
        }
    }
    Title(onBack = onBack, topBarAlpha = alpha)

    if (viewModel.showGuestPaySuccessDialog) {
        val expirationTime by viewModel.vipFailureTimeTextFlow.collectAsStateWithLifecycle()
        GuestPaySuccessDialog(
            "${viewModel.paySku?.name}", //（${stringResource(R.string.expires_title)}${expirationTime}）
            viewModel.payEmail,
            viewModel.payEmailPassword,
            onDismiss = {
                viewModel.showGuestPaySuccessDialog = false
            }
        )
    }
}

@Composable
fun Title(onBack: () -> Unit, topBarAlpha: Float) {
    Box(modifier = Modifier.fillMaxWidth()) {
        var height by remember { mutableIntStateOf(0) }
        // 获取当前屏幕的密度
        val density = LocalDensity.current.density
        Box(
            Modifier
                .alpha(topBarAlpha)
                .background(Color(0xE6101015))
                .fillMaxWidth()
                .height((height / density).toInt().dp)

        )
        Row(
            modifier = Modifier
                .onGloballyPositioned {
                    height = it.size.height
                }
                .statusBarsPadding()
                .fillMaxWidth()
                .padding(start = 20.dp, top = 4.dp, end = 20.dp, bottom = 10.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Image(
                painterResource(R.drawable.ic_back2),
                modifier = Modifier
                    .size(36.dp)
                    .click {
                        onBack()
                    },
                contentDescription = "backIcon"
            )
            Text(
                text = stringResource(R.string.member_center),
                style = TextStyle(
                    fontSize = 18.sp,
                    fontWeight = FontWeight.W600,
                    color = White,
                    textAlign = TextAlign.Center
                ), modifier = Modifier.weight(1f)
            )
            DTHorizontalSpacer(36.dp)
        }
    }
}

@Composable
private fun SwitchSku() {
    val ratio by remember { mutableFloatStateOf(375 / 58f) }
    val viewModel: ProductViewModel = viewModel()
    Box(
        modifier = Modifier
            .fillMaxWidth()
    ) {
        Image(
            painterResource(if (viewModel.isSelectSubPlan) R.drawable.ic_select_lincese else R.drawable.ic_select_coin),
            contentDescription = "background",
            modifier = Modifier
                .fillMaxWidth()
                .aspectRatio(ratio)
        )
        val brush2 = Brush.horizontalGradient(
            listOf(
                White50, White50
            )
        )
        Row(
            modifier = Modifier
                .background(Transparent, RoundedCornerShape(15.dp))
                .fillMaxWidth()
                .aspectRatio(ratio),
            horizontalArrangement = Arrangement.Center,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Box(
                modifier = Modifier
                    .click(viewModel::switchLicenseSku)
                    .weight(1f)
                    .fillMaxSize()
                    .padding(top = 12.dp),
                contentAlignment = Alignment.TopCenter
            ) {
                val brush1 = Brush.horizontalGradient(
                    listOf(
                        Color(0xffF5C0FF),
                        Color(0xffF6D9FF),
                        Color(0xffCBB1FF)
                    )
                )
                Text(
                    text = stringResource(R.string.lincese),
                    style = TextStyle(
                        fontSize = if (viewModel.isSelectSubPlan) 15.sp else 14.sp,
                        textAlign = TextAlign.Center,
                        fontWeight = FontWeight.W600,
                        brush = if (viewModel.isSelectSubPlan) brush1 else brush2
                    )
                )
            }

            Box(
                modifier = Modifier
                    .click(viewModel::switchCoinSku)
                    .weight(1f)
                    .fillMaxSize()
                    .padding(top = 12.dp),
                contentAlignment = Alignment.TopCenter
            ) {
                val brush1 = Brush.linearGradient(listOf(Color(0xffFFFAF4), Color(0xffFFCE74)))
                Text(
                    text = stringResource(R.string.coin),
                    style = TextStyle(
                        fontSize = if (!viewModel.isSelectSubPlan) 15.sp else 14.sp,
                        fontWeight = FontWeight.W600,
                        textAlign = TextAlign.Center,
                        brush = if (!viewModel.isSelectSubPlan) brush1 else brush2
                    )
                )
            }
        }
    }
}

@Composable
fun LayBottom(
    modifier: Modifier = Modifier,
    viewModel: ProductViewModel = viewModel(),
    onBuyProduct: (skuBean: SKUBean) -> Unit,
    onPrivacy: () -> Unit,
    onTerm: () -> Unit,
    onEula: () -> Unit,
    onCancelSubscript: () -> Unit
) {
    Column(
        modifier = modifier
            .background(Black, shape = RoundedCornerShape(topStart = 14.dp, topEnd = 14.dp))
            .fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Spacer(modifier = Modifier.height(20.dp))
        DTButton(
            text = stringResource(R.string.subscribe),
            contentColor = White,
            bg = if (viewModel.isSelectSubPlan) SelectableDrawable(R.drawable.bg_btn_buy) else SelectableDrawable(
                R.drawable.bg_btn_coin_buy
            ),
            textStyle = TextStyle(
                fontSize = 15.sp,
                fontWeight = FontWeight.W700,
                lineHeight = 24.sp,
                color = if (viewModel.isSelectSubPlan) White else Color(0xff481E0E)
            ),
            enable = viewModel.skus.isNotEmpty(),
            modifier = Modifier
                .padding(start = 16.dp, end = 16.dp)
                .fillMaxWidth()
                .height(48.dp),
            contentPadding = PaddingValues(0.dp),
            shape = RoundedCornerShape(22.dp)
        ) {
            onBuyProduct(viewModel.skus[viewModel.selectIndex])
        }

        Spacer(modifier = Modifier.height(12.dp))
        if (viewModel.isGoogleChannel) {
            Text(
                text = stringResource(R.string.how_to_cancel_subscript),
                style = TextStyle(fontSize = 12.sp, color = colorResource(R.color.color_B8B8B8)),
                modifier = Modifier.click {
                    onCancelSubscript()
                }
            )
            Spacer(modifier = Modifier.height(6.dp))
        }
        Text(
            stringResource(R.string.auto_renewal_text),
            style = TextStyle(fontSize = 12.sp, color = colorResource(R.color.color_B8B8B8))
        )
        Spacer(modifier = Modifier.height(6.dp))
        val userPrivacy = userPrivacy()
        @Suppress("DEPRECATION")
        ClickableText(text = userPrivacy(), onClick = { offset ->
            userPrivacy.getStringAnnotations(TAG_PRIVACY, start = offset, end = offset)
                .firstOrNull()
                ?.let {
                    onPrivacy()
                }
            userPrivacy.getStringAnnotations(TAG_TERMS, start = offset, end = offset).firstOrNull()
                ?.let {
                    onTerm()
                }
            userPrivacy.getStringAnnotations(TAG_EULA, start = offset, end = offset).firstOrNull()
                ?.let {
                    onEula()
                }
        }, style = TextStyle(fontSize = 10.sp, color = colorResource(R.color.color_B8B8B8)))
    }
}

@Composable
fun PlanDescText() {
    Column(modifier = Modifier.fillMaxWidth()) {
        val brush = Brush.linearGradient(
            listOf(
                colorResource(R.color.color_F4A0FF),
                colorResource(R.color.color_DD6FFF),
                colorResource(R.color.color_4E65FF),
                White80
            )
        )
        Text(
            text = stringResource(R.string.choose_your_plan),
            style = TextStyle(
                brush = brush,
                fontSize = 18.sp,
                fontWeight = FontWeight.W800,
                fontStyle = FontStyle.Italic,
                lineHeight = 20.sp, textAlign = TextAlign.Center
            ), modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 12.dp)
        )
        Text(
            text = stringResource(R.string.anonymous),
            style = TextStyle(fontSize = 12.sp, color = White70, textAlign = TextAlign.Center),
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 12.dp)
        )
    }
}

@Composable
fun ProductItem(
    skuBean: SKUBean,
    isHot: Boolean,
    selected: Boolean,
    onSelect: () -> Unit
) {
    val brush1 = Brush.linearGradient(
        listOf(
            colorResource(R.color.color_dc68ff), colorResource(R.color.color_FFB489),
            colorResource(R.color.color_816BFF)
        )
    )
    val brush2 = Brush.linearGradient(
        listOf(
            Color.Transparent, Color.Transparent
        )
    )

    Row(
        modifier = Modifier
            .padding(start = 16.dp, end = 16.dp)
            .background(
                if (selected) {
                    colorResource(R.color.color_4D416B50)
                } else White12, shape = RoundedCornerShape(14.dp)
            )
            .border(
                width = 1.2.dp,
                brush = if (selected) brush1 else brush2,
                shape = RoundedCornerShape(14.dp)
            )
            .click {
                onSelect()
            }
            .fillMaxWidth()
            .padding(start = 12.dp, end = 12.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        val buyItem = skuBean.buyList.firstOrNull()

        Column(modifier = Modifier.weight(1f)) {
            Spacer(Modifier.height(12.dp))
            Row(verticalAlignment = Alignment.CenterVertically) {
                val hotIconId = remember { "hot_icon" }
                val inlineContent = remember {
                    mapOf(
                        hotIconId to InlineTextContent(
                            Placeholder(
                                width = 48.sp,
                                height = 36.sp,
                                placeholderVerticalAlign = PlaceholderVerticalAlign.Center
                            )
                        ) {
                            Icon(
                                painterResource(R.drawable.ic_buy_hot),
                                null,
                                modifier = Modifier.size(width = 48.dp, height = 36.dp),
                                tint = Color.Unspecified
                            )
                        }
                    )
                }
                Text(
                    text = buildAnnotatedString {
                        append(skuBean.name)
                        if (isHot) {
                            appendInlineContent(hotIconId)
                        }
                    },
                    inlineContent = inlineContent,
                    style = TextStyle(fontSize = 16.sp, color = White, fontWeight = FontWeight.W800)
                )
            }
            Spacer(modifier = Modifier.height(7.dp))
            Text(
                text = buildAnnotatedString {
                    withStyle(SpanStyle(textDecoration = TextDecoration.LineThrough)) {
                        append(buyItem?.virtualPriceText.orEmpty())
                    }
                    withStyle(
                        SpanStyle(
                            color = MaterialTheme.colorScheme.onPrimary,
                            fontSize = 14.sp,
                        )
                    ) {
                        val coinsAndUnit = skuBean.content.split(" ")
                        val coins = coinsAndUnit.firstOrNull().orEmpty()
                        val unit = coinsAndUnit.lastOrNull().orEmpty()

                        append(" (")
                        withStyle(SpanStyle(color = Color(0xFFFFCE74))) {
                            append(coins)
                        }
                        append(" $unit)")
                    }
                },
                style = MaterialTheme.typography.labelSmall.copy(
                    color = White70,
                    fontSize = 10.sp,
                )
            )
            Spacer(Modifier.height(17.dp))
        }
        Text(
            text = buildAnnotatedString {
                append(buyItem?.actualPriceText.orEmpty())
            },
            style = MaterialTheme.typography.headlineLarge.copy(
                color = MaterialTheme.colorScheme.onPrimary,
                fontSize = 36.sp,
                lineHeight = 36.sp,
            )
        )
    }
}

@Composable
fun SkuCoinItem(
    skuBean: SKUBean,
    selected: Boolean,
    onSelect: () -> Unit
) {
    val brush = Brush.linearGradient(
        0.0f to Color(0xff322E27),
        0.2f to Color(0xff202322),
        0.47f to Color(0xff2C241D),
        0.69f to Color(0xff24201B),
        1f to Color(0xff3E3630)
    )

    val selectedBrush = Brush.linearGradient(
        0.0f to Color(0xffF99466),
        0.37f to Color(0xffFFD8AA),
        1f to Color(0xffFFA449)
    )
    val unSelectBrush = Brush.linearGradient(listOf(Transparent, Transparent))

    Row(
        modifier = Modifier
            .padding(start = 16.dp, end = 16.dp)
            .background(brush, RoundedCornerShape(14.dp))
            .border(
                width = 1.2.dp,
                brush = if (selected) selectedBrush else unSelectBrush,
                shape = RoundedCornerShape(14.dp)
            )
            .click {
                onSelect()
            }
            .fillMaxWidth()
            .heightIn(min = 56.dp)
            .padding(start = 8.dp, end = 16.dp, top = 12.dp, bottom = 12.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Image(
            painterResource(R.drawable.ic_coin),
            contentDescription = "coin",
            modifier = Modifier.size(20.dp)
        )
        DTHorizontalSpacer(4.dp)
        Row(modifier = Modifier.weight(1f), verticalAlignment = Alignment.CenterVertically) {
            Text(
                text = skuBean.name,
                style = TextStyle(
                    fontSize = 14.sp,
                    color = Color(0xffFFF8EF),
                    fontWeight = FontWeight.W600, lineHeight = 22.sp
                )
            )
            DTHorizontalSpacer(8.dp)
            if (skuBean.content.lastOrNull() == '%') {
                Box(
                    Modifier
                        .widthIn(36.dp)
                        .height(21.dp)
                        .clip(RoundedCornerShape(4.dp))
                        .background(
                            brush = Brush.horizontalGradient(
                                listOf(
                                    Color(0xFFFFCE4B),
                                    Color(0xFFFF8F49),
                                    Color(0xFFFF5549),
                                )
                            )
                        )
                        .padding(horizontal = 4.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        "+${skuBean.content}",
                        style = MaterialTheme.typography.headlineLarge.copy(
                            color = MaterialTheme.colorScheme.onPrimary,
                            fontSize = 12.sp,
                            lineHeight = 18.sp
                        )
                    )
                }
            }
        }
        Text(
            text = skuBean.buyList.firstOrNull()?.actualPriceText ?: "",
            style = TextStyle(
                fontSize = 22.sp,
                brush = Brush.linearGradient(listOf(White, Color(0xffFFCDA1))),
                fontWeight = FontWeight.W700
            )
        )
    }
}

@Composable
fun BenefitCompareTableHeader() {
    Row(
        modifier = Modifier
            .padding(horizontal = 16.dp)
            .fillMaxWidth()
            .height(IntrinsicSize.Max)
            .padding(top = 16.dp)
    ) {
        Column(
            modifier = Modifier
                .background(
                    color = colorResource(R.color.color_232328),
                    shape = RoundedCornerShape(topStart = 14.dp)
                )
                .padding(top = 16.dp, bottom = 16.dp)
                .weight(2f)
                .fillMaxHeight(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                stringResource(R.string.premium_benefits),
                style = TextStyle(
                    fontSize = 16.sp,
                    fontWeight = FontWeight.W600,
                    color = colorResource(R.color.color_FBBD8A), textAlign = TextAlign.Center
                ), modifier = Modifier.fillMaxWidth()
            )
        }
        Column(
            modifier = Modifier
                .background(
                    color = colorResource(R.color.color_303035),
                )
                .padding(top = 16.dp, bottom = 16.dp)
                .weight(1f)
                .fillMaxHeight(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                stringResource(R.string.free),
                style = TextStyle(
                    fontSize = 16.sp,
                    fontWeight = FontWeight.W600,
                    textAlign = TextAlign.Center,
                    color = colorResource(R.color.color_FBBD8A)
                ), modifier = Modifier.fillMaxWidth()
            )
        }
        Column(
            modifier = Modifier
                .background(
                    color = colorResource(R.color.color_392A55),
                    shape = RoundedCornerShape(topEnd = 14.dp)
                )
                .padding(top = 16.dp, bottom = 16.dp)
                .weight(1f)
                .fillMaxHeight(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                stringResource(R.string.premium),
                style = TextStyle(
                    fontSize = 16.sp,
                    textAlign = TextAlign.Center,
                    fontWeight = FontWeight.W600,
                    color = colorResource(R.color.color_F4A0FF)
                ), modifier = Modifier.fillMaxWidth()
            )
        }
    }
}

@Composable
fun ItemBenefitCompare(benefitCompareBean: BenefitCompareBean, needRadius: Boolean) {
    Row(
        modifier = Modifier
            .padding(horizontal = 16.dp)
            .fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically, horizontalArrangement = Arrangement.Center
    ) {
        Column(
            modifier = Modifier
                .background(
                    color = colorResource(R.color.color_232328),
                    if (needRadius) RoundedCornerShape(bottomStart = 14.dp) else RoundedCornerShape(
                        0.dp
                    )
                )
                .weight(2f)
                .height(if (needRadius) 42.dp else 30.dp)
                .padding(horizontal = 16.dp)
                .then(if (needRadius) Modifier.padding(bottom = 12.dp) else Modifier.padding(bottom = 0.dp)),
            horizontalAlignment = Alignment.Start
        ) {
            BasicText(
                stringResource(benefitCompareBean.benefit),
                style = TextStyle(
                    fontSize = 12.sp,
                    color = if (benefitCompareBean.enable) Color(0xffFBBD8A) else Color(0xffACACAE),
                    textAlign = TextAlign.Start
                ),
                autoSize = TextAutoSize.StepBased(minFontSize = 2.sp, maxFontSize = 12.sp),
                maxLines = 2, modifier = Modifier.fillMaxWidth()
            )
        }
        Column(
            modifier = Modifier
                .background(
                    color = colorResource(R.color.color_303035),
                )
                .weight(1f)
                .height(if (needRadius) 42.dp else 30.dp)
                .then(if (needRadius) Modifier.padding(bottom = 12.dp) else Modifier.padding(bottom = 0.dp)),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = benefitCompareBean.premium,
                style = TextStyle(fontSize = 14.sp, fontWeight = FontWeight.W600, color = White)
            )
        }
        Column(
            modifier = Modifier
                .background(
                    color = colorResource(R.color.color_392A55),
                    if (needRadius) RoundedCornerShape(bottomEnd = 14.dp) else RoundedCornerShape(
                        0.dp
                    )
                )
                .weight(1f)
                .height(if (needRadius) 42.dp else 30.dp)
                .then(if (needRadius) Modifier.padding(bottom = 12.dp) else Modifier.padding(bottom = 0.dp)),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            if (benefitCompareBean.isText) {
                Text(
                    text = stringResource(benefitCompareBean.free),
                    style = TextStyle(
                        fontSize = 14.sp,
                        fontWeight = FontWeight.W600,
                        color = Color(0xffEADEFF)
                    )
                )
            } else {
                Image(
                    painterResource(benefitCompareBean.free),
                    contentDescription = "icon",
                    modifier = Modifier.size(16.dp)
                )
            }

        }
    }
}


@Composable
fun userPrivacy(): AnnotatedString {
    val privacy = stringResource(R.string.policy)
    val terms = stringResource(R.string.terms)
    val eula = stringResource(R.string.eula)
    val mainStr = stringResource(R.string.buy_privacy_terms_eula)
    val color = colorResource(R.color.colorTextAccent)
    return remember {
        mainStr.toAnnotatedStringParameters(
            ChildDesign(
                childString = privacy,
                annotatedTag = TAG_PRIVACY,
                spanStyle = SpanStyle(
                    color = color,
                    fontWeight = FontWeight.W600
                )
            ),
            ChildDesign(
                childString = terms,
                annotatedTag = TAG_TERMS,
                spanStyle = SpanStyle(
                    color = color,
                    fontWeight = FontWeight.W600,
                )
            ),
            ChildDesign(
                childString = eula,
                annotatedTag = TAG_EULA,
                spanStyle = SpanStyle(
                    color = color,
                    fontWeight = FontWeight.W600,
                )
            )
        )
    }
}