package com.deeptalkie.main.compose.ui.dialog

import android.os.Build
import android.view.WindowManager
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.DialogProperties
import com.deeptalkie.main.R
import com.deeptalkie.main.compose.effect.ResumeEffect
import com.deeptalkie.main.compose.theme.White10
import com.deeptalkie.main.compose.ui.components.DTTextFieldNoBorder

@Composable
fun BottomInputDialog(
    onDismiss: () -> Unit,
    onSubmit: (text: String) -> Unit
) {
    var text by remember { mutableStateOf("") }
    DTDialog(
        onDismiss = onDismiss,
        DialogProperties(
            usePlatformDefaultWidth = false,
            decorFitsSystemWindows = false
        )
    ) { window ->
        ResumeEffect(
            window,
            onResume = {
                window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN)
            },
            onPause = {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                    window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_NOTHING)
                } else {
                    window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)
                }
            }
        )
        Box(Modifier.fillMaxSize()) {
            Row(
                Modifier
                    .align(Alignment.BottomCenter)
                    .clip(RoundedCornerShape(16.dp, 16.dp))
                    .background(Color(0xFF28282D))
                    .navigationBarsPadding()
                    .imePadding()
                    .fillMaxWidth()
                    .height(76.dp)
                    .padding(horizontal = 16.dp),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                DTTextFieldNoBorder(
                    value = text,
                    onValueChange = {
                        text = it
                    },
                    Modifier
                        .weight(1f)
                        .height(44.dp)
                        .background(White10, RoundedCornerShape(12.dp))
                )
                Icon(
                    painterResource(R.drawable.ic_bottom_input_complete),
                    null,
                    Modifier
                        .size(44.dp)
                        .clip(CircleShape)
                        .clickable(enabled = text.isNotBlank()) {
                            onSubmit(text)
                        },
                    Color.Unspecified
                )
            }
        }
    }
}