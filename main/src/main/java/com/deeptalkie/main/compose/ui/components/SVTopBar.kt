package com.deeptalkie.main.compose.ui.components

import androidx.annotation.StringRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.deeptalkie.main.R

@Composable
fun SVTopBar(
    @StringRes strId: Int? = null,
    onBack: () -> Unit
) {
    Box(
        Modifier
            .fillMaxWidth()
            .height(56.dp)
    ) {
        IconButton(
            onClick = onBack,
            Modifier
                .align(Alignment.CenterStart)
                .padding(start = 7.dp)
                .size(48.dp)
        ) {
            Image(painterResource(R.drawable.ic_top_bar_back), null, Modifier.size(30.dp))
        }
        Text(
            if (strId == null) "" else stringResource(strId),
            Modifier
                .padding(horizontal = 62.dp)
                .fillMaxWidth()
                .align(Alignment.Center),
            style = MaterialTheme.typography.headlineMedium.copy(
                color = MaterialTheme.colorScheme.onPrimary,
                textAlign = TextAlign.Center
            )
        )
    }
}