package com.deeptalkie.main.compose.navigation

import androidx.activity.compose.BackHandler
import androidx.compose.runtime.Composable
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavHostController
import androidx.navigation.NavType
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.rememberNavController
import androidx.navigation.toRoute
import androidx.savedstate.SavedState
import com.deeptalkie.kidsguard.net.util.toJsonString
import com.deeptalkie.main.bean.BindAccountBean
import com.deeptalkie.main.compose.ui.page.about.AboutMePage
import com.deeptalkie.main.compose.ui.page.assets.MineAssetRoute
import com.deeptalkie.main.compose.ui.page.chat.ChatPage
import com.deeptalkie.main.compose.ui.page.feedback.FeedbackPage
import com.deeptalkie.main.compose.ui.page.main.MainPage
import com.deeptalkie.main.compose.ui.page.main.createrole.AIGenerationImagePage
import com.deeptalkie.main.compose.ui.page.main.createrole.AIGenerationImageViewModel
import com.deeptalkie.main.compose.ui.page.main.createrole.AIImageResultPage
import com.deeptalkie.main.compose.ui.page.main.createrole.AIImageResultViewModel
import com.deeptalkie.main.compose.ui.page.main.createrole.AI_IMAGE_RESULT_KEY
import com.deeptalkie.main.compose.ui.page.main.createrole.CreateAIRolePage
import com.deeptalkie.main.compose.ui.page.main.createrole.CreateAIRoleViewModel
import com.deeptalkie.main.compose.ui.page.main.createrole.SELECT_VOICE_KEY
import com.deeptalkie.main.compose.ui.page.main.createrole.SelectVoicePage
import com.deeptalkie.main.compose.ui.page.main.createrole.SelectVoiceViewModel
import com.deeptalkie.main.compose.ui.page.main.language.LanguagePage
import com.deeptalkie.main.compose.ui.page.main.mine.BindAnotherEmailRoute
import com.deeptalkie.main.compose.ui.page.main.mine.BindEmailRoute
import com.deeptalkie.main.compose.ui.page.main.mine.ChangeUserInfoRoute
import com.deeptalkie.main.compose.ui.page.main.mine.CheckExistAccountPwdRoute
import com.deeptalkie.main.compose.ui.page.main.mine.CreateAccountRoute
import com.deeptalkie.main.compose.ui.page.main.mine.ForgetPasswordPage
import com.deeptalkie.main.compose.ui.page.main.mine.LoginRoute
import com.deeptalkie.main.compose.ui.page.main.mine.MineRoute
import com.deeptalkie.main.compose.ui.page.main.mine.SignUpRoute
import com.deeptalkie.main.compose.ui.page.main.mine.UserInfoRoute
import com.deeptalkie.main.compose.ui.page.main.report.ReportPage
import com.deeptalkie.main.compose.ui.page.main.video.VideoPlayPage
import com.deeptalkie.main.compose.ui.page.main.zoom.ZoomImagePage
import com.deeptalkie.main.compose.ui.page.order.OrderPage
import com.deeptalkie.main.compose.ui.page.product.ProductRoute
import com.deeptalkie.main.compose.ui.page.product.PurchaseSuccessRoute
import com.deeptalkie.main.compose.ui.page.roledetail.RoleDetailScreen
import com.deeptalkie.main.config.ReportEventUtils
import com.deeptalkie.main.config.UmConstant
import com.deeptalkie.main.ext.viewModelFactory
import com.imyfone.membership.api.bean.ConfirmResultBean
import kotlinx.serialization.json.Json
import kotlin.reflect.typeOf

@Composable
fun MainNavigation(navController: NavHostController = rememberNavController()) {
    NavHost(navController, MainRoute.Main) {
        animComposable<MainRoute.Main> { backStackEntry ->
            MainPage { route ->
                navController.navigate(route) {
                    if (route is MainRoute.RoleDetail) {
                        launchSingleTop = true
                    }
                }
            }
        }
        animComposable<MainRoute.Chat> { backStackEntry ->
            ChatPage(onBack = { navController.navigateUp() }) { route ->
                navController.navigate(route)
            }
        }
        animComposable<MainRoute.ZoomImage> { backStackEntry ->
            ZoomImagePage(onBack = { navController.navigateUp() }, backStackEntry.toRoute())
        }
        animComposable<MainRoute.VideoPlay> { backStackEntry ->
            VideoPlayPage(backStackEntry.toRoute()) {
                navController.navigateUp()
            }
        }
        animComposable<MainRoute.RoleDetail> { backStackEntry ->
            RoleDetailScreen(
                onBack = { navController.navigateUp() },
                onChatClick = { roleId, sessionId ->
                    navController.navigateUp()
                    navController.navigate(MainRoute.Chat(roleId, sessionId)) {
                        launchSingleTop = true
                    }
                },
                onLogin = { navController.navigate(MainRoute.Login) },
                onBuy = { navController.navigate(MainRoute.Product) },
                onReport = {
                    navController.navigate(MainRoute.Report(it))
                }
            )
        }
        animComposable<MainRoute.Mine> {
            MineRoute(
                onEnterUserInfo = { navController.navigate(MainRoute.UserInfo) },
                onLogin = { navController.navigate(MainRoute.Login) },
                onSignUp = { navController.navigate(MainRoute.SignUp) },
                onEnterProduct = {
                    ReportEventUtils.onEvent(UmConstant.PURCHASE_PAGE, mapOf("source" to "Mine"))
                    navController.navigate(MainRoute.Product)
                },
                onNavigate = { navController.navigate(it) }
            )
        }
        animComposable<MainRoute.Login> {
            BackHandler {
                navController.popBackStack<MainRoute.Main>(inclusive = false)
            }
            LoginRoute(
                onBack = {
                    navController.popBackStack<MainRoute.Main>(inclusive = false)
                },
                onSignUp = {
                    navController.navigate(MainRoute.SignUp)
                },
                onLoginSuccess = { navController.popBackStack<MainRoute.Main>(inclusive = false) },
                onForgetPwd = { navController.navigate(MainRoute.ForgetPassword) },
                onBindEmail = { info ->
                    navController.navigate(MainRoute.BindEmail(data = info))
                },
            )
        }
        animComposable<MainRoute.SignUp> {
            BackHandler {
                navController.popBackStack<MainRoute.Main>(inclusive = false)
            }
            SignUpRoute(
                onBack = {
                    navController.popBackStack<MainRoute.Main>(inclusive = false)
                },
                onLogin = { navController.navigate(MainRoute.Login) },
                onSignUpSuccess = {
                    navController.popBackStack<MainRoute.Main>(inclusive = false)
                }
            )
        }
        animComposable<MainRoute.Product> {
            ProductRoute(
                onGooglePurchaseSuccess = { email: String, bean: ConfirmResultBean? ->
                    navController.navigate(
                        MainRoute.GooglePaySuccess(
                            email = email,
                            bean = bean.toJsonString()
                        )
                    )
                },
                onBack = { navController.navigateUp() },
                onLogin = { navController.navigate(MainRoute.Login) },
            )
        }
        animComposable<MainRoute.Language> {
            LanguagePage(onBack = { navController.navigateUp() })
        }
        animComposable<MainRoute.MyOrder> {
            OrderPage(onBack = { navController.navigateUp() })
        }
        animComposable<MainRoute.GooglePaySuccess> { backStackEntry ->
            val paySuccess = backStackEntry.toRoute<MainRoute.GooglePaySuccess>()
            PurchaseSuccessRoute(paySuccess.email, paySuccess.bean) {
                navController.navigateUp()
            }
        }
        animComposable<MainRoute.AboutMe> {
            AboutMePage(onBack = { navController.navigateUp() })
        }
        animComposable<MainRoute.Feedback> {
            FeedbackPage(onBack = { navController.navigateUp() })
        }
        animComposable<MainRoute.BindEmail>(typeMap = mapOf(typeOf<BindAccountBean>() to bindAccountBean)) { backStackEntry ->
            val bean = backStackEntry.toRoute<MainRoute.BindEmail>()
            BindEmailRoute(
                info = bean.data,
                onBack = { navController.popBackStack() },
                onNeedCreateAccount = {
                    navController.navigate(MainRoute.CreateAccount(it))
                },
                onCheckPassword = {
                    navController.navigate(MainRoute.CheckPassword(it))
                },
                onBindAnotherAccount = {
                    navController.navigate(MainRoute.BindAnotherEmail(data = it))
                })
        }
        animComposable<MainRoute.CheckPassword>(typeMap = mapOf(typeOf<BindAccountBean>() to bindAccountBean)) { backStackEntry ->
            val bean = backStackEntry.toRoute<MainRoute.CheckPassword>()
            CheckExistAccountPwdRoute(
                info = bean.data,
                onBindSuccess = {
                    navController.popBackStack<MainRoute.Main>(inclusive = false)
                },
                onBindEmail = {
                    navController.navigate(MainRoute.BindEmail(it))
                },
                onBack = {
                    navController.popBackStack()
                })

        }
        animComposable<MainRoute.BindAnotherEmail>(typeMap = mapOf(typeOf<BindAccountBean>() to bindAccountBean)) { backStackEntry ->
            val bean = backStackEntry.toRoute<MainRoute.BindAnotherEmail>()
            BindAnotherEmailRoute(
                info = bean.data,
                onBindEmail = {
                    navController.navigate(MainRoute.BindAnotherEmail(it))
                },
                onBack = {
                    navController.popBackStack()
                })
        }
        animComposable<MainRoute.Report> { backStackEntry ->
            ReportPage(onBack = { navController.navigateUp() })
        }
        animComposable<MainRoute.CreateAccount>(typeMap = mapOf(typeOf<BindAccountBean>() to bindAccountBean)) { backStackEntry ->
            val bean = backStackEntry.toRoute<MainRoute.CreateAccount>()
            CreateAccountRoute(
                info = bean.data,
                onBindAnotherAccount = {
                    navController.navigate(MainRoute.BindEmail(it))
                },
                onBindEmail = { navController.navigate(MainRoute.BindEmail(bean.data)) },
                onCreateAccountSuccess = {
                    navController.popBackStack<MainRoute.Main>(inclusive = false)
                }, onBack = {
                    navController.popBackStack()
                })
        }
        animComposable<MainRoute.UserInfo> {
            UserInfoRoute(
                onBack = { navController.popBackStack() },
                onChangeFirstName = {
                    navController.navigate(MainRoute.ChangeAccountInfo(it))
                },
                onChangeLastName = {
                    navController.navigate(MainRoute.ChangeAccountInfo(it))
                })
        }
        animComposable<MainRoute.ChangeAccountInfo> { backStackEntry ->
            val bean = backStackEntry.toRoute<MainRoute.ChangeAccountInfo>()
            ChangeUserInfoRoute(bean.type) {
                navController.popBackStack()
            }
        }
        animComposable<MainRoute.ForgetPassword> {
            ForgetPasswordPage(onBack = { navController.navigateUp() })
        }
        animComposable<MainRoute.CreateAIRole> { backStackEntry ->
            CreateAIRolePage(
                onBack = { navController.navigateUp() },
                navigate = { navController.navigate(it) },
                onCreateComplete = { roleId ->
                    navController.navigate(MainRoute.RoleDetail(roleId)) {
                        popUpTo<MainRoute.CreateAIRole> {
                            inclusive = true
                        }
                    }
                },
                viewModel = viewModel(
                    factory = viewModelFactory {
                        CreateAIRoleViewModel(backStackEntry.savedStateHandle)
                    }
                )
            )
        }
        animComposable<MainRoute.SelectVoice> { backStackEntry ->
            SelectVoicePage(
                onBack = { navController.navigateUp() },
                onConfirm = { voice ->
                    navController.previousBackStackEntry
                        ?.savedStateHandle
                        ?.set(SELECT_VOICE_KEY, voice)
                    navController.navigateUp()
                },
                viewModel = viewModel(
                    factory = viewModelFactory {
                        SelectVoiceViewModel(backStackEntry.savedStateHandle)
                    }
                )
            )
        }
        animComposable<MainRoute.MyAssets> {
            MineAssetRoute(
                onClickRole = {
                    navController.navigate(MainRoute.RoleDetail(it)) {
                        launchSingleTop = true
                    }
                },
                onBack = { navController.navigateUp() },
                navigate = { navController.navigate(it) }
            )
        }
        animComposable<MainRoute.AIGenerationImage> { backStackEntry ->
            AIGenerationImagePage(
                onBack = { navController.navigateUp() },
                onGotoPay = {
                    navController.navigate(MainRoute.Product)
                },
                onGenerate = { prompt, aiRoleSex, stylePrompt ->
                    navController.navigate(
                        MainRoute.AIGenerationImageResult(prompt, aiRoleSex, stylePrompt)
                    ) {
                        launchSingleTop = true
                    }
                },
                viewModel = viewModel(
                    factory = viewModelFactory {
                        AIGenerationImageViewModel(backStackEntry.savedStateHandle)
                    }
                )
            )
        }
        animComposable<MainRoute.AIGenerationImageResult> { backStackEntry ->
            AIImageResultPage(
                onBack = { navController.navigateUp() },
                onConfirm = { aiRoleSex, image ->
                    navController.navigate(MainRoute.CreateAIRole(aiRoleSex)) {
                        launchSingleTop = true
                        popUpTo(MainRoute.CreateAIRole(aiRoleSex)) {
                            inclusive = false
                        }
                    }
                    navController.currentBackStackEntry
                        ?.savedStateHandle
                        ?.set(AI_IMAGE_RESULT_KEY, image)
                },
                onGotoPay = {
                    navController.navigate(MainRoute.Product)
                },
                viewModel = viewModel(
                    factory = viewModelFactory {
                        AIImageResultViewModel(backStackEntry.savedStateHandle)
                    }
                )
            )
        }
    }
}


internal val bindAccountBean = object : NavType<BindAccountBean>(true) {
    override fun get(bundle: SavedState, key: String): BindAccountBean? {
        val json = bundle.getString(key) ?: return null
        return parseValue(json)
    }

    override fun put(bundle: SavedState, key: String, value: BindAccountBean) {
        bundle.putString(key, serializeAsValue(value))
    }

    override fun parseValue(value: String): BindAccountBean {
        return Json.decodeFromString(value)
    }

    override fun serializeAsValue(value: BindAccountBean): String {
        return Json.encodeToString(value)
    }
}