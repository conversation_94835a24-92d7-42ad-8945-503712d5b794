package com.deeptalkie.main.compose.ui.page.explore

import androidx.compose.runtime.Stable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.deeptalkie.main.R
import com.deeptalkie.main.compose.ui.page.main.createrole.CreateAIRoleRepo
import com.deeptalkie.main.compose.utils.ILoadingState
import com.deeptalkie.main.compose.utils.loadingState
import com.deeptalkie.main.db.result.AIRoleWithTags
import com.deeptalkie.main.db.table.AIRoleTag
import com.deeptalkie.main.utils.getString
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock

enum class ExploreModule {
    Girls, Guys, Anime
}

class ExploreViewModel(
    private val module: ExploreModule,
    private val exploreRepo: ExploreRepo = ExploreRepo(),
    private val createAIRoleRepo: CreateAIRoleRepo = CreateAIRoleRepo()
) : ViewModel(), ILoadingState by loadingState() {
    private val _roleTagsFlow = MutableStateFlow<List<AIRoleTag>>(emptyList())
    val roleTagsFlow: StateFlow<List<AIRoleTag>> = _roleTagsFlow.asStateFlow()

    private val pagingStateMap = mutableMapOf<AIRoleTag, PagingState>()
    private val rolesMap = mutableMapOf<AIRoleTag, MutableStateFlow<List<AIRoleWithTags>>>()

    private val mutex = Mutex()

    private fun getTagPagingState(tag: AIRoleTag): PagingState {
        return pagingStateMap.getOrPut(tag) { PagingState() }
    }

    fun getTagRolesFlow(tag: AIRoleTag): MutableStateFlow<List<AIRoleWithTags>> {
        return rolesMap.getOrPut(tag) { MutableStateFlow(emptyList()) }
    }

    fun refreshTags(index: Int) {
        viewModelScope.launch {
            if (loading) return@launch
            showLoading(true)
            val tags = createAIRoleRepo.loadAllTagsToDb(module.name)
            showLoading(false)
            if (tags.isNullOrEmpty()) {
                return@launch
            }
            _roleTagsFlow.value = buildList {
                add(AIRoleTag(0, getString(R.string.explore_page_all_tags), 1, "", ""))
                addAll(tags)
            }
            val tag = roleTagsFlow.value.getOrNull(index) ?: roleTagsFlow.value.first()
            refresh(tag)
        }
    }

    fun refresh(tag: AIRoleTag) {
        viewModelScope.launch {
            mutex.withLock {
                showLoading(true)
                refreshRoles(tag)
                showLoading(false)
            }
        }
    }

    private suspend fun refreshRoles(tag: AIRoleTag) {
        val pagingState = getTagPagingState(tag)
        if (pagingState.refreshing) return
        pagingState.refreshing = true
        pagingState.page = 1
        pagingState.hasMore = true
        loadRoles(tag)
        pagingState.refreshing = false
    }

    fun canLoadMore(tag: AIRoleTag): Boolean {
        val pagingState = getTagPagingState(tag)
        val tagRoles = getTagRolesFlow(tag).value
        return !loading &&
                pagingState.hasMore &&
                tagRoles.size >= pagingState.pageSize
    }

    fun loadMoreRoles(tag: AIRoleTag) {
        viewModelScope.launch {
            val pagingState = getTagPagingState(tag)
            if (pagingState.refreshing || pagingState.loadingMore) return@launch
            pagingState.loadingMore = true
            pagingState.page++
            loadRoles(tag)
            pagingState.loadingMore = false
        }
    }

    private suspend fun loadRoles(tag: AIRoleTag) {
        val pagingState = getTagPagingState(tag)
        val tagRolesFlow = getTagRolesFlow(tag)
        exploreRepo.loadAIRolesForTag(
            tag.id,
            module.name,
            pagingState.page,
            pagingState.pageSize
        )?.let { roles ->
            if (pagingState.page == 1) {
                tagRolesFlow.value = roles
            } else {
                tagRolesFlow.value += roles
            }
            pagingState.hasMore = roles.size >= pagingState.pageSize
        }
    }
}

@Stable
class PagingState {
    var page: Int = 1
    var pageSize: Int = 8
    var refreshing = false
    var hasMore by mutableStateOf(true)
    var loadingMore by mutableStateOf(false)
}