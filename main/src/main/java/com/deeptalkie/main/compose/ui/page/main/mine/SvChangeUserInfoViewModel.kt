package com.deeptalkie.main.compose.ui.page.main.mine

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.deeptalkie.main.Membership
import com.mfccgroup.android.httpclient.adapter.API
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 *creater:linjinhao on 2025/5/28 20:10
 */
class SvChangeUserInfoViewModel : ViewModel() {
    private val account = Membership.membershipClient.account

    private val _state = MutableStateFlow(false)
    val state = _state.asStateFlow()

    private val _event = MutableSharedFlow<ChangeValueEvent>()
    val event = _event.asSharedFlow()

    val member = account.memberFlow

    fun changeFirstName(value: String) {
        changeValue(value, account::updateFirstName)
    }

    fun changeLastName(value: String) {
        changeValue(value, account::updateLastName)
    }


    private fun changeValue(value: String, block: suspend (String) -> API<String>) {
        viewModelScope.launch {
            _state.emit(true)
            val response = block(value)
            _state.emit(false)
            if (response.isSuccess) {
                _event.emit(ChangeValueEvent.ChangeSuccess)
            } else {
                _event.emit(ChangeValueEvent.ChangeFail)
            }
        }
    }

}

sealed interface ChangeValueEvent {
    object ChangeSuccess : ChangeValueEvent
    object ChangeFail : ChangeValueEvent
}
