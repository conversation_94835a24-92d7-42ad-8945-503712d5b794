package com.deeptalkie.main.compose.ui.page.main.mine

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.deeptalkie.kidsguard.net.util.EncryptedUtil
import com.deeptalkie.main.Membership
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 *creater:linjinhao on 2025/5/26 17:04
 */
class SvCheckExistAccountPwdViewModel : ViewModel() {
    private val account = Membership.membershipClient.account

    private val _state = MutableStateFlow(false)
    val state = _state.asStateFlow()

    private val _checkPasswordEvent = MutableSharedFlow<CheckPasswordEvent>()
    val checkPasswordEvent = _checkPasswordEvent.asSharedFlow()

    fun checkAccountPassword(email: String, password: String) {
        viewModelScope.launch {
            _state.emit(true)
            val pwdStr = EncryptedUtil.md5(password)
            val response = account.checkAccountPassword(email = email, password = pwdStr)
            _state.emit(false)
            if (response.isSuccess) {
                Membership.loginSuccess()
                _checkPasswordEvent.emit(CheckPasswordEvent.Success)
            } else {
                when (response.code) {
                    209, 409 -> {
                        //密码错误
                        _checkPasswordEvent.emit(CheckPasswordEvent.PasswordError)
                    }

                    415 -> {
                        //注册密码格式错误
                        _checkPasswordEvent.emit(CheckPasswordEvent.PasswordFormatError)
                    }

                    414 -> {
                        //注册密码长度必须在6-16字符
                        _checkPasswordEvent.emit(CheckPasswordEvent.PasswordLengthError)
                    }

                    else -> { //普通错误
                        _checkPasswordEvent.emit(CheckPasswordEvent.CommonError)
                    }
                }

            }
        }
    }
}

sealed interface CheckPasswordEvent {
    data object Success : CheckPasswordEvent
    data object PasswordError : CheckPasswordEvent
    data object PasswordFormatError : CheckPasswordEvent
    data object PasswordLengthError : CheckPasswordEvent
    data object CommonError : CheckPasswordEvent
}