package com.deeptalkie.main.compose.ui.page.chat

import android.app.Activity
import androidx.activity.compose.LocalActivity
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalClipboardManager
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import coil3.compose.AsyncImage
import com.deeptalkie.main.Membership
import com.deeptalkie.main.compose.ui.components.DTHorizontalSpacer
import com.deeptalkie.main.compose.ui.components.ThreeDotsLoading
import com.deeptalkie.main.db.result.MsgWithReply
import com.deeptalkie.main.db.table.AIRole
import com.deeptalkie.main.db.table.MsgType
import com.deeptalkie.main.ext.getVideoSize
import com.deeptalkie.main.ext.imageRequest
import com.deeptalkie.main.view.RequirePurchaseDialog
import kotlinx.coroutines.launch

@Composable
fun MsgView(
    aiRole: AIRole?,
    msgWithReply: MsgWithReply,
    showTalkSuggestion: Boolean,
    onAvatarClick: () -> Unit,
    gotoBuy: () -> Unit,
    onZoomImage: () -> Unit,
    onPlayVideo: (width: Int, height: Int) -> Unit,
    focusRequester: FocusRequester,
    viewModel: ChatViewModel = viewModel(),
) {
    val context = LocalContext.current
    val activity = LocalActivity.current
    val clipboardManager = LocalClipboardManager.current
    val keyboardController = LocalSoftwareKeyboardController.current
    val scope = rememberCoroutineScope()

    val msgRecord = msgWithReply.msg

    val showMsgSelectedPopup = remember(msgRecord) {
        {
            viewModel.isShowMsgSelectedPopup(msgRecord)
        }
    }
    val onMsgLongClick = remember(keyboardController, msgRecord) {
        {
            keyboardController?.hide()
            viewModel.showLongPressedMsgPopup(msgRecord)
        }
    }
    val onMsgPopupClick = remember(clipboardManager, msgRecord) {
        { type: MsgPopupOptionType ->
            when (type) {
                MsgPopupOptionType.Copy -> {
                    clipboardManager.setText(AnnotatedString(msgRecord.content))
                }

                MsgPopupOptionType.Reply -> {
                    viewModel.onReplyMsg()
                    focusRequester.requestFocus()
                    keyboardController?.show()
                }

                MsgPopupOptionType.Delete -> {
                    viewModel.onDeleteMsg(msgRecord)
                }
            }
            viewModel.showLongPressedMsgPopup(null)
        }
    }
    val onMsgPopupDismiss = remember {
        {
            viewModel.showLongPressedMsgPopup(null)
        }
    }

    if (msgRecord.fromSelf) {
        MyMsgView(
            aiRole = aiRole,
            msgWithReply = msgWithReply,
            showMsgSelectedPopup = showMsgSelectedPopup(),
            onClick = {},
            onLongClick = onMsgLongClick,
            onMsgPopupDismiss = onMsgPopupDismiss,
            onMsgPopupClick = onMsgPopupClick,
            onRetrySend = {
                viewModel.onRetrySendMsg(msgRecord)
            }
        )
    } else {
        RoleMsgView(
            aiRole = aiRole,
            msgRecord = msgRecord,
            showTalkSuggestion = showTalkSuggestion,
            ttsPlaying = viewModel.isTTSPlaying(msgRecord),
            showMsgSelectedPopup = showMsgSelectedPopup(),
            showDeletePopup = viewModel.isShowDeletePopup(msgRecord),
            onAvatarClick = onAvatarClick,
            onTTSClick = click@{
                viewModel.text2Audio(context, msgRecord, gotoBuy)
            },
            onMsgClick = click@{
                keyboardController?.hide()
                viewModel.foldAiReply()
                when (msgRecord.msgType) {
                    MsgType.Text -> {}
                    MsgType.Image -> {
                        if (!Membership.isVip()) {
                            if (activity == null) return@click
                            showBuyDialog(activity, RequirePurchaseDialog.TYPE_PICTURE, gotoBuy)
                            return@click
                        }
                        onZoomImage()
                    }

                    MsgType.Video -> {
                        scope.launch {
                            if (!Membership.isVip()) {
                                if (activity == null) return@launch
                                showBuyDialog(activity, RequirePurchaseDialog.TYPE_VIDEO, gotoBuy)
                                return@launch
                            }
                            val (w, h) = msgRecord.content.getVideoSize()
                            onPlayVideo(w, h)
                        }
                    }

                    MsgType.Voice -> {}
                    null -> {}
                }
            },
            onSuggestionClick = { suggestion ->
                viewModel.onTextInput(suggestion)
                viewModel.sendTextMsg(context, gotoBuy = gotoBuy)
            },
            onMsgLongClick = onMsgLongClick,
            onMsgMoreClick = {
                viewModel.showDeleteVideoPop(msgRecord)
            },
            onRegenerateImage = {
                viewModel.sendRequestPhotoMsg(context, msgRecord.prompt, gotoBuy)
            },
            onMsgPopupDismiss = onMsgPopupDismiss,
            onMsgPopupClick = onMsgPopupClick,
            onDeletePopupDismiss = {
                viewModel.showDeleteVideoPop(null)
            },
            onDelete = {
                viewModel.onDeleteMsg(msgRecord)
            },
        )
    }
}

@Composable
fun WaitingMessage(avatar: String?) {
    Row(Modifier.padding(bottom = 16.dp), verticalAlignment = Alignment.Bottom) {
        AsyncImage(
            avatar?.imageRequest(LocalContext.current),
            null,
            Modifier
                .padding(start = 16.dp)
                .size(32.dp)
                .clip(CircleShape),
            contentScale = ContentScale.Crop
        )
        DTHorizontalSpacer(8.dp)
        ThreeDotsLoading(
            Modifier
                .size(61.dp, 33.dp)
                .clip(RoundedCornerShape(12.dp, 12.dp, 12.dp, 2.dp))
                .background(
                    Brush.verticalGradient(
                        listOf(
                            Color(0xFF2D284A),
                            Color(0xFF6B5FB0),
                        )
                    )
                )
        )
    }
}

fun showBuyDialog(context: Activity, type: Int, gotoBuy: () -> Unit) {
    RequirePurchaseDialog(
        context,
        type,
        gotoBuy = gotoBuy
    ).show()
}

@Composable
fun String.toStyledAnnotatedString(): AnnotatedString {
    val originalText = this
    return buildAnnotatedString {
        var currentIndex = 0
        val regex = "(\\(.*?\\)|（[^）]*）)".toRegex() // 正则表达式匹配所有形如 (XXX) 的内容，包括括号

        regex.findAll(originalText).forEach { matchResult ->
            // 添加非括号部分
            append(originalText.substring(currentIndex, matchResult.range.first))

            // 添加括号内带样式的部分
            withStyle(
                style = SpanStyle(
                    color = MaterialTheme.colorScheme.onTertiary,
                    fontStyle = FontStyle.Italic
                )
            ) {
                append(matchResult.value)
            }
            currentIndex = matchResult.range.last + 1
        }
        // 添加文本末尾剩余的部分（如果存在）
        if (currentIndex < originalText.length) {
            append(originalText.substring(currentIndex))
        }
    }
}
