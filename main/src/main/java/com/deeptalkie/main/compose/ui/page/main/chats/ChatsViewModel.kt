package com.deeptalkie.main.compose.ui.page.main.chats

import android.content.Context
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.clevguard.utils.ext.logv
import com.deeptalkie.main.Membership
import com.deeptalkie.main.compose.utils.ILoadingState
import com.deeptalkie.main.compose.utils.loadingState
import com.deeptalkie.main.db.result.AIRoleSessionInfo
import com.deeptalkie.main.ext.stateInViewModel
import com.deeptalkie.main.ext.stateInViewModelDefault
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch

class ChatsViewModel : ViewModel(), ILoadingState by loadingState() {
    private val chatsRepo = ChatsRepo()

    var chatsStateFlow: StateFlow<List<AIRoleSessionInfo>> = MutableStateFlow(emptyList())
        private set
    var longClickingChat by mutableStateOf<AIRoleSessionInfo?>(null)
        private set
    var deletingChat by mutableStateOf<AIRoleSessionInfo?>(null)
        private set

    init {
        viewModelScope.launch {
            Membership.userStateFlow.collect {
                collectSessions()
            }
        }
    }

    private fun collectSessions() {
        viewModelScope.launch {
            val userId = Membership.getUserId()

            chatsStateFlow = chatsRepo.queryAllSession(userId).stateInViewModelDefault(listOf())

            chatsStateFlow.map {
                it.map { (userAIRole, _, _) -> userAIRole.sessionId }
                    .sortedBy { sessionId -> sessionId }
            }.stateInViewModel().collect { sessions ->
                logv("预加载消息记录：$sessions")
                chatsRepo.preloadMsgRecord(chatsStateFlow.value)
            }
        }
    }

    fun requestChats() {
        viewModelScope.launch {
            val userId = Membership.getUserId() ?: return@launch
            chatsRepo.requestSessionList(userId)
        }
    }

    suspend fun randomChat(
        context: Context,
        gotoLogin: () -> Unit,
        gotoBuy: () -> Unit
    ): Pair<Long, Long>? {
        return chatsRepo.randomChat(context, gotoLogin, gotoBuy)
    }

    fun showChatDialog(chat: AIRoleSessionInfo?) {
        longClickingChat = chat
    }

    fun showDelChatDialog(chat: AIRoleSessionInfo?) {
        deletingChat = chat
    }

    fun deleteSession(roleId: Long, sessionId: Long) {
        viewModelScope.launch {
            val userId = Membership.getUserId() ?: return@launch
            chatsRepo.deleteSession(userId, roleId, sessionId)
        }
    }

    fun pipToTop(roleId: Long, sessionId: Long, pipTop: Int) {
        viewModelScope.launch {
            val userId = Membership.getUserId() ?: return@launch
            chatsRepo.pipToTop(userId, roleId, sessionId, pipTop)
        }
    }
}