package com.deeptalkie.main.compose.ui.page.roledetail

import android.content.Context
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.toRoute
import com.deeptalkie.main.Membership
import com.deeptalkie.main.compose.navigation.MainRoute
import com.deeptalkie.main.compose.ui.page.main.chats.ChatsRepo
import com.deeptalkie.main.compose.utils.ILoadingState
import com.deeptalkie.main.compose.utils.loadingState
import com.deeptalkie.main.ext.stateInViewModelDefault
import com.deeptalkie.main.repo.AIRoleRepo
import kotlinx.coroutines.launch

/**
 *creater:linjinhao on 2025/6/18 15:12
 */
class DTRoleDetailViewModel(savedStateHandle: SavedStateHandle) : ViewModel(),
    ILoadingState by loadingState() {
    val route = savedStateHandle.toRoute<MainRoute.RoleDetail>()
    private val aiRoleRepo = AIRoleRepo()
    private val chatsRepo = ChatsRepo()

    val aiRoleWithTagsFlow = aiRoleRepo.getAIRoleWithTagsFlow(route.roleId)
        .stateInViewModelDefault(null)

    val userAIRoleFlow = aiRoleRepo.userAiRoleFlow(Membership.getUserId(), route.roleId)
        .stateInViewModelDefault(null)

    var currentImageIndex by mutableIntStateOf(0)
        private set

    init {
        requestRoleDetails()
    }

    fun requestRoleDetails() {
        viewModelScope.launch {
            if (aiRoleWithTagsFlow.value?.tags.isNullOrEmpty()) {
                // 有缓存就不显示 loading
                showLoading(true)
            }
            aiRoleRepo.loadAIRoleDetails(route.roleId)
            showLoading(false)
        }
    }

    fun getCurrentImage() = aiRoleWithTagsFlow.value?.aiRole?.images?.getOrNull(currentImageIndex)

    fun onImageSelected(index: Int) {
        currentImageIndex = index
    }

    fun favorite() {
        val userId = Membership.getUserId() ?: return
        val userAIRole = userAIRoleFlow.value ?: return
        viewModelScope.launch {
            aiRoleRepo.favorite(userId, userAIRole.roleId, userAIRole.isFavorite == true)
        }
    }

    suspend fun startChat(
        context: Context,
        gotoLogin: () -> Unit,
        gotoBuy: () -> Unit
    ): Long? {
        showLoading(true)
        val sessionId = chatsRepo.requestChat(route.roleId, context, gotoLogin, gotoBuy)
        showLoading(false)
        return sessionId
    }
}