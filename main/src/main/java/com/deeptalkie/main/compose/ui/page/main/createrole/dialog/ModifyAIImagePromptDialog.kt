package com.deeptalkie.main.compose.ui.page.main.createrole.dialog

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.deeptalkie.main.R
import com.deeptalkie.main.compose.theme.Black100
import com.deeptalkie.main.compose.ui.components.DTButton
import com.deeptalkie.main.compose.ui.components.DTTextFieldNoBorder
import com.deeptalkie.main.compose.ui.components.DTVerticalSpacer
import com.deeptalkie.main.compose.ui.dialog.DTDialog

@Composable
fun ModifyAIImagePromptDialog(
    prompt: String,
    onDismiss: () -> Unit,
    onConfirm: (newPrompt: String) -> Unit
) {
    var newPrompt by remember { mutableStateOf(prompt) }
    DTDialog(onDismiss) {
        Column(
            Modifier
                .width(300.dp)
                .background(
                    MaterialTheme.colorScheme.onPrimary,
                    RoundedCornerShape(16.dp)
                ),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            DTVerticalSpacer(20.dp)
            Title()
            DTVerticalSpacer(16.dp)
            ModifyPromptCell(newPrompt) {
                newPrompt = it
            }
            DTVerticalSpacer(20.dp)
            ConfirmBtn(newPrompt.isNotBlank()) {
                onConfirm(newPrompt)
            }
            DTVerticalSpacer(20.dp)
        }
    }
}

@Composable
private fun Title() {
    Text(
        stringResource(R.string.ai_image_result_page_modify_prompt_dialog_title),
        style = MaterialTheme.typography.headlineLarge.copy(
            color = Black100,
            fontSize = 16.sp
        )
    )
}

@Composable
private fun ModifyPromptCell(
    value: String,
    onValueChange: (String) -> Unit
) {
    DTTextFieldNoBorder(
        value = value,
        onValueChange = onValueChange,
        Modifier
            .padding(horizontal = 12.dp)
            .fillMaxWidth()
            .height(178.dp)
            .background(Color(0xFFECECEC), RoundedCornerShape(6.dp)),
        placeholder = stringResource(R.string.ai_image_result_page_modify_prompt_dialog_textfield_placeholder),
        textStyle = MaterialTheme.typography.bodySmall.copy(
            color = Color(0xFF4B4B51),
            lineHeight = 20.sp
        ),
        cursorBrush = SolidColor(Color(0xFF4B4B51)),
        singleLine = false,
        verticalAlignment = Alignment.Top,
        contentPadding = PaddingValues(8.dp),
    )
}

@Composable
private fun ConfirmBtn(enable: Boolean, onClick: () -> Unit) {
    DTButton(
        R.string.ai_image_result_page_modify_prompt_dialog_confirm_btn,
        modifier = Modifier
            .padding(horizontal = 40.dp)
            .fillMaxWidth()
            .height(40.dp),
        enable = enable,
        containerColor = MaterialTheme.colorScheme.primary,
        disabledContainerColor = Color(0xFF636364),
        onClick = onClick
    )
}