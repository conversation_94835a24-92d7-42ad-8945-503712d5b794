package com.deeptalkie.main.compose.navigation

import com.deeptalkie.main.bean.BindAccountBean
import com.deeptalkie.main.compose.ui.dialog.AIRoleSex
import kotlinx.serialization.Serializable

sealed interface MainRoute {
    @Serializable
    data object Main : MainRoute

    @Serializable
    data class Chat(val roleId: Long, val sessionId: Long) : MainRoute

    @Serializable
    data class ZoomImage(val url: String) : MainRoute

    @Serializable
    data class VideoPlay(val url: String, val width: Int, val height: Int) : MainRoute

    @Serializable
    data class RoleDetail(val roleId: Long) : MainRoute

    @Serializable
    data object Login : MainRoute

    @Serializable
    data object SignUp : MainRoute

    @Serializable
    data object UserInfo : MainRoute

    @Serializable
    data object Mine : MainRoute

    @Serializable
    data object ForgetPassword : MainRoute

    @Serializable
    data object Product : MainRoute

    @Serializable
    data object Language : MainRoute

    @Serializable
    data object MyOrder : MainRoute

    @Serializable
    data class GooglePaySuccess(val email: String, val bean: String) :
        MainRoute

    @Serializable
    data object AboutMe : MainRoute

    @Serializable
    data object Feedback : MainRoute

    /* @Serializable
     data object GoogleLogin : MainRoute*/

    @Serializable
    data class BindEmail(val data: BindAccountBean) : MainRoute

    @Serializable
    data class CheckPassword(val data: BindAccountBean) :
        MainRoute

    @Serializable
    data class CreateAccount(val data: BindAccountBean) :
        MainRoute

    @Serializable
    data class BindAnotherEmail(val data: BindAccountBean) :
        MainRoute

    @Serializable
    data class Report(val roleName: String) : MainRoute

    @Serializable
    data class ChangeAccountInfo(val type: String) : MainRoute

    @Serializable
    data class CreateAIRole(val aiRoleSex: AIRoleSex) : MainRoute

    @Serializable
    data class SelectVoice(val voiceJson: String?) : MainRoute

    @Serializable
    data object MyAssets : MainRoute

    @Serializable
    data class AIGenerationImage(val aiRoleSex: AIRoleSex) : MainRoute

    @Serializable
    data class AIGenerationImageResult(
        val prompt: String,
        val aiRoleSex: AIRoleSex,
        val stylePrompt: String
    ) : MainRoute
}