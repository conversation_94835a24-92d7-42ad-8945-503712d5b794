package com.deeptalkie.main.compose.ui.page.main.mine

import android.text.TextUtils
import com.deeptalkie.main.config.EMAIL_REGEX
import com.deeptalkie.main.config.PASSWORD_REGEX

/**
 *creater:<PERSON><PERSON><PERSON><PERSON> on 2025/5/21 20:22
 */

fun checkEmail(email: String): <PERSON><PERSON><PERSON> {
    return when {
        email.isEmpty() -> {
            false
        }

        !isEmailFormat(email) -> {
            false
        }

        else -> {
            true
        }
    }
}

fun isEmailFormat(email: String): <PERSON><PERSON>an {
    return if (TextUtils.isEmpty(email)) false
    else {
        EMAIL_REGEX.matches(email)
    }
}


fun checkAccountPassword(pwd: String): <PERSON><PERSON>an {
    return when {
        pwd.isEmpty() -> {
            false
        }

        !isPwdFormat(pwd) -> {
            false
        }

        !isPwdContainSpecial(pwd) -> {
            false
        }

        else -> {
            true
        }
    }
}


private fun isPwdFormat(pwd: CharSequence): <PERSON><PERSON>an {
    if (pwd.length in 6 until 17) {
        return pwd.contains(" ", true) != true
    }
    return false
}

fun isPwdContainSpecial(pwd: CharSequence): Bo<PERSON>an {
    return PASSWORD_REGEX.matches(pwd)
}