package com.deeptalkie.main.compose.ui.page.main.createrole

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil3.compose.AsyncImage
import com.deeptalkie.main.R
import com.deeptalkie.main.compose.theme.White10
import com.deeptalkie.main.compose.ui.components.AIImageResultLoading
import com.deeptalkie.main.compose.ui.components.DTButton
import com.deeptalkie.main.compose.ui.components.DTPage
import com.deeptalkie.main.compose.ui.components.DTVerticalSpacer
import com.deeptalkie.main.compose.ui.components.ThreeDotsLoading
import com.deeptalkie.main.compose.ui.dialog.AIRoleSex
import com.deeptalkie.main.compose.ui.dialog.DTSingleBtnWarnDialog
import com.deeptalkie.main.compose.ui.dialog.DTWarnDialog
import com.deeptalkie.main.compose.ui.page.main.createrole.dialog.AIImageGenFailDialog
import com.deeptalkie.main.compose.ui.page.main.createrole.dialog.ModifyAIImagePromptDialog

const val AI_IMAGE_RESULT_KEY = "AI_IMAGE_RESULT_KEY"

@Composable
fun AIImageResultPage(
    onBack: () -> Unit,
    onConfirm: (aiRoleSex: AIRoleSex, String) -> Unit,
    onGotoPay: () -> Unit,
    viewModel: AIImageResultViewModel
) {
    val backHandler = remember {
        {
            viewModel.showReminderDialog(true)
        }
    }

    BackHandler {
        backHandler()
    }

    val imageResult = viewModel.imageResult
    DTPage {
        if (imageResult !is ImageResult.Success) {
            LoadingPage(backHandler, viewModel)
        } else {
            SuccessPage(backHandler, onConfirm, viewModel)
        }
    }

    if (viewModel.showFailDialog) {
        AIImageGenFailDialog(
            onDismiss = {
                viewModel.showFailDialog(false)
                onBack()
            },
            onRetry = {
                viewModel.onRetry()
            }
        )
    } else if (viewModel.showReminderDialog) {
        DTWarnDialog(
            title = R.string.reminder,
            content = if (imageResult is ImageResult.Success)
                R.string.ai_image_result_page_sussess_gen_image_reminder_dialog_tips
            else
                R.string.ai_image_result_page_reminder_dialog_tips,
            cancelText = R.string.cancel,
            confirmText = R.string.exit,
            onCancel = {
                viewModel.showReminderDialog(false)
            },
            onConfirm = {
                viewModel.showReminderDialog(false)
                onBack()
            }
        )
    }
    if (viewModel.showEditPromptDialog) {
        ModifyAIImagePromptDialog(
            viewModel.prompt,
            onDismiss = {
                viewModel.showEditPromptDialog(false)
            },
            onConfirm = viewModel::onEditPrompt
        )
    }
    if (viewModel.showCoinNotEnoughDialog) {
        DTSingleBtnWarnDialog(
            title = R.string.reminder,
            content = R.string.ai_generation_image_page_coin_not_enough_dialog_tips,
            confirmText = R.string.check,
            icon = R.drawable.ic_coin_not_enough,
            onCancel = {
                viewModel.showCoinNotEnoughDialog(false)
            },
            onConfirm = {
                viewModel.showCoinNotEnoughDialog(false)
                onGotoPay()
            }
        )
    }
}

@Composable
private fun LoadingPage(onBack: () -> Unit, viewModel: AIImageResultViewModel) {
    Column(
        Modifier
            .fillMaxSize()
            .background(
                Brush.verticalGradient(
                    listOf(Color(0xFF0C0122), Color(0xFF050117))
                )
            )
            .statusBarsPadding(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        TitleBar(onBack)
        DTVerticalSpacer(180.dp)
        AIImageResultLoading(Modifier.size(100.dp))
        DTVerticalSpacer(30.dp)
        LoadingProgressText(viewModel.imageResult)
        Spacer(Modifier.weight(1f))
        ThreeDotsLoading(
            Modifier
                .size(60.dp, 80.dp)
                .border(2.dp, MaterialTheme.colorScheme.primary, RoundedCornerShape(5.dp))
                .background(White10, RoundedCornerShape(5.dp))
                .padding(10.dp)
        )
        DTVerticalSpacer(30.dp)
        ConfirmBtn(false, onClick = {})
    }
}

@Composable
private fun SuccessPage(
    onBack: () -> Unit,
    onConfirm: (AIRoleSex, String) -> Unit,
    viewModel: AIImageResultViewModel
) {
    val imageResult = viewModel.imageResult as ImageResult.Success
    Box(Modifier.fillMaxSize()) {
        ImagePreview(imageResult.images[viewModel.selectedImageIndex])
        Column(
            Modifier
                .fillMaxSize()
                .statusBarsPadding(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            TitleBar(
                onBack = onBack,
                showEdit = true,
                onEdit = {
                    viewModel.showEditPromptDialog(true)
                }
            )
            Spacer(Modifier.weight(1f))
            ImageList(imageResult.images, viewModel.selectedImageIndex, viewModel::onImageSelected)
            DTVerticalSpacer(30.dp)
            ConfirmBtn(true) {
                onConfirm(
                    viewModel.route.aiRoleSex,
                    imageResult.images[viewModel.selectedImageIndex]
                )
            }
        }
    }
}

@Composable
private fun TitleBar(
    onBack: () -> Unit,
    showEdit: Boolean = false,
    onEdit: () -> Unit = {}
) {
    Row(
        Modifier
            .padding(horizontal = 16.dp)
            .fillMaxWidth()
            .height(56.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Image(
            painterResource(R.drawable.ic_back2),
            null,
            Modifier
                .size(36.dp)
                .clip(CircleShape)
                .clickable(onClick = onBack)
        )
        if (showEdit) {
            Image(
                painterResource(R.drawable.ic_edit_image_prompt),
                null,
                Modifier
                    .size(36.dp)
                    .clip(CircleShape)
                    .clickable(onClick = onEdit)
            )
        }
    }
}

@Composable
private fun LoadingProgressText(
    imageResult: ImageResult
) {
    val progress = when (imageResult) {
        is ImageResult.Loading -> imageResult.progress
        else -> 0
    }
    Text(
        "${stringResource(R.string.ai_image_result_page_loading_progress)}\n$progress%...",
        style = MaterialTheme.typography.bodySmall.copy(
            color = MaterialTheme.colorScheme.onPrimary,
            fontSize = 15.sp,
            lineHeight = 24.sp,
            textAlign = TextAlign.Center
        )
    )
}

@Composable
private fun ImagePreview(imageUrl: String) {
    Column(Modifier.fillMaxSize()) {
        Box {
            AsyncImage(
                imageUrl,
                null,
                Modifier
                    .fillMaxWidth()
                    .height(677.dp),
                contentScale = ContentScale.Crop
            )
            Box(
                Modifier
                    .align(Alignment.BottomCenter)
                    .fillMaxWidth()
                    .height(144.dp)
                    .background(
                        Brush.verticalGradient(
                            listOf(Color(0x000D1116), Color(0xFF0D1116))
                        )
                    )
            )
        }
        Box(
            Modifier
                .weight(1f)
                .fillMaxWidth()
                .background(Color(0xFF0D1116))
        )
    }
}

@Composable
private fun ImageList(
    images: List<String>,
    selectedImageIndex: Int,
    onImageSelected: (Int) -> Unit
) {
    LazyRow(
        Modifier
            .fillMaxWidth()
            .height(80.dp),
        horizontalArrangement = Arrangement.spacedBy(10.dp, Alignment.CenterHorizontally)
    ) {
        itemsIndexed(images, key = { _, url -> url }) { index, url ->
            AsyncImage(
                url,
                null,
                Modifier
                    .size(60.dp, 80.dp)
                    .clip(RoundedCornerShape(5.dp))
                    .run {
                        if (index == selectedImageIndex) {
                            border(
                                2.dp,
                                MaterialTheme.colorScheme.primary,
                                RoundedCornerShape(5.dp)
                            )
                        } else this
                    }
                    .clickable {
                        onImageSelected(index)
                    },
                contentScale = ContentScale.Crop
            )
        }
    }
}

@Composable
private fun ConfirmBtn(
    enable: Boolean,
    onClick: () -> Unit
) {
    DTButton(
        R.string.confirm,
        modifier = Modifier
            .padding(start = 30.dp, end = 30.dp, bottom = 16.dp)
            .navigationBarsPadding()
            .fillMaxWidth()
            .height(44.dp),
        enable = enable,
        containerColor = MaterialTheme.colorScheme.primary,
        disabledContainerColor = Color(0xFF636364),
        onClick = onClick
    )
}