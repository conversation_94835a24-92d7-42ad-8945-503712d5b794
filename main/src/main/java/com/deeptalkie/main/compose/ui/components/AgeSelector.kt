package com.deeptalkie.main.compose.ui.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.deeptalkie.main.R

@Composable
fun AgeSelector(index: Int, onSelected: (index: Int) -> Unit) {
    Row(Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.Center) {
        AgeSelectBox(
            index == 0,
            stringResource(R.string.new_user_info_select_page_age_below_16)
        ) {
            onSelected(0)
        }
        AgeSelectBox(
            index == 1,
            stringResource(R.string.new_user_info_select_page_age_16_18)
        ) {
            onSelected(1)
        }
        AgeSelectBox(
            index == 2,
            stringResource(R.string.new_user_info_select_page_age_18)
        ) {
            onSelected(2)
        }
    }
}

@Composable
fun AgeSelectBox(selected: Boolean, text: String, onSelected: () -> Unit) {
    val bg = if (selected) R.drawable.age_selected_bg else R.drawable.age_un_selected_bg

    Box(
        Modifier.clickable(
            onClick = onSelected,
            indication = null,
            interactionSource = remember { MutableInteractionSource() }),
        contentAlignment = Alignment.Center,
    ) {
        Image(
            painterResource(bg), null,
            Modifier
                .width(115.dp)
                .height(52.dp)
        )
        Text(
            text = text,
            color = MaterialTheme.colorScheme.onPrimary,
            fontSize = 14.sp,
            fontWeight = FontWeight.W500,
            textAlign = TextAlign.Center,
        )
    }
}

@Preview
@Composable
fun AgeSelectorPrev() {
    AgeSelector(0) {}
}