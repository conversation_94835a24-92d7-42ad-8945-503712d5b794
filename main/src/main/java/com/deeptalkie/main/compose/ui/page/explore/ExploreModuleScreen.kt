package com.deeptalkie.main.compose.ui.page.explore

import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.constraintlayout.compose.ConstraintLayout
import com.clevguard.utils.ext.loge
import com.deeptalkie.main.R
import com.deeptalkie.main.compose.ui.components.DTPage
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ExploreModuleScreen(
    onRoleClick: (Long) -> Unit = {}
) {
    val pagerState = rememberPagerState { 3 }
    val scope = rememberCoroutineScope()

    DTPage(background = R.drawable.bg_mine) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .statusBarsPadding()
        ) {
            // Tab区域
            Tabs(
                selectedTabIndex = pagerState.currentPage,
                onTabSelected = { index ->
                    scope.launch {
                        loge("page:$index")
                        pagerState.animateScrollToPage(index)
                    }
                }
            )
            // ViewPager区域
            HorizontalPager(
                state = pagerState,
                modifier = Modifier.weight(1f),
                beyondViewportPageCount = 3,
                userScrollEnabled = false
            ) { page ->
                when (page) {
                    0 -> ExploreRoleScreen(module = ExploreModule.Girls, onRoleClick = onRoleClick)
                    1 -> ExploreRoleScreen(module = ExploreModule.Guys, onRoleClick = onRoleClick)
                    2 -> ExploreRoleScreen(module = ExploreModule.Anime, onRoleClick = onRoleClick)
                }
            }
        }
    }
}

@Composable
private fun Tabs(selectedTabIndex: Int, onTabSelected: (Int) -> Unit) {
    Box(
        modifier = Modifier.fillMaxWidth(),
        contentAlignment = Alignment.Center
    ) {
        ConstraintLayout(
            modifier = Modifier
                .wrapContentSize()
                .padding(top = 16.dp)
                .height(48.dp)
        ) {
            val (girls, guys, anime, indicator, shadow, line, help) = createRefs()
            val verticalGuideline1 = createGuidelineFromStart(1 / 3f)
            val verticalGuideline2 = createGuidelineFromStart(2 / 3f)
            Row(
                modifier = Modifier
                    .clickable { onTabSelected(0) }
                    .constrainAs(girls) {
                        // 约束到中线左侧
                        start.linkTo(parent.start)
                        end.linkTo(verticalGuideline1)
                        top.linkTo(parent.top)
                    },
                verticalAlignment = Alignment.CenterVertically
            ) {
                Image(
                    painter = if (selectedTabIndex == 0) {
                        painterResource(R.drawable.ic_girls_selected)
                    } else {
                        painterResource(R.drawable.ic_girls_unselected)
                    },
                    contentDescription = null,
                    modifier = Modifier
                        .size(16.dp)
                )
                Spacer(modifier = Modifier.width(6.dp))
                Text(
                    text = stringResource(R.string.girls),
                    color = if (selectedTabIndex == 0) Color.White else colorResource(R.color.color_ACACAC),
                )
            }

            Row(
                modifier = Modifier
                    .clickable { onTabSelected(1) }
                    .constrainAs(guys) {
                        start.linkTo(verticalGuideline1)
                        end.linkTo(verticalGuideline2)
                        top.linkTo(parent.top)
                    },
                verticalAlignment = Alignment.CenterVertically
            ) {
                Image(
                    painter = if (selectedTabIndex == 1) {
                        painterResource(R.drawable.ic_guys_selected)
                    } else {
                        painterResource(R.drawable.ic_guys_unselected)
                    },
                    contentDescription = null,
                    modifier = Modifier
                        .size(16.dp)
                )
                Spacer(modifier = Modifier.width(6.dp))
                Text(
                    text = stringResource(R.string.guys),
                    color = if (selectedTabIndex == 1) Color.White else colorResource(R.color.color_ACACAC),
                )
            }

            Row(
                modifier = Modifier
                    .clickable { onTabSelected(2) }
                    .constrainAs(anime) {
                        start.linkTo(verticalGuideline2)
                        end.linkTo(parent.end)
                        top.linkTo(parent.top)
                    },
                verticalAlignment = Alignment.CenterVertically
            ) {
                Image(
                    painter = if (selectedTabIndex == 2) {
                        painterResource(R.drawable.ic_anime_selected)
                    } else {
                        painterResource(R.drawable.ic_anime_def)
                    },
                    contentDescription = null,
                    modifier = Modifier
                        .size(16.dp)
                )
                Spacer(modifier = Modifier.width(6.dp))
                Text(
                    text = stringResource(R.string.anime),
                    color = if (selectedTabIndex == 2) Color.White else colorResource(R.color.color_ACACAC),
                )
            }

            val roundedCornerShape = RoundedCornerShape(8.dp)
            // 滑动指示条
            Box(
                modifier = Modifier
                    .height(1.dp)
                    .width(302.dp)
                    .background(colorResource(R.color.white10), roundedCornerShape)
                    .constrainAs(line) {
                        // 动态对齐选中项
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                        top.linkTo(indicator.top)
                        bottom.linkTo(indicator.bottom)
                    }
                    .animateContentSize() // 宽度动画
            )

            Spacer(
                modifier = Modifier
                    .padding(4.dp)
                    .constrainAs(help) {
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                        top.linkTo(girls.bottom)
                    })
            val gradientBrush = Brush.horizontalGradient(
                colors = listOf(
                    colorResource(R.color.color_554FFF),
                    colorResource(R.color.color_A332FF),
                    colorResource(R.color.color_FA6DF2)
                )
            )
            // 滑动指示条
            Box(
                modifier = Modifier
                    .width(96.dp)
                    .height(3.dp)
                    .background(gradientBrush, roundedCornerShape)
                    .constrainAs(indicator) {
                        // 动态对齐选中项 if (selectedTabIndex == 0) girls.start else guys.start
                        start.linkTo(
                            when (selectedTabIndex) {
                                0 -> girls.start
                                1 -> guys.start
                                else -> anime.start
                            }
                        )
                        end.linkTo(
                            when (selectedTabIndex) {
                                0 -> girls.end
                                1 -> guys.end
                                else -> anime.end
                            }
                        )
                        top.linkTo(help.bottom)
                    }
                    .animateContentSize() // 宽度动画
            )

            Image(
                painter = painterResource(id = R.drawable.ic_planet_shadow),
                contentDescription = "装饰图片",
                modifier = Modifier
                    .size(96.dp, 24.dp) // 图片大小
                    .constrainAs(shadow) {
                        top.linkTo(indicator.bottom) // 图片顶部在指示条下方
                        centerHorizontallyTo(indicator) // 水平居中对齐指示条
                    },
                contentScale = ContentScale.Crop
            )
        }
    }
}