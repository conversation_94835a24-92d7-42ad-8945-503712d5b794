package com.deeptalkie.main.compose.ui.page.chat

import android.os.Build
import android.view.WindowManager
import androidx.activity.compose.LocalActivity
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.animateContentSize
import androidx.compose.animation.core.snap
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.SideEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.clevguard.utils.ext.logv
import com.deeptalkie.main.Membership
import com.deeptalkie.main.R
import com.deeptalkie.main.compose.effect.ReactiveEffect
import com.deeptalkie.main.compose.navigation.MainRoute
import com.deeptalkie.main.compose.theme.White20
import com.deeptalkie.main.compose.ui.components.DTHorizontalSpacer
import com.deeptalkie.main.compose.ui.components.DTPage
import com.deeptalkie.main.compose.ui.dialog.CharacterImageBottomSheet
import com.deeptalkie.main.compose.ui.dialog.CharacterImageHelpDialog
import com.deeptalkie.main.compose.ui.dialog.CharacterImageInputBottomSheet
import com.deeptalkie.main.compose.ui.dialog.DTSingleBtnWarnDialog
import com.deeptalkie.main.compose.ui.modifier.click
import com.deeptalkie.main.db.table.MsgRecord
import com.deeptalkie.main.view.RequirePurchaseDialog
import kotlinx.coroutines.launch

/**
 * 聊天页
 */
@Composable
fun ChatPage(
    viewModel: ChatViewModel = viewModel(),
    onBack: () -> Unit,
    onNavigate: (MainRoute) -> Unit
) {
    val context = LocalContext.current
    val msgListState = rememberLazyListState()
    val scope = rememberCoroutineScope()
    val aiRoleSessionInfoState = viewModel.aiRoleSessionInfoStateFlow?.collectAsStateWithLifecycle()
    val aiRole by remember(aiRoleSessionInfoState?.value) {
        derivedStateOf {
            aiRoleSessionInfoState?.value?.aiRole
        }
    }
    val userAIRole by remember(aiRoleSessionInfoState?.value) {
        derivedStateOf {
            aiRoleSessionInfoState?.value?.userAIRole
        }
    }
    val messages by viewModel.messagesStateFlow.collectAsStateWithLifecycle()
    var bottomSizeChangeFlag by remember { mutableIntStateOf(0) }
    val keyboardController = LocalSoftwareKeyboardController.current
    val focusRequester = remember { FocusRequester() }

    val isScrolling by remember {
        derivedStateOf {
            msgListState.isScrollInProgress
        }
    }

    val window = LocalActivity.current?.window

    SideEffect {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_NOTHING)
        } else {
            window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)
        }
    }

    ReactiveEffect(viewModel.lastMessage?.id) {
        if (viewModel.lastMessage != null && !isScrolling) {
            logv("有新消息(${viewModel.lastMessage?.msgId}): ${viewModel.lastMessage?.content}")
            scope.launch {
                msgListState.scrollToItem(0)
            }
        }
    }

    ReactiveEffect(bottomSizeChangeFlag) {
        scope.launch {
            if (messages.isNotEmpty() && !isScrolling) {
                msgListState.scrollToItem(0)
            }
        }
    }

    val loadMore by remember {
        derivedStateOf {
            msgListState.firstVisibleItemIndex >= (messages.lastIndex - 100)
        }
    }

    LaunchedEffect(messages.size, loadMore) {
        if ((loadMore || messages.isEmpty()) && viewModel.hasMoreMsg) {
            logv("加载更多历史消息")
            viewModel.refreshMessages()
        }
    }

    DisposableEffect(Unit) {
        onDispose {
            viewModel.stopTTSPlay()
        }
    }

    DTPage(
        Modifier
            .background(Color(0xFF16161A))
            .click {
                keyboardController?.hide()
                viewModel.foldAiReply()
            },
        loading = viewModel.loading,
        onDismissLoading = {
            viewModel.showLoading(false)
        }
    ) {
        Column {
            ChatTopBar(
                aiRole?.name ?: "",
                aiRole?.approvalStatusText,
                userAIRole?.isFavorite == true,
                onBackClick = onBack,
                onFavoritesClick = {
                    val roleId = aiRole?.id ?: return@ChatTopBar
                    val currentState = if (userAIRole?.isFavorite == true) 0 else 1
                    viewModel.favorite(roleId, currentState)
                },
                onReportClick = {
                    onNavigate(MainRoute.Report(aiRole?.name.orEmpty()))
                }
            )
            Box(
                Modifier
                    .fillMaxWidth()
                    .background(Color(0x4D101015))
                    .padding(horizontal = 16.dp, vertical = 5.dp),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    stringResource(R.string.chat_page_chat_content_tips),
                    color = Color(0xFF727477),
                    fontSize = 12.sp,
                    fontWeight = FontWeight.W400,
                    lineHeight = 16.sp,
                    textAlign = TextAlign.Center
                )
            }
            LazyColumn(
                Modifier
                    .fillMaxWidth()
                    .weight(1f),
                state = msgListState,
                verticalArrangement = Arrangement.spacedBy(16.dp),
                contentPadding = PaddingValues(top = 8.dp),
                reverseLayout = true
            ) {
                item("MASSAGE_BOTTOM_OR_WAITING_MESSAGE") {
                    AnimatedVisibility(
                        viewModel.showWaitingMessage,
                        enter = slideInVertically { it },
                        exit = fadeOut(snap())
                    ) {
                        WaitingMessage(aiRole?.avatar)
                    }
                }
                itemsIndexed(
                    messages,
                    key = { _, it -> it.msg.id },
                    contentType = { _, it -> it.msg.type }
                ) { index, it ->
                    MsgView(
                        aiRole = aiRole,
                        msgWithReply = it,
                        showTalkSuggestion = index == 0 && viewModel.showTalkSuggestion,
                        {
                            onNavigate(MainRoute.RoleDetail(it.msg.roleId))
                        },
                        {
                            onNavigate(MainRoute.Product)
                        },
                        {
                            onNavigate(MainRoute.ZoomImage(it.msg.content))
                        },
                        { w, h ->
                            onNavigate(MainRoute.VideoPlay(it.msg.content, w, h))
                        },
                        focusRequester = focusRequester
                    )
                }
            }

            if (viewModel.selectedReplyMsg != null) {
                ReplyMsgView(viewModel.selectedReplyMsg!!, viewModel::cancelReply)
            }

            ChatBottomBar(
                focusRequester,
                modifier = Modifier.onSizeChanged { bottomSizeChangeFlag++ },
            ) {
                onNavigate(MainRoute.Product)
            }
        }
    }

    if (viewModel.isShowCharacterImage) {
        CharacterImageBottomSheet(
            onDismiss = {
                viewModel.showCharacterImage(false)
            },
            onQuestionClick = {
                viewModel.showCharacterImageHelpDialog(true)
            },
            onSelectionClick = {
                viewModel.showCharacterImage(false)
                viewModel.setCharacterImageTextValue(it)
                viewModel.showCharacterImageInputBottomSheet(true)
            }
        )
    }
    if (viewModel.isShowCharacterImageHelpDialog) {
        CharacterImageHelpDialog(
            onDismiss = {
                viewModel.showCharacterImageHelpDialog(false)
            }
        )
    }
    if (viewModel.isShowCharacterImageInputBottomSheet) {
        CharacterImageInputBottomSheet(
            initValue = viewModel.characterImageText,
            onDismiss = {
                viewModel.showCharacterImageInputBottomSheet(false)
            },
            onSendMsg = fn@{
                if (!Membership.isVip()) {
                    RequirePurchaseDialog(context, RequirePurchaseDialog.TYPE_PICTURE) {
                        onNavigate(MainRoute.Product)
                    }.show()
                    return@fn
                }
                if (!viewModel.checkCoinEnough) {
                    viewModel.showCoinNotEnoughDialog(true)
                    return@fn
                }
                viewModel.sendRequestPhotoMsg(context, it) {
                    onNavigate(MainRoute.Product)
                }
                viewModel.showCharacterImageInputBottomSheet(false)
            }
        )
    }
    if (viewModel.showCoinNotEnoughDialog) {
        DTSingleBtnWarnDialog(
            title = R.string.reminder,
            content = R.string.ai_generation_image_page_coin_not_enough_dialog_tips,
            confirmText = R.string.check,
            icon = R.drawable.ic_coin_not_enough,
            onCancel = {
                viewModel.showCoinNotEnoughDialog(false)
            },
            onConfirm = {
                viewModel.showCoinNotEnoughDialog(false)
                onNavigate(MainRoute.Product)
            }
        )
    }
}

@Composable
fun ReplyMsgView(
    msg: MsgRecord,
    onClose: () -> Unit,
) {
    Row(
        Modifier
            .fillMaxWidth()
            .background(Color(0xFF35343C))
            .padding(16.dp, 7.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            msg.content,
            Modifier
                .weight(1f)
                .animateContentSize(),
            color = Color(0xFFB3B4BD),
            fontSize = 12.sp,
            fontWeight = FontWeight.W400,
            lineHeight = 15.sp,
            maxLines = 3,
            overflow = TextOverflow.Ellipsis
        )
        DTHorizontalSpacer(10.dp)
        Icon(
            painterResource(R.drawable.ic_reply_close_circle),
            null,
            Modifier
                .size(20.dp)
                .clip(CircleShape)
                .clickable(onClick = onClose),
            tint = White20,
        )
    }
}