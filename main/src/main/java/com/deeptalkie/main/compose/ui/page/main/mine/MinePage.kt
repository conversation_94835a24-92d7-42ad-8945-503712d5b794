package com.deeptalkie.main.compose.ui.page.main.mine

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicText
import androidx.compose.foundation.text.ClickableText
import androidx.compose.foundation.text.TextAutoSize
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.deeptalkie.main.R
import com.deeptalkie.main.activity.WebActivity
import com.deeptalkie.main.bean.UserManager
import com.deeptalkie.main.compose.navigation.MainRoute
import com.deeptalkie.main.compose.theme.White
import com.deeptalkie.main.compose.theme.White5
import com.deeptalkie.main.compose.theme.White50
import com.deeptalkie.main.compose.ui.components.DTHorizontalSpacer
import com.deeptalkie.main.compose.ui.components.DTPage
import com.deeptalkie.main.compose.ui.components.DTVerticalSpacer
import com.deeptalkie.main.compose.ui.components.LocalDTNavigationBarHeight
import com.deeptalkie.main.compose.ui.modifier.click
import com.deeptalkie.main.compose.utils.ChildDesign
import com.deeptalkie.main.compose.utils.toAnnotatedStringParameters
import com.deeptalkie.main.config.Constant
import com.imyfone.membership.api.bean.MemberBean
import com.imyfone.membership.api.bean.UserBean

private const val TAG_LOGIN = "TAG_LOGIN"
private const val TAG_SIGNUP = "TAG_SIGNUP"

@Composable
fun MineRoute(
    viewModel: MineViewModel = viewModel(), onEnterUserInfo: () -> Unit,
    onLogin: () -> Unit,
    onSignUp: () -> Unit,
    onEnterProduct: () -> Unit,
    onNavigate: (MainRoute) -> Unit
) {
    val context = LocalContext.current
    val memberBean by viewModel.memberFlow.collectAsStateWithLifecycle()
    val user by viewModel.userFlow.collectAsStateWithLifecycle()
    MineScreen(
        user = user,
        member = memberBean,
        onEnterUserInfo = onEnterUserInfo,
        onLogin = onLogin,
        onSignUp = onSignUp,
        onEnterProduct = onEnterProduct,
        onNavigate = onNavigate,
        onTerms = { WebActivity.startBrowser(context, Constant.terms + Constant.webParams) },
        onPrivacy = { WebActivity.startBrowser(context, Constant.privacy + Constant.webParams) }
    )
    if (user != null) {
        LaunchedEffect(user) {
            viewModel.refreshInfo()
        }
    }
}

@Composable
private fun MineScreen(
    user: UserBean?,
    member: MemberBean?,
    onEnterUserInfo: () -> Unit,
    onLogin: () -> Unit,
    onSignUp: () -> Unit,
    onEnterProduct: () -> Unit,
    onNavigate: (MainRoute) -> Unit,
    onTerms: () -> Unit,
    onPrivacy: () -> Unit
) {
    DTPage(background = if (user == null) R.drawable.bg_mine else R.drawable.bg_login_state) {
        Column(
            Modifier
                .statusBarsPadding()
                .navigationBarsPadding()
                .fillMaxSize()
                .padding(horizontal = 20.dp)
        ) {
            DTVerticalSpacer(36.dp)
            UserState(
                user,
                member = member,
                onEnterUserInfo = onEnterUserInfo,
                onLogin = onLogin,
                onSignUp = onSignUp
            )
            DTVerticalSpacer(20.dp)
            VipState(onEnterProduct = onEnterProduct)
            DTVerticalSpacer(36.dp)
            MineItems(
                member = member, onNavigate,
                onTerms = onTerms,
                onPrivacy = onPrivacy
            )
        }
    }
}

@Composable
private fun UserState(
    user: UserBean?,
    member: MemberBean?,
    onEnterUserInfo: () -> Unit,
    onLogin: () -> Unit,
    onSignUp: () -> Unit
) {
    val viewModel: MineViewModel = viewModel()
    val enterUserInfoModifier =
        Modifier.clickable(enabled = member != null, onClick = onEnterUserInfo)
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = Alignment.CenterVertically
    ) {
        UserAvatar(member, onEnterUserInfo)
        if (user != null) {
            DTHorizontalSpacer(13.dp)
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(
                    modifier = Modifier
                        .weight(1f)
                        .then(enterUserInfoModifier),
                    verticalArrangement = Arrangement.Center
                ) {
                    Text(
                        text = member?.firstName ?: "",
                        style = TextStyle(
                            fontSize = 16.sp,
                            color = White, fontWeight = FontWeight.W700,
                        ),
                        overflow = TextOverflow.Ellipsis,
                        maxLines = 1,
                        lineHeight = 26.sp
                    )
                    DTVerticalSpacer(2.dp)
                    Text(
                        text = member?.email ?: "",
                        style = TextStyle(
                            fontSize = 12.sp,
                            color = White50
                        ),
                        overflow = TextOverflow.Ellipsis,
                        maxLines = 1,
                        lineHeight = 20.sp
                    )
                }
                val brush = Brush.horizontalGradient(
                    listOf(
                        colorResource(R.color.color_642EE5),
                        colorResource(R.color.color_8837CA),
                        colorResource(R.color.color_E34C86),
                        colorResource(R.color.color_EE79A0),
                        colorResource(R.color.color_FFC3C9)
                    )
                )
                Row(
                    horizontalArrangement = Arrangement.Center,
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier
                        .background(
                            brush,
                            RoundedCornerShape(100.dp)
                        )
                        .padding(horizontal = 6.dp, vertical = 4.dp)
                ) {
                    Image(
                        painterResource(R.drawable.ic_coin_flag),
                        contentDescription = "icon",
                        modifier = Modifier.size(22.dp, 18.dp)
                    )
                    DTHorizontalSpacer(7.dp)
                    Text(
                        text = viewModel.coins.toString(),
                        style = TextStyle(
                            fontSize = 14.sp,
                            fontWeight = FontWeight.W600,
                            color = White
                        )
                    )
                }
                DTHorizontalSpacer(6.dp)
                Image(
                    painterResource(R.drawable.ic_right_arrow1),
                    modifier = Modifier.size(16.dp),
                    contentDescription = "more"
                )
            }

        } else {
            DTHorizontalSpacer(20.dp)
            val annotatedString = loginAndSignUpAnnotationString(White)
            Row(modifier = Modifier.weight(1f), verticalAlignment = Alignment.CenterVertically) {
                ClickableText(
                    text = annotatedString,
                    onClick = {
                        annotatedString
                            .getStringAnnotations(tag = TAG_LOGIN, start = it, end = it)
                            .firstOrNull()?.let { _ -> onLogin() }
                        annotatedString
                            .getStringAnnotations(tag = TAG_SIGNUP, start = it, end = it)
                            .firstOrNull()?.let { _ -> onSignUp() }
                    },
                    style = TextStyle(
                        fontWeight = FontWeight.W700,
                        fontSize = 16.sp,
                        lineHeight = 24.sp,
                        color = colorResource(R.color.color_747474)
                    )
                )
            }
        }
    }
}


@Composable
private fun VipState(onEnterProduct: () -> Unit) {
    val viewModel: MineViewModel = viewModel()
    Box {
        //背景
        val ratio by remember { mutableFloatStateOf(335 / 134f) }
        val isVip by viewModel.vipStateFlow.collectAsStateWithLifecycle()
        Image(
            painterResource(
                if (isVip) R.drawable.vip_state_bg_enable else R.drawable.vip_state_bg_disable
            ),
            contentDescription = "bg",
            contentScale = ContentScale.FillBounds,
            modifier = Modifier
                .click(onEnterProduct)
                .fillMaxWidth()
                .aspectRatio(ratio)
        )
        //权益信息
        Row(
            Modifier
                .fillMaxWidth()
                .aspectRatio(ratio)
        ) {
            Column(
                modifier = Modifier
                    .align(alignment = Alignment.CenterVertically)
                    .weight(2f)
                    .padding(start = 14.dp, top = 14.dp, bottom = 14.dp),
                verticalArrangement = Arrangement.Center
            ) {
                val isVip by viewModel.vipStateFlow.collectAsStateWithLifecycle()
                val vipFailureTimeText by viewModel.vipFailureTimeTextFlow.collectAsStateWithLifecycle()
                if (isVip) {
                    val brush = Brush.horizontalGradient(
                        listOf(
                            colorResource(R.color.color_7567C4),
                            colorResource(R.color.color_483795),
                            colorResource(R.color.color_2B166C)
                        )
                    )
                    Text(
                        text = stringResource(id = R.string.deeptalkie_premium),
                        style = TextStyle(
                            fontSize = 18.sp,
                            fontWeight = FontWeight.W800,
                            brush = brush,
                            lineHeight = 24.sp,
                        ),
                        modifier = Modifier.fillMaxWidth(),
                    )

                    DTVerticalSpacer(10.dp)
                    Row {
                        Image(
                            painterResource(R.drawable.ic_vip),
                            modifier = Modifier.size(16.dp),
                            contentDescription = "icon"
                        )
                        DTHorizontalSpacer(5.dp)
                        val expires = stringResource(R.string.expires_title)
                        Text(
                            text = "$expires${vipFailureTimeText}",
                            style = TextStyle(
                                fontSize = 14.sp,
                                fontWeight = FontWeight.W500,
                                color = colorResource(R.color.color_73331A)
                            ),
                            minLines = 1
                        )
                    }
                } else {
                    val brush = Brush.horizontalGradient(
                        listOf(
                            colorResource(R.color.color_7567C4),
                            colorResource(R.color.color_483795),
                            colorResource(R.color.color_2B166C)
                        )
                    )
                    Text(
                        text = stringResource(id = R.string.deeptalkie_premium),
                        style = TextStyle(
                            fontSize = 18.sp,
                            fontWeight = FontWeight.W800,
                            brush = brush,
                            lineHeight = 24.sp,
                        ),
                        modifier = Modifier.fillMaxWidth(),
                    )
                    DTVerticalSpacer(6.dp)
                    Text(
                        text = stringResource(R.string.better_ai_model_and_free_coins_for_each_renewal),
                        style = TextStyle(
                            fontSize = 12.sp,
                            color = colorResource(R.color.color_13007B),
                            lineHeight = 16.sp
                        )
                    )
                }
                DTVerticalSpacer(12.dp)
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    //金币信息
                    if (isVip) {
                        val brush = Brush.horizontalGradient(
                            listOf(
                                Color(0xffFFBC93),
                                Color(0xffFF7EF0),
                                Color(0xff7F78FF),
                            )
                        )
                        Row(
                            horizontalArrangement = Arrangement.Center,
                            verticalAlignment = Alignment.CenterVertically,
                            modifier = Modifier
                                .weight(1f)
                                .height(32.dp)
                                .background(
                                    brush,
                                    RoundedCornerShape(100.dp)
                                )
                        ) {
                            Image(
                                painterResource(R.drawable.ic_coin_flag),
                                contentDescription = "icon",
                                modifier = Modifier.size(19.dp, 17.dp)
                            )
                            DTHorizontalSpacer(7.dp)
                            Text(
                                text = viewModel.coins.toString(),
                                style = TextStyle(
                                    fontSize = 15.sp,
                                    fontWeight = FontWeight.W500,
                                    color = White
                                )
                            )
                        }
                        DTHorizontalSpacer(6.dp)
                    }
                    //订阅按钮
                    Box(
                        contentAlignment = Alignment.Center,
                        modifier = Modifier
                            .then(if (isVip) Modifier.weight(1f) else Modifier.wrapContentSize())
                            .height(32.dp)
                    ) {
                        Image(
                            painterResource(if (isVip) R.drawable.bg_btn_vip_update else R.drawable.bg_btn_sub),
                            contentDescription = "background",
                            modifier = Modifier
                                .clip(shape = RoundedCornerShape(100.dp))
                                .then(if (isVip) Modifier.fillMaxWidth() else Modifier.wrapContentWidth())
                                .height(32.dp)
                                .aspectRatio(if (isVip) 88 / 32f else 114 / 32f),
                            contentScale = ContentScale.Crop
                        )
                        BasicText(
                            text = stringResource(if (isVip) R.string.upgrade else R.string.subscribe),
                            style = TextStyle(
                                color = White,
                                fontSize = 15.sp,
                                textAlign = TextAlign.Center,
                                fontWeight = FontWeight.W500,
                            ),
                            modifier = Modifier
                                .click {
                                    onEnterProduct()
                                }
                                .padding(horizontal = 10.dp),
                            maxLines = 1,
                            autoSize = TextAutoSize.StepBased(maxFontSize = 15.sp)
                        )
                    }
                }
            }
            Image(
                painterResource(if (isVip) R.drawable.ic_crown_enable else R.drawable.ic_crown_disable),
                contentDescription = "icon",
                modifier = Modifier
                    .weight(1.2f)
                    .offset(0.dp, (-16).dp)
            )
        }
    }
}

@Composable
private fun ItemMine(@DrawableRes img: Int, @StringRes title: Int, onClick: () -> Unit) {
    Row(
        Modifier
            .fillMaxWidth()
            .click(onClick = onClick),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            painterResource(img),
            contentDescription = "icon",
            modifier = Modifier.size(24.dp),
            tint = White
        )
        Spacer(modifier = Modifier.width(12.dp))
        Text(
            text = stringResource(title),
            fontSize = 14.sp,
            lineHeight = 24.sp,
            modifier = Modifier.weight(1f),
            color = White
        )
        Icon(
            painterResource(R.drawable.ic_right_arrow1),
            modifier = Modifier.size(16.dp),
            contentDescription = "more", tint = colorResource(R.color.color_828589)
        )
    }
}

@Composable
fun ItemLanguage(language: String, onClick: () -> Unit) {
    Row(
        Modifier
            .fillMaxWidth()
            .click(onClick = onClick),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Image(
            painterResource(R.drawable.ic_language),
            contentDescription = "icon",
            modifier = Modifier.size(24.dp)
        )
        Spacer(modifier = Modifier.width(12.dp))
        Text(
            text = stringResource(R.string.language),
            fontSize = 14.sp,
            lineHeight = 24.sp,
            modifier = Modifier.weight(1f),
            color = White
        )
        Text(
            text = language,
            fontSize = 14.sp,
            lineHeight = 24.sp,
            color = colorResource(R.color.color_6c6c6c)
        )
        Icon(
            painterResource(R.drawable.ic_right_arrow1),
            modifier = Modifier.size(16.dp),
            contentDescription = "more",
            tint = colorResource(R.color.color_828589)
        )
    }
}

@Composable
private fun MineItems(
    member: MemberBean?,
    onItemClick: (MainRoute) -> Unit,
    onTerms: () -> Unit,
    onPrivacy: () -> Unit,
) {
    val state = rememberScrollState()
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .verticalScroll(state)
    ) {
        if (member != null) {
            ItemMine(R.drawable.ic_assets, R.string.my_character) {
                onItemClick(MainRoute.MyAssets)
            }
            DTVerticalSpacer(16.dp)
            HorizontalDivider(thickness = 1.dp, color = White5)
            DTVerticalSpacer(16.dp)
            ItemMine(R.drawable.ic_order, R.string.my_order) {
                onItemClick(MainRoute.MyOrder)
            }
            DTVerticalSpacer(16.dp)
            HorizontalDivider(thickness = 1.dp, color = White5)
            DTVerticalSpacer(16.dp)
        }
        val language by UserManager.languageFlow.collectAsStateWithLifecycle()
        ItemLanguage(language.title) {
            onItemClick(MainRoute.Language)
        }
        DTVerticalSpacer(16.dp)
        HorizontalDivider(thickness = 1.dp, color = White5)
        DTVerticalSpacer(16.dp)
        ItemMine(R.drawable.ic_about, R.string.about_me) {
            onItemClick(MainRoute.AboutMe)
        }
        DTVerticalSpacer(16.dp)
        HorizontalDivider(thickness = 1.dp, color = White5)
        DTVerticalSpacer(16.dp)
        ItemMine(R.drawable.ic_feedback, R.string.feedback) {
            onItemClick(MainRoute.Feedback)
        }
        DTVerticalSpacer(16.dp)
        HorizontalDivider(thickness = 1.dp, color = White5)
        DTVerticalSpacer(16.dp)
        ItemMine(R.drawable.ic_terms, R.string.terms_of_service) { onTerms() }
        DTVerticalSpacer(16.dp)
        HorizontalDivider(thickness = 1.dp, color = White5)
        DTVerticalSpacer(16.dp)
        ItemMine(R.drawable.ic_privacy, R.string.policy) { onPrivacy() }
        DTVerticalSpacer(LocalDTNavigationBarHeight.current)
    }
}

@Composable
private fun loginAndSignUpAnnotationString(annotatedStrColor: Color): AnnotatedString {
    val mainString = stringResource(id = R.string.please_login)
    val login = stringResource(id = R.string.log_in)
    val signup = stringResource(id = R.string.sign_up)
    return remember(mainString, login, signup, annotatedStrColor) {
        mainString.toAnnotatedStringParameters(
            ChildDesign(
                childString = login,
                annotatedTag = TAG_LOGIN,
                spanStyle = SpanStyle(
                    color = annotatedStrColor,
                    fontWeight = FontWeight.W600
                )
            ),
            ChildDesign(
                childString = signup,
                annotatedTag = TAG_SIGNUP,
                spanStyle = SpanStyle(
                    color = annotatedStrColor,
                    fontWeight = FontWeight.W600,
                )
            )
        )
    }
}

@Composable
private fun UserAvatar(member: MemberBean?, onEnterUserInfo: () -> Unit) {
    val resource = when {
        member == null -> {
            R.drawable.ic_header
        }

        else -> {
            R.drawable.ic_header_login
        }
    }
    Image(
        painterResource(resource),
        contentDescription = "avatar",
        modifier = Modifier
            .clickable(enabled = member != null, onClick = onEnterUserInfo)
            .size(50.dp)
    )
}