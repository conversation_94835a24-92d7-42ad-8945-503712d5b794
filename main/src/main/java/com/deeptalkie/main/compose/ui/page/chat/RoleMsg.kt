package com.deeptalkie.main.compose.ui.page.chat

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import coil3.compose.AsyncImage
import com.deeptalkie.main.Membership
import com.deeptalkie.main.R
import com.deeptalkie.main.compose.theme.White15
import com.deeptalkie.main.compose.theme.White6
import com.deeptalkie.main.compose.ui.components.BasicVoiceLoading
import com.deeptalkie.main.compose.ui.components.DTHorizontalSpacer
import com.deeptalkie.main.compose.ui.components.DTVerticalSpacer
import com.deeptalkie.main.db.table.AIRole
import com.deeptalkie.main.db.table.MsgRecord
import com.deeptalkie.main.db.table.MsgType
import com.deeptalkie.main.ext.imageRequest
import com.skydoves.landscapist.ImageOptions
import com.skydoves.landscapist.coil3.CoilImage
import com.skydoves.landscapist.components.rememberImageComponent
import com.skydoves.landscapist.transformation.blur.BlurTransformationPlugin

@Composable
fun RoleMsgView(
    aiRole: AIRole?,
    msgRecord: MsgRecord,
    showTalkSuggestion: Boolean,
    ttsPlaying: Boolean,
    showMsgSelectedPopup: Boolean,
    showDeletePopup: Boolean,
    onAvatarClick: () -> Unit,
    onTTSClick: () -> Unit,
    onMsgClick: () -> Unit,
    onSuggestionClick: (String) -> Unit,
    onMsgLongClick: () -> Unit,
    onMsgMoreClick: () -> Unit,
    onRegenerateImage: () -> Unit,
    onMsgPopupDismiss: () -> Unit,
    onMsgPopupClick: (MsgPopupOptionType) -> Unit,
    onDeletePopupDismiss: () -> Unit,
    onDelete: () -> Unit,
) {
    Column(
        Modifier
            .padding(horizontal = 16.dp)
            .fillMaxWidth()
    ) {
        Row(Modifier.fillMaxWidth(), verticalAlignment = Alignment.Bottom) {
            ChatAvatar(aiRole?.avatar, onAvatarClick)
            DTHorizontalSpacer(8.dp)
            when (msgRecord.msgType) {
                MsgType.Text -> {
                    RoleTextMsgView(
                        msgRecord,
                        ttsPlaying,
                        onTTSClick,
                        onMsgLongClick,
                        showMsgSelectedPopup,
                        onMsgPopupDismiss,
                        onMsgPopupClick
                    )
                }

                MsgType.Image -> {
                    RoleImageMsgView(
                        msgRecord,
                        showDeletePopup,
                        onMsgClick,
                        onRegenerateImage,
                        onMsgMoreClick,
                        onDeletePopupDismiss,
                        onDelete
                    )
                }

                MsgType.Video -> {
                    RoleVideoMsgView(
                        msgRecord,
                        showDeletePopup,
                        onMsgClick,
                        onMsgMoreClick,
                        onDeletePopupDismiss,
                        onDelete
                    )
                }

                MsgType.Voice -> {}
                null -> {}
            }
        }

        if (showTalkSuggestion) {
            DTVerticalSpacer(14.dp)
            aiRole?.talkSuggestionList?.let { talkSuggestionList ->
                talkSuggestionList.forEachIndexed { index, suggestion ->
                    Text(
                        suggestion,
                        Modifier
                            .padding(start = 40.dp)
                            .clip(RoundedCornerShape(12.dp))
                            .clickable {
                                onSuggestionClick(suggestion)
                            }
                            .background(White6)
                            .padding(12.dp, 10.dp),
                        style = MaterialTheme.typography.bodySmall.copy(
                            color = MaterialTheme.colorScheme.onTertiary,
                            lineHeight = 20.sp,
                        )
                    )
                    if (index < talkSuggestionList.lastIndex) {
                        DTVerticalSpacer(10.dp)
                    }
                }
            }
        }
    }
}

@Composable
fun RoleTextMsgView(
    msgRecord: MsgRecord,
    ttsPlaying: Boolean,
    onTTSClick: () -> Unit,
    onLongClick: () -> Unit,
    showMsgSelectedPopup: Boolean,
    onMsgPopupDismiss: () -> Unit,
    onMsgPopupClick: (MsgPopupOptionType) -> Unit,
) {
    Column(
        Modifier
            .roleMsgBg()
            .combinedClickable(
                onClick = {},
                onLongClick = onLongClick
            )
            .padding(12.dp, 10.dp, 12.dp, 8.dp)
    ) {
        Text(
            msgRecord.content.toStyledAnnotatedString(),
            style = MaterialTheme.typography.bodySmall.copy(
                color = MaterialTheme.colorScheme.onPrimary,
                lineHeight = 20.sp,
            )
        )
        DTVerticalSpacer(7.dp)
        BasicVoiceLoading(
            ttsPlaying,
            Modifier
                .size(33.dp, 17.dp)
                .clip(RoundedCornerShape(9.dp, 10.dp, 10.dp, 1.dp))
                .clickable(onClick = onTTSClick)
                .background(
                    Brush.verticalGradient(
                        listOf(
                            Color(0xFF924EFF),
                            Color(0xFFA480FF),
                        )
                    )
                )
        )

        if (showMsgSelectedPopup) {
            MsgSelectedPopup(
                modifier = Modifier.align(Alignment.End),
                offsetX = 12.dp,
                offsetY = 12.dp,
                onDismiss = onMsgPopupDismiss,
                onClick = onMsgPopupClick
            )
        }
    }
}

@Composable
fun RoleImageMsgView(
    msgRecord: MsgRecord,
    isShowDeletePopup: Boolean,
    onClick: () -> Unit,
    onRegenerateImage: () -> Unit,
    onMoreClick: () -> Unit,
    onDismissDeletePopup: () -> Unit,
    onDelete: () -> Unit,
) {
    MsgImage(
        msgRecord.content,
        msgRecord.prompt,
        false,
        "",
        isShowDeletePopup,
        onClick,
        onMoreClick,
        onRegenerateImage,
        onDismissDeletePopup,
        onDelete
    )
}

@Composable
fun RoleVideoMsgView(
    msgRecord: MsgRecord,
    isShowDeletePopup: Boolean,
    onClick: () -> Unit,
    onMoreClick: () -> Unit,
    onDismissDeletePopup: () -> Unit,
    onDelete: () -> Unit,
) {
    MsgImage(
        msgRecord.content,
        msgRecord.prompt,
        true,
        msgRecord.videoTime,
        isShowDeletePopup,
        onClick,
        onMoreClick,
        {},
        onDismissDeletePopup,
        onDelete
    )
}

@Composable
private fun ChatAvatar(avatar: String?, onClick: () -> Unit) {
    AsyncImage(
        avatar,
        null,
        Modifier
            .size(32.dp)
            .clip(CircleShape)
            .clickable(onClick = onClick),
        contentScale = ContentScale.Crop
    )
}

@Composable
private fun Modifier.roleMsgBg(): Modifier {
    return this
        .clip(
            RoundedCornerShape(12.dp, 12.dp, 12.dp, 2.dp)
        )
        .background(White15)
}

@Composable
private fun MsgImage(
    url: String,
    prompt: String,
    isVideo: Boolean,
    videoTime: String,
    isShowDeletePopup: Boolean,
    onClick: () -> Unit,
    onLongClick: () -> Unit,
    onRegenerateImage: () -> Unit,
    onDismissDeletePopup: () -> Unit,
    onDelete: () -> Unit,
) {
    val context = LocalContext.current
    val isVip by Membership.vipStateFlow.collectAsStateWithLifecycle()

    Box(Modifier.size(150.dp, 200.dp)) {
        CoilImage(
            imageRequest = { url.imageRequest(context) },
            Modifier
                .fillMaxSize()
                .clip(RoundedCornerShape(12.dp))
                .combinedClickable(
                    onClick = onClick,
                    onLongClick = onLongClick
                ),
            component = if (!isVip) rememberImageComponent {
                +BlurTransformationPlugin(radius = 80)
            } else rememberImageComponent {},
            imageOptions = ImageOptions(contentScale = ContentScale.Crop)
        )
        if (!isVip) {
            Icon(
                painter = painterResource(R.drawable.ic_locked),
                null,
                Modifier
                    .size(48.dp)
                    .clip(CircleShape)
                    .background(Color(0x1AFFFFFF))
                    .padding(14.dp)
                    .align(Alignment.Center),
                tint = MaterialTheme.colorScheme.onPrimary
            )
        } else {
            if (isVideo) {
                Image(
                    painterResource(R.drawable.ic_video_msg_play),
                    null,
                    Modifier
                        .size(50.dp)
                        .align(Alignment.Center),
                )

                Text(
                    videoTime,
                    Modifier
                        .align(Alignment.BottomEnd)
                        .padding(end = 6.dp, bottom = 6.dp),
                    style = MaterialTheme.typography.labelMedium.copy(
                        color = MaterialTheme.colorScheme.onPrimary,
                        lineHeight = 12.sp
                    )
                )
            }
            if (isShowDeletePopup) {
                DeletePopup(
                    Modifier
                        .padding(end = 20.dp, bottom = 20.dp)
                        .align(Alignment.BottomEnd),
                    onDismiss = onDismissDeletePopup,
                    onClick = onDelete
                )
            }
        }
        if (prompt.isNotBlank()) {
            Icon(
                painterResource(R.drawable.ic_regenerate_image),
                null,
                Modifier
                    .size(34.dp)
                    .clip(RoundedCornerShape(8.dp))
                    .clickable(onClick = onRegenerateImage)
                    .align(Alignment.BottomEnd)
                    .padding(end = 6.dp, bottom = 6.dp),
                tint = Color.Unspecified
            )
        }
    }
}