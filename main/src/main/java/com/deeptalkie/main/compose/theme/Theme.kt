package com.deeptalkie.main.compose.theme

import android.os.Build
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.LocalRippleConfiguration
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.RippleConfiguration
import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.dynamicDarkColorScheme
import androidx.compose.material3.dynamicLightColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.ui.platform.LocalContext

private val DarkColorScheme = darkColorScheme(
    primary = SocialPurple,
    secondary = SocialBlue,
    tertiary = SocialPink,
    onPrimary = White,
    onSecondary = White70,
    onTertiary = White50,
    onSecondaryContainer = LightGray,
)

private val LightColorScheme = lightColorScheme(
    primary = SocialPurple,
    secondary = SocialBlue,
    tertiary = SocialPink,
    onPrimary = White,
    onSecondary = White70,
    onTertiary = White50,
    onSecondaryContainer = LightGray,
)

@OptIn(ExperimentalMaterial3Api::class)
private val DTRippleConfiguration = RippleConfiguration(color = White10)

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DTTheme(
    darkTheme: Boolean = false, // isSystemInDarkTheme(),
    dynamicColor: Boolean = false, // Dynamic color is available on Android 12+
    content: @Composable () -> Unit
) {
    val colorScheme = when {
        dynamicColor && Build.VERSION.SDK_INT >= Build.VERSION_CODES.S -> {
            val context = LocalContext.current
            if (darkTheme) dynamicDarkColorScheme(context) else dynamicLightColorScheme(context)
        }

        darkTheme -> DarkColorScheme
        else -> LightColorScheme
    }


    MaterialTheme(
        colorScheme = colorScheme,
        typography = Typography,
        content = {
            CompositionLocalProvider(
                LocalRippleConfiguration provides DTRippleConfiguration,
                content
            )
        }
    )
}