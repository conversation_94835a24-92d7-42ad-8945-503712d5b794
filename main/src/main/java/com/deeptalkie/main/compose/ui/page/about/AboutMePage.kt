package com.deeptalkie.main.compose.ui.page.about

import androidx.activity.compose.LocalActivity
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.azhon.appupdate.listener.OnDownloadListener
import com.azhon.appupdate.manager.DownloadManager
import com.deeptalkie.main.R
import com.deeptalkie.main.compose.theme.SocialBlue
import com.deeptalkie.main.compose.ui.components.DTButton
import com.deeptalkie.main.compose.ui.components.DTPage
import com.deeptalkie.main.compose.ui.components.DTVerticalSpacer
import com.deeptalkie.main.compose.ui.components.RequestPermission
import com.deeptalkie.main.compose.ui.components.SVTopBar
import com.deeptalkie.main.compose.ui.dialog.RequestNotifyDialog
import com.deeptalkie.main.compose.utils.AppUtils
import com.deeptalkie.main.compose.utils.NotifyPermissionUtil
import com.deeptalkie.main.ext.showToast
import java.io.File

@Composable
fun AboutMePage(
    onBack: () -> Unit,
    viewModel: AboutViewModel = viewModel()
) {
    val context = LocalContext.current
    val activity = LocalActivity.current
    var notifyShow by remember { mutableStateOf(false) }
    var startRequestPermission by remember { mutableStateOf(false) }
    DTPage(
        background = R.drawable.bg_mine_about,
        loading = viewModel.loading,
        onDismissLoading = {
            viewModel.showLoading(false)
        }
    ) {
        Column(
            Modifier
                .statusBarsPadding()
                .navigationBarsPadding()
                .fillMaxSize()
        ) {
            SVTopBar(null, onBack = onBack)
            DTVerticalSpacer(45.dp)
            AppIcon()
            DTVerticalSpacer(20.dp)
            AppName()
            DTVerticalSpacer(8.dp)
            AppVersion()
            DTVerticalSpacer(50.dp)
            AppDesc()
            Spacer(Modifier.weight(1f))
            CheckUpdateBtn {
                viewModel.checkUpdate()
            }
            DTVerticalSpacer(18.dp)
            Copyright()
            DTVerticalSpacer(16.dp)
        }
    }

    viewModel.versionBean?.let {
        UpdateDialog(
            versionBean = it,
            onUpdate = {
                if (!NotifyPermissionUtil.checkNotificationPermission(context)) {
                    startRequestPermission = true
                }
                if (viewModel.isDownloading) {
                    showToast(context.getString(R.string.new_version_is_downloading))
                    return@UpdateDialog viewModel.showUpdateDialog(null)
                } else {
                    val url = it.link ?: ""
                    if (url.isEmpty()) return@UpdateDialog viewModel.showUpdateDialog(null)
                    val apkName = "${context.getString(R.string.app_name)}.apk"
                    if (activity == null) return@UpdateDialog viewModel.showUpdateDialog(null)
                    val manager = DownloadManager.Builder(activity).run {
                        apkUrl(url)
                        apkName(apkName)
                        smallIcon(R.drawable.ic_launcher)
                        onDownloadListener(object : OnDownloadListener {
                            override fun cancel() {
                                viewModel.isDownloading = false
                            }

                            override fun done(apk: File) {
                                viewModel.isDownloading = false
                            }

                            override fun downloading(max: Int, progress: Int) {
                            }

                            override fun error(e: Throwable) {
                                viewModel.isDownloading = false
                            }

                            override fun start() {
                                viewModel.isDownloading = true
                            }

                        })
                        build()
                    }
                    manager.download()
                    viewModel.showUpdateDialog(null)
                }
            },
            onDismiss = {
                viewModel.showUpdateDialog(null)
            }
        )
    }
    RequestNotifyDialog(
        notifyShow,
        onConfirm = {
            NotifyPermissionUtil.requestNotificationPermission(context)
        },
        onDismissRequest = {
            notifyShow = false
        },
    )

    RequestPermission(startRequestPermission = startRequestPermission) {
        notifyShow = true
    }
}

@Composable
private fun ColumnScope.AppIcon() {
    Image(
        painterResource(R.drawable.ic_launcher),
        null,
        Modifier
            .size(80.dp)
            .align(Alignment.CenterHorizontally)
    )
}

@Composable
private fun ColumnScope.AppName() {
    Text(
        stringResource(R.string.app_name),
        Modifier.align(Alignment.CenterHorizontally),
        style = MaterialTheme.typography.headlineLarge.copy(
            color = MaterialTheme.colorScheme.onPrimary
        )
    )
}

@Composable
private fun ColumnScope.AppVersion() {
    val context = LocalContext.current
    val version = buildString {
        append("${stringResource(R.string.version)} ${AppUtils.getVersion(context)}")
    }
    Text(
        version,
        Modifier.align(Alignment.CenterHorizontally),
        style = MaterialTheme.typography.bodySmall.copy(
            color = MaterialTheme.colorScheme.onPrimary
        )
    )
}

@Composable
private fun AppDesc() {
    Text(
        stringResource(R.string.about_note),
        Modifier
            .padding(horizontal = 20.dp)
            .fillMaxWidth(),
        style = MaterialTheme.typography.bodySmall.copy(
            color = MaterialTheme.colorScheme.onPrimary,
            lineHeight = 20.sp,
            textAlign = TextAlign.Center
        )
    )
}

@Composable
private fun CheckUpdateBtn(onClick: () -> Unit) {
    DTButton(
        stringResource(R.string.check_for_updates),
        background = SocialBlue,
        modifier = Modifier
            .padding(horizontal = 30.dp)
            .fillMaxWidth()
            .height(44.dp),
        onClick = onClick
    )
}

@Composable
private fun ColumnScope.Copyright() {
    Text(
        stringResource(R.string.all_right),
        Modifier.align(Alignment.CenterHorizontally),
        style = MaterialTheme.typography.labelMedium.copy(
            color = Color(0xFF8A8C91),
        )
    )
}



