package com.deeptalkie.main.compose.ui.page.product

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.deeptalkie.main.R
import com.deeptalkie.main.compose.theme.SocialBlue
import com.deeptalkie.main.compose.theme.White
import com.deeptalkie.main.compose.ui.modifier.click
import com.deeptalkie.main.compose.utils.ChildDesign
import com.deeptalkie.main.compose.utils.toAnnotatedStringParameters
import com.imyfone.membership.api.bean.ConfirmResultBean
import kotlinx.serialization.json.Json

/**
 *creater:linjinhao on 2025/5/20 18:41
 */

private val TAG_EMAIL = "TAG_EMAIL"

@Composable
fun PurchaseSuccessRoute(email: String, bean: String, onBack: () -> Unit) {
    val confirmResultBean = Json.decodeFromString<ConfirmResultBean>(bean)
    PurchaseSuccessScreen(email = email, bean = confirmResultBean, onBack)
}

@Composable
fun PurchaseSuccessScreen(email: String, bean: ConfirmResultBean, onBack: () -> Unit) {
    Box(
        modifier = Modifier.background(White)
            .fillMaxSize()
    ) {
        Column(
            modifier = Modifier
                .statusBarsPadding()
                .fillMaxSize()
        ) {
            Image(
                painterResource(R.drawable.ic_back2),
                contentDescription = "closeButton",
                modifier = Modifier
                    .padding(start = 16.dp)
                    .click {
                        onBack()
                    }
                    .size(40.dp),
                alignment = Alignment.TopStart
            )
            Spacer(modifier = Modifier.height(30.dp))
            Image(
                painterResource(R.drawable.ic_success),
                contentDescription = "icon",
                modifier = Modifier.size(36.dp),
                alignment = Alignment.Center
            )
            Spacer(modifier = Modifier.height(20.dp))
            Text(
                stringResource(R.string.payment_successful),
                style = TextStyle(
                    fontSize = 18.sp,
                    fontWeight = FontWeight.W800,
                    color = Color.Black
                )
            )
            Column(
                modifier = Modifier
                    .padding(start = 12.dp, end = 12.dp)
                    .background(
                        colorResource(R.color.colorViewBackground),
                        shape = RoundedCornerShape(16.dp)
                    )
                    .fillMaxWidth()
                    .padding(top = 20.dp, bottom = 20.dp)
            ) {
                ItemDetail(stringResource(R.string.product_name), bean.skuName ?: "")
                Spacer(modifier = Modifier.height(16.dp))
                ItemDetail(stringResource(R.string.order_number), bean.orderNo ?: "")
                Spacer(modifier = Modifier.height(16.dp))
                ItemDetail(stringResource(R.string.total_text), "${bean.currencyCode}${bean.price}")
                Spacer(modifier = Modifier.height(16.dp))
                ItemDetail(
                    "Auto-renewal",
                    if (bean.isSubscribe == 1) stringResource(R.string.active) else stringResource(R.string.disabled)
                )
            }
            Spacer(modifier = Modifier.height(24.dp))
            Text(
                text = sendEmailAnnotationString(email),
                style = TextStyle(fontSize = 14.sp, color = colorResource(R.color.colorTextAccent))
            )
        }
    }
}

@Composable
fun ItemDetail(title: String, context: String) {
    Row(modifier = Modifier.fillMaxWidth()) {
        Text(
            text = title,
            style = TextStyle(fontSize = 14.sp, color = colorResource(R.color.colorTextAccent))
        )
        Text(
            text = context,
            style = TextStyle(
                fontSize = 14.sp,
                color = colorResource(R.color.colorTextAccent),
                textAlign = TextAlign.End
            ),
            modifier = Modifier.weight(1f)
        )
    }
}

@Composable
fun sendEmailAnnotationString(email: String): AnnotatedString {
    val mainStr = stringResource(R.string.email_has_send)
    return remember(email) {
        mainStr.toAnnotatedStringParameters(
            ChildDesign(
                childString = email,
                annotatedTag = TAG_EMAIL,
                spanStyle = SpanStyle(
                    color = SocialBlue,
                    fontWeight = FontWeight.W600
                )
            )
        )
    }
}
