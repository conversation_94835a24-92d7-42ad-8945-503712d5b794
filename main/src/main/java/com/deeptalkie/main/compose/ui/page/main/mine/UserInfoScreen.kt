package com.deeptalkie.main.compose.ui.page.main.mine

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.deeptalkie.main.R
import com.deeptalkie.main.compose.theme.SocialBlue
import com.deeptalkie.main.compose.theme.White
import com.deeptalkie.main.compose.theme.White12
import com.deeptalkie.main.compose.ui.components.DTButton
import com.deeptalkie.main.compose.ui.components.DTLoading
import com.deeptalkie.main.compose.ui.dialog.AccountExistPermissionDialog
import com.deeptalkie.main.compose.ui.dialog.AccountExistSubscriptPermissionDialog
import com.deeptalkie.main.compose.ui.dialog.CancelAccountDialogTip
import com.deeptalkie.main.compose.ui.dialog.CancelAccountSuccessDialog
import com.deeptalkie.main.compose.ui.dialog.VerifyCodeAccountDialog
import com.deeptalkie.main.compose.ui.modifier.click
import com.deeptalkie.main.ext.showToast
import com.imyfone.membership.api.bean.MemberBean
import kotlinx.coroutines.flow.SharedFlow


private val Type_First_Name = "First_Name"
private val Type_Last_Name = "Last_Name"

/**
 *creater:linjinhao on 2025/5/27 09:50
 */
@Composable
fun UserInfoRoute(
    viewModel: SvUserInfoViewModel = viewModel(),
    onBack: () -> Unit,
    onChangeLastName: (type: String) -> Unit,
    onChangeFirstName: (type: String) -> Unit
) {
    var showExistPermissionDialog by remember { mutableStateOf(false) }
    var showExistSubscriptPermissionDialog by remember { mutableStateOf(false) }
    var sendCodeDialogShow by remember { mutableStateOf(false) } //发送验证码弹窗
    var cancelAccountSuccessDialogShow by remember { mutableStateOf(false) } //注销成功弹窗
    var verifyCode by remember { mutableStateOf("") } //验证码
    var codeTip by remember { mutableStateOf("") } //验证码的提示
    UserInfoScreen(
        viewModel = viewModel,
        showExistPermissionDialog = showExistPermissionDialog,
        sendCodeDialogShow = sendCodeDialogShow,
        onBack = onBack,
        onChangeExistPermissionDialog = { showExistPermissionDialog = it },
        onChangeLastName = onChangeLastName,
        onChangeFirstName = onChangeFirstName,
        onChangeSendCodeDialogShow = { sendCodeDialogShow = it },
        verifyCode = verifyCode,
        onChangeVerifyCode = {
            verifyCode = it
            codeTip = ""
        },
        codeTip = codeTip,
        cancelAccountSuccess = cancelAccountSuccessDialogShow,
        onChangeCancelAccountSuccessDialogShow = {
            cancelAccountSuccessDialogShow = it
        },
        showExistSubscriptPermissionDialog = showExistSubscriptPermissionDialog,
        onChangeExistSubscriptPermissionDialog = {
            showExistSubscriptPermissionDialog = it
        }
    )
    //事件处理
    val context = LocalContext.current
    HandleLogout(viewModel.logoutEvent, onBack = { onBack() })
    HandleUserInfoEvent(
        viewModel.userInformationEvent,
        onAccountExitsPermission = { showExistPermissionDialog = it },
        onAccountExitNoPermission = {
            sendCodeDialogShow = it
        },
        onAccountExitsSubscriptPermission = {
            showExistSubscriptPermissionDialog = it
        },
        onCancelAccountSuccess = {
            cancelAccountSuccessDialogShow = true
            sendCodeDialogShow = false
        },
        onCancelAccountFail = {
            showToast(context.getString(R.string.network_error))
        },
        onCodeInputInvalid = {
            codeTip =
                context.getString(R.string.code_is_incorrect_please_enter_the_verification_code_received_by_your_email)
        },
        onGetCodeError = {
            showToast(context.getString(R.string.network_error))
        },
        onGetCodeSuccess = {
            showToast(String.format(context.getString(R.string.code_send_success), it))
        },
        onGetCodeToMany = {
            showToast(it)
        },
    )
}

@Composable
private fun UserInfoScreen(
    viewModel: SvUserInfoViewModel = viewModel(),
    verifyCode: String,
    codeTip: String,
    onChangeVerifyCode: (code: String) -> Unit,
    cancelAccountSuccess: Boolean,
    onChangeCancelAccountSuccessDialogShow: (show: Boolean) -> Unit,
    showExistPermissionDialog: Boolean,
    showExistSubscriptPermissionDialog: Boolean,
    sendCodeDialogShow: Boolean,
    onChangeExistPermissionDialog: (show: Boolean) -> Unit,
    onChangeExistSubscriptPermissionDialog: (show: Boolean) -> Unit,
    onChangeSendCodeDialogShow: (show: Boolean) -> Unit,
    onBack: () -> Unit,
    onChangeFirstName: (type: String) -> Unit,
    onChangeLastName: (type: String) -> Unit
) {
    val member by viewModel.member.collectAsState(null)
    Box(
        modifier = Modifier
            .background(color = colorResource(R.color.color_13111B))
            .fillMaxSize()
    ) {
        var logoutDialogShow by remember { mutableStateOf(false) } //退出登录弹窗
        var cancelAccountTipShow by remember { mutableStateOf(false) } //注销提示弹窗
        val state = rememberScrollState()
        Column(
            modifier = Modifier
                .statusBarsPadding()
                .verticalScroll(state)
        ) {
            Spacer(modifier = Modifier.height(20.dp))
            val context = LocalContext.current
            TitleTopBar(onBack = onBack)
            Spacer(modifier = Modifier.height(32.dp))
            ItemAvatar()
            Spacer(modifier = Modifier.height(13.dp))
            HorizontalDivider(
                modifier = Modifier
                    .padding(horizontal = 20.dp, vertical = 0.dp)
                    .fillMaxWidth(),
                thickness = 0.5.dp,
                color = White12
            )
            ItemUserEmail(member, viewModel)
            HorizontalDivider(
                modifier = Modifier
                    .padding(horizontal = 20.dp, vertical = 0.dp)
                    .fillMaxWidth(),
                thickness = 0.5.dp,
                color = White12
            )
            ItemUserInfo(
                context.getString(R.string.first_name),
                member?.firstName ?: "",
                onChangeValue = { onChangeFirstName(Type_First_Name) }
            )
            HorizontalDivider(
                modifier = Modifier
                    .padding(horizontal = 20.dp, vertical = 0.dp)
                    .fillMaxWidth(),
                thickness = 0.5.dp,
                color = White12
            )
            ItemUserInfo(
                context.getString(R.string.last_name),
                member?.lastName ?: "",
                onChangeValue = { onChangeLastName(Type_Last_Name) }
            )
            HorizontalDivider(
                modifier = Modifier
                    .padding(horizontal = 20.dp, vertical = 0.dp)
                    .fillMaxWidth(),
                thickness = 0.5.dp,
                color = White12
            )
            Spacer(modifier = Modifier.height(270.dp))
            DTButton(
                R.string.login_out,
                contentColor = White,
                containerColor = SocialBlue,
                shape = RoundedCornerShape(22.dp),
                modifier = Modifier
                    .padding(horizontal = 20.dp, vertical = 0.dp)
                    .fillMaxWidth()
                    .height(46.dp)
            ) {
                logoutDialogShow = true
            }
            Spacer(modifier = Modifier.height(16.dp))

            DTButton(
                R.string.delete_account,
                border = BorderStroke(width = 1.dp, color = SocialBlue),
                contentColor = SocialBlue,
                shape = RoundedCornerShape(22.dp),
                modifier = Modifier
                    .padding(horizontal = 20.dp, vertical = 0.dp)
                    .fillMaxWidth()
                    .height(46.dp)
            ) {
                cancelAccountTipShow = true
            }
            Spacer(modifier = Modifier.height(40.dp))
        }

        val loading by viewModel.state.collectAsState()
        DTLoading(loading = loading) { }
        com.deeptalkie.main.compose.ui.dialog.LogoutDialog(show = logoutDialogShow, onConfirm = {
            viewModel.logout()
        }) { logoutDialogShow = false }

        CancelAccountDialogTip(show = cancelAccountTipShow, onConfirm = {
            viewModel.checkPermissionStatus()
        }) {
            cancelAccountTipShow = false
        }
        val user by viewModel.user.collectAsState(null)
        AccountExistPermissionDialog(
            show = showExistPermissionDialog,
            email = user?.email ?: "",
            onConfirm = {
                onChangeSendCodeDialogShow(true)
            }) {
            onChangeExistPermissionDialog(false)
        }
        val lastGetCodeTime by viewModel.setSendVerificationCodeTime.collectAsState(-1L)
        val localFocusManager = LocalFocusManager.current
        VerifyCodeAccountDialog(
            show = sendCodeDialogShow,
            email = user?.email ?: "",
            codeTip = codeTip,
            lastGetCodeTime = lastGetCodeTime,
            onConfirm = {
                viewModel.cancelAccount(code = verifyCode)
            },
            getCode = { viewModel.getCode(member?.noticeEmail ?: "", member?.email ?: "") },
            verifyCode = verifyCode,
            onChangeVerifyCode = { onChangeVerifyCode(it) }
        ) {
            localFocusManager.clearFocus()
            onChangeSendCodeDialogShow(false)
        }
        CancelAccountSuccessDialog(
            cancelAccountSuccess,
            onConfirm = { onBack() },
        ) {
            onChangeCancelAccountSuccessDialogShow(false)
        }

        AccountExistSubscriptPermissionDialog(
            show = showExistSubscriptPermissionDialog,
            email = member?.email ?: ""
        ) {
            onChangeExistSubscriptPermissionDialog(false)
        }
    }
}

@Composable
private fun TitleTopBar(onBack: () -> Unit) {
    Row(
        modifier = Modifier
            .padding(horizontal = 20.dp)
            .fillMaxSize(),
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Image(
            painterResource(R.drawable.ic_back2),
            contentDescription = "BackIcon",
            modifier = Modifier
                .click { onBack() }
                .size(36.dp)
        )
        Spacer(modifier = Modifier.width(5.dp))
        Text(
            stringResource(R.string.personal_information),
            style = TextStyle(
                textAlign = TextAlign.Center,
                fontSize = 16.sp,
                fontWeight = FontWeight.W600,
                color = White
            ),
            modifier = Modifier.weight(1f, true)
        )
        Spacer(modifier = Modifier.width(5.dp))
        Spacer(modifier = Modifier.size(36.dp))
    }
}

@Composable
private fun ItemAvatar() {
    Row(
        modifier = Modifier
            .padding(horizontal = 20.dp)
            .fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center
    ) {
        Text(
            stringResource(R.string.profile_image),
            style = TextStyle(fontSize = 14.sp, fontWeight = FontWeight.W500, color = White),
            modifier = Modifier.weight(1f)
        )
        Image(
            painterResource(R.drawable.image_avatar_sex_non),
            contentDescription = "avatar",
            modifier = Modifier.size(30.dp)
        )
    }
}

@Composable
private fun ItemUserEmail(member: MemberBean?, viewModel: SvUserInfoViewModel = viewModel()) {
    Row(
        Modifier
            .padding(horizontal = 20.dp)
            .fillMaxWidth()
            .padding(horizontal = 0.dp, vertical = 20.dp),
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            stringResource(R.string.account_email),
            style = TextStyle(fontSize = 14.sp, fontWeight = FontWeight.W500, color = White),
            modifier = Modifier.weight(1f, true), maxLines = 1
        )
        Row(
            modifier = Modifier
                .weight(1f),
            horizontalArrangement = Arrangement.End
        ) {
            val isVip by viewModel.vipStateFlow.collectAsState(false)
//            val isVip by remember(member) { mutableStateOf(member?.vipTypeOfProduct == 2) }
            Image(
                painterResource(if (isVip) R.drawable.ic_vip else R.drawable.ic_vip_white_ex),
                contentDescription = "icon",
                modifier = Modifier.size(18.dp)
            )
            Spacer(modifier = Modifier.width(2.dp))
            Text(
                text = member?.email ?: "",
                style = TextStyle(
                    fontSize = 14.sp,
                    fontWeight = FontWeight.W500,
                    color = colorResource(R.color.color_8A8C91)
                ),
                maxLines = 1
            )
        }
    }
}

@Composable
private fun ItemUserInfo(
    title: String,
    content: String,
    onChangeValue: () -> Unit
) {
    Row(
        Modifier
            .padding(horizontal = 20.dp)
            .fillMaxWidth(),
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = title,
            style = TextStyle(fontSize = 14.sp, fontWeight = FontWeight.W500, color = White),
            modifier = Modifier
                .weight(1f, true)
                .padding(horizontal = 0.dp, vertical = 20.dp),
            maxLines = 1
        )
        Text(
            text = content,
            style = TextStyle(
                textAlign = TextAlign.End,
                fontSize = 14.sp,
                fontWeight = FontWeight.W500,
                color = colorResource(R.color.color_8A8C91)
            ), modifier = Modifier
                .click {
                    onChangeValue()
                }
                .weight(1f, true)
                .padding(horizontal = 0.dp, vertical = 20.dp),
            maxLines = 1
        )
    }
}

@Composable
private fun HandleUserInfoEvent(
    event: SharedFlow<UserInformationEvent>,
    onAccountExitsPermission: (show: Boolean) -> Unit,
    onAccountExitNoPermission: (show: Boolean) -> Unit,
    onAccountExitsSubscriptPermission: (show: Boolean) -> Unit,
    onCancelAccountSuccess: () -> Unit,
    onCancelAccountFail: () -> Unit,
    onCodeInputInvalid: () -> Unit,
    onGetCodeError: () -> Unit,
    onGetCodeSuccess: (email: String) -> Unit,
    onGetCodeToMany: (msg: String) -> Unit
) {
    val context = LocalContext.current
    LaunchedEffect(event) {
        event.collect {
            when (it) {
                UserInformationEvent.AccountExitNoPermission -> {
                    onAccountExitNoPermission(true)
                }

                UserInformationEvent.AccountExitsPermission -> {
                    onAccountExitsPermission(true)
                }

                UserInformationEvent.AccountExitsSubscriptPermission -> {
                    onAccountExitsSubscriptPermission(true)
                }

                UserInformationEvent.CancelAccountFail -> {
                    onCancelAccountFail()
                }

                UserInformationEvent.CancelAccountSuccess -> {
                    onCancelAccountSuccess()
                }

                UserInformationEvent.CommonError -> {
                    showToast(context.getString(R.string.network_error))
                }

                UserInformationEvent.CodeInputInvalid -> {
                    onCodeInputInvalid()
                }

                UserInformationEvent.GetCodeError -> {
                    onGetCodeError()
                }

                is UserInformationEvent.GetCodeSuccess -> {
                    onGetCodeSuccess(it.email)
                }

                is UserInformationEvent.GetCodeToMany -> {
                    onGetCodeToMany(it.msg)
                }
            }
        }
    }
}

@Composable
private fun HandleLogout(event: SharedFlow<LogoutEvent>, onBack: () -> Unit) {
    LaunchedEffect(event) {
        event.collect {
            when (it) {
                LogoutEvent.Logout -> {
                    onBack()
                }
            }
        }
    }
}
