package com.deeptalkie.main.compose.ui.page.main.createrole

import androidx.room.Transaction
import com.clevguard.utils.ext.logw
import com.deeptalkie.kidsguard.net.getSuccessData
import com.deeptalkie.kidsguard.net.runHttp
import com.deeptalkie.main.bean.UserManager
import com.deeptalkie.main.bean.VoiceLink
import com.deeptalkie.main.repo.BaseDeepTalkieRepo

class SelectVoiceRepo : BaseDeepTalkieRepo() {
    fun getRecommendedVoiceFlow() = voiceDao.getRecommendedVoiceFlow()
    fun getFemaleVoiceFlow() = voiceDao.getFemaleVoiceFlow()
    fun getMaleVoiceFlow() = voiceDao.getMaleVoiceFlow()

    @Transaction
    suspend fun refreshLocalVoices() {
        val language = UserManager.getLanguage()
        val langId = language.toVoiceLangId()
        val remoteVoices = runHttp { commonApi.getVoices(langId) }?.getSuccessData()
        when {
            remoteVoices == null -> {
                logw("刷新本地语音失败：${remoteVoices}")
            }

            remoteVoices.isEmpty() -> {
                logw("远端语音列表为空")
                voiceDao.deleteAll()
            }

            else -> {
                // 删除本地不存在的语音
                voiceDao.deleteNotInIds(remoteVoices.map { it.id })
                voiceDao.upsertAll(remoteVoices.map { it.toVoice() })
            }
        }
    }

    suspend fun requestVoiceLink(langId: Long, voice: String): VoiceLink? {
        return runHttp { commonApi.getVoiceLink(langId, voice) }?.getSuccessData()
    }
}