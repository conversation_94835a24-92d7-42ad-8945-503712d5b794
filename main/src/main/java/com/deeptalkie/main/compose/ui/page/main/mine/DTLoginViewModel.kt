package com.deeptalkie.main.compose.ui.page.main.mine

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.deeptalkie.kidsguard.net.util.EncryptedUtil
import com.deeptalkie.main.Membership
import com.deeptalkie.main.bean.BindAccountBean
import com.deeptalkie.main.config.Constant
import com.imyfone.membership.api.bean.Auth_PROVIDER_GOOGLE
import com.imyfone.membership.api.bean.OAUTH_GOOGLE_LOGIN
import com.imyfone.membership.api.bean.OAUTH_OPERATE_LOGIN
import com.imyfone.membership.api.bean.OAuthInfo
import com.imyfone.membership.api.bean.UserBean.Companion.BIND_TYPE_BIND
import com.imyfone.membership.api.bean.UserBean.Companion.BIND_TYPE_UN_BIND
import com.imyfone.membership.ext.googlelogin.GoogleLoginData
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import java.net.URLEncoder

/**
 *creater:linjinhao on 2025/5/15 18:48
 */
class DTLoginViewModel : ViewModel() {
    private val account = Membership.membershipClient.account

    private val _loadingState = MutableStateFlow(LoginUIState())
    val loadingState = _loadingState.asStateFlow()

    private val _loginEvent = MutableSharedFlow<LoginEvent>()
    val loginEvent = _loginEvent.asSharedFlow()

    fun login(email: String, pwd: String) {
        viewModelScope.launch {
            _loadingState.update { it.copy(loading = true) }
            val pwdStr = EncryptedUtil.md5(pwd)
            val response = account.login(email = email, password = pwdStr)
            _loadingState.update { it.copy(loading = false) }
            if (response.isSuccess) {
                Membership.loginSuccess()
                _loginEvent.emit(LoginEvent.LoginSuccess)
            } else if (response.code == 410) {
                _loginEvent.emit(LoginEvent.AccountNotExist)
            } else if (response.code == 409) {
                _loginEvent.emit(LoginEvent.PasswordInvalid)
            } else {
                _loginEvent.emit(LoginEvent.CommonError)
            }
        }
    }


    private val _googleLoginState = MutableStateFlow(false)
    val googleLoginState = _googleLoginState.asStateFlow()
    private val _googleLoginEvent = MutableSharedFlow<SVGoogleLoginEvent>()
    val googleLoginEvent = _googleLoginEvent.asSharedFlow()

    fun googleAuthLogin(bean: GoogleLoginData) {
        viewModelScope.launch {
            _googleLoginState.emit(true)
            val oAuthInfo = OAuthInfo(
                openId = bean.id ?: "",
                username = bean.displayName ?: "",
                stateID = bean.googleIdToken ?: "",
                idToken = bean.googleIdToken ?: "",
                email = bean.id,
                headPhoto = bean.profilePictureUri?.toString() ?: "",
                firstName = bean.givenName,
                lastName = bean.familyName,
                softwareCode = null,
                sourceSite = Constant.fromSite
            )
            val response = account.oAuthLogin(
                operateType = OAUTH_OPERATE_LOGIN,
                provider = OAUTH_GOOGLE_LOGIN,
                oauthInfo = oAuthInfo
            )
            _googleLoginState.emit(false)
            val data = response.data
            if (response.isSuccess && data != null && data.isBind != null) {
                if (data.isBind == BIND_TYPE_UN_BIND || data.isBind == BIND_TYPE_BIND) {
                    _googleLoginEvent.emit(
                        SVGoogleLoginEvent.BindEmail(
                            BindAccountBean(
                                avatar = URLEncoder.encode(bean.profilePictureUri.toString()),
                                thirdEmail = bean.id ?: "",
                                displayName = bean.displayName ?: "",
                                provider = Auth_PROVIDER_GOOGLE,
                                state = bean.googleIdToken ?: ""
                            )
                        )
                    )
                } else {
                    Membership.loginSuccess()
                    _googleLoginEvent.emit(SVGoogleLoginEvent.LoginSuccess)
                }
            } else {
                _googleLoginEvent.emit(SVGoogleLoginEvent.CommonError)
            }
        }
    }
}

data class LoginUIState(
    val loading: Boolean = false
)

sealed interface LoginEvent {
    data object LoginSuccess : LoginEvent
    data object AccountNotExist : LoginEvent
    data object PasswordInvalid : LoginEvent
    data object CommonError : LoginEvent
}


sealed interface SVGoogleLoginEvent {
    data object LoginSuccess : SVGoogleLoginEvent
    data class BindEmail(val bean: BindAccountBean) : SVGoogleLoginEvent
    data object CommonError : SVGoogleLoginEvent
}