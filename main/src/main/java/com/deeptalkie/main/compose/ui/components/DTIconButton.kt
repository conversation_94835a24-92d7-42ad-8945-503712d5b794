package com.deeptalkie.main.compose.ui.components

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.TextAutoSize
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonColors
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.ButtonElevation
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.deeptalkie.main.compose.theme.Transparent

val DTButtonTextStyle: TextStyle
    @Composable get() = MaterialTheme.typography.headlineLarge.copy(
        fontSize = 16.sp
    )

@Composable
fun DTIconButton(
    text: String,
    brush: Brush,
    modifier: Modifier = Modifier,
    icon: Painter? = null,
    iconSize: Dp = 24.dp,
    enable: Boolean = true,
    shape: Shape = RoundedCornerShape(50),
    elevation: Dp = 0.dp,
    textStyle: TextStyle = DTButtonTextStyle,
    onClick: () -> Unit,
) {
    Button(
        onClick, modifier, enable,
        colors = ButtonDefaults.buttonColors(
            containerColor = Transparent,
            contentColor = MaterialTheme.colorScheme.onPrimary,
            disabledContainerColor = Transparent,
            disabledContentColor = MaterialTheme.colorScheme.onPrimary,
        ),
        shape = shape,
        elevation = ButtonDefaults.elevatedButtonElevation(
            elevation, elevation, elevation, elevation,
        ),
        contentPadding = PaddingValues(0.dp)
    ) {
        Row(
            Modifier
                .fillMaxSize()
                .background(brush),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center
        ) {
            if (icon != null) {
                Icon(icon, text, Modifier.size(iconSize))
                DTHorizontalSpacer(8.dp)
            }
            Text(text = text, style = textStyle)
        }
    }
}

@Composable
fun DTIconButton(
    text: String,
    bg: SelectableDrawable,
    modifier: Modifier = Modifier,
    icon: Painter? = null,
    iconSize: Dp = 24.dp,
    enable: Boolean = true,
    shape: Shape = RoundedCornerShape(50),
    elevation: Dp = 0.dp,
    textStyle: TextStyle = DTButtonTextStyle,
    onClick: () -> Unit,
) {
    Button(
        onClick, modifier, enable,
        colors = ButtonDefaults.buttonColors(
            containerColor = Transparent,
            contentColor = MaterialTheme.colorScheme.onPrimary,
            disabledContainerColor = Transparent,
            disabledContentColor = MaterialTheme.colorScheme.onPrimary,
        ),
        shape = shape,
        elevation = ButtonDefaults.elevatedButtonElevation(
            elevation, elevation, elevation, elevation,
        ),
        contentPadding = PaddingValues(0.dp)
    ) {
        val bgImage = if (enable) bg.enableDrawable else bg.disableDrawable
        Box(Modifier.fillMaxSize()) {
            Image(
                painterResource(bgImage),
                null,
                Modifier.fillMaxSize(),
                contentScale = ContentScale.FillBounds
            )
            Row(
                Modifier.fillMaxSize(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Center
            ) {
                if (icon != null) {
                    Icon(icon, text, Modifier.size(iconSize))
                    DTHorizontalSpacer(8.dp)
                }
                Text(text = text, style = textStyle)
            }
        }
    }
}

object DTButtonDefaults {
    val ContentPadding = PaddingValues(horizontal = 10.dp)
    val shape = RoundedCornerShape(50)

    @Composable
    fun buttonColors(
        containerColor: Color = MaterialTheme.colorScheme.primary,
        contentColor: Color = MaterialTheme.colorScheme.onPrimary,
        disabledContainerColor: Color = Color(0xFF636364),
        disabledContentColor: Color = MaterialTheme.colorScheme.onPrimary,
    ) = ButtonDefaults.buttonColors(
        containerColor = containerColor,
        contentColor = contentColor,
        disabledContainerColor = disabledContainerColor,
        disabledContentColor = disabledContentColor,
    )

    @Composable
    fun buttonElevation(
        defaultElevation: Dp = 0.dp,
        pressedElevation: Dp = 0.dp,
        focusedElevation: Dp = 0.dp,
        hoveredElevation: Dp = 0.dp,
        disabledElevation: Dp = 0.dp,
    ) = ButtonDefaults.buttonElevation(
        defaultElevation, pressedElevation, focusedElevation, hoveredElevation, disabledElevation
    )

    val textStyle @Composable get() = DTButtonTextStyle

    @Composable
    fun autoSize(
        minFontSize: TextUnit = 5.sp,
        maxFontSize: TextUnit = 16.sp,
        stepSize: TextUnit = 0.25.sp
    ) = TextAutoSize.StepBased(
        minFontSize = minFontSize,
        maxFontSize = maxFontSize,
        stepSize = stepSize
    )
}

@Composable
fun DTIconButton(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    shape: Shape = DTButtonDefaults.shape,
    colors: ButtonColors = DTButtonDefaults.buttonColors(),
    elevation: ButtonElevation? = DTButtonDefaults.buttonElevation(),
    border: BorderStroke? = null,
    contentPadding: PaddingValues = DTButtonDefaults.ContentPadding,
    interactionSource: MutableInteractionSource? = null,
    content: @Composable RowScope.() -> Unit
) {
    Button(
        onClick, modifier, enabled,
        shape, colors, elevation,
        border, contentPadding, interactionSource,
        content
    )
}