package com.deeptalkie.main.compose.utils

import android.content.Context
import android.content.Intent
import androidx.core.app.NotificationManagerCompat

/**
 *creater:l<PERSON><PERSON><PERSON> on 2025/6/20 15:23
 */
object NotifyPermissionUtil {
    /**
     * 申请通知权限
     */
    fun requestNotificationPermission(context: Context) {
        val intent = Intent();
        intent.action = "android.settings.APP_NOTIFICATION_SETTINGS";
        intent.putExtra("app_package", context.packageName);
        intent.putExtra("app_uid", context.applicationInfo.uid)
        // for Android 8 and above
        intent.putExtra("android.provider.extra.APP_PACKAGE", context.packageName);
        context.startActivity(intent)
    }

    /**
     * 检查是否有通知权限
     */
    fun checkNotificationPermission(context: Context): <PERSON>olean {
        return NotificationManagerCompat.from(context).areNotificationsEnabled()
    }
}