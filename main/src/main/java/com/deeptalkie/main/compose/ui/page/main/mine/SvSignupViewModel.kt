package com.deeptalkie.main.compose.ui.page.main.mine

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.deeptalkie.kidsguard.net.util.EncryptedUtil
import com.deeptalkie.main.Membership
import com.deeptalkie.main.config.Constant
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 *creater:linjin<PERSON> on 2025/5/21 16:45
 */
class SvSignupViewModel : ViewModel() {
    val account = Membership.membershipClient.account
    private val _loadingState = MutableStateFlow(SignUpUIState())
    val loadingState = _loadingState.asStateFlow()

    private val _signUpEvent = MutableSharedFlow<SignupEvent>()
    val signUpEvent = _signUpEvent.asSharedFlow()

    fun signUp(email: String, pwd: String) {
        viewModelScope.launch {
            _loadingState.emit(SignUpUIState(true))
            val pwdStr = EncryptedUtil.md5(pwd)
            val response = account.register(
                email = email,
                password = pwdStr,
                firstName = "",
                lastName = "",
                source = Constant.fromSite
            )
            _loadingState.emit(SignUpUIState(false))
            if (response.isSuccess) {
                Membership.loginSuccess()
                _signUpEvent.emit(SignupEvent.SignupSuccess)
            } else if (response.code == 406) {
                _signUpEvent.emit(SignupEvent.AccountHasBeenRegistered)
            } else {
                _signUpEvent.emit(SignupEvent.CommonError)
            }
        }
    }
}

data class SignUpUIState(
    val loading: Boolean = false
)


sealed interface SignupEvent {
    object SignupSuccess : SignupEvent

    /**
     * 账号已被注册
     */
    object AccountHasBeenRegistered : SignupEvent

    object CommonError : SignupEvent
}
