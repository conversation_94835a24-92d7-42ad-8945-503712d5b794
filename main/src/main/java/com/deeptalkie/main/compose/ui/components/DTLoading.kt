package com.deeptalkie.main.compose.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.LottieConstants
import com.airbnb.lottie.compose.rememberLottieComposition

@Composable
fun DTLoading(
    loading: Boolean,
    onDismissRequest: () -> Unit,
) {
    if (loading) {
        Dialog(
            onDismissRequest = onDismissRequest,
            DialogProperties(usePlatformDefaultWidth = false)
        ) {
            BasicLoading(
                Modifier
                    .size(72.dp)
                    .background(
                        Color(0xFF4B4B51),
                        RoundedCornerShape(15.dp)
                    )
            )
        }
    }
}

@Composable
fun BasicLoading(modifier: Modifier = Modifier) {
    val composition by rememberLottieComposition(LottieCompositionSpec.Asset("animations/loading.json"))
    LottieAnimation(
        composition,
        modifier,
        iterations = LottieConstants.IterateForever
    )
}

@Composable
fun MsgLoading(modifier: Modifier = Modifier) {
    val composition by rememberLottieComposition(LottieCompositionSpec.Asset("animations/waitmessage.json"))
    LottieAnimation(
        composition,
        modifier,
        iterations = LottieConstants.IterateForever
    )
}

@Composable
fun BasicVoiceLoading(
    isPlaying: Boolean,
    modifier: Modifier = Modifier
) {
    val composition by rememberLottieComposition(LottieCompositionSpec.Asset("animations/audio_play.json"))
    LottieAnimation(
        composition,
        modifier,
        isPlaying = isPlaying,
        iterations = LottieConstants.IterateForever
    )
}

@Composable
fun AIImageResultLoading(
    modifier: Modifier = Modifier
) {
    val composition by rememberLottieComposition(LottieCompositionSpec.Asset("animations/ai_image_waitting.json"))
    LottieAnimation(
        composition,
        modifier,
        iterations = LottieConstants.IterateForever
    )
}

@Composable
fun ThreeDotsLoading(
    modifier: Modifier = Modifier
) {
    val composition by rememberLottieComposition(LottieCompositionSpec.Asset("animations/jumping_dots.json"))
    LottieAnimation(
        composition,
        modifier,
        iterations = LottieConstants.IterateForever
    )
}
@Composable
fun BasicLoadMore(modifier: Modifier = Modifier) {
    val composition by rememberLottieComposition(LottieCompositionSpec.Asset("animations/load_more_loading.lottie"))
    LottieAnimation(
        composition,
        modifier,
        iterations = LottieConstants.IterateForever
    )
}