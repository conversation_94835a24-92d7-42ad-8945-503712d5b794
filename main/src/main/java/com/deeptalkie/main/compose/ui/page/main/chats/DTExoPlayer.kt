package com.deeptalkie.main.compose.ui.page.main.chats

import android.view.Surface
import androidx.compose.foundation.AndroidEmbeddedExternalSurface
import androidx.compose.foundation.AndroidExternalSurface
import androidx.compose.foundation.AndroidExternalSurfaceScope
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.deeptalkie.main.compose.ui.page.main.video.ExoPlayerController

@Composable
fun ComposeExoPlayer(
    player: ExoPlayerController,
    modifier: Modifier = Modifier,
    surfaceType: SurfaceType = SurfaceType.Texture,
) {
    val onSurfaceCreated: (Surface) -> Unit = { surface -> player.setVideoSurface(surface) }
    val onSurfaceDestroyed: () -> Unit = { player.setVideoSurface(null) }
    val onSurfaceInitialized: AndroidExternalSurfaceScope.() -> Unit = {
        onSurface { surface, _, _ ->
            onSurfaceCreated(surface)
            surface.onDestroyed { onSurfaceDestroyed() }
        }
    }

    when (surfaceType) {
        SurfaceType.Surface -> AndroidExternalSurface(modifier, onInit = onSurfaceInitialized)
        SurfaceType.Texture -> AndroidEmbeddedExternalSurface(
            modifier, onInit = onSurfaceInitialized
        )
    }
}

enum class SurfaceType {
    Surface, Texture
}