package com.deeptalkie.main.compose.utils

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue

interface ILoadingState {
    val loading: Boolean
    fun showLoading(loading: Boolean)
}

class LoadingState : ILoadingState {
    private var _loading by mutableStateOf(false)
    override val loading: Boolean
        get() = _loading

    override fun showLoading(loading: Boolean) {
        _loading = loading
    }
}

fun loadingState() = LoadingState()