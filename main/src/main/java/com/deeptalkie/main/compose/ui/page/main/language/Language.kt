package com.deeptalkie.main.compose.ui.page.main.language

import java.util.Locale

/**
 * @param id: 语言id，和标准语言的标识符一致
 * @param backendId: 后端语言id
 * @param title: 语言名称
 * @param locale: 语言区域
 */
enum class AppSupportedLanguage(
    val id: String,
    val backendId: String,
    val title: String,
    val locale: Locale,
) {
    ENGLISH("en", "en", "English", Locale.ENGLISH),
    CHINESE_TW("zh_TW", "tw", "繁体中文", Locale.TAIWAN),
    JAPANESE("ja", "ja", "日本語", Locale.JAPANESE),
    HINDI("hi", "hi", "हिंदी", Locale.forLanguageTag("hi")),
    INDONESIA("id", "id", "Indonesia", Locale.forLanguageTag("id")),
    THAI("th", "th", "ภาษาไทย", Locale.forLanguageTag("th")),
    VIETNAM("vi", "vn", "Tiếng Việt", Locale.forLanguageTag("vi")),
    MELAYU("ms", "ms", "Melayu", Locale.forLanguageTag("ms"));

    fun toVoiceLangId(): Long {
        return when (this) {
            ENGLISH -> 1L
            CHINESE_TW -> 12L
            JAPANESE -> 3L
            else -> 1L
        }
    }
}