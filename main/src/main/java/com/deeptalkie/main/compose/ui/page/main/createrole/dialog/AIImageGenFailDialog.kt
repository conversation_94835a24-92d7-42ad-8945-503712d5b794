package com.deeptalkie.main.compose.ui.page.main.createrole.dialog

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.deeptalkie.main.R
import com.deeptalkie.main.compose.theme.Black100
import com.deeptalkie.main.compose.ui.components.DTButton
import com.deeptalkie.main.compose.ui.components.DTButtonTextStyle
import com.deeptalkie.main.compose.ui.components.DTHorizontalSpacer
import com.deeptalkie.main.compose.ui.components.DTVerticalSpacer
import com.deeptalkie.main.compose.ui.dialog.DTDialog

@Composable
fun AIImageGenFailDialog(
    onDismiss: () -> Unit,
    onRetry: () -> Unit
) {
    DTDialog(onDismiss) {
        Box(Modifier.width(300.dp)) {
            Column(
                Modifier
                    .padding(top = 40.dp)
                    .background(MaterialTheme.colorScheme.onPrimary, RoundedCornerShape(16.dp)),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                DTVerticalSpacer(50.dp)
                Title()
                DTVerticalSpacer(6.dp)
                Tips()
                DTVerticalSpacer(30.dp)
                Buttons(onDismiss, onRetry)
                DTVerticalSpacer(20.dp)
            }
            DialogIcon()
        }
    }
}

@Composable
private fun BoxScope.DialogIcon() {
    Image(
        painterResource(R.drawable.ic_ai_gen_img_fail),
        null,
        Modifier
            .size(134.dp, 90.dp)
            .align(Alignment.TopCenter)
    )
}

@Composable
private fun Title() {
    Text(
        stringResource(R.string.reminder),
        style = MaterialTheme.typography.headlineLarge.copy(
            color = Black100,
            fontSize = 16.sp
        )
    )
}

@Composable
private fun Tips() {
    Text(
        stringResource(R.string.ai_image_result_page_fail_dialog_tips),
        Modifier
            .padding(horizontal = 13.dp)
            .fillMaxWidth(),
        style = MaterialTheme.typography.bodySmall.copy(
            color = Black100,
            lineHeight = 20.sp
        )
    )
}

@Composable
private fun Buttons(onCancel: () -> Unit, onRetry: () -> Unit) {
    Row(
        Modifier
            .padding(horizontal = 18.dp)
            .fillMaxWidth()
            .height(40.dp)
    ) {
        DTButton(
            R.string.cancel,
            modifier = Modifier
                .weight(1f)
                .height(40.dp),
            border = BorderStroke(2.dp, MaterialTheme.colorScheme.primary),
            textStyle = DTButtonTextStyle.copy(
                color = MaterialTheme.colorScheme.primary,
            ),
            onClick = onCancel
        )
        DTHorizontalSpacer(16.dp)
        DTButton(
            R.string.retry,
            modifier = Modifier
                .weight(1f)
                .height(40.dp),
            onClick = onRetry,
            containerColor = MaterialTheme.colorScheme.primary
        )
    }
}
