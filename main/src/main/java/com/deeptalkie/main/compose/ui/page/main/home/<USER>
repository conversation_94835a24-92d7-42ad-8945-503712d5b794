package com.deeptalkie.main.compose.ui.page.main.home

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.deeptalkie.main.R
import com.deeptalkie.main.compose.ui.components.DTHorizontalSpacer
import com.deeptalkie.main.compose.ui.components.DTTextField
import com.deeptalkie.main.compose.ui.components.DTTextFieldIcon
import com.deeptalkie.main.compose.ui.components.DTTextFieldTextStyle

@Composable
fun DTHomeSearchBar(
    searchText: String,
    onSearchTextChange: (String) -> Unit,
    placeholder: String,
    onSearch: () -> Unit,
    avatar: Painter,
    onAvatarClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    val keyboardController = LocalSoftwareKeyboardController.current
    val focusManager = LocalFocusManager.current

    Row(
        modifier
            .padding(vertical = 12.dp)
            .fillMaxWidth()
            .height(42.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        DTHorizontalSpacer(16.dp)
        DTTextField(
            value = searchText,
            onValueChange = onSearchTextChange,
            modifier = Modifier
                .weight(1f)
                .fillMaxHeight()
                .clickable(
                    interactionSource = remember { MutableInteractionSource() },
                    indication = null
                ) {
                    // 空实现，只用于拦截点击事件
                },
            placeholder = placeholder,
            rightIcon = DTTextFieldIcon(
                painterResource(R.drawable.ic_search),
                onClick = {
                    // 点击搜索按钮时也隐藏键盘和清除焦点
                    keyboardController?.hide()
                    focusManager.clearFocus()
                    onSearch()
                },
            ),
            keyboardOptions = KeyboardOptions(imeAction = ImeAction.Search),
            keyboardActions = KeyboardActions(onSearch = {
                keyboardController?.hide()
                focusManager.clearFocus()
                onSearch()
            }),
            textStyle = DTTextFieldTextStyle.copy(fontSize = 12.sp),
        )
        DTHorizontalSpacer(12.dp)
        Image(
            avatar,
            null,
            Modifier
                .size(42.dp)
                .clip(CircleShape)
                .clickable {
                    // 点击头像按钮时，先隐藏键盘和清除焦点，再执行原来的点击事件
                    keyboardController?.hide()
                    focusManager.clearFocus()
                    onAvatarClick()
                }
        )
        DTHorizontalSpacer(16.dp)
    }
}