package com.deeptalkie.main.compose.ui.page.main.home

import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.requiredWidth
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.GridItemSpan
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.clevguard.utils.ext.logd
import com.deeptalkie.main.Membership
import com.deeptalkie.main.compose.navigation.MainRoute
import com.deeptalkie.main.compose.theme.LightPink
import com.deeptalkie.main.compose.theme.LightPurple
import com.deeptalkie.main.compose.theme.Pink40
import com.deeptalkie.main.compose.theme.Purple40
import com.deeptalkie.main.compose.ui.components.DTBanner
import com.deeptalkie.main.compose.ui.components.DTPage
import com.deeptalkie.main.compose.ui.components.DTVerticalSpacer
import com.deeptalkie.main.compose.ui.components.LocalDTNavigationBarHeight
import com.deeptalkie.main.config.Constant
import com.deeptalkie.main.utils.StatisticsUtil
import kotlinx.coroutines.launch

@Composable
fun HomeModulePage(
    navigate: (MainRoute) -> Unit,
    onJumpMine: () -> Unit,
    showCreateRoleBottomSheet: () -> Unit,
    homeViewModel: HomeViewModel = viewModel(),
) {
    val keyboardController = LocalSoftwareKeyboardController.current
    val scope = rememberCoroutineScope()
    val context = LocalContext.current
    val focusManager = LocalFocusManager.current
    val interactionSource = remember { MutableInteractionSource() }

    val dailyFeatured by homeViewModel.dailyFeaturedFlow.collectAsStateWithLifecycle()
    val createdFromUsers by homeViewModel.createdFromUsersFlow.collectAsStateWithLifecycle()

    LaunchedEffect(Unit) {
        homeViewModel.requestHomeData()
    }
    DTPage(
        Modifier
            .clickable(
                interactionSource = interactionSource,
                indication = null
            ) {
                // 点击页面任何地方，清除焦点并隐藏键盘
                focusManager.clearFocus()
                keyboardController?.hide()
            },
        com.deeptalkie.main.R.drawable.home_module_page_bg,
        loading = homeViewModel.loading,
        onDismissLoading = {
            homeViewModel.showLoading(false)
        },
    ) {
        Column(
            Modifier
                .statusBarsPadding()
                .fillMaxSize()
        ) {
            DTHomeSearchBar(
                searchText = homeViewModel.searchText,
                onSearchTextChange = homeViewModel::onSearchTextInput,
                placeholder = stringResource(com.deeptalkie.main.R.string.home_page_search_placeholder),
                onSearch = {
                    StatisticsUtil.onEvent(
                        context,
                        Constant.Search_box,
                        mapOf(Constant.Search_box to homeViewModel.searchText)
                    )
                    homeViewModel.onSearch()
                },
                avatar = painterResource(com.deeptalkie.main.R.drawable.image_avatar_no_login),
                onAvatarClick = {
                    if (!Membership.isLogin()) {
                        navigate(MainRoute.Login)
                    } else {
                        onJumpMine()
                    }
                }
            )
            SearchResultPop(
                homeViewModel.searchResult,
                (-8).dp,
                onItemClick = {
                    scope.launch {
                        if (it.id == -1L) return@launch

                        if (!Membership.isLogin()) {
                            navigate(MainRoute.RoleDetail(it.id))
                        } else {
                            homeViewModel.showLoading(true)
                            val sessionId = homeViewModel.startChat(
                                it.id, context,
                                gotoLogin = {
                                    navigate(MainRoute.Login)
                                },
                                gotoBuy = {
                                    navigate(MainRoute.Product)
                                }
                            )
                            homeViewModel.showLoading(false)
                            if (sessionId == null) return@launch
                            navigate(MainRoute.Chat(it.id, sessionId))
                        }

                        homeViewModel.clearSearchText()
                        homeViewModel.closeSearchResultPopup()
                    }
                },
                onDismiss = {
                    homeViewModel.closeSearchResultPopup()
                }
            )
            BoxWithConstraints(Modifier.fillMaxSize()) {
                LazyVerticalGrid(
                    GridCells.Fixed(2),
                    Modifier.fillMaxSize(),
                    contentPadding = PaddingValues(
                        start = 16.dp,
                        end = 16.dp,
                        bottom = LocalDTNavigationBarHeight.current
                    ),
                    horizontalArrangement = Arrangement.spacedBy(12.dp),
                    verticalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    item(key = "banner", span = { GridItemSpan(maxLineSpan) }) {
                        DTBanner(
                            homeViewModel.bannerData,
                            modifier = Modifier.requiredWidth(<EMAIL>)
                        ) { index, item ->
                            when (index) {
                                0 -> {
                                    //聊天页面
                                    StatisticsUtil.onEvent(
                                        context, Constant.Top_Banner_clicks,
                                        mapOf(Constant.Top_Banner_clicks to "Banner_1")
                                    )
                                    scope.launch {
                                        homeViewModel.showLoading(true)
                                        val res = homeViewModel.randomChat(
                                            context,
                                            gotoLogin = {
                                                navigate(MainRoute.Login)
                                            },
                                            gotoBuy = {
                                                navigate(MainRoute.Product)
                                            }
                                        )
                                        homeViewModel.showLoading(false)
                                        if (res == null) return@launch
                                        val (roleId, sessionId) = res
                                        navigate(MainRoute.Chat(roleId, sessionId))
                                    }
                                }

                                1 -> showCreateRoleBottomSheet()

                                2 -> {
                                    //购买页面
                                    StatisticsUtil.onEvent(
                                        context, Constant.Top_Banner_clicks,
                                        mapOf(Constant.Top_Banner_clicks to "Banner_2")
                                    )
                                    StatisticsUtil.onEvent(
                                        context, Constant.Purchase_page,
                                        mapOf(Constant.Purchase_page to "Banner")
                                    )
                                    navigate(MainRoute.Product)
                                }
                            }
                            logd("轮播图被点击：$index")
                        }
                    }
                    if (dailyFeatured.isNotEmpty()) {
                        item(key = "DAILY_FEATURED_TITLE", span = { GridItemSpan(maxLineSpan) }) {
                            Column {
                                DTVerticalSpacer(18.dp)
                                HomeRoleListTitle(
                                    title = stringResource(com.deeptalkie.main.R.string.home_page_daily_featured_title),
                                    subtitle = stringResource(com.deeptalkie.main.R.string.home_page_daily_featured_sub_title),
                                    painterResource(com.deeptalkie.main.R.drawable.image_daily_featured)
                                )
                                DTVerticalSpacer(8.dp)
                            }
                        }
                        items(
                            dailyFeatured,
                            key = { it.id },
                            contentType = { "ROLE_CARD" }
                        ) { role ->
                            HomeRoleCard(
                                role,
                                painterResource(com.deeptalkie.main.R.drawable.ic_chat_home_role_card),
                                LightPurple,
                                Purple40,
                                onClick = {
                                    scope.launch {
                                        homeViewModel.showLoading(true)
                                        val sessionId = homeViewModel.startChat(
                                            role.id, context,
                                            gotoLogin = {
                                                navigate(MainRoute.Login)
                                            },
                                            gotoBuy = {
                                                navigate(MainRoute.Product)
                                            }
                                        )
                                        homeViewModel.showLoading(false)
                                        if (sessionId == null) return@launch
                                        navigate(MainRoute.Chat(role.id, sessionId))
                                    }
                                }
                            )
                        }
                    }

                    if (createdFromUsers.isNotEmpty()) {
                        item(
                            key = "CREATED_FROM_USERS_TITLE",
                            span = { GridItemSpan(maxLineSpan) }) {
                            Column {
                                DTVerticalSpacer(18.dp)
                                HomeRoleListTitle(
                                    title = stringResource(com.deeptalkie.main.R.string.home_page_created_from_users_title),
                                    subtitle = stringResource(com.deeptalkie.main.R.string.home_page_created_from_users_sub_title),
                                    painterResource(com.deeptalkie.main.R.drawable.image_created_from_users)
                                )
                                DTVerticalSpacer(8.dp)
                            }
                        }
                        items(
                            createdFromUsers,
                            key = { it.id },
                            contentType = { "ROLE_CARD" }) { role ->
                            HomeRoleCard(
                                role,
                                painterResource(com.deeptalkie.main.R.drawable.ic_hearts_home_role_card),
                                LightPink,
                                Pink40,
                                onClick = {
                                    scope.launch {
                                        homeViewModel.showLoading(true)
                                        val sessionId = homeViewModel.startChat(
                                            role.id, context,
                                            gotoLogin = {
                                                navigate(MainRoute.Login)
                                            },
                                            gotoBuy = {
                                                navigate(MainRoute.Product)
                                            }
                                        )
                                        homeViewModel.showLoading(false)
                                        if (sessionId == null) return@launch
                                        navigate(MainRoute.Chat(role.id, sessionId))
                                    }
                                }
                            )
                        }
                    }

                    if (createdFromUsers.isNotEmpty() && dailyFeatured.isNotEmpty()) {
                        item(key = "LIST_FOOTER", span = { GridItemSpan(maxLineSpan) }) {
                            Box(Modifier.fillMaxWidth(), contentAlignment = Alignment.Center) {
                                Text(
                                    stringResource(com.deeptalkie.main.R.string.home_page_role_list_footer),
                                    color = MaterialTheme.colorScheme.onSecondaryContainer,
                                    fontSize = 12.sp,
                                    fontWeight = FontWeight.W400,
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}