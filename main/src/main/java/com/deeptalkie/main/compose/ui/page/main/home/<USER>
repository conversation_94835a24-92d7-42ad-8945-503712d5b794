package com.deeptalkie.main.compose.ui.page.main.home

import android.app.Activity
import android.content.Intent
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.Image
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextLayoutResult
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.deeptalkie.main.App
import com.deeptalkie.main.R
import com.deeptalkie.main.activity.WebActivity
import com.deeptalkie.main.bean.UserManager
import com.deeptalkie.main.compose.ui.components.DTButton
import com.deeptalkie.main.compose.ui.components.DTVerticalSpacer
import com.deeptalkie.main.compose.ui.dialog.DTDialog
import com.deeptalkie.main.config.Constant
import com.deeptalkie.main.utils.StatisticsUtil
import kotlinx.coroutines.delay

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun PrivacyAgreementDialog() {
    var show by remember { mutableStateOf(false) }

    LaunchedEffect(Unit) {
        delay(1000)
        show = !UserManager.isAgreePrivacyPolicy()
    }

    val onAccept = remember {
        {
            UserManager.setAgreePrivacyPolicy()
            show = false
        }
    }


    if (show) {
        val context = LocalContext.current
        val terms = stringResource(R.string.terms)
        val eula = stringResource(R.string.eula_detail)
        val policy = stringResource(R.string.policy)

        val fullText = String.format(
            stringResource(R.string.privacy_agreement_dialog_content),
            terms,
            eula,
            policy
        )

        // 定义三个可点击区域及对应跳转目标
        val clickableItems = listOf(
            ClickableItem(
                text = stringResource(R.string.terms),
                target = WebActivity::class.java,
                color = MaterialTheme.colorScheme.secondary,
                paramKey = "url",
                paramValue = Constant.terms + Constant.webParams
            ),
            ClickableItem(
                text = stringResource(R.string.eula_detail),
                target = WebActivity::class.java,
                color = MaterialTheme.colorScheme.secondary,
                paramKey = "url",
                paramValue = Constant.eula + Constant.webParams
            ),
            ClickableItem(
                text = stringResource(R.string.policy),
                target = WebActivity::class.java,
                color = MaterialTheme.colorScheme.secondary,
                paramKey = "url",
                paramValue = Constant.privacy + Constant.webParams
            )
        )

        // 构建带注解的字符串
        val annotatedString = buildAnnotatedString {
            append(fullText)

            clickableItems.forEach { item ->
                val startIndex = fullText.indexOf(item.text)
                check(startIndex != -1) { "文本中未找到 ${item.text}" }
                val endIndex = startIndex + item.text.length

                addStyle(
                    style = SpanStyle(
                        color = item.color,
                        textDecoration = TextDecoration.Underline
                    ),
                    start = startIndex,
                    end = endIndex
                )

                addStringAnnotation(
                    tag = "CLICKABLE",
                    annotation = "${item.target.name}|${item.paramKey}|${item.paramValue}",
                    start = startIndex,
                    end = endIndex
                )
            }
        }

        DTDialog({}) {
            Box(
                Modifier
                    .fillMaxWidth()
                    .aspectRatio(1f),
                contentAlignment = Alignment.TopCenter
            ) {
                var layoutResult by remember { mutableStateOf<TextLayoutResult?>(null) }
                Image(
                    painterResource(R.drawable.image_privacy_agreement_bg),
                    null,
                    Modifier.fillMaxSize()
                )
                Column(
                    Modifier
                        .padding(top = 52.dp)
                        .width(301.dp)
                        .height(287.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    DTVerticalSpacer(36.dp)
                    Image(
                        painterResource(R.drawable.image_welcome_to_deeptalkie),
                        null,
                        Modifier
                            .padding(horizontal = 40.dp)
                            .fillMaxWidth()
                    )
                    DTVerticalSpacer(10.dp)
                    Text(
                        text = annotatedString,
                        modifier = Modifier
                            .padding(horizontal = 20.dp)
                            .fillMaxWidth()
                            .weight(1f)
                            .verticalScroll(rememberScrollState())
                            .pointerInput(Unit) {
                                detectTapGestures { tapOffset ->
                                    layoutResult?.let { layout ->
                                        // 将点击位置转换为文本偏移量
                                        val offset = layout.getOffsetForPosition(tapOffset)

                                        // 获取所有匹配的注解
                                        annotatedString.getStringAnnotations(
                                            "CLICKABLE",
                                            offset,
                                            offset
                                        ).firstOrNull()
                                            ?.let { annotation ->
                                                val parts = annotation.item.split("|")
                                                if (parts.size == 3) {
                                                    val className = parts[0]
                                                    val paramKey = parts[1]
                                                    val paramValue = parts[2]

                                                    Intent(
                                                        context,
                                                        Class.forName(className)
                                                    ).apply {
                                                        putExtra(paramKey, paramValue)
                                                        context.startActivity(this)
                                                    }
                                                }
                                            }
                                    }
                                }
                            },
                        onTextLayout = { layoutResult = it },
                        color = MaterialTheme.colorScheme.onPrimary,
                        fontSize = 14.sp,
                        fontWeight = FontWeight.W400,
                        textAlign = TextAlign.Center
                    )
                    DTVerticalSpacer(10.dp)
                    DTButton(
                        stringResource(R.string.privacy_agreement_dialog_accept_btn_text),
                        Brush.horizontalGradient(
                            listOf(
                                Color(0xFFF4A0FF),
                                Color(0xFFD64FFF),
                                Color(0xFF684EFF)
                            )
                        ),
                        Modifier
                            .padding(horizontal = 31.dp)
                            .fillMaxWidth()
                            .height(42.dp),
                        elevation = 6.dp,
                        textStyle = TextStyle.Default.copy(
                            fontSize = 14.sp,
                            fontWeight = FontWeight.W700
                        ),
                        onClick = {
                            StatisticsUtil.init(context, true, App.showLog)
                            onAccept()
                        }
                    )
                    DTVerticalSpacer(36.dp)
                }
            }
        }
    }
}

data class ClickableItem(
    val text: String,
    val target: Class<out Activity>,
    val color: Color,
    val paramKey: String,
    val paramValue: String
)