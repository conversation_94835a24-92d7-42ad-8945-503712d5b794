package com.deeptalkie.main.compose.ui.page.main.home

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.deeptalkie.main.bean.SearchResultBean
import com.deeptalkie.main.compose.theme.White10
import com.deeptalkie.main.compose.ui.components.DTPopup
import com.deeptalkie.main.compose.ui.components.DTVerticalSpacer

@Composable
fun SearchResultPop(
    data: List<SearchResultBean>,
    offsetY: Dp,
    onItemClick: (SearchResultBean) -> Unit,
    onDismiss: () -> Unit
) {
    if (data.isNotEmpty()) {
        DTPopup(offsetY = offsetY, onDismissRequest = onDismiss) {
            LazyColumn(
                Modifier
                    .padding(
                        start = 16.dp,
                        end = 70.dp
                    )
                    .fillMaxWidth()
                    .clip(RoundedCornerShape(12.dp))
                    .background(Color(0xFF39393C)),
                contentPadding = PaddingValues(vertical = 2.dp)
            ) {
                itemsIndexed(data, key = { _, it -> it.id }) { index, item ->
                    Column(
                        Modifier
                            .fillMaxWidth()
                            .clickable {
                                onItemClick(item)
                            }
                            .padding(horizontal = 12.dp)
                    ) {
                        DTVerticalSpacer(10.dp)
                        Text(
                            item.name ?: "",
                            Modifier.fillMaxWidth(),
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis,
                            style = MaterialTheme.typography.bodySmall.copy(
                                color = MaterialTheme.colorScheme.onPrimary,
                            )
                        )
                        DTVerticalSpacer(10.dp)
                        if (index < data.lastIndex) {
                            HorizontalDivider(
                                Modifier.fillMaxWidth(),
                                0.5.dp,
                                color = White10
                            )
                        }
                    }
                }
            }
        }
    }
}