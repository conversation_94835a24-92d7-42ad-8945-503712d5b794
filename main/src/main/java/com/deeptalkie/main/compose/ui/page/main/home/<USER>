package com.deeptalkie.main.compose.ui.page.main.home

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import coil3.compose.AsyncImage
import com.deeptalkie.main.compose.theme.Black
import com.deeptalkie.main.compose.theme.Black63
import com.deeptalkie.main.compose.theme.Transparent
import com.deeptalkie.main.compose.ui.components.DTBlurTag
import com.deeptalkie.main.db.table.AIRole
import com.deeptalkie.main.ext.imageRequest
import com.deeptalkie.main.utils.formatNumber

@Composable
fun HomeRoleCard(
    roleUIInfo: AIRole,
    tagIcon: Painter,
    tagIconColor: Color,
    tagColor: Color,
    onClick: () -> Unit,
) {
    ConstraintLayout(
        Modifier
            .fillMaxWidth()
            .height(221.dp)
            .clip(RoundedCornerShape(10.dp))
            .clickable(
                onClick = onClick
            )
    ) {
        val (nameRef, descRef, chatNumRef, bottomMaskRef) = createRefs()
        AsyncImage(
            roleUIInfo.avatar.imageRequest(LocalContext.current),
            null,
            Modifier.fillMaxSize(),
            contentScale = ContentScale.Crop
        )

        Box(
            Modifier
                .fillMaxWidth()
                .height(77.dp)
                .background(
                    Brush.verticalGradient(
                        listOf(Transparent, Black63, Black)
                    )
                )
                .constrainAs(bottomMaskRef) {
                    bottom.linkTo(parent.bottom)
                }
        )

        DTBlurTag(
            tagIcon,
            formatNumber(roleUIInfo.messageCount),
            tagIconColor,
            Modifier
                .height(22.dp)
                .clip(RoundedCornerShape(50))
                .background(tagColor)
                .constrainAs(chatNumRef) {
                    top.linkTo(parent.top, 6.dp)
                    end.linkTo(parent.end, 6.dp)
                }
        )

        Text(
            roleUIInfo.name,
            Modifier.constrainAs(nameRef) {
                start.linkTo(parent.start, 10.dp)
                end.linkTo(parent.end, 10.dp)
                bottom.linkTo(descRef.top, 1.dp)
                width = Dimension.fillToConstraints
            },
            maxLines = 2,
            overflow = TextOverflow.Ellipsis,
            style = MaterialTheme.typography.headlineLarge.copy(
                color = MaterialTheme.colorScheme.onPrimary,
                fontSize = 14.sp,
                lineHeight = 14.sp
            ),
        )

        Text(
            roleUIInfo.description,
            Modifier
                .constrainAs(descRef) {
                    width = Dimension.fillToConstraints
                    start.linkTo(parent.start, 10.dp)
                    end.linkTo(parent.end, 10.dp)
                    bottom.linkTo(parent.bottom, 8.dp)
                },
            maxLines = 2,
            overflow = TextOverflow.Ellipsis,
            style = MaterialTheme.typography.labelSmall.copy(
                color = MaterialTheme.colorScheme.onSecondaryContainer,
            ),
        )
    }
}