package com.deeptalkie.main.compose.ui.page.main.home

import android.content.Context
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.deeptalkie.kidsguard.net.success
import com.deeptalkie.main.R
import com.deeptalkie.main.bean.SearchResultBean
import com.deeptalkie.main.bean.emptySearchResultBean
import com.deeptalkie.main.compose.ui.components.DTBannerItem
import com.deeptalkie.main.compose.ui.page.main.chats.ChatsRepo
import com.deeptalkie.main.compose.utils.ILoadingState
import com.deeptalkie.main.compose.utils.loadingState
import com.deeptalkie.main.ext.stateInViewModelDefault
import com.deeptalkie.main.utils.getString
import kotlinx.coroutines.launch

class HomeViewModel : ViewModel(), ILoadingState by loadingState() {
    private val homeRepo = HomeRepo()
    private val chatsRepo = ChatsRepo()

    var searchText by mutableStateOf("")
        private set
    val bannerData = getBanner()
    val dailyFeaturedFlow = homeRepo.getDailyAiRolesFlow()
        .stateInViewModelDefault(emptyList())
    val createdFromUsersFlow = homeRepo.getUserCreatedAiRolesFlow()
        .stateInViewModelDefault(emptyList())
    var searchResult by mutableStateOf(listOf<SearchResultBean>())
        private set

    fun getBanner(): List<DTBannerItem> {
        return listOf(
            DTBannerItem(R.drawable.banner_chat),
            DTBannerItem(R.drawable.banner_create_role),
            DTBannerItem(R.drawable.banner_unlock_vip),
        )
    }

    fun onSearchTextInput(text: String) {
        searchText = text
        closeSearchResultPopup()
    }

    fun closeSearchResultPopup() {
        searchResult = emptyList()
    }

    suspend fun startChat(
        id: Long,
        context: Context,
        gotoLogin: () -> Unit,
        gotoBuy: () -> Unit
    ): Long? {
        return chatsRepo.requestChat(id, context, gotoLogin, gotoBuy)
    }

    fun clearSearchText() {
        searchText = ""
    }

    fun onSearch() {
        if (searchText.isEmpty()) return
        viewModelScope.launch {
            showLoading(true)
            homeRepo.searchRole(searchText)
                ?.success {
                    searchResult = it.ifEmpty {
                        listOf(emptySearchResultBean(getString(R.string.no_result)))
                    }
                }
            showLoading(false)
        }
    }

    fun requestHomeData() {
        viewModelScope.launch {
            homeRepo.requestHomeData()
        }
    }

    suspend fun randomChat(
        context: Context,
        gotoLogin: () -> Unit,
        gotoBuy: () -> Unit
    ): Pair<Long, Long>? {
        return chatsRepo.randomChat(context, gotoLogin, gotoBuy)
    }
}