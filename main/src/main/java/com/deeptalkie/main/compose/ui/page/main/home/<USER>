package com.deeptalkie.main.compose.ui.page.main.home

import com.deeptalkie.kidsguard.net.ListResponse
import com.deeptalkie.kidsguard.net.asyncSuccess
import com.deeptalkie.kidsguard.net.runHttp
import com.deeptalkie.main.App
import com.deeptalkie.main.Membership
import com.deeptalkie.main.bean.SearchResultBean
import com.deeptalkie.main.config.preloadImages
import com.deeptalkie.main.db.converters.AiRoleClassify
import com.deeptalkie.main.db.table.UserAIRole
import com.deeptalkie.main.repo.BaseDeepTalkieRepo
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class HomeRepo : BaseDeepTalkieRepo() {
    fun getDailyAiRolesFlow() = aiRoleDao.queryDailyAiRoles()
    fun getUserCreatedAiRolesFlow() = aiRoleDao.queryUserCreatedAiRoles()

    suspend fun queryHomePageImages() = aiRoleDao.queryHomePageImages()

    suspend fun requestHomeData() {
        runHttp { remoteApi.getHomeData() }?.asyncSuccess { homeBean ->
            if (homeBean != null) {
                val aiRoles = buildList {
                    homeBean.dailyRecommend
                        ?.map { role ->
                            role.toAIRole(AiRoleClassify.DAILY)
                        }?.also {
                            addAll(it)
                        }
                    homeBean.userCreated
                        ?.map { role ->
                            role.toAIRole(AiRoleClassify.USER_CREATED)
                        }?.also {
                            addAll(it)
                        }
                }
                if (aiRoles.isNotEmpty()) {
                    aiRoleDao.resetClassifyWhereNotInRemote(aiRoles.map { it.id })
                }
                aiRoleDao.upsertAll(aiRoles)
            }
        }
    }

    suspend fun searchRole(word: String): ListResponse<List<SearchResultBean>>? {
        return withContext(Dispatchers.IO) {
            runHttp { remoteApi.searchRole(word) }.apply {
                launch {
                    this@apply?.asyncSuccess { data ->
                        aiRoleDao.upsertAll(data.map { it.toAIRole() })

                        val userId = Membership.getUserId() ?: return@asyncSuccess
                        userAIRoleDao.upsertAll(data.map {
                            UserAIRole(userId, it.id)
                        })
                    }
                }
            }
        }
    }

    fun preloadHomeData() {
        App.launch(Dispatchers.IO) {
            requestHomeData()
            queryHomePageImages()
                .filter { it.images.isNotEmpty() }
                .map { it.images[0] }
                .preloadImages()
        }
    }
}