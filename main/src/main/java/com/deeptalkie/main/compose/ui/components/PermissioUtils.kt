package com.deeptalkie.main.compose.ui.components

import android.Manifest
import android.content.pm.PackageManager
import android.os.Build
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.LocalContext
import androidx.core.content.ContextCompat
import com.deeptalkie.main.compose.utils.NotifyPermissionUtil

/**
 *creater:linjinhao on 2025/6/20 17:24
 */

/**
 * 大于andorid 13动态申请通知权限，否则跳转到设置让用户手动同意权限
 */
@Composable
fun RequestPermission(startRequestPermission: Boolean, onRequestPermission: () -> Unit) {
    val context = LocalContext.current
    val permissionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.RequestPermission(),
        onResult = { isGranted ->
            if (isGranted) {
                // 权限被允许
            } else {
                // 权限被拒绝
                onRequestPermission()
            }
        }
    )
    if (startRequestPermission){
        if (Build.VERSION.SDK_INT >= 33) {
            if (ContextCompat.checkSelfPermission(
                    context, Manifest.permission.POST_NOTIFICATIONS
                ) == PackageManager.PERMISSION_GRANTED
            ) {
                //同意权限了
            } else {
                permissionLauncher.launch(Manifest.permission.POST_NOTIFICATIONS)
            }
        } else {
            if (!NotifyPermissionUtil.checkNotificationPermission(context)) {
                onRequestPermission()
            }
        }
    }
}