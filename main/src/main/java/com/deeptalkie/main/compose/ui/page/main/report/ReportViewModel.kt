package com.deeptalkie.main.compose.ui.page.main.report

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.toRoute
import com.deeptalkie.main.Membership
import com.deeptalkie.main.R
import com.deeptalkie.main.compose.navigation.MainRoute
import com.deeptalkie.main.compose.utils.ILoadingState
import com.deeptalkie.main.compose.utils.loadingState
import com.deeptalkie.main.ext.showToast
import com.deeptalkie.main.repo.AppRepo
import com.deeptalkie.main.utils.NetWorkManager
import com.deeptalkie.main.utils.getString
import kotlinx.coroutines.launch

class ReportViewModel(
    handle: SavedStateHandle
) : ViewModel(),
    ILoadingState by loadingState() {
    val route = handle.toRoute<MainRoute.Report>()

    private val appRepo = AppRepo()

    var showBackDialog by mutableStateOf(false)
        private set
    var roleName by mutableStateOf(route.roleName)
        private set
    var reportContent by mutableStateOf("")
        private set
    var email by mutableStateOf(Membership.getEmail().orEmpty())
        private set

    fun showBackDialog(show: Boolean) {
        showBackDialog = show
    }

    fun onRoleNameChanged(content: String) {
        roleName = content
    }

    fun onReportContentChanged(content: String) {
        reportContent = content
    }

    fun onEmailChanged(content: String) {
        email = content
    }

    fun canSubmit() = roleName.isNotBlank() && reportContent.isNotBlank() && email.isNotBlank()

    fun submit(onResp: (Int?) -> Unit) {
        if (!NetWorkManager.isNetworkConnectedSystem()) {
            showToast(getString(R.string.report_failed))
            return
        }
        viewModelScope.launch {
            showLoading(true)
            val content = "$roleName : $reportContent"
            val resp = appRepo.submitFeedback(email, "Report", content)
            onResp(resp?.code)
            showLoading(false)
        }
    }
}