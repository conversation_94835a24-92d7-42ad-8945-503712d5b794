package com.deeptalkie.main.compose.ui.components

import androidx.compose.foundation.layout.height
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp

@Composable
fun DTProgress(
    progress: Float,
    height: Dp = 6.dp,
    modifier: Modifier = Modifier,
    color: Color = Color(0xFF9568F7),
    trackColor: Color = Color(0x33FFFFFF),
    strokeCap: StrokeCap = StrokeCap.Round,
) {
    val p by rememberUpdatedState(progress)

    LinearProgressIndicator(
        progress = { p },
        modifier = modifier.height(height),
        color = color,
        trackColor = trackColor,
        strokeCap = strokeCap,
        gapSize = -height,
        drawStopIndicator = {}
    )
}

@Composable
fun DTVideoLoading(show: Boolean, modifier: Modifier = Modifier) {
    if (show) {
        CircularProgressIndicator(modifier = modifier)
    }
}