package com.deeptalkie.main.compose.ui.page.main.createrole

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.toRoute
import com.clevguard.utils.ext.logv
import com.deeptalkie.main.R
import com.deeptalkie.main.bean.AIImageStyle
import com.deeptalkie.main.bean.UserManager
import com.deeptalkie.main.compose.navigation.MainRoute
import com.deeptalkie.main.config.ReportEventUtils
import com.deeptalkie.main.config.UmConstant
import com.deeptalkie.main.ext.showToast
import com.deeptalkie.main.ext.stateInViewModelDefault
import com.deeptalkie.main.utils.PromptUtils
import com.deeptalkie.main.utils.getString
import kotlinx.coroutines.launch

class AIGenerationImageViewModel(savedStateHandle: SavedStateHandle) : ViewModel() {
    private val route = savedStateHandle.toRoute<MainRoute.AIGenerationImage>()
    private val createAIRoleRepo = CreateAIRoleRepo()

    var imageDesc by mutableStateOf("")
        private set

    var descInfringingWord by mutableStateOf(emptyList<String>())
        private set

    var aiImageDescLoading by mutableStateOf(false)
        private set

    var aiImageDescReqFailState by mutableStateOf<AIDescReqFailState>(AIDescReqFailState.NotFail)
        private set

    var imageStyle by mutableStateOf<AIImageStyle?>(null)
        private set

    var aiImageStyleList by mutableStateOf(emptyList<AIImageStyle>())
        private set

    val aiRoleSex = route.aiRoleSex

    var showCoinNotEnoughDialog by mutableStateOf(false)
        private set

    private val coinsStateFlow = UserManager.userCoinsFlow.stateInViewModelDefault(0)

    val checkCoinEnough: Boolean
        get() {
            logv("当前金币:${coinsStateFlow.value}")
            return coinsStateFlow.value >= 10
        }

    init {
        loadAIImageStyleList()

        ReportEventUtils.onEvent(
            UmConstant.CREATE_IMAGE,
            mapOf(UmConstant.CREATE_IMAGE to "Character_image_page")
        )
    }

    private fun loadAIImageStyleList() {
        viewModelScope.launch {
            createAIRoleRepo.requestAIImageStyleList()?.let { imageStyleList ->
                aiImageStyleList = imageStyleList
            }
        }
    }

    fun onImageDescInput(desc: String) {
        imageDesc = desc
    }

    fun onImageStyleChange(style: AIImageStyle) {
        imageStyle = style
    }

    fun requestImageDesc() {
        viewModelScope.launch {
            aiImageDescReqFailState = AIDescReqFailState.NotFail
            aiImageDescLoading = true
            val aiGenerationImageDesc = createAIRoleRepo.requestAIRoleImageDesc(route.aiRoleSex)
                ?.description
            if (!aiGenerationImageDesc.isNullOrEmpty()) {
                ReportEventUtils.onEvent(
                    UmConstant.CREATE_IMAGE,
                    mapOf(UmConstant.CREATE_IMAGE to "AI_inspiration_succeed")
                )
                onImageDescInput(aiGenerationImageDesc)
            } else {
                aiImageDescReqFailState = AIDescReqFailState.GenFail
            }
            aiImageDescLoading = false
        }
    }

    fun requestImageDescOptimize() {
        viewModelScope.launch {
            aiImageDescReqFailState = AIDescReqFailState.NotFail
            aiImageDescLoading = true
            val aiGenerationImageDesc =
                createAIRoleRepo.requestAIRoleImageDescOptimize(imageDesc, route.aiRoleSex)
                    ?.description
            if (!aiGenerationImageDesc.isNullOrEmpty()) {
                ReportEventUtils.onEvent(
                    UmConstant.CREATE_IMAGE,
                    mapOf(UmConstant.CREATE_IMAGE to "Optimize_succeed")
                )
                onImageDescInput(aiGenerationImageDesc)
            } else {
                aiImageDescReqFailState = AIDescReqFailState.OptimizeFail
            }
            aiImageDescLoading = false
        }
    }

    fun onRetryGenDesc() {
        when (aiImageDescReqFailState) {
            AIDescReqFailState.GenFail -> requestImageDesc()
            AIDescReqFailState.OptimizeFail -> requestImageDescOptimize()
            AIDescReqFailState.NotFail -> {}
        }
    }

    fun canGenerate(): Boolean {
        return imageDesc.isNotEmpty() && imageDesc.length <= 1000 && imageStyle != null
    }

    suspend fun verifyImageDesc(): Boolean {
        val result = PromptUtils.checkSensitive(imageDesc)
        if (result?.labels?.contains("regional") == true) {
            descInfringingWord = result.reason?.riskWords.orEmpty().split(",")
            showToast(getString(R.string.create_ai_role_page_check_content_tips))
            return false
        }
        return true
    }

    fun showCoinNotEnoughDialog(show: Boolean) {
        if (show){
            ReportEventUtils.onEvent(
                UmConstant.CREATE_IMAGE,
                mapOf(UmConstant.CREATE_IMAGE to "Insufficient points_popup")
            )
        }
        showCoinNotEnoughDialog = show
    }
}

sealed interface AIDescReqFailState {
    object NotFail : AIDescReqFailState
    object GenFail : AIDescReqFailState
    object OptimizeFail : AIDescReqFailState
}