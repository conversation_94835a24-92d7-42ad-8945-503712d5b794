package com.deeptalkie.main.compose.ui.page.main.mine

import androidx.annotation.StringRes
import androidx.compose.animation.animateColorAsState
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.ClickableText
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.lifecycle.viewmodel.compose.viewModel
import com.deeptalkie.main.R
import com.deeptalkie.main.activity.WebActivity
import com.deeptalkie.main.bean.BindAccountBean
import com.deeptalkie.main.compose.theme.SecondaryColor
import com.deeptalkie.main.compose.theme.SocialBlue
import com.deeptalkie.main.compose.theme.White
import com.deeptalkie.main.compose.theme.White10
import com.deeptalkie.main.compose.theme.White12
import com.deeptalkie.main.compose.theme.White30
import com.deeptalkie.main.compose.theme.White70
import com.deeptalkie.main.compose.theme.White80
import com.deeptalkie.main.compose.ui.components.DTButton
import com.deeptalkie.main.compose.ui.components.DTHorizontalSpacer
import com.deeptalkie.main.compose.ui.components.DTLoading
import com.deeptalkie.main.compose.ui.components.DTVerticalSpacer
import com.deeptalkie.main.compose.ui.modifier.click
import com.deeptalkie.main.compose.utils.ChildDesign
import com.deeptalkie.main.compose.utils.GOOGLE_LOGIN_TIMEOUT
import com.deeptalkie.main.compose.utils.GOOGLE_LOGIN_TOKEN
import com.deeptalkie.main.compose.utils.toAnnotatedStringParameters
import com.deeptalkie.main.config.Constant
import com.deeptalkie.main.config.PASSWORD_REGEX
import com.deeptalkie.main.ext.showToast
import com.imyfone.membership.ext.googlelogin.GoogleLoginData
import com.imyfone.membership.ext.googlelogin.GoogleLoginEvent
import com.imyfone.membership.ext.googlelogin.rememberLauncherGoogleLoginWithResult
import kotlinx.coroutines.flow.SharedFlow

/**
 *creater:linjinhao on 2025/5/14 16:10
 */
private const val TAG_POLICY = "TAG_POLICY"
private const val TAG_TERMS = "TAG_TERMS"
private const val TAG_AGREEMENT = "TAG_AGREEMENT"

@Composable
fun LoginRoute(
    viewModel: DTLoginViewModel = viewModel(),
    onBindEmail: (info: BindAccountBean) -> Unit,
    onBack: () -> Unit,
    onSignUp: () -> Unit,
    onLoginSuccess: () -> Unit,
    onForgetPwd: () -> Unit
) {
    var emailTip by remember { mutableStateOf("") }
    var pwdTip by remember { mutableStateOf("") }
    val context = LocalContext.current
    LoginScreen(
        viewModel = viewModel,
        emailTip = emailTip,
        pwdTip = pwdTip,
        onBack = onBack,
        onForgetPwd = onForgetPwd,
        onSignUp = onSignUp,
        onchangePwdTip = { pwdTip = it },
        onChangeEmailTip = { emailTip = it },
        onPolicy = { WebActivity.startBrowser(context, Constant.privacy + Constant.webParams) },
        onTerms = { WebActivity.startBrowser(context, (Constant.terms) + Constant.webParams) },
        onAgreement = { WebActivity.startBrowser(context, (Constant.eula) + Constant.webParams) },
    )
    HandleLoginEvent(
        viewModel.loginEvent,
        onLoginSuccess,
        onChangeTipPassword = { pwdTip = it },
        onChangeTipEmail = { emailTip = it })

    HandleGoogleLoginEvent(
        viewModel.googleLoginEvent,
        onBindEmail = { onBindEmail(it) },
        onLoginSuccess = { onLoginSuccess() }
    )
}


@Composable
fun LoginScreen(
    viewModel: DTLoginViewModel = viewModel(),
    emailTip: String,
    pwdTip: String,
    onBack: () -> Unit,
    onSignUp: () -> Unit,
    onForgetPwd: () -> Unit,
    onchangePwdTip: (String) -> Unit,
    onChangeEmailTip: (String) -> Unit,
    onPolicy: () -> Unit,
    onTerms: () -> Unit,
    onAgreement: () -> Unit,
) {
    val state = rememberScrollState()
    val softwareKeyboardController = LocalSoftwareKeyboardController.current
    val localFocusManager = LocalFocusManager.current
    var isShowPopup by remember { mutableStateOf(false) }
    Box(
        Modifier
            .fillMaxSize()
            .click {
                localFocusManager.clearFocus()
                isShowPopup = false
                softwareKeyboardController?.hide()
            }) {
        Image(
            painterResource(R.drawable.bg_common_bg_dt),
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.FillBounds,
            contentDescription = "bg"
        )
        Column(
            modifier = Modifier
                .statusBarsPadding()
                .fillMaxSize()
                .navigationBarsPadding()
                .verticalScroll(state)
        ) {
            ConstraintLayout(Modifier.padding(start = 30.dp, end = 30.dp)) {
                val (ivBack, title, googleLogin, tvEmail, tvPwd, btn, emailSuffix) = createRefs()
                var email by remember { mutableStateOf(TextFieldValue("")) }
                var password by remember { mutableStateOf("") }
                Image(
                    painter = painterResource(R.drawable.ic_back2),
                    contentDescription = "backIcon",
                    modifier = Modifier
                        .constrainAs(ivBack) {
                            top.linkTo(parent.top, 10.dp)
                            start.linkTo(parent.start)
                        }
                        .click(onBack)
                        .size(36.dp)
                        .offset((-10).dp)
                )
                Title(
                    Modifier.constrainAs(title) {
                        top.linkTo(ivBack.bottom, 30.dp)
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                    },
                    title = R.string.log_in,
                    des = R.string.an_account_will_be_automatically_created_if_you_haven_t_registered_yet
                )
                GoogleLogin(modifier = Modifier.constrainAs(googleLogin) {
                    top.linkTo(title.bottom, 40.dp)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                }, onGoogleLoginSuccess = { viewModel.googleAuthLogin(it) })

                val context = LocalContext.current
                LoginEmail(
                    modifier = Modifier.constrainAs(tvEmail) {
                        top.linkTo(googleLogin.bottom, 30.dp)
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                    },
                    email = email, emailTip = emailTip,
                    onEmailChanged = { newEmail ->
                        email = if (newEmail.text.length > 50) {
                            val substring = newEmail.text.substring(0, 50)
                            TextFieldValue(substring, selection = TextRange(substring.length))
                        } else {
                            newEmail
                        }
                        if (email.text.isEmpty()) {
                            onChangeEmailTip(context.getString(R.string.enter_email))
                        } else {
                            onChangeEmailTip("")
                        }
                        isShowPopup = email.text.isNotEmpty()
                    }, onChangeEmailTip = { onChangeEmailTip(it) })
                LoginPwd(
                    modifier = Modifier.constrainAs(tvPwd) {
                        top.linkTo(tvEmail.bottom, 22.dp)
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                    },
                    pwd = password,
                    pwdTip = pwdTip,
                    onchangePwdTip = { onchangePwdTip(it) },
                    onPasswordChanged = { newPwd ->
                        password = newPwd
                        when {
                            // 未填写密码，失去焦点时，输入框下方提示“Please enter a password.”
                            password.isEmpty() -> {
                                onchangePwdTip(context.getString(R.string.enter_psw))
                            }
                            // 密码小于6个字符，失去焦点时，输入框下方提示“Password length must be 6-16 characters.”
                            password.length < 6 || password.length > 16 -> {
                                onchangePwdTip(context.getString(R.string.login_pwd_valid))
                            }
                            // 仅允许输入字母、数字、和特殊字符（见注册页的符号限制），当输入了不被允许的其他字符时，失去焦点，输入框下方提示“Password only contains alphabets, numbers and special characters.”
                            !PASSWORD_REGEX.matches(password) -> {
                                onchangePwdTip(context.getString(R.string.login_psw_valid))
                            }
                            // 密码符合情况，则清空提示
                            else -> {
                                onchangePwdTip("")
                            }
                        }
                    })


                DTButton(
                    R.string.login,
                    modifier = Modifier
                        .constrainAs(btn) {
                            top.linkTo(tvPwd.bottom, 50.dp)
                            start.linkTo(parent.start)
                            end.linkTo(parent.end)
                        }
                        .fillMaxSize()
                        .height(46.dp),
                    enable = checkEmail(email.text) && checkAccountPassword(password),
                    containerColor = SocialBlue,
                    contentColor = White,
                    disabledContainerColor =colorResource(R.color.color_4b4b51),
                    disabledContentColor = colorResource(R.color.color_8A8C91)
                ) {
                    localFocusManager.clearFocus()
                    viewModel.login(email = email.text, pwd = password)
                }
                EmailSuffix(
                    modifier = Modifier
                        .constrainAs(emailSuffix) {
                            top.linkTo(tvEmail.bottom)
                            start.linkTo(parent.start)
                            end.linkTo(parent.end)
                        }
                        .offset(0.dp, (-5).dp),
                    isShowPopup = isShowPopup,
                    email = email,
                    onItemEmailClick = { lastedEmail ->
                        email =
                            TextFieldValue(lastedEmail, selection = TextRange(lastedEmail.length))
                        isShowPopup = false
                    })
            }
            Spacer(modifier = Modifier.height(14.dp))
            LoginHelp(onSignUp = onSignUp, onForgetPwd = onForgetPwd)
            Spacer(modifier = Modifier.weight(1f))
            UserPolicy(onPolicy = onPolicy, onTerms = onTerms, onAgreement = onAgreement)
        }
        val loading by viewModel.loadingState.collectAsState()
        val googleLoginState  by viewModel.googleLoginState.collectAsState()
        DTLoading(loading = loading.loading||googleLoginState) {}
    }
}

@Composable
fun Title(modifier: Modifier = Modifier, @StringRes title: Int, @StringRes des: Int) {
    Column(modifier = modifier.fillMaxSize()) {
        Text(
            stringResource(title),
            color = White,
            fontSize = 24.sp,
            fontWeight = FontWeight.W700,
            lineHeight = 32.sp,
            modifier = modifier.fillMaxSize()
        )
        Spacer(
            modifier = Modifier
                .fillMaxWidth()
                .height(8.dp)
        )
        Text(
            stringResource(des),
            color = White70,
            fontSize = 14.sp,
            fontWeight = FontWeight.W400,
            lineHeight = 20.sp,
            modifier = Modifier.fillMaxSize()
        )
    }
}

@Composable
fun LoginEmail(
    modifier: Modifier = Modifier,
    email: TextFieldValue,
    emailTip: String,
    onEmailChanged: (TextFieldValue) -> Unit,
    onChangeEmailTip: (String) -> Unit
) {
    Column(modifier = modifier.fillMaxWidth()) {
        val context = LocalContext.current
        BasicTextField(
            value = email,
            onValueChange = {
                onEmailChanged(it)
            },
            singleLine = true,
            textStyle = TextStyle(fontSize = 15.sp, color = White),
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Email),
            modifier = Modifier
                .onFocusChanged {
                    if (it.isFocused) {
                        if (email.text.isEmpty()) {
                            onChangeEmailTip(context.getString(R.string.enter_email))
                        } else {
                            onChangeEmailTip("")
                        }
                    }
                }
                .background(color = White12, RoundedCornerShape(100.dp))
                .padding(start = 16.dp)
                .height(48.dp)
                .fillMaxWidth(),
            decorationBox = { innerTextField ->
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.padding(horizontal = 10.dp)
                ) {
                    Box(
                        modifier = Modifier.weight(1f),
                        contentAlignment = Alignment.CenterStart
                    ) {
                        if (email.text.isEmpty()) {
                            Text(
                                stringResource(R.string.email_address),
                                style = TextStyle(fontSize = 15.sp, color = White30)
                            )
                        }
                        innerTextField()
                    }
                }
            }
        )
        val backgroundColor by animateColorAsState(
            targetValue = if (emailTip.isNotEmpty()) Color.Red else Color.Transparent
        )
        Spacer(modifier = Modifier.height(5.dp))
        Text(
            emailTip, style = TextStyle(
                fontSize = 10.sp,
                color = backgroundColor
            ), modifier = Modifier
                .padding(start = 16.dp, end = 16.dp)
                .fillMaxWidth()
        )
    }

}

@Composable
fun LoginPwd(
    modifier: Modifier = Modifier,
    pwd: String,
    pwdTip: String,
    onPasswordChanged: (String) -> Unit,
    onchangePwdTip: (String) -> Unit
) {
    var isPasswordVisible by remember { mutableStateOf(false) }

    Column(modifier = modifier.fillMaxWidth()) {
        val context = LocalContext.current
        BasicTextField(
            value = pwd,
            onValueChange = {
                onPasswordChanged(it)
            }, textStyle = TextStyle(fontSize = 15.sp, color = White),
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Password),
            visualTransformation = if (isPasswordVisible) VisualTransformation.None else PasswordVisualTransformation(),
            modifier = Modifier
                .onFocusChanged {
                    if (it.isFocused) {
                        when {
                            // 未填写密码，失去焦点时，输入框下方提示“Please enter a password.”
                            pwd.isEmpty() -> {
                                onchangePwdTip(context.getString(R.string.enter_psw))
                            }
                            // 密码小于6个字符，失去焦点时，输入框下方提示“Password length must be 6-16 characters.”
                            pwd.length < 6 || pwd.length > 16 -> {
                                onchangePwdTip(context.getString(R.string.login_pwd_valid))
                            }
                            // 仅允许输入字母、数字、和特殊字符（见注册页的符号限制），当输入了不被允许的其他字符时，失去焦点，输入框下方提示“Password only contains alphabets, numbers and special characters.”
                            !PASSWORD_REGEX.matches(pwd) -> {
                                onchangePwdTip(context.getString(R.string.login_psw_valid))
                            }
                            // 密码符合情况，则清空提示
                            else -> {
                                onchangePwdTip("")
                            }
                        }
                    }
                }
                .background(color = White12, RoundedCornerShape(100.dp))
                .padding(start = 16.dp)
                .height(48.dp)
                .fillMaxWidth(),
            decorationBox = { innerTextField ->
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.padding(horizontal = 10.dp)
                ) {
                    Box(
                        modifier = Modifier.weight(1f),
                        contentAlignment = Alignment.CenterStart
                    ) {
                        if (pwd.isEmpty()) {
                            Text(
                                stringResource(R.string.password),
                                style = TextStyle(fontSize = 15.sp, color = White30)
                            )
                        }
                        innerTextField()
                    }
                    // 眼睛图标
                    IconButton(
                        onClick = { isPasswordVisible = !isPasswordVisible },
                    ) {
                        if (isPasswordVisible) {
                            Icon(
                                painterResource(R.drawable.ic_login_pwd_show),
                                null,
                                tint = White30
                            )
                        } else {
                            Icon(
                                painterResource(R.drawable.ic_login_pwd_hide),
                                null,
                                tint = White30
                            )
                        }
                    }
                }
            }
        )
        val backgroundColor by animateColorAsState(
            targetValue = if (pwdTip.isNotEmpty()) Color.Red else Color.Transparent
        )
        Spacer(modifier = Modifier.height(5.dp))
        Text(
            pwdTip,
            fontSize = 10.sp,
            color = backgroundColor,
            modifier = Modifier
                .padding(start = 16.dp, end = 16.dp)
                .fillMaxWidth()
        )
    }
}


@Composable
fun LoginHelp(modifier: Modifier = Modifier, onSignUp: () -> Unit, onForgetPwd: () -> Unit) {
    Row(
        modifier = modifier
            .padding(horizontal = 30.dp)
            .fillMaxWidth()
    ) {
        Text(
            stringResource(R.string.sign_up),
            style = TextStyle(
                fontSize = 14.sp,
                color = SecondaryColor
            ),
            modifier = Modifier
                .weight(1f)
                .click {
                    onSignUp()
                }
        )
        Text(
            stringResource(R.string.login_forgot_password),
            modifier = Modifier
                .weight(1f)
                .click {
                    onForgetPwd()
                },
            style = TextStyle(
                fontSize = 14.sp,
                color = colorResource(R.color.color_8A8C91),
                textAlign = TextAlign.End
            )
        )
    }
}

@Composable
fun UserPolicy(
    modifier: Modifier = Modifier,
    onPolicy: () -> Unit,
    onTerms: () -> Unit,
    onAgreement: () -> Unit
) {
    Box(
        modifier = modifier
            .padding(horizontal = 20.dp)
            .fillMaxWidth(), contentAlignment = Alignment.Center
    ) {
        val text = rememberUserPolicyAnnotationString(SecondaryColor)
        ClickableText(
            text = text, onClick = { offset ->
                text.getStringAnnotations(TAG_POLICY, start = offset, end = offset).firstOrNull()
                    ?.let {
                        onPolicy()
                    }
                text.getStringAnnotations(TAG_TERMS, start = offset, end = offset).firstOrNull()
                    ?.let {
                        onTerms()
                    }
                text.getStringAnnotations(TAG_AGREEMENT, start = offset, end = offset).firstOrNull()
                    ?.let {
                        onAgreement()
                    }
            }, style = TextStyle(
                fontSize = 12.sp,
                color = colorResource(R.color.color_A4A4A4),
                lineHeight = 19.sp,
                textAlign = TextAlign.Center
            ), modifier = modifier.fillMaxWidth()
        )
    }
}

@Composable
fun rememberUserPolicyAnnotationString(color: Color): AnnotatedString {
    val mainString = stringResource(id = R.string.login_policy_eula)
    val policy = stringResource(id = R.string.policy)
    val terms = stringResource(id = R.string.terms)
    val agreement = stringResource(id = R.string.eula_detail)
    return remember(mainString, policy, terms, agreement, color) {
        mainString.toAnnotatedStringParameters(
            ChildDesign(
                childString = policy,
                annotatedTag = TAG_POLICY,
                spanStyle = SpanStyle(
                    color = color,
                    fontWeight = FontWeight.Normal
                )
            ),
            ChildDesign(
                childString = terms,
                annotatedTag = TAG_TERMS,
                spanStyle = SpanStyle(
                    color = color,
                    fontWeight = FontWeight.Normal,
                )
            ),
            ChildDesign(
                childString = agreement,
                annotatedTag = TAG_AGREEMENT,
                spanStyle = SpanStyle(
                    color = color,
                    fontWeight = FontWeight.Normal,
                )
            )
        )
    }
}


@Composable
private fun HandleLoginEvent(
    event: SharedFlow<LoginEvent>,
    onLoginSuccess: () -> Unit,
    onChangeTipEmail: (String) -> Unit,
    onChangeTipPassword: (String) -> Unit
) {
    val context = LocalContext.current
    LaunchedEffect(event) {
        event.collect {
            when (it) {
                LoginEvent.LoginSuccess -> {
                    // 账号和密码正确，点击登录成功后返回原页面
                    onLoginSuccess()
                }

                LoginEvent.AccountNotExist -> {
                    // 账号未注册会员账号，邮箱输入框下方提示“The account doesn't exist"
                    onChangeTipEmail(context.getString(R.string.login_email_no_exist))
                }

                LoginEvent.PasswordInvalid -> {
                    // 密码错误，密码输入框下方提示“Please enter the correct password.”
                    onChangeTipPassword(context.getString(R.string.please_enter_the_correct_password))
                }

                else -> {
                    showToast(context.getString(R.string.network_error))
                }
            }
        }
    }
}

@Composable
fun EmailSuffix(
    modifier: Modifier,
    isShowPopup: Boolean,
    email: TextFieldValue,
    onItemEmailClick: (String) -> Unit
) {
    //邮箱后缀
    if (isShowPopup) {
        Box(
            modifier = modifier
                .background(
                    color = colorResource(R.color.color_39393C),
                    shape = RoundedCornerShape(12.dp)
                )
                .fillMaxWidth()

        ) {
            val current = LocalContext.current
            val suffixArray by
            remember { mutableStateOf(current.resources.getStringArray(R.array.recommend_mail_box)) }
            val data = remember(email) { mutableStateListOf<String>() }

            if (email.text.contains("@")) {
                val index = email.text.indexOfLast { it == '@' }
                val suffix = email.text.subSequence(index, email.text.length).toString()
                val startStr = email.text.substring(0, index)
                suffixArray.forEach {
                    if (it.contains(suffix)) {
                        val str = startStr.plus(it)
                        if (str.length > 50) {
                            val end = 50 - it.length
                            val textStr = email.text.substring(0, end)
                            data.add(textStr.plus(it))
                        } else {
                            data.add(email.text.replace(suffix, "", true).plus(it))
                        }
                    }
                }
            } else {
                suffixArray.forEach {
                    if (email.text.plus(it).length > 50) {
                        val end = 50 - it.length
                        val textStr = email.text.substring(0, end)
                        data.add(textStr.plus(it))
                    } else {
                        data.add(email.text.plus(it))
                    }
                }
            }

            LazyColumn(
                modifier = Modifier
                    .fillMaxWidth()
                    .heightIn(max = 210.dp)
            ) {
                itemsIndexed(items = data) { index, item ->
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(start = 10.98.dp, end = 10.98.dp)
                    ) {
                        Spacer(modifier = Modifier.height(12.dp))
                        Text(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(start = 10.02.dp)
                                .click {
                                    onItemEmailClick(item)
                                },
                            text = item,
                            style = TextStyle(fontSize = 13.sp, color = White80),
                        )
                        Spacer(modifier = Modifier.height(7.dp))
                        if (index != data.lastIndex) {
                            HorizontalDivider(
                                modifier = Modifier.fillMaxWidth(),
                                thickness = 1.dp,
                                color = White10
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun GoogleLogin(
    modifier: Modifier = Modifier,
    onGoogleLoginSuccess: (data: GoogleLoginData) -> Unit
) {
    Column(modifier = modifier.fillMaxWidth()) {
        GoogleLoginButton(onGoogleLoginSuccess)
        DTVerticalSpacer(30.dp)
        Row(
            modifier = modifier
                .fillMaxWidth()
                .height(24.dp),
            horizontalArrangement = Arrangement.Center,
            verticalAlignment = Alignment.CenterVertically
        ) {
            HorizontalDivider(
                modifier = modifier.width(130.dp),
                thickness = 0.85.dp,
                color = White30
            )
            DTHorizontalSpacer(19.dp)
            Text(
                text = stringResource(R.string.or),
                style = TextStyle(fontSize = 12.sp, color = colorResource(R.color.color_F2F2F2))
            )
            DTHorizontalSpacer(19.dp)
            HorizontalDivider(
                modifier = modifier.width(130.dp),
                thickness = 0.85.dp,
                color = White30
            )
        }
    }
}

@Composable
private fun GoogleLoginButton(
    onGoogleLoginSuccess: (data: GoogleLoginData) -> Unit,
) {
    val context = LocalContext.current
    val googleLauncher = rememberLauncherGoogleLoginWithResult {
        when (it) {
            GoogleLoginEvent.GoogleServiceNotAvailable -> showToast(context.getString(R.string.google_service_not_available))
            is GoogleLoginEvent.RequestError -> {
                showToast(it.codeString)
            }

            GoogleLoginEvent.RequestErrorCancelToManyTimes -> showToast(context.getString(R.string.google_call_fast))
            GoogleLoginEvent.RequestNetWorkError -> showToast(context.getString(R.string.network_error))
            GoogleLoginEvent.RequestStart -> Unit
            GoogleLoginEvent.ResultCancel -> Unit
            is GoogleLoginEvent.ResultError -> showToast(context.getString(R.string.network_error))
            is GoogleLoginEvent.ResultSuccess -> onGoogleLoginSuccess(it.data)
        }
    }
    Row(
        modifier = Modifier
            .clip(RoundedCornerShape(100.dp))
            .click {
                googleLauncher(
                    GOOGLE_LOGIN_TOKEN,
                    true,
                    GOOGLE_LOGIN_TIMEOUT
                )
            }
            .background(White)
            .fillMaxWidth()
            .height(48.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center
    ) {
        Image(
            painterResource(R.drawable.ic_google),
            modifier = Modifier.size(32.dp),
            contentDescription = "icon"
        )
        Spacer(modifier = Modifier.size(6.dp))
        Text(
            stringResource(R.string.continue_with_google),
            fontSize = 14.sp,
            color = colorResource(R.color.color_101015)
        )
    }
}


@Composable
private fun HandleGoogleLoginEvent(
    event: SharedFlow<SVGoogleLoginEvent>,
    onBindEmail: (info: BindAccountBean) -> Unit,
    onLoginSuccess: () -> Unit
) {
    val context = LocalContext.current
    LaunchedEffect(event) {
        event.collect {
            when (it) {
                SVGoogleLoginEvent.CommonError -> {
                    showToast(context.getString(R.string.network_error))
                }

                is SVGoogleLoginEvent.LoginSuccess -> {
                    onLoginSuccess()
                }

                is SVGoogleLoginEvent.BindEmail -> {
                    onBindEmail(it.bean)
                }
            }
        }
    }
}