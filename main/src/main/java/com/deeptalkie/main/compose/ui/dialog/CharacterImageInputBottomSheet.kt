package com.deeptalkie.main.compose.ui.dialog

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.DialogProperties
import com.deeptalkie.main.R
import com.deeptalkie.main.compose.theme.White6
import com.deeptalkie.main.compose.ui.components.DTHorizontalSpacer
import com.deeptalkie.main.compose.ui.components.DTTextFieldRowGroup
import com.deeptalkie.main.compose.ui.components.DTVerticalSpacer
import com.deeptalkie.main.compose.ui.modifier.click

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CharacterImageInputBottomSheet(
    initValue: String,
    onDismiss: () -> Unit,
    onSendMsg: (String) -> Unit
) {
    val valuePrefix = stringResource(R.string.chat_page_generate_image_input_bottom_sheet_prefix)
    var textFieldValue by remember {
        val value = "$valuePrefix$initValue"
        mutableStateOf(
            TextFieldValue(
                value,
                TextRange(value.length, value.length)
            ),
        )
    }
    val focusRequester = remember { FocusRequester() }

    val value by remember(textFieldValue) {
        derivedStateOf {
            textFieldValue.text.removePrefix(valuePrefix)
        }
    }

    LaunchedEffect(Unit) {
        focusRequester.requestFocus()
    }

    DTDialog(
        onDismiss = onDismiss,
        DialogProperties(
            usePlatformDefaultWidth = false,
            decorFitsSystemWindows = false
        )
    ) {
        Box(
            Modifier
                .fillMaxSize()
                .click(onDismiss)
        ) {
            Column(
                Modifier
                    .align(Alignment.BottomCenter)
                    .clip(RoundedCornerShape(20.dp, 20.dp))
                    .background(Color(0xFF28282D))
                    .navigationBarsPadding()
                    .imePadding()
            ) {
                GestureBar()
                DTVerticalSpacer(6.dp)
                Row(
                    Modifier.padding(horizontal = 16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    DTTextFieldRowGroup(
                        value = textFieldValue,
                        onValueChange = {
                            // 最少要保留前缀
                            if (it.text.startsWith(valuePrefix)) {
                                textFieldValue = it
                            }
                            if (it.selection.start < valuePrefix.length || it.selection.end < valuePrefix.length) {
                                textFieldValue = TextFieldValue(
                                    textFieldValue.text,
                                    TextRange(
                                        textFieldValue.text.length,
                                        textFieldValue.text.length
                                    )
                                )
                            }
                        },
                        Modifier
                            .focusRequester(focusRequester)
                            .weight(1f)
                            .heightIn(44.dp)
                            .background(White6, RoundedCornerShape(10.dp))
                            .padding(horizontal = 10.dp),
                        maxLines = 4,
                        textPadding = PaddingValues(vertical = 12.dp)
                    )
                    DTHorizontalSpacer(6.dp)
                    Icon(
                        painterResource(R.drawable.ic_send_msg),
                        null,
                        Modifier
                            .size(44.dp)
                            .clip(CircleShape)
                            .clickable(enabled = value.isNotBlank()) {
                                onSendMsg(value)
                            },
                        Color.Unspecified
                    )
                }
                DTVerticalSpacer(10.dp)
            }
        }
    }
}