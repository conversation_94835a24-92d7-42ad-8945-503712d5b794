package com.deeptalkie.main.compose.ui.dialog

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.deeptalkie.main.R
import com.deeptalkie.main.compose.theme.Black100
import com.deeptalkie.main.compose.ui.components.DTButton
import com.deeptalkie.main.compose.ui.components.DTVerticalSpacer

@Composable
fun CharacterImageHelpDialog(
    onDismiss: () -> Unit
) {
    DTDialog(onDismiss) {
        Box {
            Column(
                Modifier
                    .padding(top = 44.dp)
                    .width(300.dp)
                    .background(MaterialTheme.colorScheme.onPrimary, RoundedCornerShape(16.dp))
                    .padding(horizontal = 20.dp)
                    .align(Alignment.TopCenter)
            ) {
                DTVerticalSpacer(56.dp)
                CoinsRules()
                DTVerticalSpacer(6.dp)
                RuleContent()
                DTVerticalSpacer(10.dp)
                RuleTips()
                DTVerticalSpacer(26.dp)
                GotItBtn(onDismiss)
                DTVerticalSpacer(20.dp)
            }
            Icon(
                painterResource(R.drawable.ic_character_image_help_dialog_coin),
                null,
                Modifier
                    .size(130.dp, 100.dp)
                    .align(Alignment.TopCenter),
                tint = Color.Unspecified
            )
        }
    }
}

@Composable
private fun CoinsRules() {
    Text(
        stringResource(R.string.chat_page_generate_image_help_dialog_title),
        style = MaterialTheme.typography.headlineLarge.copy(
            color = Black100,
            fontSize = 16.sp
        )
    )
}

@Composable
private fun RuleContent() {
    Row(Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceBetween) {
        Text(
            stringResource(R.string.chat_page_generate_image_help_dialog_rule_content_title),
            style = MaterialTheme.typography.bodySmall.copy(
                color = Black100,
                lineHeight = 20.sp
            )
        )
        Text(
            stringResource(R.string.chat_page_generate_image_help_dialog_rule_content),
            style = MaterialTheme.typography.bodySmall.copy(
                color = Black100,
                lineHeight = 20.sp
            )
        )
    }
}

@Composable
private fun RuleTips() {
    Text(
        stringResource(R.string.chat_page_generate_image_help_dialog_tips),
        Modifier.fillMaxWidth(),
        style = MaterialTheme.typography.bodySmall.copy(
            color = Color(0xFFFF2424),
        )
    )
}

@Composable
private fun GotItBtn(onDismiss: () -> Unit) {
    DTButton(
        R.string.got_it,
        modifier = Modifier
            .padding(horizontal = 20.dp)
            .fillMaxWidth()
            .height(42.dp),
        containerColor = MaterialTheme.colorScheme.primary,
        onClick = onDismiss
    )
}
