package com.deeptalkie.main.compose.ui.page.main.createrole

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.media3.common.PlaybackException
import androidx.media3.common.Player
import androidx.media3.exoplayer.ExoPlayer
import androidx.navigation.toRoute
import com.deeptalkie.main.App
import com.deeptalkie.main.R
import com.deeptalkie.main.compose.navigation.MainRoute
import com.deeptalkie.main.compose.ui.page.main.video.playUrl
import com.deeptalkie.main.db.table.Voice
import com.deeptalkie.main.ext.showToast
import com.deeptalkie.main.ext.stateInViewModel
import com.deeptalkie.main.utils.getString
import kotlinx.coroutines.launch
import kotlinx.serialization.json.Json

class SelectVoiceViewModel(savedStateHandle: SavedStateHandle) : ViewModel() {
    private val route = savedStateHandle.toRoute<MainRoute.SelectVoice>()

    private val selectVoiceRepo = SelectVoiceRepo()
    var selectedVoice by mutableStateOf<Voice?>(route.voiceJson?.let { Json.decodeFromString(it) })
        private set

    val tabVoicesMap = linkedMapOf(
        getString(R.string.select_voice_page_tab_recommended) to selectVoiceRepo.getRecommendedVoiceFlow()
            .stateInViewModel(emptyList()),
        getString(R.string.select_voice_page_tab_female_voice) to selectVoiceRepo.getFemaleVoiceFlow()
            .stateInViewModel(emptyList()),
        getString(R.string.select_voice_page_tab_male_voice) to selectVoiceRepo.getMaleVoiceFlow()
            .stateInViewModel(emptyList())
    )

    private val player by lazy {
        ExoPlayer.Builder(App.getInstance()).build()
    }

    var playingVoiceId by mutableStateOf<String?>(null)
        private set

    init {
        refreshLocalVoices()
        listenerPlayingState()
    }

    fun refreshLocalVoices() {
        viewModelScope.launch {
            selectVoiceRepo.refreshLocalVoices()
        }
    }

    fun listenerPlayingState() {
        viewModelScope.launch {
            player.addListener(object : Player.Listener {
                override fun onIsPlayingChanged(isPlaying: Boolean) {
                    super.onIsPlayingChanged(isPlaying)
                    val playingMediaId = player.currentMediaItem?.mediaId
                    if (!isPlaying) {
                        if (playingMediaId == playingVoiceId) {
                            playingVoiceId = null
                        }
                    } else {
                        playingVoiceId = playingMediaId
                    }
                }

                override fun onPlayerError(error: PlaybackException) {
                    super.onPlayerError(error)
                    playingVoiceId = null
                }
            })
        }
    }

    fun onVoiceSelected(voice: Voice) {
        selectedVoice = voice

        viewModelScope.launch {
            val resp = selectVoiceRepo.requestVoiceLink(voice.langId, voice.voice)
            if (resp == null) {
                showToast(getString(R.string.play_audio_failed))
                return@launch
            }
            player.playUrl(resp.audioLink, "${voice.id}")
        }
    }

    private fun stopTTSPlay() {
        player.pause()
        player.stop()
        playingVoiceId = null
    }

    override fun onCleared() {
        super.onCleared()
        stopTTSPlay()
        player.clearMediaItems()
        player.release()
    }
}

val Voice.info
    get() = buildString {
        append(
            if (sex == 1) getString(R.string.guys)
            else getString(R.string.girls)
        )
        append(" / ")
        append(
            when (age) {
                1 -> getString(R.string.select_voice_page_age_child)
                2 -> getString(R.string.select_voice_page_age_adult)
                else -> getString(R.string.select_voice_page_age_old)
            }
        )
        append(" / ")
        append(
            when (langId) {
                1L -> getString(R.string.en)
                12L -> getString(R.string.zh_rtw)
                3L -> getString(R.string.jp)
                else -> getString(R.string.en)
            }
        )
    }