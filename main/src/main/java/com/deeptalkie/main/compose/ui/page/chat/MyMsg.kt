package com.deeptalkie.main.compose.ui.page.chat

import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import com.deeptalkie.main.R
import com.deeptalkie.main.compose.theme.White20
import com.deeptalkie.main.compose.theme.White60
import com.deeptalkie.main.compose.ui.components.DTVerticalSpacer
import com.deeptalkie.main.db.result.MsgWithReply
import com.deeptalkie.main.db.table.AIRole
import com.deeptalkie.main.db.table.MsgSendStatus
import com.deeptalkie.main.db.table.MsgType

@Composable
fun MyMsgView(
    aiRole: AIRole?,
    msgWithReply: MsgWithReply,
    showMsgSelectedPopup: Boolean,
    onClick: () -> Unit,
    onLongClick: () -> Unit,
    onMsgPopupDismiss: () -> Unit,
    onMsgPopupClick: (MsgPopupOptionType) -> Unit,
    onRetrySend: () -> Unit,
) {
    val msgRecord = msgWithReply.msg

    Box(
        Modifier
            .padding(start = 56.dp, end = 16.dp)
            .fillMaxWidth(),
        contentAlignment = Alignment.BottomEnd
    ) {
        when (msgRecord.msgType) {
            MsgType.Text -> MyTextMsgView(aiRole, msgWithReply, onClick, onLongClick, onRetrySend)
            MsgType.Image -> {}
            MsgType.Video -> {}
            MsgType.Voice -> {}
            null -> {}
        }

        if (showMsgSelectedPopup) {
            MsgSelectedPopup(
                onDismiss = onMsgPopupDismiss,
                onClick = onMsgPopupClick
            )
        }
    }
}

@Composable
fun MyTextMsgView(
    aiRole: AIRole?,
    msgWithReply: MsgWithReply,
    onClick: () -> Unit,
    onLongClick: () -> Unit,
    onRetrySend: () -> Unit,
) {
    val (msgRecord, reply) = msgWithReply
    Row {
        when (msgRecord.sendStatus) {
            MsgSendStatus.Sending -> {}

            MsgSendStatus.Failed -> {
                Icon(
                    painterResource(R.drawable.ic_resend_msg),
                    null,
                    Modifier
                        .padding(end = 8.dp, bottom = 8.dp)
                        .size(24.dp)
                        .clip(CircleShape)
                        .clickable(onClick = onRetrySend)
                        .align(Alignment.Bottom),
                    tint = Color.Unspecified
                )
            }

            MsgSendStatus.Success -> {}
        }
        Column(
            Modifier
                .myMsgBg()
                .combinedClickable(
                    onClick = onClick,
                    onLongClick = onLongClick
                )
                .padding(12.dp, 10.dp)
                .animateContentSize()
        ) {
            if (reply != null) {
                ReplyContent(aiRole?.name.orEmpty(), reply.fromSelf, reply.content)
                DTVerticalSpacer(10.dp)
            }
            Text(
                msgRecord.content.toStyledAnnotatedString(),
                style = MaterialTheme.typography.bodySmall.copy(
                    color = MaterialTheme.colorScheme.onPrimary,
                    lineHeight = 20.sp,
                )
            )
        }
    }
}

@Composable
private fun ReplyContent(roleName: String, replySelf: Boolean, content: String) {
    ConstraintLayout(Modifier.padding(end = 12.dp)) {
        val (lineRef, contentRef) = createRefs()
        Box(
            Modifier
                .width(2.dp)
                .clip(RoundedCornerShape(50))
                .background(White20)
                .constrainAs(lineRef) {
                    start.linkTo(parent.start)
                    top.linkTo(parent.top)
                    bottom.linkTo(parent.bottom)
                    height = Dimension.fillToConstraints
                }
        )
        Column(
            Modifier.constrainAs(contentRef) {
                start.linkTo(lineRef.end, 10.dp)
                top.linkTo(parent.top)
            }
        ) {
            if (!replySelf) {
                Text(
                    "$roleName:",
                    style = MaterialTheme.typography.labelMedium.copy(
                        color = White60,
                        lineHeight = 14.sp,
                    )
                )
                DTVerticalSpacer(4.dp)
            }
            Text(
                content,
                maxLines = 2,
                overflow = TextOverflow.Ellipsis,
                style = MaterialTheme.typography.labelMedium.copy(
                    color = White60,
                    lineHeight = 15.sp,
                )
            )
        }
    }
}

@Composable
private fun Modifier.myMsgBg(): Modifier {
    return this
        .clip(
            RoundedCornerShape(12.dp, 12.dp, 2.dp, 12.dp)
        )
        .background(MaterialTheme.colorScheme.primary)
}