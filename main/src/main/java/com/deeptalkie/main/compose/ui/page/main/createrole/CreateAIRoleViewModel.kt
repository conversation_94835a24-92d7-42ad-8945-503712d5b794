package com.deeptalkie.main.compose.ui.page.main.createrole

import android.net.Uri
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.core.net.toUri
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.toRoute
import com.clevguard.utils.ext.logv
import com.deeptalkie.main.R
import com.deeptalkie.main.bean.UserManager
import com.deeptalkie.main.compose.navigation.MainRoute
import com.deeptalkie.main.compose.utils.ILoadingState
import com.deeptalkie.main.compose.utils.loadingState
import com.deeptalkie.main.config.ReportEventUtils
import com.deeptalkie.main.config.UmConstant
import com.deeptalkie.main.db.table.AIRoleTag
import com.deeptalkie.main.db.table.Voice
import com.deeptalkie.main.ext.lengthIn
import com.deeptalkie.main.ext.showToast
import com.deeptalkie.main.ext.stateInViewModel
import com.deeptalkie.main.s3.AwsS3
import com.deeptalkie.main.utils.PromptUtils
import com.deeptalkie.main.utils.getString
import kotlinx.coroutines.async
import kotlinx.coroutines.launch

class CreateAIRoleViewModel(savedStateHandle: SavedStateHandle) : ViewModel(),
    ILoadingState by loadingState() {
    val route = savedStateHandle.toRoute<MainRoute.CreateAIRole>()
    private val createAIRoleRepo = CreateAIRoleRepo()

    val voiceStateFlow = savedStateHandle.getStateFlow<Voice?>(SELECT_VOICE_KEY, null)
    val aiImageResultFlow = savedStateHandle.getStateFlow<String?>(AI_IMAGE_RESULT_KEY, null)

    var selectedImageUri by mutableStateOf<Uri?>(null)
        private set
    var selectedImage by mutableStateOf<String?>(null)
        private set
    var imageVerifiedState by mutableStateOf<VerifiedState>(VerifiedState.NotVerified)
        private set

    var name by mutableStateOf("")
        private set
    var nameInfringingWord by mutableStateOf(emptyList<String>())
        private set

    var description by mutableStateOf("")
        private set
    var descriptionInfringingWord by mutableStateOf(emptyList<String>())
        private set

    var aiDescReqFailState by mutableStateOf<AIDescReqFailState>(AIDescReqFailState.NotFail)
        private set

    var showMoreSetting by mutableStateOf(false)
        private set
    private val _selectedTags = mutableStateListOf<AIRoleTag>()
    val selectedTags: List<AIRoleTag> get() = _selectedTags
    var openingRemarks by mutableStateOf("")
        private set
    var openingRemarksInfringingWord by mutableStateOf(emptyList<String>())
        private set

    var characterIntroduction by mutableStateOf("")
        private set
    var characterIntroductionInfringingWord by mutableStateOf(emptyList<String>())
        private set

    var aiDescriptionLoading by mutableStateOf(false)
        private set

    val allTagsFlow = createAIRoleRepo.getAllTagsFlow().stateInViewModel(emptyList())
    var showSelectTagBottomSheet by mutableStateOf(false)
        private set
    var showCreateTagDialog by mutableStateOf(false)
        private set
    var showCreateRoleBottomSheet by mutableStateOf(false)
        private set
    var showRemainDialog by mutableStateOf(false)
        private set
    var creatingRole = false
        private set

    val pageContentChanged
        get() = selectedImageUri != null ||
                name.isNotEmpty() ||
                description.isNotEmpty() ||
                voiceStateFlow.value != null ||
                selectedTags.isNotEmpty() ||
                openingRemarks.isNotEmpty() ||
                characterIntroduction.isNotEmpty()

    init {
        loadTags()

        viewModelScope.launch {
            aiImageResultFlow.collect { image ->
                logv("选择ai生成图片:$image")
                if (image.isNullOrBlank()) return@collect
                selectedImageUri = image.toUri()
                selectedImage = image
                imageVerifiedState = VerifiedState.Verified
            }
        }

        ReportEventUtils.onEvent(
            UmConstant.CREATE_CHARACTER,
            mapOf(UmConstant.CREATE_CHARACTER to "View_page")
        )
    }

    private fun loadTags() {
        viewModelScope.launch {
            createAIRoleRepo.loadAllTagsToDb()
        }
    }

    fun showRemainDialog(show: Boolean) {
        showRemainDialog = show
    }

    fun onRemoveImage() {
        selectedImageUri = null
        selectedImage = null
        imageVerifiedState = VerifiedState.NotVerified
    }

    fun onSelectedImage(uri: Uri) {
        viewModelScope.launch {
            selectedImage = null
            selectedImageUri = uri

            imageVerifiedState = VerifiedState.Verifying

            val remoteUrl = AwsS3.uploadFileByUri(uri)

            imageVerifiedState = when {
                selectedImageUri == null -> {
                    logv("上传已取消")
                    VerifiedState.NotVerified
                }

                remoteUrl == null -> {
                    logv("上传失败")
                    showToast(getString(R.string.upload_image_failed))
                    VerifiedState.VerifyFail
                }

                else -> {
                    logv("上传成功")
                    ReportEventUtils.onEvent(
                        UmConstant.UPLOAD_IMAGE,
                        mapOf(UmConstant.UPLOAD_IMAGE to "Upload_succeed")
                    )
                    selectedImage = remoteUrl
                    VerifiedState.Verified
                }
            }
        }
    }

    fun onNameChange(name: String) {
        this.name = name
    }

    fun onDescriptionChange(description: String) {
        this.description = description
    }

    fun onSelectTag(tag: AIRoleTag, isSelected: Boolean) {
        if (isSelected) {
            if (selectedTags.size >= 4) return
            _selectedTags += tag
        } else {
            _selectedTags -= tag
        }
    }

    fun onOpeningRemarksChange(remarks: String) {
        openingRemarks = remarks
    }

    fun onCharacterIntroductionChange(introduction: String) {
        characterIntroduction = introduction
    }

    fun requestAIDescription() {
        viewModelScope.launch {
            aiDescReqFailState = AIDescReqFailState.NotFail
            aiDescriptionLoading = true
            val desc = createAIRoleRepo.requestAIRoleDesc(route.aiRoleSex)
                ?.description.orEmpty()
            if (desc.isEmpty()) {
                aiDescReqFailState = AIDescReqFailState.GenFail
            } else {
                description = desc
            }
            aiDescriptionLoading = false
        }
    }

    fun requestAIDescriptionOptimize() {
        viewModelScope.launch {
            aiDescReqFailState = AIDescReqFailState.NotFail
            aiDescriptionLoading = true
            val desc = createAIRoleRepo.requestAIRoleDescOptimize(description, route.aiRoleSex)
                ?.description.orEmpty()
            if (desc.isEmpty()) {
                aiDescReqFailState = AIDescReqFailState.OptimizeFail
            } else {
                description = desc
            }
            aiDescriptionLoading = false
        }
    }

    fun onRetryGenDesc() {
        when (aiDescReqFailState) {
            AIDescReqFailState.GenFail -> requestAIDescription()
            AIDescReqFailState.OptimizeFail -> requestAIDescriptionOptimize()
            AIDescReqFailState.NotFail -> {}
        }
    }

    fun showMoreSetting() {
        showMoreSetting = true
    }

    fun showSelectTagBottomSheet(show: Boolean) {
        showSelectTagBottomSheet = show
    }

    fun showCreateTagDialog(show: Boolean) {
        showCreateTagDialog = show
    }

    suspend fun createTag(tag: String): Boolean {
        showLoading(true)
        val verifyResult = PromptUtils.checkSensitive(tag)
        if (verifyResult?.labels?.contains("regional") == true) {
            logv("检查到违禁词:${tag}")
            showLoading(false)
            showToast(getString(R.string.create_ai_role_page_check_content_tips))
            return false
        }
        val res = createAIRoleRepo.createTag(tag)
        if (res == null) {
            showLoading(false)
            showToast(getString(R.string.network_error))
            return false
        }
        showLoading(false)
        return true
    }

    fun canCreate(): Boolean {
        return selectedImage != null &&
                name.lengthIn(1..40) &&
                description.isNotEmpty() &&
                voiceStateFlow.value != null &&
                openingRemarks.lengthIn(0..600) &&
                characterIntroduction.lengthIn(0..600)
    }

    suspend fun checkContent(): Boolean {
        return viewModelScope.async {
            showLoading(true)
            val allCheckedResult = buildList {
                if (name.isNotBlank()) add(name)
                // if (description.isNotBlank()) add(description) // 角色描述不校验敏感词
                if (openingRemarks.isNotBlank()) add(openingRemarks)
                if (characterIntroduction.isNotBlank()) add(characterIntroduction)
            }.mapIndexed { index, text ->
                async {
                    index to PromptUtils.checkSensitive(text)
                }
            }.map { it.await() }

            if (allCheckedResult.any { it.second == null }) {
                showLoading(false)
                showToast(getString(R.string.network_error))
                return@async false
            }

            val allChecked = allCheckedResult.map { (index, result) ->
                if (result?.labels == "regional") {
                    val riskWords = result.reason?.riskWords.orEmpty().split(",")
                    logv("检查到违禁词:$riskWords")
                    when (index) {
                        0 -> nameInfringingWord = riskWords
                        1 -> descriptionInfringingWord = riskWords
                        2 -> openingRemarksInfringingWord = riskWords
                        3 -> characterIntroductionInfringingWord = riskWords
                    }
                    return@map false
                }
                true
            }.all { it }
            showLoading(false)
            if (!allChecked) {
                showToast(getString(R.string.create_ai_role_page_check_content_tips))
            }
            allChecked
        }.await()
    }

    fun showCreateRoleBottomSheet(show: Boolean) {
        showCreateRoleBottomSheet = show
    }

    suspend fun createRole(isPublic: Boolean): Long? {
        return viewModelScope.async {
            showLoading(true)
            creatingRole = true
            UserManager.plusCreateAIRoleCount()
            val roleId = createAIRoleRepo.createRole(
                name,
                characterIntroduction,
                description,
                openingRemarks,
                selectedImage!!,
                voiceStateFlow.value!!.id,
                if (isPublic) 1 else 0,
                selectedTags.joinToString(",") { it.id.toString() }
            )
            creatingRole = false
            showLoading(false)
            roleId
        }.await()
    }

    /**
     * 输入项验证状态
     * @see VerifiedState.NotVerified 未验证
     * @see VerifiedState.Verifying 验证中
     * @see VerifiedState.Verified 验证通过
     * @see VerifiedState.VerifyFail 验证失败
     */
    sealed interface VerifiedState {
        object NotVerified : VerifiedState
        object Verifying : VerifiedState
        object Verified : VerifiedState
        object VerifyFail : VerifiedState
    }
}