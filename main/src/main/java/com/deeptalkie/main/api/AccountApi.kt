package com.deeptalkie.main.api

import com.imyfone.membership.Constant
import com.imyfone.membership.api.bean.GuestBean
import com.mfccgroup.android.httpclient.adapter.API
import retrofit2.http.Field
import retrofit2.http.FormUrlEncoded
import retrofit2.http.POST

interface AccountApi {
    /** 通过品牌、操作系统、设备标识生成并返回游客标识 */
    @FormUrlEncoded
    @POST("/app/v2/report-data")
    suspend fun reportAndCreateGuest(
        @Field("software_code") softwareCode: String,
        @Field("lang") language: String,
        @Field("source_site") sourceSite: String?,
        @Field("information_sources") informationSources: String,
        @Field("operating_type") operatingType: String = Constant.PLATFORM_APP,
        @Field("operating_system") operatingSystem: String = "android",
    ): API<GuestBean>
}