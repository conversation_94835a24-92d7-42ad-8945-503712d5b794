package com.deeptalkie.main.api

import com.deeptalkie.kidsguard.net.ListResponse
import com.deeptalkie.kidsguard.net.Response
import com.deeptalkie.main.bean.VoiceBean
import com.deeptalkie.main.bean.VoiceLink
import retrofit2.http.GET
import retrofit2.http.Query

interface CommonApi {
    @GET("api/tts/eleven-labs-voiceover")
    suspend fun getVoices(
        @Query("lang_id") langId: Long,
        @Query("cate_id") cateId: Long? = null,
        @Query("sex") sex: String = "1,2,3",
        @Query("age") age: String = "1,2,3",
        @Query("timestamp") timestamp: Long = System.currentTimeMillis() / 1000
    ): ListResponse<List<VoiceBean>>

    @GET("api/tts/eleven-labs-audio-link")
    suspend fun getVoiceLink(
        @Query("lang_id") langId: Long,
        @Query("voice") voice: String,
        @Query("timestamp") timestamp: Long = System.currentTimeMillis() / 1000
    ): Response<VoiceLink>
}