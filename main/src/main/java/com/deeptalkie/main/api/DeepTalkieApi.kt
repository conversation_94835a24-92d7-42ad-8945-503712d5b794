package com.deeptalkie.main.api

import com.deeptalkie.kidsguard.net.ListResponse
import com.deeptalkie.kidsguard.net.Resp
import com.deeptalkie.kidsguard.net.Response
import com.deeptalkie.main.BuildConfig
import com.deeptalkie.main.bean.AIImageResult
import com.deeptalkie.main.bean.AIImageStyleListResp
import com.deeptalkie.main.bean.AIReplyResp
import com.deeptalkie.main.bean.AIRoleDescResp
import com.deeptalkie.main.bean.AIRoleTagsResp
import com.deeptalkie.main.bean.AIRoleWithTagsResp
import com.deeptalkie.main.bean.AskResp
import com.deeptalkie.main.bean.AssetsBean
import com.deeptalkie.main.bean.AwsConfigResp
import com.deeptalkie.main.bean.ChatMessagesResult
import com.deeptalkie.main.bean.CoinBean
import com.deeptalkie.main.bean.ConfigBean
import com.deeptalkie.main.bean.CreateAIRoleResp
import com.deeptalkie.main.bean.CreateTagResp
import com.deeptalkie.main.bean.ExploreRoleBean
import com.deeptalkie.main.bean.FavoriteBean
import com.deeptalkie.main.bean.FavoriteRes
import com.deeptalkie.main.bean.HomeBean
import com.deeptalkie.main.bean.InChineseMainland
import com.deeptalkie.main.bean.ListData
import com.deeptalkie.main.bean.RandomChatData
import com.deeptalkie.main.bean.RequestChatResult
import com.deeptalkie.main.bean.RoleDetailBean
import com.deeptalkie.main.bean.SearchResultBean
import com.deeptalkie.main.bean.SendMsgResp
import com.deeptalkie.main.bean.SessionBean
import com.deeptalkie.main.bean.SwAgentData
import com.deeptalkie.main.bean.SwData
import com.deeptalkie.main.bean.TTS
import com.deeptalkie.main.bean.TextModerationBean
import com.deeptalkie.main.bean.VersionBean
import com.mfccgroup.android.httpclient.adapter.API
import retrofit2.http.Field
import retrofit2.http.FormUrlEncoded
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query

const val AppCheckUpdateUrl = BuildConfig.AppCheckUpdate

interface DeepTalkieApi {
    @GET("home")
    suspend fun getHomeData(): Response<HomeBean>

    @GET("search")
    suspend fun searchRole(@Query("word") word: String): ListResponse<List<SearchResultBean>>

    @GET("is-favorite")
    suspend fun isFavorite(@Query("id") roleId: Long): Response<FavoriteRes>

    @GET("sessions")
    suspend fun getSessionList(): ListResponse<List<SessionBean>>

    @GET("random")
    suspend fun randomChat(): Response<RandomChatData>

    @GET("chat")
    suspend fun requestChat(@Query("id") id: Long): Response<RequestChatResult>

    @GET("messages")
    suspend fun requestChatMessages(
        @Query("session_id") sessionId: Long,
        @Query("last_id") lastId: Long? = null,
        @Query("page_size") pageSize: Int,
    ): ListResponse<List<ChatMessagesResult>>

    @GET("role")
    suspend fun getRoleDetail(@Query("id") id: Long): API<RoleDetailBean>

    @GET("explore")
    suspend fun getExploreRoles(
        @Query("module") module: String,
        @Query("page") page: Int = 1,
        @Query("page_size") pageSize: Int = 6,
    ): ListResponse<List<ExploreRoleBean>>

    @GET("activity")
    suspend fun activity(@Query("key") key: String = "key1"): Response<ConfigBean>

    @POST("favorite")
    @FormUrlEncoded
    suspend fun favorite(
        @Field("id") id: Long,
        @Field("favorite") favorite: Int,
    ): Response<String>

    /**
     * 语音合成
     */
    @GET("https://lac-api.deeptalkie.com/api/tts/audio-with-timestamp")
    suspend fun audioWithTimestamp(
        @Query("text") text: String?,
        @Query("voiceover_id") voiceoverId: String?,
        @Query("speech_rate") speechRate: String?,
    ): Response<TTS>

    @POST("delete-session")
    @FormUrlEncoded
    suspend fun deleteSession(@Field("id") id: Long): Response<String>

    @POST("pin-to-top")
    @FormUrlEncoded
    suspend fun pipToTop(
        @Field("session_id") sessionId: Long,
        @Field("pin_to_top") pipToTop: Int,
    ): Response<String>

    @POST("inspiration")
    @FormUrlEncoded
    suspend fun inspiration(
        @Field("session_id") sessionId: Long,
        @Field("content") content: String,
    ): Response<AIReplyResp>

    /**
     * # 敏感词接口
     * ```kotlin
     * val labels = PromptUtils.checkSensitive(prompt)
     * if (labels.isNotEmpty() && labels == "regional") {
     *      cb.invoke(-436)
     *      return@launch
     * }
     * ```
     */
    @POST("https://lac-api.deeptalkie.com/api/utils/text-moderation")
    @FormUrlEncoded
    suspend fun checkSensitive(@Field("content") prompt: String): Response<TextModerationBean>

    @GET("https://ut-api.deeptalkie.com/ip/ipInChineseMainland")
    suspend fun getIpInfo(): Response<InChineseMainland>

    @POST("member-info")
    @FormUrlEncoded
    suspend fun submitMemberInfo(
        @Field("name") name: String,
        @Field("email") email: String,
        @Field("age") age: Int,
        @Field("gender") gender: Int,
    ): Response<String>

    @POST("send-message")
    @FormUrlEncoded
    suspend fun sendMsg(
        @Field("session_id") sessionId: Long,
        @Field("content") content: String,
        @Field("reply_id") replyId: Long,
        @Field("type") type: Int,
        @Field("time") time: Long = System.currentTimeMillis() / 1000,
        @Field("timestamp") timestamp: Long = time
    ): Response<SendMsgResp>

    @POST("delete-message")
    @FormUrlEncoded
    suspend fun deleteMsg(@Field("id") msgId: Long): Response<Any>

    @GET("sts-config")
    suspend fun getAwsConfig(): Response<AwsConfigResp>

    /**
     * 检查app版本更新
     */
    @GET("${AppCheckUpdateUrl}v2/verinfo")
    suspend fun getVersionInfo(
        @Query("pid") pid: String,
        @Query("lang") lang: String
    ): Resp<VersionBean>

    @GET("ai-role-description")
    suspend fun requestAIRoleDesc(@Query("gender") gender: Int): Response<AIRoleDescResp>

    @GET("ai-role-description-optimize")
    suspend fun requestAIRoleDescOptimize(
        @Query("prompt") prompt: String,
        @Query("gender") gender: Int
    ): Response<AIRoleDescResp>

    @GET("ai-role-image-description")
    suspend fun requestAIRoleImageDesc(@Query("gender") gender: Int): Response<AIRoleDescResp>

    @GET("ai-role-image-description-optimize")
    suspend fun requestAIRoleImageDescOptimize(
        @Query("prompt") prompt: String,
        @Query("gender") gender: Int
    ): Response<AIRoleDescResp>

    /**
     * 账号金币信息接口
     */
    @GET("account")
    suspend fun getAccountInfo(): API<CoinBean>

    @POST("create-role")
    @FormUrlEncoded
    suspend fun createRole(
        @Field("name") name: String,
        @Field("description") description: String,
        @Field("prompt") prompt: String,
        @Field("hello_word") helloWord: String,
        @Field("image") image: String,
        @Field("voice_id") voiceId: Long,
        @Field("is_public") isPublic: Int,
        @Field("tag_ids") tagIds: String,
    ): Response<CreateAIRoleResp>

    /**
     * 获取角色标签列表
     */
    @GET("tags")
    suspend fun getTags(
        @Query("including_not_used") includingNotUsed: Int = 0,
        @Query("module") module: String? = null,
    ): API<AIRoleTagsResp>

    /**
     * 获取标签下的角色
     */
    @GET("roles-for-tag")
    suspend fun getRolesForTag(
        @Query("id") tagId: Long,
        @Query("module") module: String,
        @Query("page") page: Int,
        @Query("page_size") pageSize: Int,
    ): API<List<AIRoleWithTagsResp>>

    @GET("my-favorite")
    suspend fun getMyFavorite(
        @Query("page") page: Int = 1,
        @Query("page_size") pageSize: Int = 8,
    ): API<FavoriteBean>

    @GET("my-assets")
    suspend fun getMyAssets(
        @Query("page") page: Int = 1,
        @Query("page_size") pageSize: Int = 8,
    ): API<AssetsBean>

    @POST("create-tag")
    @FormUrlEncoded
    suspend fun createTag(@Field("name") name: String): API<CreateTagResp>

    @GET("image-styles")
    suspend fun getAIImageStyleList(): API<AIImageStyleListResp>

    @POST("generate-image")
    @FormUrlEncoded
    suspend fun generateAIImage(
        @Field("prompt") prompt: String,
        @Field("gender") gender: Int,
        @Field("style_prompt") stylePrompt: String,
    ): API<CreateAIRoleResp>

    /**
     * 查询图片生成结果
     */
    @GET("job")
    suspend fun queryImageResult(@Query("id") id: Long): Response<AIImageResult>

    @GET("talk-suggestion")
    suspend fun getTalkSuggestion(@Query("id") roleId: Long): API<ListData<String>>

    @POST("rtc/get-token")
    @FormUrlEncoded
    suspend fun getSwToken(
        @Field("type") type: String,
    ): API<SwData>

    @POST("rtc/join")
    @FormUrlEncoded
    suspend fun join(
        @Field("token") token: String,
        @Field("name") name: String,
    ): API<SwAgentData>

    @POST("rtc/leave")
    @FormUrlEncoded
    suspend fun leave(
        @Field("agent_id") agentId: String,
    ): API<String>

    @GET("ask")
    suspend fun ask(
        @Query("content") content: String,
        @Query("stream") stream: Int = 0,
        @Query("time") time: Long = System.currentTimeMillis() / 1000,
        @Query("reply_id") replyId: Long = 0,
        @Query("type") type: Int = 1,
    ): API<AskResp>
}