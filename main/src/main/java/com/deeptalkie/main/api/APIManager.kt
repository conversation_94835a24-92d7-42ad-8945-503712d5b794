package com.deeptalkie.main.api

import com.deeptalkie.kidsguard.net.client.newFastJsonRetrofitApi
import com.deeptalkie.kidsguard.net.client.newMoshiRetrofitApi
import com.deeptalkie.main.BuildConfig

val deepTalkieApi
    get() = newMoshiRetrofitApi<DeepTalkieApi>(BuildConfig.BaseUrlDeepTalkie)

val deepTalkieApiFastJson
    get() = newFastJsonRetrofitApi<DeepTalkieApi>(BuildConfig.BaseUrlDeepTalkie)

val commonApi
    get() = newMoshiRetrofitApi<CommonApi>(BuildConfig.CommonApi)

val accountApi
    get() = newFastJsonRetrofitApi<AccountApi>(com.imyfone.membership.BuildConfig.ACCOUNT_API)