//package com.deeptalkie.main.viewmodel
//
//import android.util.Log
//import androidx.lifecycle.MutableLiveData
//import androidx.lifecycle.ViewModel
//import androidx.lifecycle.viewModelScope
//import com.deeptalkie.kidsguard.net.Api
//import com.deeptalkie.kidsguard.net.error
//import com.deeptalkie.kidsguard.net.successFun
//import com.deeptalkie.main.App
//import com.deeptalkie.main.api.DeepTalkieApi
//import com.deeptalkie.main.bean.RoleDetailBean
//import com.deeptalkie.main.bean.UserManager
//import com.deeptalkie.main.db.DTDatabase
//import com.deeptalkie.main.db.table.UserAIRole
//import com.deeptalkie.main.config.TimberUtil
//import kotlinx.coroutines.Dispatchers
//import kotlinx.coroutines.launch
//import kotlinx.coroutines.withContext
//import kotlinx.io.IOException
//import java.io.File
//import java.io.FileOutputStream
//
//class RoleDetailViewModel : ViewModel() {
//    companion object {
//        private const val TAG = "RoleDetailViewModel"
//    }
//
//    val roleDetailMLD = MutableLiveData<RoleDetailBean?>()
//
//    fun getRole(id: Long) {
//        viewModelScope.launch {
//            roleDetailMLD.value = aiRoleDao.queryAIRoleAsync(id)?.toRoleDetailBean()
//            Api.with(DeepTalkieApi::class.java).getRoleDetail(id).successFun {
//                TimberUtil.d(TAG, "it = $it")
//                roleDetailMLD.value = it
//            }.error { i, s ->
//                TimberUtil.d(TAG, "i = $i,s = $s")
//                roleDetailMLD.value = null
//            }
//        }
//    }
//
//    private val aiRoleDao by lazy {
//        DTDatabase.instance.aiRoleDao()
//    }
//
//    private val userAIRoleDao by lazy {
//        DTDatabase.instance.userAIRoleDao()
//    }
//
//    val favoriteMLD = MutableLiveData<Int>()
//
//    fun favorite(roleId: Long, favorite: Int) {
//        val userId = UserManager.getUserId() ?: ""
//        viewModelScope.launch {
//            userAIRoleDao.upsertOne(UserAIRole(userId, roleId, isFavorite = favorite == 1))
//            Api.with(DeepTalkieApi::class.java).favorite(roleId, favorite).successFun {
//                TimberUtil.d(TAG, "success $it")
//                favoriteMLD.value = 200
//            }.error { i, s ->
//                TimberUtil.d(TAG, "i = $i,s = $s")
//                favoriteMLD.value = i
//            }
//        }
//    }
//
//    suspend fun saveByteArrayToFile(byteArray: ByteArray, fileName: String, cb: (Boolean) -> Unit) {
//        withContext(Dispatchers.IO) {
//            try {
//                // 1. 获取应用私有目录（无需权限）
//                val file = File(App.getInstance().filesDir, fileName)
//                // 2. 将 ByteArray 写入文件
//                FileOutputStream(file).use { fos ->
//                    fos.write(byteArray)
//                    fos.flush()
//                }
//                // 可选：提示保存路径
//                Log.d("SAVE_PATH", "文件保存成功: ${file.absolutePath}")
//                cb.invoke(true)
//            } catch (e: IOException) {
//                e.printStackTrace()
//                cb.invoke(false)
//            }
//        }
//    }
//}