package com.deeptalkie.main.ext

import android.bluetooth.BluetoothManager
import android.content.Context
import android.graphics.drawable.Drawable
import android.location.LocationManager
import android.net.wifi.WifiManager
import android.text.format.DateFormat
import android.view.View
import android.view.ViewGroup
import androidx.activity.ComponentActivity
import androidx.annotation.ColorRes
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.annotation.StyleRes
import androidx.appcompat.view.ContextThemeWrapper
import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.TextUnit
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.deeptalkie.main.compose.theme.DTTheme
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch

inline fun ComponentActivity.launchAndRepeatOnLifecycle(
    minActiveState: Lifecycle.State = Lifecycle.State.STARTED,
    crossinline block: suspend CoroutineScope.() -> Unit
) {
    lifecycleScope.launch {
        repeatOnLifecycle(minActiveState) {
            block()
        }
    }
}

inline fun Fragment.launchAndRepeatOnLifecycle(
    minActiveState: Lifecycle.State = Lifecycle.State.STARTED,
    crossinline block: suspend CoroutineScope.() -> Unit
) {
    viewLifecycleOwner.lifecycleScope.launch {
        repeatOnLifecycle(minActiveState) {
            block()
        }
    }
}

fun Context.getCompatColor(@ColorRes colorResId: Int): Int {
    return ContextCompat.getColor(this, colorResId)
}

fun View.getColor(@ColorRes colorResId: Int): Int {
    return context.getCompatColor(colorResId)
}

fun View.getDrawable(@DrawableRes id: Int): Drawable? {
    return ContextCompat.getDrawable(context, id)
}

fun View.getString(@StringRes strResId: Int, vararg formatArgs: Any = arrayOf()): String {
    return context.getString(strResId, formatArgs)
}

fun Context.withStyle(@StyleRes themeResId: Int) = ContextThemeWrapper(this, themeResId)

@Composable
fun Content(
    block: @Composable () -> Unit
) {
    DTTheme {
        block()
    }
}

fun ComponentActivity.newComposeContent(
    layoutParams: ViewGroup.LayoutParams = ViewGroup.LayoutParams(matchParent, matchParent),
    block: @Composable () -> Unit
) = ComposeView(this).apply {
    this.layoutParams = layoutParams
    setContent {
        Content(block)
    }
}

fun ComponentActivity.newComposeContent(
    width: Int = matchParent,
    height: Int = matchParent,
    block: @Composable () -> Unit
) = ComposeView(this).apply {
    this.layoutParams = ViewGroup.LayoutParams(width, height)
    setContent {
        Content(block)
    }
}

fun Fragment.newComposeContent(
    layoutParams: ViewGroup.LayoutParams = ViewGroup.LayoutParams(matchParent, matchParent),
    block: @Composable () -> Unit
) = ComposeView(requireActivity()).apply {
    this.layoutParams = layoutParams
    setViewCompositionStrategy(ViewCompositionStrategy.DisposeOnViewTreeLifecycleDestroyed)
    setContent {
        Content(block)
    }
}

fun Context.newComposeView(
    width: Int = matchParent,
    height: Int = matchParent,
) = ComposeView(this).apply {
    layoutParams = ViewGroup.LayoutParams(
        width,
        height
    )
}

fun ComposeView.content(
    block: @Composable () -> Unit
) {
    setContent {
        Content(block)
    }
}

fun Context.newComposeContent(
    layoutParams: ViewGroup.LayoutParams = ViewGroup.LayoutParams(matchParent, matchParent),
    block: @Composable () -> Unit
) = ComposeView(this).apply {
    this.layoutParams = layoutParams
    setContent {
        Content(block)
    }
}

val View.parentView get() = parent as ViewGroup

val View.activity get() = context as FragmentActivity

fun View.newComposeContent(
    layoutParams: ViewGroup.LayoutParams = ViewGroup.LayoutParams(matchParent, matchParent),
    block: @Composable () -> Unit
) = activity.newComposeContent(layoutParams, block)

fun View.newComposeContent(
    width: Int = matchParent,
    height: Int = matchParent,
    block: @Composable () -> Unit
) = activity.newComposeContent(ViewGroup.LayoutParams(width, height), block)


fun Context.isBluetoothEnable(): Boolean =
    (getSystemService(Context.BLUETOOTH_SERVICE) as BluetoothManager).let {
        it.adapter != null && it.adapter.isEnabled
    }

val Context.locationManager get() = getSystemService(Context.LOCATION_SERVICE) as LocationManager

fun Context.isLocationEnable(): Boolean {
    return locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER)
}

fun Context.isWifiEnable(): Boolean = (applicationContext.getSystemService(Context.WIFI_SERVICE) as
        WifiManager).isWifiEnabled


fun Context.is24HourFormat() = DateFormat.is24HourFormat(this)

const val matchParent = ViewGroup.LayoutParams.MATCH_PARENT
const val wrapContent = ViewGroup.LayoutParams.WRAP_CONTENT

val Dp.toPx: Float
    @Composable get() = LocalDensity.current.run {
        <EMAIL>()
    }

val TextUnit.toPx: Float
    @Composable get() = LocalDensity.current.run {
        <EMAIL>()
    }

val Int.toDp: Dp
    @Composable get() = with(LocalDensity.current) {
        toDp()
    }