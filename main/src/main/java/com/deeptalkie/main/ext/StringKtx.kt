package com.deeptalkie.main.ext

import android.content.Context
import androidx.core.net.toUri
import androidx.media3.common.MediaItem
import androidx.media3.common.Player
import androidx.media3.exoplayer.ExoPlayer
import coil3.ImageLoader
import coil3.request.ImageRequest
import com.clevguard.utils.ext.loge
import com.clevguard.utils.ext.logv
import com.deeptalkie.main.App
import com.deeptalkie.main.config.TimberUtil
import com.imyfone.track.track.TAG
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import java.net.URI
import kotlin.coroutines.resume
import kotlin.time.Duration.Companion.seconds

fun String.toNoQueryURI(): String {
    return try {
        val uri = URI(this)

        val key = URI(uri.scheme, uri.authority, uri.path, null, uri.fragment).toString()
        TimberUtil.d(TAG, "uri = $this,key = $key")
        key
    } catch (_: Exception) {
        this
    }
}

fun String.imageRequest(
    context: Context,
    block: ImageRequest.Builder.() -> Unit = {}
): ImageRequest {
    val key = toNoQueryURI()
    return ImageRequest.Builder(context)
        .data(this)
        .diskCacheKey(key)
        .memoryCacheKey(key)
        .apply(block)
        .build()
}

fun String.uriEquals(other: String): Boolean {
    return try {
        toNoQueryURI() == other.toNoQueryURI()
    } catch (_: Exception) {
        false
    }
}

suspend fun String.getVideoSize(): Pair<Int, Int> {
    return withContext(Dispatchers.IO) {
        val start = System.currentTimeMillis()
        try {
            var width = 1
            var height = 1

            val image = getImage()

            if (image != null) {
                width = image.width
                height = image.height
            }

            logv("获取宽高：url=${this@getVideoSize}, w=$width, h=$height, time=${System.currentTimeMillis() - start}")

            width to height
        } catch (e: Exception) {
            e.printStackTrace()
            1 to 1
        }
    }
}

/**
 * 通过视频 URL 获取视频时长。
 * 此方法会创建一个临时的 ExoPlayer 实例来加载媒体信息，但不会开始播放。
 * @param context 应用上下文。
 * @param videoUrl 视频的 URL。
 * @return 视频时长（毫秒），如果获取失败则返回 null。
 */
suspend fun getVideoDurationFromUrl(videoUrl: String): Long? {
    return suspendCancellableCoroutine { continuation ->
        var player: ExoPlayer? = null // 定义为可空，以便在 finally 块中安全释放
        try {
            player = ExoPlayer.Builder(App.getInstance()).build()

            // 创建 MediaItem
            val mediaItem = MediaItem.fromUri(videoUrl.toUri())
            player.setMediaItem(mediaItem)

            var isDurationFetched = false // 确保只获取一次时长

            val listener = object : Player.Listener {
                override fun onPlaybackStateChanged(playbackState: Int) {
                    // 当播放器准备好时，时长信息就可用了
                    if (playbackState == Player.STATE_READY && !isDurationFetched) {
                        isDurationFetched = true
                        val duration = player.duration // 获取时长，单位是毫秒
                        if (continuation.isActive) {
                            continuation.resume(duration) // 恢复协程并返回时长
                        }
                        player.release() // 立即释放播放器资源
                    } else if (playbackState == Player.STATE_ENDED || playbackState == Player.STATE_IDLE) {
                        // 如果播放器进入结束或空闲状态，但之前没有获取到时长（例如URL无效或格式不支持）
                        if (!isDurationFetched && continuation.isActive) {
                            continuation.resume(null) // 返回 null 表示无法获取
                            player.release() // 立即释放播放器资源
                        }
                    }
                }

                override fun onPlayerError(error: androidx.media3.common.PlaybackException) {
                    // 处理错误情况，例如 URL 无效、网络问题、媒体格式不支持等
                    if (!isDurationFetched && continuation.isActive) {
                        continuation.resume(null) // 返回 null 表示发生错误
                        player.release() // 立即释放播放器资源
                    }
                    loge("获取视频时长失败，${error.stackTraceToString()}")
                }
            }

            player.addListener(listener)
            player.prepare() // 开始准备播放器，加载媒体信息。不会开始播放！

            // 如果协程被取消，确保释放播放器资源
            continuation.invokeOnCancellation {
                player.release()
            }
        } catch (e: Exception) {
            // 捕获构建播放器或解析 URI 时的同步异常
            if (continuation.isActive) {
                continuation.resume(null)
            }
            player?.release()
            loge("获取视频时长失败，${e.stackTraceToString()}")
        }
    }
}

fun Long.toFormattedTime(isSeconds: Boolean = false): String {
    val secondsTime = if (isSeconds) this else this / 1000
    return secondsTime.seconds.toComponents { _, hours, minutes, seconds, _ ->
        val hour = hours.toString().padStart(2, '0')
        val min = minutes.toString().padStart(2, '0')
        val sec = seconds.toString().padStart(2, '0')

        buildString {
            if (hours > 0) {
                append(hour)
                append(":")
            }
            append(min)
            append(":")
            append(sec)
        }
    }
}

suspend fun String.getImage() = suspendCancellableCoroutine {
    ImageLoader.Builder(App.getInstance()).build()
        .enqueue(
            ImageRequest.Builder(App.getInstance())
                .data(this@getImage)
                .listener(
                    onSuccess = { _, res ->
                        if (it.isActive) {
                            it.resume(res.image)
                        }
                    },
                    onError = { _, _ ->
                        if (it.isActive) {
                            it.resume(null)
                        }
                    },
                    onCancel = { _ ->
                        if (it.isActive) {
                            it.resume(null)
                        }
                    },
                )
                .build()
        )
}

fun String.lengthIn(range: IntRange): Boolean {
    return length in range
}