package com.deeptalkie.main.ext

import com.deeptalkie.kidsguard.net.ws.WsData
import com.deeptalkie.main.db.table.MsgRecord
import com.deeptalkie.main.db.table.MsgSendStatus

fun WsData.toMsgRecord(userId: String, videoTime: String): MsgRecord {
    val prompt: String
    val (msgType, content, isLock) = when (this) {
        is WsData.Text -> {
            prompt = ""
            Triple(1, text, 0)
        }

        is WsData.Image -> {
            prompt = this.prompt.orEmpty()
            Triple(2, url, isLock)
        }

        is WsData.Video -> {
            prompt = ""
            Triple(3, url, isLock)
        }
    }
    return MsgRecord(
        msgId = msgId,
        roleId = roleId,
        speakerType = 1,
        type = msgType,
        content = content,
        createdAt = time,
        userId = userId,
        isRead = false,
        replyId = replyId,
        isLock = isLock,
        reply = reply,
        videoTime = videoTime,
        prompt = prompt,
        sendStatus = MsgSendStatus.Success
    )
}