package com.deeptalkie.main.rtc

import android.content.Context
import com.clevguard.utils.ext.logv
import io.agora.rtc2.ChannelMediaOptions
import io.agora.rtc2.Constants
import io.agora.rtc2.IRtcEngineEventHandler
import io.agora.rtc2.LocalTranscoderConfiguration
import io.agora.rtc2.RtcEngine
import io.agora.rtc2.RtcEngineConfig

class VoiceRtc(
    private val baseContext: Context,
    private val swAppId: String,
) {
    private val rtcEngine = RtcEngine.create(
        RtcEngineConfig().apply {
            mContext = baseContext
            mAppId = swAppId
            mEventHandler = object : IRtcEngineEventHandler() {
                // 成功加入频道回调
                override fun onJoinChannelSuccess(channel: String?, uid: Int, elapsed: Int) {
                    super.onJoinChannelSuccess(channel, uid, elapsed)
                    showLog("加入语音成功")
                }

                // 远端用户或主播加入当前频道回调
                override fun onUserJoined(uid: Int, elapsed: Int) {
                    super.onUserJoined(uid, elapsed)
                    showLog("用户加入: $uid")
                }

                // 远端用户或主播离开当前频道回调
                override fun onUserOffline(uid: Int, reason: Int) {
                    super.onUserOffline(uid, reason)
                    showLog("用户离开: $uid")
                }

                override fun onError(err: Int) {
                    super.onError(err)
                    showLog("错误: $err")
                }

                override fun onEncryptionError(errorType: Int) {
                    super.onEncryptionError(errorType)
                    showLog("加密错误: $errorType")
                }

                override fun onPermissionError(permission: Int) {
                    super.onPermissionError(permission)
                    showLog("权限错误: $permission")
                }

                override fun onStreamMessageError(
                    uid: Int, streamId: Int, error: Int, missed: Int, cached: Int
                ) {
                    super.onStreamMessageError(uid, streamId, error, missed, cached)
                    showLog("流消息错误: $uid, $streamId, $error, $missed, $cached")
                }

                override fun onLocalVideoTranscoderError(
                    stream: LocalTranscoderConfiguration.TranscodingVideoStream?, error: Int
                ) {
                    super.onLocalVideoTranscoderError(stream, error)
                    showLog("本地视频转码错误: $stream, $error")
                }
            }
        }
    )

    fun joinChannel(token: String, channelId: String, userId: String) {
        rtcEngine.run {
            // 创建 ChannelMediaOptions 对象，并进行配置
            val options = ChannelMediaOptions()
            // 设置用户角色为 BROADCASTER (主播) 或 AUDIENCE (观众)
            options.clientRoleType = Constants.CLIENT_ROLE_BROADCASTER
            // 设置频道场景为 BROADCASTING (直播场景)
            options.channelProfile = Constants.CHANNEL_PROFILE_LIVE_BROADCASTING
            // 发布麦克风采集的音频
            options.publishMicrophoneTrack = true
            // 自动订阅所有音频流
            options.autoSubscribeAudio = true
            // 使用临时 Token 和频道名加入频道，uid 为 0 表示引擎内部随机生成用户名
            // 成功后会触发 onJoinChannelSuccess 回调
            joinChannel(token, channelId, userId.toInt(), options)
        }
    }

    private fun showLog(log: String) {
        logv(log, "rtc_logging")
    }

    fun destroy() {
        rtcEngine.run {
            // 离开频道
            leaveChannel()
            // 销毁引擎
            RtcEngine.destroy()
        }
    }
}