package com.deeptalkie.main.rtc

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.clevguard.utils.ext.logv
import com.deeptalkie.kidsguard.net.runHttp
import com.deeptalkie.main.Membership
import com.deeptalkie.main.api.deepTalkieApiFastJson
import com.deeptalkie.main.bean.SwData
import kotlinx.coroutines.launch

class Live2DRtcViewModel(application: Application) : AndroidViewModel(application) {
    private var voiceRtc: VoiceRtc? = null
    private var agentId: String? = null

    fun start() {
        viewModelScope.launch {
            runHttp {
                val resp = deepTalkieApiFastJson.getSwToken("1")
                if (!resp.isSuccess || resp.data == null) {
                    logv("获取swToken失败:${resp}")
                    return@runHttp
                }
                logv("获取swToken成功:${resp}")
                startRtc(resp.data!!)
            }
        }
    }

    private suspend fun startRtc(swData: SwData) {
        val agentId = join(swData) ?: return

        this.agentId = agentId
        logv("加入sw成功, agentId = $agentId")
        voiceRtc = VoiceRtc(getApplication(), swData.appId)
        voiceRtc?.joinChannel(swData.token, swData.name, Membership.getUserId()!!)
    }

    private suspend fun join(swData: SwData): String? {
        val resp = runHttp { deepTalkieApiFastJson.join(swData.token, swData.name) }
        if (resp?.isSuccess == false || resp?.data == null) {
            logv("加入sw失败:${resp}")
            return null
        }
        return resp.data?.agentId
    }

    fun leave() {
        voiceRtc?.destroy()
        val agentId = agentId ?: return
        viewModelScope.launch {
            runHttp {
                val resp = deepTalkieApiFastJson.leave(agentId)
                if (!resp.isSuccess) {
                    logv("离开sw失败:${resp}")
                    return@runHttp
                }
            }
        }
    }

    override fun onCleared() {
        super.onCleared()
        leave()
    }
}