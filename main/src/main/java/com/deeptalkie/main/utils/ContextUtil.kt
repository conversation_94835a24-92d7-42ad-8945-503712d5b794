package com.deeptalkie.main.utils

import android.annotation.SuppressLint
import android.content.Context
import android.os.Build
import android.os.LocaleList
import androidx.annotation.StringRes
import com.deeptalkie.main.App
import com.deeptalkie.main.bean.UserManager
import java.util.Locale

/**
 *
 * @Description: java类作用描述
 * @Author: Lee<PERSON>iuuu
 * @CreateDate: 2022/4/26 16:58
 */
@SuppressLint("StaticFieldLeak")
object ContextUtil {
    fun attachBaseContext(context: Context, locale: Locale): Context {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            updateResources(context, locale)
        } else {
            context
        }
    }

    private fun updateResources(context: Context, locale: Locale): Context {
        val res = context.resources
        val dm = res.displayMetrics
        val conf = res.configuration
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            val defaultList = LocaleList.forLanguageTags(locale.toLanguageTag())
            LocaleList.setDefault(defaultList)
            conf.setLocales(defaultList)
        } else {
            conf.locale = locale
        }
        conf.locale = locale
        conf.setLocale(locale)
        val mContext = context.createConfigurationContext(conf)
        res.updateConfiguration(conf, dm)
        return mContext
    }

    /**
     * 切换多语言时，在 viewmodel 中调用，获取 R.string.xxx 才会获取到最新的
     */
    private var nowLocal: Locale? = null
    private var mCacheContext: Context? = null
    fun getContext(): Context {
        val language = UserManager.getLanguage()
        return if (nowLocal == Locale.getDefault()) {
            mCacheContext ?: attachBaseContext(App.getInstance(), language.locale).also {
                mCacheContext = it
            }
        } else {
            attachBaseContext(App.getInstance(), language.locale).also {
                mCacheContext = it
            }
        }
    }
}

fun getString(@StringRes id: Int): String {
    return ContextUtil.getContext().getString(id)
}