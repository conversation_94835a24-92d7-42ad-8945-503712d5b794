package com.deeptalkie.main.utils

import com.deeptalkie.main.bean.UserManager
import java.security.MessageDigest
import java.security.NoSuchAlgorithmException
import java.util.Locale
import java.util.Random
import kotlin.experimental.and

object EncryptUtils {
    fun getSign(time: String): String {
        val key = UserManager.getToken() + time + "member_sign"
        val sha1 = EncryptUtils.sha1(key)
        return sha1.uppercase()
    }

    fun createNonceStr(): String? {
        val builder = StringBuilder()
        val randomStr =
            "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
        val count = randomStr.length - 1
        val random = Random()
        for (i in 0..7) {
            val id = random.nextInt(count)
            val s = randomStr.substring(id, id + 1)
            builder.append(s)
        }
        return builder.toString()
    }


    fun getEncryptedStr(timeStamp: String, randomStr: String?): String? {
        val secret = "uN3lu01bFtumul8W"
        val secretStr = timeStamp + randomStr + secret
        val sha1 = sha1(secretStr)
        val mad5Str = md5(sha1)
        return mad5Str.uppercase(Locale.getDefault())
    }


    fun sha1(decript: String): String {
        try {
            val digest =
                MessageDigest.getInstance("SHA-1")
            digest.update(decript.toByteArray())
            val messageDigest = digest.digest()
            // Create Hex String
            val hexString = StringBuffer()
            // 字节数组转换为 十六进制 数
            for (i in messageDigest.indices) {
                val shaHex =
                    Integer.toHexString(messageDigest[i].and(0xFF.toByte()).toInt())
                if (shaHex.length < 2) {
                    hexString.append(0)
                }
                hexString.append(shaHex)
            }
            return hexString.toString()
        } catch (e: NoSuchAlgorithmException) {
            e.printStackTrace()
        }
        return ""
    }

    fun md5(string: String): String {
        var md5: MessageDigest? = null
        try {
            md5 = MessageDigest.getInstance("MD5")
            val bytes = md5.digest(string.toByteArray())
            var result = ""
            for (b in bytes) {
                var temp = Integer.toHexString(b.and(0xFF.toByte()).toInt())
                if (temp.length == 1) {
                    temp = "0$temp"
                }
                result += temp
            }
            return result
        } catch (e: NoSuchAlgorithmException) {
            e.printStackTrace()
        }
        return ""
    }

    fun encryptPassWord(passWord: String, passWordKey: String) = md5(passWordKey + passWord)


}