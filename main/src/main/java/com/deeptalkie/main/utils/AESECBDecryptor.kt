package com.deeptalkie.main.utils

import android.annotation.SuppressLint
import android.util.Base64
import com.clevguard.utils.ext.loge
import javax.crypto.Cipher
import javax.crypto.spec.SecretKeySpec

/**
 * 使用 Kotlin 进行 AES-128-ECB 模式解密的工具类。
 *
 * 强烈建议：
 * - 在实际应用中，避免使用 ECB 模式，因为它不安全。考虑使用 CBC 或 GCM。
 * - 安全地管理你的 AES 密钥，例如使用 Android Keystore System。
 */
object AESECBDecryptor {
    private const val ALGORITHM = "AES" // 加密算法
    private const val MODE_AND_PADDING = "AES/ECB/PKCS5Padding" // 模式和填充方式

    fun decrypt(encryptedDataString: String, key: String): String? {
        return try {
            val keyBytes = key.toByteArray()
            val encryptedDataBytes = Base64.decode(encryptedDataString, Base64.DEFAULT)
            val decryptedBytes = decryptBytes(encryptedDataBytes, keyBytes)
            String(decryptedBytes)
        } catch (e: Exception) {
            loge("解密失败:${e.stackTraceToString()}")
            null
        }
    }

    /**
     * 使用AES-128-ECB模式解密数据。
     *
     * @param encryptedDataBytes 待解密的字节数组 (密文)。如果密文是 Base64 编码的字符串，请先解码。
     * @param keyBytes           AES密钥的字节数组。必须是16字节 (128位)。
     * @return 解密后的明文字节数组。
     * @throws IllegalArgumentException 如果密钥长度不正确。
     * @throws Exception                如果解密过程中发生其他错误。
     */
    @SuppressLint("GetInstance")
    @Throws(Exception::class)
    fun decryptBytes(encryptedDataBytes: ByteArray, keyBytes: ByteArray): ByteArray {
        if (keyBytes.size != 16) {
            throw IllegalArgumentException("AES-128 密钥必须是16字节 (128位)")
        }

        return try {
            // 1. 创建 SecretKeySpec
            val secretKey = SecretKeySpec(keyBytes, ALGORITHM)

            // 2. 获取 Cipher 实例
            val cipher = Cipher.getInstance(MODE_AND_PADDING)

            // 3. 初始化 Cipher 为解密模式
            cipher.init(Cipher.DECRYPT_MODE, secretKey)

            // 4. 执行解密
            cipher.doFinal(encryptedDataBytes)

        } catch (e: Exception) {
            // 捕获所有可能的加密异常并重新抛出，提供更具体的错误信息
            when (e) {
                is java.security.NoSuchAlgorithmException, is javax.crypto.NoSuchPaddingException ->
                    throw Exception("加密算法或填充模式不支持: ${e.message}", e)

                is java.security.InvalidKeyException ->
                    throw Exception("无效的密钥: ${e.message}", e)

                is javax.crypto.BadPaddingException ->
                    throw Exception("填充无效或密文损坏: ${e.message}", e)

                is javax.crypto.IllegalBlockSizeException ->
                    throw Exception("密文块大小不合法: ${e.message}", e)

                else ->
                    throw Exception("解密过程中发生未知错误: ${e.message}", e)
            }
        }
    }
}