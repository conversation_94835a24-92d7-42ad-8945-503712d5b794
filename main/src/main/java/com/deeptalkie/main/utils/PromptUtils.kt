package com.deeptalkie.main.utils

import com.deeptalkie.kidsguard.net.runHttp
import com.deeptalkie.main.api.deepTalkieApi
import com.deeptalkie.main.bean.TextModerationBean
import com.deeptalkie.main.config.TimberUtil

class PromptUtils {
    companion object {
        private const val TAG = "PromptUtils"

        suspend fun checkSensitive(prompt: String): TextModerationBean? {
            if (prompt.isBlank()) {
                return null
            }
            val response = runHttp {
                deepTalkieApi.checkSensitive(prompt.trim())
            }
            if (response?.code == 200) {
                TimberUtil.d(TAG, "textModerationBean = ${response.data}")
                return response.data
            } else {
                return null
            }
        }
    }
}
