package com.deeptalkie.main.utils


import com.deeptalkie.main.bean.UserManager
import java.util.Locale

object LanguageUtil {
    /**
     * 英语 en
     * 简中 zh-Hans
     * 繁中 zh-Hant
     * 日语 ja
     * 韩语 ko
     */
    fun getLanguageParam(): String {
        return UserManager.getLanguage().backendId
    }

    /**
     * 获取手机本地语言
     */
    fun getLocaleLanguage(): String = Locale.getDefault().toString()
}