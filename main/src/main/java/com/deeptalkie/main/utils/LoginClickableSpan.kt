package com.deeptalkie.main.utils

import android.content.Context
import android.text.TextPaint
import android.text.style.ClickableSpan
import androidx.core.content.ContextCompat
import com.deeptalkie.main.R

abstract class LoginClickableSpan(
    private val context: Context,
    private val textColorResId: Int = R.color.color_A98EF6
) : ClickableSpan() {
    override fun updateDrawState(ds: TextPaint) {
        super.updateDrawState(ds)
//        ds.isUnderlineText = false
        ds.color = ContextCompat.getColor(context, textColorResId)
    }
}