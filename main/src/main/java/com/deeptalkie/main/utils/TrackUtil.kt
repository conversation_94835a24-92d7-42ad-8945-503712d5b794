package com.deeptalkie.main.utils

import android.content.Context
import com.deeptalkie.main.BuildConfig
import com.deeptalkie.main.config.Constant
import com.deeptalkie.main.config.TimberUtil
import com.imyfone.track.IMyfoneTrack
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch

object TrackUtil {
    private const val TAG = "TrackUtil"
    private val scope by lazy {
        MainScope()
    }

    /**
     * 初始化渠道信息
     */
    fun initTrack(context: Context) {
        val host = BuildConfig.BaseUrl
        scope.launch(Dispatchers.IO) {
            try {
                IMyfoneTrack.create(context)
                    .enableDebug(BuildConfig.DEBUG)//设置是否启动debug模式,生产环境要设置为false;默认是false
                    .setHost(host)
                    .setProduct(Constant.INFORMATION_SOURCES)//TODO 修改成当前产品的pid 字段[pid]
                    .setTrackCallBack { _, msg ->  //设置上报IMyfone的回调,正式环境可以不用调用 [code的说明::IMyfoneTrack.ResponseCode]
                        //...
                        TimberUtil.d(TAG, "msg = $msg")
                    }
                    .setGACallBack { _, msg ->   //设置上报Google play的回调,正式环境可以不用调用  [code的说明::IMyfoneTrack.ResponseCode]
                        //...
                        TimberUtil.d(TAG, "msg = $msg")
                    }.report()//发起上报
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }


    /**
     * 获取渠道信息
     */
/*    fun getTrackInfo(context: Context) {
        if (BuildConfig.channel == Constant.GoogleChannel) {
            scope.launch(Dispatchers.IO) {
                val jsonObject = IMyfoneTrack.getChannelInfo(context)
                Log.e("jsonObject", "onCreate: $jsonObject")
                val source = jsonObject.optString("utm_source")
                if (source.isNullOrEmpty()) {
                    Constant.fromSite = "google-play"
                } else {
                    Constant.fromSite = source
                }
                Log.e("source-site", "source: $source")
                if (jsonObject.has("lang")) {
                    val lang = jsonObject.optString("lang")
                    Log.e("lang", " lang:$lang")
                    if (lang.isEmpty()) {
                        Constant.language = "EN"
                    } else {
                        Constant.language = lang
                    }
                } else {
                    Constant.language = "EN"
                }
            }
        } else {
            Constant.fromSite = BuildConfig.channel
        }
    }*/
}