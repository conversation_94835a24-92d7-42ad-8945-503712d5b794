package com.deeptalkie.main.utils

import java.math.BigDecimal
import java.math.RoundingMode

fun formatNumber(number: Long): String {
    if (number < 1_000) return number.toString()

    val unit = determineUnit(number)
    val scaledValue = BigDecimal(number).divide(unit.divisor, unit.decimalPlaces, RoundingMode.DOWN)
    return "${scaledValue.removeTrailingZeros()}${unit.suffix}"
}

private fun determineUnit(number: Long): FormatUnit = when {
    number >= 1_000_000 -> FormatUnit.M
    number >= 10_000 -> FormatUnit.W
    else -> FormatUnit.K
}

private fun BigDecimal.removeTrailingZeros(): String {
    var str = this.stripTrailingZeros().toPlainString()
    if ("." in str) str = str.replace(Regex("\\.?0+$"), "")
    return str
}

private sealed class FormatUnit(
    val divisor: BigDecimal,
    val suffix: String,
    val decimalPlaces: Int
) {
    object K : FormatUnit(BigDecimal(1_000), "K", 3)
    object W : FormatUnit(BigDecimal(10_000), "W", 2)
    object M : FormatUnit(BigDecimal(1_000_000), "M", 2)
}