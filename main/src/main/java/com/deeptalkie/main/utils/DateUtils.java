package com.deeptalkie.main.utils;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;
import java.util.TimeZone;

import kotlin.jvm.JvmStatic;

public class DateUtils {//https://blog.csdn.net/DeMonliuhui/article/details/82226601 Android 校正系统时间的三种解决方案
    private static SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    private static SimpleDateFormat sfe = new SimpleDateFormat("MMM dd,yyyy HH:mm:ss");
    public static final DateFormat YMD_FORMAT = new SimpleDateFormat("yyyy-MM-dd");
    public static final DateFormat YMD_FORMAT1 = new SimpleDateFormat("MMM dd,yyyy");
    public static final DateFormat YMD_FORMAT2 = new SimpleDateFormat("yyyy/MM/dd");

    public static int getWeek() {
        Date date = new Date();
        Calendar calendar = Calendar.getInstance(TimeZone.getDefault());
        calendar.setTime(date);
        int i = calendar.get(Calendar.DAY_OF_WEEK);
        return i-1;
    }


    public static String getMonth(String day){
        try{
            Date parse = YMD_FORMAT.parse(day);
            return String.format(Locale.US,"%tB",parse);
        }catch (Exception e){
            return day;
        }
    }

    public static String getDay(String day){
        try{
            Date parse = YMD_FORMAT.parse(day);
            return String.format(Locale.US,"%te",parse);
        }catch (Exception e){
            return day;
        }
    }

    public static String getDate() {
        Date date = new Date(System.currentTimeMillis());
        return YMD_FORMAT.format(date);
    }

    public static String getDate(Long l) {
        Date date = new Date(l);
        return YMD_FORMAT2.format(date);
    }


    public static String getDateToFormat(String data) {
        SimpleDateFormat format = new SimpleDateFormat("MMM dd,yyyy HH:mm:ss", Locale.ENGLISH);
        String datetime = "";//将你的日期转换为时间戳
        try {
            datetime = sf.format(format.parse(data));
            return datetime;
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return data;
    }
    @JvmStatic
    public static String getDateToFormat(String data,String strFormat) {
        SimpleDateFormat format = new SimpleDateFormat(strFormat, Locale.ENGLISH);
        String datetime = "";//将你的日期转换为时间戳
        try {
            Date str = sf.parse(data);
            datetime = format.format(str);
            return datetime;
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return data;
    }

    public static String getDateToFormat(Long time) {
        SimpleDateFormat format = new SimpleDateFormat("MMM dd,yyyy HH:mm:ss", Locale.ENGLISH);
        String datetime = "";//将你的日期转换为时间戳
            datetime = format.format(new Date(time));
            return datetime;
    }

    public static String getHHmmss(String date){
        try{
            String split =  date.split(",")[1];
            String time = split.split(" ")[1];

            return time;

        }catch (Exception e){

        }
      return date;
    }

    public static String getDateUs(Long time){
        SimpleDateFormat format = new SimpleDateFormat("MMM dd,yyyy", Locale.ENGLISH);
        String datetime = "";//将你的日期转换为时间戳
        datetime = format.format(new Date(time));
        return datetime;
    }

    //转换时间为国外
    public static String transData(String data){
        try{
            Date parse = sf.parse(data);
            return sfe.format(parse);
        }catch (Exception e){

        }
        return data;
    }

    //转换时间为国外
    public static String transDataUs(String data){
        try{
            Date parse = YMD_FORMAT.parse(data);
            return YMD_FORMAT1.format(parse);
        }catch (Exception e){

        }
        return data;
    }
}


