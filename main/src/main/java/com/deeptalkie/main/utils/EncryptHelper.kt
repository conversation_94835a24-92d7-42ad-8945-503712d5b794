package com.imyfone.main.dv.util

import android.util.Base64
import okhttp3.FormBody
import okhttp3.HttpUrl
import java.security.MessageDigest
import kotlin.experimental.xor

/**
 *creater:l<PERSON><PERSON><PERSON> on 2023/7/25 17:21
 */

internal fun String.toSHA1(): ByteArray {
    return toByteArray().digest("SHA-1")
}

internal fun String.toMD5(): ByteArray {
    return toByteArray().digest("MD5")
}

private fun ByteArray.digest(algorithm: String = "MD5"): ByteArray {
    val localMessageDigest = MessageDigest.getInstance(algorithm)
    localMessageDigest.update(this)
    return localMessageDigest.digest()
}

fun ByteArray.toHexString(): String {
    val sb = StringBuilder(2 * size)
    for (i in indices) {
        sb.append(String.format("%02x", (0xFF and get(i).toInt())))
    }
    return sb.toString()
}

/**
 * 加密
 */
fun String.signal(key: String = "imyfone"): String {
    val bytes = toByteArray().xor(key.toMD5().toHexString().toByteArray())
    return Base64.encodeToString(bytes, Base64.NO_WRAP)
}

private fun ByteArray.xor(byteArray: ByteArray): ByteArray {
    val ans = ByteArray(size)
    for (i in 0 until size) {
        ans[i] = this[i] xor byteArray[i % byteArray.size]
    }
    return ans
}


internal fun FormBody.toCBSSignal(): String {
    val map = HashMap<String, String>()
    for (i in 0 until size) {
        map[name(i)] = value(i)
    }
    return map
        .toSortedMap { o1, o2 ->
            when {
                o1 == o2 -> 0
                o1 < o2 -> -1
                else -> 1
            }
        }
        .map { "${it.key}=${it.value}" }
        .joinToString("&")
        .let { "$it&key=4d4458340699d7f5619f5b3b27b563ac" }
        .toMD5()
        .toHexString()
        .uppercase()
}

internal fun HttpUrl.toFileSignal(extra: Pair<String, String>?): String {
    val map = HashMap<String, String>()
    for (i in 0 until querySize) {
        map[queryParameterName(i)] = queryParameterValue(i) ?: ""
    }
    if (extra != null) {
        map[extra.first] = extra.second
    }
    return map
        .toSortedMap { o1, o2 ->
            when {
                o1 == o2 -> 0
                o1 < o2 -> -1
                else -> 1
            }
        }
        .map { "${it.key}=${it.value}" }
        .joinToString("&")
        .let { "$it&key=PDM637d875cd89a9" }
        .toMD5()
        .toHexString()
        .uppercase()
}