package com.deeptalkie.main.utils

import android.content.Context
import com.clevguard.utils.ext.logv
import com.deeptalkie.main.BuildConfig
import com.deeptalkie.main.config.Constant
import com.google.firebase.analytics.ktx.analytics
import com.google.firebase.analytics.ktx.logEvent
import com.google.firebase.ktx.Firebase
import com.umeng.analytics.MobclickAgent
import com.umeng.commonsdk.UMConfigure

object StatisticsUtil {
    /**
     * 初始化
     * @param isSurePrivacy 是否确认过隐私
     */
    fun init(context: Context, isSurePrivacy: Boolean, showLog: Boolean) {
        logv("埋点初始化：$isSurePrivacy", "stat_logging")
        UMConfigure.preInit(context, Constant.umKey, BuildConfig.channel)
        UMConfigure.setLogEnabled(showLog)
        if (isSurePrivacy) {
            UMConfigure.init(
                context,
                Constant.umKey,
                BuildConfig.channel,
                UMConfigure.DEVICE_TYPE_PHONE,
                ""
            )
        }
    }

    fun onEvent(context: Context, event: String, map: Map<String, String>? = null) {
        logv("埋点事件上报：$event map:$map", tag = "stat_logging")
        if (map == null) {
            MobclickAgent.onEvent(context, event)
        } else {
            MobclickAgent.onEventObject(context, event, map)
        }
        Firebase.analytics.logEvent(event) {
            param("Label", event)
            map?.entries?.forEach { entry ->
                param(entry.key, entry.value)
            }
        }
    }
}
