package com.deeptalkie.main.view

import android.content.Context
import android.view.WindowManager
import com.clevguard.utils.ext.dpi
import com.deeptalkie.main.config.Constant
import com.deeptalkie.main.databinding.DialogRequirePurchaseBinding
import com.deeptalkie.main.utils.StatisticsUtil

class RequirePurchaseDialog(context: Context, val type: Int, private val gotoBuy: () -> Unit) :
    CPBaseDialog<DialogRequirePurchaseBinding>(context) {
    companion object {
        const val TYPE_PICTURE = 0
        const val TYPE_INSPIRATION = 1
        const val TYPE_VOICE = 2
        const val TYPE_CHATS = 3
        const val NUMBER_OF_CHATS = 4
        const val TYPE_VIDEO = 5
        const val TYPE_CREATE_AI_ROLE = 6
    }

    override fun getViewBinding(): DialogRequirePurchaseBinding {
        return DialogRequirePurchaseBinding.inflate(layoutInflater)
    }

    override fun initView() {
        when (type) {
            TYPE_PICTURE -> {
                StatisticsUtil.onEvent(
                    context,
                    Constant.Purchase_pop_up,
                    mapOf(Constant.Purchase_pop_up to "View_pictures")
                )
            }

            TYPE_INSPIRATION -> {
                StatisticsUtil.onEvent(
                    context,
                    Constant.Purchase_pop_up,
                    mapOf(Constant.Purchase_pop_up to "Inspiration_prompt")
                )
            }

            TYPE_VOICE -> {
                StatisticsUtil.onEvent(
                    context,
                    Constant.Purchase_pop_up,
                    mapOf(Constant.Purchase_pop_up to "Voice_audition")
                )
            }

            TYPE_CHATS -> {
                StatisticsUtil.onEvent(
                    context,
                    Constant.Purchase_pop_up,
                    mapOf(Constant.Purchase_pop_up to "Number_of_chats")
                )
            }

            NUMBER_OF_CHATS -> {
                StatisticsUtil.onEvent(
                    context,
                    Constant.Purchase_pop_up,
                    mapOf(Constant.Purchase_pop_up to "number_of_chats")
                )
            }

            TYPE_VIDEO -> {
                StatisticsUtil.onEvent(
                    context,
                    Constant.Purchase_pop_up,
                    mapOf(Constant.Purchase_pop_up to "view videos")
                )
            }
        }
        StatisticsUtil.onEvent(
            context,
            Constant.Purchase_pop_up,
            mapOf(Constant.Purchase_pop_up to "Open_up")
        )
        setCanceledOnTouchOutside(false)
        setCancelable(false)
        applyWindowSetting(
            300.dpi,
            WindowManager.LayoutParams.WRAP_CONTENT
        )
        vb.ivClose.setOnClickListener {
            dismiss()
        }
        vb.tvBuy.setOnClickListener {
            StatisticsUtil.onEvent(
                context,
                Constant.Purchase_pop_up,
                mapOf(Constant.Purchase_pop_up to "Buy_now")
            )
            StatisticsUtil.onEvent(
                context,
                Constant.Purchase_page,
                mapOf(Constant.Purchase_page to "Purchase_pop-up")
            )
            gotoBuy()
            dismiss()
        }
    }
}