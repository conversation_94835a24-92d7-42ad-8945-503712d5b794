package com.deeptalkie.main.view

import android.content.ActivityNotFoundException
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.method.LinkMovementMethod
import android.text.style.ForegroundColorSpan
import android.text.style.RelativeSizeSpan
import android.view.View
import android.widget.Toast
import com.deeptalkie.main.R
import com.deeptalkie.main.bean.ConfigBean
import com.deeptalkie.main.config.TimberUtil
import com.deeptalkie.main.databinding.DialogConfigBinding
import com.deeptalkie.main.utils.LoginClickableSpan

class ConfigDialog(context: Context) : CPBaseDialog<DialogConfigBinding>(context) {
    companion object {
        private const val TAG = "ConfigDialog"
    }

    override fun getViewBinding(): DialogConfigBinding {
        return DialogConfigBinding.inflate(layoutInflater)
    }

    fun getSpanText(
        totalText: String,
        color: Int,
        scale: Float,
        url: String
    ): SpannableStringBuilder {
        val spannable = SpannableStringBuilder(totalText)
        val colorSpan = ForegroundColorSpan(color) // 改变颜色
        val sizeSpan = RelativeSizeSpan(scale)
        val clickSpan = object : LoginClickableSpan(context) {
            override fun onClick(widget: View) {
                openBrowser(url)
            }
        }
        val startIndex = totalText.indexOf(url)
        val endIndex = url.length + startIndex
        TimberUtil.d(TAG, "startIndex = $startIndex,endIndex = $endIndex")
        spannable.setSpan(colorSpan, startIndex, endIndex, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        spannable.setSpan(sizeSpan, startIndex, endIndex, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        spannable.setSpan(clickSpan, startIndex, endIndex, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)

        return spannable
    }

    fun setTextAndShow(configBean: ConfigBean) {
        vb.btnYes.text = configBean.button_text
        vb.tvTitle.movementMethod = LinkMovementMethod.getInstance()
        try {
            vb.tvTitle.text = getSpanText(
                configBean.content,
                context.resources.getColor(R.color.color_A98EF6),
                1.0f,
                configBean.content_url
            )
        } catch (e: Exception) {
            vb.tvTitle.text = configBean.content
            e.printStackTrace()
        }
        vb.tvTitle.highlightColor = context.resources.getColor(R.color.transparent)
        vb.btnYes.setOnClickListener {
            val url = configBean.content_url
            openBrowser(url)
        }
        vb.btnCancel.setOnClickListener {
            dismiss()
        }
        TimberUtil.d(TAG, "config = $configBean")

        show()
    }

    private fun openBrowser(url: String) {
        val uri = Uri.parse(url).buildUpon().build()
        val intent = Intent(Intent.ACTION_VIEW, uri).apply {
            // 确保新任务中打开（适用于非 Activity context）
            flags = Intent.FLAG_ACTIVITY_NEW_TASK
        }
        try {
            context.startActivity(intent)
        } catch (e: ActivityNotFoundException) {
            Toast.makeText(context, "need install browser!", Toast.LENGTH_SHORT).show()
        }
    }


    override fun initView() {
        setCanceledOnTouchOutside(false)
        setCancelable(false)

    }
}