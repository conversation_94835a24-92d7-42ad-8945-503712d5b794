package com.deeptalkie.main.view

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.view.Gravity
import android.view.WindowManager
import androidx.annotation.ColorRes
import androidx.annotation.DimenRes
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.core.content.ContextCompat
import androidx.viewbinding.ViewBinding
import com.deeptalkie.main.R

abstract class CPBaseDialog<VB : ViewBinding>(context: Context) :
    Dialog(context, R.style.Theme_NoTitle_Dialog) {

    protected val vb: VB by lazy { getViewBinding() }

    abstract fun getViewBinding(): VB

    abstract fun initView()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(vb.root)
        initView()
    }

    protected fun applyWindowSetting(
        width: Int = WindowManager.LayoutParams.WRAP_CONTENT,
        height: Int = WindowManager.LayoutParams.WRAP_CONTENT,
        gravity:Int = Gravity.CENTER
    ) {
        this.window?.apply {
            setGravity(gravity)
            attributes.width = width
            attributes.height = height
        }
    }

    protected open fun stringRes(@StringRes id: Int) = context.getString(id)

    protected open fun colorRes(@ColorRes id: Int) = ContextCompat.getColor(context, id)

    protected open fun drawableRes(@DrawableRes id: Int) = ContextCompat.getDrawable(context, id)

    protected open fun dimensionRes(@DimenRes id: Int) = context.resources.getDimension(id)

    protected open fun dimensionPixelOffsetRes(@DimenRes id: Int) =
        context.resources.getDimensionPixelOffset(id)
}