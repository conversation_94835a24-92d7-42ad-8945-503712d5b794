package com.deeptalkie.main.s3

import android.net.Uri
import android.webkit.MimeTypeMap
import aws.sdk.kotlin.runtime.auth.credentials.StaticCredentialsProvider
import aws.sdk.kotlin.services.s3.S3Client
import aws.sdk.kotlin.services.s3.model.PutObjectRequest
import aws.smithy.kotlin.runtime.content.ByteStream
import aws.smithy.kotlin.runtime.content.asByteStream
import com.clevguard.utils.ext.jsonAsOrNull
import com.clevguard.utils.ext.loge
import com.clevguard.utils.ext.logv
import com.deeptalkie.kidsguard.net.getSuccessData
import com.deeptalkie.kidsguard.net.runHttp
import com.deeptalkie.main.App
import com.deeptalkie.main.api.deepTalkieApi
import com.deeptalkie.main.utils.AESECBDecryptor
import com.google.gson.annotations.SerializedName
import java.io.File

object AwsS3 {
    private var awsConfig: AwsConfig? = null
    private const val AWS_S3_BASE_URL = "https://files.deeptalkie.com/"
    private const val AES_KEY = "T4aNa2d1sJ6ESwbs"

    suspend fun uploadFile(file: File): String? {
        val awsConfig = getOrInitAwsConfig() ?: return null
        return uploadFile(
            awsConfig,
            file.asByteStream(),
            "dt_${System.currentTimeMillis()}_${file.name}"
        )
    }

    suspend fun uploadFileByUri(uri: Uri): String? {
        val awsConfig = getOrInitAwsConfig() ?: return null
        val inputStream = App.getInstance().contentResolver.openInputStream(uri) ?: return null
        val name = "dt_${System.currentTimeMillis()}.${getUriExtension(uri)}"
        return uploadFile(awsConfig, inputStream.asByteStream(), name)
    }

    private suspend fun uploadFile(
        awsConfig: AwsConfig,
        byteStream: ByteStream,
        name: String
    ): String? {
        return try {
            val s3Client = S3Client {
                credentialsProvider = StaticCredentialsProvider {
                    accessKeyId = awsConfig.accessKeyId
                    secretAccessKey = awsConfig.accessKeySecret
                    sessionToken = awsConfig.securityToken
                }
                region = awsConfig.region
            }
            val putObjectRequest = PutObjectRequest {
                bucket = awsConfig.bucket
                key = name
                body = byteStream
            }
            logv("上传文件: ${putObjectRequest.key}")
            val resp = s3Client.putObject(putObjectRequest)
            val url = "${AWS_S3_BASE_URL}${putObjectRequest.key}"
            logv("上传成功: ${resp.eTag}, url=${url}")
            url
        } catch (e: Exception) {
            loge("上传失败: ${e.stackTraceToString()}")
            null
        }
    }

    fun getUriExtension(uri: Uri): String? {
        // 1. 尝试通过MIME类型获取后缀
        val mimeType = App.getInstance().contentResolver.getType(uri)

        var extension: String? = null
        mimeType?.let {
            // 使用 MimeTypeMap 将 MIME 类型映射到文件后缀
            extension = MimeTypeMap.getSingleton().getExtensionFromMimeType(it)
        }

        // 2. 如果通过MIME类型没有得到，或者MIME类型获取失败，尝试从URI路径中获取（不推荐作为首选）
        if (extension == null) {
            val path = uri.path
            if (path != null) {
                val lastDotIndex = path.lastIndexOf('.')
                if (lastDotIndex != -1) {
                    val potentialExtension = path.substring(lastDotIndex + 1)
                    // 简单检查一下，确保它看起来像一个有效的扩展名
                    if (potentialExtension.matches(Regex("[a-zA-Z0-9]+"))) {
                        extension = potentialExtension
                    }
                }
            }
        }

        return extension
    }

    private suspend fun getOrInitAwsConfig(): AwsConfig? {
        if (awsConfig == null) {
            awsConfig = getAwsConfig()
        }
        val timestamp = System.currentTimeMillis() / 1000
        val expiration = awsConfig?.expiration ?: 0
        // 如果当前时间距离过期时间还有1分钟，就重新获取
        if (expiration == 0L || (timestamp - 60) > expiration) {
            awsConfig = getAwsConfig()
        }
        return awsConfig
    }

    private suspend fun getAwsConfig(): AwsConfig? {
        val configJsonAes = runHttp {
            deepTalkieApi.getAwsConfig().getSuccessData()?.data
        } ?: return null

        val awsConfigJson = AESECBDecryptor.decrypt(configJsonAes, AES_KEY)
        return awsConfigJson.jsonAsOrNull()
    }
}

/**
 * 亚马逊配置
 * @param accessKeyId 访问密钥
 * @param accessKeySecret 秘钥
 * @param securityToken 安全令牌
 * @param expiration 令牌过期时间
 * @param region 区域
 * @param bucket 桶
 */
data class AwsConfig(
    @SerializedName("access_key_id")
    val accessKeyId: String,
    @SerializedName("access_key_secret")
    val accessKeySecret: String,
    @SerializedName("security_token")
    val securityToken: String,
    val expiration: Long,
    val region: String,
    val bucket: String,
)