plugins {
    id 'com.android.library'
    id 'org.jetbrains.kotlin.android'
    id 'com.google.devtools.ksp'
    id 'org.jetbrains.kotlin.plugin.compose'
    id 'org.jetbrains.kotlin.plugin.serialization'
    id 'kotlin-parcelize'
    id 'com.google.protobuf'
}

apply from: "$rootProject.projectDir/common.gradle"

android {
    namespace "com.deeptalkie.main"

    defaultConfig {
        ndk {
            // 设置支持的SO库架构
            abiFilters 'armeabi', 'x86', 'armeabi-v7a', 'x86_64', 'arm64-v8a'
        }

        ksp {
            arg("room.schemaLocation", "$projectDir/schemas")
        }

        javaCompileOptions {
            annotationProcessorOptions {
                arguments = ["room.schemaLocation": "$projectDir/schemas"]
            }
        }
    }

    buildFeatures {
        buildConfig true
        compose true
    }

    buildTypes {
        debug {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            buildConfigField "String", "BaseUrl", "\"https://account-api-deeptalkie.ifonelab.net/\""
            buildConfigField "String", "BaseUrlICart", "\"https://order-api-deeptalkie.ifonelab.net/\""
            buildConfigField "String", "BaseUrlICartNet", "\"https://order-api-deeptalkie.ifonelab.net/\""
            buildConfigField "String", "BaseUrlDeepTalkie", "\"https://deeptalkie-api.ifonelab.net/\""
            buildConfigField "String", "AppCheckUpdate", "\"https://apipdm.imyfone.club/\""
            buildConfigField "String", "CommonApi", "\"https://lac-api.ifonelab.net/\""
        }
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            buildConfigField "String", "BaseUrl", "\"https://account-api.deeptalkie.com/\""
            buildConfigField "String", "BaseUrlICart", "\"https://order-api.deeptalkie.com/\""
            //com站域名 iCart需要根据来源切换域名
            buildConfigField "String", "BaseUrlICartNet", "\"https://order-api.deeptalkie.com/\""
            buildConfigField "String", "BaseUrlDeepTalkie", "\"https://deeptalkie-api.irocketx.com/\""
            buildConfigField "String", "AppCheckUpdate", "\"https://apipdm.deeptalkie.com/\""
            //net站域名 iCart需要根据来源切换域名
            buildConfigField "String", "CommonApi", "\"https://lac-api.imyfone.com/\""
        }
        rc {
            initWith release
            buildConfigField "String", "BaseUrlDeepTalkie", "\"https://deeptalkie-api.ifonelab.com/\""
        }
    }

    viewBinding {
        enabled = true
    }
}

apply from: "$rootProject.projectDir/channel.gradle"

composeCompiler {
    reportsDestination = layout.buildDirectory.dir("compose_compiler")
    stabilityConfigurationFile = rootProject.layout.projectDirectory.file("stability_config.conf")
}

dependencies {
    api project(':utils')
    implementation project(':net')
    implementation project(':icart')
    implementation project(':live2d')

    implementation fileTree(include: ['*.aar', '*.jar'], dir: 'libs')
    // Import the Firebase BoM
    implementation platform(libs.firebase.bom)
    // When using the BoM, don't specify versions in Firebase dependencies
    implementation libs.firebase.analytics.ktx

    //渠道追踪依赖
    implementation libs.track
    implementation libs.okhttp3
    implementation libs.localbroadcastmanager
    implementation libs.work.runtime.ktx

    implementation libs.androidx.core.ktx
    implementation libs.app.compat
    implementation libs.constraintlayout
    implementation libs.constraintlayout.compose

    implementation libs.lottie.compose // 加载json动画框架

    //apk下载
    implementation libs.okhttp3
    implementation libs.lifecycle.viewmodel.ktx
    implementation libs.lifecycle.runtime.ktx
    implementation libs.lifecycle.extensions
    implementation libs.room.runtime
    implementation libs.room.ktx
    implementation libs.androidx.activity
    ksp libs.room.compiler
    implementation libs.datastore

    implementation libs.kotlinx.serialization.json

    // Koin Core features
    implementation libs.koin.core
    api libs.koin.android
    // Koin Test features
    testImplementation libs.koin.test

    implementation platform(libs.androidx.compose.bom)
    implementation libs.androidx.activity.compose
    implementation libs.fragment
    implementation libs.fragment.ktx
    implementation libs.fragment.compose
    implementation libs.androidx.ui
    implementation libs.androidx.ui.graphics
    implementation libs.androidx.ui.tooling.preview
    implementation libs.androidx.material3
    implementation libs.navigation.compose
    implementation libs.material3.navigation.suite
    api libs.coil.compose
    api libs.coil.network.okhttp
    api libs.coil.gif
    api libs.coil.video
    api libs.landscapist.coil3
    api libs.landscapist.transformation
    api libs.zoomable.coil3
    implementation(libs.compose.placeholder.material3)

    implementation libs.media3.exoplayer
    implementation libs.media3.ui
    implementation libs.media3.ui.compose
    implementation libs.media3.common

    testImplementation libs.junit
    androidTestImplementation libs.androidx.junit
    androidTestImplementation libs.androidx.espresso.core
    androidTestImplementation platform(libs.androidx.compose.bom)
    androidTestImplementation libs.androidx.ui.test.junit4
    debugImplementation libs.androidx.ui.tooling
    debugImplementation libs.androidx.ui.test.manifest

    implementation libs.play.services.auth
    implementation platform(libs.firebase.bom)
    api libs.firebase.analytics.ktx
    api libs.firebase.config.ktx
    api libs.firebase.crashlytics.ktx
    api libs.firebase.auth.ktx

    implementation libs.protobuf.javalite
    //会员系统的组件
    releaseApi libs.membership.release
    rcApi libs.membership.release
    debugApi libs.membership.dev
    //网络环境
    implementation libs.httpclient
    implementation(libs.membership.ext.googlelogin)

    implementation(libs.aws.s3)
    implementation(libs.aws.sts)
    implementation libs.membership.ext.googlelogin
    implementation libs.androidx.savedstate.ktx
    implementation libs.appupdate
    //paging
    implementation libs.androidx.paging.runtime
    implementation(libs.androidx.paging.compose)

    implementation(libs.sw.full.sdk)

    // 布局检查工具
    debugImplementation libs.codelocator.core
    debugImplementation libs.codelocator.lancet.all
}

protobuf {
    protoc {
        artifact = 'com.google.protobuf:protoc:3.19.1'
    }
    generateProtoTasks {
        all().configureEach { task ->
            task.builtins {
                java {
                    option "lite"
                }
            }
        }
    }
}