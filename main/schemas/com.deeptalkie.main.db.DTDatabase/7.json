{"formatVersion": 1, "database": {"version": 7, "identityHash": "c5fe9142088a54360e295fddfb2f46fe", "entities": [{"tableName": "ai_role", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER NOT NULL, `name` TEXT NOT NULL, `description` TEXT NOT NULL, `images` TEXT NOT NULL, `message_count` INTEGER NOT NULL, `classify` TEXT, `voice_id` INTEGER NOT NULL, `width` INTEGER NOT NULL DEFAULT 0, `height` INTEGER NOT NULL DEFAULT 0, `is_hot` INTEGER NOT NULL DEFAULT 0, `created_at` TEXT NOT NULL DEFAULT '', `updated_at` TEXT NOT NULL DEFAULT '', `approval_status` INTEGER, `is_show_model` INTEGER, `model_name` TEXT, `type` INTEGER, `talk_suggestion` TEXT, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "description", "columnName": "description", "affinity": "TEXT", "notNull": true}, {"fieldPath": "images", "columnName": "images", "affinity": "TEXT", "notNull": true}, {"fieldPath": "messageCount", "columnName": "message_count", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "classify", "columnName": "classify", "affinity": "TEXT"}, {"fieldPath": "voiceId", "columnName": "voice_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "width", "columnName": "width", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "height", "columnName": "height", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "isHot", "columnName": "is_hot", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "createdAt", "columnName": "created_at", "affinity": "TEXT", "notNull": true, "defaultValue": "''"}, {"fieldPath": "updatedAt", "columnName": "updated_at", "affinity": "TEXT", "notNull": true, "defaultValue": "''"}, {"fieldPath": "approvalStatus", "columnName": "approval_status", "affinity": "INTEGER"}, {"fieldPath": "isShowModel", "columnName": "is_show_model", "affinity": "INTEGER"}, {"fieldPath": "modelName", "columnName": "model_name", "affinity": "TEXT"}, {"fieldPath": "type", "columnName": "type", "affinity": "INTEGER"}, {"fieldPath": "talkSuggestion", "columnName": "talk_suggestion", "affinity": "TEXT"}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}}, {"tableName": "msg_record", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `msg_id` INTEGER, `role_id` INTEGER NOT NULL, `speaker_type` INTEGER NOT NULL, `type` INTEGER NOT NULL, `content` TEXT NOT NULL, `created_at` INTEGER NOT NULL, `user_id` TEXT NOT NULL, `is_read` INTEGER NOT NULL, `reply_id` INTEGER NOT NULL, `is_lock` INTEGER NOT NULL, `reply` TEXT, `voice_path` TEXT, `video_time` TEXT NOT NULL DEFAULT '', `prompt` TEXT NOT NULL DEFAULT '', `send_status` TEXT NOT NULL DEFAULT 'Success', `msg_send_type` INTEGER DEFAULT NULL)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "msgId", "columnName": "msg_id", "affinity": "INTEGER"}, {"fieldPath": "roleId", "columnName": "role_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "speakerType", "columnName": "speaker_type", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "type", "columnName": "type", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "content", "columnName": "content", "affinity": "TEXT", "notNull": true}, {"fieldPath": "createdAt", "columnName": "created_at", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "userId", "columnName": "user_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "isRead", "columnName": "is_read", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "replyId", "columnName": "reply_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isLock", "columnName": "is_lock", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "reply", "columnName": "reply", "affinity": "TEXT"}, {"fieldPath": "voicePath", "columnName": "voice_path", "affinity": "TEXT"}, {"fieldPath": "videoTime", "columnName": "video_time", "affinity": "TEXT", "notNull": true, "defaultValue": "''"}, {"fieldPath": "prompt", "columnName": "prompt", "affinity": "TEXT", "notNull": true, "defaultValue": "''"}, {"fieldPath": "sendStatus", "columnName": "send_status", "affinity": "TEXT", "notNull": true, "defaultValue": "'Success'"}, {"fieldPath": "msgSendType", "columnName": "msg_send_type", "affinity": "INTEGER", "defaultValue": "NULL"}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}}, {"tableName": "user_ai_role", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`user_id` TEXT NOT NULL, `role_id` INTEGER NOT NULL, `session_id` INTEGER, `last_msg` TEXT, `last_message_at` INTEGER, `session_top_up_at` INTEGER, `is_favorite` INTEGER, PRIMARY KEY(`user_id`, `role_id`))", "fields": [{"fieldPath": "userId", "columnName": "user_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "roleId", "columnName": "role_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "sessionId", "columnName": "session_id", "affinity": "INTEGER"}, {"fieldPath": "lastMsg", "columnName": "last_msg", "affinity": "TEXT"}, {"fieldPath": "lastMsgAt", "columnName": "last_message_at", "affinity": "INTEGER"}, {"fieldPath": "sessionTopUpAt", "columnName": "session_top_up_at", "affinity": "INTEGER"}, {"fieldPath": "isFavorite", "columnName": "is_favorite", "affinity": "INTEGER"}], "primaryKey": {"autoGenerate": false, "columnNames": ["user_id", "role_id"]}, "indices": [{"name": "index_user_ai_role_user_id_role_id", "unique": true, "columnNames": ["user_id", "role_id"], "orders": [], "createSql": "CREATE UNIQUE INDEX IF NOT EXISTS `index_user_ai_role_user_id_role_id` ON `${TABLE_NAME}` (`user_id`, `role_id`)"}]}, {"tableName": "voice", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER NOT NULL, `type` INTEGER NOT NULL, `voice` TEXT NOT NULL, `show_name` TEXT NOT NULL, `avatar` TEXT NOT NULL, `lang_id` INTEGER NOT NULL, `cate_id` INTEGER NOT NULL, `sex` INTEGER NOT NULL, `advanced` INTEGER NOT NULL, `recommended` INTEGER NOT NULL, `category_name` TEXT NOT NULL, `default` INTEGER NOT NULL, `age` INTEGER NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "type", "columnName": "type", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "voice", "columnName": "voice", "affinity": "TEXT", "notNull": true}, {"fieldPath": "showName", "columnName": "show_name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "avatar", "columnName": "avatar", "affinity": "TEXT", "notNull": true}, {"fieldPath": "langId", "columnName": "lang_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "cateId", "columnName": "cate_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "sex", "columnName": "sex", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "advanced", "columnName": "advanced", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "recommended", "columnName": "recommended", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "categoryName", "columnName": "category_name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "default", "columnName": "default", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "age", "columnName": "age", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}}, {"tableName": "ai_role_tag", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER NOT NULL, `name` TEXT NOT NULL, `status` INTEGER NOT NULL, `created_at` TEXT NOT NULL, `updated_at` TEXT NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "status", "columnName": "status", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "createdAt", "columnName": "created_at", "affinity": "TEXT", "notNull": true}, {"fieldPath": "updatedAt", "columnName": "updated_at", "affinity": "TEXT", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}}, {"tableName": "ai_role_tag_relation", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`role_id` INTEGER NOT NULL, `tag_id` INTEGER NOT NULL, PRIMARY KEY(`role_id`, `tag_id`))", "fields": [{"fieldPath": "roleId", "columnName": "role_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "tagId", "columnName": "tag_id", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["role_id", "tag_id"]}}], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, 'c5fe9142088a54360e295fddfb2f46fe')"]}}