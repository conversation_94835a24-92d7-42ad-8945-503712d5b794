android {
    flavorDimensions "channel"

    productFlavors {
        def channels = [
                "google" : "google play",
                "samsung": "samsung",
                "xiaomi" : "xiaomi",
                "huawei" : "huawei",
                "web"    : "web"
        ]

        // 批量添加 kol 渠道
        (1..14).each { num ->
            def flavorName = "kol${String.format('%03d', num)}"
            channels[flavorName] = flavorName
        }

        channels.each { flavorName, channelValue ->
            "$flavorName" {
                dimension "channel"
                if (buildFeatures.buildConfig) {
                    buildConfigField "String", "channel", "\"$channelValue\""
                }
            }
        }
    }
}
