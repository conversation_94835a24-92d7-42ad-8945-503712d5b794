plugins {
    id 'com.android.library'
    id 'kotlin-android'
    id 'com.google.devtools.ksp'
}

apply from: "$rootProject.projectDir/common.gradle"
apply from: "$rootProject.projectDir/channel.gradle"
apply from: "$rootProject.projectDir/build_type.gradle"

android {
    namespace "com.deeptalkie.kidsguard.net"

    buildFeatures {
        buildConfig true
    }
}

dependencies {
    implementation project(':utils')
    implementation libs.androidx.core.ktx
    api libs.retrofit
    api libs.okhttp3
    api libs.okhttp3.sse
    api libs.okhttp3.logging
    api libs.moshi
    implementation libs.retrofit.converter.moshi
    ksp libs.moshi.kotlin.codegen
    api libs.fastjson

    testImplementation libs.junit
    androidTestImplementation libs.androidx.junit
    androidTestImplementation libs.androidx.espresso.core
}