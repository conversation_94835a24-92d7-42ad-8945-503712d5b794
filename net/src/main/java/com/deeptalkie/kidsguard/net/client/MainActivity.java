/*
package com.imyfone.kidsguard.net.client;

import android.os.Bundle;
import android.os.Message;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;

import org.apache.commons.lang3.RandomStringUtils;

import java.io.UnsupportedEncodingException;
import java.math.BigInteger;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Calendar;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;


public class MainActivity extends AppCompatActivity {

    // Used to load the 'native-lib' library on application startup.
    static {
        System.loadLibrary("native-lib");
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        Button button = (Button)findViewById(R.id.button);
        button.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                TextView tv = findViewById(R.id.sample_text);
                String randomAlphanumeric = RandomStringUtils.randomAlphanumeric(5);

                tv.setText(stringFromJNI(randomAlphanumeric));
                check("inputString",55,66);
                alg();
                String arg = "nonce_str=05171317cc6b458caa315637753bc025&timestamp=1657093821&url=https://www.tiktok.com/@twice_tiktok_official/video/7113919201215089921?is_from_webapp=1&sender_device=mobile&sender_web_id=7113730375067911682&key=yxnBJ8HDpeLjDjIinpfrpuzs5Iig4naOSOxKXLsyN5E";
//                TTDownload(arg);
                Log.i("cmcc",TTDownload(arg));

                String valueOf = String.valueOf((Calendar.getInstance().getTime().getTime() /1000 /60));
                Log.i("cmcc","Calendar:"+ valueOf);

                String ret = getTT("https://www.tiktok.com/@lovesingapore315/video/7112043728759573762?is_from_webapp=1&sender_device=mobile&sender_web_id=7113730375067911682","27626961");
                Log.i("cmcc",ret);

                Log.i("cmcc",getMd5("zc8LV7XM6"));

            }
        });

    }

    public static StringBuilder u(String arg1) {
        StringBuilder v0 = new StringBuilder();
        v0.append(arg1);
        return v0;
    }

    public String getTT(String str,String str2)
    {
        char[] v6 = (str2 + "1.5" + str + "ssstik.io" + "zc8LV7XM6").toCharArray();

        String v1 = "";
        for (int v2 = 0;v2 < v6.length;++v2)
        {
            int v3 = v6[v2];
            StringBuilder v1_1 = u(v1);
            v1_1.append(String.format("%03d",(int)v3));
            v1 = v1_1.toString();
        }
        Log.i("cmcc","v1:"+v1);
        return getMd5(v1.length()+v1);

    }

    public static final String getMd5(String arg5) {
        try {
            MessageDigest v0 = MessageDigest.getInstance("MD5");
            v0.update(arg5.getBytes());
            byte[] v5_1 = v0.digest();
            StringBuffer v0_1 = new StringBuffer();
            int v1;
            for(v1 = 0; v1 < v5_1.length; ++v1) {
                String v2;
                v2 = Integer.toHexString(v5_1[v1] & 0xFF);
                while (v2.length() < 2)
                {
                    v2 = "0" + v2;
                }

                v0_1.append(v2);
            }

            return v0_1.toString();
        }
        catch(NoSuchAlgorithmException v5) {
            System.out.println(v5.getLocalizedMessage());
            return "";
        }
    }


    public String S(String arg3,int arg4,char arg5)
    {
        try {
            if(arg4 >= 0) {
                if(arg4 <= arg3.length()) {
                    return arg3.subSequence(0, arg3.length()).toString();
                }

                StringBuilder v0 = new StringBuilder(arg4);
                int v4 = arg4 - arg3.length();
                int v1 = 1;
                if(1 <= v4) {
                    while(true) {
                        int v2 = v1 + 1;
                        v0.append(arg5);
                        if(v1 == v4) {
                            break;
                        }

                        v1 = v2;
                    }
                }

                v0.append(arg3);
                return v0.toString();
            }
        }catch (Exception e)
        {

        }
        return "";
    }


    public void alg() {
        try {
            MessageDigest instance = MessageDigest.getInstance("MD5");
            byte[] bytes = ("https://www.tiktok.com/@hafiythedude/video/7113497446243749121?is_from_webapp=1&sender_device=mobile&sender_web_id=7113730375067911682" + "1656900490383"+"iHOxRwONkrD3oL2hArKIyg").getBytes("UTF-8");
            String bigInteger = new BigInteger(1,instance.digest(bytes)).toString(16);
            String result = S(bigInteger,0x20,'0');
            Log.i("cmcc",result);
        }catch (NoSuchAlgorithmException | UnsupportedEncodingException e)
        {

        }
    }

    public String TTDownload(String str)
    {
        try {
            Mac instance = Mac.getInstance("HmacSHA256");
            instance.init(new SecretKeySpec("yxnBJ8HDpeLjDjIinpfrpuzs5Iig4naOSOxKXLsyN5E".getBytes("UTF-8"), "HmacSHA256"));
            byte[] doFinal = instance.doFinal(str.getBytes("UTF-8"));
            StringBuilder sb = new StringBuilder();
            for (byte b3 : doFinal) {
                sb.append(Integer.toHexString((b3 & 255) | 256).substring(1, 3));
            }
            return sb.toString().toUpperCase();

        }catch (Exception e)
        {

        }

        return "";
    }

    public void check(String str,int n,int m)
    {
        if(nativeCheck(str,n,m))
        {
            Log.i("theSoCall:","true");
        }
        else
        {
            Log.i("theSoCall:","false");
        }
    }

    */
/**
     * A native method that is implemented by the 'native-lib' native library,
     * which is packaged with this application.
     *//*

    public native String stringFromJNI(String s);

    public native boolean nativeCheck(String str,int n,int m);
}*/
