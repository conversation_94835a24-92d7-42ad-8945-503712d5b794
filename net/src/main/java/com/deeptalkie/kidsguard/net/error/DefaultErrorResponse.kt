package com.deeptalkie.kidsguard.net.error

import android.content.Context
import com.deeptalkie.kidsguard.net.R
import okhttp3.Request
import okhttp3.Response

/**
 * 默认错误处理构建
 * 可根据项目情况实现ErrorResponseInterface即可
 */
class DefaultErrorResponse(private val context: Context) : BaseResponseBuilder(),
    ErrorResponseInterface {

    val defaultErrorMap: Map<Int, String>
        get() = mapOf(
            Pair(NetError.NO_CACHE, context.getString(R.string.NO_CACHE)),
            Pair(NetError.UNAUTHORIZED, context.getString(R.string.UNAUTHORIZED)),
            Pair(NetError.FORBIDDEN, context.getString(R.string.FORBIDDEN)),
            Pair(NetError.NOT_FOUND, context.getString(R.string.NOT_FOUND)),
            Pair(NetError.REQUEST_TIMEOUT, context.getString(R.string.REQUEST_TIMEOUT)),
            Pair(NetError.INTERNAL_SERVER_ERROR, context.getString(R.string.INTERNAL_SERVER_ERROR)),
            Pair(NetError.SERVICE_UNAVAILABLE, context.getString(R.string.SERVICE_UNAVAILABLE)),
            Pair(NetError.PARSE_ERROR, context.getString(R.string.PARSE_ERROR)),
            Pair(NetError.NETWORK_ERROR, context.getString(R.string.NETWORK_ERROR)),
            Pair(NetError.TIMEOUT_ERROR, context.getString(R.string.TIMEOUT_ERROR)),
            Pair(NetError.NULL_DATA, context.getString(R.string.NULL_DATA)),
            Pair(NetError.NOT_NETWORK, context.getString(R.string.NETWORK_ERROR)),
            Pair(NetError.UNKNOWN, context.getString(R.string.UNKNOWN))
        )

    override fun buildErrorResp(request: Request, errorCode: Int): Response {
        var msg = defaultErrorMap.get(errorCode)
        return buildResponse(
            request,
            buildErrorJson(errorCode, msg ?: context.getString(R.string.UNKNOWN))
        )
    }

    override fun reBuildErrorResp(response: Response, errorCode: Int): Response {
        var msg = defaultErrorMap.get(errorCode)
        return reBuildResponse(
            response,
            buildErrorJson(errorCode, msg ?: context.getString(R.string.UNKNOWN))
        )
    }

    //可根据后台接口结构修改错误返回体
    override fun buildErrorJson(code: Int, msg: String): String {
        return "{\"code\":${code},\"data\":null,\"msg\":\"${msg}\"}"
    }
}