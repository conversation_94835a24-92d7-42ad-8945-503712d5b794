package com.deeptalkie.kidsguard.net.ws

import com.clevguard.utils.BuildConfig
import com.clevguard.utils.ext.logv
import com.google.gson.Gson
import com.google.gson.annotations.SerializedName
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Response
import okhttp3.WebSocket
import okhttp3.WebSocketListener
import okio.ByteString
import java.util.concurrent.TimeUnit

object WSManager {
    private val scope = CoroutineScope(Dispatchers.IO)
    private const val DEBUG_DOMAIN = "deeptalkie-api.ifonelab.net"
    private const val RELEASE_DOMAIN = "deeptalkie-api.irocketx.com"

    private val okHttpClient by lazy {
        OkHttpClient.Builder()
            .pingInterval(10, TimeUnit.SECONDS) // 设置 PING 帧发送间隔
            .build()
    }

    private var ws: WebSocket? = null
    private val _websocketMessageFlow = MutableSharedFlow<WSResp>()
    val websocketMessageFlow = _websocketMessageFlow.asSharedFlow()

    private var lastToken: String? = null
    private var lastLanguage = "en"

    fun open(token: String, language: String) {
        if (lastToken == token) {
            return
        }

        close()
        lastToken = token
        lastLanguage = language

        val domain = if (BuildConfig.DEBUG) DEBUG_DOMAIN else RELEASE_DOMAIN

        val request = Request.Builder()
            .url("wss://${domain}/ws?token=${token}&lang=${language}")
            .build()
        ws = okHttpClient.newWebSocket(request, object : WebSocketListener() {
            override fun onOpen(webSocket: WebSocket, response: Response) {
                super.onOpen(webSocket, response)
                showLog("websocket 已连接: ${request.url}")
            }

            override fun onMessage(webSocket: WebSocket, text: String) {
                super.onMessage(webSocket, text)
                dispatchMessage(text)
            }

            override fun onMessage(webSocket: WebSocket, bytes: ByteString) {
                super.onMessage(webSocket, bytes)
                dispatchMessage(bytes.utf8())
            }

            private fun dispatchMessage(msg: String) {
                showLog("websocket 收到消息：$msg")
                scope.launch {
                    tryParseMsg(msg)?.let { webSocketMsg ->
                        _websocketMessageFlow.emit(webSocketMsg.parse2WSResp())
                    }
                }
            }

            private fun tryParseMsg(msg: String): WebsocketMessage? {
                return try {
                    Gson().fromJson(msg, WebsocketMessage::class.java)
                } catch (e: Exception) {
                    showLog(e.stackTraceToString())
                    null
                }
            }

            override fun onFailure(webSocket: WebSocket, t: Throwable, response: Response?) {
                super.onFailure(webSocket, t, response)
                showLog("websocket onFailure：${t.stackTraceToString()}")
                reconnect()
            }

            override fun onClosing(webSocket: WebSocket, code: Int, reason: String) {
                super.onClosing(webSocket, code, reason)
                showLog("websocket 关闭中")
            }

            override fun onClosed(webSocket: WebSocket, code: Int, reason: String) {
                super.onClosed(webSocket, code, reason)
                showLog("websocket 已关闭")
            }
        })
    }

    private fun showLog(log: String) {
        logv(log, "websocket_logging", false, false)
    }

    private fun reconnect() {
        scope.launch {
            val token = lastToken ?: return@launch
            close()
            delay(2000)
            open(token, lastLanguage)
        }
    }

    fun close() {
        ws?.close(1000, null)
        ws = null
        lastToken = null
    }
}

sealed interface WSResp

data class WsError(val code: Long, val msg: String? = null) : WSResp

sealed interface WsData : WSResp {
    val msgId: Long
    val roleId: Long
    val time: Long
    val replyId: Long
    val reply: String?

    data class Text(
        override val msgId: Long,
        override val roleId: Long,
        val text: String,
        override val time: Long,
        override val replyId: Long,
        override val reply: String?
    ) : WsData

    sealed interface LockedWsData : WsData {
        val isLock: Int
    }

    data class Image(
        override val msgId: Long,
        override val roleId: Long,
        val url: String,
        val prompt: String?,
        override val time: Long,
        override val isLock: Int,
        override val replyId: Long,
        override val reply: String?
    ) : LockedWsData

    data class Video(
        override val msgId: Long,
        override val roleId: Long,
        val url: String,
        override val time: Long,
        override val isLock: Int,
        override val replyId: Long,
        override val reply: String?
    ) : LockedWsData
}

const val UNKNOWN_WS_ERROR = -1L

const val GEN_IMAGE_ERROR_COINS_NOT_ENOUGH = 3L

private data class WebsocketMessage(
    @SerializedName("type")
    val type: String,
    @SerializedName("data")
    val data: WebsocketMessageData?
) {
    fun parse2WSResp(): WSResp {
        return when (type) {
            "message" -> {
                data?.toWsData() ?: WsError(UNKNOWN_WS_ERROR)
            }

            "error" -> {
                WsError(data?.code ?: UNKNOWN_WS_ERROR)
            }

            else -> {
                WsError(UNKNOWN_WS_ERROR)
            }
        }
    }
}

private data class WebsocketMessageData(
    @SerializedName("code")
    val code: Long?,
    @SerializedName("id")
    val messageId: Long?,
    @SerializedName("session_id")
    val sessionId: Long?,
    @SerializedName("role_id")
    val roleId: Long?,
    @SerializedName("type")
    val type: Int,
    @SerializedName("content")
    val content: String,
    @SerializedName("time")
    val time: Long,
    @SerializedName("is_lock")
    val isLock: Int,
    @SerializedName("reply_id")
    val replyId: Long = 0,
    @SerializedName("reply")
    val reply: String? = null,
    @SerializedName("prompt")
    val prompt: String? = null,
) {
    fun toWsData(): WsData? {
        messageId ?: return null
        roleId ?: return null
        return when (type) {
            1 -> WsData.Text(messageId, roleId, content, time, replyId, reply)
            2 -> WsData.Image(messageId, roleId, content, prompt, time, isLock, replyId, reply)
            3 -> WsData.Video(messageId, roleId, content, time, isLock, replyId, reply)
            else -> null
        }
    }
}