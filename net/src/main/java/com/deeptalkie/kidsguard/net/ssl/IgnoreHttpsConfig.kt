package com.deeptalkie.kidsguard.net.ssl

import com.deeptalkie.kidsguard.net.ssl.HttpsConfigInterface
import java.security.cert.X509Certificate
import javax.net.ssl.*

class IgnoreHttpsConfig : HttpsConfigInterface {

    override fun providerTrustManager() = object : X509TrustManager {
        override fun checkClientTrusted(chain: Array<out X509Certificate>?, authType: String?) {
        }

        //检查服务器证书
        override fun checkServerTrusted(chain: Array<out X509Certificate>?, authType: String?) {

        }

        override fun getAcceptedIssuers(): Array<X509Certificate?> {
            return arrayOfNulls(0)
        }
    }

    override fun providerHostNameVerifier() = HostnameVerifier { hostname, session ->
        return@HostnameVerifier true
    }

    override fun providerSSLSocketFactory(): SSLSocketFactory {
        val sslContext = SSLContext.getInstance("TLS")
        sslContext.init(null, null, null)
        return sslContext.socketFactory
    }
}