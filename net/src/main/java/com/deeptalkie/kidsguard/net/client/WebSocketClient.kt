package com.deeptalkie.kidsguard.net.client

import android.os.Bundle
import android.view.View
import okhttp3.*
import java.util.concurrent.TimeUnit

/**
 *
 */
open class WebSocketClient {
    protected val mClient by lazy {
        OkHttpClient.Builder()
            .readTimeout(5, TimeUnit.SECONDS) //设置读取超时时间
            .writeTimeout(5, TimeUnit.SECONDS) //设置写的超时时间
            .connectTimeout(15, TimeUnit.SECONDS) //设置连接超时时间
            .pingInterval(5, TimeUnit.SECONDS)
            .build()
    }

    fun newConnect(url:String,listener: WebSocketListener): WebSocket {
        val request = Request.Builder().get().url(url).build()
        return mClient.newWebSocket(request,listener)
    }

}
