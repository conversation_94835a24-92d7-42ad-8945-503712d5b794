package com.deeptalkie.kidsguard.net.error;

public interface NetError {
    int SUCCESS = 200;
    /**
     * 服务器响应码304，但是客户端没有缓存
     */
    int NO_CACHE = 304;

    /**
     * 操作未授权
     */
    int UNAUTHORIZED = 401;

    /**
     * 拒绝请求
     */
    int FORBIDDEN = 403;

    /**
     * 资源不存在
     */
    int NOT_FOUND = 404;

    /**
     * 服务器执行超时
     */
    int REQUEST_TIMEOUT = 408;

    /**
     * 服务器内部错误
     */
    int INTERNAL_SERVER_ERROR = 500;

    /**
     * 服务器不可用
     */
    int SERVICE_UNAVAILABLE = 503;
    /**
     * 未知错误
     */
    int UNKNOWN = 10;

    /**
     * 解析错误
     */
    int PARSE_ERROR = 11;

    /**
     * SSL_ERROR
     * 网络错误
     */
    int NETWORK_ERROR = 12;
    /**
     * 协议出错
     */
    int HTTP_ERROR = 13;
    /**
     * 证书出错
     */
    int SSL_ERROR = 14;
    /**
     * 连接超时
     */
    int TIMEOUT_ERROR = 15;

    /**
     * 没有数据
     */
    int NULL_DATA = 16;

    /**
     * 当前没有开启网络
     */
    int NOT_NETWORK = 17;

}
