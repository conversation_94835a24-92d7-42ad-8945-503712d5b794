package com.deeptalkie.kidsguard.net.error

import okhttp3.MediaType.Companion.toMediaType
import okhttp3.Protocol
import okhttp3.Request
import okhttp3.Response
import okhttp3.ResponseBody

/**
 * 把服务器等外部错误状态重新构建，当成成功响应，和请求逻辑一起返回这样可以统一在请求体测错误中处理
 */
open class BaseResponseBuilder {
    private fun buildResponse(request: Request,httpCode: Int,httpMsg: String,bodyJsonString:String): Response {
        return Response.Builder()
            .request(request)
            .protocol(Protocol.HTTP_1_1)
            .message(httpMsg)
            .code(httpCode)//这个是网络请求code,(非请求体中客户端返回的)
            .body(ResponseBody.create("application/json".toMediaType(), bodyJsonString))
            .build()
    }

    fun reBuildResponse(response: Response, httpCode: Int,httpMsg: String,bodyJsonString:String): Response {
        return response
            .newBuilder()
            .message(httpMsg)
            .code(httpCode)//这个是网络请求code，非请求体中客户端返回的（）
            .body(ResponseBody.create("application/json".toMediaType(), bodyJsonString))
            .build()
    }

    fun reBuildResponse(response: Response, bodyJsonString:String): Response {
        return reBuildResponse(response, 203, "",bodyJsonString)
    }


    fun buildResponse(request: Request, bodyJsonString:String): Response {
        return buildResponse(request, 203, "",bodyJsonString)
    }

}