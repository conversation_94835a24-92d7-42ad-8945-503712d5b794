package com.deeptalkie.kidsguard.net.client

import com.deeptalkie.kidsguard.net.ssl.HttpsConfigInterface
import com.deeptalkie.kidsguard.net.ssl.IgnoreHttpsConfig
import okhttp3.Interceptor
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.CallAdapter
import retrofit2.Converter
import retrofit2.Retrofit
import retrofit2.converter.moshi.MoshiConverterFactory
import timber.log.Timber

class RetrofitClient(builder: Builder) : BaseRetrofitClient() {
    private val mCacheServiceMap: HashMap<Class<*>, Any> = HashMap()
    private var mOkHttpClient: OkHttpClient? = null
    private var mRetrofitClient: Retrofit? = null

    var baseUrl = builder.baseUrl
    var httpsConfig: HttpsConfigInterface = builder.httpsConfig
    val interceptors: List<Interceptor> = builder.interceptors
    var timeout = builder.timeout
    var callAdapter: CallAdapter.Factory? = builder.callAdapter
    var converter: Converter.Factory = builder.converter

    fun newBuilder(): Builder = Builder(this)

    private fun initClient() {
        mOkHttpClient = getOkhttpClient(httpsConfig, interceptors, timeout)
        mRetrofitClient = getRetrofit(baseUrl, mOkHttpClient!!, converter, callAdapter)
    }


    fun <S> create(serviceClass: Class<S>): S {
        if (mCacheServiceMap.containsKey(serviceClass)) {
            return mCacheServiceMap.get(serviceClass) as S
        } else {
            if (mRetrofitClient == null) {
                initClient()
            }
            val instance = mRetrofitClient?.create(serviceClass)
            if (instance != null) {
                mCacheServiceMap.put(serviceClass, instance)
            }
            return instance as S
        }
    }

    class Builder constructor() {
        internal var baseUrl = ""
        internal var httpsConfig: HttpsConfigInterface = IgnoreHttpsConfig()
        internal val interceptors: MutableList<Interceptor> = mutableListOf()
        internal var timeout = 60_000L
        internal var callAdapter: CallAdapter.Factory? = null
        internal var converter: Converter.Factory = MoshiConverterFactory.create()

        internal constructor(client: RetrofitClient) : this() {
            this.baseUrl = client.baseUrl
            this.httpsConfig = client.httpsConfig
            this.callAdapter = client.callAdapter
            this.interceptors.addAll(client.interceptors)
            this.converter = client.converter
            this.timeout = client.timeout
        }

        fun baseUrl(baseUrl: String) = apply {
            this.baseUrl = baseUrl
        }

        fun httpsConfig(config: HttpsConfigInterface) = apply {
            this.httpsConfig = config
        }

        fun addInterceptor(interceptor: Interceptor) = apply {
            this.interceptors.add(interceptor)
        }

        fun addLoggingInterceptor(isDebug: Boolean): Builder {
            if (isDebug) {
                val logging = HttpLoggingInterceptor(object : HttpLoggingInterceptor.Logger {
                    override fun log(message: String) {
                        Timber.tag("http_logging").v(message)
                    }
                })
                logging.level = HttpLoggingInterceptor.Level.BODY
                interceptors.add(logging)
            }
            return this
        }

        fun timeout(timeout: Long) = apply {
            this.timeout = timeout
        }

        fun callAdapter(callAdapter: CallAdapter.Factory) = apply {
            this.callAdapter = callAdapter
        }

        fun converterFactory(converter: Converter.Factory) = apply {
            this.converter = converter
        }

        fun build(): RetrofitClient = RetrofitClient(this)
    }
}