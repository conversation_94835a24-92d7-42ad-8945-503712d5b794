package com.deeptalkie.kidsguard.net.client

import com.deeptalkie.kidsguard.net.StringConverterFactory
import com.deeptalkie.kidsguard.net.ssl.HttpsConfigInterface
import okhttp3.Interceptor
import okhttp3.OkHttpClient
import retrofit2.CallAdapter
import retrofit2.Converter
import retrofit2.Retrofit
import java.util.concurrent.TimeUnit

open class BaseRetrofitClient {

    fun getOkhttpClient(httpsConfig: HttpsConfigInterface, interceptors: List<Interceptor>, timeout:Long): OkHttpClient {
        val clientBuilder = OkHttpClient
            .Builder()
            .sslSocketFactory(
                httpsConfig.providerSSLSocketFactory(),
                httpsConfig.providerTrustManager()
            )
            .hostnameVerifier(httpsConfig.providerHostNameVerifier())
            .connectTimeout(timeout, TimeUnit.MILLISECONDS)
            .callTimeout(timeout, TimeUnit.MILLISECONDS)
            .readTimeout(timeout, TimeUnit.MILLISECONDS)
            .writeTimeout(timeout, TimeUnit.MILLISECONDS)
        clientBuilder.interceptors().addAll(interceptors)
        return clientBuilder.build()
    }


    fun getRetrofit(baseUrl:String, okHttpClient: OkHttpClient, converter: Converter.Factory, callAdapter: CallAdapter.Factory?): Retrofit {
        return Retrofit.Builder()
            .baseUrl(baseUrl)
            .client(okHttpClient).apply {
                if (callAdapter != null) {
                    addCallAdapterFactory(callAdapter)
                }
            }.addConverterFactory(converter).addConverterFactory(StringConverterFactory())
            .build()
    }

}