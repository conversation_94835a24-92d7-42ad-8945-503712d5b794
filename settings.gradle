/*pluginManagement {
    repositories {
        gradlePluginPortal()
        google()
        mavenCentral()
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        google()
        mavenCentral()
    }
}*/
rootProject.name = "DeepTalkie"
include ':app'
include ':net'
include ':utils'
include ':main'
include ':icart'
include ':live2d'
