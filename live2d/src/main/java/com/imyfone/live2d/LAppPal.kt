package com.imyfone.live2d

import com.clevguard.utils.ext.logv
import com.imyfone.live2d.LAppDelegate.Companion.instance
import java.io.IOException
import java.io.InputStream

/**
 * 平台抽象层工具类
 */
object LAppPal {
    private var s_currentFrame = 0.0
    private var _lastNanoTime = 0.0
    private var _deltaNanoTime = 0.0

    /** 更新时间差 */
    fun updateTime() {
        s_currentFrame = systemNanoTime.toDouble()
        _deltaNanoTime = s_currentFrame - _lastNanoTime
        _lastNanoTime = s_currentFrame
    }

    /**
     * 从assets加载文件为字节数组
     * @param path 文件路径
     * @return 文件字节数组
     */
    @JvmStatic
    fun loadFileAsBytes(path: String): ByteArray {
        var fileData: InputStream? = null
        try {
            fileData = instance.context.assets.open(path)
            val fileSize = fileData.available()
            val fileBuffer = ByteArray(fileSize)
            fileData.read(fileBuffer, 0, fileSize)
            return fileBuffer
        } catch (e: IOException) {
            e.printStackTrace()
            if (LAppDefine.DEBUG_LOG_ENABLE) {
                printLog("File open error.")
            }
            return ByteArray(0)
        } finally {
            try {
                fileData?.close()
            } catch (e: IOException) {
                e.printStackTrace()
                if (LAppDefine.DEBUG_LOG_ENABLE) {
                    printLog("File open error.")
                }
            }
        }
    }

    /** 获取时间差（秒） */
    val deltaTime: Float
        get() = (_deltaNanoTime / 1000000000.0f).toFloat()

    /**
     * 日志输出函数
     * @param message 日志消息
     */
    @JvmStatic
    fun printLog(message: String) {
        logv(message, "Live2D_logging")
    }

    private val systemNanoTime: Long
        get() = System.nanoTime()
}
