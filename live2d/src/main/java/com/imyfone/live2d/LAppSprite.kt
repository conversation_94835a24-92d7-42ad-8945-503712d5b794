package com.imyfone.live2d

import android.opengl.GLES20
import java.nio.ByteBuffer
import java.nio.ByteOrder
import java.nio.FloatBuffer

/**
 * 精灵渲染类，用于渲染2D纹理
 */
class LAppSprite(
    x: Float,
    y: Float,
    width: Float,
    height: Float,
    textureId: Int,
    programId: Int
) {
    private val rect = Rect()
    private val textureId: Int
    private val positionLocation: Int // 位置属性
    private val uvLocation: Int // UV属性
    private val textureLocation: Int // 纹理属性
    private val colorLocation: Int // 颜色属性
    private val spriteColor = FloatArray(4) // 显示颜色
    private var maxWidth = 0 // 窗口宽度
    private var maxHeight = 0 // 窗口高度
    private val uvVertex = FloatArray(8)
    private val positionVertex = FloatArray(8)
    private var posVertexFloatBuffer: FloatBuffer? = null
    private var uvVertexFloatBuffer: FloatBuffer? = null

    init {
        rect.left = x - width * 0.5f
        rect.right = x + width * 0.5f
        rect.up = y + height * 0.5f
        rect.down = y - height * 0.5f
        this.textureId = textureId
        // 获取着色器属性位置
        positionLocation = GLES20.glGetAttribLocation(programId, "position")
        uvLocation = GLES20.glGetAttribLocation(programId, "uv")
        textureLocation = GLES20.glGetUniformLocation(programId, "texture")
        colorLocation = GLES20.glGetUniformLocation(programId, "baseColor")
        spriteColor[0] = 1.0f
        spriteColor[1] = 1.0f
        spriteColor[2] = 1.0f
        spriteColor[3] = 1.0f
    }

    /** 渲染精灵 */
    fun render() {
        // 设置UV坐标
        uvVertex[0] = 1.0f; uvVertex[1] = 0.0f
        uvVertex[2] = 0.0f; uvVertex[3] = 0.0f
        uvVertex[4] = 0.0f; uvVertex[5] = 1.0f
        uvVertex[6] = 1.0f; uvVertex[7] = 1.0f
        // 透明度设置
        GLES20.glBlendFunc(GLES20.GL_SRC_ALPHA, GLES20.GL_ONE_MINUS_SRC_ALPHA)
        GLES20.glEnableVertexAttribArray(positionLocation)
        GLES20.glEnableVertexAttribArray(uvLocation)
        GLES20.glUniform1i(textureLocation, 0)
        // 顶点数据
        positionVertex[0] = (rect.right - maxWidth * 0.5f) / (maxWidth * 0.5f)
        positionVertex[1] = (rect.up - maxHeight * 0.5f) / (maxHeight * 0.5f)
        positionVertex[2] = (rect.left - maxWidth * 0.5f) / (maxWidth * 0.5f)
        positionVertex[3] = (rect.up - maxHeight * 0.5f) / (maxHeight * 0.5f)
        positionVertex[4] = (rect.left - maxWidth * 0.5f) / (maxWidth * 0.5f)
        positionVertex[5] = (rect.down - maxHeight * 0.5f) / (maxHeight * 0.5f)
        positionVertex[6] = (rect.right - maxWidth * 0.5f) / (maxWidth * 0.5f)
        positionVertex[7] = (rect.down - maxHeight * 0.5f) / (maxHeight * 0.5f)
        if (posVertexFloatBuffer == null) {
            val posVertexByteBuffer = ByteBuffer.allocateDirect(positionVertex.size * 4)
            posVertexByteBuffer.order(ByteOrder.nativeOrder())
            posVertexFloatBuffer = posVertexByteBuffer.asFloatBuffer()
        }
        if (uvVertexFloatBuffer == null) {
            val uvVertexByteBuffer = ByteBuffer.allocateDirect(uvVertex.size * 4)
            uvVertexByteBuffer.order(ByteOrder.nativeOrder())
            uvVertexFloatBuffer = uvVertexByteBuffer.asFloatBuffer()
        }
        posVertexFloatBuffer!!.put(positionVertex).position(0)
        uvVertexFloatBuffer!!.put(uvVertex).position(0)
        GLES20.glVertexAttribPointer(
            positionLocation,
            2,
            GLES20.GL_FLOAT,
            false,
            0,
            posVertexFloatBuffer
        )
        GLES20.glVertexAttribPointer(uvLocation, 2, GLES20.GL_FLOAT, false, 0, uvVertexFloatBuffer)
        GLES20.glUniform4f(
            colorLocation,
            spriteColor[0],
            spriteColor[1],
            spriteColor[2],
            spriteColor[3]
        )
        GLES20.glBindTexture(GLES20.GL_TEXTURE_2D, textureId)
        GLES20.glDrawArrays(GLES20.GL_TRIANGLE_FAN, 0, 4)
    }

    /**
     * 指定纹理ID立即渲染
     * @param textureId 纹理ID
     * @param uvVertex UV顶点坐标
     */
    fun renderImmediate(textureId: Int, uvVertex: FloatArray) {
        GLES20.glEnableVertexAttribArray(positionLocation)
        GLES20.glEnableVertexAttribArray(uvLocation)
        GLES20.glUniform1i(textureLocation, 0)
        // 顶点数据
        val positionVertex = floatArrayOf(
            (rect.right - maxWidth * 0.5f) / (maxWidth * 0.5f),
            (rect.up - maxHeight * 0.5f) / (maxHeight * 0.5f),
            (rect.left - maxWidth * 0.5f) / (maxWidth * 0.5f),
            (rect.up - maxHeight * 0.5f) / (maxHeight * 0.5f),
            (rect.left - maxWidth * 0.5f) / (maxWidth * 0.5f),
            (rect.down - maxHeight * 0.5f) / (maxHeight * 0.5f),
            (rect.right - maxWidth * 0.5f) / (maxWidth * 0.5f),
            (rect.down - maxHeight * 0.5f) / (maxHeight * 0.5f)
        )
        // 设置顶点属性
        run {
            val bb = ByteBuffer.allocateDirect(positionVertex.size * 4)
            bb.order(ByteOrder.nativeOrder())
            val buffer = bb.asFloatBuffer()
            buffer.put(positionVertex)
            buffer.position(0)
            GLES20.glVertexAttribPointer(positionLocation, 2, GLES20.GL_FLOAT, false, 0, buffer)
        }
        run {
            val bb = ByteBuffer.allocateDirect(uvVertex.size * 4)
            bb.order(ByteOrder.nativeOrder())
            val buffer = bb.asFloatBuffer()
            buffer.put(uvVertex)
            buffer.position(0)
            GLES20.glVertexAttribPointer(uvLocation, 2, GLES20.GL_FLOAT, false, 0, buffer)
        }
        GLES20.glUniform4f(
            colorLocation,
            spriteColor[0],
            spriteColor[1],
            spriteColor[2],
            spriteColor[3]
        )
        GLES20.glBindTexture(GLES20.GL_TEXTURE_2D, textureId)
        GLES20.glDrawArrays(GLES20.GL_TRIANGLE_FAN, 0, 4)
    }

    /** 调整大小 */
    fun resize(x: Float, y: Float, width: Float, height: Float) {
        rect.left = x - width * 0.5f
        rect.right = x + width * 0.5f
        rect.up = y + height * 0.5f
        rect.down = y - height * 0.5f
    }

    /**
     * 碰撞检测
     * @param pointX 触摸点x坐标
     * @param pointY 触摸点y坐标
     * @return 是否碰撞
     */
    fun isHit(pointX: Float, pointY: Float): Boolean {
        val y = maxHeight - pointY
        return (pointX >= rect.left && pointX <= rect.right && y <= rect.up && y >= rect.down)
    }

    /** 设置颜色 */
    fun setColor(r: Float, g: Float, b: Float, a: Float) {
        spriteColor[0] = r
        spriteColor[1] = g
        spriteColor[2] = b
        spriteColor[3] = a
    }

    /**
     * 设置窗口大小
     * @param width 宽度
     * @param height 高度
     */
    fun setWindowSize(width: Int, height: Int) {
        maxWidth = width
        maxHeight = height
    }

    /** 矩形类 */
    private class Rect {
        var left: Float = 0f
        var right: Float = 0f
        var up: Float = 0f
        var down: Float = 0f
    }
}
