package com.imyfone.live2d

import android.opengl.GLES20
import com.imyfone.live2d.LAppPal.loadFileAsBytes
import com.live2d.sdk.cubism.framework.utils.CubismDebug
import java.lang.AutoCloseable

/**
 * 精灵着色器设置类
 */
class LAppSpriteShader : AutoCloseable {
    /** 着色器ID */
    val shaderId: Int = createShader()

    override fun close() {
        GLES20.glDeleteShader(this.shaderId)
    }

    /**
     * 创建着色器
     * @return 着色器ID，创建失败返回0
     */
    private fun createShader(): Int {
        // 创建着色器路径
        var vertShaderFile = LAppDefine.ResourcePath.SHADER_ROOT.path
        vertShaderFile += ("/" + LAppDefine.ResourcePath.VERT_SHADER.path)
        var fragShaderFile = LAppDefine.ResourcePath.SHADER_ROOT.path
        fragShaderFile += ("/" + LAppDefine.ResourcePath.FRAG_SHADER.path)
        // 编译着色器
        val vertexShaderId = compileShader(vertShaderFile, GLES20.GL_VERTEX_SHADER)
        val fragmentShaderId = compileShader(fragShaderFile, GLES20.GL_FRAGMENT_SHADER)
        if (vertexShaderId == 0 || fragmentShaderId == 0) {
            return 0
        }
        // 创建程序对象
        val programId = GLES20.glCreateProgram()
        // 设置程序着色器
        GLES20.glAttachShader(programId, vertexShaderId)
        GLES20.glAttachShader(programId, fragmentShaderId)
        GLES20.glLinkProgram(programId)
        GLES20.glUseProgram(programId)
        // 删除不需要的着色器对象
        GLES20.glDeleteShader(vertexShaderId)
        GLES20.glDeleteShader(fragmentShaderId)
        return programId
    }

    /**
     * 检查着色器错误
     * @param shaderId 着色器ID
     * @return 检查结果，true表示无错误
     */
    private fun checkShader(shaderId: Int): Boolean {
        val logLength = IntArray(1)
        GLES20.glGetShaderiv(shaderId, GLES20.GL_INFO_LOG_LENGTH, logLength, 0)
        if (logLength[0] > 0) {
            val log = GLES20.glGetShaderInfoLog(shaderId)
            CubismDebug.cubismLogError("Shader compile log: %s", log)
        }
        val status = IntArray(1)
        GLES20.glGetShaderiv(shaderId, GLES20.GL_COMPILE_STATUS, status, 0)
        if (status[0] == GLES20.GL_FALSE) {
            GLES20.glDeleteShader(shaderId)
            return false
        }
        return true
    }

    /**
     * 编译着色器
     * @param fileName 着色器文件名
     * @param shaderType 着色器类型
     * @return 着色器ID，创建失败返回0
     */
    private fun compileShader(fileName: String, shaderType: Int): Int {
        // 读取文件
        val shaderBuffer = loadFileAsBytes(fileName)
        // 编译
        val shaderId = GLES20.glCreateShader(shaderType)
        GLES20.glShaderSource(shaderId, String(shaderBuffer))
        GLES20.glCompileShader(shaderId)
        if (!checkShader(shaderId)) {
            return 0
        }
        return shaderId
    }
}
