package com.imyfone.live2d

import android.media.AudioAttributes
import android.media.AudioFormat
import android.media.AudioTrack
import android.media.MediaExtractor
import android.media.MediaFormat
import com.imyfone.live2d.LAppDelegate.Companion.instance
import com.imyfone.live2d.LAppPal.loadFileAsBytes
import java.io.IOException

/**
 * WAV文件处理器
 */
class LAppWavFileHandler(private val filePath: String) : Thread() {
    override fun run() {
        loadWavFile()
    }

    /** 加载WAV文件 */
    fun loadWavFile() {
        val mediaExtractor = MediaExtractor()
        try {
            val afd = instance.context.assets.openFd(filePath)
            mediaExtractor.setDataSource(afd)
        } catch (e: IOException) {
            // 发生异常时只输出错误，不播放直接返回
            e.printStackTrace()
            return
        }
        val mf = mediaExtractor.getTrackFormat(0)
        val samplingRate = mf.getInteger(MediaFormat.KEY_SAMPLE_RATE)
        val bufferSize = AudioTrack.getMinBufferSize(
            samplingRate,
            AudioFormat.CHANNEL_OUT_MONO,
            AudioFormat.ENCODING_PCM_16BIT
        )
        val audioTrack = AudioTrack.Builder()
            .setAudioAttributes(
                AudioAttributes.Builder()
                    .setUsage(AudioAttributes.USAGE_GAME)
                    .setContentType(AudioAttributes.CONTENT_TYPE_SPEECH)
                    .build()
            )
            .setAudioFormat(
                AudioFormat.Builder()
                    .setEncoding(AudioFormat.ENCODING_PCM_16BIT)
                    .setSampleRate(samplingRate)
                    .setChannelMask(AudioFormat.CHANNEL_OUT_MONO)
                    .build()
            )
            .setBufferSizeInBytes(bufferSize)
            .build()
        audioTrack.play()
        // 避免杂音
        val offset = 100
        val voiceBuffer = loadFileAsBytes(filePath)
        audioTrack.write(voiceBuffer, offset, voiceBuffer.size - offset)
    }
}
