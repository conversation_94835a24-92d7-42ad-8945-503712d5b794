{"Version": 3, "Meta": {"Duration": 4, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 63, "TotalSegmentCount": 219, "TotalPointCount": 554, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Model", "Id": "Opacity", "Segments": [0, 1, 1, 1.32, 1, 2.64, 1, 3.97, 1, 0, 4, 1]}, {"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 9, 1, 0.333, 9, 0.667, 9, 1, 9, 1, 1.156, 9, 1.311, -3, 1.467, -3, 1, 1.722, -3, 1.978, -2.724, 2.233, -2, 1, 2.444, -1.402, 2.656, -1, 2.867, -1, 0, 4, -1]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, -6, 1, 0.289, -6, 0.578, -6, 0.867, -6, 1, 0.944, -6, 1.022, -8, 1.1, -8, 1, 1.256, -8, 1.411, -1.51, 1.567, 5, 1, 1.656, 8.72, 1.744, 9, 1.833, 9, 1, 2, 9, 2.167, -7, 2.333, -7, 1, 2.544, -7, 2.756, -6, 2.967, -6, 0, 4, -6]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, -16, 1, 0.333, -16, 0.667, -16, 1, -16, 1, 1.156, -16, 1.311, -26, 1.467, -26, 1, 1.722, -26, 1.978, 27, 2.233, 27, 1, 2.444, 27, 2.656, 24, 2.867, 24, 0, 4, 24]}, {"Target": "Parameter", "Id": "ParamTere", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.156, 0, 1.311, 0, 1.467, 0, 1, 1.667, 0, 1.867, 0, 2.067, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "ParamFaceForm", "Segments": [0, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 1, 0.333, 1, 0.667, 1, 1, 1, 1, 1.156, 1, 1.311, 1.898, 1.467, 1.898, 1, 1.667, 1.898, 1.867, 1, 2.067, 1, 0, 4, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.156, 0, 1.311, 0, 1.467, 0, 1, 1.667, 0, 1.867, 0, 2.067, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 1, 0.333, 1, 0.667, 1, 1, 1, 1, 1.156, 1, 1.311, 1.898, 1.467, 1.898, 1, 1.667, 1.898, 1.867, 1, 2.067, 1, 0, 4, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.156, 0, 1.311, 0, 1.467, 0, 1, 1.667, 0, 1.867, 0, 2.067, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "ParamEyeForm", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.156, 0, 1.311, 0, 1.467, 0, 1, 1.667, 0, 1.867, 0.1, 2.067, 0.1, 0, 4, 0.1]}, {"Target": "Parameter", "Id": "ParamEyeBallForm", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.156, 0, 1.311, 0, 1.467, 0, 1, 1.667, 0, 1.867, 0, 2.067, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "ParamTear", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.156, 0, 1.311, 0, 1.467, 0, 1, 1.667, 0, 1.867, 0, 2.067, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, -0.3, 1, 0.333, -0.3, 0.667, -0.3, 1, -0.3, 1, 1.156, -0.3, 1.311, 0.2, 1.467, 0.2, 1, 1.667, 0.2, 1.867, 0.1, 2.067, 0.1, 0, 4, 0.1]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0.5, 1, 0.333, 0.5, 0.667, 0.5, 1, 0.5, 1, 1.156, 0.5, 1.311, 0.2, 1.467, 0.2, 1, 1.667, 0.2, 1.867, 0.3, 2.067, 0.3, 0, 4, 0.3]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.156, 0, 1.311, 0.399, 1.467, 0.399, 1, 1.667, 0.399, 1.867, 0.2, 2.067, 0.2, 0, 4, 0.2]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.156, 0, 1.311, 0.399, 1.467, 0.399, 1, 1.667, 0.399, 1.867, 0.2, 2.067, 0.2, 0, 4, 0.2]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.156, 0, 1.311, 0.699, 1.467, 0.699, 1, 1.667, 0.699, 1.867, 0.5, 2.067, 0.5, 0, 4, 0.5]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.156, 0, 1.311, 0.699, 1.467, 0.699, 1, 1.667, 0.699, 1.867, 0.5, 2.067, 0.5, 0, 4, 0.5]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.156, 0, 1.311, 0.698, 1.467, 0.698, 1, 1.667, 0.698, 1.867, 0.4, 2.067, 0.4, 0, 4, 0.4]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.156, 0, 1.311, 0.698, 1.467, 0.698, 1, 1.667, 0.698, 1.867, 0.4, 2.067, 0.4, 0, 4, 0.4]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.156, 0, 1.311, 0.599, 1.467, 0.599, 1, 1.667, 0.599, 1.867, 0.4, 2.067, 0.4, 0, 4, 0.4]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.156, 0, 1.311, 0.599, 1.467, 0.599, 1, 1.667, 0.599, 1.867, 0.4, 2.067, 0.4, 0, 4, 0.4]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 1, 1, 0.333, 1, 0.667, 1, 1, 1, 1, 1.156, 1, 1.311, 0.603, 1.467, 0.603, 1, 1.667, 0.603, 1.867, 1, 2.067, 1, 0, 4, 1]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.156, 0, 1.311, 0.1, 1.467, 0.1, 1, 1.667, 0.1, 1.867, 0, 2.067, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "ParamScarf", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.156, 0, 1.311, 0, 1.467, 0, 1, 1.667, 0, 1.867, 0, 2.067, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 9, 1, 0.333, 9, 0.667, 9, 1, 9, 1, 1.156, 9, 1.311, 7.216, 1.467, 2.03, 1, 1.667, -4.639, 1.867, -9, 2.067, -9, 0, 4, -9]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.344, 0, 0.689, 0, 1.033, 0, 1, 1.189, 0, 1.344, 6, 1.5, 6, 1, 1.7, 6, 1.9, -1, 2.1, -1, 1, 2.456, -1, 2.811, 0, 3.167, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 1, 0.289, 0, 0.578, 0, 0.867, 0, 1, 1.022, 0, 1.178, -4.928, 1.333, -4.928, 1, 1.533, -4.928, 1.733, 5, 1.933, 5, 1, 2.289, 5, 2.644, 4, 3, 4, 0, 4, 4]}, {"Target": "Parameter", "Id": "ParamBodyUpper", "Segments": [0, 0, 1, 0.367, 0, 0.733, 0, 1.1, 0, 1, 1.256, 0, 1.411, -3.95, 1.567, -3.95, 1, 1.767, -3.95, 1.967, 3, 2.167, 3, 1, 2.522, 3, 2.878, 2, 3.233, 2, 0, 4, 2]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.156, 0, 1.311, 0, 1.467, 0, 1, 1.667, 0, 1.867, 0, 2.067, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "ParamBustY", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.156, 0, 1.311, 0, 1.467, 0, 1, 1.667, 0, 1.867, 0, 2.067, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "ParamArmLA", "Segments": [0, 1, 1, 0.333, 1, 0.667, 1, 1, 1, 1, 1.156, 1, 1.311, 0.5, 1.467, 0.5, 1, 1.667, 0.5, 1.867, 1, 2.067, 1, 0, 4, 1]}, {"Target": "Parameter", "Id": "ParamArmRA", "Segments": [0, 1, 1, 0.333, 1, 0.667, 1, 1, 1, 1, 1.156, 1, 1.311, 0.5, 1.467, 0.5, 1, 1.667, 0.5, 1.867, 1, 2.067, 1, 0, 4, 1]}, {"Target": "Parameter", "Id": "ParamArmLB", "Segments": [0, 5, 1, 0.333, 5, 0.667, 5, 1, 5, 1, 1.156, 5, 1.311, 5, 1.467, 5, 1, 1.667, 5, 1.867, 5, 2.067, 5, 0, 4, 5]}, {"Target": "Parameter", "Id": "ParamArmRB", "Segments": [0, 5, 1, 0.333, 5, 0.667, 5, 1, 5, 1, 1.156, 5, 1.311, 5, 1.467, 5, 1, 1.667, 5, 1.867, 5, 2.067, 5, 0, 4, 5]}, {"Target": "Parameter", "Id": "ParamHandChangeR", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.156, 0, 1.311, 0, 1.467, 0, 1, 1.667, 0, 1.867, 0, 2.067, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "ParamHandAngleR", "Segments": [0, -0.2, 1, 0.333, -0.2, 0.667, -0.2, 1, -0.2, 1, 1.078, -0.2, 1.156, 0.7, 1.233, 0.7, 1, 1.311, 0.7, 1.389, 0.508, 1.467, 0.069, 1, 1.578, -0.557, 1.689, -0.9, 1.8, -0.9, 1, 1.889, -0.9, 1.978, -0.1, 2.067, -0.1, 0, 4, -0.1]}, {"Target": "Parameter", "Id": "ParamHandDhangeL", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.156, 0, 1.311, 0, 1.467, 0, 1, 1.667, 0, 1.867, 0, 2.067, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "ParamHandAngleL", "Segments": [0, 0.3, 1, 0.333, 0.3, 0.667, 0.3, 1, 0.3, 1, 1.078, 0.3, 1.156, -1, 1.233, -1, 1, 1.311, -1, 1.389, -0.785, 1.467, -0.239, 1, 1.578, 0.542, 1.689, 1, 1.8, 1, 1, 1.889, 1, 1.978, 0.1, 2.067, 0.1, 0, 4, 0.1]}, {"Target": "Parameter", "Id": "ParamHairFront", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.156, 0, 1.311, 0, 1.467, 0, 1, 1.667, 0, 1.867, 0, 2.067, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "ParamHairSide", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.156, 0, 1.311, 0, 1.467, 0, 1, 1.667, 0, 1.867, 0, 2.067, 0, 0, 4, 0]}, {"Target": "Parameter", "Id": "ParamHairBack", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.156, 0, 1.311, 0, 1.467, 0, 1, 1.667, 0, 1.867, 0, 2.067, 0, 0, 4, 0]}, {"Target": "PartOpacity", "Id": "Part01Core", "Segments": [0, 1, 2, 3.97, 1, 0, 4, 1]}, {"Target": "PartOpacity", "Id": "Part01Hoho001", "Segments": [0, 1, 2, 3.97, 1, 0, 4, 1]}, {"Target": "PartOpacity", "Id": "Part01Brow001", "Segments": [0, 1, 2, 3.97, 1, 0, 4, 1]}, {"Target": "PartOpacity", "Id": "Part01Tear", "Segments": [0, 1, 2, 3.97, 1, 0, 4, 1]}, {"Target": "PartOpacity", "Id": "Part01EyeBall001", "Segments": [0, 1, 2, 3.97, 1, 0, 4, 1]}, {"Target": "PartOpacity", "Id": "Part01Eye001", "Segments": [0, 1, 2, 3.97, 1, 0, 4, 1]}, {"Target": "PartOpacity", "Id": "Part01Nose001", "Segments": [0, 1, 2, 3.97, 1, 0, 4, 1]}, {"Target": "PartOpacity", "Id": "Part01Mouth001", "Segments": [0, 1, 2, 3.97, 1, 0, 4, 1]}, {"Target": "PartOpacity", "Id": "Part01Face001", "Segments": [0, 1, 2, 3.97, 1, 0, 4, 1]}, {"Target": "PartOpacity", "Id": "Part01Ear001", "Segments": [0, 1, 2, 3.97, 1, 0, 4, 1]}, {"Target": "PartOpacity", "Id": "Part01Neck001", "Segments": [0, 1, 2, 3.97, 1, 0, 4, 1]}, {"Target": "PartOpacity", "Id": "Part01HairFront001", "Segments": [0, 1, 2, 3.97, 1, 0, 4, 1]}, {"Target": "PartOpacity", "Id": "Part01HairSide001", "Segments": [0, 1, 2, 3.97, 1, 0, 4, 1]}, {"Target": "PartOpacity", "Id": "Part01HairBack001", "Segments": [0, 1, 2, 3.97, 1, 0, 4, 1]}, {"Target": "PartOpacity", "Id": "Part01ArmRB001", "Segments": [0, 0, 2, 3.97, 0, 0, 4, 0]}, {"Target": "PartOpacity", "Id": "Part01ArmLB001", "Segments": [0, 0, 2, 3.97, 0, 0, 4, 0]}, {"Target": "PartOpacity", "Id": "Part01ArmRA001", "Segments": [0, 1, 2, 3.97, 1, 0, 4, 1]}, {"Target": "PartOpacity", "Id": "Part01ArmLA001", "Segments": [0, 1, 2, 3.97, 1, 0, 4, 1]}, {"Target": "PartOpacity", "Id": "Part01Body001", "Segments": [0, 1, 2, 3.97, 1, 0, 4, 1]}, {"Target": "PartOpacity", "Id": "Part01Sketch", "Segments": [0, 0, 2, 3.97, 0, 0, 4, 0]}]}