{"Version": 3, "Meta": {"Duration": 6.03, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 63, "TotalSegmentCount": 327, "TotalPointCount": 878, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Model", "Id": "Opacity", "Segments": [0, 1, 1, 2, 1, 4, 1, 6, 1, 0, 6.03, 1]}, {"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.733, 0, 1.133, -21, 1.533, -21, 1, 1.611, -21, 1.689, -21, 1.767, -21, 1, 1.944, -21, 2.122, -20.074, 2.3, -17.479, 1, 2.5, -12.897, 2.7, 19, 2.9, 19, 1, 3.078, 19, 3.256, 19, 3.433, 19, 1, 3.689, 19, 3.944, 19, 4.2, 19, 1, 4.8, 19, 5.4, 19, 6, 19, 0, 6.033, 19]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.544, 0, 0.756, -13, 0.967, -13, 1, 1.122, -13, 1.278, 7.633, 1.433, 8.986, 1, 1.578, 10.242, 1.722, 9.689, 1.867, 11, 1, 2.044, 12.613, 2.222, 28, 2.4, 28, 1, 2.6, 28, 2.8, -9.978, 3, -11.975, 1, 3.144, -13.418, 3.289, -13, 3.433, -13, 1, 3.689, -13, 3.944, -13, 4.2, -13, 1, 4.311, -13, 4.422, -6, 4.533, -6, 1, 4.644, -6, 4.756, -13, 4.867, -13, 1, 5.244, -13, 5.622, -13, 6, -13, 0, 6.033, -13]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, -6, 1, 0.111, -6, 0.222, -6, 0.333, -6, 1, 0.811, -6, 1.289, -17, 1.767, -17, 1, 1.844, -17, 1.922, -16.739, 2, -16, 1, 2.3, -7.838, 2.6, 30, 2.9, 30, 1, 3.078, 30, 3.256, 30, 3.433, 30, 1, 3.722, 30, 4.011, 30, 4.3, 30, 1, 4.411, 30, 4.522, 20, 4.633, 20, 1, 4.744, 20, 4.856, 30, 4.967, 30, 1, 5.311, 30, 5.656, 30, 6, 30, 0, 6.033, 30]}, {"Target": "Parameter", "Id": "ParamTere", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.767, 0, 1.2, 0, 1.633, 0, 1, 1.767, 0, 1.9, 0, 2.033, 0, 1, 3.356, 0, 4.678, 0, 6, 0, 0, 6.033, 0]}, {"Target": "Parameter", "Id": "ParamFaceForm", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 2.222, 0, 4.111, 0, 6, 0, 0, 6.033, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 1, 0.111, 1, 0.222, 1, 0.333, 1, 1, 0.467, 1, 0.6, 1, 0.733, 1, 1, 0.789, 1, 0.844, 0, 0.9, 0, 1, 0.967, 0, 1.033, 1, 1.1, 1, 1, 1.278, 1, 1.456, 1, 1.633, 1, 1, 1.767, 1, 1.9, 1, 2.033, 1, 1, 2.1, 1, 2.167, 0, 2.233, 0, 1, 2.267, 0, 2.3, 0, 2.333, 0, 1, 2.411, 0, 2.489, 0.9, 2.567, 0.9, 1, 2.678, 0.9, 2.789, 0.8, 2.9, 0.8, 1, 3.244, 0.8, 3.589, 0.8, 3.933, 0.8, 1, 3.978, 0.8, 4.022, 0, 4.067, 0, 1, 4.178, 0, 4.289, 0, 4.4, 0, 1, 4.478, 0, 4.556, 0.8, 4.633, 0.8, 1, 5.089, 0.8, 5.544, 0.8, 6, 0.8, 0, 6.033, 0.8]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.767, 0, 1.2, 0, 1.633, 0, 1, 1.767, 0, 1.9, 0, 2.033, 0, 1, 3.356, 0, 4.678, 0, 6, 0, 0, 6.033, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 1, 0.111, 1, 0.222, 1, 0.333, 1, 1, 0.467, 1, 0.6, 1, 0.733, 1, 1, 0.789, 1, 0.844, 0, 0.9, 0, 1, 0.967, 0, 1.033, 1, 1.1, 1, 1, 1.278, 1, 1.456, 1, 1.633, 1, 1, 1.767, 1, 1.9, 1, 2.033, 1, 1, 2.1, 1, 2.167, 0, 2.233, 0, 1, 2.267, 0, 2.3, 0, 2.333, 0, 1, 2.411, 0, 2.489, 0.9, 2.567, 0.9, 1, 2.678, 0.9, 2.789, 0.8, 2.9, 0.8, 1, 3.244, 0.8, 3.589, 0.8, 3.933, 0.8, 1, 3.978, 0.8, 4.022, 0, 4.067, 0, 1, 4.178, 0, 4.289, 0, 4.4, 0, 1, 4.478, 0, 4.556, 0.8, 4.633, 0.8, 1, 5.089, 0.8, 5.544, 0.8, 6, 0.8, 0, 6.033, 0.8]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.767, 0, 1.2, 0, 1.633, 0, 1, 1.767, 0, 1.9, 0, 2.033, 0, 1, 3.356, 0, 4.678, 0, 6, 0, 0, 6.033, 0]}, {"Target": "Parameter", "Id": "ParamEyeForm", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.767, 0, 1.2, 0, 1.633, 0, 1, 1.767, 0, 1.9, 0, 2.033, 0, 1, 3.356, 0, 4.678, 0, 6, 0, 0, 6.033, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallForm", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.767, 0, 1.2, 0, 1.633, 0, 1, 1.767, 0, 1.9, 0, 2.033, 0, 1, 3.356, 0, 4.678, 0, 6, 0, 0, 6.033, 0]}, {"Target": "Parameter", "Id": "ParamTear", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 0, 6.033, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.467, 0, 0.6, 0, 0.733, 0, 1, 0.856, 0, 0.978, -0.82, 1.1, -0.82, 1, 1.411, -0.82, 1.722, -0.82, 2.033, -0.82, 1, 2.667, -0.82, 3.3, -0.82, 3.933, -0.82, 1, 4.167, -0.82, 4.4, 1, 4.633, 1, 1, 5.089, 1, 5.544, 1, 6, 1, 0, 6.033, 1]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.467, 0, 0.6, 0, 0.733, 0, 1, 0.789, 0, 0.844, -0.4, 0.9, -0.4, 1, 0.967, -0.4, 1.033, 0.33, 1.1, 0.33, 1, 1.411, 0.33, 1.722, 0.33, 2.033, 0.33, 1, 2.211, 0.33, 2.389, -0.6, 2.567, -0.6, 1, 3.022, -0.6, 3.478, 0.33, 3.933, 0.33, 1, 3.978, 0.33, 4.022, -0.5, 4.067, -0.5, 1, 4.178, -0.5, 4.289, -0.5, 4.4, -0.5, 1, 4.478, -0.5, 4.556, -1, 4.633, -1, 1, 5.089, -1, 5.544, -1, 6, -1, 0, 6.033, -1]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.9, 0, 1.467, 0, 2.033, 0, 1, 2.211, 0, 2.389, 0, 2.567, 0, 1, 3.111, 0, 3.656, -0.73, 4.2, -0.73, 1, 4.8, -0.73, 5.4, -0.73, 6, -0.73, 0, 6.033, -0.73]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.9, 0, 1.467, 0, 2.033, 0, 1, 2.211, 0, 2.389, 0, 2.567, 0, 1, 3.111, 0, 3.656, -0.73, 4.2, -0.73, 1, 4.8, -0.73, 5.4, -0.73, 6, -0.73, 0, 6.033, -0.73]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.9, 0, 1.467, 0, 2.033, 0, 1, 2.211, 0, 2.389, 0, 2.567, 0, 1, 3.111, 0, 3.656, -0.4, 4.2, -0.4, 1, 4.8, -0.4, 5.4, -0.4, 6, -0.4, 0, 6.033, -0.4]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.9, 0, 1.467, 0, 2.033, 0, 1, 2.211, 0, 2.389, 0, 2.567, 0, 1, 3.111, 0, 3.656, -0.34, 4.2, -0.34, 1, 4.8, -0.34, 5.4, -0.34, 6, -0.34, 0, 6.033, -0.34]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.9, 0, 1.467, 0, 2.033, 0, 1, 2.211, 0, 2.389, 0, 2.567, 0, 1, 3.111, 0, 3.656, 0.47, 4.2, 0.47, 1, 4.8, 0.47, 5.4, 0.47, 6, 0.47, 0, 6.033, 0.47]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.9, 0, 1.467, 0, 2.033, 0, 1, 2.211, 0, 2.389, 0, 2.567, 0, 1, 3.111, 0, 3.656, 0.46, 4.2, 0.46, 1, 4.8, 0.46, 5.4, 0.46, 6, 0.46, 0, 6.033, 0.46]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.9, 0, 1.467, 0, 2.033, 0, 1, 2.211, 0, 2.389, -1, 2.567, -1, 1, 3.111, -1, 3.656, -1, 4.2, -1, 1, 4.8, -1, 5.4, -1, 6, -1, 0, 6.033, -1]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.9, 0, 1.467, 0, 2.033, 0, 1, 2.211, 0, 2.389, -1, 2.567, -1, 1, 3.111, -1, 3.656, -1, 4.2, -1, 1, 4.8, -1, 5.4, -1, 6, -1, 0, 6.033, -1]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.9, 0, 1.467, 0, 2.033, 0, 1, 2.211, 0, 2.389, 0, 2.567, 0, 1, 3.111, 0, 3.656, -1, 4.2, -1, 1, 4.8, -1, 5.4, -1, 6, -1, 0, 6.033, -1]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.767, 0, 1.2, 0, 1.633, 0, 1, 3.089, 0, 4.544, 0, 6, 0, 0, 6.033, 0]}, {"Target": "Parameter", "Id": "ParamScarf", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 2.222, 0, 4.111, 0, 6, 0, 0, 6.033, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.456, 0, 0.578, 1, 0.7, 1, 1, 1.056, 1, 1.411, -10, 1.767, -10, 1, 1.911, -10, 2.056, -10.237, 2.2, -9, 1, 2.389, -7.382, 2.578, 6.715, 2.767, 6.783, 1, 3.011, 6.871, 3.256, 6.898, 3.5, 6.933, 1, 3.711, 6.963, 3.922, 6.965, 4.133, 7, 1, 4.256, 7.02, 4.378, 10, 4.5, 10, 1, 4.622, 10, 4.744, 7, 4.867, 7, 1, 5.244, 7, 5.622, 7, 6, 7, 0, 6.033, 7]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.456, 0, 0.578, -1, 0.7, -1, 1, 1.056, -1, 1.411, -0.804, 1.767, 0, 1, 1.911, 0.327, 2.056, 1, 2.2, 1, 1, 2.389, 1, 2.578, -2.307, 2.767, -2.494, 1, 3.011, -2.735, 3.256, -2.828, 3.5, -2.918, 1, 3.711, -2.996, 3.922, -3, 4.133, -3, 1, 4.256, -3, 4.378, -2, 4.5, -2, 1, 4.622, -2, 4.744, -3, 4.867, -3, 1, 5.244, -3, 5.622, -3, 6, -3, 0, 6.033, -3]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.456, 0, 0.578, -1, 0.7, -1, 1, 1.056, -1, 1.411, 5, 1.767, 5, 1, 1.911, 5, 2.056, 0, 2.2, 0, 1, 2.389, 0, 2.578, 3.604, 2.767, 3.716, 1, 3.011, 3.86, 3.256, 3.923, 3.5, 3.965, 1, 3.711, 4.002, 3.922, 4, 4.133, 4, 1, 4.256, 4, 4.378, 3, 4.5, 3, 1, 4.622, 3, 4.744, 4, 4.867, 4, 1, 5.244, 4, 5.622, 4, 6, 4, 0, 6.033, 4]}, {"Target": "Parameter", "Id": "ParamBodyUpper", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.811, 0, 1.289, 0, 1.767, 0, 1, 1.911, 0, 2.056, -1, 2.2, -1, 1, 2.389, -1, 2.578, 0, 2.767, 0, 1, 3.222, 0, 3.678, 0, 4.133, 0, 1, 4.256, 0, 4.378, -1, 4.5, -1, 1, 4.622, -1, 4.744, 0, 4.867, 0, 1, 5.244, 0, 5.622, 0, 6, 0, 0, 6.033, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.678, 0, 1.022, 0, 1.367, 0, 1, 1.5, 0, 1.633, 0, 1.767, 0, 1, 3.178, 0, 4.589, 0, 6, 0, 0, 6.033, 0]}, {"Target": "Parameter", "Id": "ParamBustY", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.678, 0, 1.022, 0, 1.367, 0, 1, 1.5, 0, 1.633, 0, 1.767, 0, 1, 3.178, 0, 4.589, 0, 6, 0, 0, 6.033, 0]}, {"Target": "Parameter", "Id": "ParamArmLA", "Segments": [0, 0.5, 1, 0.111, 0.5, 0.222, 0.5, 0.333, 0.5, 1, 0.678, 0.5, 1.022, 0.5, 1.367, 0.5, 1, 1.5, 0.5, 1.633, 0.5, 1.767, 0.5, 1, 3.178, 0.5, 4.589, 0.5, 6, 0.5, 0, 6.033, 0.5]}, {"Target": "Parameter", "Id": "ParamArmRA", "Segments": [0, 0.5, 1, 0.111, 0.5, 0.222, 0.5, 0.333, 0.5, 1, 0.678, 0.5, 1.022, 0.5, 1.367, 0.5, 1, 1.5, 0.5, 1.633, 0.5, 1.767, 0.5, 1, 3.178, 0.5, 4.589, 0.5, 6, 0.5, 0, 6.033, 0.5]}, {"Target": "Parameter", "Id": "ParamArmLB", "Segments": [0, -0.2, 1, 0.111, -0.2, 0.222, -0.2, 0.333, -0.2, 1, 0.489, -0.2, 0.644, -0.2, 0.8, -0.2, 1, 0.989, -0.2, 1.178, -1, 1.367, -1, 1, 1.5, -1, 1.633, -1, 1.767, -1, 1, 3.178, -1, 4.589, -1, 6, -1, 0, 6.033, -1]}, {"Target": "Parameter", "Id": "ParamArmRB", "Segments": [0, 0.3, 1, 0.111, 0.3, 0.222, 0.3, 0.333, 0.3, 1, 0.489, 0.3, 0.644, 0.336, 0.8, 0.41, 1, 0.978, 0.57, 1.156, 1.867, 1.333, 2.688, 1, 1.478, 3.355, 1.622, 3.8, 1.767, 3.8, 1, 2.1, 3.8, 2.433, 3.11, 2.767, 2.9, 1, 3.111, 2.683, 3.456, 2.7, 3.8, 2.7, 1, 3.933, 2.7, 4.067, 3.2, 4.2, 3.2, 1, 4.5, 3.2, 4.8, 2.7, 5.1, 2.7, 1, 5.4, 2.7, 5.7, 2.7, 6, 2.7, 0, 6.033, 2.7]}, {"Target": "Parameter", "Id": "ParamHandChangeR", "Segments": [0, 0, 1, 0.189, 0, 0.378, 0, 0.567, 0, 1, 2.378, 0, 4.189, 0, 6, 0, 0, 6.033, 0]}, {"Target": "Parameter", "Id": "ParamHandAngleR", "Segments": [0, 0, 1, 0.189, 0, 0.378, 0, 0.567, 0, 1, 2.378, 0, 4.189, 0, 6, 0, 0, 6.033, 0]}, {"Target": "Parameter", "Id": "ParamHandDhangeL", "Segments": [0, 0, 1, 0.189, 0, 0.378, 0, 0.567, 0, 1, 2.378, 0, 4.189, 0, 6, 0, 0, 6.033, 0]}, {"Target": "Parameter", "Id": "ParamHandAngleL", "Segments": [0, -0.3, 1, 0.189, -0.3, 0.378, -0.3, 0.567, -0.3, 1, 0.756, -0.3, 0.944, -0.621, 1.133, -0.621, 1, 1.289, -0.621, 1.444, -0.632, 1.6, -0.573, 1, 1.767, -0.509, 1.933, -0.064, 2.1, 0.312, 1, 2.233, 0.613, 2.367, 1, 2.5, 1, 1, 2.722, 1, 2.944, -0.55, 3.167, -0.55, 1, 3.456, -0.55, 3.744, -0.55, 4.033, -0.55, 1, 4.156, -0.55, 4.278, 0.35, 4.4, 0.35, 1, 4.711, 0.35, 5.022, -0.55, 5.333, -0.55, 1, 5.556, -0.55, 5.778, -0.55, 6, -0.55, 0, 6.033, -0.55]}, {"Target": "Parameter", "Id": "ParamHairFront", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.678, 0, 1.022, 0, 1.367, 0, 1, 1.5, 0, 1.633, 0, 1.767, 0, 1, 3.178, 0, 4.589, 0, 6, 0, 0, 6.033, 0]}, {"Target": "Parameter", "Id": "ParamHairSide", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 2.222, 0, 4.111, 0, 6, 0, 0, 6.033, 0]}, {"Target": "Parameter", "Id": "ParamHairBack", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.678, 0, 1.022, 0, 1.367, 0, 1, 1.5, 0, 1.633, 0, 1.767, 0, 1, 3.178, 0, 4.589, 0, 6, 0, 0, 6.033, 0]}, {"Target": "PartOpacity", "Id": "Part01Core", "Segments": [0, 1, 2, 6, 1, 0, 6.03, 1]}, {"Target": "PartOpacity", "Id": "Part01Hoho001", "Segments": [0, 1, 2, 6, 1, 0, 6.03, 1]}, {"Target": "PartOpacity", "Id": "Part01Brow001", "Segments": [0, 1, 2, 6, 1, 0, 6.03, 1]}, {"Target": "PartOpacity", "Id": "Part01Tear", "Segments": [0, 1, 2, 6, 1, 0, 6.03, 1]}, {"Target": "PartOpacity", "Id": "Part01EyeBall001", "Segments": [0, 1, 2, 6, 1, 0, 6.03, 1]}, {"Target": "PartOpacity", "Id": "Part01Eye001", "Segments": [0, 1, 2, 6, 1, 0, 6.03, 1]}, {"Target": "PartOpacity", "Id": "Part01Nose001", "Segments": [0, 1, 2, 6, 1, 0, 6.03, 1]}, {"Target": "PartOpacity", "Id": "Part01Mouth001", "Segments": [0, 1, 2, 6, 1, 0, 6.03, 1]}, {"Target": "PartOpacity", "Id": "Part01Face001", "Segments": [0, 1, 2, 6, 1, 0, 6.03, 1]}, {"Target": "PartOpacity", "Id": "Part01Ear001", "Segments": [0, 1, 2, 6, 1, 0, 6.03, 1]}, {"Target": "PartOpacity", "Id": "Part01Neck001", "Segments": [0, 1, 2, 6, 1, 0, 6.03, 1]}, {"Target": "PartOpacity", "Id": "Part01HairFront001", "Segments": [0, 1, 2, 6, 1, 0, 6.03, 1]}, {"Target": "PartOpacity", "Id": "Part01HairSide001", "Segments": [0, 1, 2, 6, 1, 0, 6.03, 1]}, {"Target": "PartOpacity", "Id": "Part01HairBack001", "Segments": [0, 1, 2, 6, 1, 0, 6.03, 1]}, {"Target": "PartOpacity", "Id": "Part01ArmRB001", "Segments": [0, 1, 2, 6, 1, 0, 6.03, 1]}, {"Target": "PartOpacity", "Id": "Part01ArmLB001", "Segments": [0, 1, 2, 6, 1, 0, 6.03, 1]}, {"Target": "PartOpacity", "Id": "Part01ArmRA001", "Segments": [0, 0, 2, 6, 0, 0, 6.03, 0]}, {"Target": "PartOpacity", "Id": "Part01ArmLA001", "Segments": [0, 0, 2, 6, 0, 0, 6.03, 0]}, {"Target": "PartOpacity", "Id": "Part01Body001", "Segments": [0, 1, 2, 6, 1, 0, 6.03, 1]}, {"Target": "PartOpacity", "Id": "Part01Sketch", "Segments": [0, 0, 2, 6, 0, 0, 6.03, 0]}]}