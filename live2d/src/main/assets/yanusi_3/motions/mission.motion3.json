{"Version": 3, "Meta": {"Duration": 14.733, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 355, "TotalSegmentCount": 5089, "TotalPointCount": 5767, "UserDataCount": 3, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, -2.46, 0, 0.933, 2.82, 0, 1.667, 2.16, 0, 3.833, 2.28, 0, 4, 1.91, 0, 4.5, 2.792, 2, 5.333, 2.792, 2, 6.5, 2.792, 2, 7.367, 2.792, 0, 7.8, 3.752, 0, 8.233, 2.417, 0, 8.633, 3.533, 0, 8.933, 2.992, 0, 9.467, 3.517, 0, 10.333, 2.672, 0, 11.133, 2.792, 0, 11.333, 2.076, 1, 11.422, 2.076, 11.511, 2.064, 11.6, 2.681, 1, 11.667, 3.143, 11.733, 5.349, 11.8, 5.349, 0, 12.033, -0.089, 0, 12.233, 5.349, 0, 12.467, -0.089, 1, 12.534, -0.089, 12.6, 1.618, 12.667, 2.681, 1, 12.767, 4.275, 12.867, 4.569, 12.967, 4.569, 0, 13.433, 2.681, 2, 13.667, 2.681, 0, 14.1, 5.218, 0, 14.467, -1.816, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, -5.22, 0, 0.867, 13.02, 1, 0.956, 13.02, 1.044, 0.319, 1.133, -0.55, 1, 1.311, -2.288, 1.489, -2.412, 1.667, -2.412, 2, 2.9, -2.412, 1, 3.211, -2.412, 3.522, -1.954, 3.833, -0.081, 1, 3.911, 0.388, 3.989, 5.362, 4.067, 5.362, 1, 4.189, 5.362, 4.311, -6.291, 4.433, -10.167, 1, 4.555, -14.044, 4.678, -13.989, 4.8, -13.989, 2, 5.333, -13.989, 0, 5.6, -17.178, 1, 5.789, -17.178, 5.978, -7.019, 6.167, -3.842, 1, 6.278, -1.973, 6.389, -2.412, 6.5, -2.412, 0, 7.367, -19.873, 0, 7.8, -13.665, 0, 8.233, -18.816, 0, 8.633, -11.405, 0, 8.933, -18.816, 0, 9.5, -13.665, 0, 10.267, -18.855, 0, 11.133, -8.512, 0, 11.333, -12.184, 0, 11.6, 8.304, 0, 12.033, 5.362, 0, 12.2, 8.629, 0, 12.467, 4.105, 1, 12.545, 4.105, 12.622, 4.608, 12.7, 6.2, 1, 12.789, 8.018, 12.878, 9.318, 12.967, 9.318, 0, 13.433, 7.584, 2, 13.667, 7.584, 0, 14.1, -1.44, 0, 14.467, 3.569, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, -1.28, 0, 0.867, 13.967, 1, 1.111, 13.967, 1.356, 12.39, 1.6, 11.332, 1, 1.789, 10.514, 1.978, 10.574, 2.167, 9.708, 1, 2.311, 9.046, 2.456, 4.502, 2.6, 4.502, 1, 2.7, 4.502, 2.8, 5.605, 2.9, 6.118, 1, 3.067, 6.972, 3.233, 7.051, 3.4, 8.072, 1, 3.578, 9.161, 3.755, 11.642, 3.933, 11.642, 1, 4.078, 11.642, 4.222, 5.326, 4.367, 4.502, 1, 4.467, 3.931, 4.567, 4.101, 4.667, 4.101, 1, 4.789, 4.101, 4.911, 5.302, 5.033, 5.764, 1, 5.189, 6.352, 5.344, 6.371, 5.5, 6.371, 2, 6.5, 6.371, 2, 7.367, 6.371, 2, 10.333, 6.371, 2, 11.133, 6.371, 0, 11.333, 5.764, 0, 11.6, 6.678, 0, 12.667, 6.118, 2, 12.967, 6.118, 2, 13.433, 6.118, 2, 13.667, 6.118, 1, 13.811, 6.118, 13.956, 6.19, 14.1, 6.474, 1, 14.133, 6.54, 14.167, 7.193, 14.2, 7.193, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 2, 0.333, 1, 0, 0.433, 0, 0, 0.567, 1, 2, 0.933, 1, 1, 1.178, 1, 1.422, 0.864, 1.667, 0.825, 1, 2.078, 0.759, 2.489, 0.754, 2.9, 0.754, 0, 3.833, 0.825, 0, 4, 0, 0, 4.267, 0.9, 0, 4.5, 0.721, 1, 4.778, 0.721, 5.055, 0.733, 5.333, 0.823, 1, 5.533, 0.888, 5.733, 1, 5.933, 1, 2, 6.5, 1, 2, 7.367, 1, 0, 7.767, 0.765, 2, 10.333, 0.765, 0, 11.133, 1, 0, 11.333, 0, 0, 11.6, 1, 2, 12.667, 1, 2, 12.967, 1, 2, 13.433, 1, 2, 13.667, 1, 2, 14.1, 1, 0, 14.2, 0, 0, 14.333, 1, 2, 14.733, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 0, 0.933, 1, 0, 1.667, 0, 2, 2.9, 0, 2, 3.833, 0, 2, 4.5, 0, 2, 5.333, 0, 2, 6.5, 0, 0, 7.367, 1, 0, 11.133, 0, 2, 11.6, 0, 2, 12.667, 0, 2, 12.967, 0, 2, 13.433, 0, 2, 13.667, 0, 2, 14.1, 0, 2, 14.333, 0, 2, 14.733, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 2, 0.333, 1, 0, 0.433, 0, 0, 0.567, 1, 2, 0.933, 1, 1, 1.178, 1, 1.422, 0.875, 1.667, 0.844, 1, 2.078, 0.792, 2.489, 0.789, 2.9, 0.789, 0, 3.833, 0.844, 0, 4, 0, 0, 4.267, 0.9, 0, 4.5, 0.729, 1, 4.778, 0.729, 5.055, 0.741, 5.333, 0.828, 1, 5.533, 0.891, 5.733, 1, 5.933, 1, 2, 6.5, 1, 2, 7.367, 1, 0, 7.767, 0.792, 2, 10.333, 0.792, 0, 11.133, 1, 0, 11.333, 0, 0, 11.6, 1, 2, 12.667, 1, 2, 12.967, 1, 2, 13.433, 1, 2, 13.667, 1, 2, 14.1, 1, 0, 14.2, 0, 0, 14.333, 1, 2, 14.733, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 0, 0.933, 1, 0, 1.667, 0, 2, 2.9, 0, 2, 3.833, 0, 2, 4.5, 0, 2, 5.333, 0, 2, 6.5, 0, 0, 7.367, 1, 2, 10.333, 1, 0, 11.133, 0, 2, 11.6, 0, 2, 12.667, 0, 2, 12.967, 0, 2, 13.433, 0, 2, 13.667, 0, 2, 14.733, 0]}, {"Target": "Parameter", "Id": "Param76", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 0, 0.933, -30, 2, 1.667, -30, 0, 2.9, -29.779, 2, 3.833, -29.779, 2, 4.5, -29.779, 2, 5.333, -29.779, 2, 6.5, -29.779, 2, 7.367, -29.779, 2, 10.333, -29.779, 2, 11.133, -29.779, 2, 11.6, -29.779, 0, 12.667, 0, 2, 12.967, 0, 2, 13.433, 0, 2, 13.667, 0, 2, 14.733, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.933, 0, 2, 1.667, 0, 2, 2.9, 0, 2, 3.833, 0, 2, 4.5, 0, 2, 5.333, 0, 2, 6.5, 0, 0, 7.367, -0.4, 2, 10.767, -0.4, 0, 10.933, 0, 2, 11.133, 0, 2, 11.6, 0, 2, 12.667, 0, 2, 12.967, 0, 2, 13.433, 0, 2, 13.667, 0, 2, 14.733, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.933, 0, 2, 1.667, 0, 2, 2.9, 0, 2, 3.833, 0, 0, 4.5, -0.5, 1, 4.778, -0.5, 5.055, -0.504, 5.333, -0.458, 1, 5.533, -0.424, 5.733, 0, 5.933, 0, 2, 6.5, 0, 0, 7.367, -0.6, 2, 10.767, -0.6, 0, 10.933, 0, 2, 11.133, 0, 2, 11.6, 0, 2, 12.667, 0, 2, 12.967, 0, 2, 13.433, 0, 2, 13.667, 0, 2, 14.733, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.933, 0, 2, 1.667, 0, 2, 2.9, 0, 2, 3.833, 0, 2, 4.5, 0, 2, 5.333, 0, 2, 6.5, 0, 2, 7.367, 0, 2, 10.333, 0, 2, 11.133, 0, 0, 11.6, 0.064, 2, 12.667, 0.064, 2, 12.967, 0.064, 2, 13.433, 0.064, 2, 13.667, 0.064, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, -0.362, 2, 0.933, -0.362, 2, 1.667, -0.362, 2, 2.9, -0.362, 2, 3.833, -0.362, 0, 4.5, -0.358, 2, 5.333, -0.358, 2, 6.5, -0.358, 2, 7.367, -0.358, 2, 10.333, -0.358, 2, 11.133, -0.358, 0, 11.6, -0.344, 2, 12.667, -0.344, 2, 12.967, -0.344, 2, 13.433, -0.344, 2, 13.667, -0.344, 0, 14.2, 0, 2, 14.733, 0]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.933, 0, 2, 1.667, 0, 2, 2.9, 0, 2, 3.833, 0, 2, 4.5, 0, 2, 5.333, 0, 2, 6.5, 0, 2, 7.367, 0, 2, 10.333, 0, 2, 11.133, 0, 0, 11.6, 0.082, 2, 12.667, 0.082, 2, 12.967, 0.082, 2, 13.433, 0.082, 2, 13.667, 0.082, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, -0.406, 2, 0.933, -0.406, 2, 1.667, -0.406, 2, 2.9, -0.406, 2, 3.833, -0.406, 0, 4.5, -0.402, 2, 5.333, -0.402, 2, 6.5, -0.402, 2, 7.367, -0.402, 2, 10.333, -0.402, 2, 11.133, -0.402, 0, 11.6, -0.386, 2, 12.667, -0.386, 2, 12.967, -0.386, 2, 13.433, -0.386, 2, 13.667, -0.386, 0, 14.2, 0, 2, 14.733, 0]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, -1, 2, 0.333, -1, 2, 0.433, -1, 0, 0.5, 0, 2, 0.933, 0, 2, 1.667, 0, 2, 2.9, 0, 2, 3.833, 0, 0, 4.5, -1, 2, 5.333, -1, 2, 6.5, -1, 2, 7.367, -1, 2, 10.333, -1, 2, 11.133, -1, 2, 11.6, -1, 2, 12.667, -1, 2, 12.967, -1, 2, 13.433, -1, 2, 13.667, -1, 2, 14.733, -1]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, -1, 2, 0.333, -1, 2, 0.433, -1, 0, 0.5, 0, 2, 0.933, 0, 2, 1.667, 0, 2, 2.9, 0, 2, 3.833, 0, 0, 4.5, -1, 2, 5.333, -1, 2, 6.5, -1, 2, 7.367, -1, 2, 10.333, -1, 2, 11.133, -1, 2, 11.6, -1, 2, 12.667, -1, 2, 12.967, -1, 2, 13.433, -1, 2, 13.667, -1, 2, 14.733, -1]}, {"Target": "Parameter", "Id": "Param", "Segments": [0, -30, 2, 0.333, -30, 0, 0.433, 0, 2, 0.5, 0, 2, 0.933, 0, 2, 1.467, 0, 2, 1.667, 0, 2, 2.9, 0, 2, 3.833, 0, 2, 4.5, 0, 2, 5.333, 0, 2, 6.5, 0, 2, 7.367, 0, 2, 10.333, 0, 2, 11.133, 0, 2, 11.333, 0, 0, 11.6, 30, 2, 11.8, 30, 2, 12.667, 30, 2, 12.967, 30, 2, 13.433, 30, 1, 13.511, 30, 13.589, 28.123, 13.667, 21, 1, 14.022, -11.563, 14.378, -30, 14.733, -30]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 1, 2, 0.333, 1, 0, 0.433, 0, 2, 0.5, 0, 0, 0.633, 0.8, 1, 0.678, 0.8, 0.722, 0.601, 0.767, 0.3, 1, 0.8, 0.074, 0.834, 0, 0.867, 0, 2, 0.933, 0, 2, 1.467, 0, 2, 1.667, 0, 2, 2.9, 0, 2, 3.833, 0, 1, 4.055, 0, 4.278, -0.221, 4.5, -0.7, 1, 4.556, -0.82, 4.611, -0.9, 4.667, -0.9, 0, 6.5, 0, 2, 7.367, 0, 2, 10.333, 0, 2, 11.133, 0, 1, 11.2, 0, 11.266, -0.361, 11.333, -0.454, 1, 11.422, -0.578, 11.511, -0.583, 11.6, -0.7, 1, 11.667, -0.787, 11.733, -1, 11.8, -1, 1, 11.878, -1, 11.955, -1.002, 12.033, -0.6, 1, 12.1, -0.255, 12.166, 0.7, 12.233, 0.7, 0, 12.467, -0.543, 2, 12.667, -0.543, 2, 12.967, -0.543, 2, 13.433, -0.543, 0, 13.667, 0, 2, 14.2, 0, 0, 14.733, 1]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 1, 0.466, 0, 0.5, 0.129, 0.533, 0.373, 1, 0.555, 0.536, 0.578, 0.611, 0.6, 0.611, 0, 0.667, 0.45, 0, 0.733, 0.505, 0, 0.8, 0.028, 0, 0.867, 0.744, 0, 1.333, 0, 2, 1.6, 0, 0, 1.733, 0.673, 0, 1.8, 0.004, 0, 1.867, 0.196, 0, 1.933, 0.094, 0, 2, 0.752, 0, 2.067, 0.646, 0, 2.133, 0.873, 0, 2.267, 0.326, 0, 2.4, 0.768, 0, 2.467, 0.426, 0, 2.6, 0.713, 0, 2.667, 0.043, 0, 2.733, 0.673, 0, 2.933, 0, 2, 4.4, 0, 1, 4.433, 0, 4.467, 0.212, 4.5, 0.541, 1, 4.522, 0.76, 4.545, 0.831, 4.567, 0.831, 0, 4.9, 0, 2, 5.333, 0, 0, 5.533, 0.694, 0, 5.6, 0.671, 0, 5.667, 0.737, 0, 5.8, 0.408, 0, 5.933, 0.6, 0, 6, 0.451, 0, 6.133, 0.643, 0, 6.4, 0.098, 0, 6.533, 0.235, 0, 6.733, 0, 2, 7.467, 0, 0, 7.6, 0.404, 0, 7.733, 0.012, 0, 7.867, 0.663, 0, 8, 0, 0, 8.133, 0.686, 0, 8.267, 0.307, 0, 8.4, 0.526, 0, 8.467, 0.161, 0, 8.533, 0.643, 0, 8.667, 0, 0, 8.8, 0.639, 0, 8.933, 0.008, 0, 9.067, 0.581, 0, 9.267, 0.004, 0, 9.333, 0.42, 0, 9.4, 0, 0, 9.467, 0.682, 0, 9.6, 0, 0, 9.667, 0.392, 0, 9.733, 0.213, 0, 9.8, 0.463, 0, 9.867, 0.135, 0, 10, 0.486, 0, 10.4, 0, 2, 11.133, 0, 2, 11.333, 0, 2, 11.6, 0, 0, 11.8, 0.7, 0, 12.033, 0, 0, 12.233, 0.7, 0, 12.467, 0, 2, 12.667, 0, 2, 12.967, 0, 2, 13.433, 0, 0, 13.667, 0.1, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param252", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.933, 0, 2, 1.667, 0, 2, 2.9, 0, 2, 3.833, 0, 2, 4.5, 0, 2, 5.333, 0, 2, 6.5, 0, 2, 7.367, 0, 2, 10.333, 0, 2, 11.133, 0, 0, 11.6, 1, 2, 12.667, 1, 2, 12.967, 1, 2, 13.433, 1, 0, 13.667, 0, 2, 14.733, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 2, 0.333, 0, 0, 0.5, 3.42, 1, 0.544, 3.42, 0.589, 3.735, 0.633, 2.81, 1, 0.755, 0.266, 0.878, -5.4, 1, -5.4, 0, 1.6, -0.946, 0, 2.067, -4.423, 0, 2.533, -0.946, 0, 3.033, -4.152, 1, 3.211, -4.152, 3.389, -1.482, 3.567, 0, 1, 3.734, 1.39, 3.9, 1.406, 4.067, 1.406, 1, 4.234, 1.406, 4.4, -7.29, 4.567, -8.185, 1, 4.845, -9.676, 5.122, -9.685, 5.4, -9.685, 1, 5.6, -9.685, 5.8, -0.356, 6, 1.475, 1, 6.256, 3.814, 6.511, 3.74, 6.767, 3.74, 0, 7.433, -5.905, 0, 7.933, -0.139, 0, 8.633, -5.226, 0, 9.167, -0.757, 0, 9.6, -4.64, 1, 9.867, -4.64, 10.133, -3.303, 10.4, -1.465, 1, 10.511, -0.699, 10.622, -0.591, 10.733, -0.591, 0, 11.2, -5.425, 0, 11.667, 3.954, 0, 12.1, -3.063, 0, 12.333, 3.999, 0, 12.733, -5.057, 0, 13.033, 1.32, 0, 13.433, -10.357, 0, 13.667, -3.599, 0, 14, -14.542, 0, 14.467, 1.572, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 2, 0.333, 0, 0, 0.5, -2.853, 0, 1.1, 5.363, 0, 1.833, 4.163, 1, 2.555, 4.163, 3.278, 4.507, 4, 5.363, 1, 4.056, 5.429, 4.111, 6.606, 4.167, 6.606, 1, 4.334, 6.606, 4.5, 2.806, 4.667, 1.829, 1, 4.945, 0.2, 5.222, 0.089, 5.5, 0.089, 0, 6.1, 8.729, 0, 7.533, 7.829, 0, 7.967, 9.904, 0, 8.4, 6.546, 0, 8.8, 10.742, 0, 9.1, 6.574, 0, 9.667, 10.486, 0, 10.5, 2.549, 0, 11.3, 5.309, 0, 11.5, 3.403, 0, 11.767, 5.098, 2, 12.833, 5.098, 2, 13.133, 5.098, 2, 13.433, 5.098, 2, 13.667, 5.098, 0, 14, -9.406, 0, 14.467, 1.729, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 2, 0.333, 0, 0, 0.5, 1.027, 0, 1, -2.22, 0, 1.6, -1.359, 0, 2.067, -2.45, 0, 2.533, -1.479, 0, 3.033, -2.22, 0, 3.9, -1.919, 0, 4.067, -2.45, 1, 4.234, -2.45, 4.4, 0.943, 4.567, 1.461, 1, 4.845, 2.325, 5.122, 2.34, 5.4, 2.34, 0, 6, -0.819, 0, 7.433, -0.639, 0, 7.867, -1.479, 0, 8.3, -1.012, 0, 8.7, -1.406, 0, 9.133, -0.424, 0, 9.567, -1.012, 0, 10.4, -0.639, 0, 11.2, -1.299, 0, 11.667, 0.917, 0, 12.1, 0.326, 0, 12.333, 1.019, 0, 12.733, 0.272, 0, 13.033, 1.033, 0, 13.433, 0.303, 1, 13.511, 0.303, 13.589, 0.371, 13.667, 0.466, 1, 13.845, 0.683, 14.022, 0.775, 14.2, 0.775, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param267", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, 0.42, 0, 0.933, -0.96, 0, 1.533, 0.727, 0, 2, 0, 0, 2.467, 0.451, 0, 2.967, 0, 2, 3.833, 0, 0, 4, -0.182, 1, 4.167, -0.182, 4.333, 3.819, 4.5, 4.38, 1, 4.778, 5.316, 5.055, 5.328, 5.333, 5.328, 0, 5.933, 2.04, 0, 7.367, 2.34, 2, 10.333, 2.34, 2, 11.133, 2.34, 0, 11.6, 2.247, 2, 12.667, 2.247, 2, 12.967, 2.247, 2, 13.433, 2.247, 2, 13.667, 2.247, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param3", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, -1.26, 0, 0.933, 5.292, 0, 1.533, 2.408, 0, 2, 3.672, 0, 2.467, 0.991, 0, 2.967, 3.672, 1, 3.311, 3.672, 3.656, 3.586, 4, 2.356, 1, 4.167, 1.76, 4.333, -8.981, 4.5, -9.557, 1, 4.778, -10.517, 5.055, -10.516, 5.333, -10.516, 0, 5.933, -5.334, 0, 7.367, -5.848, 0, 7.8, -5.309, 0, 8.2, -5.978, 0, 8.633, -4.704, 0, 8.933, -6.122, 0, 9.467, -4.953, 1, 9.756, -4.953, 10.044, -5.402, 10.333, -5.848, 1, 10.6, -6.26, 10.866, -6.334, 11.133, -6.334, 1, 11.2, -6.334, 11.266, -6.129, 11.333, -3.061, 1, 11.422, 1.03, 11.511, 6.289, 11.6, 6.289, 0, 12.667, 5.805, 2, 12.967, 5.805, 2, 13.433, 5.805, 2, 13.667, 5.805, 0, 14.3, -1.484, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param4", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, -2.76, 0, 0.933, 6.924, 0, 1.267, 3.684, 0, 2, 5.005, 0, 2.633, 3.444, 0, 3.033, 4.537, 0, 3.833, 3.444, 0, 4, 5.094, 0, 4.5, -0.204, 1, 4.778, -0.204, 5.055, -0.25, 5.333, 0.309, 1, 5.533, 0.712, 5.733, 5.976, 5.933, 5.976, 0, 6.633, 2.712, 1, 6.878, 2.712, 7.122, 3.315, 7.367, 4.716, 1, 7.511, 5.544, 7.656, 6.07, 7.8, 6.07, 0, 8.2, 3.9, 0, 8.633, 5.917, 0, 8.933, 4.266, 0, 9.467, 6.306, 0, 10.333, 4.716, 2, 11.133, 4.716, 0, 11.333, 3.444, 0, 11.7, 6.306, 0, 12, 4.537, 0, 12.467, 6.924, 0, 12.8, 4.537, 0, 13.433, 6.208, 0, 13.8, 2.712, 0, 14.167, 4.247, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param5", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, 3.9, 0, 0.933, -12.518, 2, 1.667, -12.518, 2, 2.9, -12.518, 2, 3.833, -12.518, 2, 3.9, -12.518, 0, 4.067, -14.374, 0, 4.5, 0, 2, 5.333, 0, 0, 5.933, -10.92, 1, 6.411, -10.92, 6.889, 3.07, 7.367, 3.18, 1, 8.422, 3.422, 9.478, 3.431, 10.533, 3.431, 0, 11.133, -8.397, 0, 11.6, 3.25, 0, 12.667, 0, 2, 12.967, 0, 2, 13.433, 0, 2, 13.667, 0, 2, 14.733, 0]}, {"Target": "Parameter", "Id": "Param6", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, 3.48, 0, 0.933, 0, 2, 1.667, 0, 2, 2.9, 0, 2, 3.833, 0, 0, 4.067, -2.7, 0, 4.5, 0, 2, 5.333, 0, 0, 5.933, -4.32, 0, 7.367, 4, 1, 8.422, 4, 9.478, 3.881, 10.533, 3.431, 1, 10.733, 3.346, 10.933, -8.397, 11.133, -8.397, 0, 11.6, 3.25, 0, 12.667, 0, 2, 12.967, 0, 2, 13.433, 0, 2, 13.667, 0, 2, 14.733, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.933, 0, 2, 1.667, 0, 2, 2.9, 0, 2, 3.833, 0, 2, 4.5, 0, 2, 5.333, 0, 2, 5.933, 0, 2, 7.367, 0, 2, 10.333, 0, 2, 11.133, 0, 2, 11.333, 0, 0, 11.6, 0.7, 0, 12.667, 0.664, 2, 12.967, 0.664, 2, 13.433, 0.664, 2, 13.667, 0.664, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param8", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, 0.4, 0, 0.933, -0.297, 2, 1.667, -0.297, 2, 2.9, -0.297, 2, 3.833, -0.297, 0, 4.5, -0.294, 2, 5.333, -0.294, 2, 5.933, -0.294, 2, 7.367, -0.294, 2, 10.333, -0.294, 2, 11.133, -0.294, 0, 11.6, -0.282, 2, 12.667, -0.282, 2, 12.967, -0.282, 2, 13.433, -0.282, 2, 13.667, -0.282, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param23", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, 0.694, 0, 0.933, -1.08, 2, 1.667, -1.08, 2, 2.9, -1.08, 2, 3.833, -1.08, 0, 4.5, -1.069, 2, 5.333, -1.069, 2, 5.933, -1.069, 2, 7.367, -1.069, 2, 10.333, -1.069, 2, 11.133, -1.069, 0, 11.6, -1.027, 2, 12.667, -1.027, 2, 12.967, -1.027, 2, 13.433, -1.027, 2, 13.667, -1.027, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param25", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, 0.469, 0, 0.933, -0.609, 2, 1.667, -0.609, 2, 2.9, -0.609, 2, 3.833, -0.609, 0, 4.5, -0.603, 2, 5.333, -0.603, 2, 5.933, -0.603, 2, 7.367, -0.603, 2, 10.333, -0.603, 2, 11.133, -0.603, 0, 11.6, -0.579, 2, 12.667, -0.579, 2, 12.967, -0.579, 2, 13.433, -0.579, 2, 13.667, -0.579, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param27", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, 1.279, 0, 0.933, -2.28, 2, 1.667, -2.28, 2, 2.9, -2.28, 2, 3.833, -2.28, 0, 4.5, -2.257, 2, 5.333, -2.257, 2, 5.933, -2.257, 2, 7.367, -2.257, 2, 10.333, -2.257, 2, 11.133, -2.257, 0, 11.6, -2.167, 2, 12.667, -2.167, 2, 12.967, -2.167, 2, 13.433, -2.167, 2, 13.667, -2.167, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param201", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.667, 0, 0, 0.7, 1, 0, 0.933, 0.999, 2, 1.667, 0.999, 2, 2.9, 0.999, 2, 3.833, 0.999, 2, 4.5, 0.999, 2, 11.133, 0.999, 2, 11.167, 0.999, 0, 11.2, 1, 2, 12.667, 1, 2, 12.967, 1, 2, 13.433, 1, 2, 14, 1, 0, 14.033, 0.964, 2, 14.267, 0.964, 0, 14.3, 0, 2, 14.733, 0]}, {"Target": "Parameter", "Id": "Param209", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.667, 0, 0, 0.7, 1, 0, 0.933, 0.999, 2, 1.667, 0.999, 2, 2.9, 0.999, 2, 3.833, 0.999, 2, 4.5, 0.999, 2, 11.133, 0.999, 2, 11.167, 0.999, 0, 11.2, 1, 2, 12.667, 1, 2, 12.967, 1, 2, 13.433, 1, 2, 14, 1, 0, 14.033, 0.964, 2, 14.267, 0.964, 0, 14.3, 0, 2, 14.733, 0]}, {"Target": "Parameter", "Id": "Param214", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.667, 0, 2, 0.7, 0, 2, 0.933, 0, 2, 1.667, 0, 2, 2.9, 0, 2, 3.833, 0, 2, 4.5, 0, 2, 11.133, 0, 2, 11.167, 0, 2, 11.2, 0, 2, 12.667, 0, 2, 12.967, 0, 2, 13.433, 0, 2, 14, 0, 0, 14.033, 0.964, 2, 14.267, 0.964, 0, 14.3, 0, 2, 14.733, 0]}, {"Target": "Parameter", "Id": "Param216", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.667, 0, 0, 0.7, 1, 2, 0.933, 1, 2, 1.667, 1, 2, 2.9, 1, 2, 3.833, 1, 2, 4.5, 1, 2, 11.133, 1, 2, 11.167, 1, 0, 11.2, 0, 2, 12.667, 0, 2, 12.967, 0, 2, 13.433, 0, 2, 14, 0, 2, 14.033, 0, 2, 14.267, 0, 2, 14.3, 0, 2, 14.733, 0]}, {"Target": "Parameter", "Id": "Param217", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.667, 0, 2, 0.933, 0, 2, 1.667, 0, 2, 2.9, 0, 2, 3.833, 0, 2, 4.5, 0, 2, 11.133, 0, 2, 11.167, 0, 0, 11.2, 1, 2, 12.667, 1, 2, 12.967, 1, 2, 13.433, 1, 2, 14, 1, 0, 14.033, 0, 2, 14.267, 0, 2, 14.3, 0, 2, 14.733, 0]}, {"Target": "Parameter", "Id": "Param9", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, 0.48, 0, 0.767, -12.9, 0, 0.967, -6.143, 0, 1.7, -6.503, 0, 2.167, -5.967, 0, 2.933, -6.143, 2, 3.867, -6.143, 0, 4.233, -3.966, 0, 4.533, -4.602, 2, 5.367, -4.602, 0, 6.533, -2.262, 1, 6.9, -2.262, 7.266, -3.164, 7.633, -4.062, 1, 7.844, -4.579, 8.056, -4.602, 8.267, -4.602, 0, 8.833, -4.062, 0, 9.333, -4.602, 0, 10.367, -4.062, 0, 11.167, -4.602, 0, 11.633, -3.019, 2, 12.667, -3.019, 2, 12.967, -3.019, 2, 13.433, -3.019, 2, 13.8, -3.019, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param10", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, -0.48, 0, 0.933, 24.3, 2, 1.667, 24.3, 2, 2.9, 24.3, 2, 3.833, 24.3, 0, 4.5, 24.055, 2, 5.333, 24.055, 2, 6.5, 24.055, 2, 7.6, 24.055, 2, 8.233, 24.055, 2, 8.8, 24.055, 2, 9.3, 24.055, 2, 10.333, 24.055, 2, 11.133, 24.055, 0, 11.6, 23.099, 2, 12.667, 23.099, 2, 12.967, 23.099, 2, 13.433, 23.099, 2, 13.8, 23.099, 1, 13.889, 23.099, 13.978, 18.789, 14.067, 14.86, 1, 14.1, 13.387, 14.134, 12.831, 14.167, 12.202, 1, 14.222, 11.154, 14.278, 11.13, 14.333, 9.508, 1, 14.466, 5.615, 14.6, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param11", "Segments": [0, 0, 2, 0.333, 0, 1, 0.366, 0, 0.4, -0.585, 0.433, 0.96, 1, 0.6, 8.687, 0.766, 18.78, 0.933, 18.78, 2, 1.667, 18.78, 2, 2.9, 18.78, 2, 3.833, 18.78, 0, 4.5, 18.59, 2, 5.333, 18.59, 2, 6.5, 18.59, 2, 7.6, 18.59, 2, 8.233, 18.59, 2, 8.8, 18.59, 2, 9.3, 18.59, 2, 10.333, 18.59, 2, 11.133, 18.59, 0, 11.6, 17.852, 2, 12.667, 17.852, 2, 12.967, 17.852, 2, 13.433, 17.852, 2, 13.8, 17.852, 0, 14.333, 30, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param12", "Segments": [0, 0, 2, 0.333, 0, 1, 0.366, 0, 0.4, -15.028, 0.433, -17, 1, 0.6, -26.858, 0.766, -30, 0.933, -30, 2, 1.667, -30, 2, 2.9, -30, 2, 3.833, -30, 0, 4.5, -29.697, 2, 5.333, -29.697, 2, 6.5, -29.697, 2, 7.6, -29.697, 2, 8.233, -29.697, 2, 8.8, -29.697, 2, 9.3, -29.697, 2, 10.333, -29.697, 2, 11.133, -29.697, 0, 11.6, -28.517, 2, 12.667, -28.517, 2, 12.967, -28.517, 2, 13.433, -28.517, 2, 13.8, -28.517, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param22", "Segments": [0, 0, 2, 0.333, 0, 0, 0.367, 1, 2, 0.433, 1, 2, 0.933, 1, 2, 1.667, 1, 2, 2.9, 1, 2, 3.833, 1, 2, 4.5, 1, 2, 5.333, 1, 2, 6.5, 1, 2, 7.6, 1, 2, 8.233, 1, 2, 8.8, 1, 2, 9.3, 1, 2, 10.333, 1, 2, 11.133, 1, 2, 11.6, 1, 2, 12.667, 1, 2, 12.967, 1, 2, 13.433, 1, 2, 14.667, 1, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param13", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 0, 0.933, 10.56, 2, 1.667, 10.56, 2, 2.9, 10.56, 2, 3.833, 10.56, 0, 4.5, 10.453, 2, 5.333, 10.453, 2, 6.5, 10.453, 2, 7.6, 10.453, 2, 8.233, 10.453, 2, 8.8, 10.453, 2, 9.3, 10.453, 2, 10.333, 10.453, 2, 11.133, 10.453, 0, 11.6, 10.038, 2, 12.667, 10.038, 2, 12.967, 10.038, 2, 13.433, 10.038, 2, 13.8, 10.038, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param14", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 1, 0.511, 0, 0.589, -16.595, 0.667, -16.824, 1, 0.756, -17.086, 0.844, -17.04, 0.933, -17.04, 2, 1.667, -17.04, 2, 2.9, -17.04, 2, 3.833, -17.04, 0, 4.5, -16.868, 2, 5.333, -16.868, 2, 6.5, -16.868, 2, 7.6, -16.868, 2, 8.233, -16.868, 2, 8.8, -16.868, 2, 9.3, -16.868, 2, 10.333, -16.868, 2, 11.133, -16.868, 0, 11.6, -16.198, 2, 12.667, -16.198, 2, 12.967, -16.198, 2, 13.433, -16.198, 2, 13.8, -16.198, 1, 13.978, -16.198, 14.155, -16.214, 14.333, -13.56, 1, 14.466, -11.569, 14.6, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param210", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, 30, 1, 0.511, 30, 0.589, 22.672, 0.667, 13.208, 1, 0.756, 2.392, 0.844, -1.14, 0.933, -1.14, 0, 1.1, 0.36, 0, 1.667, -0.06, 0, 1.967, 0.3, 0, 2.9, -0.06, 2, 3.833, -0.06, 0, 4.2, -1.02, 0, 4.5, 0.241, 1, 4.778, 0.241, 5.055, 0.022, 5.333, -0.059, 1, 5.722, -0.173, 6.111, -0.179, 6.5, -0.179, 0, 7.6, -0.059, 2, 8.233, -0.059, 2, 8.8, -0.059, 2, 9.3, -0.059, 2, 10.333, -0.059, 2, 11.133, -0.059, 0, 11.6, -12.117, 0, 11.8, -6.117, 0, 12.033, -12.117, 0, 12.233, -6.117, 0, 12.467, -12.117, 0, 12.7, -6.117, 0, 12.967, -12.117, 0, 13.433, -6.117, 0, 13.8, -7.497, 1, 13.978, -7.497, 14.155, -0.834, 14.333, 14.769, 1, 14.4, 20.62, 14.466, 24.976, 14.533, 24.976, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param211", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 1, 0.6, 0, 0.766, 0.254, 0.933, 1.45, 1, 0.989, 1.849, 1.044, 3.79, 1.1, 3.79, 0, 1.667, 3.19, 0, 1.967, 4.229, 0, 2.433, 3.835, 0, 2.9, 4.929, 2, 3.833, 4.929, 0, 4.2, 7.479, 0, 4.433, 2.36, 1, 4.455, 2.36, 4.478, 2.399, 4.5, 2.549, 1, 4.589, 3.146, 4.678, 4.241, 4.767, 4.429, 1, 4.956, 4.828, 5.144, 4.88, 5.333, 4.88, 2, 6.5, 4.88, 2, 7.6, 4.88, 2, 8.233, 4.88, 2, 8.8, 4.88, 2, 9.3, 4.88, 2, 10.333, 4.88, 2, 11.133, 4.88, 1, 11.289, 4.88, 11.444, 4.868, 11.6, 4.686, 1, 11.667, 4.608, 11.733, 0.606, 11.8, 0.606, 0, 11.933, 6.735, 0, 12.133, 0.606, 0, 12.367, 6.735, 0, 12.6, 0.606, 0, 12.833, 6.735, 1, 13.033, 6.735, 13.233, 5.774, 13.433, 4.686, 1, 13.555, 4.021, 13.678, 3.573, 13.8, 3.186, 1, 13.989, 2.588, 14.178, 2.334, 14.367, 1.62, 1, 14.489, 1.158, 14.611, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param221", "Segments": [0, 0, 2, 0.333, 0, 2, 0.667, 0, 0, 0.7, 1, 2, 1.667, 1, 2, 2.9, 1, 2, 4.3, 1, 2, 4.333, 1, 2, 5.333, 1, 2, 6.5, 1, 2, 7.6, 1, 2, 8.233, 1, 2, 8.8, 1, 2, 9.3, 1, 2, 10.333, 1, 2, 11.133, 1, 2, 11.2, 1, 2, 11.233, 1, 2, 12.667, 1, 2, 12.967, 1, 2, 13.433, 1, 2, 13.667, 1, 2, 13.9, 1, 0, 13.933, 0, 2, 14.733, 0]}, {"Target": "Parameter", "Id": "Param223", "Segments": [0, 0, 2, 0.333, 0, 2, 0.667, 0, 0, 0.7, 1, 2, 1.667, 1, 2, 2.9, 1, 2, 4.3, 1, 2, 4.333, 1, 2, 5.333, 1, 2, 6.5, 1, 2, 7.6, 1, 2, 8.233, 1, 2, 8.8, 1, 2, 9.3, 1, 2, 10.333, 1, 2, 11.133, 1, 2, 11.2, 1, 2, 11.233, 1, 2, 12.667, 1, 2, 12.967, 1, 2, 13.433, 1, 2, 13.667, 1, 2, 13.9, 1, 0, 13.933, 0, 2, 14.733, 0]}, {"Target": "Parameter", "Id": "Param237", "Segments": [0, 0, 2, 0.333, 0, 2, 0.667, 0, 2, 0.7, 0, 2, 1.667, 0, 2, 2.9, 0, 2, 4.3, 0, 0, 4.333, 1, 2, 5.333, 1, 2, 6.5, 1, 2, 7.6, 1, 2, 8.233, 1, 2, 8.8, 1, 2, 9.3, 1, 2, 10.333, 1, 2, 11.133, 1, 2, 11.2, 1, 0, 11.233, 0, 2, 12.667, 0, 2, 12.967, 0, 2, 13.433, 0, 2, 13.667, 0, 2, 13.9, 0, 2, 13.933, 0, 2, 14.733, 0]}, {"Target": "Parameter", "Id": "Param243", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 0, 0.7, 1, 2, 1.667, 1, 2, 2.9, 1, 2, 4.3, 1, 0, 4.333, 0, 2, 5.333, 0, 2, 6.5, 0, 2, 7.6, 0, 2, 8.233, 0, 2, 8.8, 0, 2, 9.3, 0, 2, 10.333, 0, 2, 11.133, 0, 2, 11.2, 0, 0, 11.233, 1, 2, 12.667, 1, 2, 12.967, 1, 2, 13.433, 1, 2, 13.667, 1, 2, 13.9, 1, 0, 13.933, 0, 2, 14.733, 0]}, {"Target": "Parameter", "Id": "Param15", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, -2.46, 1, 0.533, -2.46, 0.633, 1.788, 0.733, 11.113, 1, 0.8, 17.33, 0.866, 20.892, 0.933, 20.892, 0, 1.533, 19.932, 0, 2.033, 20.892, 0, 2.467, 19.692, 0, 2.967, 20.892, 2, 3.833, 20.892, 0, 4, 22.005, 0, 4.5, 9.461, 2, 5.333, 9.461, 0, 6.5, 5.981, 1, 6.867, 5.981, 7.233, 6.67, 7.6, 8.141, 1, 7.811, 8.988, 8.022, 9.461, 8.233, 9.461, 0, 8.8, 8.141, 0, 9.3, 9.461, 0, 10.333, 8.141, 0, 11.133, 9.461, 0, 11.6, 6.925, 2, 12.667, 6.925, 2, 12.967, 6.925, 2, 13.433, 6.925, 2, 13.667, 6.925, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param16", "Segments": [0, 0, 2, 0.333, 0, 1, 0.366, 0, 0.4, 2.554, 0.433, 3.54, 1, 0.511, 5.84, 0.589, 7.381, 0.667, 9.208, 1, 0.756, 11.296, 0.844, 14.336, 0.933, 15.3, 1, 1.178, 17.95, 1.422, 18.66, 1.667, 18.66, 2, 2.9, 18.66, 2, 3.833, 18.66, 0, 4.5, 18.471, 2, 5.333, 18.471, 2, 6.5, 18.471, 2, 7.6, 18.471, 2, 8.233, 18.471, 2, 8.8, 18.471, 2, 9.3, 18.471, 2, 10.333, 18.471, 2, 11.133, 18.471, 0, 11.6, 17.738, 2, 12.667, 17.738, 2, 12.967, 17.738, 2, 13.433, 17.738, 2, 13.667, 17.738, 1, 13.756, 17.738, 13.844, 11.891, 13.933, 9.446, 1, 14.2, 2.111, 14.466, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param17", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, -7.32, 1, 0.511, -7.32, 0.589, 4.493, 0.667, 12.584, 1, 0.756, 21.831, 0.844, 23.16, 0.933, 23.16, 2, 1.667, 23.16, 2, 2.9, 23.16, 2, 3.833, 23.16, 0, 4.5, 22.926, 2, 5.333, 22.926, 2, 6.5, 22.926, 2, 7.6, 22.926, 2, 8.233, 22.926, 2, 8.8, 22.926, 2, 9.3, 22.926, 2, 10.333, 22.926, 2, 11.133, 22.926, 0, 11.6, 22.015, 2, 12.667, 22.015, 2, 12.967, 22.015, 2, 13.433, 22.015, 2, 13.667, 22.015, 1, 13.756, 22.015, 13.844, 6.481, 13.933, 5.075, 1, 14.2, 0.858, 14.466, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param19", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 0, 0.933, -28.44, 2, 1.667, -28.44, 2, 2.9, -28.44, 2, 3.833, -28.44, 0, 4.5, -28.153, 2, 5.333, -28.153, 2, 6.5, -28.153, 2, 7.6, -28.153, 2, 8.233, -28.153, 2, 8.8, -28.153, 2, 9.3, -28.153, 2, 10.333, -28.153, 2, 11.133, -28.153, 0, 11.6, -27.034, 2, 12.667, -27.034, 2, 12.967, -27.034, 2, 13.433, -27.034, 2, 13.667, -27.034, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param233", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, 8.729, 0, 0.933, 4.32, 0, 1.533, 7.38, 0, 2, 4.32, 0, 2.467, 7.38, 0, 2.967, 3.323, 1, 3.256, 3.323, 3.544, 3.65, 3.833, 4.32, 1, 3.955, 4.603, 4.078, 4.753, 4.2, 4.753, 0, 4.5, -1.604, 0, 4.833, -1.085, 0, 5.333, -1.124, 2, 6.5, -1.124, 0, 7.6, -1.536, 2, 8.233, -1.536, 2, 8.8, -1.536, 2, 9.3, -1.536, 2, 10.333, -1.536, 2, 11.133, -1.536, 0, 11.6, 5.281, 0, 11.767, 2.041, 0, 12, 5.281, 0, 12.2, 2.041, 0, 12.4, 5.281, 0, 12.667, 2.041, 0, 12.967, 5.281, 1, 13.045, 5.281, 13.122, 2.921, 13.2, 2.041, 1, 13.278, 1.161, 13.355, 1.193, 13.433, 1.193, 1, 13.555, 1.193, 13.678, 2.28, 13.8, 4.32, 1, 13.933, 6.546, 14.067, 8.351, 14.2, 10.869, 1, 14.256, 11.918, 14.311, 13.448, 14.367, 13.448, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param234", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 0, 0.733, 5.797, 1, 0.8, 5.797, 0.866, 6.056, 0.933, 4.32, 1, 1.022, 2.005, 1.111, -7.2, 1.2, -7.2, 0, 1.667, 6.48, 0, 2.167, -0.117, 0, 2.6, 7.799, 0, 3.267, -0.117, 1, 3.456, -0.117, 3.644, 0.822, 3.833, 4.32, 1, 3.889, 5.349, 3.944, 9.54, 4, 9.54, 1, 4.167, 9.54, 4.333, 9.425, 4.5, 7.349, 1, 4.633, 5.688, 4.767, 1.667, 4.9, 1.667, 2, 5.333, 1.667, 0, 6.5, 7.576, 1, 6.867, 7.576, 7.233, 6.235, 7.6, 5.401, 1, 7.811, 4.921, 8.022, 4.986, 8.233, 4.986, 0, 8.8, 5.401, 0, 9.3, 4.986, 0, 10.333, 5.401, 1, 10.6, 5.401, 10.866, 4.492, 11.133, 4.276, 1, 11.289, 4.151, 11.444, 4.183, 11.6, 4.106, 1, 11.656, 4.079, 11.711, 0.281, 11.767, 0.281, 0, 12.1, 6.469, 0, 12.3, 0.281, 0, 12.5, 6.469, 0, 12.767, 0.281, 0, 13.067, 6.469, 0, 13.433, 4.106, 2, 13.667, 4.106, 0, 13.933, -16.8, 1, 14.022, -16.8, 14.111, -17.3, 14.2, -15.002, 1, 14.378, -10.406, 14.555, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param235", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 0, 0.933, -9.6, 2, 1.667, -9.6, 2, 2.9, -9.6, 2, 3.833, -9.6, 0, 4.5, -9.503, 2, 5.333, -9.503, 2, 6.5, -9.503, 2, 7.6, -9.503, 2, 8.233, -9.503, 2, 8.8, -9.503, 2, 9.3, -9.503, 2, 10.333, -9.503, 2, 11.133, -9.503, 0, 11.6, -9.125, 2, 12.667, -9.125, 2, 12.967, -9.125, 2, 13.433, -9.125, 2, 13.667, -9.125, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param277", "Segments": [0, 0, 2, 0.333, 0, 0, 0.6, 12.46, 0, 1.133, -9.549, 0, 1.567, 12.46, 0, 1.967, -9.549, 0, 2.4, 12.46, 0, 2.8, -9.549, 0, 3.233, 12.46, 0, 3.633, -9.549, 0, 4.067, 12.46, 0, 4.467, -9.549, 0, 4.9, 12.46, 0, 5.3, -9.549, 0, 5.733, 12.46, 0, 6.133, -9.549, 0, 6.567, 12.46, 0, 6.967, -9.549, 0, 7.4, 12.46, 0, 7.8, -9.549, 0, 8.233, 12.46, 0, 8.633, -9.549, 1, 8.778, -9.549, 8.922, -7.835, 9.067, 0, 1, 9.156, 4.822, 9.244, 12.46, 9.333, 12.46, 0, 9.867, -9.549, 0, 10.3, 12.46, 0, 10.7, -9.549, 0, 11.133, 12.46, 0, 11.533, -9.549, 0, 11.967, 12.46, 0, 12.367, -9.549, 0, 12.8, 12.46, 0, 13.2, -9.549, 0, 13.633, 12.46, 0, 14.033, -9.549, 0, 14.467, 12.46, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param278", "Segments": [0, 0, 2, 0.333, 0, 0, 0.6, 0.25, 0, 1.133, -0.17, 0, 1.567, 0.25, 0, 1.967, -0.17, 0, 2.4, 0.25, 0, 2.8, -0.17, 0, 3.233, 0.25, 0, 3.633, -0.17, 0, 4.067, 0.25, 0, 4.467, -0.17, 0, 4.9, 0.25, 0, 5.3, -0.17, 0, 5.733, 0.25, 0, 6.133, -0.17, 0, 6.567, 0.25, 0, 6.967, -0.17, 0, 7.4, 0.25, 0, 7.8, -0.17, 0, 8.233, 0.25, 0, 8.633, -0.17, 1, 8.778, -0.17, 8.922, -0.146, 9.067, 0, 1, 9.156, 0.09, 9.244, 0.25, 9.333, 0.25, 0, 9.867, -0.17, 0, 10.3, 0.25, 0, 10.7, -0.17, 0, 11.133, 0.25, 0, 11.533, -0.17, 0, 11.967, 0.25, 0, 12.367, -0.17, 0, 12.8, 0.25, 0, 13.2, -0.17, 0, 13.633, 0.25, 0, 14.033, -0.17, 0, 14.467, 0.25, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param280", "Segments": [0, 0, 2, 0.333, 0, 0, 0.6, 0.49, 0, 1.133, -0.407, 0, 1.567, 0.49, 0, 1.967, -0.407, 0, 2.4, 0.49, 0, 2.8, -0.407, 0, 3.233, 0.49, 0, 3.633, -0.407, 0, 4.067, 0.49, 0, 4.467, -0.407, 0, 4.9, 0.49, 0, 5.3, -0.407, 0, 5.733, 0.49, 0, 6.133, -0.407, 0, 6.567, 0.49, 0, 6.967, -0.407, 0, 7.4, 0.49, 0, 7.8, -0.407, 0, 8.233, 0.49, 0, 8.633, -0.407, 1, 8.778, -0.407, 8.922, -0.334, 9.067, 0, 1, 9.156, 0.205, 9.244, 0.49, 9.333, 0.49, 0, 9.867, -0.407, 0, 10.3, 0.49, 0, 10.7, -0.407, 0, 11.133, 0.49, 0, 11.533, -0.407, 0, 11.967, 0.49, 0, 12.367, -0.407, 0, 12.8, 0.49, 0, 13.2, -0.407, 0, 13.633, 0.49, 0, 14.033, -0.407, 0, 14.467, 0.49, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param7", "Segments": [0, 0, 2, 0.333, 0, 0, 1.533, -9, 0, 2.733, 0, 0, 3.933, -9, 0, 5.133, 0, 0, 6.333, -9, 0, 7.533, 0, 0, 8.733, -9, 0, 9.933, 0, 0, 11.133, -9, 0, 12.333, 0, 0, 13.533, -9, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param66", "Segments": [0, 0, 2, 0.333, 0, 0, 1.533, -19, 0, 2.733, 0, 0, 3.933, -19, 0, 5.133, 0, 0, 6.333, -19, 0, 7.533, 0, 0, 8.733, -19, 0, 9.933, 0, 0, 11.133, -19, 0, 12.333, 0, 0, 13.533, -19, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param67", "Segments": [0, 0, 2, 0.333, 0, 0, 0.933, 16, 0, 2.133, -16, 1, 2.333, -16, 2.533, -10.667, 2.733, 0, 1, 2.933, 10.667, 3.133, 16, 3.333, 16, 0, 4.533, -16, 1, 4.733, -16, 4.933, -10.667, 5.133, 0, 1, 5.333, 10.667, 5.533, 16, 5.733, 16, 0, 6.933, -16, 1, 7.133, -16, 7.333, -10.667, 7.533, 0, 1, 7.733, 10.667, 7.933, 16, 8.133, 16, 0, 9.333, -16, 1, 9.533, -16, 9.733, -10.667, 9.933, 0, 1, 10.133, 10.667, 10.333, 16, 10.533, 16, 0, 11.733, -16, 1, 11.933, -16, 12.133, -10.667, 12.333, 0, 1, 12.533, 10.667, 12.733, 16, 12.933, 16, 0, 14.133, -16, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param68", "Segments": [0, 0, 2, 0.333, 0, 0, 1.533, 17, 0, 2.733, 0, 0, 3.933, 17, 0, 5.133, 0, 0, 6.333, 17, 0, 7.533, 0, 0, 8.733, 17, 0, 9.933, 0, 0, 11.133, 17, 0, 12.333, 0, 0, 13.533, 17, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param69", "Segments": [0, 0, 2, 0.333, 0, 0, 0.933, -16, 0, 2.133, 16, 1, 2.333, 16, 2.533, 10.667, 2.733, 0, 1, 2.933, -10.667, 3.133, -16, 3.333, -16, 0, 4.533, 16, 1, 4.733, 16, 4.933, 10.667, 5.133, 0, 1, 5.333, -10.667, 5.533, -16, 5.733, -16, 0, 6.933, 16, 1, 7.133, 16, 7.333, 10.667, 7.533, 0, 1, 7.733, -10.667, 7.933, -16, 8.133, -16, 0, 9.333, 16, 1, 9.533, 16, 9.733, 10.667, 9.933, 0, 1, 10.133, -10.667, 10.333, -16, 10.533, -16, 0, 11.733, 16, 1, 11.933, 16, 12.133, 10.667, 12.333, 0, 1, 12.533, -10.667, 12.733, -16, 12.933, -16, 0, 14.133, 16, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param35", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.633, 2.508, 0, 0.9, -8.186, 0, 1.167, 2.65, 0, 1.5, 0.053, 0, 1.6, 0.14, 0, 1.767, -0.203, 0, 2.033, 1.963, 0, 2.5, -2.195, 0, 3.067, 1.57, 0, 3.833, -7.618, 0, 4.167, 9.256, 0, 4.6, -8.156, 0, 4.933, 4.752, 0, 5.233, -0.771, 0, 5.533, 1.413, 0, 5.833, -3.911, 0, 6.3, 1.911, 0, 6.567, 0.438, 0, 6.833, 1.889, 0, 7.5, -3.211, 0, 7.933, 3.889, 0, 8.467, -6.524, 0, 8.8, 7.978, 0, 9.133, -3.248, 0, 9.4, 0.407, 0, 9.667, -2.613, 0, 10.033, 2.969, 0, 10.4, -0.379, 0, 10.633, -0.115, 0, 11.167, -4.024, 0, 11.4, 2.152, 0, 11.567, -4.636, 0, 11.8, 6.506, 0, 12.167, -5.103, 0, 12.433, 2.9, 0, 12.7, -3.127, 0, 13.1, 2.449, 0, 13.5, 0.314, 0, 13.667, 0.458, 0, 13.7, 0.451, 0, 13.9, 1.823, 0, 14.267, -3.342, 0, 14.633, 2.736, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param36", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.433, -0.639, 0, 0.5, -0.345, 0, 0.6, -0.753, 0, 0.833, 5.417, 0, 1.067, -7.916, 0, 1.333, 4.948, 0, 1.633, -2.073, 0, 1.833, 0.035, 0, 1.967, -0.273, 0, 2.233, 1.221, 0, 2.633, -1.129, 0, 3.167, 0.55, 0, 3.467, -0.175, 0, 3.8, 3.766, 0, 4, -8.259, 0, 4.3, 7.103, 0, 4.733, -6.977, 0, 5.067, 6.14, 0, 5.367, -3.45, 0, 5.7, 3.481, 0, 6, -3.194, 0, 6.367, 1.808, 0, 6.7, -1.476, 0, 7, 1.188, 0, 7.267, -0.117, 0, 7.433, 0.482, 0, 7.733, -2.115, 0, 8.067, 2.569, 0, 8.333, 0.095, 0, 8.4, 0.571, 0, 8.7, -5.601, 0, 8.967, 7.932, 0, 9.267, -5.83, 0, 9.567, 3.809, 0, 9.867, -3.269, 0, 10.167, 3.04, 0, 10.467, -1.569, 0, 10.8, 0.723, 0, 11, -0.111, 0, 11.133, 1.497, 0, 11.367, -4.025, 0, 11.533, 5.312, 0, 11.733, -6.394, 0, 11.967, 6.467, 0, 12.3, -6.502, 0, 12.567, 5.944, 0, 12.867, -4.325, 0, 13.2, 2.72, 0, 13.533, -1.176, 0, 13.733, 0.093, 0, 13.867, -0.242, 0, 14.133, 1.732, 0, 14.433, -2.603, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param37", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -0.577, 0, 0.667, 1.426, 0, 0.933, -2.567, 0, 1.167, -0.431, 0, 1.3, -0.773, 0, 1.633, 0.523, 0, 1.767, 0.36, 0, 2.033, 2.194, 0, 2.533, -2.689, 0, 3.1, 2.138, 0, 3.833, -9.185, 0, 4.1, 9.469, 0, 4.567, -5.53, 0, 4.933, 4.423, 0, 5.233, -0.599, 0, 5.467, 0.278, 0, 5.833, -2.912, 0, 6.267, 2.4, 0, 6.567, 0.629, 0, 6.767, 0.909, 0, 7.467, -3.989, 0, 7.9, 4.946, 0, 8.467, -7.445, 0, 8.8, 7.428, 0, 9.133, -0.861, 0, 9.3, 0.189, 0, 9.667, -4.903, 0, 10.033, 4.348, 0, 10.333, -0.069, 0, 10.6, 1.069, 0, 11.2, -6.614, 0, 11.5, 6.742, 0, 11.8, -2.097, 0, 12, 5.392, 0, 12.233, -10.527, 0, 12.467, 6.819, 0, 12.7, -6.803, 0, 13.033, 3.888, 0, 13.533, 0.494, 0, 13.667, 0.632, 0, 14, -2.069, 0, 14.333, 3.784, 0, 14.633, -4.724, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param39", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, 0.577, 0, 0.633, -1.095, 0, 0.867, 2.542, 0, 1.1, -2.339, 0, 1.333, 1.048, 0, 1.567, -0.483, 0, 1.767, 0.254, 0, 1.967, -0.692, 0, 2.233, 1.353, 0, 2.7, -1.181, 0, 3.2, 0.676, 0, 3.467, -0.023, 0, 3.8, 4.233, 0, 4, -10.519, 0, 4.267, 9.869, 0, 4.6, -4.493, 0, 5.067, 4.058, 0, 5.367, -2.557, 0, 5.7, 1.982, 0, 6, -2.206, 0, 6.367, 1.893, 0, 6.667, -1.075, 0, 7, 0.721, 0, 7.233, -0.059, 0, 7.4, 1.026, 0, 7.7, -2.825, 0, 8.033, 3.197, 0, 8.3, -0.346, 0, 8.433, 0.823, 0, 8.7, -6.337, 0, 8.967, 7.055, 0, 9.233, -4.354, 0, 9.567, 3.46, 0, 9.867, -4.208, 0, 10.167, 4.375, 0, 10.467, -2.61, 0, 10.767, 1.495, 0, 11, -0.262, 0, 11.167, 2.183, 0, 11.4, -6.553, 0, 11.667, 6.925, 0, 11.933, -7.642, 0, 12.167, 10.191, 0, 12.4, -12.345, 0, 12.633, 11.211, 0, 12.9, -8.601, 0, 13.167, 4.913, 0, 13.5, -1.9, 0, 13.867, 1.546, 0, 14.233, -2.502, 0, 14.533, 4.904, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param38", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -0.577, 0, 0.667, 1.426, 0, 0.933, -2.567, 0, 1.167, -0.431, 0, 1.3, -0.773, 0, 1.633, 0.523, 0, 1.767, 0.36, 0, 2.033, 2.194, 0, 2.533, -2.689, 0, 3.1, 2.138, 0, 3.833, -9.185, 0, 4.1, 9.469, 0, 4.567, -5.53, 0, 4.933, 4.423, 0, 5.233, -0.599, 0, 5.467, 0.278, 0, 5.833, -2.912, 0, 6.267, 2.4, 0, 6.567, 0.629, 0, 6.767, 0.909, 0, 7.467, -3.989, 0, 7.9, 4.946, 0, 8.467, -7.445, 0, 8.8, 7.428, 0, 9.133, -0.861, 0, 9.3, 0.189, 0, 9.667, -4.903, 0, 10.033, 4.348, 0, 10.333, -0.069, 0, 10.6, 1.069, 0, 11.2, -6.614, 0, 11.5, 6.742, 0, 11.8, -2.097, 0, 12, 5.392, 0, 12.233, -10.527, 0, 12.467, 6.819, 0, 12.7, -6.803, 0, 13.033, 3.888, 0, 13.533, 0.494, 0, 13.667, 0.632, 0, 14, -2.069, 0, 14.333, 3.784, 0, 14.633, -4.724, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param40", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, 0.577, 0, 0.633, -1.095, 0, 0.867, 2.542, 0, 1.1, -2.339, 0, 1.333, 1.048, 0, 1.567, -0.483, 0, 1.767, 0.254, 0, 1.967, -0.692, 0, 2.233, 1.353, 0, 2.7, -1.181, 0, 3.2, 0.676, 0, 3.467, -0.023, 0, 3.8, 4.233, 0, 4, -10.519, 0, 4.267, 9.869, 0, 4.6, -4.493, 0, 5.067, 4.058, 0, 5.367, -2.557, 0, 5.7, 1.982, 0, 6, -2.206, 0, 6.367, 1.893, 0, 6.667, -1.075, 0, 7, 0.721, 0, 7.233, -0.059, 0, 7.4, 1.026, 0, 7.7, -2.825, 0, 8.033, 3.197, 0, 8.3, -0.346, 0, 8.433, 0.823, 0, 8.7, -6.337, 0, 8.967, 7.055, 0, 9.233, -4.354, 0, 9.567, 3.46, 0, 9.867, -4.208, 0, 10.167, 4.375, 0, 10.467, -2.61, 0, 10.767, 1.495, 0, 11, -0.262, 0, 11.167, 2.183, 0, 11.4, -6.553, 0, 11.667, 6.925, 0, 11.933, -7.642, 0, 12.167, 10.191, 0, 12.4, -12.345, 0, 12.633, 11.211, 0, 12.9, -8.601, 0, 13.167, 4.913, 0, 13.5, -1.9, 0, 13.867, 1.546, 0, 14.233, -2.502, 0, 14.533, 4.904, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param41", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -0.577, 0, 0.667, 1.426, 0, 0.933, -2.567, 0, 1.167, -0.431, 0, 1.3, -0.773, 0, 1.633, 0.523, 0, 1.767, 0.36, 0, 2.033, 2.194, 0, 2.533, -2.689, 0, 3.1, 2.138, 0, 3.833, -9.185, 0, 4.1, 9.469, 0, 4.567, -5.53, 0, 4.933, 4.423, 0, 5.233, -0.599, 0, 5.467, 0.278, 0, 5.833, -2.912, 0, 6.267, 2.4, 0, 6.567, 0.629, 0, 6.767, 0.909, 0, 7.467, -3.989, 0, 7.9, 4.946, 0, 8.467, -7.445, 0, 8.8, 7.428, 0, 9.133, -0.861, 0, 9.3, 0.189, 0, 9.667, -4.903, 0, 10.033, 4.348, 0, 10.333, -0.069, 0, 10.6, 1.069, 0, 11.2, -6.614, 0, 11.5, 6.742, 0, 11.8, -2.097, 0, 12, 5.392, 0, 12.233, -10.527, 0, 12.467, 6.819, 0, 12.7, -6.803, 0, 13.033, 3.888, 0, 13.533, 0.494, 0, 13.667, 0.632, 0, 14, -2.069, 0, 14.333, 3.784, 0, 14.633, -4.724, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param42", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, 0.577, 0, 0.633, -1.095, 0, 0.867, 2.542, 0, 1.1, -2.339, 0, 1.333, 1.048, 0, 1.567, -0.483, 0, 1.767, 0.254, 0, 1.967, -0.692, 0, 2.233, 1.353, 0, 2.7, -1.181, 0, 3.2, 0.676, 0, 3.467, -0.023, 0, 3.8, 4.233, 0, 4, -10.519, 0, 4.267, 9.869, 0, 4.6, -4.493, 0, 5.067, 4.058, 0, 5.367, -2.557, 0, 5.7, 1.982, 0, 6, -2.206, 0, 6.367, 1.893, 0, 6.667, -1.075, 0, 7, 0.721, 0, 7.233, -0.059, 0, 7.4, 1.026, 0, 7.7, -2.825, 0, 8.033, 3.197, 0, 8.3, -0.346, 0, 8.433, 0.823, 0, 8.7, -6.337, 0, 8.967, 7.055, 0, 9.233, -4.354, 0, 9.567, 3.46, 0, 9.867, -4.208, 0, 10.167, 4.375, 0, 10.467, -2.61, 0, 10.767, 1.495, 0, 11, -0.262, 0, 11.167, 2.183, 0, 11.4, -6.553, 0, 11.667, 6.925, 0, 11.933, -7.642, 0, 12.167, 10.191, 0, 12.4, -12.345, 0, 12.633, 11.211, 0, 12.9, -8.601, 0, 13.167, 4.913, 0, 13.5, -1.9, 0, 13.867, 1.546, 0, 14.233, -2.502, 0, 14.533, 4.904, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param43", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -0.577, 0, 0.667, 1.426, 0, 0.933, -2.567, 0, 1.167, -0.431, 0, 1.3, -0.773, 0, 1.633, 0.523, 0, 1.767, 0.36, 0, 2.033, 2.194, 0, 2.533, -2.689, 0, 3.1, 2.138, 0, 3.833, -9.185, 0, 4.1, 9.469, 0, 4.567, -5.53, 0, 4.933, 4.423, 0, 5.233, -0.599, 0, 5.467, 0.278, 0, 5.833, -2.912, 0, 6.267, 2.4, 0, 6.567, 0.629, 0, 6.767, 0.909, 0, 7.467, -3.989, 0, 7.9, 4.946, 0, 8.467, -7.445, 0, 8.8, 7.428, 0, 9.133, -0.861, 0, 9.3, 0.189, 0, 9.667, -4.903, 0, 10.033, 4.348, 0, 10.333, -0.069, 0, 10.6, 1.069, 0, 11.2, -6.614, 0, 11.5, 6.742, 0, 11.8, -2.097, 0, 12, 5.392, 0, 12.233, -10.527, 0, 12.467, 6.819, 0, 12.7, -6.803, 0, 13.033, 3.888, 0, 13.533, 0.494, 0, 13.667, 0.632, 0, 14, -2.069, 0, 14.333, 3.784, 0, 14.633, -4.724, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param44", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, 0.577, 0, 0.633, -1.095, 0, 0.867, 2.542, 0, 1.1, -2.339, 0, 1.333, 1.048, 0, 1.567, -0.483, 0, 1.767, 0.254, 0, 1.967, -0.692, 0, 2.233, 1.353, 0, 2.7, -1.181, 0, 3.2, 0.676, 0, 3.467, -0.023, 0, 3.8, 4.233, 0, 4, -10.519, 0, 4.267, 9.869, 0, 4.6, -4.493, 0, 5.067, 4.058, 0, 5.367, -2.557, 0, 5.7, 1.982, 0, 6, -2.206, 0, 6.367, 1.893, 0, 6.667, -1.075, 0, 7, 0.721, 0, 7.233, -0.059, 0, 7.4, 1.026, 0, 7.7, -2.825, 0, 8.033, 3.197, 0, 8.3, -0.346, 0, 8.433, 0.823, 0, 8.7, -6.337, 0, 8.967, 7.055, 0, 9.233, -4.354, 0, 9.567, 3.46, 0, 9.867, -4.208, 0, 10.167, 4.375, 0, 10.467, -2.61, 0, 10.767, 1.495, 0, 11, -0.262, 0, 11.167, 2.183, 0, 11.4, -6.553, 0, 11.667, 6.925, 0, 11.933, -7.642, 0, 12.167, 10.191, 0, 12.4, -12.345, 0, 12.633, 11.211, 0, 12.9, -8.601, 0, 13.167, 4.913, 0, 13.5, -1.9, 0, 13.867, 1.546, 0, 14.233, -2.502, 0, 14.533, 4.904, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param45", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -0.577, 0, 0.667, 1.426, 0, 0.933, -2.567, 0, 1.167, -0.431, 0, 1.3, -0.773, 0, 1.633, 0.523, 0, 1.767, 0.36, 0, 2.033, 2.194, 0, 2.533, -2.689, 0, 3.1, 2.138, 0, 3.833, -9.185, 0, 4.1, 9.469, 0, 4.567, -5.53, 0, 4.933, 4.423, 0, 5.233, -0.599, 0, 5.467, 0.278, 0, 5.833, -2.912, 0, 6.267, 2.4, 0, 6.567, 0.629, 0, 6.767, 0.909, 0, 7.467, -3.989, 0, 7.9, 4.946, 0, 8.467, -7.445, 0, 8.8, 7.428, 0, 9.133, -0.861, 0, 9.3, 0.189, 0, 9.667, -4.903, 0, 10.033, 4.348, 0, 10.333, -0.069, 0, 10.6, 1.069, 0, 11.2, -6.614, 0, 11.5, 6.742, 0, 11.8, -2.097, 0, 12, 5.392, 0, 12.233, -10.527, 0, 12.467, 6.819, 0, 12.7, -6.803, 0, 13.033, 3.888, 0, 13.533, 0.494, 0, 13.667, 0.632, 0, 14, -2.069, 0, 14.333, 3.784, 0, 14.633, -4.724, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param46", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, 0.577, 0, 0.633, -1.095, 0, 0.867, 2.542, 0, 1.1, -2.339, 0, 1.333, 1.048, 0, 1.567, -0.483, 0, 1.767, 0.254, 0, 1.967, -0.692, 0, 2.233, 1.353, 0, 2.7, -1.181, 0, 3.2, 0.676, 0, 3.467, -0.023, 0, 3.8, 4.233, 0, 4, -10.519, 0, 4.267, 9.869, 0, 4.6, -4.493, 0, 5.067, 4.058, 0, 5.367, -2.557, 0, 5.7, 1.982, 0, 6, -2.206, 0, 6.367, 1.893, 0, 6.667, -1.075, 0, 7, 0.721, 0, 7.233, -0.059, 0, 7.4, 1.026, 0, 7.7, -2.825, 0, 8.033, 3.197, 0, 8.3, -0.346, 0, 8.433, 0.823, 0, 8.7, -6.337, 0, 8.967, 7.055, 0, 9.233, -4.354, 0, 9.567, 3.46, 0, 9.867, -4.208, 0, 10.167, 4.375, 0, 10.467, -2.61, 0, 10.767, 1.495, 0, 11, -0.262, 0, 11.167, 2.183, 0, 11.4, -6.553, 0, 11.667, 6.925, 0, 11.933, -7.642, 0, 12.167, 10.191, 0, 12.4, -12.345, 0, 12.633, 11.211, 0, 12.9, -8.601, 0, 13.167, 4.913, 0, 13.5, -1.9, 0, 13.867, 1.546, 0, 14.233, -2.502, 0, 14.533, 4.904, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param62", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -0.577, 0, 0.667, 1.426, 0, 0.933, -2.567, 0, 1.167, -0.431, 0, 1.3, -0.773, 0, 1.633, 0.523, 0, 1.767, 0.36, 0, 2.033, 2.194, 0, 2.533, -2.689, 0, 3.1, 2.138, 0, 3.833, -9.185, 0, 4.1, 9.469, 0, 4.567, -5.53, 0, 4.933, 4.423, 0, 5.233, -0.599, 0, 5.467, 0.278, 0, 5.833, -2.912, 0, 6.267, 2.4, 0, 6.567, 0.629, 0, 6.767, 0.909, 0, 7.467, -3.989, 0, 7.9, 4.946, 0, 8.467, -7.445, 0, 8.8, 7.428, 0, 9.133, -0.861, 0, 9.3, 0.189, 0, 9.667, -4.903, 0, 10.033, 4.348, 0, 10.333, -0.069, 0, 10.6, 1.069, 0, 11.2, -6.614, 0, 11.5, 6.742, 0, 11.8, -2.097, 0, 12, 5.392, 0, 12.233, -10.527, 0, 12.467, 6.819, 0, 12.7, -6.803, 0, 13.033, 3.888, 0, 13.533, 0.494, 0, 13.667, 0.632, 0, 14, -2.069, 0, 14.333, 3.784, 0, 14.633, -4.724, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param63", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, 0.577, 0, 0.633, -1.095, 0, 0.867, 2.542, 0, 1.1, -2.339, 0, 1.333, 1.048, 0, 1.567, -0.483, 0, 1.767, 0.254, 0, 1.967, -0.692, 0, 2.233, 1.353, 0, 2.7, -1.181, 0, 3.2, 0.676, 0, 3.467, -0.023, 0, 3.8, 4.233, 0, 4, -10.519, 0, 4.267, 9.869, 0, 4.6, -4.493, 0, 5.067, 4.058, 0, 5.367, -2.557, 0, 5.7, 1.982, 0, 6, -2.206, 0, 6.367, 1.893, 0, 6.667, -1.075, 0, 7, 0.721, 0, 7.233, -0.059, 0, 7.4, 1.026, 0, 7.7, -2.825, 0, 8.033, 3.197, 0, 8.3, -0.346, 0, 8.433, 0.823, 0, 8.7, -6.337, 0, 8.967, 7.055, 0, 9.233, -4.354, 0, 9.567, 3.46, 0, 9.867, -4.208, 0, 10.167, 4.375, 0, 10.467, -2.61, 0, 10.767, 1.495, 0, 11, -0.262, 0, 11.167, 2.183, 0, 11.4, -6.553, 0, 11.667, 6.925, 0, 11.933, -7.642, 0, 12.167, 10.191, 0, 12.4, -12.345, 0, 12.633, 11.211, 0, 12.9, -8.601, 0, 13.167, 4.913, 0, 13.5, -1.9, 0, 13.867, 1.546, 0, 14.233, -2.502, 0, 14.533, 4.904, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param64", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -0.577, 0, 0.667, 1.426, 0, 0.933, -2.567, 0, 1.167, -0.431, 0, 1.3, -0.773, 0, 1.633, 0.523, 0, 1.767, 0.36, 0, 2.033, 2.194, 0, 2.533, -2.689, 0, 3.1, 2.138, 0, 3.833, -9.185, 0, 4.1, 9.469, 0, 4.567, -5.53, 0, 4.933, 4.423, 0, 5.233, -0.599, 0, 5.467, 0.278, 0, 5.833, -2.912, 0, 6.267, 2.4, 0, 6.567, 0.629, 0, 6.767, 0.909, 0, 7.467, -3.989, 0, 7.9, 4.946, 0, 8.467, -7.445, 0, 8.8, 7.428, 0, 9.133, -0.861, 0, 9.3, 0.189, 0, 9.667, -4.903, 0, 10.033, 4.348, 0, 10.333, -0.069, 0, 10.6, 1.069, 0, 11.2, -6.614, 0, 11.5, 6.742, 0, 11.8, -2.097, 0, 12, 5.392, 0, 12.233, -10.527, 0, 12.467, 6.819, 0, 12.7, -6.803, 0, 13.033, 3.888, 0, 13.533, 0.494, 0, 13.667, 0.632, 0, 14, -2.069, 0, 14.333, 3.784, 0, 14.633, -4.724, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param65", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, 0.577, 0, 0.633, -1.095, 0, 0.867, 2.542, 0, 1.1, -2.339, 0, 1.333, 1.048, 0, 1.567, -0.483, 0, 1.767, 0.254, 0, 1.967, -0.692, 0, 2.233, 1.353, 0, 2.7, -1.181, 0, 3.2, 0.676, 0, 3.467, -0.023, 0, 3.8, 4.233, 0, 4, -10.519, 0, 4.267, 9.869, 0, 4.6, -4.493, 0, 5.067, 4.058, 0, 5.367, -2.557, 0, 5.7, 1.982, 0, 6, -2.206, 0, 6.367, 1.893, 0, 6.667, -1.075, 0, 7, 0.721, 0, 7.233, -0.059, 0, 7.4, 1.026, 0, 7.7, -2.825, 0, 8.033, 3.197, 0, 8.3, -0.346, 0, 8.433, 0.823, 0, 8.7, -6.337, 0, 8.967, 7.055, 0, 9.233, -4.354, 0, 9.567, 3.46, 0, 9.867, -4.208, 0, 10.167, 4.375, 0, 10.467, -2.61, 0, 10.767, 1.495, 0, 11, -0.262, 0, 11.167, 2.183, 0, 11.4, -6.553, 0, 11.667, 6.925, 0, 11.933, -7.642, 0, 12.167, 10.191, 0, 12.4, -12.345, 0, 12.633, 11.211, 0, 12.9, -8.601, 0, 13.167, 4.913, 0, 13.5, -1.9, 0, 13.867, 1.546, 0, 14.233, -2.502, 0, 14.533, 4.904, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param72", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -0.577, 0, 0.667, 1.426, 0, 0.933, -2.567, 0, 1.167, -0.431, 0, 1.3, -0.773, 0, 1.633, 0.523, 0, 1.767, 0.36, 0, 2.033, 2.194, 0, 2.533, -2.689, 0, 3.1, 2.138, 0, 3.833, -9.185, 0, 4.1, 9.469, 0, 4.567, -5.53, 0, 4.933, 4.423, 0, 5.233, -0.599, 0, 5.467, 0.278, 0, 5.833, -2.912, 0, 6.267, 2.4, 0, 6.567, 0.629, 0, 6.767, 0.909, 0, 7.467, -3.989, 0, 7.9, 4.946, 0, 8.467, -7.445, 0, 8.8, 7.428, 0, 9.133, -0.861, 0, 9.3, 0.189, 0, 9.667, -4.903, 0, 10.033, 4.348, 0, 10.333, -0.069, 0, 10.6, 1.069, 0, 11.2, -6.614, 0, 11.5, 6.742, 0, 11.8, -2.097, 0, 12, 5.392, 0, 12.233, -10.527, 0, 12.467, 6.819, 0, 12.7, -6.803, 0, 13.033, 3.888, 0, 13.533, 0.494, 0, 13.667, 0.632, 0, 14, -2.069, 0, 14.333, 3.784, 0, 14.633, -4.724, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param73", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, 0.577, 0, 0.633, -1.095, 0, 0.867, 2.542, 0, 1.1, -2.339, 0, 1.333, 1.048, 0, 1.567, -0.483, 0, 1.767, 0.254, 0, 1.967, -0.692, 0, 2.233, 1.353, 0, 2.7, -1.181, 0, 3.2, 0.676, 0, 3.467, -0.023, 0, 3.8, 4.233, 0, 4, -10.519, 0, 4.267, 9.869, 0, 4.6, -4.493, 0, 5.067, 4.058, 0, 5.367, -2.557, 0, 5.7, 1.982, 0, 6, -2.206, 0, 6.367, 1.893, 0, 6.667, -1.075, 0, 7, 0.721, 0, 7.233, -0.059, 0, 7.4, 1.026, 0, 7.7, -2.825, 0, 8.033, 3.197, 0, 8.3, -0.346, 0, 8.433, 0.823, 0, 8.7, -6.337, 0, 8.967, 7.055, 0, 9.233, -4.354, 0, 9.567, 3.46, 0, 9.867, -4.208, 0, 10.167, 4.375, 0, 10.467, -2.61, 0, 10.767, 1.495, 0, 11, -0.262, 0, 11.167, 2.183, 0, 11.4, -6.553, 0, 11.667, 6.925, 0, 11.933, -7.642, 0, 12.167, 10.191, 0, 12.4, -12.345, 0, 12.633, 11.211, 0, 12.9, -8.601, 0, 13.167, 4.913, 0, 13.5, -1.9, 0, 13.867, 1.546, 0, 14.233, -2.502, 0, 14.533, 4.904, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param74", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -0.577, 0, 0.667, 1.426, 0, 0.933, -2.567, 0, 1.167, -0.431, 0, 1.3, -0.773, 0, 1.633, 0.523, 0, 1.767, 0.36, 0, 2.033, 2.194, 0, 2.533, -2.689, 0, 3.1, 2.138, 0, 3.833, -9.185, 0, 4.1, 9.469, 0, 4.567, -5.53, 0, 4.933, 4.423, 0, 5.233, -0.599, 0, 5.467, 0.278, 0, 5.833, -2.912, 0, 6.267, 2.4, 0, 6.567, 0.629, 0, 6.767, 0.909, 0, 7.467, -3.989, 0, 7.9, 4.946, 0, 8.467, -7.445, 0, 8.8, 7.428, 0, 9.133, -0.861, 0, 9.3, 0.189, 0, 9.667, -4.903, 0, 10.033, 4.348, 0, 10.333, -0.069, 0, 10.6, 1.069, 0, 11.2, -6.614, 0, 11.5, 6.742, 0, 11.8, -2.097, 0, 12, 5.392, 0, 12.233, -10.527, 0, 12.467, 6.819, 0, 12.7, -6.803, 0, 13.033, 3.888, 0, 13.533, 0.494, 0, 13.667, 0.632, 0, 14, -2.069, 0, 14.333, 3.784, 0, 14.633, -4.724, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param75", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, 0.577, 0, 0.633, -1.095, 0, 0.867, 2.542, 0, 1.1, -2.339, 0, 1.333, 1.048, 0, 1.567, -0.483, 0, 1.767, 0.254, 0, 1.967, -0.692, 0, 2.233, 1.353, 0, 2.7, -1.181, 0, 3.2, 0.676, 0, 3.467, -0.023, 0, 3.8, 4.233, 0, 4, -10.519, 0, 4.267, 9.869, 0, 4.6, -4.493, 0, 5.067, 4.058, 0, 5.367, -2.557, 0, 5.7, 1.982, 0, 6, -2.206, 0, 6.367, 1.893, 0, 6.667, -1.075, 0, 7, 0.721, 0, 7.233, -0.059, 0, 7.4, 1.026, 0, 7.7, -2.825, 0, 8.033, 3.197, 0, 8.3, -0.346, 0, 8.433, 0.823, 0, 8.7, -6.337, 0, 8.967, 7.055, 0, 9.233, -4.354, 0, 9.567, 3.46, 0, 9.867, -4.208, 0, 10.167, 4.375, 0, 10.467, -2.61, 0, 10.767, 1.495, 0, 11, -0.262, 0, 11.167, 2.183, 0, 11.4, -6.553, 0, 11.667, 6.925, 0, 11.933, -7.642, 0, 12.167, 10.191, 0, 12.4, -12.345, 0, 12.633, 11.211, 0, 12.9, -8.601, 0, 13.167, 4.913, 0, 13.5, -1.9, 0, 13.867, 1.546, 0, 14.233, -2.502, 0, 14.533, 4.904, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param53", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -0.577, 0, 0.667, 1.426, 0, 0.933, -2.567, 0, 1.167, -0.431, 0, 1.3, -0.773, 0, 1.633, 0.523, 0, 1.767, 0.36, 0, 2.033, 2.194, 0, 2.533, -2.689, 0, 3.1, 2.138, 0, 3.833, -9.185, 0, 4.1, 9.469, 0, 4.567, -5.53, 0, 4.933, 4.423, 0, 5.233, -0.599, 0, 5.467, 0.278, 0, 5.833, -2.912, 0, 6.267, 2.4, 0, 6.567, 0.629, 0, 6.767, 0.909, 0, 7.467, -3.989, 0, 7.9, 4.946, 0, 8.467, -7.445, 0, 8.8, 7.428, 0, 9.133, -0.861, 0, 9.3, 0.189, 0, 9.667, -4.903, 0, 10.033, 4.348, 0, 10.333, -0.069, 0, 10.6, 1.069, 0, 11.2, -6.614, 0, 11.5, 6.742, 0, 11.8, -2.097, 0, 12, 5.392, 0, 12.233, -10.527, 0, 12.467, 6.819, 0, 12.7, -6.803, 0, 13.033, 3.888, 0, 13.533, 0.494, 0, 13.667, 0.632, 0, 14, -2.069, 0, 14.333, 3.784, 0, 14.633, -4.724, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param54", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, 0.577, 0, 0.633, -1.095, 0, 0.867, 2.542, 0, 1.1, -2.339, 0, 1.333, 1.048, 0, 1.567, -0.483, 0, 1.767, 0.254, 0, 1.967, -0.692, 0, 2.233, 1.353, 0, 2.7, -1.181, 0, 3.2, 0.676, 0, 3.467, -0.023, 0, 3.8, 4.233, 0, 4, -10.519, 0, 4.267, 9.869, 0, 4.6, -4.493, 0, 5.067, 4.058, 0, 5.367, -2.557, 0, 5.7, 1.982, 0, 6, -2.206, 0, 6.367, 1.893, 0, 6.667, -1.075, 0, 7, 0.721, 0, 7.233, -0.059, 0, 7.4, 1.026, 0, 7.7, -2.825, 0, 8.033, 3.197, 0, 8.3, -0.346, 0, 8.433, 0.823, 0, 8.7, -6.337, 0, 8.967, 7.055, 0, 9.233, -4.354, 0, 9.567, 3.46, 0, 9.867, -4.208, 0, 10.167, 4.375, 0, 10.467, -2.61, 0, 10.767, 1.495, 0, 11, -0.262, 0, 11.167, 2.183, 0, 11.4, -6.553, 0, 11.667, 6.925, 0, 11.933, -7.642, 0, 12.167, 10.191, 0, 12.4, -12.345, 0, 12.633, 11.211, 0, 12.9, -8.601, 0, 13.167, 4.913, 0, 13.5, -1.9, 0, 13.867, 1.546, 0, 14.233, -2.502, 0, 14.533, 4.904, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param81", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -0.577, 0, 0.667, 1.426, 0, 0.933, -2.567, 0, 1.167, -0.431, 0, 1.3, -0.773, 0, 1.633, 0.523, 0, 1.767, 0.36, 0, 2.033, 2.194, 0, 2.533, -2.689, 0, 3.1, 2.138, 0, 3.833, -9.185, 0, 4.1, 9.469, 0, 4.567, -5.53, 0, 4.933, 4.423, 0, 5.233, -0.599, 0, 5.467, 0.278, 0, 5.833, -2.912, 0, 6.267, 2.4, 0, 6.567, 0.629, 0, 6.767, 0.909, 0, 7.467, -3.989, 0, 7.9, 4.946, 0, 8.467, -7.445, 0, 8.8, 7.428, 0, 9.133, -0.861, 0, 9.3, 0.189, 0, 9.667, -4.903, 0, 10.033, 4.348, 0, 10.333, -0.069, 0, 10.6, 1.069, 0, 11.2, -6.614, 0, 11.5, 6.742, 0, 11.8, -2.097, 0, 12, 5.392, 0, 12.233, -10.527, 0, 12.467, 6.819, 0, 12.7, -6.803, 0, 13.033, 3.888, 0, 13.533, 0.494, 0, 13.667, 0.632, 0, 14, -2.069, 0, 14.333, 3.784, 0, 14.633, -4.724, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param82", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, 0.577, 0, 0.633, -1.095, 0, 0.867, 2.542, 0, 1.1, -2.339, 0, 1.333, 1.048, 0, 1.567, -0.483, 0, 1.767, 0.254, 0, 1.967, -0.692, 0, 2.233, 1.353, 0, 2.7, -1.181, 0, 3.2, 0.676, 0, 3.467, -0.023, 0, 3.8, 4.233, 0, 4, -10.519, 0, 4.267, 9.869, 0, 4.6, -4.493, 0, 5.067, 4.058, 0, 5.367, -2.557, 0, 5.7, 1.982, 0, 6, -2.206, 0, 6.367, 1.893, 0, 6.667, -1.075, 0, 7, 0.721, 0, 7.233, -0.059, 0, 7.4, 1.026, 0, 7.7, -2.825, 0, 8.033, 3.197, 0, 8.3, -0.346, 0, 8.433, 0.823, 0, 8.7, -6.337, 0, 8.967, 7.055, 0, 9.233, -4.354, 0, 9.567, 3.46, 0, 9.867, -4.208, 0, 10.167, 4.375, 0, 10.467, -2.61, 0, 10.767, 1.495, 0, 11, -0.262, 0, 11.167, 2.183, 0, 11.4, -6.553, 0, 11.667, 6.925, 0, 11.933, -7.642, 0, 12.167, 10.191, 0, 12.4, -12.345, 0, 12.633, 11.211, 0, 12.9, -8.601, 0, 13.167, 4.913, 0, 13.5, -1.9, 0, 13.867, 1.546, 0, 14.233, -2.502, 0, 14.533, 4.904, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param70", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -0.577, 0, 0.667, 1.426, 0, 0.933, -2.567, 0, 1.167, -0.431, 0, 1.3, -0.773, 0, 1.633, 0.523, 0, 1.767, 0.36, 0, 2.033, 2.194, 0, 2.533, -2.689, 0, 3.1, 2.138, 0, 3.833, -9.185, 0, 4.1, 9.469, 0, 4.567, -5.53, 0, 4.933, 4.423, 0, 5.233, -0.599, 0, 5.467, 0.278, 0, 5.833, -2.912, 0, 6.267, 2.4, 0, 6.567, 0.629, 0, 6.767, 0.909, 0, 7.467, -3.989, 0, 7.9, 4.946, 0, 8.467, -7.445, 0, 8.8, 7.428, 0, 9.133, -0.861, 0, 9.3, 0.189, 0, 9.667, -4.903, 0, 10.033, 4.348, 0, 10.333, -0.069, 0, 10.6, 1.069, 0, 11.2, -6.614, 0, 11.5, 6.742, 0, 11.8, -2.097, 0, 12, 5.392, 0, 12.233, -10.527, 0, 12.467, 6.819, 0, 12.7, -6.803, 0, 13.033, 3.888, 0, 13.533, 0.494, 0, 13.667, 0.632, 0, 14, -2.069, 0, 14.333, 3.784, 0, 14.633, -4.724, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param71", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, 0.577, 0, 0.633, -1.095, 0, 0.867, 2.542, 0, 1.1, -2.339, 0, 1.333, 1.048, 0, 1.567, -0.483, 0, 1.767, 0.254, 0, 1.967, -0.692, 0, 2.233, 1.353, 0, 2.7, -1.181, 0, 3.2, 0.676, 0, 3.467, -0.023, 0, 3.8, 4.233, 0, 4, -10.519, 0, 4.267, 9.869, 0, 4.6, -4.493, 0, 5.067, 4.058, 0, 5.367, -2.557, 0, 5.7, 1.982, 0, 6, -2.206, 0, 6.367, 1.893, 0, 6.667, -1.075, 0, 7, 0.721, 0, 7.233, -0.059, 0, 7.4, 1.026, 0, 7.7, -2.825, 0, 8.033, 3.197, 0, 8.3, -0.346, 0, 8.433, 0.823, 0, 8.7, -6.337, 0, 8.967, 7.055, 0, 9.233, -4.354, 0, 9.567, 3.46, 0, 9.867, -4.208, 0, 10.167, 4.375, 0, 10.467, -2.61, 0, 10.767, 1.495, 0, 11, -0.262, 0, 11.167, 2.183, 0, 11.4, -6.553, 0, 11.667, 6.925, 0, 11.933, -7.642, 0, 12.167, 10.191, 0, 12.4, -12.345, 0, 12.633, 11.211, 0, 12.9, -8.601, 0, 13.167, 4.913, 0, 13.5, -1.9, 0, 13.867, 1.546, 0, 14.233, -2.502, 0, 14.533, 4.904, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param77", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -0.577, 0, 0.667, 1.426, 0, 0.933, -2.567, 0, 1.167, -0.431, 0, 1.3, -0.773, 0, 1.633, 0.523, 0, 1.767, 0.36, 0, 2.033, 2.194, 0, 2.533, -2.689, 0, 3.1, 2.138, 0, 3.833, -9.185, 0, 4.1, 9.469, 0, 4.567, -5.53, 0, 4.933, 4.423, 0, 5.233, -0.599, 0, 5.467, 0.278, 0, 5.833, -2.912, 0, 6.267, 2.4, 0, 6.567, 0.629, 0, 6.767, 0.909, 0, 7.467, -3.989, 0, 7.9, 4.946, 0, 8.467, -7.445, 0, 8.8, 7.428, 0, 9.133, -0.861, 0, 9.3, 0.189, 0, 9.667, -4.903, 0, 10.033, 4.348, 0, 10.333, -0.069, 0, 10.6, 1.069, 0, 11.2, -6.614, 0, 11.5, 6.742, 0, 11.8, -2.097, 0, 12, 5.392, 0, 12.233, -10.527, 0, 12.467, 6.819, 0, 12.7, -6.803, 0, 13.033, 3.888, 0, 13.533, 0.494, 0, 13.667, 0.632, 0, 14, -2.069, 0, 14.333, 3.784, 0, 14.633, -4.724, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param78", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, 0.577, 0, 0.633, -1.095, 0, 0.867, 2.542, 0, 1.1, -2.339, 0, 1.333, 1.048, 0, 1.567, -0.483, 0, 1.767, 0.254, 0, 1.967, -0.692, 0, 2.233, 1.353, 0, 2.7, -1.181, 0, 3.2, 0.676, 0, 3.467, -0.023, 0, 3.8, 4.233, 0, 4, -10.519, 0, 4.267, 9.869, 0, 4.6, -4.493, 0, 5.067, 4.058, 0, 5.367, -2.557, 0, 5.7, 1.982, 0, 6, -2.206, 0, 6.367, 1.893, 0, 6.667, -1.075, 0, 7, 0.721, 0, 7.233, -0.059, 0, 7.4, 1.026, 0, 7.7, -2.825, 0, 8.033, 3.197, 0, 8.3, -0.346, 0, 8.433, 0.823, 0, 8.7, -6.337, 0, 8.967, 7.055, 0, 9.233, -4.354, 0, 9.567, 3.46, 0, 9.867, -4.208, 0, 10.167, 4.375, 0, 10.467, -2.61, 0, 10.767, 1.495, 0, 11, -0.262, 0, 11.167, 2.183, 0, 11.4, -6.553, 0, 11.667, 6.925, 0, 11.933, -7.642, 0, 12.167, 10.191, 0, 12.4, -12.345, 0, 12.633, 11.211, 0, 12.9, -8.601, 0, 13.167, 4.913, 0, 13.5, -1.9, 0, 13.867, 1.546, 0, 14.233, -2.502, 0, 14.533, 4.904, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param79", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -0.577, 0, 0.667, 1.426, 0, 0.933, -2.567, 0, 1.167, -0.431, 0, 1.3, -0.773, 0, 1.633, 0.523, 0, 1.767, 0.36, 0, 2.033, 2.194, 0, 2.533, -2.689, 0, 3.1, 2.138, 0, 3.833, -9.185, 0, 4.1, 9.469, 0, 4.567, -5.53, 0, 4.933, 4.423, 0, 5.233, -0.599, 0, 5.467, 0.278, 0, 5.833, -2.912, 0, 6.267, 2.4, 0, 6.567, 0.629, 0, 6.767, 0.909, 0, 7.467, -3.989, 0, 7.9, 4.946, 0, 8.467, -7.445, 0, 8.8, 7.428, 0, 9.133, -0.861, 0, 9.3, 0.189, 0, 9.667, -4.903, 0, 10.033, 4.348, 0, 10.333, -0.069, 0, 10.6, 1.069, 0, 11.2, -6.614, 0, 11.5, 6.742, 0, 11.8, -2.097, 0, 12, 5.392, 0, 12.233, -10.527, 0, 12.467, 6.819, 0, 12.7, -6.803, 0, 13.033, 3.888, 0, 13.533, 0.494, 0, 13.667, 0.632, 0, 14, -2.069, 0, 14.333, 3.784, 0, 14.633, -4.724, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param80", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, 0.577, 0, 0.633, -1.095, 0, 0.867, 2.542, 0, 1.1, -2.339, 0, 1.333, 1.048, 0, 1.567, -0.483, 0, 1.767, 0.254, 0, 1.967, -0.692, 0, 2.233, 1.353, 0, 2.7, -1.181, 0, 3.2, 0.676, 0, 3.467, -0.023, 0, 3.8, 4.233, 0, 4, -10.519, 0, 4.267, 9.869, 0, 4.6, -4.493, 0, 5.067, 4.058, 0, 5.367, -2.557, 0, 5.7, 1.982, 0, 6, -2.206, 0, 6.367, 1.893, 0, 6.667, -1.075, 0, 7, 0.721, 0, 7.233, -0.059, 0, 7.4, 1.026, 0, 7.7, -2.825, 0, 8.033, 3.197, 0, 8.3, -0.346, 0, 8.433, 0.823, 0, 8.7, -6.337, 0, 8.967, 7.055, 0, 9.233, -4.354, 0, 9.567, 3.46, 0, 9.867, -4.208, 0, 10.167, 4.375, 0, 10.467, -2.61, 0, 10.767, 1.495, 0, 11, -0.262, 0, 11.167, 2.183, 0, 11.4, -6.553, 0, 11.667, 6.925, 0, 11.933, -7.642, 0, 12.167, 10.191, 0, 12.4, -12.345, 0, 12.633, 11.211, 0, 12.9, -8.601, 0, 13.167, 4.913, 0, 13.5, -1.9, 0, 13.867, 1.546, 0, 14.233, -2.502, 0, 14.533, 4.904, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param294", "Segments": [0, 1, 2, 0.333, 1, 0, 0.4, 0, 2, 14.633, 0, 0, 14.733, 1]}, {"Target": "Parameter", "Id": "Param47", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -3.442, 0, 0.667, 9.445, 0, 0.967, -11.168, 0, 1.367, 5.311, 0, 1.667, -1.531, 0, 2.033, 6.162, 0, 2.567, -8.363, 0, 3.033, 7.23, 0, 3.833, -15.88, 0, 4.067, 12.451, 0, 4.633, -8.888, 0, 4.967, 9.828, 0, 5.367, 0.371, 0, 5.4, 0.386, 0, 5.833, -7.545, 0, 6.233, 5.945, 0, 6.567, 0.824, 0, 6.8, 1.929, 0, 7.467, -7.503, 0, 7.9, 9.067, 0, 8.467, -11.823, 0, 8.8, 10.959, 0, 9.133, -1.383, 0, 9.333, 0.203, 0, 9.667, -7.601, 0, 10.033, 6.79, 0, 10.333, 0.257, 0, 10.6, 1.808, 0, 11.2, -9.534, 0, 11.5, 9.272, 0, 11.8, -2.709, 0, 12, 5.183, 0, 12.233, -10.34, 0, 12.467, 6.943, 0, 12.7, -6.767, 0, 13.033, 3.811, 0, 13.467, 0.593, 0, 13.633, 0.768, 0, 14, -2.119, 0, 14.333, 3.295, 0, 14.633, -4.679, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param48", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.433, 2.345, 0, 0.6, -6.63, 0, 0.867, 11.738, 0, 1.133, -10.726, 0, 1.5, 7.436, 0, 1.867, -4.901, 0, 2.167, 4.991, 0, 2.733, -3.329, 0, 3.167, 3.536, 0, 3.467, -1.337, 0, 3.767, 6.96, 0, 3.967, -16.826, 0, 4.233, 14.251, 0, 4.5, -4.98, 0, 4.667, -2.503, 0, 4.833, -5.186, 0, 5.1, 7.411, 0, 5.4, -3.81, 0, 5.7, 3.611, 0, 6, -4.799, 0, 6.367, 4.762, 0, 6.667, -2.92, 0, 7, 1.808, 0, 7.233, -0.285, 0, 7.4, 1.878, 0, 7.7, -5.131, 0, 8.033, 5.795, 0, 8.3, -0.815, 0, 8.433, 0.973, 0, 8.667, -9.562, 0, 8.933, 10.619, 0, 9.233, -6.05, 0, 9.567, 5.175, 0, 9.867, -6.523, 0, 10.167, 6.617, 0, 10.467, -3.869, 0, 10.767, 2.257, 0, 11, -0.317, 0, 11.133, 2.885, 0, 11.4, -9.268, 0, 11.667, 9.624, 0, 11.933, -8.936, 0, 12.167, 10.348, 0, 12.4, -12.07, 0, 12.633, 11.067, 0, 12.9, -8.543, 0, 13.167, 4.86, 0, 13.5, -2.12, 0, 13.867, 1.702, 0, 14.233, -2.525, 0, 14.533, 4.839, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param56", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -1.011, 0, 0.733, 3.383, 0, 1.167, -5.168, 0, 1.633, 3.556, 0, 2, 0.979, 0, 2.1, 1.019, 0, 2.667, -5.203, 0, 3.2, 6.531, 0, 3.833, -8.852, 0, 4.267, 6.486, 0, 4.733, -7.314, 0, 5.2, 6.896, 0, 5.833, -6.598, 0, 6.367, 5.834, 0, 6.933, -1.947, 0, 7.267, -0.574, 0, 7.567, -3.121, 0, 8.033, 6.09, 0, 8.567, -8.801, 0, 9.033, 7.065, 0, 9.633, -6.032, 0, 10.167, 5.644, 0, 10.733, -2.046, 0, 11, -0.755, 0, 11.267, -3.334, 0, 11.667, 4.381, 0, 12.267, -5.187, 0, 12.6, 0.553, 0, 12.8, 0.242, 0, 13.3, 1.529, 0, 14.033, -1.395, 0, 14.433, 1.527, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param57", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.433, 0.861, 0, 0.633, -2.741, 0, 0.933, 5.569, 0, 1.333, -6.028, 0, 1.767, 2.811, 0, 2.033, -0.371, 0, 2.4, 2.549, 0, 2.9, -5.435, 0, 3.4, 4.946, 0, 3.7, 1.494, 0, 3.767, 1.963, 0, 4.033, -9.399, 0, 4.433, 6.297, 0, 4.9, -8.276, 0, 5.367, 5.608, 0, 6.067, -5.236, 0, 6.567, 4.153, 0, 7.067, -0.631, 0, 7.4, 1.218, 0, 7.8, -4.764, 0, 8.267, 5.873, 0, 8.733, -9.583, 0, 9.167, 5.807, 0, 9.933, -5.269, 0, 10.333, 4.243, 0, 10.867, -0.718, 0, 11.167, 1.607, 0, 11.467, -5.289, 0, 11.8, 4.261, 0, 12.033, 0.953, 0, 12.167, 2.974, 0, 12.433, -5.349, 0, 12.733, 1.814, 0, 13, -1.438, 0, 13.467, 1.431, 0, 14.3, -1.707, 0, 14.6, 2.116, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param58", "Segments": [0, 0, 2, 0.333, 0, 2, 0.4, 0, 0, 0.533, 1.117, 0, 0.767, -3.66, 0, 1.067, 7.225, 0, 1.4, -8.202, 0, 1.767, 5.578, 0, 2.133, -3.366, 0, 2.5, 3.616, 0, 2.967, -5.228, 0, 3.4, 5.044, 0, 3.733, -0.266, 0, 3.9, 1.264, 0, 4.133, -9.481, 0, 4.5, 9.911, 0, 4.967, -9.419, 0, 5.367, 7.363, 0, 5.8, -1.446, 0, 5.867, -1.404, 0, 6.2, -3.862, 0, 6.6, 4.871, 0, 7, -2.451, 0, 7.5, 2.32, 0, 7.9, -5.361, 0, 8.3, 6.531, 0, 8.833, -9.965, 0, 9.2, 8.601, 0, 9.567, -2.362, 0, 9.767, -0.916, 0, 10.033, -4.185, 0, 10.4, 5.373, 0, 10.8, -2.636, 0, 11.267, 2.897, 0, 11.567, -6.292, 0, 11.9, 6.332, 0, 12.133, -1.212, 0, 12.333, 1.321, 0, 12.567, -5.409, 0, 12.833, 4.776, 0, 13.167, -3.305, 0, 13.5, 2.106, 0, 13.833, -0.616, 0, 14.1, 0.255, 0, 14.4, -1.778, 0, 14.7, 3.16, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param59", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -1.011, 0, 0.733, 3.383, 0, 1.167, -5.168, 0, 1.633, 3.556, 0, 2, 0.979, 0, 2.1, 1.019, 0, 2.667, -5.203, 0, 3.2, 6.531, 0, 3.833, -8.852, 0, 4.267, 6.486, 0, 4.733, -7.314, 0, 5.2, 6.896, 0, 5.833, -6.598, 0, 6.367, 5.834, 0, 6.933, -1.947, 0, 7.267, -0.574, 0, 7.567, -3.121, 0, 8.033, 6.09, 0, 8.567, -8.801, 0, 9.033, 7.065, 0, 9.633, -6.032, 0, 10.167, 5.644, 0, 10.733, -2.046, 0, 11, -0.755, 0, 11.267, -3.334, 0, 11.667, 4.381, 0, 12.267, -5.187, 0, 12.6, 0.553, 0, 12.8, 0.242, 0, 13.3, 1.529, 0, 14.033, -1.395, 0, 14.433, 1.527, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param60", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.433, 0.861, 0, 0.633, -2.741, 0, 0.933, 5.569, 0, 1.333, -6.028, 0, 1.767, 2.811, 0, 2.033, -0.371, 0, 2.4, 2.549, 0, 2.9, -5.435, 0, 3.4, 4.946, 0, 3.7, 1.494, 0, 3.767, 1.963, 0, 4.033, -9.399, 0, 4.433, 6.297, 0, 4.9, -8.276, 0, 5.367, 5.608, 0, 6.067, -5.236, 0, 6.567, 4.153, 0, 7.067, -0.631, 0, 7.4, 1.218, 0, 7.8, -4.764, 0, 8.267, 5.873, 0, 8.733, -9.583, 0, 9.167, 5.807, 0, 9.933, -5.269, 0, 10.333, 4.243, 0, 10.867, -0.718, 0, 11.167, 1.607, 0, 11.467, -5.289, 0, 11.8, 4.261, 0, 12.033, 0.953, 0, 12.167, 2.974, 0, 12.433, -5.349, 0, 12.733, 1.814, 0, 13, -1.438, 0, 13.467, 1.431, 0, 14.3, -1.707, 0, 14.6, 2.116, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param61", "Segments": [0, 0, 2, 0.333, 0, 2, 0.4, 0, 0, 0.533, 1.117, 0, 0.767, -3.66, 0, 1.067, 7.225, 0, 1.4, -8.202, 0, 1.767, 5.578, 0, 2.133, -3.366, 0, 2.5, 3.616, 0, 2.967, -5.228, 0, 3.4, 5.044, 0, 3.733, -0.266, 0, 3.9, 1.264, 0, 4.133, -9.481, 0, 4.5, 9.911, 0, 4.967, -9.419, 0, 5.367, 7.363, 0, 5.8, -1.446, 0, 5.867, -1.404, 0, 6.2, -3.862, 0, 6.6, 4.871, 0, 7, -2.451, 0, 7.5, 2.32, 0, 7.9, -5.361, 0, 8.3, 6.531, 0, 8.833, -9.965, 0, 9.2, 8.601, 0, 9.567, -2.362, 0, 9.767, -0.916, 0, 10.033, -4.185, 0, 10.4, 5.373, 0, 10.8, -2.636, 0, 11.267, 2.897, 0, 11.567, -6.292, 0, 11.9, 6.332, 0, 12.133, -1.212, 0, 12.333, 1.321, 0, 12.567, -5.409, 0, 12.833, 4.776, 0, 13.167, -3.305, 0, 13.5, 2.106, 0, 13.833, -0.616, 0, 14.1, 0.255, 0, 14.4, -1.778, 0, 14.7, 3.16, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param49", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -3.442, 0, 0.667, 9.445, 0, 0.967, -11.168, 0, 1.367, 5.311, 0, 1.667, -1.531, 0, 2.033, 6.162, 0, 2.567, -8.363, 0, 3.033, 7.23, 0, 3.833, -15.88, 0, 4.067, 12.451, 0, 4.633, -8.888, 0, 4.967, 9.828, 0, 5.367, 0.371, 0, 5.4, 0.386, 0, 5.833, -7.545, 0, 6.233, 5.945, 0, 6.567, 0.824, 0, 6.8, 1.929, 0, 7.467, -7.503, 0, 7.9, 9.067, 0, 8.467, -11.823, 0, 8.8, 10.959, 0, 9.133, -1.383, 0, 9.333, 0.203, 0, 9.667, -7.601, 0, 10.033, 6.79, 0, 10.333, 0.257, 0, 10.6, 1.808, 0, 11.2, -9.534, 0, 11.5, 9.272, 0, 11.8, -2.709, 0, 12, 5.183, 0, 12.233, -10.34, 0, 12.467, 6.943, 0, 12.7, -6.767, 0, 13.033, 3.811, 0, 13.467, 0.593, 0, 13.633, 0.768, 0, 14, -2.119, 0, 14.333, 3.295, 0, 14.633, -4.679, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param50", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.433, 2.345, 0, 0.6, -6.63, 0, 0.867, 11.738, 0, 1.133, -10.726, 0, 1.5, 7.436, 0, 1.867, -4.901, 0, 2.167, 4.991, 0, 2.733, -3.329, 0, 3.167, 3.536, 0, 3.467, -1.337, 0, 3.767, 6.96, 0, 3.967, -16.826, 0, 4.233, 14.251, 0, 4.5, -4.98, 0, 4.667, -2.503, 0, 4.833, -5.186, 0, 5.1, 7.411, 0, 5.4, -3.81, 0, 5.7, 3.611, 0, 6, -4.799, 0, 6.367, 4.762, 0, 6.667, -2.92, 0, 7, 1.808, 0, 7.233, -0.285, 0, 7.4, 1.878, 0, 7.7, -5.131, 0, 8.033, 5.795, 0, 8.3, -0.815, 0, 8.433, 0.973, 0, 8.667, -9.562, 0, 8.933, 10.619, 0, 9.233, -6.05, 0, 9.567, 5.175, 0, 9.867, -6.523, 0, 10.167, 6.617, 0, 10.467, -3.869, 0, 10.767, 2.257, 0, 11, -0.317, 0, 11.133, 2.885, 0, 11.4, -9.268, 0, 11.667, 9.624, 0, 11.933, -8.936, 0, 12.167, 10.348, 0, 12.4, -12.07, 0, 12.633, 11.067, 0, 12.9, -8.543, 0, 13.167, 4.86, 0, 13.5, -2.12, 0, 13.867, 1.702, 0, 14.233, -2.525, 0, 14.533, 4.839, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param51", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -3.442, 0, 0.667, 9.445, 0, 0.967, -11.168, 0, 1.367, 5.311, 0, 1.667, -1.531, 0, 2.033, 6.162, 0, 2.567, -8.363, 0, 3.033, 7.23, 0, 3.833, -15.88, 0, 4.067, 12.451, 0, 4.633, -8.888, 0, 4.967, 9.828, 0, 5.367, 0.371, 0, 5.4, 0.386, 0, 5.833, -7.545, 0, 6.233, 5.945, 0, 6.567, 0.824, 0, 6.8, 1.929, 0, 7.467, -7.503, 0, 7.9, 9.067, 0, 8.467, -11.823, 0, 8.8, 10.959, 0, 9.133, -1.383, 0, 9.333, 0.203, 0, 9.667, -7.601, 0, 10.033, 6.79, 0, 10.333, 0.257, 0, 10.6, 1.808, 0, 11.2, -9.534, 0, 11.5, 9.272, 0, 11.8, -2.709, 0, 12, 5.183, 0, 12.233, -10.34, 0, 12.467, 6.943, 0, 12.7, -6.767, 0, 13.033, 3.811, 0, 13.467, 0.593, 0, 13.633, 0.768, 0, 14, -2.119, 0, 14.333, 3.295, 0, 14.633, -4.679, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param52", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.433, 2.345, 0, 0.6, -6.63, 0, 0.867, 11.738, 0, 1.133, -10.726, 0, 1.5, 7.436, 0, 1.867, -4.901, 0, 2.167, 4.991, 0, 2.733, -3.329, 0, 3.167, 3.536, 0, 3.467, -1.337, 0, 3.767, 6.96, 0, 3.967, -16.826, 0, 4.233, 14.251, 0, 4.5, -4.98, 0, 4.667, -2.503, 0, 4.833, -5.186, 0, 5.1, 7.411, 0, 5.4, -3.81, 0, 5.7, 3.611, 0, 6, -4.799, 0, 6.367, 4.762, 0, 6.667, -2.92, 0, 7, 1.808, 0, 7.233, -0.285, 0, 7.4, 1.878, 0, 7.7, -5.131, 0, 8.033, 5.795, 0, 8.3, -0.815, 0, 8.433, 0.973, 0, 8.667, -9.562, 0, 8.933, 10.619, 0, 9.233, -6.05, 0, 9.567, 5.175, 0, 9.867, -6.523, 0, 10.167, 6.617, 0, 10.467, -3.869, 0, 10.767, 2.257, 0, 11, -0.317, 0, 11.133, 2.885, 0, 11.4, -9.268, 0, 11.667, 9.624, 0, 11.933, -8.936, 0, 12.167, 10.348, 0, 12.4, -12.07, 0, 12.633, 11.067, 0, 12.9, -8.543, 0, 13.167, 4.86, 0, 13.5, -2.12, 0, 13.867, 1.702, 0, 14.233, -2.525, 0, 14.533, 4.839, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation63", "Segments": [0, -0.675, 2, 0.333, -0.675, 0, 0.367, 0, 0, 0.467, -1.147, 0, 0.667, 3.148, 0, 0.967, -3.723, 0, 1.367, 1.77, 0, 1.667, -0.51, 0, 2.033, 2.054, 0, 2.567, -2.788, 0, 3.033, 2.41, 0, 3.833, -5.293, 0, 4.067, 4.15, 0, 4.633, -2.963, 0, 4.967, 3.276, 0, 5.367, 0.124, 0, 5.4, 0.129, 0, 5.833, -2.515, 0, 6.233, 1.982, 0, 6.567, 0.275, 0, 6.8, 0.643, 0, 7.467, -2.501, 0, 7.9, 3.022, 0, 8.467, -3.941, 0, 8.8, 3.653, 0, 9.133, -0.461, 0, 9.333, 0.068, 0, 9.667, -2.534, 0, 10.033, 2.264, 0, 10.333, 0.086, 0, 10.6, 0.603, 0, 11.2, -3.178, 0, 11.5, 3.091, 0, 11.8, -0.903, 0, 12, 1.728, 0, 12.233, -3.447, 0, 12.467, 2.314, 0, 12.7, -2.256, 0, 13.033, 1.27, 0, 13.467, 0.198, 0, 13.633, 0.256, 0, 14, -0.706, 0, 14.333, 1.099, 0, 14.633, -1.56, 0, 14.733, -0.675]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation64", "Segments": [0, 2.951, 2, 0.333, 2.951, 0, 0.367, 0, 0, 0.433, 1.173, 0, 0.6, -3.315, 0, 0.867, 5.869, 0, 1.133, -5.363, 0, 1.5, 3.718, 0, 1.867, -2.45, 0, 2.167, 2.495, 0, 2.733, -1.664, 0, 3.167, 1.768, 0, 3.467, -0.669, 0, 3.767, 3.48, 0, 3.967, -8.413, 0, 4.233, 7.125, 0, 4.5, -2.49, 0, 4.667, -1.252, 0, 4.833, -2.593, 0, 5.1, 3.706, 0, 5.4, -1.905, 0, 5.7, 1.806, 0, 6, -2.4, 0, 6.367, 2.381, 0, 6.667, -1.46, 0, 7, 0.904, 0, 7.233, -0.142, 0, 7.4, 0.939, 0, 7.7, -2.566, 0, 8.033, 2.898, 0, 8.3, -0.407, 0, 8.433, 0.486, 0, 8.667, -4.781, 0, 8.933, 5.309, 0, 9.233, -3.025, 0, 9.567, 2.587, 0, 9.867, -3.262, 0, 10.167, 3.309, 0, 10.467, -1.934, 0, 10.767, 1.128, 0, 11, -0.159, 0, 11.133, 1.442, 0, 11.4, -4.634, 0, 11.667, 4.812, 0, 11.933, -4.468, 0, 12.167, 5.174, 0, 12.4, -6.035, 0, 12.633, 5.534, 0, 12.9, -4.271, 0, 13.167, 2.43, 0, 13.5, -1.06, 0, 13.867, 0.851, 0, 14.233, -1.262, 1, 14.333, -1.262, 14.433, 1.436, 14.533, 2.42, 1, 14.6, 3.075, 14.666, 2.951, 14.733, 2.951]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation65", "Segments": [0, 4.849, 2, 0.333, 4.849, 0, 0.367, 0, 0, 0.5, 0.97, 0, 0.7, -4.059, 0, 0.967, 8.76, 0, 1.267, -10.453, 0, 1.6, 8.529, 0, 1.933, -6.506, 0, 2.267, 5.797, 0, 2.667, -2.866, 0, 3.267, 2.969, 0, 3.567, -1.811, 0, 3.867, 4.754, 0, 4.1, -10.68, 0, 4.367, 12.812, 0, 4.667, -7.043, 0, 5.2, 6.031, 0, 5.533, -4.511, 0, 5.8, 3.98, 0, 6.133, -5.015, 0, 6.467, 5.01, 0, 6.8, -3.715, 0, 7.1, 2.646, 0, 7.367, -0.49, 0, 7.5, 0.465, 0, 7.8, -4.041, 0, 8.133, 5.702, 0, 8.433, -1.75, 0, 8.533, -1.087, 0, 8.767, -5.913, 0, 9.067, 9.385, 0, 9.367, -7.129, 0, 9.667, 5.94, 0, 9.967, -6.881, 0, 10.267, 6.896, 0, 10.6, -4.949, 0, 10.9, 3.357, 0, 11.133, -0.154, 0, 11.233, 0.498, 0, 11.467, -6.092, 0, 11.767, 8.845, 0, 12.067, -8.321, 0, 12.3, 7.877, 0, 12.533, -8.05, 0, 12.767, 8.318, 0, 13.033, -7.864, 0, 13.3, 5.843, 0, 13.633, -3.408, 0, 13.933, 2.386, 0, 14.3, -2.554, 1, 14.411, -2.554, 14.522, 1.301, 14.633, 4.208, 1, 14.666, 5.08, 14.7, 4.849, 14.733, 4.849]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation66", "Segments": [0, -11.659, 2, 0.333, -11.659, 1, 0.366, -11.659, 0.4, -0.695, 0.433, 0, 1, 0.478, 0.927, 0.522, 0.901, 0.567, 0.901, 0, 0.8, -5.094, 0, 1.067, 12.491, 0, 1.367, -17.369, 0, 1.7, 16.202, 0, 2.033, -13.618, 0, 2.367, 12.138, 0, 2.733, -7.491, 0, 3.333, 4.085, 0, 3.667, -3.647, 0, 3.933, 6.696, 0, 4.167, -14.485, 0, 4.467, 20.181, 0, 4.8, -15.126, 0, 5.267, 9.379, 0, 5.633, -8.666, 0, 5.933, 8.119, 0, 6.233, -9.642, 0, 6.567, 9.909, 0, 6.9, -8.155, 0, 7.2, 6.361, 0, 7.5, -2.336, 0, 7.667, -0.789, 0, 7.9, -5.057, 0, 8.233, 9.583, 0, 8.567, -5.168, 0, 8.7, -3.936, 0, 8.833, -5.994, 0, 9.167, 14.169, 0, 9.467, -13.48, 0, 9.767, 11.984, 0, 10.067, -13.011, 0, 10.367, 13.101, 0, 10.7, -10.752, 0, 11, 8.02, 0, 11.567, -7.25, 0, 11.867, 13.925, 0, 12.167, -13.733, 0, 12.433, 10.99, 0, 12.667, -10.18, 0, 12.9, 11.411, 0, 13.167, -12.593, 0, 13.433, 11.543, 0, 13.733, -8.192, 0, 14.067, 6.025, 0, 14.367, -5.595, 0, 14.7, 7.371, 1, 14.711, 7.371, 14.722, -11.659, 14.733, -11.659]}, {"Target": "Parameter", "Id": "Param55", "Segments": [0, -10.57, 2, 0.333, -10.57, 0, 0.367, 0.385, 0, 0.533, -0.008, 0, 0.667, 0.673, 0, 0.9, -5.952, 0, 1.167, 16.604, 0, 1.433, -25.561, 0, 1.8, 26.152, 0, 2.133, -23.779, 0, 2.467, 21.57, 0, 2.8, -15.484, 0, 3.2, 5.484, 0, 3.767, -5.708, 0, 4.033, 8.727, 0, 4.267, -19.093, 0, 4.533, 28.127, 0, 4.9, -25.259, 0, 5.3, 16.091, 0, 5.7, -14.944, 0, 6.033, 14.875, 0, 6.333, -17.081, 0, 6.667, 17.819, 0, 7, -15.71, 0, 7.3, 13.108, 0, 7.633, -6.989, 0, 7.833, -2.158, 0, 8, -4.678, 0, 8.3, 14.134, 0, 8.667, -10.958, 0, 9.233, 19.306, 0, 9.567, -21.922, 0, 9.867, 20.772, 0, 10.167, -22.114, 0, 10.467, 22.385, 0, 10.8, -19.78, 0, 11.1, 15.751, 0, 11.533, -9.37, 0, 11.967, 19.548, 0, 12.267, -22.023, 0, 12.533, 17.736, 0, 12.767, -14.091, 0, 13.033, 14.733, 0, 13.267, -18.505, 0, 13.567, 19.447, 0, 13.867, -16.183, 0, 14.167, 12.812, 0, 14.467, -11.567, 0, 14.733, -10.57]}, {"Target": "Parameter", "Id": "Param28", "Segments": [0, -7.12, 2, 0.333, -7.12, 0, 1.533, 7.36, 0, 2.733, -7.12, 0, 3.933, 7.36, 0, 5.133, -7.12, 0, 6.333, 7.36, 0, 7.533, -7.12, 0, 8.733, 7.36, 0, 9.933, -7.12, 0, 11.133, 7.36, 0, 12.333, -7.12, 0, 13.533, 7.36, 0, 14.733, -7.12]}, {"Target": "Parameter", "Id": "Param29", "Segments": [0, -1.278, 2, 0.333, -1.278, 0, 0.8, -4.74, 0, 2, 5.1, 0, 3.2, -4.74, 0, 4.4, 5.1, 1, 4.644, 5.1, 4.889, 2.706, 5.133, -1.278, 1, 5.289, -3.812, 5.444, -4.74, 5.6, -4.74, 0, 6.8, 5.1, 0, 8, -4.74, 0, 9.2, 5.1, 1, 9.444, 5.1, 9.689, 2.706, 9.933, -1.278, 1, 10.089, -3.812, 10.244, -4.74, 10.4, -4.74, 0, 11.6, 5.1, 0, 12.8, -4.74, 0, 14, 5.1, 0, 14.733, -1.278]}, {"Target": "Parameter", "Id": "Param30", "Segments": [0, -5.909, 2, 0.333, -5.909, 0, 1.367, 2.533, 0, 2.567, -6.287, 0, 3.767, 2.533, 0, 4.967, -6.287, 1, 5.022, -6.287, 5.078, -6.569, 5.133, -5.909, 1, 5.478, -1.813, 5.822, 2.533, 6.167, 2.533, 0, 7.367, -6.287, 0, 8.567, 2.533, 0, 9.767, -6.287, 1, 9.822, -6.287, 9.878, -6.569, 9.933, -5.909, 1, 10.278, -1.813, 10.622, 2.533, 10.967, 2.533, 0, 12.167, -6.287, 0, 13.367, 2.533, 0, 14.567, -6.287, 0, 14.733, -5.909]}, {"Target": "Parameter", "Id": "Param31", "Segments": [0, -0.551, 2, 0.333, -0.551, 0, 0.833, 3.018, 0, 2.033, -6.18, 0, 3.233, 3.018, 0, 4.433, -6.18, 1, 4.666, -6.18, 4.9, -4.115, 5.133, -0.551, 1, 5.3, 1.995, 5.466, 3.018, 5.633, 3.018, 0, 6.833, -6.18, 0, 8.033, 3.018, 0, 9.233, -6.18, 1, 9.466, -6.18, 9.7, -4.115, 9.933, -0.551, 1, 10.1, 1.995, 10.266, 3.018, 10.433, 3.018, 0, 11.633, -6.18, 0, 12.833, 3.018, 0, 14.033, -6.18, 0, 14.733, -0.551]}, {"Target": "Parameter", "Id": "Param32", "Segments": [0, -2.714, 2, 0.333, -2.714, 0, 1.433, 2.284, 0, 2.633, -2.796, 0, 3.833, 2.284, 0, 5.033, -2.796, 1, 5.066, -2.796, 5.1, -2.933, 5.133, -2.714, 1, 5.5, -0.309, 5.866, 2.284, 6.233, 2.284, 0, 7.433, -2.796, 0, 8.633, 2.284, 0, 9.833, -2.796, 1, 9.866, -2.796, 9.9, -2.933, 9.933, -2.714, 1, 10.3, -0.309, 10.666, 2.284, 11.033, 2.284, 0, 12.233, -2.796, 0, 13.433, 2.284, 0, 14.633, -2.796, 0, 14.733, -2.714]}, {"Target": "Parameter", "Id": "Param33", "Segments": [0, -2.392, 2, 0.333, -2.392, 0, 0.867, 3.279, 0, 2.067, -10.047, 0, 3.267, 3.279, 0, 4.467, -10.047, 1, 4.689, -10.047, 4.911, -7.319, 5.133, -2.392, 1, 5.311, 1.55, 5.489, 3.279, 5.667, 3.279, 0, 6.867, -10.047, 0, 8.067, 3.279, 0, 9.267, -10.047, 1, 9.489, -10.047, 9.711, -7.319, 9.933, -2.392, 1, 10.111, 1.55, 10.289, 3.279, 10.467, 3.279, 0, 11.667, -10.047, 0, 12.867, 3.279, 0, 14.067, -10.047, 0, 14.733, -2.392]}, {"Target": "Parameter", "Id": "Param34", "Segments": [0, -21.182, 2, 0.333, -21.182, 0, 1.467, 11.7, 0, 2.667, -21.42, 0, 3.867, 11.7, 0, 5.067, -21.42, 1, 5.089, -21.42, 5.111, -22.036, 5.133, -21.182, 1, 5.511, -6.656, 5.889, 11.7, 6.267, 11.7, 0, 7.467, -21.42, 0, 8.667, 11.7, 0, 9.867, -21.42, 1, 9.889, -21.42, 9.911, -22.036, 9.933, -21.182, 1, 10.311, -6.656, 10.689, 11.7, 11.067, 11.7, 0, 12.267, -21.42, 0, 13.467, 11.7, 0, 14.667, -21.42, 0, 14.733, -21.182]}, {"Target": "Parameter", "Id": "Param84", "Segments": [0, 0, 2, 0.333, 0, 0, 1.533, -6.9, 0, 2.733, 0, 0, 3.933, -6.9, 0, 5.133, 0, 0, 6.333, -6.9, 0, 7.533, 0, 0, 8.733, -6.9, 0, 9.933, 0, 0, 11.133, -6.9, 0, 12.333, 0, 0, 13.533, -6.9, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param85", "Segments": [0, 0, 2, 0.333, 0, 0, 0.933, 4.74, 0, 2.133, -4.74, 1, 2.333, -4.74, 2.533, -3.16, 2.733, 0, 1, 2.933, 3.16, 3.133, 4.74, 3.333, 4.74, 0, 4.533, -4.74, 1, 4.733, -4.74, 4.933, -3.16, 5.133, 0, 1, 5.333, 3.16, 5.533, 4.74, 5.733, 4.74, 0, 6.933, -4.74, 1, 7.133, -4.74, 7.333, -3.16, 7.533, 0, 1, 7.733, 3.16, 7.933, 4.74, 8.133, 4.74, 0, 9.333, -4.74, 1, 9.533, -4.74, 9.733, -3.16, 9.933, 0, 1, 10.133, 3.16, 10.333, 4.74, 10.533, 4.74, 0, 11.733, -4.74, 1, 11.933, -4.74, 12.133, -3.16, 12.333, 0, 1, 12.533, 3.16, 12.733, 4.74, 12.933, 4.74, 0, 14.133, -4.74, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param83", "Segments": [0, 0, 2, 0.333, 0, 0, 1.533, 3.96, 0, 2.733, 0, 0, 3.933, 3.96, 0, 5.133, 0, 0, 6.333, 3.96, 0, 7.533, 0, 0, 8.733, 3.96, 0, 9.933, 0, 0, 11.133, 3.96, 0, 12.333, 0, 0, 13.533, 3.96, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param86", "Segments": [0, 1, 2, 0.333, 1, 2, 2.033, 1, 0, 2.133, 0, 0, 2.233, 1, 2, 2.733, 1, 2, 4.433, 1, 0, 4.533, 0, 0, 4.633, 1, 2, 5.133, 1, 2, 6.833, 1, 0, 6.933, 0, 0, 7.033, 1, 2, 7.533, 1, 2, 9.233, 1, 0, 9.333, 0, 0, 9.433, 1, 2, 9.933, 1, 2, 11.633, 1, 0, 11.733, 0, 0, 11.833, 1, 2, 12.333, 1, 2, 14.033, 1, 0, 14.133, 0, 0, 14.233, 1, 2, 14.733, 1]}, {"Target": "Parameter", "Id": "Param95", "Segments": [0, 0, 2, 0.333, 0, 0, 1.533, 10, 0, 2.733, 0, 0, 3.933, 10, 0, 5.133, 0, 0, 6.333, 10, 0, 7.533, 0, 0, 8.733, 10, 0, 9.933, 0, 0, 11.133, 10, 0, 12.333, 0, 0, 13.533, 10, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param92", "Segments": [0, 0, 2, 0.333, 0, 0, 0.933, -12, 0, 2.133, 12, 1, 2.333, 12, 2.533, 8, 2.733, 0, 1, 2.933, -8, 3.133, -12, 3.333, -12, 0, 4.533, 12, 1, 4.733, 12, 4.933, 8, 5.133, 0, 1, 5.333, -8, 5.533, -12, 5.733, -12, 0, 6.933, 12, 1, 7.133, 12, 7.333, 8, 7.533, 0, 1, 7.733, -8, 7.933, -12, 8.133, -12, 0, 9.333, 12, 1, 9.533, 12, 9.733, 8, 9.933, 0, 1, 10.133, -8, 10.333, -12, 10.533, -12, 0, 11.733, 12, 1, 11.933, 12, 12.133, 8, 12.333, 0, 1, 12.533, -8, 12.733, -12, 12.933, -12, 0, 14.133, 12, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param93", "Segments": [0, 0, 2, 0.333, 0, 0, 1.533, -12, 0, 2.733, 0, 0, 3.933, -12, 0, 5.133, 0, 0, 6.333, -12, 0, 7.533, 0, 0, 8.733, -12, 0, 9.933, 0, 0, 11.133, -12, 0, 12.333, 0, 0, 13.533, -12, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param96", "Segments": [0, 0, 2, 0.333, 0, 0, 0.467, -16, 0, 0.633, 0, 0, 0.8, -16, 0, 0.967, 0, 2, 2.733, 0, 0, 2.867, -16, 0, 3.033, 0, 0, 3.2, -16, 0, 3.367, 0, 2, 5.133, 0, 0, 5.267, -16, 0, 5.433, 0, 0, 5.6, -16, 0, 5.767, 0, 2, 7.533, 0, 0, 7.667, -16, 0, 7.833, 0, 0, 8, -16, 0, 8.167, 0, 2, 9.933, 0, 0, 10.067, -16, 0, 10.233, 0, 0, 10.4, -16, 0, 10.567, 0, 2, 12.333, 0, 0, 12.467, -16, 0, 12.633, 0, 0, 12.8, -16, 0, 12.967, 0, 2, 14.733, 0]}, {"Target": "Parameter", "Id": "Param97", "Segments": [0, 0, 2, 0.333, 0, 0, 0.567, -14, 1, 0.589, -14, 0.611, -7.166, 0.633, 0, 1, 0.666, 10.749, 0.7, 14, 0.733, 14, 0, 0.9, -14, 1, 0.922, -14, 0.945, -0.363, 0.967, 0, 1, 1.556, 9.612, 2.144, 14, 2.733, 14, 0, 2.967, -14, 1, 2.989, -14, 3.011, -7.166, 3.033, 0, 1, 3.066, 10.749, 3.1, 14, 3.133, 14, 0, 3.3, -14, 1, 3.322, -14, 3.345, -0.363, 3.367, 0, 1, 3.956, 9.612, 4.544, 14, 5.133, 14, 0, 5.367, -14, 1, 5.389, -14, 5.411, -7.166, 5.433, 0, 1, 5.466, 10.749, 5.5, 14, 5.533, 14, 0, 5.7, -14, 1, 5.722, -14, 5.745, -0.363, 5.767, 0, 1, 6.356, 9.612, 6.944, 14, 7.533, 14, 0, 7.767, -14, 1, 7.789, -14, 7.811, -7.166, 7.833, 0, 1, 7.866, 10.749, 7.9, 14, 7.933, 14, 0, 8.1, -14, 1, 8.122, -14, 8.145, -0.363, 8.167, 0, 1, 8.756, 9.612, 9.344, 14, 9.933, 14, 0, 10.167, -14, 1, 10.189, -14, 10.211, -7.166, 10.233, 0, 1, 10.266, 10.749, 10.3, 14, 10.333, 14, 0, 10.5, -14, 1, 10.522, -14, 10.545, -0.363, 10.567, 0, 1, 11.156, 9.612, 11.744, 14, 12.333, 14, 0, 12.567, -14, 1, 12.589, -14, 12.611, -7.166, 12.633, 0, 1, 12.666, 10.749, 12.7, 14, 12.733, 14, 0, 12.9, -14, 0, 12.967, 0, 2, 14.733, 0]}, {"Target": "Parameter", "Id": "Param89", "Segments": [0, 0, 2, 0.333, 0, 0, 1.533, 18.72, 0, 2.733, 0, 0, 3.933, 18.72, 0, 5.133, 0, 0, 6.333, 18.72, 0, 7.533, 0, 0, 8.733, 18.72, 0, 9.933, 0, 0, 11.133, 18.72, 0, 12.333, 0, 0, 13.533, 18.72, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param90", "Segments": [0, 0, 2, 0.333, 0, 0, 0.933, -19, 0, 2.133, 19, 1, 2.333, 19, 2.533, 12.667, 2.733, 0, 1, 2.933, -12.667, 3.133, -19, 3.333, -19, 0, 4.533, 19, 1, 4.733, 19, 4.933, 12.667, 5.133, 0, 1, 5.333, -12.667, 5.533, -19, 5.733, -19, 0, 6.933, 19, 1, 7.133, 19, 7.333, 12.667, 7.533, 0, 1, 7.733, -12.667, 7.933, -19, 8.133, -19, 0, 9.333, 19, 1, 9.533, 19, 9.733, 12.667, 9.933, 0, 1, 10.133, -12.667, 10.333, -19, 10.533, -19, 0, 11.733, 19, 1, 11.933, 19, 12.133, 12.667, 12.333, 0, 1, 12.533, -12.667, 12.733, -19, 12.933, -19, 0, 14.133, 19, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation56", "Segments": [0, 0, 2, 0.333, 0, 0, 0.933, 9, 0, 1.533, -9, 0, 2.133, 9, 0, 2.733, 0, 0, 3.333, 9, 0, 3.933, -9, 0, 4.533, 9, 0, 5.133, 0, 0, 5.733, 9, 0, 6.333, -9, 0, 6.933, 9, 0, 7.533, 0, 0, 8.133, 9, 0, 8.733, -9, 0, 9.333, 9, 0, 9.933, 0, 0, 10.533, 9, 0, 11.133, -9, 0, 11.733, 9, 0, 12.333, 0, 0, 12.933, 9, 0, 13.533, -9, 0, 14.133, 9, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param98", "Segments": [0, -6, 2, 0.333, -6, 0, 0.633, 0, 0, 1.233, -12, 0, 1.833, 12, 0, 2.433, -12, 0, 3.033, 0, 0, 3.633, -12, 0, 4.233, 12, 0, 4.833, -12, 1, 4.933, -12, 5.033, -10, 5.133, -6, 1, 5.233, -2, 5.333, 0, 5.433, 0, 0, 6.033, -12, 0, 6.633, 12, 0, 7.233, -12, 0, 7.833, 0, 0, 8.433, -12, 0, 9.033, 12, 0, 9.633, -12, 1, 9.733, -12, 9.833, -10, 9.933, -6, 1, 10.033, -2, 10.133, 0, 10.233, 0, 0, 10.833, -12, 0, 11.433, 12, 0, 12.033, -12, 0, 12.633, 0, 0, 13.233, -12, 0, 13.833, 12, 0, 14.433, -12, 0, 14.733, -6]}, {"Target": "Parameter", "Id": "Param99", "Segments": [0, -10.921, 2, 0.333, -10.921, 0, 0.9, 0, 0, 1.5, -11, 0, 2.1, 11, 0, 2.7, -11, 0, 3.3, 0, 0, 3.9, -11, 0, 4.5, 11, 0, 5.1, -11, 1, 5.111, -11, 5.122, -11.212, 5.133, -10.921, 1, 5.322, -5.97, 5.511, 0, 5.7, 0, 0, 6.3, -11, 0, 6.9, 11, 0, 7.5, -11, 0, 8.1, 0, 0, 8.7, -11, 0, 9.3, 11, 0, 9.9, -11, 1, 9.911, -11, 9.922, -11.212, 9.933, -10.921, 1, 10.122, -5.97, 10.311, 0, 10.5, 0, 0, 11.1, -11, 0, 11.7, 11, 0, 12.3, -11, 0, 12.9, 0, 0, 13.5, -11, 0, 14.1, 11, 0, 14.7, -11, 0, 14.733, -10.921]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation67", "Segments": [0, -10.939, 2, 0.333, -10.939, 0, 0.533, -25, 0, 1.133, 0, 0, 1.733, -25, 0, 2.333, 25, 0, 2.933, -25, 0, 3.533, 0, 0, 4.133, -25, 0, 4.733, 25, 1, 4.866, 25, 5, 10.959, 5.133, -10.939, 1, 5.2, -21.888, 5.266, -25, 5.333, -25, 0, 5.933, 0, 0, 6.533, -25, 0, 7.133, 25, 0, 7.733, -25, 0, 8.333, 0, 0, 8.933, -25, 0, 9.533, 25, 1, 9.666, 25, 9.8, 10.959, 9.933, -10.939, 1, 10, -21.888, 10.066, -25, 10.133, -25, 0, 10.733, 0, 0, 11.333, -25, 0, 11.933, 25, 0, 12.533, -25, 0, 13.133, 0, 0, 13.733, -25, 0, 14.333, 25, 0, 14.733, -10.939]}, {"Target": "Parameter", "Id": "Param281", "Segments": [0, 0, 2, 0.333, 0, 0, 1.667, 1, 0, 3.133, 0, 0, 4.6, 1, 0, 6.067, 0, 0, 7.533, 1, 0, 8.9, 0, 0, 10.333, 1, 0, 11.8, 0, 0, 13.267, 1, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param197mao2", "Segments": [0, 0, 2, 0.333, 0, 0, 0.967, 2.894, 0, 2.533, -2.454, 1, 2.733, -2.454, 2.933, -1.604, 3.133, 0, 1, 3.378, 1.96, 3.622, 2.894, 3.867, 2.894, 0, 5.333, -2.454, 1, 5.578, -2.454, 5.822, -1.867, 6.067, 0, 1, 6.278, 1.612, 6.489, 2.894, 6.7, 2.894, 0, 8.267, -2.454, 1, 8.478, -2.454, 8.689, -1.652, 8.9, 0, 1, 9.144, 1.913, 9.389, 2.894, 9.633, 2.894, 0, 11.2, -2.454, 1, 11.4, -2.454, 11.6, -1.646, 11.8, 0, 1, 12.033, 1.92, 12.267, 2.894, 12.5, 2.894, 0, 14.033, -2.454, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param216mao2", "Segments": [0, -8.88, 2, 0.333, -8.88, 0, 5.133, -8.9, 2, 9.933, -8.9, 0, 14.733, -8.88]}, {"Target": "Parameter", "Id": "Param212mao2", "Segments": [0, 0, 2, 0.333, 0, 0, 0.467, -16, 0, 0.633, 12.568, 0, 0.8, -16, 0, 0.967, 0, 2, 2.733, 0, 2, 5.133, 0, 0, 5.267, -16, 0, 5.433, 12.568, 0, 5.6, -16, 0, 5.767, 0, 2, 7.533, 0, 2, 9.933, 0, 0, 10.067, -16, 0, 10.233, 12.568, 0, 10.4, -16, 0, 10.567, 0, 2, 12.333, 0, 2, 14.733, 0]}, {"Target": "Parameter", "Id": "Param214mao2", "Segments": [0, 0, 2, 0.333, 0, 2, 1.4, 0, 0, 1.533, 16, 0, 1.7, 0, 0, 1.867, 16, 0, 2.067, 0, 2, 2.733, 0, 2, 5.133, 0, 2, 6.2, 0, 0, 6.333, 16, 0, 6.5, 0, 0, 6.667, 16, 0, 6.867, 0, 2, 7.533, 0, 2, 9.933, 0, 2, 11, 0, 0, 11.133, 16, 0, 11.3, 0, 0, 11.467, 16, 0, 11.667, 0, 2, 12.333, 0, 2, 14.733, 0]}, {"Target": "Parameter", "Id": "Param215mao2", "Segments": [0, 0, 2, 0.333, 0, 2, 1.4, 0, 0, 1.467, -14, 0, 1.633, 15, 0, 1.8, -8, 0, 1.933, 9, 0, 2.167, 0, 2, 2.733, 0, 2, 5.133, 0, 2, 6.2, 0, 0, 6.267, -14, 0, 6.433, 15, 0, 6.6, -8, 0, 6.733, 9, 0, 6.967, 0, 2, 7.533, 0, 2, 9.933, 0, 2, 11, 0, 0, 11.067, -14, 0, 11.233, 15, 0, 11.4, -8, 0, 11.533, 9, 0, 11.767, 0, 2, 12.333, 0, 2, 14.733, 0]}, {"Target": "Parameter", "Id": "Param198mao2", "Segments": [0, 0, 2, 0.333, 0, 0, 1.467, 30, 0, 2.733, 0, 0, 3.8, 30, 0, 5.133, 0, 0, 6.267, 30, 0, 7.533, 0, 0, 8.6, 30, 0, 9.933, 0, 0, 11.067, 30, 0, 12.333, 0, 0, 13.4, 30, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation143mao2", "Segments": [0, 0, 2, 0.333, 0, 0, 0.867, 17, 0, 1.533, 0, 0, 2.133, 17, 2, 2.733, 17, 2, 3.267, 17, 0, 3.933, 0, 0, 4.533, 17, 0, 5.133, 0, 0, 5.667, 17, 0, 6.333, 0, 0, 6.933, 17, 2, 7.533, 17, 2, 8.067, 17, 0, 8.733, 0, 0, 9.333, 17, 0, 9.933, 0, 0, 10.467, 17, 0, 11.133, 0, 0, 11.733, 17, 2, 12.333, 17, 2, 12.867, 17, 0, 13.533, 0, 0, 14.133, 17, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation144mao2", "Segments": [0, -10.82, 2, 0.333, -10.82, 0, 0.5, -19.438, 0, 1.033, 17.12, 0, 1.7, -19.438, 0, 2.333, 17.12, 1, 2.466, 17.12, 2.6, 6.024, 2.733, -10.82, 1, 2.789, -17.839, 2.844, -19.438, 2.9, -19.438, 0, 3.433, 17.12, 0, 4.1, -19.438, 0, 4.733, 17.12, 1, 4.866, 17.12, 5, 6.024, 5.133, -10.82, 1, 5.189, -17.839, 5.244, -19.438, 5.3, -19.438, 0, 5.833, 17.12, 0, 6.5, -19.438, 0, 7.133, 17.12, 1, 7.266, 17.12, 7.4, 6.024, 7.533, -10.82, 1, 7.589, -17.839, 7.644, -19.438, 7.7, -19.438, 0, 8.233, 17.12, 0, 8.9, -19.438, 0, 9.533, 17.12, 1, 9.666, 17.12, 9.8, 6.024, 9.933, -10.82, 1, 9.989, -17.839, 10.044, -19.438, 10.1, -19.438, 0, 10.633, 17.12, 0, 11.3, -19.438, 0, 11.933, 17.12, 1, 12.066, 17.12, 12.2, 6.024, 12.333, -10.82, 1, 12.389, -17.839, 12.444, -19.438, 12.5, -19.438, 0, 13.033, 17.12, 0, 13.7, -19.438, 0, 14.333, 17.12, 0, 14.733, -10.82]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation145mao2", "Segments": [0, 8.46, 2, 0.333, 8.46, 0, 0.7, -17.322, 0, 1.233, 16.411, 0, 1.9, -17.322, 0, 2.533, 16.411, 1, 2.6, 16.411, 2.666, 15.759, 2.733, 8.46, 1, 2.855, -4.922, 2.978, -17.322, 3.1, -17.322, 0, 3.633, 16.411, 0, 4.3, -17.322, 0, 4.933, 16.411, 1, 5, 16.411, 5.066, 15.759, 5.133, 8.46, 1, 5.255, -4.922, 5.378, -17.322, 5.5, -17.322, 0, 6.033, 16.411, 0, 6.7, -17.322, 0, 7.333, 16.411, 1, 7.4, 16.411, 7.466, 15.759, 7.533, 8.46, 1, 7.655, -4.922, 7.778, -17.322, 7.9, -17.322, 0, 8.433, 16.411, 0, 9.1, -17.322, 0, 9.733, 16.411, 1, 9.8, 16.411, 9.866, 15.759, 9.933, 8.46, 1, 10.055, -4.922, 10.178, -17.322, 10.3, -17.322, 0, 10.833, 16.411, 0, 11.5, -17.322, 0, 12.133, 16.411, 1, 12.2, 16.411, 12.266, 15.759, 12.333, 8.46, 1, 12.455, -4.922, 12.578, -17.322, 12.7, -17.322, 0, 13.233, 16.411, 0, 13.9, -17.322, 0, 14.533, 16.411, 0, 14.733, 8.46]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation146mao2", "Segments": [0, 28.83, 2, 0.333, 28.83, 0, 0.933, -30, 0, 1.467, 29.224, 0, 2.133, -30, 0, 2.733, 29.224, 0, 3.333, -30, 0, 3.867, 29.224, 0, 4.533, -30, 0, 5.133, 29.224, 0, 5.733, -30, 0, 6.267, 29.224, 0, 6.933, -30, 0, 7.533, 29.224, 0, 8.133, -30, 0, 8.667, 29.224, 0, 9.333, -30, 0, 9.933, 29.224, 0, 10.533, -30, 0, 11.067, 29.224, 0, 11.733, -30, 0, 12.333, 29.224, 0, 12.933, -30, 0, 13.467, 29.224, 0, 14.133, -30, 0, 14.733, 28.83]}, {"Target": "Parameter", "Id": "Param100", "Segments": [0, 0, 2, 0.333, 0, 0, 1.533, -2.04, 0, 2.733, 0, 0, 3.933, -2.04, 0, 5.133, 0, 0, 6.333, -2.04, 0, 7.533, 0, 0, 8.733, -2.04, 0, 9.933, 0, 0, 11.133, -2.04, 0, 12.333, 0, 0, 13.533, -2.04, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param105", "Segments": [0, 0, 2, 0.333, 0, 0, 0.933, 16, 0, 2.133, -16, 1, 2.333, -16, 2.533, -6.321, 2.733, 0, 1, 3.133, 12.641, 3.533, 16, 3.933, 16, 0, 5.133, 0, 0, 5.733, 16, 0, 6.933, -16, 1, 7.133, -16, 7.333, -6.321, 7.533, 0, 1, 7.933, 12.641, 8.333, 16, 8.733, 16, 0, 9.933, 0, 0, 10.533, 16, 0, 11.733, -16, 1, 11.933, -16, 12.133, -6.321, 12.333, 0, 1, 12.733, 12.641, 13.133, 16, 13.533, 16, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param108", "Segments": [0, 0, 2, 0.333, 0, 0, 1.533, 4, 0, 2.733, 0, 0, 3.933, 4, 0, 5.133, 0, 0, 6.333, 4, 0, 7.533, 0, 0, 8.733, 4, 0, 9.933, 0, 0, 11.133, 4, 0, 12.333, 0, 0, 13.533, 4, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param109", "Segments": [0, 0, 2, 0.333, 0, 0, 0.933, -4, 0, 2.133, 4.46, 1, 2.333, 4.46, 2.533, 2.811, 2.733, 0, 1, 2.933, -2.811, 3.133, -4, 3.333, -4, 0, 4.533, 4.46, 1, 4.733, 4.46, 4.933, 2.811, 5.133, 0, 1, 5.333, -2.811, 5.533, -4, 5.733, -4, 0, 6.933, 4.46, 1, 7.133, 4.46, 7.333, 2.811, 7.533, 0, 1, 7.733, -2.811, 7.933, -4, 8.133, -4, 0, 9.333, 4.46, 1, 9.533, 4.46, 9.733, 2.811, 9.933, 0, 1, 10.133, -2.811, 10.333, -4, 10.533, -4, 0, 11.733, 4.46, 1, 11.933, 4.46, 12.133, 2.811, 12.333, 0, 1, 12.533, -2.811, 12.733, -4, 12.933, -4, 0, 14.133, 4.46, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param101", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, -21, 0, 0.533, 0, 0, 0.633, -21, 0, 0.733, 0, 2, 2.733, 0, 0, 2.8, -21, 0, 2.933, 0, 0, 3.033, -21, 0, 3.133, 0, 0, 3.933, -21, 0, 5.133, 0, 0, 5.2, -21, 0, 5.333, 0, 0, 5.433, -21, 0, 5.533, 0, 2, 7.533, 0, 0, 7.6, -21, 0, 7.733, 0, 0, 7.833, -21, 0, 7.933, 0, 0, 8.733, -21, 0, 9.933, 0, 0, 10, -21, 0, 10.133, 0, 0, 10.233, -21, 0, 10.333, 0, 2, 12.333, 0, 0, 12.4, -21, 0, 12.533, 0, 0, 12.633, -21, 0, 12.733, 0, 0, 13.533, -21, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param106", "Segments": [0, 0, 2, 0.333, 0, 0, 1.533, 15.405, 0, 2.733, 0, 0, 3.933, 15.405, 0, 5.133, 0, 0, 6.333, 15.405, 0, 7.533, 0, 0, 8.733, 15.405, 0, 9.933, 0, 0, 11.133, 15.405, 0, 12.333, 0, 0, 13.533, 15.405, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param104", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, -16, 0, 0.533, 0, 0, 0.633, -16, 0, 0.733, 0, 2, 2.733, 0, 0, 2.8, -16, 0, 2.933, 0, 0, 3.033, -16, 0, 3.133, 0, 0, 3.933, -16, 0, 5.133, 0, 0, 5.2, -16, 0, 5.333, 0, 0, 5.433, -16, 0, 5.533, 0, 2, 7.533, 0, 0, 7.6, -16, 0, 7.733, 0, 0, 7.833, -16, 0, 7.933, 0, 0, 8.733, -16, 0, 9.933, 0, 0, 10, -16, 0, 10.133, 0, 0, 10.233, -16, 0, 10.333, 0, 2, 12.333, 0, 0, 12.4, -16, 0, 12.533, 0, 0, 12.633, -16, 0, 12.733, 0, 0, 13.533, -16, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param103", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, 27, 0, 0.533, 0, 0, 0.633, 27, 0, 0.733, 0, 2, 2.733, 0, 0, 2.8, 27, 0, 2.933, 0, 0, 3.033, 27, 0, 3.133, 0, 0, 3.933, 27, 0, 5.133, 0, 0, 5.2, 27, 0, 5.333, 0, 0, 5.433, 27, 0, 5.533, 0, 2, 7.533, 0, 0, 7.6, 27, 0, 7.733, 0, 0, 7.833, 27, 0, 7.933, 0, 0, 8.733, 27, 0, 9.933, 0, 0, 10, 27, 0, 10.133, 0, 0, 10.233, 27, 0, 10.333, 0, 2, 12.333, 0, 1, 12.355, 0, 12.378, 27, 12.4, 27, 0, 12.533, 0, 0, 12.633, 27, 0, 12.733, 0, 0, 13.533, 27, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param102", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, 15, 0, 0.533, 0, 0, 0.633, 15, 0, 0.733, 0, 2, 2.733, 0, 0, 2.8, 15, 0, 2.933, 0, 0, 3.033, 15, 0, 3.133, 0, 0, 3.933, 15, 0, 5.133, 0, 0, 5.2, 15, 0, 5.333, 0, 0, 5.433, 15, 0, 5.533, 0, 2, 7.533, 0, 0, 7.6, 15, 0, 7.733, 0, 0, 7.833, 15, 0, 7.933, 0, 0, 8.733, 15, 0, 9.933, 0, 0, 10, 15, 0, 10.133, 0, 0, 10.233, 15, 0, 10.333, 0, 2, 12.333, 0, 0, 12.4, 15, 0, 12.533, 0, 0, 12.633, 15, 0, 12.733, 0, 0, 13.533, 15, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation71", "Segments": [0, 0, 2, 0.333, 0, 0, 0.933, 18, 0, 1.533, -18, 0, 2.133, 18, 0, 2.733, 0, 0, 3.333, 18, 0, 3.933, -18, 0, 4.533, 18, 0, 5.133, 0, 0, 5.733, 18, 0, 6.333, -18, 0, 6.933, 18, 0, 7.533, 0, 0, 8.133, 18, 0, 8.733, -18, 0, 9.333, 18, 0, 9.933, 0, 0, 10.533, 18, 0, 11.133, -18, 0, 11.733, 18, 0, 12.333, 0, 0, 12.933, 18, 0, 13.533, -18, 0, 14.133, 18, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation72", "Segments": [0, 8.085, 2, 0.333, 8.085, 0, 0.6, 0, 0, 1.2, 19, 0, 1.8, -19, 0, 2.4, 19, 0, 3, 0, 0, 3.6, 19, 0, 4.2, -19, 0, 4.8, 19, 1, 4.911, 19, 5.022, 15.11, 5.133, 8.085, 1, 5.222, 2.465, 5.311, 0, 5.4, 0, 0, 6, 19, 0, 6.6, -19, 0, 7.2, 19, 0, 7.8, 0, 0, 8.4, 19, 0, 9, -19, 0, 9.6, 19, 1, 9.711, 19, 9.822, 15.11, 9.933, 8.085, 1, 10.022, 2.465, 10.111, 0, 10.2, 0, 0, 10.8, 19, 0, 11.4, -19, 0, 12, 19, 0, 12.6, 0, 0, 13.2, 19, 0, 13.8, -19, 0, 14.4, 19, 0, 14.733, 8.085]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation73", "Segments": [0, 11.915, 2, 0.333, 11.915, 0, 0.6, 0, 0, 1.2, 28, 0, 1.8, -28, 0, 2.4, 28, 0, 3, 0, 0, 3.6, 28, 0, 4.2, -28, 0, 4.8, 28, 1, 4.911, 28, 5.022, 22.268, 5.133, 11.915, 1, 5.222, 3.633, 5.311, 0, 5.4, 0, 0, 6, 28, 0, 6.6, -28, 0, 7.2, 28, 0, 7.8, 0, 0, 8.4, 28, 0, 9, -28, 0, 9.6, 28, 1, 9.711, 28, 9.822, 22.268, 9.933, 11.915, 1, 10.022, 3.633, 10.111, 0, 10.2, 0, 0, 10.8, 28, 0, 11.4, -28, 0, 12, 28, 0, 12.6, 0, 0, 13.2, 28, 0, 13.8, -28, 0, 14.4, 28, 0, 14.733, 11.915]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation74", "Segments": [0, 12.341, 2, 0.333, 12.341, 0, 0.567, 0, 0, 1.1, 29, 0, 1.633, -29, 0, 2.167, 29, 0, 2.733, 0, 0, 3.267, 29, 0, 3.8, -29, 0, 4.333, 29, 1, 4.433, 29, 4.533, 23.209, 4.633, 12.341, 1, 4.711, 3.888, 4.789, 0, 4.867, 0, 0, 5.433, 29, 0, 5.967, -29, 0, 6.5, 29, 0, 7.033, 0, 0, 7.567, 29, 0, 8.133, -29, 0, 8.667, 29, 1, 8.767, 29, 8.867, 23.209, 8.967, 12.341, 1, 9.045, 3.888, 9.122, 0, 9.2, 0, 0, 9.733, 29, 0, 10.267, -29, 0, 10.833, 29, 0, 11.367, 0, 0, 11.9, 29, 0, 12.433, -29, 0, 12.967, 29, 0, 13.267, 12.341, 2, 14.733, 12.341]}, {"Target": "Parameter", "Id": "Param111", "Segments": [0, 0, 2, 0.333, 0, 0, 1.533, 6.54, 0, 2.733, 0, 0, 3.933, 6.54, 0, 5.133, 0, 0, 6.333, 6.54, 0, 7.533, 0, 0, 8.733, 6.54, 0, 9.933, 0, 0, 11.133, 6.54, 0, 12.333, 0, 0, 13.533, 6.54, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param112", "Segments": [0, -30, 2, 0.333, -30, 0, 2.733, 0, 2, 5.133, 0, 2, 7.533, 0, 2, 9.933, 0, 2, 12.333, 0, 0, 14.733, -30]}, {"Target": "Parameter", "Id": "Param114", "Segments": [0, 0, 2, 0.333, 0, 0, 0.933, 19, 0, 2.133, -19, 1, 2.333, -19, 2.533, -12.667, 2.733, 0, 1, 2.933, 12.667, 3.133, 19, 3.333, 19, 0, 4.533, -19, 1, 4.733, -19, 4.933, -12.667, 5.133, 0, 1, 5.333, 12.667, 5.533, 19, 5.733, 19, 0, 6.933, -19, 1, 7.133, -19, 7.333, -12.667, 7.533, 0, 1, 7.733, 12.667, 7.933, 19, 8.133, 19, 0, 9.333, -19, 1, 9.533, -19, 9.733, -12.667, 9.933, 0, 1, 10.133, 12.667, 10.333, 19, 10.533, 19, 0, 11.733, -19, 1, 11.933, -19, 12.133, -12.667, 12.333, 0, 1, 12.533, 12.667, 12.733, 19, 12.933, 19, 0, 14.133, -19, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param113", "Segments": [0, 1, 2, 0.333, 1, 2, 1.433, 1, 0, 1.533, 0, 0, 1.6, 1, 2, 2.733, 1, 2, 3.833, 1, 0, 3.933, 0, 0, 4, 1, 2, 5.133, 1, 2, 6.233, 1, 0, 6.333, 0, 0, 6.4, 1, 2, 7.533, 1, 2, 8.633, 1, 0, 8.733, 0, 0, 8.8, 1, 2, 9.933, 1, 2, 11.033, 1, 0, 11.133, 0, 0, 11.2, 1, 2, 12.333, 1, 2, 13.433, 1, 0, 13.533, 0, 0, 13.6, 1, 2, 14.733, 1]}, {"Target": "Parameter", "Id": "Param272", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param293", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param273", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "ParamCheek", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param268", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param269", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param193", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param192", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param196", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param282", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param183", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param194", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param195", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param184", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param289", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param185", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param290", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param291", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param186", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param187", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param188", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param292", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param189", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param190", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param191", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param266", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param24", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param26", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param200", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param286", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param287", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param288", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param202", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param205", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param203", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param204", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param21", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param213", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param215", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param218", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param219", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param220", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param197", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param198", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param199", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param208", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param279", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param212", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param228", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param270", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param271", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param283", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param230", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param231", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param222", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param207", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param236", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param238", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param239", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param240", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param241", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param242", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param244", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param18", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param20", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param275", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param224", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param225", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param227", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param226", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param232", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param285", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param274", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param276", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param142", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param143", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param144", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param147", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param145", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param134", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param246", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param141", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param182", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param247", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param229", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param245", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param117", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param140", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param262", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param263", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param115", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param116", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param260", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param261", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param133", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param139", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param257", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param137", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param138", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param135", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param176", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param136", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param177", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param251", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param91", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param2", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param87", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param88", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param94", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param218mao2", "Segments": [0, 1, 0, 14.733, 1]}, {"Target": "Parameter", "Id": "Param211mao2", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param217mao2", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param202mao2", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param203mao2", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param204mao2", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param199mao2", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param200mao2", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param201mao2", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param208mao2", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param209mao2", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param210mao2", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param205mao2", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param206mao2", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param207mao2", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param213mao2", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param219mao2", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param264", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param107", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param284", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param148", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param146", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param110", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param248", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param249", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param206", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param253", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param254", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param158", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param149", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param150", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param151", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param255", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param256", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param153", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param152", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param155", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param154", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param181", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param180", "Segments": [0, 0.5, 0, 14.733, 0.5]}, {"Target": "Parameter", "Id": "Param156", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param157", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param159", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param160", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param119", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param265", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param118", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param120", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param121", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param161", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param259", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param123", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param124", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param130", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param131", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param178", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param179", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param125", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param126", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param127", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param128", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param171", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param173", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param172", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param122", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param170", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param129", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param132", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param250", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param258", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param162", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param169", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param174", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param175", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param163", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param164", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param165", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param166", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param167", "Segments": [0, 0, 0, 14.733, 0]}, {"Target": "Parameter", "Id": "Param168", "Segments": [0, 0, 0, 14.733, 0]}], "UserData": [{"Time": 0.0, "Value": ""}, {"Time": 0.533, "Value": ""}, {"Time": 14.233, "Value": ""}]}