{"Version": 3, "Meta": {"Duration": 8.8, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 355, "TotalSegmentCount": 3479, "TotalPointCount": 4117, "UserDataCount": 3, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, -2.46, 0, 0.933, 2.82, 1, 1.078, 2.82, 1.222, 2.611, 1.367, 1.8, 1, 1.422, 1.488, 1.478, 1.211, 1.533, -0.092, 1, 1.644, -2.698, 1.756, -10.92, 1.867, -10.92, 0, 2.067, -9.429, 1, 2.178, -9.429, 2.289, -10.086, 2.4, -10.397, 1, 2.589, -10.926, 2.778, -11.006, 2.967, -11.006, 0, 3.433, -9.84, 0, 3.7, -10.38, 1, 3.8, -10.38, 3.9, -4.948, 4, 1.474, 1, 4.1, 7.896, 4.2, 9.66, 4.3, 9.66, 0, 4.5, 8.313, 1, 4.622, 8.313, 4.745, 8.955, 4.867, 9, 1, 5.167, 9.111, 5.467, 9.14, 5.767, 9.243, 1, 5.856, 9.273, 5.944, 9.869, 6.033, 9.869, 0, 6.367, 2.225, 1, 6.456, 2.225, 6.544, 3.871, 6.633, 3.905, 1, 7.089, 4.077, 7.544, 4.161, 8, 4.339, 1, 8.111, 4.383, 8.222, 5.652, 8.333, 5.652, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, -5.22, 1, 0.478, -5.22, 0.555, -5.368, 0.633, -4.447, 1, 0.711, -3.526, 0.789, 14.824, 0.867, 14.824, 1, 0.9, 14.824, 0.934, 13.308, 0.967, 13.101, 1, 1.1, 12.275, 1.234, 12.046, 1.367, 11.027, 1, 1.456, 10.347, 1.544, 6.686, 1.633, 2.819, 1, 1.733, -1.532, 1.833, -8.46, 1.933, -8.46, 0, 2.133, -6.74, 0, 3.767, -7.528, 0, 3.967, 1.38, 1, 4.011, 1.38, 4.056, 1.662, 4.1, 0.577, 1, 4.189, -1.593, 4.278, -7.86, 4.367, -7.86, 1, 4.556, -7.86, 4.744, -6.972, 4.933, -6.78, 1, 5.244, -6.464, 5.556, -6.452, 5.867, -6.452, 0, 6, -9.441, 0, 6.3, 13.869, 0, 6.567, -1.911, 1, 6.634, -1.911, 6.7, -1.779, 6.767, 0.814, 1, 6.845, 3.839, 6.922, 8.626, 7, 8.626, 0, 7.333, -6.74, 1, 7.4, -6.74, 7.466, -0.117, 7.533, 0.334, 1, 7.711, 1.535, 7.889, 1.706, 8.067, 1.706, 0, 8.4, -1.44, 0, 8.667, 3.569, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 2, 0.333, 0, 1, 0.378, 0, 0.422, 4.716, 0.467, 5, 1, 0.622, 5.993, 0.778, 6.2, 0.933, 6.2, 0, 1.2, 5.149, 0, 1.6, 11.339, 1, 1.733, 11.339, 1.867, 3.836, 2, 2.997, 1, 2.144, 2.087, 2.289, 1.484, 2.433, 1.426, 1, 2.855, 1.258, 3.278, 1.179, 3.7, 1.04, 1, 3.733, 1.029, 3.767, -0.28, 3.8, -0.28, 1, 3.967, -0.28, 4.133, 10.618, 4.3, 12, 1, 4.489, 13.566, 4.678, 13.402, 4.867, 13.402, 1, 5.178, 13.402, 5.489, 13.177, 5.8, 12.531, 1, 5.944, 12.231, 6.089, 11.909, 6.233, 11.339, 1, 6.311, 11.032, 6.389, 7.912, 6.467, 6.019, 1, 6.511, 4.938, 6.556, 4.174, 6.6, 4.174, 0, 7.033, 9.628, 0, 8, 5.635, 0, 8.333, 6.474, 0, 8.667, -0.823, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 2, 0.333, 1, 0, 0.4, 0, 0, 0.5, 1, 2, 0.8, 1, 2, 1.033, 1, 2, 1.3, 1, 0, 1.4, 0, 1, 1.433, 0, 1.467, 0.698, 1.5, 0.728, 1, 1.6, 0.819, 1.7, 0.832, 1.8, 0.832, 2, 3.633, 0.832, 0, 3.833, 0, 0, 4.233, 0.832, 2, 4.8, 0.832, 2, 5.733, 0.832, 0, 5.9, 0, 2, 6.167, 0, 2, 6.3, 0, 2, 6.567, 0, 2, 7.933, 0, 2, 8.367, 0, 2, 8.467, 0, 0, 8.6, 1, 2, 8.8, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 2, 0.333, 0, 2, 0.4, 0, 2, 0.8, 0, 2, 1.033, 0, 2, 1.3, 0, 2, 1.8, 0, 2, 3.633, 0, 2, 4.233, 0, 2, 4.8, 0, 2, 5.733, 0, 2, 5.9, 0, 2, 6.167, 0, 0, 6.3, 1, 2, 6.567, 1, 2, 7.933, 1, 2, 8.367, 1, 0, 8.6, 0, 2, 8.8, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 2, 0.333, 1, 0, 0.4, 0, 0, 0.5, 1, 2, 0.8, 1, 2, 1.033, 1, 2, 1.3, 1, 0, 1.4, 0, 1, 1.433, 0, 1.467, 0.704, 1.5, 0.735, 1, 1.6, 0.828, 1.7, 0.841, 1.8, 0.841, 2, 3.633, 0.841, 0, 3.833, 0, 0, 4.233, 0.841, 2, 4.8, 0.841, 2, 5.733, 0.841, 0, 5.9, 0, 2, 6.167, 0, 2, 6.3, 0, 2, 6.567, 0, 2, 7.933, 0, 2, 8.367, 0, 2, 8.467, 0, 0, 8.6, 1, 2, 8.8, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 2, 0.333, 0, 2, 0.4, 0, 2, 0.8, 0, 2, 1.033, 0, 2, 1.3, 0, 2, 1.4, 0, 2, 1.8, 0, 2, 3.633, 0, 2, 4.233, 0, 2, 4.8, 0, 2, 5.733, 0, 2, 5.9, 0, 2, 6.167, 0, 0, 6.3, 1, 2, 6.567, 1, 2, 7.933, 1, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param76", "Segments": [0, 0, 2, 0.333, 0, 2, 0.4, 0, 2, 0.5, 0, 0, 0.633, -30, 0, 0.8, 0, 0, 1.3, -30, 0, 1.8, 0, 2, 3.633, 0, 2, 4.233, 0, 2, 4.8, 0, 2, 5.733, 0, 2, 6.567, 0, 2, 7.933, 0, 2, 8.8, 0]}, {"Target": "Parameter", "Id": "Param293", "Segments": [0, 0, 2, 0.333, 0, 2, 0.4, 0, 0, 0.533, -0.5, 0, 0.7, 0, 2, 8.8, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 2, 0.333, 0, 2, 0.4, 0, 2, 0.8, 0, 2, 1.033, 0, 2, 1.3, 0, 2, 1.4, 0, 0, 1.433, -0.698, 2, 1.8, -0.698, 2, 3.633, -0.698, 0, 4.233, 0.8, 2, 4.8, 0.8, 2, 5.733, 0.8, 0, 6.4, 0, 2, 6.567, 0, 2, 7.933, 0, 2, 8.8, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 2, 0.333, 0, 2, 0.4, 0, 2, 0.8, 0, 2, 1.033, 0, 2, 1.3, 0, 2, 1.4, 0, 0, 1.433, -0.5, 2, 1.8, -0.5, 2, 3.633, -0.5, 0, 4.233, 0.042, 2, 4.8, 0.042, 2, 5.733, 0.042, 0, 6.567, 0.02, 2, 7.933, 0.02, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 2, 0.333, 0, 2, 0.4, 0, 2, 0.8, 0, 2, 1.033, 0, 2, 1.3, 0, 0, 1.8, 0.04, 2, 3.633, 0.04, 2, 4.233, 0.04, 2, 4.8, 0.04, 2, 5.733, 0.04, 2, 5.9, 0.04, 2, 6.1, 0.04, 2, 6.167, 0.04, 0, 6.3, -0.142, 2, 6.567, -0.142, 2, 7.933, -0.142, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 2, 0.333, 0, 1, 0.355, 0, 0.378, -0.115, 0.4, -0.362, 1, 0.411, -0.485, 0.422, -0.556, 0.433, -0.556, 2, 0.8, -0.556, 2, 1.033, -0.556, 2, 1.3, -0.556, 2, 1.4, -0.556, 0, 1.433, -0.46, 2, 1.8, -0.46, 2, 3.633, -0.46, 2, 4.233, -0.46, 2, 4.8, -0.46, 2, 5.733, -0.46, 2, 5.9, -0.46, 2, 6.1, -0.46, 2, 6.167, -0.46, 0, 6.3, -0.204, 2, 6.567, -0.204, 2, 7.933, -0.204, 2, 8.467, -0.204, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 2, 0.333, 0, 2, 0.4, 0, 0, 1.433, 0.054, 2, 1.8, 0.054, 2, 3.633, 0.054, 2, 4.233, 0.054, 2, 4.8, 0.054, 2, 5.733, 0.054, 2, 5.9, 0.054, 2, 6.1, 0.054, 2, 6.167, 0.054, 0, 6.3, -0.094, 2, 6.567, -0.094, 2, 7.933, -0.094, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 2, 0.333, 0, 1, 0.355, 0, 0.378, -0.139, 0.4, -0.406, 1, 0.411, -0.539, 0.422, -0.6, 0.433, -0.6, 2, 0.8, -0.6, 2, 1.033, -0.6, 2, 1.3, -0.6, 2, 1.4, -0.6, 0, 1.433, -0.456, 2, 1.8, -0.456, 2, 3.633, -0.456, 2, 4.233, -0.456, 2, 4.8, -0.456, 2, 5.733, -0.456, 2, 5.9, -0.456, 2, 6.1, -0.456, 2, 6.167, -0.456, 0, 6.3, -0.204, 2, 6.567, -0.204, 2, 7.933, -0.204, 2, 8.467, -0.204, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 2, 0.333, 0, 2, 0.4, 0, 0, 0.433, -0.308, 2, 0.8, -0.308, 2, 1.033, -0.308, 2, 1.3, -0.308, 2, 1.4, -0.308, 0, 1.433, 0, 2, 1.8, 0, 2, 3.633, 0, 2, 4.233, 0, 2, 4.8, 0, 2, 5.733, 0, 2, 5.9, 0, 2, 6.1, 0, 2, 6.167, 0, 0, 6.3, 0.368, 2, 6.567, 0.368, 2, 7.933, 0.368, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 2, 0.333, 0, 2, 0.4, 0, 0, 0.433, -0.334, 2, 0.8, -0.334, 2, 1.033, -0.334, 2, 1.3, -0.334, 2, 1.4, -0.334, 0, 1.433, 0, 2, 1.8, 0, 2, 3.633, 0, 2, 4.233, 0, 2, 4.8, 0, 2, 5.733, 0, 2, 5.9, 0, 2, 6.1, 0, 2, 6.167, 0, 0, 6.3, 0.166, 2, 6.567, 0.166, 2, 7.933, 0.166, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, -1, 2, 0.333, -1, 2, 0.4, -1, 2, 0.8, -1, 2, 1.033, -1, 2, 1.3, -1, 2, 1.433, -1, 2, 1.8, -1, 2, 3.633, -1, 2, 4.233, -1, 2, 4.8, -1, 2, 5.733, -1, 2, 5.9, -1, 2, 6.1, -1, 2, 6.167, -1, 0, 6.3, 1, 2, 6.567, 1, 2, 7.933, 1, 0, 8.8, -1]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, -1, 2, 0.333, -1, 2, 0.4, -1, 2, 0.8, -1, 2, 1.033, -1, 2, 1.3, -1, 2, 1.8, -1, 2, 3.633, -1, 2, 4.233, -1, 2, 4.8, -1, 2, 5.733, -1, 2, 5.9, -1, 2, 6.1, -1, 2, 6.167, -1, 0, 6.3, 1, 2, 6.567, 1, 2, 7.933, 1, 0, 8.8, -1]}, {"Target": "Parameter", "Id": "Param", "Segments": [0, -30, 1, 0.111, -30, 0.222, -22.864, 0.333, 0, 1, 0.355, 4.573, 0.378, 30, 0.4, 30, 2, 0.8, 30, 0, 1.033, 0, 2, 1.3, 0, 2, 1.8, 0, 2, 3.633, 0, 2, 4.233, 0, 2, 4.8, 0, 0, 5.733, 22, 0, 5.9, 0, 2, 6.167, 0, 2, 6.567, 0, 2, 7.933, 0, 0, 8.8, -30]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 1, 1, 0.111, 1, 0.222, 0.765, 0.333, 0, 1, 0.355, -0.153, 0.378, -1, 0.4, -1, 2, 0.8, -1, 0, 1.033, 0, 0, 1.3, -0.5, 0, 1.8, -0.42, 2, 3.633, -0.42, 0, 4.233, -1, 1, 4.422, -1, 4.611, -0.493, 4.8, -0.42, 1, 5.111, -0.301, 5.422, -0.3, 5.733, -0.3, 0, 5.9, -0.397, 2, 6.167, -0.397, 0, 6.567, 1, 2, 7.933, 1, 0, 8.467, 0, 0, 8.8, 1]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 2, 0.333, 0, 1, 0.344, 0, 0.356, 0.35, 0.367, 0.471, 1, 0.378, 0.591, 0.389, 0.694, 0.4, 0.7, 1, 0.533, 0.772, 0.667, 0.8, 0.8, 0.8, 1, 0.844, 0.8, 0.889, 0.737, 0.933, 0.527, 1, 1, 0.212, 1.066, 0, 1.133, 0, 2, 1.867, 0, 0, 2, 0.443, 0, 2.067, 0, 2, 2.733, 0, 0, 2.8, 0.578, 0, 2.933, 0.003, 0, 3.067, 0.537, 0, 3.133, 0.003, 0, 3.2, 0.346, 0, 3.267, 0.047, 0, 3.467, 0.471, 0, 3.533, 0.169, 0, 3.667, 0.593, 0, 3.733, 0, 0, 3.8, 0.311, 0, 3.867, 0.034, 0, 4.133, 0.611, 0, 4.2, 0.011, 0, 4.333, 0.341, 0, 4.4, 0.024, 0, 4.533, 0.41, 0, 4.667, 0.006, 0, 4.867, 0.463, 0, 4.933, 0.044, 0, 5, 0.43, 0, 5.267, 0, 2, 6, 0, 0, 6.133, 0.499, 0, 6.2, 0.417, 0, 6.267, 0.585, 0, 6.467, 0, 0, 6.6, 0.461, 0, 6.667, 0.202, 0, 6.733, 0.562, 0, 6.8, 0.303, 0, 6.867, 0.572, 0, 7, 0.415, 0, 7.067, 0.552, 0, 7.533, 0, 2, 8.2, 0, 2, 8.8, 0]}, {"Target": "Parameter", "Id": "Param252", "Segments": [0, 0, 2, 0.333, 0, 2, 1.433, 0, 0, 1.8, 1, 2, 5.9, 1, 0, 6.167, 0, 2, 8.8, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, 3.42, 0, 0.633, -8.607, 1, 0.666, -8.607, 0.7, -6.992, 0.733, -6.922, 1, 0.933, -6.505, 1.133, -6.323, 1.333, -5.837, 1, 1.411, -5.647, 1.489, -2.403, 1.567, 0, 1, 1.645, 2.403, 1.722, 4.817, 1.8, 4.817, 0, 2.233, -3.42, 0, 2.9, 3.708, 1, 3.056, 3.708, 3.211, -3.486, 3.367, -3.66, 1, 3.489, -3.797, 3.611, -3.751, 3.733, -3.845, 1, 3.8, -3.897, 3.866, -6.912, 3.933, -6.912, 0, 4.233, 4.74, 0, 4.567, 0.974, 0, 4.733, 1.615, 0, 5.7, 0.949, 1, 5.767, 0.949, 5.833, 1.476, 5.9, 2.853, 1, 5.911, 3.082, 5.922, 3.626, 5.933, 3.626, 0, 6.167, -13.374, 0, 6.733, -10.511, 0, 8.3, -11.87, 0, 8.667, 1.572, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 2, 0.333, 0, 0, 0.467, -11.929, 0, 0.7, 12.165, 0, 0.933, 0.872, 0, 1.3, 5.896, 1, 1.344, 5.896, 1.389, 5.997, 1.433, 5.106, 1, 1.478, 4.215, 1.522, -4.085, 1.567, -4.085, 0, 1.767, -2.842, 0, 2, -4.676, 0, 2.767, -2.654, 0, 3.767, -5.262, 0, 3.933, 5.689, 0, 4.233, -3.621, 0, 4.5, -0.462, 1, 4.911, -0.462, 5.322, -0.539, 5.733, -0.769, 1, 5.789, -0.8, 5.844, -1.282, 5.9, -1.282, 0, 6.067, 0.872, 0, 6.267, -9.055, 1, 6.356, -9.055, 6.444, -6.643, 6.533, -4.085, 1, 6.633, -1.207, 6.733, -0.462, 6.833, -0.462, 0, 7.167, -10.5, 0, 7.4, -8.234, 0, 8.267, -9.406, 0, 8.667, 3.649, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, 1.687, 0, 0.567, -3.84, 0, 0.8, -2.585, 0, 1.3, -2.725, 0, 1.733, -1.04, 0, 2.367, -1.46, 1, 2.767, -1.46, 3.167, -1.364, 3.567, -1.04, 1, 3.634, -0.986, 3.7, 0.46, 3.767, 0.46, 0, 4.167, -1.22, 0, 4.733, -0.98, 0, 5.667, -1.1, 0, 5.833, -0.53, 0, 6.1, -1.816, 1, 6.689, -1.816, 7.278, -1.789, 7.867, -1.618, 1, 8.045, -1.567, 8.222, 0.775, 8.4, 0.775, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param267", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, -1.02, 0, 0.633, 13.208, 1, 0.711, 13.208, 0.789, 9.868, 0.867, 9.723, 1, 1.011, 9.455, 1.156, 9.554, 1.3, 9.185, 1, 1.467, 8.759, 1.633, -1.356, 1.8, -1.356, 0, 2.1, -0.329, 0, 2.833, -1.218, 0, 3.367, 0.009, 0, 3.733, -0.231, 0, 3.867, 1.511, 0, 4.233, -0.733, 0, 4.4, 0.83, 0, 5.733, -0.852, 0, 5.9, 2.035, 0, 6.167, -1.461, 1, 6.3, -1.461, 6.434, -1.163, 6.567, -0.734, 1, 6.656, -0.447, 6.744, -0.495, 6.833, 0, 1, 6.944, 0.619, 7.056, 6.976, 7.167, 6.976, 0, 7.367, 5.535, 2, 7.933, 5.535, 1, 8.111, 5.535, 8.289, 3.467, 8.467, 1.397, 1, 8.578, 0.103, 8.689, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param3", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, -1.26, 0, 0.6, 5.478, 0, 0.9, 3.372, 0, 1.3, 4.416, 0, 1.8, -7.617, 0, 2.267, -2.705, 0, 2.933, -5.771, 0, 3.367, -3.558, 0, 3.7, -4.408, 0, 3.933, 10.339, 0, 4.3, 4.197, 0, 4.567, 6.954, 0, 5.733, 6.139, 0, 6.167, 11.737, 0, 6.733, 9.767, 0, 7.933, 10.643, 0, 8.567, -1.484, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param4", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, -6.652, 0, 0.633, 11.364, 1, 0.689, 11.364, 0.744, 7.04, 0.8, 6.355, 1, 0.967, 4.3, 1.133, 3.864, 1.3, 3.864, 0, 1.4, 4.918, 0, 1.8, -7.945, 0, 2.1, -5.963, 0, 2.833, -7.388, 0, 3.367, -6.03, 0, 3.733, -6.652, 0, 3.9, -5.756, 0, 4.167, -8.65, 1, 4.234, -8.65, 4.3, -8.652, 4.367, -7.945, 1, 4.545, -6.06, 4.722, -4.443, 4.9, -4.443, 0, 5.733, -6.487, 0, 5.9, -3.829, 0, 6.167, -9.246, 1, 6.3, -9.246, 6.434, -8.734, 6.567, -6.889, 1, 6.656, -5.659, 6.744, -4.366, 6.833, -4.366, 0, 7.167, -8.65, 1, 7.234, -8.65, 7.3, -7.594, 7.367, -7.388, 1, 7.556, -6.805, 7.744, -6.799, 7.933, -6.003, 1, 8.1, -5.301, 8.266, 0.827, 8.433, 0.827, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param266", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, 3.18, 0, 0.567, -1.405, 0, 0.8, -0.023, 0, 1.233, -1.341, 0, 1.7, 2.634, 0, 2.033, -0.743, 0, 2.767, 0.782, 0, 3.3, -1.156, 0, 3.667, 0.659, 0, 3.8, -2.882, 0, 4.167, -1.217, 0, 4.333, -2.024, 0, 5.667, -1.399, 0, 5.833, -3.83, 0, 6.1, 0.881, 0, 6.5, -1.094, 0, 7.867, -0.367, 0, 8.367, -0.955, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param5", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, 5.045, 1, 0.478, 5.045, 0.555, -10.018, 0.633, -16.413, 1, 0.711, -22.808, 0.789, -22.917, 0.867, -22.917, 2, 1.3, -22.917, 0, 1.5, -24.566, 1, 1.6, -24.566, 1.7, -10.778, 1.8, -8.805, 1, 2.411, 3.248, 3.022, 7.44, 3.633, 7.44, 0, 3.833, -7.2, 0, 4.233, 9.502, 1, 4.266, 9.502, 4.3, 8.344, 4.333, 8.211, 1, 4.489, 7.591, 4.644, 7.42, 4.8, 7.42, 0, 5.733, 7.43, 1, 6.011, 7.43, 6.289, 7.311, 6.567, 6.208, 1, 6.678, 5.767, 6.789, -2.483, 6.9, -2.483, 0, 7.2, 7.44, 1, 7.289, 7.44, 7.378, 5.787, 7.467, 5.045, 1, 7.911, 1.331, 8.356, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param6", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, 20.547, 1, 0.478, 20.547, 0.555, 8.653, 0.633, 6.926, 1, 0.711, 5.199, 0.789, 5.363, 0.867, 5.363, 0, 1.3, 14.034, 0, 1.5, 13.038, 1, 1.6, 13.038, 1.7, 15.599, 1.8, 15.656, 1, 2.4, 15.996, 3, 16.096, 3.6, 16.096, 0, 3.833, 8.169, 0, 4.233, 18.435, 0, 4.8, 17.918, 2, 5.733, 17.918, 1, 6.011, 17.918, 6.289, 17.004, 6.567, 14.034, 1, 6.678, 12.847, 6.789, 11.175, 6.9, 11.175, 0, 7.133, 17.9, 1, 7.222, 17.9, 7.311, 15.906, 7.4, 14.034, 1, 7.867, 4.208, 8.333, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 2, 0.333, 0, 2, 0.4, 0, 0, 0.633, 0.105, 0, 0.867, 0, 2, 1.3, 0, 2, 1.8, 0, 2, 3.633, 0, 0, 3.833, 0.139, 0, 4.233, 0, 2, 4.8, 0, 2, 5.733, 0, 0, 5.9, 0.043, 0, 6.167, 0, 2, 6.567, 0, 2, 7.933, 0, 2, 8.8, 0]}, {"Target": "Parameter", "Id": "Param8", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, 0.4, 0, 0.633, -0.297, 0, 0.867, 0, 2, 1.3, 0, 0, 1.8, -0.78, 2, 3.633, -0.78, 2, 3.833, -0.78, 0, 4.233, 0, 2, 4.8, 0, 2, 5.733, 0, 2, 6.567, 0, 2, 7.933, 0, 2, 8.8, 0]}, {"Target": "Parameter", "Id": "Param23", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, 0.694, 0, 0.633, -1.08, 0, 0.867, 0, 2, 1.3, 0, 0, 1.8, -1.44, 2, 3.633, -1.44, 2, 3.833, -1.44, 0, 4.233, 2.22, 0, 4.8, 0, 2, 5.733, 0, 2, 6.567, 0, 2, 7.933, 0, 2, 8.8, 0]}, {"Target": "Parameter", "Id": "Param25", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, 0.469, 0, 0.633, -0.609, 0, 0.867, 0, 2, 1.3, 0, 2, 1.8, 0, 2, 3.633, 0, 2, 4.233, 0, 2, 4.8, 0, 2, 5.733, 0, 2, 6.567, 0, 2, 7.933, 0, 2, 8.8, 0]}, {"Target": "Parameter", "Id": "Param27", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, 1.279, 0, 0.633, -2.28, 0, 0.867, 0, 2, 1.3, 0, 2, 1.8, 0, 2, 3.633, 0, 2, 4.233, 0, 2, 4.8, 0, 2, 5.733, 0, 2, 6.567, 0, 2, 7.933, 0, 2, 8.8, 0]}, {"Target": "Parameter", "Id": "Param201", "Segments": [0, 0, 2, 0.333, 0, 2, 0.4, 0, 2, 0.5, 0, 0, 0.533, 1, 2, 1.033, 1, 2, 1.467, 1, 2, 1.7, 1, 2, 1.733, 1, 2, 3.5, 1, 2, 4.233, 1, 2, 4.8, 1, 2, 5.733, 1, 2, 6.4, 1, 2, 6.567, 1, 2, 7.933, 1, 2, 8.367, 1, 0, 8.4, 0, 2, 8.8, 0]}, {"Target": "Parameter", "Id": "Param209", "Segments": [0, 0, 2, 0.333, 0, 2, 0.4, 0, 2, 0.5, 0, 0, 0.533, 1, 2, 1.033, 1, 2, 1.467, 1, 2, 1.7, 1, 2, 1.733, 1, 2, 3.5, 1, 2, 4.233, 1, 2, 4.8, 1, 2, 5.733, 1, 2, 6.4, 1, 2, 6.567, 1, 2, 7.933, 1, 2, 8.367, 1, 0, 8.4, 0, 2, 8.8, 0]}, {"Target": "Parameter", "Id": "Param214", "Segments": [0, 0, 2, 0.333, 0, 2, 0.4, 0, 2, 0.5, 0, 2, 0.533, 0, 2, 1.033, 0, 2, 1.467, 0, 2, 1.7, 0, 0, 1.733, 1, 2, 3.5, 1, 2, 4.233, 1, 2, 4.8, 1, 2, 5.733, 1, 2, 6.4, 1, 0, 6.433, 0, 2, 6.567, 0, 2, 7.933, 0, 2, 8.367, 0, 2, 8.4, 0, 2, 8.8, 0]}, {"Target": "Parameter", "Id": "Param215", "Segments": [0, 0, 2, 0.333, 0, 2, 0.4, 0, 2, 0.5, 0, 2, 0.533, 0, 2, 1.033, 0, 2, 1.467, 0, 2, 1.7, 0, 2, 1.733, 0, 2, 3.5, 0, 2, 4.233, 0, 2, 4.8, 0, 2, 5.733, 0, 2, 6.4, 0, 0, 6.433, 1, 2, 6.567, 1, 2, 7.933, 1, 2, 8.367, 1, 0, 8.4, 0, 2, 8.8, 0]}, {"Target": "Parameter", "Id": "Param219", "Segments": [0, 0, 2, 0.333, 0, 2, 0.4, 0, 2, 0.5, 0, 0, 0.533, 1, 2, 1.033, 1, 2, 1.467, 1, 2, 1.7, 1, 0, 1.733, 0, 2, 3.5, 0, 2, 4.233, 0, 2, 4.8, 0, 2, 5.733, 0, 2, 6.4, 0, 2, 6.567, 0, 2, 7.933, 0, 2, 8.367, 0, 2, 8.4, 0, 2, 8.8, 0]}, {"Target": "Parameter", "Id": "Param9", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, 0.48, 0, 0.533, -12.9, 0, 0.8, -8.843, 0, 1.1, -9.08, 1, 1.167, -9.08, 1.233, -8.983, 1.3, -8.36, 1, 1.422, -7.218, 1.545, -5.614, 1.667, -4.26, 1, 1.711, -3.768, 1.756, -3.54, 1.8, -3.54, 0, 2.233, -4.122, 0, 2.833, -3.818, 1, 3.055, -3.818, 3.278, -3.863, 3.5, -4.26, 1, 3.589, -4.419, 3.678, -6.24, 3.767, -6.24, 0, 4.233, -3.72, 0, 4.467, -4.56, 2, 4.8, -4.56, 1, 5.111, -4.56, 5.422, -4.499, 5.733, -4.26, 1, 5.8, -4.209, 5.866, -3.48, 5.933, -3.48, 0, 6.4, -5.22, 2, 7.933, -5.22, 0, 8.2, -9.562, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param10", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, -0.48, 1, 0.444, -0.48, 0.489, 15.024, 0.533, 17.854, 1, 0.622, 23.515, 0.711, 24.3, 0.8, 24.3, 0, 1.033, 15.06, 2, 1.3, 15.06, 2, 1.8, 15.06, 2, 3.5, 15.06, 2, 4.233, 15.06, 2, 4.8, 15.06, 2, 5.733, 15.06, 2, 6.4, 15.06, 0, 6.567, 14.52, 2, 7.933, 14.52, 1, 8.055, 14.52, 8.178, 11.303, 8.3, 7.307, 1, 8.467, 1.858, 8.633, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param11", "Segments": [0, 0, 2, 0.333, 0, 1, 0.355, 0, 0.378, -0.229, 0.4, 0.96, 1, 0.444, 3.338, 0.489, 14.926, 0.533, 16.101, 1, 0.622, 18.453, 0.711, 18.78, 0.8, 18.78, 0, 1.033, -14.4, 2, 1.3, -14.4, 2, 1.8, -14.4, 2, 3.5, -14.4, 2, 4.233, -14.4, 2, 4.8, -14.4, 2, 5.733, -14.4, 2, 6.4, -14.4, 0, 6.567, -13.883, 2, 7.933, -13.883, 0, 8.467, 30, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param12", "Segments": [0, 0, 2, 0.333, 0, 1, 0.355, 0, 0.378, -14.379, 0.4, -17, 1, 0.444, -22.243, 0.489, -28.225, 0.533, -28.782, 1, 0.622, -29.898, 0.711, -30, 0.8, -30, 2, 1.033, -30, 2, 1.3, -30, 2, 1.8, -30, 2, 3.5, -30, 2, 4.233, -30, 2, 4.8, -30, 2, 5.733, -30, 2, 6.4, -30, 0, 6.567, -28.923, 2, 7.933, -28.923, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param22", "Segments": [0, 0, 2, 0.333, 0, 0, 0.367, 1, 2, 0.533, 1, 2, 0.8, 1, 2, 1.033, 1, 2, 1.3, 1, 2, 1.8, 1, 2, 3.5, 1, 2, 4.233, 1, 2, 4.8, 1, 2, 5.733, 1, 2, 6.4, 1, 2, 6.567, 1, 2, 8.767, 1, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param13", "Segments": [0, 0, 2, 0.333, 0, 2, 0.4, 0, 0, 0.533, 7.813, 0, 0.8, -5.94, 2, 1.033, -5.94, 2, 1.3, -5.94, 0, 1.8, 0, 2, 3.5, 0, 2, 3.767, 0, 2, 4.233, 0, 0, 4.8, 4.38, 2, 5.733, 4.38, 0, 6.4, 10.56, 0, 6.567, 10.181, 2, 7.933, 10.181, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param14", "Segments": [0, 0, 2, 0.333, 0, 2, 0.4, 0, 1, 0.433, 0, 0.467, -16.23, 0.5, -16.824, 1, 0.511, -17.022, 0.522, -17.007, 0.533, -17.01, 1, 0.622, -17.032, 0.711, -17.04, 0.8, -17.04, 0, 1.033, 0, 2, 1.3, 0, 2, 1.8, 0, 2, 3.5, 0, 2, 4.233, 0, 2, 4.8, 0, 2, 5.733, 0, 2, 6.4, 0, 2, 6.567, 0, 2, 7.933, 0, 0, 8.4, -7.32, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param210", "Segments": [0, 0, 2, 0.333, 0, 1, 0.355, 0, 0.378, 30, 0.4, 30, 1, 0.433, 30, 0.467, 25.386, 0.5, 13.208, 1, 0.511, 9.149, 0.522, -2.678, 0.533, -3.43, 1, 0.622, -9.444, 0.711, -11.7, 0.8, -11.7, 0, 1.033, -11.1, 2, 1.3, -11.1, 1, 1.467, -11.1, 1.633, 0.996, 1.8, 1.56, 1, 1.944, 2.048, 2.089, 1.92, 2.233, 1.92, 0, 2.767, 1.56, 0, 3.233, 1.92, 1, 3.433, 1.92, 3.633, 1.844, 3.833, 1.56, 1, 3.878, 1.497, 3.922, 1.196, 3.967, 0.396, 1, 4.056, -1.204, 4.144, -5.7, 4.233, -5.7, 1, 4.422, -5.7, 4.611, -5.207, 4.8, -5.02, 1, 5.111, -4.712, 5.422, -4.68, 5.733, -4.68, 0, 5.933, -6.48, 1, 6.089, -6.48, 6.244, -0.478, 6.4, 1.18, 1, 6.456, 1.772, 6.511, 1.504, 6.567, 1.504, 0, 7.3, 0.784, 1, 7.511, 0.784, 7.722, 0.594, 7.933, 1.504, 1, 8.144, 2.414, 8.356, 30, 8.567, 30, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param211", "Segments": [0, 0, 2, 0.333, 0, 2, 0.4, 0, 0, 0.533, 6.222, 1, 0.589, 6.222, 0.644, 5.569, 0.7, 2.262, 1, 0.756, -1.044, 0.811, -4.58, 0.867, -4.58, 0, 1.367, 2.262, 1, 1.434, 2.262, 1.5, -4.086, 1.567, -5.257, 1, 1.622, -6.232, 1.678, -6.079, 1.733, -6.079, 1, 1.8, -6.079, 1.866, 3.504, 1.933, 5.424, 1, 2.166, 12.143, 2.4, 13.971, 2.633, 13.971, 0, 3.067, 9.947, 0, 3.5, 13.971, 0, 3.867, 4.562, 0, 4.1, 12.654, 1, 4.144, 12.654, 4.189, 12.42, 4.233, 11.58, 1, 4.378, 8.851, 4.522, 4.267, 4.667, 2.925, 1, 4.867, 1.067, 5.067, -0.402, 5.267, -1.137, 1, 5.422, -1.709, 5.578, -1.517, 5.733, -2.221, 1, 5.911, -3.025, 6.089, -11, 6.267, -11, 1, 6.445, -11, 6.622, 8.029, 6.8, 9.947, 1, 6.978, 11.864, 7.155, 11.58, 7.333, 11.58, 1, 7.533, 11.58, 7.733, 8.309, 7.933, 5.995, 1, 8.111, 3.938, 8.289, 3.171, 8.467, 1.62, 1, 8.578, 0.651, 8.689, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param228", "Segments": [0, 0, 2, 0.333, 0, 2, 0.4, 0, 0, 0.533, -10.2, 0, 0.667, 0, 2, 0.8, 0, 2, 1.033, 0, 2, 1.3, 0, 2, 1.8, 0, 2, 3.5, 0, 0, 4.233, -3, 0, 4.8, 0, 2, 5.733, 0, 0, 6.4, -6, 1, 6.689, -6, 6.978, -5.892, 7.267, -4.214, 1, 7.489, -2.924, 7.711, 0, 7.933, 0, 0, 8.1, -25.5, 1, 8.156, -25.5, 8.211, -7.526, 8.267, -6, 1, 8.445, -1.117, 8.622, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param283", "Segments": [0, 0, 2, 0.333, 0, 2, 0.533, 0, 2, 3.5, 0, 1, 3.611, 0, 3.722, 21.157, 3.833, 26, 1, 3.933, 30.359, 4.033, 30, 4.133, 30, 0, 4.6, 0, 2, 5.733, 0, 0, 5.933, 30, 0, 6.4, 0, 2, 8.8, 0]}, {"Target": "Parameter", "Id": "Param221", "Segments": [0, 0, 2, 0.333, 0, 2, 0.4, 0, 2, 0.5, 0, 0, 0.533, 1, 2, 0.8, 1, 2, 1.033, 1, 2, 1.3, 1, 2, 1.533, 1, 2, 1.567, 1, 2, 1.8, 1, 2, 3.633, 1, 2, 4.233, 1, 2, 4.8, 1, 2, 5.733, 1, 2, 6.4, 1, 2, 6.567, 1, 2, 7.933, 1, 2, 8.2, 1, 0, 8.233, 0, 2, 8.8, 0]}, {"Target": "Parameter", "Id": "Param223", "Segments": [0, 0, 2, 0.333, 0, 2, 0.4, 0, 2, 0.5, 0, 0, 0.533, 1, 2, 0.8, 1, 2, 1.033, 1, 2, 1.3, 1, 2, 1.533, 1, 2, 1.567, 1, 2, 1.8, 1, 2, 3.633, 1, 2, 4.233, 1, 2, 4.8, 1, 2, 5.733, 1, 2, 6.4, 1, 2, 6.567, 1, 2, 7.933, 1, 2, 8.2, 1, 0, 8.233, 0, 2, 8.8, 0]}, {"Target": "Parameter", "Id": "Param238", "Segments": [0, 0, 2, 0.333, 0, 2, 0.4, 0, 2, 0.5, 0, 2, 0.533, 0, 2, 0.8, 0, 2, 1.033, 0, 2, 1.3, 0, 2, 1.533, 0, 0, 1.567, 1, 2, 1.8, 1, 2, 3.633, 1, 2, 4.233, 1, 2, 4.8, 1, 2, 5.733, 1, 2, 6.4, 1, 2, 6.567, 1, 2, 7.933, 1, 2, 8.2, 1, 0, 8.233, 0, 2, 8.8, 0]}, {"Target": "Parameter", "Id": "Param240", "Segments": [0, 0, 2, 0.333, 0, 2, 0.4, 0, 2, 0.5, 0, 0, 0.533, 1, 2, 0.8, 1, 2, 1.033, 1, 2, 1.3, 1, 2, 1.533, 1, 0, 1.567, 0, 2, 1.8, 0, 2, 3.633, 0, 2, 4.233, 0, 2, 4.8, 0, 2, 5.733, 0, 2, 6.4, 0, 2, 6.567, 0, 2, 7.933, 0, 2, 8.2, 0, 2, 8.233, 0, 2, 8.8, 0]}, {"Target": "Parameter", "Id": "Param15", "Segments": [0, 0, 2, 0.333, 0, 1, 0.366, 0, 0.4, 0.008, 0.433, 2.375, 1, 0.466, 4.742, 0.5, 10.963, 0.533, 10.963, 0, 0.8, 10.152, 1, 0.878, 10.152, 0.955, 10.25, 1.033, 10.452, 1, 1.122, 10.682, 1.211, 10.8, 1.3, 10.8, 0, 1.8, 7.32, 0, 2.233, 7.993, 0, 2.967, 7.277, 1, 3.189, 7.277, 3.411, 7.371, 3.633, 7.92, 1, 3.711, 8.112, 3.789, 9.42, 3.867, 9.42, 1, 3.989, 9.42, 4.111, 9.138, 4.233, 8.7, 1, 4.422, 8.024, 4.611, 7.74, 4.8, 7.74, 2, 5.733, 7.74, 0, 5.933, 5.82, 0, 6.4, 7.74, 0, 6.567, 7.462, 2, 7.933, 7.462, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param16", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, 3.54, 0, 0.533, -8.58, 0, 0.8, 12.84, 2, 1.033, 12.84, 2, 1.3, 12.84, 2, 1.8, 12.84, 2, 3.633, 12.84, 2, 4.233, 12.84, 2, 4.8, 12.84, 2, 5.733, 12.84, 2, 6.4, 12.84, 0, 6.567, 12.379, 2, 7.933, 12.379, 0, 8.2, 13.006, 1, 8.244, 13.006, 8.289, 8.662, 8.333, 7.132, 1, 8.378, 5.602, 8.422, 5.155, 8.467, 4.088, 1, 8.578, 1.419, 8.689, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param17", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, -7.32, 1, 0.444, -7.32, 0.489, 16.262, 0.533, 18.398, 1, 0.622, 22.668, 0.711, 23.16, 0.8, 23.16, 2, 1.033, 23.16, 2, 1.3, 23.16, 2, 1.8, 23.16, 2, 3.633, 23.16, 2, 4.233, 23.16, 2, 4.8, 23.16, 2, 5.733, 23.16, 2, 6.4, 23.16, 0, 6.567, 22.329, 2, 7.933, 22.329, 1, 8.111, 22.329, 8.289, 7.271, 8.467, 2.453, 1, 8.578, -0.558, 8.689, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param19", "Segments": [0, 0, 2, 0.333, 0, 2, 0.4, 0, 1, 0.444, 0, 0.489, -22.005, 0.533, -23.996, 1, 0.622, -27.978, 0.711, -28.44, 0.8, -28.44, 2, 1.033, -28.44, 2, 1.3, -28.44, 2, 1.8, -28.44, 2, 3.633, -28.44, 2, 4.233, -28.44, 2, 4.8, -28.44, 2, 5.733, -28.44, 2, 6.4, -28.44, 0, 6.567, -27.419, 2, 7.933, -27.419, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param20", "Segments": [0, 0, 2, 0.333, 0, 2, 0.4, 0, 0, 0.533, -30, 0, 0.8, 0, 2, 1.033, 0, 2, 1.3, 0, 2, 1.8, 0, 2, 3.633, 0, 2, 4.233, 0, 2, 4.8, 0, 2, 5.733, 0, 2, 6.4, 0, 2, 6.567, 0, 2, 7.933, 0, 2, 8.8, 0]}, {"Target": "Parameter", "Id": "Param233", "Segments": [0, 0, 2, 0.333, 0, 1, 0.355, 0, 0.378, -0.386, 0.4, -5.22, 1, 0.422, -10.054, 0.445, -17.584, 0.467, -17.584, 1, 0.478, -17.584, 0.489, -15.45, 0.5, -11.772, 1, 0.511, -8.094, 0.522, -1.821, 0.533, -1.271, 1, 0.622, 3.128, 0.711, 4.8, 0.8, 4.8, 1, 0.878, 4.8, 0.955, 4.095, 1.033, 3.9, 1, 1.122, 3.678, 1.211, 3.801, 1.3, 3.48, 1, 1.467, 2.878, 1.633, -2.008, 1.8, -2.52, 1, 1.944, -2.964, 2.089, -2.88, 2.233, -2.88, 2, 2.9, -2.88, 2, 3.367, -2.88, 1, 3.456, -2.88, 3.544, -2.904, 3.633, -2.582, 1, 3.7, -2.34, 3.766, -0.24, 3.833, -0.24, 0, 4.233, -0.72, 2, 4.8, -0.72, 2, 5.733, -0.72, 0, 6.233, 0.906, 0, 6.633, -1.453, 1, 6.855, -1.453, 7.078, -1.176, 7.3, -1.02, 1, 7.511, -0.872, 7.722, -0.902, 7.933, -0.72, 1, 8.111, -0.567, 8.289, 10.869, 8.467, 10.869, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param234", "Segments": [0, 0, 2, 0.333, 0, 2, 0.4, 0, 0, 0.533, -7.746, 0, 0.867, 22.15, 0, 1.233, 13.438, 2, 1.633, 13.438, 0, 1.867, -10.275, 1, 1.989, -10.275, 2.111, -4.852, 2.233, -2.756, 1, 2.455, 1.054, 2.678, 1.671, 2.9, 1.671, 1, 3.122, 1.671, 3.345, 0.744, 3.567, -2.756, 1, 3.622, -3.631, 3.678, -13.575, 3.733, -13.575, 0, 4.1, 10.651, 1, 4.189, 10.651, 4.278, -1.222, 4.367, -3.582, 1, 4.511, -7.417, 4.656, -7.746, 4.8, -7.746, 1, 5.111, -7.746, 5.422, -7.446, 5.733, -3.582, 1, 5.9, -1.512, 6.066, 14.724, 6.233, 14.724, 1, 6.3, 14.724, 6.366, 9.023, 6.433, 7.205, 1, 6.6, 2.661, 6.766, 0.441, 6.933, -3.968, 1, 7, -5.732, 7.066, -9.398, 7.133, -9.398, 0, 7.933, -3.582, 0, 8.2, -16.8, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param235", "Segments": [0, 0, 2, 0.333, 0, 2, 0.4, 0, 0, 0.533, -30, 0, 0.8, -9.6, 2, 1.033, -9.6, 2, 1.3, -9.6, 2, 1.8, -9.6, 2, 3.633, -9.6, 0, 4, -17.94, 2, 4.8, -17.94, 2, 5.733, -17.94, 0, 5.933, -9.6, 2, 6.4, -9.6, 0, 6.567, -30, 1, 7.022, -30, 7.478, -21.098, 7.933, -9.255, 1, 8.222, -1.745, 8.511, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param277", "Segments": [0, 0, 2, 0.333, 0, 0, 0.5, -10.453, 0, 0.733, 12.46, 0, 1.133, -9.549, 0, 1.567, 12.46, 0, 1.967, -9.549, 0, 2.4, 12.46, 0, 2.8, -9.549, 0, 3.233, 12.46, 0, 3.633, -9.549, 0, 4.067, 12.46, 0, 4.467, -9.549, 0, 4.9, 12.46, 0, 5.3, -9.549, 0, 5.733, 12.46, 0, 6.133, -9.549, 0, 6.567, 12.46, 0, 6.967, -9.549, 0, 7.4, 12.46, 0, 7.8, -9.549, 0, 8.233, 12.46, 0, 8.633, -9.549, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param278", "Segments": [0, 0, 2, 0.333, 0, 0, 0.5, -0.195, 0, 0.767, 0.25, 0, 1.2, -0.17, 0, 1.733, 0.25, 0, 2.2, -0.17, 0, 2.733, 0.25, 0, 3.2, -0.17, 0, 3.733, 0.25, 0, 4.2, -0.17, 0, 4.733, 0.25, 0, 5.2, -0.17, 0, 5.733, 0.25, 0, 6.2, -0.17, 0, 6.733, 0.25, 0, 7.2, -0.17, 0, 7.733, 0.25, 0, 8.2, -0.17, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param280", "Segments": [0, 0, 2, 0.333, 0, 0, 0.467, -0.7, 0, 0.733, 0.6, 1, 0.855, 0.6, 0.978, 0.336, 1.1, 0.24, 1, 1.567, -0.127, 2.033, -0.236, 2.5, -0.236, 0, 3.467, 0.24, 0, 4.967, -0.236, 0, 5.733, 0.24, 0, 6.567, -0.236, 0, 7.367, 0.24, 0, 8.067, -0.236, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param7", "Segments": [0, 0, 2, 0.333, 0, 0, 1.4, -9, 0, 2.467, 0, 0, 3.533, -9, 0, 4.6, 0, 0, 5.667, -9, 0, 6.733, 0, 0, 7.8, -9, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param66", "Segments": [0, 0, 2, 0.333, 0, 0, 1.4, -19, 0, 2.467, 0, 0, 3.533, -19, 0, 4.6, 0, 0, 5.667, -19, 0, 6.733, 0, 0, 7.8, -19, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param67", "Segments": [0, 0, 2, 0.333, 0, 0, 0.8, 16, 0, 1.933, -16, 1, 2.111, -16, 2.289, -10.667, 2.467, 0, 1, 2.645, 10.667, 2.822, 16, 3, 16, 0, 4.067, -16, 1, 4.245, -16, 4.422, -10.667, 4.6, 0, 1, 4.778, 10.667, 4.955, 16, 5.133, 16, 0, 6.2, -16, 1, 6.378, -16, 6.555, -10.667, 6.733, 0, 1, 6.911, 10.667, 7.089, 16, 7.267, 16, 0, 8.333, -16, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param68", "Segments": [0, 0, 2, 0.333, 0, 0, 1.4, 17, 0, 2.467, 0, 0, 3.533, 17, 0, 4.6, 0, 0, 5.667, 17, 0, 6.733, 0, 0, 7.8, 17, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param69", "Segments": [0, 0, 2, 0.333, 0, 0, 0.8, -16, 0, 1.933, 16, 1, 2.111, 16, 2.289, 10.667, 2.467, 0, 1, 2.645, -10.667, 2.822, -16, 3, -16, 0, 4.067, 16, 1, 4.245, 16, 4.422, 10.667, 4.6, 0, 1, 4.778, -10.667, 4.955, -16, 5.133, -16, 0, 6.2, 16, 1, 6.378, 16, 6.555, 10.667, 6.733, 0, 1, 6.911, -10.667, 7.089, -16, 7.267, -16, 0, 8.333, 16, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param35", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, 5.499, 0, 0.733, -6.293, 0, 1.2, 2.265, 0, 1.367, 1.744, 0, 1.533, 4.088, 0, 1.8, -2.794, 0, 2.167, 1.813, 0, 2.8, -1.26, 0, 3.667, 0.816, 0, 3.833, -3.914, 0, 4.167, 5.282, 0, 4.5, -1.897, 0, 4.9, 0.682, 0, 5.333, -2.257, 0, 6.133, 3.981, 0, 6.9, -1.417, 0, 7.667, 2.445, 0, 8.533, -3.739, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param36", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, -4.353, 0, 0.633, 6.532, 0, 1.033, -6.653, 0, 1.367, 3.444, 0, 1.567, 0.025, 0, 1.733, 3.143, 0, 2.033, -3.964, 0, 2.4, 2.726, 0, 2.833, -1.139, 0, 3.233, 0.143, 0, 3.567, -0.29, 0, 3.833, 3.404, 0, 4.067, -5.621, 0, 4.4, 5.847, 0, 4.733, -3.766, 0, 5.167, 2.558, 0, 5.567, -2.023, 0, 5.933, 0.708, 0, 6.1, -0.8, 0, 6.367, 2.647, 0, 6.7, -1.421, 0, 7.067, 0.224, 0, 7.5, -0.762, 0, 7.933, 1.365, 0, 8.333, -0.672, 0, 8.533, 0.595, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param37", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, 14.679, 0, 0.633, -14.141, 0, 1.033, 4.662, 0, 1.267, 0.117, 0, 1.667, 8.408, 0, 1.967, -4.715, 0, 2.267, 2.102, 0, 2.8, -2.572, 0, 3.567, 2.566, 0, 4.1, -9.431, 0, 4.4, 11.074, 0, 4.7, -3.301, 0, 5, 0.703, 0, 5.3, -4.391, 0, 5.967, 3.241, 0, 6, 3.24, 0, 6.2, 6.52, 0, 6.5, -3.599, 0, 6.7, -2.246, 0, 6.933, -2.93, 0, 7.633, 4.514, 0, 8.2, -4.242, 0, 8.533, 4.621, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param39", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, -10.887, 0, 0.533, 15.203, 0, 0.8, -14.33, 0, 1.167, 9.352, 0, 1.467, -5.505, 0, 1.833, 7.787, 0, 2.1, -7.426, 0, 2.4, 4.221, 0, 2.733, -1.482, 0, 3.067, 0.421, 0, 3.4, -0.937, 0, 3.733, 1.857, 0, 3.933, 1.125, 0, 4.033, 1.613, 0, 4.3, -10.293, 0, 4.567, 11.734, 0, 4.833, -7.306, 0, 5.167, 4.968, 0, 5.467, -3.571, 0, 5.767, 0.986, 0, 6.167, -1.698, 0, 6.4, 5.022, 0, 6.633, -3.796, 0, 6.933, 1.678, 0, 7.2, -1.376, 0, 7.8, 1.486, 0, 8.033, 0.59, 0, 8.1, 0.649, 0, 8.4, -3.644, 0, 8.733, 5.978, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param38", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, 14.679, 0, 0.633, -14.141, 0, 1.033, 4.662, 0, 1.267, 0.117, 0, 1.667, 8.408, 0, 1.967, -4.715, 0, 2.267, 2.102, 0, 2.8, -2.572, 0, 3.567, 2.566, 0, 4.1, -9.431, 0, 4.4, 11.074, 0, 4.7, -3.301, 0, 5, 0.703, 0, 5.3, -4.391, 0, 5.967, 3.241, 0, 6, 3.24, 0, 6.2, 6.52, 0, 6.5, -3.599, 0, 6.7, -2.246, 0, 6.933, -2.93, 0, 7.633, 4.514, 0, 8.2, -4.242, 0, 8.533, 4.621, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param40", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, -10.887, 0, 0.533, 15.203, 0, 0.8, -14.33, 0, 1.167, 9.352, 0, 1.467, -5.505, 0, 1.833, 7.787, 0, 2.1, -7.426, 0, 2.4, 4.221, 0, 2.733, -1.482, 0, 3.067, 0.421, 0, 3.4, -0.937, 0, 3.733, 1.857, 0, 3.933, 1.125, 0, 4.033, 1.613, 0, 4.3, -10.293, 0, 4.567, 11.734, 0, 4.833, -7.306, 0, 5.167, 4.968, 0, 5.467, -3.571, 0, 5.767, 0.986, 0, 6.167, -1.698, 0, 6.4, 5.022, 0, 6.633, -3.796, 0, 6.933, 1.678, 0, 7.2, -1.376, 0, 7.8, 1.486, 0, 8.033, 0.59, 0, 8.1, 0.649, 0, 8.4, -3.644, 0, 8.733, 5.978, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param41", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, 14.679, 0, 0.633, -14.141, 0, 1.033, 4.662, 0, 1.267, 0.117, 0, 1.667, 8.408, 0, 1.967, -4.715, 0, 2.267, 2.102, 0, 2.8, -2.572, 0, 3.567, 2.566, 0, 4.1, -9.431, 0, 4.4, 11.074, 0, 4.7, -3.301, 0, 5, 0.703, 0, 5.3, -4.391, 0, 5.967, 3.241, 0, 6, 3.24, 0, 6.2, 6.52, 0, 6.5, -3.599, 0, 6.7, -2.246, 0, 6.933, -2.93, 0, 7.633, 4.514, 0, 8.2, -4.242, 0, 8.533, 4.621, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param42", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, -10.887, 0, 0.533, 15.203, 0, 0.8, -14.33, 0, 1.167, 9.352, 0, 1.467, -5.505, 0, 1.833, 7.787, 0, 2.1, -7.426, 0, 2.4, 4.221, 0, 2.733, -1.482, 0, 3.067, 0.421, 0, 3.4, -0.937, 0, 3.733, 1.857, 0, 3.933, 1.125, 0, 4.033, 1.613, 0, 4.3, -10.293, 0, 4.567, 11.734, 0, 4.833, -7.306, 0, 5.167, 4.968, 0, 5.467, -3.571, 0, 5.767, 0.986, 0, 6.167, -1.698, 0, 6.4, 5.022, 0, 6.633, -3.796, 0, 6.933, 1.678, 0, 7.2, -1.376, 0, 7.8, 1.486, 0, 8.033, 0.59, 0, 8.1, 0.649, 0, 8.4, -3.644, 0, 8.733, 5.978, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param43", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, 14.679, 0, 0.633, -14.141, 0, 1.033, 4.662, 0, 1.267, 0.117, 0, 1.667, 8.408, 0, 1.967, -4.715, 0, 2.267, 2.102, 0, 2.8, -2.572, 0, 3.567, 2.566, 0, 4.1, -9.431, 0, 4.4, 11.074, 0, 4.7, -3.301, 0, 5, 0.703, 0, 5.3, -4.391, 0, 5.967, 3.241, 0, 6, 3.24, 0, 6.2, 6.52, 0, 6.5, -3.599, 0, 6.7, -2.246, 0, 6.933, -2.93, 0, 7.633, 4.514, 0, 8.2, -4.242, 0, 8.533, 4.621, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param44", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, -10.887, 0, 0.533, 15.203, 0, 0.8, -14.33, 0, 1.167, 9.352, 0, 1.467, -5.505, 0, 1.833, 7.787, 0, 2.1, -7.426, 0, 2.4, 4.221, 0, 2.733, -1.482, 0, 3.067, 0.421, 0, 3.4, -0.937, 0, 3.733, 1.857, 0, 3.933, 1.125, 0, 4.033, 1.613, 0, 4.3, -10.293, 0, 4.567, 11.734, 0, 4.833, -7.306, 0, 5.167, 4.968, 0, 5.467, -3.571, 0, 5.767, 0.986, 0, 6.167, -1.698, 0, 6.4, 5.022, 0, 6.633, -3.796, 0, 6.933, 1.678, 0, 7.2, -1.376, 0, 7.8, 1.486, 0, 8.033, 0.59, 0, 8.1, 0.649, 0, 8.4, -3.644, 0, 8.733, 5.978, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param45", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, 14.679, 0, 0.633, -14.141, 0, 1.033, 4.662, 0, 1.267, 0.117, 0, 1.667, 8.408, 0, 1.967, -4.715, 0, 2.267, 2.102, 0, 2.8, -2.572, 0, 3.567, 2.566, 0, 4.1, -9.431, 0, 4.4, 11.074, 0, 4.7, -3.301, 0, 5, 0.703, 0, 5.3, -4.391, 0, 5.967, 3.241, 0, 6, 3.24, 0, 6.2, 6.52, 0, 6.5, -3.599, 0, 6.7, -2.246, 0, 6.933, -2.93, 0, 7.633, 4.514, 0, 8.2, -4.242, 0, 8.533, 4.621, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param46", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, -10.887, 0, 0.533, 15.203, 0, 0.8, -14.33, 0, 1.167, 9.352, 0, 1.467, -5.505, 0, 1.833, 7.787, 0, 2.1, -7.426, 0, 2.4, 4.221, 0, 2.733, -1.482, 0, 3.067, 0.421, 0, 3.4, -0.937, 0, 3.733, 1.857, 0, 3.933, 1.125, 0, 4.033, 1.613, 0, 4.3, -10.293, 0, 4.567, 11.734, 0, 4.833, -7.306, 0, 5.167, 4.968, 0, 5.467, -3.571, 0, 5.767, 0.986, 0, 6.167, -1.698, 0, 6.4, 5.022, 0, 6.633, -3.796, 0, 6.933, 1.678, 0, 7.2, -1.376, 0, 7.8, 1.486, 0, 8.033, 0.59, 0, 8.1, 0.649, 0, 8.4, -3.644, 0, 8.733, 5.978, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param62", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, 14.679, 0, 0.633, -14.141, 0, 1.033, 4.662, 0, 1.267, 0.117, 0, 1.667, 8.408, 0, 1.967, -4.715, 0, 2.267, 2.102, 0, 2.8, -2.572, 0, 3.567, 2.566, 0, 4.1, -9.431, 0, 4.4, 11.074, 0, 4.7, -3.301, 0, 5, 0.703, 0, 5.3, -4.391, 0, 5.967, 3.241, 0, 6, 3.24, 0, 6.2, 6.52, 0, 6.5, -3.599, 0, 6.7, -2.246, 0, 6.933, -2.93, 0, 7.633, 4.514, 0, 8.2, -4.242, 0, 8.533, 4.621, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param63", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, -10.887, 0, 0.533, 15.203, 0, 0.8, -14.33, 0, 1.167, 9.352, 0, 1.467, -5.505, 0, 1.833, 7.787, 0, 2.1, -7.426, 0, 2.4, 4.221, 0, 2.733, -1.482, 0, 3.067, 0.421, 0, 3.4, -0.937, 0, 3.733, 1.857, 0, 3.933, 1.125, 0, 4.033, 1.613, 0, 4.3, -10.293, 0, 4.567, 11.734, 0, 4.833, -7.306, 0, 5.167, 4.968, 0, 5.467, -3.571, 0, 5.767, 0.986, 0, 6.167, -1.698, 0, 6.4, 5.022, 0, 6.633, -3.796, 0, 6.933, 1.678, 0, 7.2, -1.376, 0, 7.8, 1.486, 0, 8.033, 0.59, 0, 8.1, 0.649, 0, 8.4, -3.644, 0, 8.733, 5.978, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param64", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, 14.679, 0, 0.633, -14.141, 0, 1.033, 4.662, 0, 1.267, 0.117, 0, 1.667, 8.408, 0, 1.967, -4.715, 0, 2.267, 2.102, 0, 2.8, -2.572, 0, 3.567, 2.566, 0, 4.1, -9.431, 0, 4.4, 11.074, 0, 4.7, -3.301, 0, 5, 0.703, 0, 5.3, -4.391, 0, 5.967, 3.241, 0, 6, 3.24, 0, 6.2, 6.52, 0, 6.5, -3.599, 0, 6.7, -2.246, 0, 6.933, -2.93, 0, 7.633, 4.514, 0, 8.2, -4.242, 0, 8.533, 4.621, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param65", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, -10.887, 0, 0.533, 15.203, 0, 0.8, -14.33, 0, 1.167, 9.352, 0, 1.467, -5.505, 0, 1.833, 7.787, 0, 2.1, -7.426, 0, 2.4, 4.221, 0, 2.733, -1.482, 0, 3.067, 0.421, 0, 3.4, -0.937, 0, 3.733, 1.857, 0, 3.933, 1.125, 0, 4.033, 1.613, 0, 4.3, -10.293, 0, 4.567, 11.734, 0, 4.833, -7.306, 0, 5.167, 4.968, 0, 5.467, -3.571, 0, 5.767, 0.986, 0, 6.167, -1.698, 0, 6.4, 5.022, 0, 6.633, -3.796, 0, 6.933, 1.678, 0, 7.2, -1.376, 0, 7.8, 1.486, 0, 8.033, 0.59, 0, 8.1, 0.649, 0, 8.4, -3.644, 0, 8.733, 5.978, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param72", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, 14.679, 0, 0.633, -14.141, 0, 1.033, 4.662, 0, 1.267, 0.117, 0, 1.667, 8.408, 0, 1.967, -4.715, 0, 2.267, 2.102, 0, 2.8, -2.572, 0, 3.567, 2.566, 0, 4.1, -9.431, 0, 4.4, 11.074, 0, 4.7, -3.301, 0, 5, 0.703, 0, 5.3, -4.391, 0, 5.967, 3.241, 0, 6, 3.24, 0, 6.2, 6.52, 0, 6.5, -3.599, 0, 6.7, -2.246, 0, 6.933, -2.93, 0, 7.633, 4.514, 0, 8.2, -4.242, 0, 8.533, 4.621, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param73", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, -10.887, 0, 0.533, 15.203, 0, 0.8, -14.33, 0, 1.167, 9.352, 0, 1.467, -5.505, 0, 1.833, 7.787, 0, 2.1, -7.426, 0, 2.4, 4.221, 0, 2.733, -1.482, 0, 3.067, 0.421, 0, 3.4, -0.937, 0, 3.733, 1.857, 0, 3.933, 1.125, 0, 4.033, 1.613, 0, 4.3, -10.293, 0, 4.567, 11.734, 0, 4.833, -7.306, 0, 5.167, 4.968, 0, 5.467, -3.571, 0, 5.767, 0.986, 0, 6.167, -1.698, 0, 6.4, 5.022, 0, 6.633, -3.796, 0, 6.933, 1.678, 0, 7.2, -1.376, 0, 7.8, 1.486, 0, 8.033, 0.59, 0, 8.1, 0.649, 0, 8.4, -3.644, 0, 8.733, 5.978, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param74", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, 14.679, 0, 0.633, -14.141, 0, 1.033, 4.662, 0, 1.267, 0.117, 0, 1.667, 8.408, 0, 1.967, -4.715, 0, 2.267, 2.102, 0, 2.8, -2.572, 0, 3.567, 2.566, 0, 4.1, -9.431, 0, 4.4, 11.074, 0, 4.7, -3.301, 0, 5, 0.703, 0, 5.3, -4.391, 0, 5.967, 3.241, 0, 6, 3.24, 0, 6.2, 6.52, 0, 6.5, -3.599, 0, 6.7, -2.246, 0, 6.933, -2.93, 0, 7.633, 4.514, 0, 8.2, -4.242, 0, 8.533, 4.621, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param75", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, -10.887, 0, 0.533, 15.203, 0, 0.8, -14.33, 0, 1.167, 9.352, 0, 1.467, -5.505, 0, 1.833, 7.787, 0, 2.1, -7.426, 0, 2.4, 4.221, 0, 2.733, -1.482, 0, 3.067, 0.421, 0, 3.4, -0.937, 0, 3.733, 1.857, 0, 3.933, 1.125, 0, 4.033, 1.613, 0, 4.3, -10.293, 0, 4.567, 11.734, 0, 4.833, -7.306, 0, 5.167, 4.968, 0, 5.467, -3.571, 0, 5.767, 0.986, 0, 6.167, -1.698, 0, 6.4, 5.022, 0, 6.633, -3.796, 0, 6.933, 1.678, 0, 7.2, -1.376, 0, 7.8, 1.486, 0, 8.033, 0.59, 0, 8.1, 0.649, 0, 8.4, -3.644, 0, 8.733, 5.978, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param53", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, 14.679, 0, 0.633, -14.141, 0, 1.033, 4.662, 0, 1.267, 0.117, 0, 1.667, 8.408, 0, 1.967, -4.715, 0, 2.267, 2.102, 0, 2.8, -2.572, 0, 3.567, 2.566, 0, 4.1, -9.431, 0, 4.4, 11.074, 0, 4.7, -3.301, 0, 5, 0.703, 0, 5.3, -4.391, 0, 5.967, 3.241, 0, 6, 3.24, 0, 6.2, 6.52, 0, 6.5, -3.599, 0, 6.7, -2.246, 0, 6.933, -2.93, 0, 7.633, 4.514, 0, 8.2, -4.242, 0, 8.533, 4.621, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param54", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, -10.887, 0, 0.533, 15.203, 0, 0.8, -14.33, 0, 1.167, 9.352, 0, 1.467, -5.505, 0, 1.833, 7.787, 0, 2.1, -7.426, 0, 2.4, 4.221, 0, 2.733, -1.482, 0, 3.067, 0.421, 0, 3.4, -0.937, 0, 3.733, 1.857, 0, 3.933, 1.125, 0, 4.033, 1.613, 0, 4.3, -10.293, 0, 4.567, 11.734, 0, 4.833, -7.306, 0, 5.167, 4.968, 0, 5.467, -3.571, 0, 5.767, 0.986, 0, 6.167, -1.698, 0, 6.4, 5.022, 0, 6.633, -3.796, 0, 6.933, 1.678, 0, 7.2, -1.376, 0, 7.8, 1.486, 0, 8.033, 0.59, 0, 8.1, 0.649, 0, 8.4, -3.644, 0, 8.733, 5.978, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param81", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, 14.679, 0, 0.633, -14.141, 0, 1.033, 4.662, 0, 1.267, 0.117, 0, 1.667, 8.408, 0, 1.967, -4.715, 0, 2.267, 2.102, 0, 2.8, -2.572, 0, 3.567, 2.566, 0, 4.1, -9.431, 0, 4.4, 11.074, 0, 4.7, -3.301, 0, 5, 0.703, 0, 5.3, -4.391, 0, 5.967, 3.241, 0, 6, 3.24, 0, 6.2, 6.52, 0, 6.5, -3.599, 0, 6.7, -2.246, 0, 6.933, -2.93, 0, 7.633, 4.514, 0, 8.2, -4.242, 0, 8.533, 4.621, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param82", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, -10.887, 0, 0.533, 15.203, 0, 0.8, -14.33, 0, 1.167, 9.352, 0, 1.467, -5.505, 0, 1.833, 7.787, 0, 2.1, -7.426, 0, 2.4, 4.221, 0, 2.733, -1.482, 0, 3.067, 0.421, 0, 3.4, -0.937, 0, 3.733, 1.857, 0, 3.933, 1.125, 0, 4.033, 1.613, 0, 4.3, -10.293, 0, 4.567, 11.734, 0, 4.833, -7.306, 0, 5.167, 4.968, 0, 5.467, -3.571, 0, 5.767, 0.986, 0, 6.167, -1.698, 0, 6.4, 5.022, 0, 6.633, -3.796, 0, 6.933, 1.678, 0, 7.2, -1.376, 0, 7.8, 1.486, 0, 8.033, 0.59, 0, 8.1, 0.649, 0, 8.4, -3.644, 0, 8.733, 5.978, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param70", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, 14.679, 0, 0.633, -14.141, 0, 1.033, 4.662, 0, 1.267, 0.117, 0, 1.667, 8.408, 0, 1.967, -4.715, 0, 2.267, 2.102, 0, 2.8, -2.572, 0, 3.567, 2.566, 0, 4.1, -9.431, 0, 4.4, 11.074, 0, 4.7, -3.301, 0, 5, 0.703, 0, 5.3, -4.391, 0, 5.967, 3.241, 0, 6, 3.24, 0, 6.2, 6.52, 0, 6.5, -3.599, 0, 6.7, -2.246, 0, 6.933, -2.93, 0, 7.633, 4.514, 0, 8.2, -4.242, 0, 8.533, 4.621, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param71", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, -10.887, 0, 0.533, 15.203, 0, 0.8, -14.33, 0, 1.167, 9.352, 0, 1.467, -5.505, 0, 1.833, 7.787, 0, 2.1, -7.426, 0, 2.4, 4.221, 0, 2.733, -1.482, 0, 3.067, 0.421, 0, 3.4, -0.937, 0, 3.733, 1.857, 0, 3.933, 1.125, 0, 4.033, 1.613, 0, 4.3, -10.293, 0, 4.567, 11.734, 0, 4.833, -7.306, 0, 5.167, 4.968, 0, 5.467, -3.571, 0, 5.767, 0.986, 0, 6.167, -1.698, 0, 6.4, 5.022, 0, 6.633, -3.796, 0, 6.933, 1.678, 0, 7.2, -1.376, 0, 7.8, 1.486, 0, 8.033, 0.59, 0, 8.1, 0.649, 0, 8.4, -3.644, 0, 8.733, 5.978, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param77", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, 14.679, 0, 0.633, -14.141, 0, 1.033, 4.662, 0, 1.267, 0.117, 0, 1.667, 8.408, 0, 1.967, -4.715, 0, 2.267, 2.102, 0, 2.8, -2.572, 0, 3.567, 2.566, 0, 4.1, -9.431, 0, 4.4, 11.074, 0, 4.7, -3.301, 0, 5, 0.703, 0, 5.3, -4.391, 0, 5.967, 3.241, 0, 6, 3.24, 0, 6.2, 6.52, 0, 6.5, -3.599, 0, 6.7, -2.246, 0, 6.933, -2.93, 0, 7.633, 4.514, 0, 8.2, -4.242, 0, 8.533, 4.621, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param78", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, -10.887, 0, 0.533, 15.203, 0, 0.8, -14.33, 0, 1.167, 9.352, 0, 1.467, -5.505, 0, 1.833, 7.787, 0, 2.1, -7.426, 0, 2.4, 4.221, 0, 2.733, -1.482, 0, 3.067, 0.421, 0, 3.4, -0.937, 0, 3.733, 1.857, 0, 3.933, 1.125, 0, 4.033, 1.613, 0, 4.3, -10.293, 0, 4.567, 11.734, 0, 4.833, -7.306, 0, 5.167, 4.968, 0, 5.467, -3.571, 0, 5.767, 0.986, 0, 6.167, -1.698, 0, 6.4, 5.022, 0, 6.633, -3.796, 0, 6.933, 1.678, 0, 7.2, -1.376, 0, 7.8, 1.486, 0, 8.033, 0.59, 0, 8.1, 0.649, 0, 8.4, -3.644, 0, 8.733, 5.978, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param79", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, 14.679, 0, 0.633, -14.141, 0, 1.033, 4.662, 0, 1.267, 0.117, 0, 1.667, 8.408, 0, 1.967, -4.715, 0, 2.267, 2.102, 0, 2.8, -2.572, 0, 3.567, 2.566, 0, 4.1, -9.431, 0, 4.4, 11.074, 0, 4.7, -3.301, 0, 5, 0.703, 0, 5.3, -4.391, 0, 5.967, 3.241, 0, 6, 3.24, 0, 6.2, 6.52, 0, 6.5, -3.599, 0, 6.7, -2.246, 0, 6.933, -2.93, 0, 7.633, 4.514, 0, 8.2, -4.242, 0, 8.533, 4.621, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param80", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, -10.887, 0, 0.533, 15.203, 0, 0.8, -14.33, 0, 1.167, 9.352, 0, 1.467, -5.505, 0, 1.833, 7.787, 0, 2.1, -7.426, 0, 2.4, 4.221, 0, 2.733, -1.482, 0, 3.067, 0.421, 0, 3.4, -0.937, 0, 3.733, 1.857, 0, 3.933, 1.125, 0, 4.033, 1.613, 0, 4.3, -10.293, 0, 4.567, 11.734, 0, 4.833, -7.306, 0, 5.167, 4.968, 0, 5.467, -3.571, 0, 5.767, 0.986, 0, 6.167, -1.698, 0, 6.4, 5.022, 0, 6.633, -3.796, 0, 6.933, 1.678, 0, 7.2, -1.376, 0, 7.8, 1.486, 0, 8.033, 0.59, 0, 8.1, 0.649, 0, 8.4, -3.644, 0, 8.733, 5.978, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param294", "Segments": [0, 1, 2, 0.333, 1, 0, 0.4, 0, 2, 8.7, 0, 0, 8.8, 1]}, {"Target": "Parameter", "Id": "Param47", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, 5.177, 0, 0.633, -7.244, 0, 1.033, 2.73, 0, 1.267, 0.132, 0, 1.667, 6.178, 0, 1.967, -3.617, 0, 2.267, 1.543, 0, 2.733, -1.284, 0, 2.967, -1.136, 0, 3.1, -1.22, 0, 3.567, 1.552, 0, 4.1, -7.199, 0, 4.4, 8.219, 0, 4.7, -2.51, 0, 5, 0.581, 0, 5.3, -3.804, 0, 5.9, 2.401, 0, 6, 2.175, 0, 6.2, 4.578, 0, 6.5, -2.635, 0, 6.8, -0.9, 0, 7, -1.149, 0, 7.633, 2.601, 0, 8.2, -2.632, 0, 8.533, 3.674, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param48", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, -3.671, 0, 0.567, 6.424, 0, 0.9, -6.934, 0, 1.167, 4.904, 0, 1.5, -3.337, 0, 1.833, 5.603, 0, 2.1, -5.467, 0, 2.4, 3.112, 0, 2.733, -1.308, 0, 3.067, 0.537, 0, 3.367, -0.711, 0, 3.733, 1.17, 0, 3.9, 0.91, 0, 4.033, 1.38, 0, 4.3, -7.818, 0, 4.567, 8.824, 0, 4.833, -5.494, 0, 5.167, 3.947, 0, 5.467, -2.966, 0, 5.767, 0.801, 0, 6.167, -1.481, 0, 6.4, 3.69, 0, 6.667, -2.841, 0, 6.933, 1.363, 0, 7.267, -0.905, 0, 7.833, 0.927, 0, 8.033, 0.32, 0, 8.1, 0.415, 0, 8.4, -2.541, 0, 8.733, 4.293, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param56", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, 2.063, 0, 0.7, -3.041, 0, 1.667, 2.902, 0, 2.033, -1.845, 0, 2.533, 0.561, 0, 3.067, -1.197, 0, 3.633, 1.32, 0, 4.133, -4.151, 0, 4.5, 3.344, 0, 5.233, -2.523, 0, 5.933, 2.064, 0, 6.033, 1.926, 0, 6.167, 2.352, 0, 6.667, -2, 0, 7.667, 1.377, 0, 8.233, -1.816, 0, 8.6, 1.614, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param57", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, -1.871, 0, 0.567, 3.24, 0, 0.967, -3.464, 0, 1.267, 0.474, 0, 1.567, -0.114, 0, 1.867, 3.565, 0, 2.167, -1.637, 0, 2.667, 0.549, 0, 3.333, -1.008, 0, 4.033, 1.394, 0, 4.333, -5.601, 0, 4.633, 3.436, 0, 5, -0.041, 0, 5.133, 0.241, 0, 5.467, -1.867, 0, 6.033, 0.761, 0, 6.167, 0.298, 0, 6.4, 2.695, 0, 6.767, -1.109, 0, 7.433, -0.044, 0, 7.533, -0.068, 0, 7.933, 1.09, 0, 8, 1.073, 0, 8.067, 1.082, 0, 8.433, -2.116, 0, 8.767, 2.627, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param58", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, -1.427, 0, 0.7, 4.033, 0, 1.067, -5.011, 0, 1.367, 2.777, 0, 1.667, -1.777, 0, 1.967, 3.715, 0, 2.3, -3.629, 0, 2.633, 1.919, 0, 3, -0.279, 0, 3.133, -0.216, 0, 3.433, -0.766, 0, 4.067, 1.572, 0, 4.433, -6.142, 0, 4.733, 6.228, 0, 5.067, -2.719, 0, 5.333, 1.107, 0, 5.6, -1.78, 0, 6, 0.775, 0, 6.267, -0.467, 0, 6.533, 2.329, 0, 6.833, -2.174, 0, 7.167, 0.559, 0, 7.6, -0.225, 0, 8.067, 0.904, 0, 8.533, -2.575, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param59", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, 2.063, 0, 0.7, -3.041, 0, 1.667, 2.902, 0, 2.033, -1.845, 0, 2.533, 0.561, 0, 3.067, -1.197, 0, 3.633, 1.32, 0, 4.133, -4.151, 0, 4.5, 3.344, 0, 5.233, -2.523, 0, 5.933, 2.064, 0, 6.033, 1.926, 0, 6.167, 2.352, 0, 6.667, -2, 0, 7.667, 1.377, 0, 8.233, -1.816, 0, 8.6, 1.614, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param60", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, -1.871, 0, 0.567, 3.24, 0, 0.967, -3.464, 0, 1.267, 0.474, 0, 1.567, -0.114, 0, 1.867, 3.565, 0, 2.167, -1.637, 0, 2.667, 0.549, 0, 3.333, -1.008, 0, 4.033, 1.394, 0, 4.333, -5.601, 0, 4.633, 3.436, 0, 5, -0.041, 0, 5.133, 0.241, 0, 5.467, -1.867, 0, 6.033, 0.761, 0, 6.167, 0.298, 0, 6.4, 2.695, 0, 6.767, -1.109, 0, 7.433, -0.044, 0, 7.533, -0.068, 0, 7.933, 1.09, 0, 8, 1.073, 0, 8.067, 1.082, 0, 8.433, -2.116, 0, 8.767, 2.627, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param61", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, -1.427, 0, 0.7, 4.033, 0, 1.067, -5.011, 0, 1.367, 2.777, 0, 1.667, -1.777, 0, 1.967, 3.715, 0, 2.3, -3.629, 0, 2.633, 1.919, 0, 3, -0.279, 0, 3.133, -0.216, 0, 3.433, -0.766, 0, 4.067, 1.572, 0, 4.433, -6.142, 0, 4.733, 6.228, 0, 5.067, -2.719, 0, 5.333, 1.107, 0, 5.6, -1.78, 0, 6, 0.775, 0, 6.267, -0.467, 0, 6.533, 2.329, 0, 6.833, -2.174, 0, 7.167, 0.559, 0, 7.6, -0.225, 0, 8.067, 0.904, 0, 8.533, -2.575, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param49", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, 5.177, 0, 0.633, -7.244, 0, 1.033, 2.73, 0, 1.267, 0.132, 0, 1.667, 6.178, 0, 1.967, -3.617, 0, 2.267, 1.543, 0, 2.733, -1.284, 0, 2.967, -1.136, 0, 3.1, -1.22, 0, 3.567, 1.552, 0, 4.1, -7.199, 0, 4.4, 8.219, 0, 4.7, -2.51, 0, 5, 0.581, 0, 5.3, -3.804, 0, 5.9, 2.401, 0, 6, 2.175, 0, 6.2, 4.578, 0, 6.5, -2.635, 0, 6.8, -0.9, 0, 7, -1.149, 0, 7.633, 2.601, 0, 8.2, -2.632, 0, 8.533, 3.674, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param50", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, -3.671, 0, 0.567, 6.424, 0, 0.9, -6.934, 0, 1.167, 4.904, 0, 1.5, -3.337, 0, 1.833, 5.603, 0, 2.1, -5.467, 0, 2.4, 3.112, 0, 2.733, -1.308, 0, 3.067, 0.537, 0, 3.367, -0.711, 0, 3.733, 1.17, 0, 3.9, 0.91, 0, 4.033, 1.38, 0, 4.3, -7.818, 0, 4.567, 8.824, 0, 4.833, -5.494, 0, 5.167, 3.947, 0, 5.467, -2.966, 0, 5.767, 0.801, 0, 6.167, -1.481, 0, 6.4, 3.69, 0, 6.667, -2.841, 0, 6.933, 1.363, 0, 7.267, -0.905, 0, 7.833, 0.927, 0, 8.033, 0.32, 0, 8.1, 0.415, 0, 8.4, -2.541, 0, 8.733, 4.293, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param51", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, 5.177, 0, 0.633, -7.244, 0, 1.033, 2.73, 0, 1.267, 0.132, 0, 1.667, 6.178, 0, 1.967, -3.617, 0, 2.267, 1.543, 0, 2.733, -1.284, 0, 2.967, -1.136, 0, 3.1, -1.22, 0, 3.567, 1.552, 0, 4.1, -7.199, 0, 4.4, 8.219, 0, 4.7, -2.51, 0, 5, 0.581, 0, 5.3, -3.804, 0, 5.9, 2.401, 0, 6, 2.175, 0, 6.2, 4.578, 0, 6.5, -2.635, 0, 6.8, -0.9, 0, 7, -1.149, 0, 7.633, 2.601, 0, 8.2, -2.632, 0, 8.533, 3.674, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param52", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, -3.671, 0, 0.567, 6.424, 0, 0.9, -6.934, 0, 1.167, 4.904, 0, 1.5, -3.337, 0, 1.833, 5.603, 0, 2.1, -5.467, 0, 2.4, 3.112, 0, 2.733, -1.308, 0, 3.067, 0.537, 0, 3.367, -0.711, 0, 3.733, 1.17, 0, 3.9, 0.91, 0, 4.033, 1.38, 0, 4.3, -7.818, 0, 4.567, 8.824, 0, 4.833, -5.494, 0, 5.167, 3.947, 0, 5.467, -2.966, 0, 5.767, 0.801, 0, 6.167, -1.481, 0, 6.4, 3.69, 0, 6.667, -2.841, 0, 6.933, 1.363, 0, 7.267, -0.905, 0, 7.833, 0.927, 0, 8.033, 0.32, 0, 8.1, 0.415, 0, 8.4, -2.541, 0, 8.733, 4.293, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation63", "Segments": [0, -0.675, 1, 0.111, -0.675, 0.222, -0.527, 0.333, 0, 1, 0.355, 0.105, 0.378, 1.726, 0.4, 1.726, 0, 0.633, -2.415, 0, 1.033, 0.91, 0, 1.267, 0.044, 0, 1.667, 2.059, 0, 1.967, -1.206, 0, 2.267, 0.514, 0, 2.733, -0.428, 0, 2.967, -0.379, 0, 3.1, -0.407, 0, 3.567, 0.517, 0, 4.1, -2.4, 0, 4.4, 2.74, 0, 4.7, -0.837, 0, 5, 0.194, 0, 5.3, -1.268, 0, 5.9, 0.8, 0, 6, 0.725, 0, 6.2, 1.526, 0, 6.5, -0.878, 0, 6.8, -0.3, 0, 7, -0.383, 0, 7.633, 0.867, 0, 8.2, -0.877, 0, 8.533, 1.225, 0, 8.8, -0.675]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation64", "Segments": [0, 2.951, 1, 0.111, 2.951, 0.222, 2.193, 0.333, 0, 1, 0.355, -0.439, 0.378, -1.835, 0.4, -1.835, 0, 0.567, 3.212, 0, 0.9, -3.467, 0, 1.167, 2.452, 0, 1.5, -1.669, 0, 1.833, 2.801, 0, 2.1, -2.734, 0, 2.4, 1.556, 0, 2.733, -0.654, 0, 3.067, 0.269, 0, 3.367, -0.355, 0, 3.733, 0.585, 0, 3.9, 0.455, 0, 4.033, 0.69, 0, 4.3, -3.909, 0, 4.567, 4.412, 0, 4.833, -2.747, 0, 5.167, 1.974, 0, 5.467, -1.483, 0, 5.767, 0.401, 0, 6.167, -0.741, 0, 6.4, 1.845, 0, 6.667, -1.421, 0, 6.933, 0.682, 0, 7.267, -0.453, 0, 7.833, 0.464, 0, 8.033, 0.16, 0, 8.1, 0.208, 0, 8.4, -1.27, 1, 8.511, -1.27, 8.622, -0.189, 8.733, 2.147, 1, 8.755, 2.614, 8.778, 2.951, 8.8, 2.951]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation65", "Segments": [0, 4.849, 1, 0.111, 4.849, 0.222, 3.178, 0.333, 0, 1, 0.366, -0.953, 0.4, -1.355, 0.433, -1.355, 0, 0.667, 4.221, 0, 1, -6.13, 0, 1.3, 5.391, 0, 1.6, -4.383, 0, 1.933, 5.442, 0, 2.233, -5.564, 0, 2.533, 4.152, 0, 2.833, -2.36, 0, 3.167, 1.184, 0, 3.467, -0.971, 0, 3.833, 1.21, 0, 4.4, -5.25, 0, 4.667, 7.736, 0, 4.967, -6.382, 0, 5.267, 4.897, 0, 5.567, -3.824, 0, 5.9, 1.906, 0, 6.233, -1.428, 0, 6.5, 2.8, 0, 6.8, -2.853, 0, 7.067, 1.905, 0, 7.367, -1.339, 0, 7.867, 0.716, 0, 8.5, -1.873, 0, 8.8, 4.849]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation66", "Segments": [0, -11.659, 2, 0.333, -11.659, 0, 0.367, 0, 0, 0.5, -1.394, 0, 0.767, 5.605, 0, 1.1, -9.671, 0, 1.4, 10.043, 0, 1.733, -9.216, 0, 2.033, 10.326, 0, 2.333, -10.667, 0, 2.633, 8.946, 0, 2.967, -6.19, 0, 3.267, 3.679, 0, 3.567, -2.619, 0, 3.933, 2.635, 0, 4.467, -6.934, 0, 4.767, 12.339, 0, 5.1, -12.032, 0, 5.4, 10.252, 0, 5.7, -8.588, 0, 6, 5.438, 0, 6.333, -3.664, 0, 6.6, 4.765, 0, 6.9, -5.426, 0, 7.2, 4.338, 0, 7.5, -3.354, 0, 7.833, 2.038, 1, 8.078, 2.038, 8.322, 1.171, 8.567, -2.628, 1, 8.645, -3.837, 8.722, -11.659, 8.8, -11.659]}, {"Target": "Parameter", "Id": "Param55", "Segments": [0, -10.57, 2, 0.333, -10.57, 0, 0.4, 0, 0, 0.6, -1.574, 0, 0.933, 7.323, 0, 1.2, -14.411, 0, 1.533, 16.972, 0, 1.833, -16.956, 0, 2.133, 18.148, 0, 2.433, -18.712, 0, 2.767, 16.886, 0, 3.067, -13.137, 0, 3.4, 8.984, 0, 3.7, -6.542, 0, 4.033, 5.808, 0, 4.533, -8.749, 0, 4.867, 18.111, 0, 5.2, -20.08, 0, 5.5, 18.707, 0, 5.8, -16.494, 0, 6.133, 12.036, 0, 6.433, -8.475, 0, 6.733, 8.71, 0, 7, -9.674, 0, 7.3, 8.624, 0, 7.633, -7.236, 0, 7.933, 5.302, 0, 8.3, -1.971, 0, 8.5, -0.926, 1, 8.567, -0.926, 8.633, -1.123, 8.7, -2.776, 1, 8.733, -3.602, 8.767, -10.57, 8.8, -10.57]}, {"Target": "Parameter", "Id": "Param28", "Segments": [0, -7.12, 2, 0.333, -7.12, 0, 1.367, 7.36, 0, 2.433, -7.12, 0, 3.467, 7.36, 0, 4.533, -7.12, 0, 5.6, 7.36, 0, 6.633, -7.12, 0, 7.7, 7.36, 0, 8.8, -7.12]}, {"Target": "Parameter", "Id": "Param29", "Segments": [0, -1.278, 2, 0.333, -1.278, 0, 0.667, -4.74, 0, 1.8, 5.1, 0, 2.833, -4.74, 0, 3.9, 5.1, 1, 4.111, 5.1, 4.322, 2.563, 4.533, -1.278, 1, 4.678, -3.906, 4.822, -4.74, 4.967, -4.74, 0, 6, 5.1, 0, 7.067, -4.74, 0, 8.1, 5.1, 0, 8.8, -1.278]}, {"Target": "Parameter", "Id": "Param30", "Segments": [0, -5.909, 2, 0.333, -5.909, 0, 1.233, 2.533, 0, 2.3, -6.287, 0, 3.333, 2.533, 0, 4.4, -6.287, 1, 4.444, -6.287, 4.489, -6.534, 4.533, -5.909, 1, 4.844, -1.531, 5.156, 2.533, 5.467, 2.533, 0, 6.5, -6.287, 0, 7.567, 2.533, 0, 8.667, -6.287, 0, 8.8, -5.909]}, {"Target": "Parameter", "Id": "Param31", "Segments": [0, -0.551, 2, 0.333, -0.551, 0, 0.7, 3.018, 0, 1.833, -6.18, 0, 2.867, 3.018, 0, 3.933, -6.18, 1, 4.133, -6.18, 4.333, -4.096, 4.533, -0.551, 1, 4.678, 2.009, 4.822, 3.018, 4.967, 3.018, 0, 6.033, -6.18, 0, 7.1, 3.018, 0, 8.133, -6.18, 0, 8.8, -0.551]}, {"Target": "Parameter", "Id": "Param32", "Segments": [0, -2.714, 2, 0.333, -2.714, 0, 1.3, 2.284, 0, 2.333, -2.796, 0, 3.4, 2.284, 0, 4.467, -2.796, 1, 4.489, -2.796, 4.511, -2.904, 4.533, -2.714, 1, 4.855, 0.036, 5.178, 2.284, 5.5, 2.284, 0, 6.567, -2.796, 0, 7.6, 2.284, 0, 8.733, -2.796, 0, 8.8, -2.714]}, {"Target": "Parameter", "Id": "Param33", "Segments": [0, -2.392, 2, 0.333, -2.392, 0, 0.733, 3.279, 0, 1.833, -10.047, 0, 2.9, 3.279, 0, 3.967, -10.047, 1, 4.156, -10.047, 4.344, -7.249, 4.533, -2.392, 1, 4.689, 1.608, 4.844, 3.279, 5, 3.279, 0, 6.067, -10.047, 0, 7.1, 3.279, 0, 8.167, -10.047, 0, 8.8, -2.392]}, {"Target": "Parameter", "Id": "Param34", "Segments": [0, -21.182, 2, 0.333, -21.182, 0, 1.333, 11.7, 0, 2.367, -21.42, 0, 3.433, 11.7, 0, 4.467, -21.42, 1, 4.489, -21.42, 4.511, -22.056, 4.533, -21.182, 1, 4.866, -8.062, 5.2, 11.7, 5.533, 11.7, 0, 6.6, -21.42, 0, 7.633, 11.7, 0, 8.767, -21.42, 0, 8.8, -21.182]}, {"Target": "Parameter", "Id": "Param84", "Segments": [0, 0, 2, 0.333, 0, 0, 1.4, -6.9, 0, 2.467, 0, 0, 3.533, -6.9, 0, 4.6, 0, 0, 5.667, -6.9, 0, 6.733, 0, 0, 7.8, -6.9, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param85", "Segments": [0, 0, 2, 0.333, 0, 0, 0.8, 4.74, 0, 1.933, -4.74, 1, 2.111, -4.74, 2.289, -3.16, 2.467, 0, 1, 2.645, 3.16, 2.822, 4.74, 3, 4.74, 0, 4.067, -4.74, 1, 4.245, -4.74, 4.422, -3.16, 4.6, 0, 1, 4.778, 3.16, 4.955, 4.74, 5.133, 4.74, 0, 6.2, -4.74, 1, 6.378, -4.74, 6.555, -3.16, 6.733, 0, 1, 6.911, 3.16, 7.089, 4.74, 7.267, 4.74, 0, 8.333, -4.74, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param83", "Segments": [0, 0, 2, 0.333, 0, 0, 1.4, 3.96, 0, 2.467, 0, 0, 3.533, 3.96, 0, 4.6, 0, 0, 5.667, 3.96, 0, 6.733, 0, 0, 7.8, 3.96, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param86", "Segments": [0, 1, 2, 0.333, 1, 2, 1.833, 1, 0, 1.933, 0, 0, 2.033, 1, 2, 2.467, 1, 2, 3.967, 1, 0, 4.067, 0, 0, 4.167, 1, 2, 4.6, 1, 2, 6.1, 1, 0, 6.2, 0, 0, 6.3, 1, 2, 6.733, 1, 2, 8.233, 1, 0, 8.333, 0, 0, 8.433, 1, 2, 8.8, 1]}, {"Target": "Parameter", "Id": "Param95", "Segments": [0, 0, 2, 0.333, 0, 0, 1.4, 10, 0, 2.467, 0, 0, 3.533, 10, 0, 4.6, 0, 0, 5.667, 10, 0, 6.733, 0, 0, 7.8, 10, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param92", "Segments": [0, 0, 2, 0.333, 0, 0, 0.8, -12, 0, 1.933, 12, 1, 2.111, 12, 2.289, 8, 2.467, 0, 1, 2.645, -8, 2.822, -12, 3, -12, 0, 4.067, 12, 1, 4.245, 12, 4.422, 8, 4.6, 0, 1, 4.778, -8, 4.955, -12, 5.133, -12, 0, 6.2, 12, 1, 6.378, 12, 6.555, 8, 6.733, 0, 1, 6.911, -8, 7.089, -12, 7.267, -12, 0, 8.333, 12, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param93", "Segments": [0, 0, 2, 0.333, 0, 0, 1.4, -12, 0, 2.467, 0, 0, 3.533, -12, 0, 4.6, 0, 0, 5.667, -12, 0, 6.733, 0, 0, 7.8, -12, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param96", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, -16, 0, 0.533, 0, 0, 0.667, -16, 0, 0.9, 0, 2, 2.467, 0, 0, 2.6, -16, 0, 2.733, 0, 0, 2.867, -16, 0, 3.033, 0, 2, 4.6, 0, 0, 4.733, -16, 0, 4.867, 0, 0, 5, -16, 0, 5.167, 0, 2, 6.733, 0, 0, 6.867, -16, 0, 7, 0, 0, 7.133, -16, 0, 7.3, 0, 2, 8.8, 0]}, {"Target": "Parameter", "Id": "Param97", "Segments": [0, 0, 2, 0.333, 0, 0, 0.467, -14, 1, 0.489, -14, 0.511, -7.166, 0.533, 0, 1, 0.566, 10.749, 0.6, 14, 0.633, 14, 0, 0.767, -14, 1, 0.811, -14, 0.856, -0.845, 0.9, 0, 1, 1.422, 9.93, 1.945, 14, 2.467, 14, 0, 2.667, -14, 1, 2.689, -14, 2.711, -7.166, 2.733, 0, 1, 2.766, 10.749, 2.8, 14, 2.833, 14, 0, 2.967, -14, 1, 2.989, -14, 3.011, -0.41, 3.033, 0, 1, 3.555, 9.643, 4.078, 14, 4.6, 14, 0, 4.8, -14, 1, 4.822, -14, 4.845, -7.166, 4.867, 0, 1, 4.9, 10.749, 4.934, 14, 4.967, 14, 0, 5.1, -14, 1, 5.122, -14, 5.145, -0.41, 5.167, 0, 1, 5.689, 9.643, 6.211, 14, 6.733, 14, 0, 6.933, -14, 1, 6.955, -14, 6.978, -7.166, 7, 0, 1, 7.033, 10.749, 7.067, 14, 7.1, 14, 0, 7.233, -14, 0, 7.3, 0, 2, 8.8, 0]}, {"Target": "Parameter", "Id": "Param89", "Segments": [0, 0, 2, 0.333, 0, 0, 1.4, 18.72, 0, 2.467, 0, 0, 3.533, 18.72, 0, 4.6, 0, 0, 5.667, 18.72, 0, 6.733, 0, 0, 7.8, 18.72, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param90", "Segments": [0, 0, 2, 0.333, 0, 0, 0.8, -19, 0, 1.933, 19, 1, 2.111, 19, 2.289, 12.667, 2.467, 0, 1, 2.645, -12.667, 2.822, -19, 3, -19, 0, 4.067, 19, 1, 4.245, 19, 4.422, 12.667, 4.6, 0, 1, 4.778, -12.667, 4.955, -19, 5.133, -19, 0, 6.2, 19, 1, 6.378, 19, 6.555, 12.667, 6.733, 0, 1, 6.911, -12.667, 7.089, -19, 7.267, -19, 0, 8.333, 19, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation56", "Segments": [0, 0, 2, 0.333, 0, 0, 0.8, 9, 0, 1.4, -9, 0, 1.933, 9, 0, 2.467, 0, 0, 3, 9, 0, 3.533, -9, 0, 4.067, 9, 0, 4.6, 0, 0, 5.133, 9, 0, 5.667, -9, 0, 6.2, 9, 0, 6.733, 0, 0, 7.267, 9, 0, 7.8, -9, 0, 8.333, 9, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param98", "Segments": [0, -6, 2, 0.333, -6, 0, 0.533, 0, 0, 1.133, -12, 0, 1.667, 12, 0, 2.2, -12, 0, 2.733, 0, 0, 3.267, -12, 0, 3.8, 12, 0, 4.333, -12, 1, 4.422, -12, 4.511, -10, 4.6, -6, 1, 4.689, -2, 4.778, 0, 4.867, 0, 0, 5.4, -12, 0, 5.933, 12, 0, 6.467, -12, 0, 7, 0, 0, 7.533, -12, 0, 8.067, 12, 0, 8.6, -12, 0, 8.8, -6]}, {"Target": "Parameter", "Id": "Param99", "Segments": [0, -10.921, 2, 0.333, -10.921, 0, 0.767, 0, 0, 1.367, -11, 0, 1.9, 11, 0, 2.433, -11, 0, 2.967, 0, 0, 3.5, -11, 0, 4.033, 11, 0, 4.567, -11, 1, 4.578, -11, 4.589, -11.221, 4.6, -10.921, 1, 4.767, -6.427, 4.933, 0, 5.1, 0, 0, 5.633, -11, 0, 6.167, 11, 0, 6.7, -11, 0, 7.233, 0, 0, 7.767, -11, 0, 8.3, 11, 0, 8.767, -11, 0, 8.8, -10.921]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation67", "Segments": [0, -10.939, 2, 0.333, -10.939, 0, 0.433, -25, 0, 1.033, 0, 0, 1.567, -25, 0, 2.1, 25, 0, 2.633, -25, 0, 3.167, 0, 0, 3.7, -25, 0, 4.233, 25, 1, 4.355, 25, 4.478, 11.857, 4.6, -10.939, 1, 4.656, -21.301, 4.711, -25, 4.767, -25, 0, 5.3, 0, 0, 5.833, -25, 0, 6.367, 25, 0, 6.9, -25, 0, 7.433, 0, 0, 7.967, -25, 0, 8.5, 25, 0, 8.8, -10.939]}, {"Target": "Parameter", "Id": "Param281", "Segments": [0, 0, 2, 0.333, 0, 0, 1.733, 1, 0, 3.133, 0, 0, 4.533, 1, 0, 5.933, 0, 0, 7.333, 1, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param197mao2", "Segments": [0, 0, 2, 0.333, 0, 0, 0.967, 2.894, 0, 2.5, -2.454, 1, 2.711, -2.454, 2.922, -1.77, 3.133, 0, 1, 3.344, 1.77, 3.556, 2.894, 3.767, 2.894, 0, 5.3, -2.454, 1, 5.511, -2.454, 5.722, -1.77, 5.933, 0, 1, 6.144, 1.77, 6.356, 2.894, 6.567, 2.894, 0, 8.1, -2.454, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param216mao2", "Segments": [0, -8.88, 0, 0.333, -8.9, 2, 4.6, -8.9, 0, 8.8, -8.88]}, {"Target": "Parameter", "Id": "Param212mao2", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, -16, 0, 0.533, 12.568, 0, 0.667, -16, 0, 0.9, 0, 2, 2.467, 0, 2, 4.6, 0, 0, 4.733, -16, 0, 4.867, 12.568, 0, 5, -16, 0, 5.167, 0, 2, 6.733, 0, 2, 8.8, 0]}, {"Target": "Parameter", "Id": "Param214mao2", "Segments": [0, 0, 2, 0.333, 0, 2, 1.267, 0, 0, 1.4, 16, 0, 1.533, 0, 0, 1.7, 16, 0, 1.867, 0, 2, 2.467, 0, 2, 4.6, 0, 2, 5.533, 0, 0, 5.667, 16, 0, 5.8, 0, 0, 5.967, 16, 0, 6.133, 0, 2, 6.733, 0, 2, 8.8, 0]}, {"Target": "Parameter", "Id": "Param215mao2", "Segments": [0, 0, 2, 0.333, 0, 2, 1.267, 0, 0, 1.333, -14, 0, 1.5, 15, 0, 1.633, -8, 0, 1.767, 9, 0, 1.967, 0, 2, 2.467, 0, 2, 4.6, 0, 2, 5.533, 0, 0, 5.6, -14, 0, 5.767, 15, 0, 5.9, -8, 0, 6.033, 9, 0, 6.233, 0, 2, 6.733, 0, 2, 8.8, 0]}, {"Target": "Parameter", "Id": "Param198mao2", "Segments": [0, 0, 2, 0.333, 0, 0, 1.333, 30, 0, 2.467, 0, 0, 3.4, 30, 0, 4.6, 0, 0, 5.6, 30, 0, 6.733, 0, 0, 7.667, 30, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation143mao2", "Segments": [0, 0, 2, 0.333, 0, 0, 0.733, 17, 0, 1.4, 0, 0, 1.933, 17, 2, 2.467, 17, 2, 2.933, 17, 0, 3.533, 0, 0, 4.067, 17, 0, 4.6, 0, 0, 5.067, 17, 0, 5.667, 0, 0, 6.2, 17, 2, 6.733, 17, 2, 7.2, 17, 0, 7.8, 0, 0, 8.333, 17, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation144mao2", "Segments": [0, -10.82, 2, 0.333, -10.82, 0, 0.4, -19.438, 0, 0.967, 17.12, 0, 1.533, -19.438, 0, 2.1, 17.12, 1, 2.222, 17.12, 2.345, 6.948, 2.467, -10.82, 1, 2.511, -17.281, 2.556, -19.438, 2.6, -19.438, 0, 3.1, 17.12, 0, 3.667, -19.438, 0, 4.233, 17.12, 1, 4.355, 17.12, 4.478, 6.948, 4.6, -10.82, 1, 4.644, -17.281, 4.689, -19.438, 4.733, -19.438, 0, 5.233, 17.12, 0, 5.8, -19.438, 0, 6.367, 17.12, 1, 6.489, 17.12, 6.611, 6.948, 6.733, -10.82, 1, 6.778, -17.281, 6.822, -19.438, 6.867, -19.438, 0, 7.367, 17.12, 0, 7.933, -19.438, 0, 8.5, 17.12, 0, 8.8, -10.82]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation145mao2", "Segments": [0, 8.46, 2, 0.333, 8.46, 0, 0.6, -17.322, 0, 1.133, 16.411, 0, 1.733, -17.322, 0, 2.3, 16.411, 1, 2.356, 16.411, 2.411, 15.533, 2.467, 8.46, 1, 2.578, -5.686, 2.689, -17.322, 2.8, -17.322, 0, 3.267, 16.411, 0, 3.867, -17.322, 0, 4.433, 16.411, 1, 4.489, 16.411, 4.544, 15.533, 4.6, 8.46, 1, 4.711, -5.686, 4.822, -17.322, 4.933, -17.322, 0, 5.4, 16.411, 0, 6, -17.322, 0, 6.567, 16.411, 1, 6.622, 16.411, 6.678, 15.533, 6.733, 8.46, 1, 6.844, -5.686, 6.956, -17.322, 7.067, -17.322, 0, 7.533, 16.411, 0, 8.133, -17.322, 0, 8.7, 16.411, 0, 8.8, 8.46]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation146mao2", "Segments": [0, 28.83, 2, 0.333, 28.83, 0, 0.8, -30, 0, 1.333, 29.224, 0, 1.933, -30, 0, 2.467, 29.224, 0, 3, -30, 0, 3.467, 29.224, 0, 4.067, -30, 0, 4.6, 29.224, 0, 5.133, -30, 0, 5.6, 29.224, 0, 6.2, -30, 0, 6.733, 29.224, 0, 7.267, -30, 0, 7.733, 29.224, 0, 8.333, -30, 0, 8.8, 28.83]}, {"Target": "Parameter", "Id": "Param100", "Segments": [0, 0, 2, 0.333, 0, 2, 4.6, 0, 0, 5.667, -2.04, 0, 6.733, 0, 0, 7.8, -2.04, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param105", "Segments": [0, 0, 2, 0.333, 0, 2, 4.6, 0, 0, 5.133, 16, 0, 6.2, -16, 1, 6.378, -16, 6.555, -6.317, 6.733, 0, 1, 7.089, 12.634, 7.444, 16, 7.8, 16, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param108", "Segments": [0, 0, 2, 0.333, 0, 0, 1.4, 4, 0, 2.467, 0, 0, 3.533, 4, 0, 4.6, 0, 0, 5.667, 4, 0, 6.733, 0, 0, 7.8, 4, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param109", "Segments": [0, 0, 2, 0.333, 0, 0, 0.8, -4, 0, 1.933, 4.46, 1, 2.111, 4.46, 2.289, 2.811, 2.467, 0, 1, 2.645, -2.811, 2.822, -4, 3, -4, 0, 4.067, 4.46, 1, 4.245, 4.46, 4.422, 2.811, 4.6, 0, 1, 4.778, -2.811, 4.955, -4, 5.133, -4, 0, 6.2, 4.46, 1, 6.378, 4.46, 6.555, 2.811, 6.733, 0, 1, 6.911, -2.811, 7.089, -4, 7.267, -4, 0, 8.333, 4.46, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param101", "Segments": [0, 0, 2, 0.333, 0, 0, 0.367, -21, 0, 0.433, 0, 0, 0.533, -21, 0, 0.633, 0, 2, 2.467, 0, 0, 2.533, -21, 0, 2.633, 0, 0, 2.733, -21, 0, 2.833, 0, 0, 3.533, -21, 0, 4.6, 0, 0, 4.667, -21, 0, 4.767, 0, 0, 4.867, -21, 0, 4.967, 0, 2, 6.733, 0, 0, 6.8, -21, 0, 6.9, 0, 0, 7, -21, 0, 7.1, 0, 0, 7.8, -21, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param106", "Segments": [0, 0, 2, 0.333, 0, 0, 1.4, 15.405, 0, 2.467, 0, 0, 3.533, 15.405, 0, 4.6, 0, 0, 5.667, 15.405, 0, 6.733, 0, 0, 7.8, 15.405, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param104", "Segments": [0, 0, 2, 0.333, 0, 0, 0.367, -16, 0, 0.433, 0, 0, 0.533, -16, 0, 0.633, 0, 2, 2.467, 0, 0, 2.533, -16, 0, 2.633, 0, 0, 2.733, -16, 0, 2.833, 0, 0, 3.533, -16, 0, 4.6, 0, 0, 4.667, -16, 0, 4.767, 0, 0, 4.867, -16, 0, 4.967, 0, 2, 6.733, 0, 0, 6.8, -16, 0, 6.9, 0, 0, 7, -16, 0, 7.1, 0, 0, 7.8, -16, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param103", "Segments": [0, 0, 2, 0.333, 0, 0, 0.367, 27, 0, 0.433, 0, 0, 0.533, 27, 0, 0.633, 0, 2, 2.467, 0, 0, 2.533, 27, 0, 2.633, 0, 0, 2.733, 27, 0, 2.833, 0, 0, 3.533, 27, 0, 4.6, 0, 0, 4.667, 27, 0, 4.767, 0, 0, 4.867, 27, 0, 4.967, 0, 2, 6.733, 0, 0, 6.8, 27, 0, 6.9, 0, 0, 7, 27, 0, 7.1, 0, 0, 7.8, 27, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param102", "Segments": [0, 0, 2, 0.333, 0, 0, 0.367, 15, 0, 0.433, 0, 0, 0.533, 15, 0, 0.633, 0, 2, 2.467, 0, 0, 2.533, 15, 0, 2.633, 0, 0, 2.733, 15, 0, 2.833, 0, 0, 3.533, 15, 0, 4.6, 0, 0, 4.667, 15, 0, 4.767, 0, 0, 4.867, 15, 0, 4.967, 0, 2, 6.733, 0, 0, 6.8, 15, 0, 6.9, 0, 0, 7, 15, 0, 7.1, 0, 0, 7.8, 15, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation71", "Segments": [0, 0, 2, 0.333, 0, 0, 0.8, 18, 0, 1.4, -18, 0, 1.933, 18, 0, 2.467, 0, 0, 3, 18, 0, 3.533, -18, 0, 4.067, 18, 0, 4.6, 0, 0, 5.133, 18, 0, 5.667, -18, 0, 6.2, 18, 0, 6.733, 0, 0, 7.267, 18, 0, 7.8, -18, 0, 8.333, 18, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation72", "Segments": [0, 8.085, 2, 0.333, 8.085, 0, 0.5, 0, 0, 1.1, 19, 0, 1.633, -19, 0, 2.167, 19, 0, 2.7, 0, 0, 3.233, 19, 0, 3.767, -19, 0, 4.3, 19, 1, 4.4, 19, 4.5, 15.205, 4.6, 8.085, 1, 4.678, 2.547, 4.755, 0, 4.833, 0, 0, 5.367, 19, 0, 5.9, -19, 0, 6.433, 19, 0, 6.967, 0, 0, 7.5, 19, 0, 8.033, -19, 0, 8.567, 19, 0, 8.8, 8.085]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation73", "Segments": [0, 11.915, 2, 0.333, 11.915, 0, 0.5, 0, 0, 1.1, 28, 0, 1.633, -28, 0, 2.167, 28, 0, 2.7, 0, 0, 3.233, 28, 0, 3.767, -28, 0, 4.3, 28, 1, 4.4, 28, 4.5, 22.408, 4.6, 11.915, 1, 4.678, 3.754, 4.755, 0, 4.833, 0, 0, 5.367, 28, 0, 5.9, -28, 0, 6.433, 28, 0, 6.967, 0, 0, 7.5, 28, 0, 8.033, -28, 0, 8.567, 28, 0, 8.8, 11.915]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation74", "Segments": [0, 12.341, 2, 0.333, 12.341, 0, 0.5, 0, 0, 1.1, 29, 0, 1.633, -29, 0, 2.167, 29, 0, 2.7, 0, 0, 3.233, 29, 0, 3.767, -29, 0, 4.3, 29, 1, 4.4, 29, 4.5, 23.209, 4.6, 12.341, 1, 4.678, 3.888, 4.755, 0, 4.833, 0, 0, 5.367, 29, 0, 5.9, -29, 0, 6.433, 29, 0, 6.967, 0, 0, 7.5, 29, 0, 8.033, -29, 0, 8.567, 29, 0, 8.8, 12.341]}, {"Target": "Parameter", "Id": "Param112", "Segments": [0, -30, 0, 0.333, 0, 2, 0.4, 0, 2, 0.8, 0, 2, 1.033, 0, 2, 1.3, 0, 2, 1.8, 0, 2, 3.633, 0, 2, 4.233, 0, 2, 4.8, 0, 2, 5.733, 0, 2, 6.567, 0, 2, 7.933, 0, 0, 8.8, -30]}, {"Target": "Parameter", "Id": "Param272", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param273", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "ParamCheek", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param268", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param269", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param193", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param192", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param196", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param282", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param183", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param194", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param195", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param184", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param289", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param185", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param290", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param291", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param186", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param187", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param188", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param292", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param189", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param190", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param191", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param24", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param26", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param200", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param286", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param287", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param288", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param202", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param205", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param203", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param204", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param21", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param213", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param216", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param217", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param218", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param220", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param197", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param198", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param199", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param208", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param279", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param212", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param270", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param271", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param230", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param231", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param222", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param207", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param236", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param237", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param239", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param241", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param242", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param243", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param244", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param18", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param275", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param224", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param225", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param227", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param226", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param232", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param285", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param274", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param276", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param142", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param143", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param144", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param147", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param145", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param134", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param246", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param141", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param182", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param247", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param229", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param245", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param117", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param140", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param262", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param263", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param115", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param116", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param260", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param261", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param133", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param139", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param257", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param137", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param138", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param135", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param176", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param136", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param177", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param251", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param91", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param2", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param87", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param88", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param94", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param218mao2", "Segments": [0, 1, 0, 8.8, 1]}, {"Target": "Parameter", "Id": "Param211mao2", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param217mao2", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param202mao2", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param203mao2", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param204mao2", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param199mao2", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param200mao2", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param201mao2", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param208mao2", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param209mao2", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param210mao2", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param205mao2", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param206mao2", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param207mao2", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param213mao2", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param219mao2", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param264", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param107", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param284", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param148", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param146", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param110", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param111", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param248", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param249", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param206", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param114", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param113", "Segments": [0, 1, 0, 8.8, 1]}, {"Target": "Parameter", "Id": "Param253", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param254", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param158", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param149", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param150", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param151", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param255", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param256", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param153", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param152", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param155", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param154", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param181", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param180", "Segments": [0, 0.5, 0, 8.8, 0.5]}, {"Target": "Parameter", "Id": "Param156", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param157", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param159", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param160", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param119", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param265", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param118", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param120", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param121", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param161", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param259", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param123", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param124", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param130", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param131", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param178", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param179", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param125", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param126", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param127", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param128", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param171", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param173", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param172", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param122", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param170", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param129", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param132", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param250", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param258", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param162", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param169", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param174", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param175", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param163", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param164", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param165", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param166", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param167", "Segments": [0, 0, 0, 8.8, 0]}, {"Target": "Parameter", "Id": "Param168", "Segments": [0, 0, 0, 8.8, 0]}], "UserData": [{"Time": 0.0, "Value": ""}, {"Time": 0.333, "Value": ""}, {"Time": 8.3, "Value": ""}]}