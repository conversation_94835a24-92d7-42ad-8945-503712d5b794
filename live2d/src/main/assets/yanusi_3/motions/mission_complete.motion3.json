{"Version": 3, "Meta": {"Duration": 17.233, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 355, "TotalSegmentCount": 5762, "TotalPointCount": 6586, "UserDataCount": 3, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 2, 0.333, 0, 0, 0.5, -2.46, 0, 0.767, 4.006, 0, 1, 2.792, 0, 2.333, 3.032, 0, 4.133, 2.792, 2, 4.4, 2.792, 2, 4.9, 2.792, 2, 6.567, 2.792, 0, 6.867, 1.525, 0, 7.267, 4.338, 0, 7.833, 2.778, 2, 10, 2.778, 2, 11.333, 2.778, 0, 11.567, 1.446, 0, 11.9, 4.18, 0, 12.767, 2.74, 2, 13, 2.74, 1, 13.2, 2.74, 13.4, 2.737, 13.6, 2.6, 1, 14, 2.325, 14.4, 2.06, 14.8, 2.06, 0, 15.367, 2.6, 2, 16.167, 2.6, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 2, 0.333, 0, 0, 0.5, -5.22, 0, 0.8, 5.162, 0, 1.067, -9.557, 1, 1.134, -9.557, 1.2, -5.653, 1.267, -4.212, 1, 1.4, -1.33, 1.534, -0.792, 1.667, -0.792, 0, 2.333, -4.212, 0, 2.9, 5.162, 0, 3.533, -1.998, 0, 4.4, 5.162, 0, 4.7, -9.557, 0, 4.9, -7.092, 0, 5.3, -9.557, 0, 6.133, -4.212, 1, 6.278, -4.212, 6.422, -4.914, 6.567, -7.092, 1, 6.711, -9.27, 6.856, -10.877, 7, -10.877, 0, 7.4, -0.661, 0, 7.967, -2.101, 0, 8.467, 0.168, 0, 9.067, -2.666, 0, 9.533, 1.085, 0, 10, -2.101, 2, 11.333, -2.101, 0, 11.6, -10.877, 0, 11.867, 13.381, 0, 12.233, -9.557, 1, 12.344, -9.557, 12.456, -5.711, 12.567, -4.186, 1, 12.689, -2.508, 12.811, -2.218, 12.933, -0.792, 1, 12.978, -0.274, 13.022, 2.978, 13.067, 2.978, 0, 13.367, -15.624, 0, 13.6, 2.978, 0, 13.8, -6.22, 0, 14, -1.226, 0, 14.167, -5.22, 0, 14.333, -1.752, 0, 14.8, -3.021, 0, 15.133, 17.052, 0, 15.367, -7.473, 1, 15.634, -7.473, 15.9, -7.643, 16.167, -6.196, 1, 16.522, -4.267, 16.878, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 2, 0.333, 0, 1, 0.433, 0, 0.533, 3.653, 0.633, 5.669, 1, 0.7, 7.013, 0.766, 6.905, 0.833, 6.905, 0, 1.067, 3.113, 1, 1.134, 3.113, 1.2, 3.705, 1.267, 4.195, 1, 1.4, 5.174, 1.534, 5.463, 1.667, 5.463, 0, 2.333, 3.701, 0, 2.7, 6.905, 0, 3.667, 4.62, 0, 3.967, 7.438, 0, 4.433, 3.113, 0, 4.9, 13.261, 1, 5.044, 13.261, 5.189, 12.457, 5.333, 12.42, 1, 5.744, 12.315, 6.156, 12.301, 6.567, 12.301, 0, 6.733, 13.532, 0, 7.4, 3.44, 0, 7.967, 4.08, 0, 9.167, 4.03, 1, 9.289, 4.03, 9.411, 4.524, 9.533, 5.669, 1, 9.566, 5.981, 9.6, 6.239, 9.633, 6.239, 0, 10.2, -1.654, 0, 10.567, -0.459, 0, 11.267, -0.965, 1, 11.367, -0.965, 11.467, -1.096, 11.567, 0.282, 1, 11.678, 1.814, 11.789, 6.905, 11.9, 6.905, 0, 12.2, 5.117, 0, 12.767, 5.389, 2, 13, 5.389, 1, 13.2, 5.389, 13.4, 5.431, 13.6, 5.184, 1, 14, 4.69, 14.4, 3.944, 14.8, 3.944, 1, 14.989, 3.944, 15.178, 5.171, 15.367, 5.184, 1, 15.634, 5.202, 15.9, 5.2, 16.167, 5.2, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 2, 0.333, 1, 0, 0.4, 0, 2, 0.5, 0, 0, 0.6, 1, 2, 1, 1, 2, 1.667, 1, 2, 2.333, 1, 2, 2.8, 1, 0, 2.9, 0, 0, 3.033, 1, 2, 4.133, 1, 2, 4.4, 1, 0, 4.533, 0, 2, 4.9, 0, 2, 6.567, 0, 2, 6.833, 0, 0, 6.933, 1, 2, 7.267, 1, 2, 7.833, 1, 2, 8.733, 1, 0, 8.8, 0, 0, 8.9, 1, 2, 10, 1, 2, 11.333, 1, 0, 11.567, 0, 0, 11.9, 1, 0, 12.467, 0, 2, 12.767, 0, 2, 13, 0, 2, 13.2, 0, 0, 13.3, 1, 2, 13.6, 1, 2, 14.967, 1, 0, 15.033, 0, 0, 15.133, 1, 2, 15.367, 1, 2, 16.167, 1, 2, 16.467, 1, 0, 16.533, 0, 0, 16.633, 1, 2, 17.233, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 2, 0.333, 0, 2, 0.5, 0, 2, 1, 0, 2, 1.667, 0, 2, 2.333, 0, 2, 4.133, 0, 1, 4.222, 0, 4.311, 0.024, 4.4, 0.275, 1, 4.444, 0.4, 4.489, 1, 4.533, 1, 2, 4.9, 1, 2, 6.567, 1, 2, 6.833, 1, 0, 6.933, 0, 2, 7.267, 0, 2, 7.833, 0, 2, 10, 0, 2, 11.333, 0, 2, 11.9, 0, 0, 12.467, 1, 2, 12.767, 1, 2, 13, 1, 2, 13.2, 1, 0, 13.3, 0, 2, 13.6, 0, 2, 15.367, 0, 2, 16.167, 0, 2, 16.467, 0, 2, 16.633, 0, 2, 17.233, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 2, 0.333, 1, 0, 0.4, 0, 2, 0.5, 0, 0, 0.6, 1, 2, 1, 1, 2, 1.667, 1, 2, 2.333, 1, 2, 2.8, 1, 0, 2.9, 0, 0, 3.033, 1, 2, 4.133, 1, 2, 4.4, 1, 0, 4.533, 0, 2, 4.9, 0, 2, 6.567, 0, 2, 6.833, 0, 0, 6.933, 1, 2, 7.267, 1, 2, 7.833, 1, 2, 8.733, 1, 0, 8.8, 0, 0, 8.9, 1, 2, 10, 1, 2, 11.333, 1, 0, 11.567, 0, 0, 11.9, 1, 0, 12.467, 0, 2, 12.767, 0, 2, 13, 0, 2, 13.2, 0, 0, 13.3, 1, 2, 13.6, 1, 2, 14.967, 1, 0, 15.033, 0, 0, 15.133, 1, 2, 15.367, 1, 2, 16.167, 1, 2, 16.467, 1, 0, 16.533, 0, 0, 16.633, 1, 2, 17.233, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 2, 0.333, 0, 2, 0.5, 0, 2, 1, 0, 2, 1.667, 0, 2, 2.333, 0, 2, 4.133, 0, 1, 4.222, 0, 4.311, 0.024, 4.4, 0.275, 1, 4.444, 0.4, 4.489, 1, 4.533, 1, 2, 4.9, 1, 2, 6.567, 1, 2, 6.833, 1, 0, 6.933, 0, 2, 7.267, 0, 2, 7.833, 0, 2, 10, 0, 2, 11.333, 0, 2, 11.9, 0, 0, 12.467, 1, 2, 12.767, 1, 2, 13, 1, 2, 13.2, 1, 0, 13.3, 0, 2, 13.6, 0, 2, 15.367, 0, 2, 16.167, 0, 2, 17.233, 0]}, {"Target": "Parameter", "Id": "Param76", "Segments": [0, 0, 2, 0.333, 0, 2, 0.5, 0, 0, 0.6, -30, 2, 1, -30, 2, 1.667, -30, 2, 2.333, -30, 2, 4.133, -30, 2, 4.4, -30, 2, 4.9, -30, 2, 6.567, -30, 2, 7.267, -30, 2, 7.833, -30, 2, 10, -30, 2, 11.333, -30, 2, 11.9, -30, 2, 12.767, -30, 2, 13, -30, 2, 13.6, -30, 2, 15.367, -30, 0, 16.167, 0, 2, 17.233, 0]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 2, 0.333, 0, 0, 0.5, -0.362, 0, 0.533, 0.178, 2, 1, 0.178, 2, 1.667, 0.178, 2, 2.333, 0.178, 2, 4.133, 0.178, 2, 4.4, 0.178, 2, 4.9, 0.178, 2, 6.567, 0.178, 0, 7.267, 0.177, 2, 7.833, 0.177, 2, 10, 0.177, 2, 11.333, 0.177, 0, 11.9, 0.175, 2, 12.767, 0.175, 2, 13, 0.175, 0, 13.6, 0.166, 2, 15.367, 0.166, 2, 16.167, 0.166, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 2, 0.333, 0, 0, 0.5, -0.406, 0, 0.533, 0.184, 2, 1, 0.184, 2, 1.667, 0.184, 2, 2.333, 0.184, 2, 4.133, 0.184, 2, 4.4, 0.184, 2, 4.9, 0.184, 2, 6.567, 0.184, 0, 7.267, 0.183, 2, 7.833, 0.183, 2, 10, 0.183, 2, 11.333, 0.183, 0, 11.9, 0.181, 2, 12.767, 0.181, 2, 13, 0.181, 0, 13.6, 0.171, 2, 15.367, 0.171, 2, 16.167, 0.171, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, -1, 2, 0.333, -1, 2, 0.5, -1, 0, 0.533, 0, 2, 1, 0, 2, 1.667, 0, 2, 2.333, 0, 2, 4.133, 0, 2, 4.4, 0, 2, 4.9, 0, 2, 6.567, 0, 0, 7.267, -0.005, 2, 7.833, -0.005, 2, 10, -0.005, 2, 11.333, -0.005, 0, 11.9, -0.018, 2, 12.767, -0.018, 2, 13, -0.018, 0, 13.6, -0.069, 2, 15.367, -0.069, 2, 16.167, -0.069, 0, 17.233, -1]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, -1, 2, 0.333, -1, 2, 0.5, -1, 0, 0.533, 0, 2, 1, 0, 2, 1.667, 0, 2, 2.333, 0, 2, 4.133, 0, 2, 4.4, 0, 2, 4.9, 0, 2, 6.567, 0, 0, 7.267, -0.005, 2, 7.833, -0.005, 2, 10, -0.005, 2, 11.333, -0.005, 0, 11.9, -0.018, 2, 12.767, -0.018, 2, 13, -0.018, 0, 13.6, -0.069, 2, 15.367, -0.069, 2, 16.167, -0.069, 0, 17.233, -1]}, {"Target": "Parameter", "Id": "Param", "Segments": [0, -30, 0, 0.333, 0, 2, 0.5, 0, 2, 1, 0, 2, 1.667, 0, 2, 2.333, 0, 2, 4.133, 0, 2, 4.4, 0, 2, 4.9, 0, 2, 6.567, 0, 2, 7.267, 0, 2, 7.833, 0, 2, 10, 0, 2, 11.333, 0, 2, 11.9, 0, 2, 12.767, 0, 2, 13, 0, 2, 13.6, 0, 2, 15.367, 0, 2, 16.167, 0, 0, 17.233, -30]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 1, 0, 0.333, 0, 2, 0.5, 0, 0, 1, 0.4, 2, 1.667, 0.4, 2, 2.333, 0.4, 2, 3.933, 0.4, 2, 4.9, 0.4, 2, 6.567, 0.4, 1, 6.667, 0.4, 6.767, 0.429, 6.867, 0.2, 1, 7, -0.106, 7.134, -0.8, 7.267, -0.8, 2, 7.833, -0.8, 2, 10, -0.8, 2, 11.333, -0.8, 0, 11.9, 0.393, 2, 12.767, 0.393, 2, 13, 0.393, 0, 13.6, 0.372, 2, 15.367, 0.372, 2, 16.167, 0.372, 0, 17.233, 1]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 2, 0.333, 0, 0, 0.533, 0.577, 0, 0.733, 0.463, 0, 0.8, 0.71, 0, 0.933, 0.11, 0, 1.067, 0.718, 0, 1.133, 0.008, 0, 1.2, 0.318, 0, 1.267, 0.071, 0, 1.4, 0.812, 0, 1.733, 0, 2, 2.333, 0, 0, 2.6, 0.667, 0, 2.8, 0.235, 0, 2.867, 0.557, 0, 3, 0.275, 0, 3.2, 0.639, 0, 3.333, 0.157, 0, 3.467, 0.577, 0, 3.533, 0.435, 0, 3.667, 0.714, 0, 3.733, 0.243, 0, 3.8, 0.6, 0, 3.867, 0.224, 0, 3.933, 0.647, 0, 4, 0.004, 0, 4.067, 0.777, 0, 4.2, 0.333, 0, 4.267, 0.628, 0, 4.4, 0.185, 0, 4.667, 0.533, 0, 4.933, 0, 2, 6.8, 0, 0, 6.933, 0.431, 0, 7.067, 0, 2, 8, 0, 0, 8.267, 0.663, 0, 8.333, 0, 0, 8.4, 0.871, 0, 8.533, 0.529, 0, 8.6, 0.533, 0, 8.667, 0.337, 0, 8.8, 0.573, 0, 9.067, 0, 0, 9.2, 0.282, 0, 9.267, 0, 2, 9.333, 0, 0, 9.467, 0.769, 0, 9.667, 0, 0, 9.8, 0.333, 0, 10, 0, 2, 11.533, 0, 0, 11.667, 0.518, 0, 11.8, 0, 2, 11.867, 0, 0, 12, 0.416, 0, 12.067, 0.282, 0, 12.267, 0.82, 0, 12.333, 0.718, 0, 12.4, 0.753, 0, 12.533, 0.329, 0, 12.6, 0.635, 0, 13.133, 0, 2, 13.333, 0, 0, 13.533, 0.8, 0, 13.6, 0.145, 0, 13.8, 0.682, 0, 13.867, 0.318, 0, 13.933, 0.541, 0, 14, 0.416, 0, 14.067, 0.588, 0, 14.133, 0.365, 0, 14.2, 0.729, 0, 14.4, 0, 2, 14.533, 0, 0, 14.667, 0.592, 0, 14.733, 0.028, 0, 14.8, 0.502, 0, 14.867, 0.071, 0, 14.933, 0.745, 0, 15, 0.674, 0, 15.067, 0.726, 0, 15.133, 0.333, 0, 15.2, 0.741, 0, 15.467, 0, 2, 16.633, 0, 0, 16.7, 0.024, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 2, 0.333, 0, 0, 0.5, 3.42, 0, 0.767, -14.065, 0, 1, -8.185, 0, 2.333, -11.706, 0, 4.133, -5.838, 0, 4.4, -13.382, 1, 4.567, -13.382, 4.733, -10.279, 4.9, -9.862, 1, 5.456, -8.471, 6.011, -8.185, 6.567, -8.185, 0, 6.9, -11.706, 0, 7.3, -5.624, 0, 7.833, -8.144, 0, 8.467, -6.104, 0, 9.067, -8.536, 0, 10, 5.155, 1, 10.122, 5.155, 10.245, 3.982, 10.367, 3.825, 1, 10.689, 3.412, 11.011, 3.402, 11.333, 2.881, 1, 11.555, 2.521, 11.778, -15.395, 12, -15.395, 1, 12.067, -15.395, 12.133, -8.672, 12.2, -8.52, 1, 12.389, -8.088, 12.578, -8.033, 12.767, -8.033, 2, 13, -8.033, 0, 13.267, -5.011, 0, 13.6, -9.663, 0, 15.133, -7.023, 0, 15.367, -11.163, 1, 15.611, -11.163, 15.856, -11.391, 16.1, -9.862, 1, 16.344, -8.333, 16.589, 1.717, 16.833, 1.717, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 2, 0.333, 0, 0, 0.5, -2.853, 0, 0.767, 8.909, 0, 1, 2.153, 0, 1.667, 4.058, 0, 2.333, 0.098, 0, 3.067, 6.439, 0, 3.967, 0.344, 0, 4.233, 3.632, 0, 4.733, -9.151, 0, 4.967, -6.656, 2, 5, -6.656, 0, 5.233, -9.631, 1, 5.389, -9.631, 5.544, -9.916, 5.7, -7.422, 1, 5.978, -2.968, 6.255, 4.058, 6.533, 4.058, 0, 6.867, -0.635, 0, 7.267, 6.62, 0, 7.833, 1.82, 0, 8.467, 3.099, 0, 9.067, 0.795, 0, 9.533, 3.099, 0, 10, -1.24, 0, 11.333, 1.82, 0, 11.567, -0.108, 0, 11.9, 4.915, 0, 12.333, -14.442, 1, 12.478, -14.442, 12.622, -0.931, 12.767, 1.795, 1, 12.845, 3.263, 12.922, 2.815, 13, 2.815, 0, 13.267, -13.845, 0, 13.6, 1.703, 0, 13.767, -2.817, 0, 13.9, -0.313, 0, 14.067, -2.853, 1, 14.311, -2.853, 14.556, -2.38, 14.8, 0.383, 1, 14.911, 1.639, 15.022, 5.949, 15.133, 5.949, 0, 15.367, -9.517, 0, 15.7, -5.577, 0, 16.167, -7.39, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 2, 0.333, 0, 0, 0.5, -0.233, 0, 0.767, 1.834, 0, 1, 1.461, 0, 1.6, 2.432, 1, 1.844, 2.432, 2.089, 2.074, 2.333, 1.821, 1, 2.844, 1.293, 3.356, 0.977, 3.867, 0.48, 1, 3.934, 0.415, 4, -0.233, 4.067, -0.233, 0, 4.633, 2.961, 0, 4.833, 2.063, 1, 5.111, 2.063, 5.389, 2.13, 5.667, 2.283, 1, 5.967, 2.448, 6.267, 2.541, 6.567, 2.541, 1, 6.667, 2.541, 6.767, 2.467, 6.867, 2.058, 1, 7, 1.513, 7.134, 1.034, 7.267, 1.034, 0, 7.833, 1.874, 1, 8.4, 1.874, 8.966, 1.801, 9.533, 1.454, 1, 9.689, 1.358, 9.844, 0.314, 10, 0.314, 0, 11.333, 1.454, 0, 11.567, 0.968, 0, 11.9, 2.334, 1, 12, 2.334, 12.1, 1.913, 12.2, 1.851, 1, 12.389, 1.734, 12.578, 1.728, 12.767, 1.728, 2, 13, 1.728, 0, 13.267, 4.34, 0, 13.6, 1.775, 0, 13.833, 2.28, 1, 14.155, 2.283, 14.478, 2.283, 14.8, 2.283, 0, 15.033, 0.639, 1, 15.144, 0.639, 15.256, 2.456, 15.367, 2.735, 1, 15.634, 3.403, 15.9, 3.489, 16.167, 3.489, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param267", "Segments": [0, 0, 2, 0.333, 0, 0, 0.5, -0.3, 0, 0.767, 5.032, 0, 1, 4.02, 0, 1.533, 4.971, 0, 2.333, 3.84, 0, 3.2, 6.752, 1, 3.422, 6.752, 3.645, 6.336, 3.867, 5.4, 1, 3.956, 5.026, 4.044, 4.778, 4.133, 4.778, 1, 4.3, 4.778, 4.466, 4.875, 4.633, 5.22, 1, 4.689, 5.335, 4.744, 5.9, 4.8, 5.922, 1, 5.389, 6.152, 5.978, 6.24, 6.567, 6.24, 1, 6.667, 6.24, 6.767, 5.95, 6.867, 5.21, 1, 7, 4.223, 7.134, 3.638, 7.267, 3.638, 0, 7.833, 4.778, 1, 8.4, 4.778, 8.966, 4.681, 9.533, 4.358, 1, 9.689, 4.27, 9.844, 4.058, 10, 4.058, 0, 11.333, 4.358, 0, 11.567, 3.8, 0, 11.9, 7.119, 0, 12.2, 6.196, 0, 12.767, 6.295, 2, 13, 6.295, 0, 13.6, 6.489, 2, 14.8, 6.489, 0, 15.133, 5.649, 0, 15.367, 7.269, 1, 15.634, 7.269, 15.9, 7.483, 16.167, 6.489, 1, 16.522, 5.164, 16.878, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param3", "Segments": [0, 0, 2, 0.333, 0, 1, 0.389, 0, 0.444, 0.138, 0.5, -1.26, 1, 0.667, -5.454, 0.833, -9.557, 1, -9.557, 0, 1.667, -6.737, 0, 2.333, -8.477, 0, 3.067, -6.447, 0, 4.133, -12.797, 0, 4.6, -2.212, 1, 4.7, -2.212, 4.8, -3.083, 4.9, -3.317, 1, 5.456, -4.619, 6.011, -5.057, 6.567, -5.057, 0, 6.767, -3.931, 0, 7.267, -9.509, 2, 7.833, -9.509, 0, 8.467, -8.478, 0, 9.067, -9.892, 0, 9.533, -8.456, 0, 10, -13.529, 0, 11.333, -12.875, 0, 11.567, -14.381, 0, 11.9, -6.445, 1, 12, -6.445, 12.1, -8.567, 12.2, -8.84, 1, 12.389, -9.356, 12.578, -9.38, 12.767, -9.38, 2, 13, -9.38, 1, 13.2, -9.38, 13.4, -9.087, 13.6, -8.901, 1, 14, -8.53, 14.4, -8.368, 14.8, -7.98, 1, 14.911, -7.872, 15.022, -4.461, 15.133, -4.461, 0, 15.367, -9.557, 1, 15.634, -9.557, 15.9, -9.816, 16.167, -8.777, 1, 16.522, -7.391, 16.878, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param4", "Segments": [0, 0, 2, 0.333, 0, 0, 0.567, 4.406, 0, 1.033, -5.067, 1, 1.122, -5.067, 1.211, -1.977, 1.3, -0.76, 1, 1.422, 0.913, 1.545, 1.047, 1.667, 1.047, 0, 2.333, -1.589, 0, 3.067, 1.047, 0, 3.967, -0.76, 0, 4.233, 2.754, 0, 4.733, -1.824, 0, 5.667, 1.668, 1, 5.967, 1.668, 6.267, 0.726, 6.567, -1.524, 1, 6.634, -2.024, 6.7, -2.536, 6.767, -2.536, 0, 7.267, 1.777, 0, 7.833, -0.203, 0, 8.467, 0, 0, 9.067, -1.524, 0, 9.533, -1.104, 0, 10, -5.826, 1, 10.1, -5.826, 10.2, -5.388, 10.3, -5.067, 1, 10.644, -3.962, 10.989, -3.569, 11.333, -3.569, 0, 11.567, -5.067, 0, 11.9, 0.274, 0, 12.333, -9.793, 1, 12.455, -9.793, 12.578, -7.523, 12.7, -6.768, 1, 12.8, -6.15, 12.9, -6.236, 13, -6.236, 0, 13.233, -8.451, 0, 13.6, -2.578, 0, 14.133, -4.498, 0, 14.467, -3.698, 0, 14.8, -4.738, 0, 15.133, -2.476, 0, 15.367, -5.826, 1, 15.478, -5.826, 15.589, -3.702, 15.7, -3.396, 1, 15.856, -2.967, 16.011, -3.067, 16.167, -2.578, 1, 16.522, -1.459, 16.878, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param266", "Segments": [0, 0, 2, 0.333, 0, 1, 0.389, 0, 0.444, -1.556, 0.5, -2.34, 1, 0.667, -4.693, 0.833, -5.391, 1, -5.391, 0, 1.667, -2.34, 0, 2.333, -4.02, 0, 3.067, -1.243, 0, 4.133, -3.66, 0, 4.6, 4.212, 0, 4.9, 1.98, 0, 5.667, 3.18, 0, 6.567, 0, 0, 6.767, 1.2, 0, 7.267, -3.6, 1, 7.456, -3.6, 7.644, -3.533, 7.833, -2.64, 1, 8.044, -1.642, 8.256, -0.315, 8.467, -0.315, 0, 9.067, -2.005, 0, 9.533, 0.677, 0, 10, -2.88, 0, 11.333, -2.34, 0, 11.567, -3.786, 0, 11.9, 0.96, 1, 12, 0.96, 12.1, -0.94, 12.2, -1.286, 1, 12.389, -1.939, 12.578, -1.985, 12.767, -1.985, 2, 13, -1.985, 2, 13.6, -1.985, 1, 14, -1.985, 14.4, -1.9, 14.8, -1.442, 1, 14.911, -1.315, 15.022, 3.437, 15.133, 3.437, 1, 15.211, 3.437, 15.289, -2.389, 15.367, -2.64, 1, 15.634, -3.499, 15.9, -3.66, 16.167, -3.66, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param5", "Segments": [0, 0, 2, 0.333, 0, 0, 0.5, 3.9, 0, 1, -12.309, 0, 1.667, -11.755, 2, 2.333, -11.755, 2, 4.133, -11.755, 1, 4.222, -11.755, 4.311, -4.797, 4.4, -3.769, 1, 4.567, -1.842, 4.733, 0.107, 4.9, 0.404, 1, 5.456, 1.392, 6.011, 1.604, 6.567, 1.604, 0, 7.267, -12.832, 1, 7.456, -12.832, 7.644, -11.757, 7.833, -11.632, 1, 8.411, -11.25, 8.989, -11.055, 9.567, -10.732, 1, 9.611, -10.707, 9.656, -8.177, 9.7, -8.177, 0, 10, -16.96, 1, 10.156, -16.96, 10.311, -16.159, 10.467, -16, 1, 10.767, -15.694, 11.067, -15.666, 11.367, -15.34, 1, 11.545, -15.147, 11.722, -13.786, 11.9, -13.786, 0, 12.4, -28.073, 1, 12.644, -28.073, 12.889, -27.272, 13.133, -27.199, 1, 14.144, -26.898, 15.156, -26.788, 16.167, -26.453, 1, 16.522, -26.335, 16.878, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param6", "Segments": [0, 0, 2, 0.333, 0, 1, 0.389, 0, 0.444, 7.788, 0.5, 8.345, 1, 0.667, 10.013, 0.833, 12.044, 1, 12.3, 1, 1.222, 12.641, 1.445, 12.619, 1.667, 12.619, 2, 2.333, 12.619, 2, 4.133, 12.619, 0, 4.4, 14.52, 1, 4.567, 14.52, 4.733, 13.764, 4.9, 13.68, 1, 5.456, 13.401, 6.011, 13.326, 6.567, 13.012, 1, 6.8, 12.88, 7.034, 0, 7.267, 0, 2, 7.833, 0, 2, 9.567, 0, 0, 10, 3.912, 2, 11.333, 3.912, 0, 11.9, 0, 0, 12.4, 16, 2, 12.733, 16, 2, 15.367, 16, 2, 16.167, 16, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 2, 0.333, 0, 2, 0.5, 0, 2, 1, 0, 2, 1.667, 0, 2, 2.333, 0, 2, 4.133, 0, 2, 4.4, 0, 2, 4.9, 0, 2, 5.233, 0, 0, 5.7, 0.217, 0, 6.567, 0, 2, 7.267, 0, 2, 7.833, 0, 2, 10, 0, 2, 11.333, 0, 2, 11.9, 0, 2, 12.767, 0, 2, 13, 0, 2, 13.6, 0, 2, 15.367, 0, 2, 16.167, 0, 2, 17.233, 0]}, {"Target": "Parameter", "Id": "Param8", "Segments": [0, 0, 2, 0.333, 0, 0, 0.5, 0.4, 0, 1, -0.294, 2, 1.667, -0.294, 2, 2.333, -0.294, 2, 4.133, -0.294, 2, 4.4, -0.294, 2, 4.9, -0.294, 2, 6.567, -0.294, 0, 7.267, -0.292, 2, 7.833, -0.292, 2, 10, -0.292, 2, 11.333, -0.292, 0, 11.9, -0.288, 2, 12.767, -0.288, 2, 13, -0.288, 0, 13.6, -0.274, 2, 15.367, -0.274, 2, 16.167, -0.274, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param23", "Segments": [0, 0, 2, 0.333, 0, 0, 0.5, 0.694, 0, 1, -1.069, 2, 1.667, -1.069, 2, 2.333, -1.069, 2, 4.133, -1.069, 2, 4.4, -1.069, 2, 4.9, -1.069, 2, 6.567, -1.069, 0, 7.267, -1.064, 2, 7.833, -1.064, 2, 10, -1.064, 2, 11.333, -1.064, 0, 11.9, -1.049, 2, 12.767, -1.049, 2, 13, -1.049, 0, 13.6, -0.996, 2, 15.367, -0.996, 2, 16.167, -0.996, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param25", "Segments": [0, 0, 2, 0.333, 0, 0, 0.5, 0.469, 0, 1, -0.603, 2, 1.667, -0.603, 2, 2.333, -0.603, 2, 4.133, -0.603, 2, 4.4, -0.603, 2, 4.9, -0.603, 2, 6.567, -0.603, 0, 7.267, -0.6, 2, 7.833, -0.6, 2, 10, -0.6, 2, 11.333, -0.6, 0, 11.9, -0.591, 2, 12.767, -0.591, 2, 13, -0.591, 0, 13.6, -0.561, 2, 15.367, -0.561, 2, 16.167, -0.561, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param27", "Segments": [0, 0, 2, 0.333, 0, 0, 0.5, 1.279, 0, 1, -2.257, 2, 1.667, -2.257, 2, 2.333, -2.257, 2, 4.133, -2.257, 2, 4.4, -2.257, 2, 4.9, -2.257, 2, 6.567, -2.257, 0, 7.267, -2.246, 2, 7.833, -2.246, 2, 10, -2.246, 2, 11.333, -2.246, 0, 11.9, -2.215, 2, 12.767, -2.215, 2, 13, -2.215, 0, 13.6, -2.102, 2, 15.367, -2.102, 2, 16.167, -2.102, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param201", "Segments": [0, 0, 2, 0.333, 0, 2, 0.5, 0, 2, 0.7, 0, 0, 0.733, 0.999, 2, 1, 0.999, 2, 1.667, 0.999, 2, 2.333, 0.999, 2, 4.133, 0.999, 2, 4.4, 0.999, 2, 4.9, 0.999, 2, 6.567, 0.999, 0, 7.267, 0.994, 2, 7.833, 0.994, 2, 10, 0.994, 2, 11.333, 0.994, 0, 11.9, 0.98, 2, 12.167, 0.98, 2, 12.2, 0.98, 0, 12.767, 1, 2, 13, 1, 2, 13.367, 1, 0, 13.4, 0, 2, 15.367, 0, 2, 16.3, 0, 2, 16.333, 0, 2, 17.233, 0]}, {"Target": "Parameter", "Id": "Param202", "Segments": [0, 0, 2, 0.333, 0, 2, 0.5, 0, 2, 0.7, 0, 2, 0.733, 0, 2, 1, 0, 2, 1.667, 0, 2, 2.333, 0, 2, 4.133, 0, 2, 4.4, 0, 2, 4.9, 0, 2, 6.567, 0, 2, 7.267, 0, 2, 7.833, 0, 2, 10, 0, 2, 11.333, 0, 2, 11.9, 0, 2, 12.167, 0, 2, 12.2, 0, 2, 12.767, 0, 2, 13, 0, 2, 13.367, 0, 0, 13.4, 1, 2, 15.367, 1, 2, 16.3, 1, 0, 16.333, 0, 2, 17.233, 0]}, {"Target": "Parameter", "Id": "Param203", "Segments": [0, 0, 2, 0.333, 0, 2, 0.5, 0, 2, 0.7, 0, 2, 0.733, 0, 2, 1, 0, 2, 1.667, 0, 2, 2.333, 0, 2, 4.133, 0, 2, 4.4, 0, 2, 4.9, 0, 2, 6.567, 0, 2, 7.267, 0, 2, 7.833, 0, 2, 10, 0, 2, 11.333, 0, 2, 11.9, 0, 2, 12.167, 0, 2, 12.2, 0, 2, 12.767, 0, 2, 13, 0, 2, 13.367, 0, 0, 13.4, 1, 2, 15.367, 1, 2, 16.3, 1, 0, 16.333, 0, 2, 17.233, 0]}, {"Target": "Parameter", "Id": "Param209", "Segments": [0, 0, 2, 0.333, 0, 2, 0.5, 0, 2, 0.7, 0, 0, 0.733, 0.999, 2, 1, 0.999, 2, 1.667, 0.999, 2, 2.333, 0.999, 2, 4.133, 0.999, 2, 4.4, 0.999, 2, 4.9, 0.999, 2, 6.567, 0.999, 0, 7.267, 0.994, 2, 7.833, 0.994, 2, 10, 0.994, 2, 11.333, 0.994, 0, 11.9, 0.98, 2, 12.167, 0.98, 2, 12.2, 0.98, 0, 12.767, 1, 2, 13, 1, 2, 13.367, 1, 0, 13.4, 0, 2, 15.367, 0, 2, 16.3, 0, 2, 16.333, 0, 2, 17.233, 0]}, {"Target": "Parameter", "Id": "Param215", "Segments": [0, 0, 2, 0.333, 0, 2, 0.5, 0, 2, 0.7, 0, 2, 0.733, 0, 2, 1, 0, 2, 1.667, 0, 2, 2.333, 0, 2, 4.133, 0, 2, 4.4, 0, 2, 4.9, 0, 2, 6.567, 0, 2, 7.267, 0, 2, 7.833, 0, 2, 10, 0, 2, 11.333, 0, 2, 11.9, 0, 2, 12.167, 0, 0, 12.2, 1, 2, 12.767, 1, 2, 13, 1, 2, 13.367, 1, 0, 13.4, 0, 2, 15.367, 0, 2, 16.3, 0, 2, 16.333, 0, 2, 17.233, 0]}, {"Target": "Parameter", "Id": "Param216", "Segments": [0, 0, 2, 0.333, 0, 2, 0.5, 0, 2, 0.7, 0, 0, 0.733, 1, 2, 1, 1, 2, 1.667, 1, 2, 2.333, 1, 2, 4.133, 1, 2, 4.4, 1, 2, 4.9, 1, 2, 6.567, 1, 0, 7.267, 0.995, 2, 7.833, 0.995, 2, 10, 0.995, 2, 11.333, 0.995, 0, 11.9, 0.982, 2, 12.167, 0.982, 0, 12.2, 0, 2, 12.767, 0, 2, 13, 0, 2, 13.367, 0, 2, 13.4, 0, 2, 15.367, 0, 2, 16.3, 0, 2, 16.333, 0, 2, 17.233, 0]}, {"Target": "Parameter", "Id": "Param9", "Segments": [0, 0, 2, 0.333, 0, 0, 0.5, 0.48, 0, 0.767, -5.794, 0, 1, -4.182, 0, 1.167, -4.602, 0, 1.667, -3.642, 2, 2.333, -3.642, 1, 2.522, -3.642, 2.711, -4.411, 2.9, -4.602, 1, 3.233, -4.937, 3.567, -4.962, 3.9, -4.962, 1, 3.989, -4.962, 4.078, -4.99, 4.167, -4.863, 1, 4.267, -4.72, 4.367, -3.642, 4.467, -3.642, 2, 4.667, -3.642, 2, 6.567, -3.642, 0, 7.267, -5.419, 0, 7.833, -5.126, 0, 8.567, -5.607, 0, 9.3, -5.277, 0, 10, -5.419, 2, 11.333, -5.419, 1, 11.411, -5.419, 11.489, -5.242, 11.567, -4.732, 1, 11.678, -4.004, 11.789, -3.545, 11.9, -3.545, 0, 12.167, -4.962, 0, 12.433, -2.338, 2, 13, -2.338, 1, 13.033, -2.338, 13.067, -3.599, 13.1, -4.48, 1, 13.144, -5.655, 13.189, -5.876, 13.233, -5.876, 0, 13.6, -1.892, 1, 13.7, -1.892, 13.8, -2.021, 13.9, -2.072, 1, 14.222, -2.237, 14.545, -2.322, 14.867, -2.492, 1, 14.956, -2.539, 15.044, -3.035, 15.133, -3.161, 1, 15.211, -3.272, 15.289, -3.272, 15.367, -3.272, 2, 16.167, -3.272, 1, 16.256, -3.272, 16.344, -3.334, 16.433, -2.853, 1, 16.7, -1.411, 16.966, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param10", "Segments": [0, 0, 2, 0.333, 0, 0, 0.5, -0.48, 0, 1, 24.055, 2, 1.667, 24.055, 2, 2.333, 24.055, 2, 2.9, 24.055, 2, 3.9, 24.055, 2, 4.167, 24.055, 2, 4.467, 24.055, 2, 4.667, 24.055, 2, 6.567, 24.055, 0, 7.267, 23.934, 2, 7.833, 23.934, 2, 10, 23.934, 2, 11.333, 23.934, 0, 11.9, 23.608, 2, 12.767, 23.608, 2, 13, 23.608, 1, 13.044, 23.608, 13.089, 19.074, 13.133, 15.816, 1, 13.166, 13.372, 13.2, 12.054, 13.233, 10.724, 1, 13.3, 8.065, 13.366, 4.668, 13.433, 3.489, 1, 13.489, 2.507, 13.544, 2.543, 13.6, 2.543, 1, 14.111, 2.543, 14.622, 2.779, 15.133, 3.323, 1, 15.211, 3.406, 15.289, 3.503, 15.367, 3.503, 2, 16.167, 3.503, 1, 16.256, 3.503, 16.344, 3.562, 16.433, 3.72, 1, 16.444, 3.74, 16.456, 3.825, 16.467, 3.825, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param11", "Segments": [0, 0, 2, 0.333, 0, 1, 0.389, 0, 0.444, -0.61, 0.5, 0.96, 1, 0.667, 5.669, 0.833, 18.59, 1, 18.59, 2, 1.667, 18.59, 2, 2.333, 18.59, 2, 2.9, 18.59, 2, 3.9, 18.59, 2, 4.167, 18.59, 2, 4.467, 18.59, 2, 4.667, 18.59, 2, 6.567, 18.59, 0, 7.267, 18.497, 2, 7.833, 18.497, 2, 10, 18.497, 2, 11.333, 18.497, 0, 11.9, 18.246, 2, 12.767, 18.246, 2, 13, 18.246, 0, 13.6, 17.314, 2, 15.133, 17.314, 2, 15.367, 17.314, 2, 16.167, 17.314, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param12", "Segments": [0, 0, 2, 0.333, 0, 1, 0.389, 0, 0.444, -13.618, 0.5, -17, 1, 0.667, -27.146, 0.833, -29.697, 1, -29.697, 2, 1.667, -29.697, 2, 2.333, -29.697, 2, 2.9, -29.697, 2, 3.9, -29.697, 2, 4.167, -29.697, 2, 4.467, -29.697, 2, 4.667, -29.697, 2, 6.567, -29.697, 0, 7.267, -29.549, 2, 7.833, -29.549, 2, 10, -29.549, 2, 11.333, -29.549, 0, 11.9, -29.146, 2, 12.767, -29.146, 2, 13, -29.146, 0, 13.6, -27.658, 2, 15.133, -27.658, 2, 15.367, -27.658, 2, 16.167, -27.658, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param22", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, 1, 2, 0.5, 1, 2, 1, 1, 2, 1.667, 1, 2, 2.333, 1, 2, 2.9, 1, 2, 3.9, 1, 2, 4.167, 1, 2, 4.467, 1, 2, 4.667, 1, 2, 6.567, 1, 2, 7.267, 1, 2, 7.833, 1, 2, 10, 1, 2, 11.333, 1, 2, 11.9, 1, 2, 12.767, 1, 2, 13, 1, 2, 13.6, 1, 2, 15.133, 1, 2, 15.367, 1, 2, 17.167, 1, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param13", "Segments": [0, 0, 2, 0.333, 0, 2, 0.5, 0, 0, 1, 10.453, 2, 1.667, 10.453, 2, 2.333, 10.453, 2, 2.9, 10.453, 2, 3.9, 10.453, 2, 4.167, 10.453, 2, 4.467, 10.453, 2, 4.667, 10.453, 2, 6.567, 10.453, 0, 7.267, 10.401, 2, 7.833, 10.401, 2, 10, 10.401, 2, 11.333, 10.401, 0, 11.9, 10.259, 2, 12.767, 10.259, 2, 13, 10.259, 0, 13.6, 9.736, 2, 15.133, 9.736, 2, 15.367, 9.736, 2, 16.167, 9.736, 0, 16.433, 0, 2, 17.233, 0]}, {"Target": "Parameter", "Id": "Param14", "Segments": [0, 0, 2, 0.333, 0, 2, 0.5, 0, 0, 1, -16.868, 2, 1.667, -16.868, 2, 2.333, -16.868, 2, 2.9, -16.868, 2, 3.9, -16.868, 2, 4.167, -16.868, 2, 4.467, -16.868, 2, 4.667, -16.868, 2, 6.567, -16.868, 0, 7.267, -16.784, 2, 7.833, -16.784, 2, 10, -16.784, 2, 11.333, -16.784, 0, 11.9, -16.555, 2, 12.767, -16.555, 2, 13, -16.555, 0, 13.6, -15.71, 2, 15.133, -15.71, 2, 15.367, -15.71, 2, 16.167, -15.71, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param210", "Segments": [0, 0, 2, 0.333, 0, 0, 0.5, 30, 0, 0.933, -0.419, 0, 1.167, 0.241, 2, 1.667, 0.241, 2, 2.333, 0.241, 2, 2.9, 0.241, 0, 3.9, 0.901, 1, 3.989, 0.901, 4.078, 0.893, 4.167, 0.72, 1, 4.267, 0.525, 4.367, 0.241, 4.467, 0.241, 2, 4.667, 0.241, 2, 6.567, 0.241, 0, 7.267, -1.92, 0, 7.833, -1.413, 0, 8.567, -1.863, 0, 9.3, -1.406, 0, 10, -1.92, 2, 11.333, -1.92, 1, 11.411, -1.92, 11.489, -2.267, 11.567, -2.51, 1, 11.678, -2.858, 11.789, -2.724, 11.9, -3.334, 1, 12.067, -4.249, 12.233, -12.934, 12.4, -12.934, 1, 12.522, -12.934, 12.645, -12.812, 12.767, -12.334, 1, 12.845, -12.03, 12.922, -11.612, 13, -11.014, 1, 13.033, -10.758, 13.067, -3.985, 13.1, -0.043, 1, 13.267, 19.669, 13.433, 30, 13.6, 30, 2, 13.9, 30, 2, 15.133, 30, 0, 15.367, 29.1, 2, 16.167, 29.1, 0, 16.433, 26.098, 0, 16.567, 26.712, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param211", "Segments": [0, 0, 2, 0.333, 0, 2, 0.5, 0, 0, 0.933, 6.509, 0, 1.167, 2.549, 2, 1.667, 2.549, 2, 2.333, 2.549, 2, 2.9, 2.549, 2, 3.9, 2.549, 2, 4.167, 2.549, 2, 4.467, 2.549, 2, 4.667, 2.549, 2, 6.567, 2.549, 0, 7.267, -4.604, 0, 7.833, -1.424, 0, 8.567, -4.31, 0, 9.3, -0.908, 0, 10, -4.604, 2, 11.333, -4.604, 1, 11.522, -4.604, 11.711, -4.617, 11.9, -4.541, 1, 12.056, -4.479, 12.211, 4.744, 12.367, 4.744, 1, 12.5, 4.744, 12.634, -5.425, 12.767, -7.241, 1, 12.878, -8.755, 12.989, -8.703, 13.1, -9.744, 1, 13.167, -10.368, 13.233, -11.368, 13.3, -11.368, 0, 13.6, -4.309, 2, 13.9, -4.309, 2, 15.133, -4.309, 2, 15.367, -4.309, 2, 16.167, -4.309, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param228", "Segments": [0, 0, 2, 0.333, 0, 2, 0.5, 0, 2, 1, 0, 2, 1.667, 0, 2, 2.333, 0, 2, 2.9, 0, 0, 3.9, -2.7, 1, 3.989, -2.7, 4.078, -2.666, 4.167, -1.959, 1, 4.267, -1.163, 4.367, 0, 4.467, 0, 2, 4.667, 0, 2, 6.567, 0, 0, 7.267, -4.32, 2, 7.833, -4.32, 2, 10, -4.32, 2, 11.333, -4.32, 0, 11.9, -4.261, 2, 12.767, -4.261, 2, 13, -4.261, 0, 13.6, 11, 2, 15.133, 11, 2, 15.367, 11, 2, 16.167, 11, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param221", "Segments": [0, 0, 2, 0.333, 0, 2, 0.5, 0, 2, 0.667, 0, 0, 0.7, 1, 2, 1, 1, 2, 1.667, 1, 2, 2.333, 1, 2, 2.9, 1, 2, 3.9, 1, 2, 4.167, 1, 2, 4.467, 1, 2, 4.667, 1, 2, 6.567, 1, 2, 6.933, 1, 0, 6.967, 0.995, 2, 7.833, 0.995, 2, 10, 0.995, 2, 11.667, 0.995, 2, 12.233, 0.995, 0, 12.267, 0.982, 2, 12.767, 0.982, 2, 13, 0.982, 0, 13.6, 1, 2, 15.367, 1, 2, 16.533, 1, 0, 16.567, 0, 2, 17.233, 0]}, {"Target": "Parameter", "Id": "Param222", "Segments": [0, 0, 2, 0.333, 0, 2, 0.5, 0, 2, 0.667, 0, 2, 0.7, 0, 2, 1, 0, 2, 1.667, 0, 2, 2.333, 0, 2, 2.9, 0, 2, 3.9, 0, 2, 4.167, 0, 2, 4.467, 0, 2, 4.667, 0, 2, 6.567, 0, 2, 6.933, 0, 2, 6.967, 0, 2, 7.833, 0, 2, 10, 0, 2, 11.667, 0, 2, 12.233, 0, 2, 12.267, 0, 2, 12.767, 0, 2, 13, 0, 2, 13.167, 0, 0, 13.2, 1, 2, 13.6, 1, 2, 15.367, 1, 2, 16.533, 1, 0, 16.567, 0, 2, 17.233, 0]}, {"Target": "Parameter", "Id": "Param223", "Segments": [0, 0, 2, 0.333, 0, 2, 0.5, 0, 2, 0.667, 0, 0, 0.7, 1, 2, 1, 1, 2, 1.667, 1, 2, 2.333, 1, 2, 2.9, 1, 2, 3.9, 1, 2, 4.167, 1, 2, 4.467, 1, 2, 4.667, 1, 2, 6.567, 1, 2, 6.933, 1, 0, 6.967, 0.995, 2, 7.833, 0.995, 2, 10, 0.995, 2, 11.667, 0.995, 2, 12.233, 0.995, 0, 12.267, 0.982, 2, 12.767, 0.982, 2, 13, 0.982, 0, 13.167, 1, 0, 13.2, 0, 2, 13.6, 0, 2, 15.367, 0, 2, 16.533, 0, 2, 16.567, 0, 2, 17.233, 0]}, {"Target": "Parameter", "Id": "Param236", "Segments": [0, 0, 2, 0.333, 0, 2, 0.5, 0, 2, 0.667, 0, 2, 0.7, 0, 2, 1, 0, 2, 1.667, 0, 2, 2.333, 0, 2, 2.9, 0, 2, 3.9, 0, 2, 4.167, 0, 2, 4.467, 0, 2, 4.667, 0, 2, 6.567, 0, 2, 6.933, 0, 0, 6.967, 1, 2, 7.833, 1, 2, 10, 1, 2, 11.667, 1, 2, 12.233, 1, 0, 12.267, 0, 2, 12.767, 0, 2, 13, 0, 2, 13.167, 0, 2, 13.2, 0, 2, 13.6, 0, 2, 15.367, 0, 2, 16.533, 0, 2, 16.567, 0, 2, 17.233, 0]}, {"Target": "Parameter", "Id": "Param237", "Segments": [0, 0, 2, 0.333, 0, 2, 0.5, 0, 2, 0.667, 0, 0, 0.7, 1, 2, 1, 1, 2, 1.667, 1, 2, 2.333, 1, 2, 2.9, 1, 2, 3.9, 1, 2, 4.167, 1, 2, 4.467, 1, 2, 4.667, 1, 2, 6.567, 1, 2, 6.933, 1, 0, 6.967, 0, 2, 7.833, 0, 2, 10, 0, 2, 11.667, 0, 2, 12.233, 0, 2, 12.267, 0, 2, 12.767, 0, 2, 13, 0, 2, 13.167, 0, 2, 13.2, 0, 2, 13.6, 0, 2, 15.367, 0, 2, 16.533, 0, 2, 16.567, 0, 2, 17.233, 0]}, {"Target": "Parameter", "Id": "Param238", "Segments": [0, 0, 2, 0.333, 0, 2, 0.5, 0, 2, 0.667, 0, 2, 0.7, 0, 2, 1, 0, 2, 1.667, 0, 2, 2.333, 0, 2, 2.9, 0, 2, 3.9, 0, 2, 4.167, 0, 2, 4.467, 0, 2, 4.667, 0, 2, 6.567, 0, 2, 6.933, 0, 2, 6.967, 0, 2, 7.833, 0, 2, 10, 0, 2, 11.667, 0, 2, 12.233, 0, 2, 12.267, 0, 2, 12.767, 0, 2, 13, 0, 2, 13.167, 0, 0, 13.2, 1, 2, 13.6, 1, 2, 15.367, 1, 2, 16.533, 1, 0, 16.567, 0, 2, 17.233, 0]}, {"Target": "Parameter", "Id": "Param240", "Segments": [0, 0, 2, 0.333, 0, 2, 0.5, 0, 2, 0.667, 0, 2, 0.7, 0, 2, 1, 0, 2, 1.667, 0, 2, 2.333, 0, 2, 2.9, 0, 2, 3.9, 0, 2, 4.167, 0, 2, 4.467, 0, 2, 4.667, 0, 2, 6.567, 0, 2, 6.933, 0, 2, 6.967, 0, 2, 7.833, 0, 2, 10, 0, 2, 11.667, 0, 2, 12.233, 0, 0, 12.267, 1, 2, 12.767, 1, 2, 13, 1, 2, 13.167, 1, 0, 13.2, 0, 2, 13.6, 0, 2, 15.367, 0, 2, 16.533, 0, 2, 16.567, 0, 2, 17.233, 0]}, {"Target": "Parameter", "Id": "Param15", "Segments": [0, 0, 2, 0.333, 0, 0, 0.5, -2.46, 0, 0.767, 11.113, 1, 0.845, 11.113, 0.922, 9.759, 1, 9.461, 1, 1.222, 8.61, 1.445, 8.441, 1.667, 8.441, 2, 2.333, 8.441, 1, 2.522, 8.441, 2.711, 9.27, 2.9, 9.461, 1, 3.233, 9.799, 3.567, 9.821, 3.9, 9.821, 1, 3.989, 9.821, 4.078, 9.854, 4.167, 9.722, 1, 4.267, 9.574, 4.367, 7.601, 4.467, 7.601, 0, 4.667, 8.441, 2, 6.567, 8.441, 1, 6.667, 8.441, 6.767, 8.608, 6.867, 9.461, 1, 7, 10.598, 7.134, 11.574, 7.267, 11.574, 2, 7.833, 11.574, 2, 9.533, 11.574, 0, 9.733, 10.059, 0, 10, 11.017, 1, 10.522, 11.017, 11.045, 10.696, 11.567, 9.656, 1, 11.678, 9.435, 11.789, 8.596, 11.9, 8.596, 0, 12.167, 11.074, 0, 12.433, 7.415, 2, 13, 7.415, 1, 13.056, 7.415, 13.111, 7.801, 13.167, 9.677, 1, 13.189, 10.427, 13.211, 13.953, 13.233, 13.953, 1, 13.278, 13.953, 13.322, 14.054, 13.367, 13.575, 1, 13.445, 12.738, 13.522, 8.155, 13.6, 8.074, 1, 13.7, 7.97, 13.8, 7.956, 13.9, 7.894, 1, 14.222, 7.696, 14.545, 7.594, 14.867, 7.594, 1, 14.956, 7.594, 15.044, 7.582, 15.133, 7.66, 1, 15.211, 7.729, 15.289, 8.674, 15.367, 8.674, 2, 16.167, 8.674, 0, 16.367, 8.71, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param16", "Segments": [0, 0, 2, 0.333, 0, 1, 0.389, 0, 0.444, 0.54, 0.5, 3.54, 1, 0.667, 12.541, 0.833, 18.471, 1, 18.471, 2, 1.667, 18.471, 2, 2.333, 18.471, 2, 2.9, 18.471, 2, 3.9, 18.471, 2, 4.167, 18.471, 2, 4.467, 18.471, 2, 4.667, 18.471, 2, 6.567, 18.471, 2, 6.867, 18.471, 0, 7.267, 18.379, 2, 7.833, 18.379, 2, 9.533, 18.379, 2, 10, 18.379, 2, 11.333, 18.379, 0, 11.9, 18.129, 2, 12.767, 18.129, 2, 13, 18.129, 0, 13.6, 17.203, 2, 15.133, 17.203, 2, 15.367, 17.203, 2, 16.167, 17.203, 0, 16.267, -20.648, 1, 16.345, -20.648, 16.422, -15.712, 16.5, -9.31, 1, 16.6, -1.078, 16.7, 1.77, 16.8, 1.77, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param17", "Segments": [0, 0, 2, 0.333, 0, 0, 0.5, -7.32, 0, 1, 22.926, 2, 1.667, 22.926, 2, 2.333, 22.926, 2, 2.9, 22.926, 2, 3.9, 22.926, 2, 4.167, 22.926, 2, 4.467, 22.926, 2, 4.667, 22.926, 2, 6.567, 22.926, 2, 6.867, 22.926, 0, 7.267, 22.811, 2, 7.833, 22.811, 2, 9.533, 22.811, 2, 10, 22.811, 2, 11.333, 22.811, 0, 11.9, 22.501, 2, 12.767, 22.501, 2, 13, 22.501, 0, 13.6, 21.352, 2, 15.133, 21.352, 2, 15.367, 21.352, 2, 16.167, 21.352, 1, 16.378, 21.352, 16.589, 21.42, 16.8, 20.369, 1, 16.944, 19.65, 17.089, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param19", "Segments": [0, 0, 2, 0.333, 0, 2, 0.5, 0, 0, 1, -28.153, 2, 1.667, -28.153, 2, 2.333, -28.153, 2, 2.9, -28.153, 2, 3.9, -28.153, 2, 4.167, -28.153, 0, 4.467, -34.093, 2, 4.667, -34.093, 2, 6.567, -34.093, 0, 6.867, -28.153, 0, 7.267, -60, 2, 7.833, -60, 2, 9.533, -60, 2, 10, -60, 2, 11.333, -60, 1, 11.522, -60, 11.711, -60.212, 11.9, -59.183, 1, 12.089, -58.154, 12.278, 0, 12.467, 0, 2, 13, 0, 2, 13.6, 0, 2, 15.133, 0, 2, 15.367, 0, 2, 16.167, 0, 2, 17.233, 0]}, {"Target": "Parameter", "Id": "Param224", "Segments": [0, 0, 2, 0.333, 0, 2, 0.5, 0, 2, 1, 0, 2, 1.667, 0, 2, 2.333, 0, 2, 2.9, 0, 2, 3.9, 0, 2, 4.167, 0, 2, 4.467, 0, 2, 4.667, 0, 2, 6.567, 0, 2, 6.867, 0, 2, 7.267, 0, 2, 7.833, 0, 2, 9.533, 0, 2, 10, 0, 2, 11.333, 0, 2, 11.9, 0, 2, 12.767, 0, 2, 13, 0, 0, 13.1, -30, 0, 13.6, 0, 2, 15.133, 0, 0, 15.367, -0.66, 2, 16.167, -0.66, 0, 16.267, -1.955, 0, 16.733, 28.6, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param233", "Segments": [0, 0, 2, 0.333, 0, 0, 0.5, 8.729, 1, 0.644, 8.729, 0.789, 1.75, 0.933, -0.644, 1, 1.011, -1.933, 1.089, -1.604, 1.167, -1.604, 0, 1.667, -1.544, 2, 2.333, -1.544, 1, 2.522, -1.544, 2.711, -1.536, 2.9, -1.604, 1, 3.233, -1.724, 3.567, -1.904, 3.9, -1.904, 0, 4.167, -1.822, 0, 4.467, -1.844, 0, 4.667, -1.544, 2, 6.567, -1.544, 0, 6.867, 0.016, 1, 7, 0.016, 7.134, -0.717, 7.267, -0.996, 1, 7.456, -1.392, 7.644, -1.416, 7.833, -1.416, 2, 9.533, -1.416, 0, 9.733, -1.232, 0, 10, -2.016, 2, 11.333, -2.016, 1, 11.411, -2.016, 11.489, -1.42, 11.567, -1.289, 1, 11.678, -1.102, 11.789, -1.202, 11.9, -0.917, 1, 12.067, -0.49, 12.233, 4.387, 12.4, 4.387, 1, 12.522, 4.387, 12.645, 4.268, 12.767, 4.087, 1, 12.845, 3.972, 12.922, 3.952, 13, 3.787, 1, 13.056, 3.669, 13.111, 2.001, 13.167, -1.166, 1, 13.311, -9.4, 13.456, -22.805, 13.6, -22.805, 0, 13.9, -22.565, 2, 15.133, -22.565, 2, 15.367, -22.565, 2, 16.167, -22.565, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param234", "Segments": [0, 0, 2, 0.333, 0, 2, 0.5, 0, 0, 0.933, 11.324, 0, 1.167, 7.349, 0, 1.667, 8.024, 2, 2.333, 8.024, 0, 2.9, 7.349, 2, 3.9, 7.349, 2, 4.167, 7.349, 0, 4.467, 9.749, 0, 4.667, 8.024, 2, 6.567, 8.024, 1, 6.667, 8.024, 6.767, 10.753, 6.867, 13.349, 1, 6.945, 15.368, 7.022, 15.641, 7.1, 15.641, 0, 7.267, 5.437, 1, 7.4, 5.437, 7.534, 6.83, 7.667, 7.349, 1, 7.911, 8.301, 8.156, 9.424, 8.4, 9.511, 1, 8.756, 9.638, 9.111, 9.65, 9.467, 9.749, 1, 9.556, 9.774, 9.644, 13.998, 9.733, 13.998, 1, 9.822, 13.998, 9.911, 12.587, 10, 12.412, 1, 10.444, 11.538, 10.889, 11.035, 11.333, 10.162, 1, 11.411, 10.009, 11.489, 1.105, 11.567, -1.602, 1, 11.678, -5.47, 11.789, -6.437, 11.9, -6.437, 1, 12.067, -6.437, 12.233, -4.006, 12.4, 1.945, 1, 12.522, 6.309, 12.645, 9.145, 12.767, 9.145, 0, 13, 8.245, 0, 13.167, 13.205, 0, 13.6, 6.845, 2, 13.9, 6.845, 2, 15.133, 6.845, 2, 15.367, 6.845, 2, 16.167, 6.845, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param285", "Segments": [0, 0, 2, 0.333, 0, 2, 6.567, 0, 0, 6.867, -30, 0, 7.267, 8, 0, 7.4, -4, 0, 7.833, 4, 1, 8.022, 4, 8.211, -7.611, 8.4, -9.62, 1, 8.644, -12.22, 8.889, -12.001, 9.133, -14.66, 1, 9.333, -16.836, 9.533, -30, 9.733, -30, 0, 10, 6.06, 0, 10.233, 0, 2, 11.333, 0, 0, 12.233, -30, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param235", "Segments": [0, 0, 2, 0.333, 0, 2, 0.5, 0, 0, 1, -9.503, 2, 1.667, -9.503, 2, 2.333, -9.503, 2, 2.9, -9.503, 0, 3.9, -14.963, 1, 3.989, -14.963, 4.078, -14.882, 4.167, -13.464, 1, 4.267, -11.868, 4.367, -9.503, 4.467, -9.503, 2, 4.667, -9.503, 2, 6.567, -9.503, 0, 7.267, 25, 2, 7.833, 25, 2, 9.533, 25, 2, 10, 25, 2, 11.333, 25, 0, 11.9, 24.66, 2, 12.767, 24.66, 2, 13, 24.66, 0, 13.6, 23.401, 2, 13.9, 23.401, 2, 15.133, 23.401, 2, 15.367, 23.401, 2, 16.167, 23.401, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param277", "Segments": [0, 0, 2, 0.333, 0, 0, 0.6, 12.46, 0, 1.133, -9.549, 0, 1.567, 12.46, 0, 1.967, -9.549, 0, 2.4, 12.46, 0, 2.8, -9.549, 0, 3.233, 12.46, 0, 3.633, -9.549, 0, 4.067, 12.46, 0, 4.467, -9.549, 0, 4.9, 12.46, 0, 5.3, -9.549, 0, 5.733, 12.46, 0, 6.133, -9.549, 0, 6.567, 12.46, 0, 6.967, -9.549, 0, 7.4, 12.46, 0, 7.8, -9.549, 0, 8.233, 12.46, 0, 8.633, -9.549, 1, 8.778, -9.549, 8.922, -7.835, 9.067, 0, 1, 9.156, 4.822, 9.244, 12.46, 9.333, 12.46, 0, 9.867, -9.549, 0, 10.3, 12.46, 0, 10.7, -9.549, 0, 11.133, 12.46, 0, 11.533, -9.549, 0, 11.967, 12.46, 0, 12.367, -9.549, 0, 12.8, 12.46, 0, 13.2, -9.549, 0, 13.633, 12.46, 0, 14.033, -9.549, 0, 14.467, 12.46, 0, 14.867, -9.549, 0, 15.3, 12.46, 0, 15.7, -9.549, 0, 16.133, 12.46, 0, 16.533, -9.549, 0, 16.967, 12.46, 1, 17.056, 12.46, 17.144, 2.615, 17.233, 0.427]}, {"Target": "Parameter", "Id": "Param278", "Segments": [0, 0, 2, 0.333, 0, 0, 0.6, 0.25, 0, 1.133, -0.17, 0, 1.567, 0.25, 0, 1.967, -0.17, 0, 2.4, 0.25, 0, 2.8, -0.17, 0, 3.233, 0.25, 0, 3.633, -0.17, 0, 4.067, 0.25, 0, 4.467, -0.17, 0, 4.9, 0.25, 0, 5.3, -0.17, 0, 5.733, 0.25, 0, 6.133, -0.17, 0, 6.567, 0.25, 0, 6.967, -0.17, 0, 7.4, 0.25, 0, 7.8, -0.17, 0, 8.233, 0.25, 0, 8.633, -0.17, 1, 8.778, -0.17, 8.922, -0.146, 9.067, 0, 1, 9.156, 0.09, 9.244, 0.25, 9.333, 0.25, 0, 9.867, -0.17, 0, 10.3, 0.25, 0, 10.7, -0.17, 0, 11.133, 0.25, 0, 11.533, -0.17, 0, 11.967, 0.25, 0, 12.367, -0.17, 0, 12.8, 0.25, 0, 13.2, -0.17, 0, 13.633, 0.25, 0, 14.033, -0.17, 0, 14.467, 0.25, 0, 14.867, -0.17, 0, 15.3, 0.25, 0, 15.7, -0.17, 0, 16.133, 0.25, 0, 16.533, -0.17, 0, 16.967, 0.25, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param280", "Segments": [0, 0, 2, 0.333, 0, 0, 0.6, 0.49, 0, 1.133, -0.407, 0, 1.567, 0.49, 0, 1.967, -0.407, 0, 2.4, 0.49, 0, 2.8, -0.407, 0, 3.233, 0.49, 0, 3.633, -0.407, 0, 4.067, 0.49, 0, 4.467, -0.407, 0, 4.9, 0.49, 0, 5.3, -0.407, 0, 5.733, 0.49, 0, 6.133, -0.407, 0, 6.567, 0.49, 0, 6.967, -0.407, 0, 7.4, 0.49, 0, 7.8, -0.407, 0, 8.233, 0.49, 0, 8.633, -0.407, 1, 8.778, -0.407, 8.922, -0.334, 9.067, 0, 1, 9.156, 0.205, 9.244, 0.49, 9.333, 0.49, 0, 9.867, -0.407, 0, 10.3, 0.49, 0, 10.7, -0.407, 0, 11.133, 0.49, 0, 11.533, -0.407, 0, 11.967, 0.49, 0, 12.367, -0.407, 0, 12.8, 0.49, 0, 13.2, -0.407, 0, 13.633, 0.49, 0, 14.033, -0.407, 0, 14.467, 0.49, 0, 14.867, -0.407, 0, 15.3, 0.49, 0, 15.7, -0.407, 0, 16.133, 0.49, 0, 16.533, -0.407, 0, 16.967, 0.49, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param7", "Segments": [0, 0, 2, 0.333, 0, 0, 1.733, -9, 0, 3.133, 0, 0, 4.567, -9, 0, 5.967, 0, 0, 7.4, -9, 0, 8.8, 0, 0, 10.2, -9, 0, 11.633, 0, 0, 13.033, -9, 0, 14.467, 0, 0, 15.867, -9, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param66", "Segments": [0, 0, 2, 0.333, 0, 0, 1.733, -19, 0, 3.133, 0, 0, 4.567, -19, 0, 5.967, 0, 0, 7.4, -19, 0, 8.8, 0, 0, 10.2, -19, 0, 11.633, 0, 0, 13.033, -19, 0, 14.467, 0, 0, 15.867, -19, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param67", "Segments": [0, 0, 2, 0.333, 0, 0, 1.033, 16, 0, 2.433, -16, 1, 2.666, -16, 2.9, -10.412, 3.133, 0, 1, 3.378, 10.908, 3.622, 16, 3.867, 16, 0, 5.267, -16, 1, 5.5, -16, 5.734, -10.667, 5.967, 0, 1, 6.2, 10.667, 6.434, 16, 6.667, 16, 0, 8.1, -16, 1, 8.333, -16, 8.567, -10.667, 8.8, 0, 1, 9.033, 10.667, 9.267, 16, 9.5, 16, 0, 10.933, -16, 1, 11.166, -16, 11.4, -10.667, 11.633, 0, 1, 11.866, 10.667, 12.1, 16, 12.333, 16, 0, 13.733, -16, 1, 13.978, -16, 14.222, -10.908, 14.467, 0, 1, 14.7, 10.412, 14.934, 16, 15.167, 16, 0, 16.567, -16, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param68", "Segments": [0, 0, 2, 0.333, 0, 0, 1.733, 17, 0, 3.133, 0, 0, 4.567, 17, 0, 5.967, 0, 0, 7.4, 17, 0, 8.8, 0, 0, 10.2, 17, 0, 11.633, 0, 0, 13.033, 17, 0, 14.467, 0, 0, 15.867, 17, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param69", "Segments": [0, 0, 2, 0.333, 0, 0, 1.033, -16, 0, 2.433, 16, 1, 2.666, 16, 2.9, 10.412, 3.133, 0, 1, 3.378, -10.908, 3.622, -16, 3.867, -16, 0, 5.267, 16, 1, 5.5, 16, 5.734, 10.667, 5.967, 0, 1, 6.2, -10.667, 6.434, -16, 6.667, -16, 0, 8.1, 16, 1, 8.333, 16, 8.567, 10.667, 8.8, 0, 1, 9.033, -10.667, 9.267, -16, 9.5, -16, 0, 10.933, 16, 1, 11.166, 16, 11.4, 10.667, 11.633, 0, 1, 11.866, -10.667, 12.1, -16, 12.333, -16, 0, 13.733, 16, 1, 13.978, 16, 14.222, 10.908, 14.467, 0, 1, 14.7, -10.412, 14.934, -16, 15.167, -16, 0, 16.567, 16, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param35", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, 6.534, 0, 0.733, -12.073, 0, 1, 16.828, 0, 1.267, -14.002, 0, 1.567, 7.101, 0, 1.867, -1.066, 0, 2.133, 1.093, 0, 2.6, -4.79, 0, 2.967, 4.206, 0, 3.733, -5.096, 0, 4.033, 7.22, 0, 4.333, -9.415, 0, 4.633, 12.177, 0, 4.9, -7.145, 0, 5.233, 0.626, 0, 5.367, 0.08, 0, 5.633, 2.149, 0, 5.933, -2.158, 0, 6.267, 0.849, 0, 6.533, -0.09, 0, 6.6, -0.026, 0, 6.733, -2.256, 0, 7, 3.958, 0, 7.3, -4.351, 0, 7.6, 2.512, 0, 8, -1.753, 0, 8.633, 3.23, 0, 9.1, -3.158, 0, 9.7, 3.528, 0, 10.133, -3.04, 0, 10.5, 1.734, 0, 10.8, -0.624, 0, 11.1, 0.224, 0, 11.367, -0.063, 0, 11.5, 6.59, 0, 11.8, -15.221, 0, 12.067, 16.342, 0, 12.4, -9.011, 0, 12.933, 8.636, 0, 13.233, -6.732, 0, 13.733, 2.912, 0, 14.433, -1.494, 0, 14.833, -0.254, 0, 15.033, -5.727, 0, 15.3, 17.119, 0, 15.567, -11.082, 0, 15.9, 2.688, 0, 16.167, -0.927, 0, 16.467, 1.906, 0, 16.967, -1.984, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param36", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -4.005, 0, 0.667, 10.041, 0, 0.9, -17.13, 0, 1.133, 21.581, 0, 1.433, -18.006, 0, 1.733, 11.119, 0, 2, -5.725, 0, 2.3, 2.986, 0, 2.767, -3.833, 0, 3.067, 3.535, 0, 3.4, -1.543, 0, 3.667, 2.462, 0, 3.933, -5.843, 0, 4.233, 9.608, 0, 4.5, -13.817, 0, 4.8, 15.088, 0, 5.067, -10.315, 0, 5.367, 4.843, 0, 5.6, -2.059, 0, 5.867, 2.15, 0, 6.133, -2.319, 0, 6.4, 1.556, 0, 6.6, -0.303, 0, 6.733, 0.714, 0, 6.9, -3.259, 0, 7.167, 4.809, 0, 7.467, -5.795, 0, 7.733, 4.285, 0, 8.033, -1.959, 0, 8.367, 0.147, 0, 8.567, -0.632, 0, 8.833, 1.865, 0, 9.2, -1.736, 0, 9.5, -0.137, 0, 9.6, -0.239, 0, 9.867, 1.7, 0, 10.3, -2.195, 0, 10.633, 2.332, 0, 10.933, -1.456, 0, 11.233, 0.74, 0, 11.467, -4.541, 0, 11.7, 9.402, 0, 11.967, -18.46, 0, 12.267, 18.231, 0, 12.533, -12.103, 0, 12.8, 2.459, 0, 12.933, 0.022, 0, 13.1, 7.826, 0, 13.367, -6.962, 0, 13.7, 2.639, 0, 14.067, -0.81, 0, 14.333, 0.557, 0, 14.6, -0.59, 0, 14.967, 3.103, 0, 15.233, -10.106, 0, 15.467, 19.773, 0, 15.733, -14.43, 0, 16.033, 7.878, 0, 16.333, -4.216, 0, 16.633, 2.585, 0, 17.033, -1.065, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param37", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, 12.136, 0, 0.733, -21.997, 0, 0.967, 23.973, 0, 1.233, -13.519, 0, 1.633, 3.475, 0, 1.767, 3.139, 0, 1.9, 3.397, 0, 2.567, -8.077, 0, 2.933, 7.918, 0, 3.3, 0.021, 0, 3.333, 0.044, 0, 3.733, -8.541, 0, 4.033, 14.116, 0, 4.333, -19.73, 0, 4.633, 15.327, 0, 5.033, -9.091, 0, 5.6, 9.01, 0, 5.933, -6.794, 0, 6.267, 2.376, 0, 6.633, -9.814, 0, 6.933, 11.308, 0, 7.333, -5.087, 0, 7.633, 1.917, 0, 8.067, -4.051, 0, 8.633, 6.663, 0, 9.067, -7.269, 0, 9.667, 6.432, 0, 10.2, -7.557, 0, 10.5, 5.227, 0, 10.8, -1.849, 0, 11.133, 0.657, 0, 11.367, -0.162, 0, 11.467, 14.161, 0, 11.733, -15.742, 0, 12.067, 12.422, 0, 12.667, -12.044, 0, 12.967, 21.495, 0, 13.233, -14.359, 0, 13.667, 5.04, 0, 14.433, -4.03, 0, 15.233, 9.686, 0, 15.533, -9.276, 0, 15.867, 0.506, 0, 16.067, -0.413, 0, 16.433, 5.204, 0, 17, -4.804, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param39", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -7.188, 0, 0.633, 17.052, 0, 0.867, -28.494, 0, 1.133, 28.36, 0, 1.4, -17.035, 0, 1.7, 7.67, 0, 2, -2.497, 0, 2.267, 2.02, 0, 2.4, 1.436, 0, 2.467, 1.741, 0, 2.767, -6.25, 0, 3.067, 7, 0, 3.367, -3.487, 0, 3.667, 4.657, 0, 3.933, -10.544, 0, 4.233, 17.782, 0, 4.5, -25.082, 0, 4.8, 17.656, 0, 5.133, -8.648, 0, 5.433, 1.561, 0, 5.567, 0.559, 0, 5.833, 4.917, 0, 6.1, -7.497, 0, 6.4, 4.979, 0, 6.467, 4.365, 0, 6.533, 4.664, 0, 6.833, -11.231, 0, 7.1, 11.399, 0, 7.433, -7.402, 0, 7.767, 4.907, 0, 8.167, -2.334, 0, 8.433, -0.138, 0, 8.533, -0.518, 0, 8.833, 3.629, 0, 9.2, -4.273, 0, 9.5, 0.801, 0, 9.633, 0.402, 0, 9.867, 2.501, 0, 10.4, -5.573, 0, 10.667, 6.552, 0, 10.933, -4.093, 0, 11.267, 2.099, 0, 11.467, -9.744, 0, 11.633, 14.516, 0, 11.933, -16.809, 0, 12.233, 12.98, 0, 12.5, -5.082, 0, 12.667, 1.615, 0, 12.867, -13.151, 0, 13.1, 25.012, 0, 13.4, -17.058, 0, 13.7, 7.476, 0, 14, -2.029, 0, 14.3, 1.762, 0, 14.567, -1.885, 0, 14.867, 0.526, 0, 15.167, -3.749, 0, 15.433, 9.88, 0, 15.7, -9.296, 0, 16, 4.805, 0, 16.3, -3.508, 0, 16.6, 3.366, 0, 17.167, -2.443, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param38", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, 12.136, 0, 0.733, -21.997, 0, 0.967, 23.973, 0, 1.233, -13.519, 0, 1.633, 3.475, 0, 1.767, 3.139, 0, 1.9, 3.397, 0, 2.567, -8.077, 0, 2.933, 7.918, 0, 3.3, 0.021, 0, 3.333, 0.044, 0, 3.733, -8.541, 0, 4.033, 14.116, 0, 4.333, -19.73, 0, 4.633, 15.327, 0, 5.033, -9.091, 0, 5.6, 9.01, 0, 5.933, -6.794, 0, 6.267, 2.376, 0, 6.633, -9.814, 0, 6.933, 11.308, 0, 7.333, -5.087, 0, 7.633, 1.917, 0, 8.067, -4.051, 0, 8.633, 6.663, 0, 9.067, -7.269, 0, 9.667, 6.432, 0, 10.2, -7.557, 0, 10.5, 5.227, 0, 10.8, -1.849, 0, 11.133, 0.657, 0, 11.367, -0.162, 0, 11.467, 14.161, 0, 11.733, -15.742, 0, 12.067, 12.422, 0, 12.667, -12.044, 0, 12.967, 21.495, 0, 13.233, -14.359, 0, 13.667, 5.04, 0, 14.433, -4.03, 0, 15.233, 9.686, 0, 15.533, -9.276, 0, 15.867, 0.506, 0, 16.067, -0.413, 0, 16.433, 5.204, 0, 17, -4.804, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param40", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -7.188, 0, 0.633, 17.052, 0, 0.867, -28.494, 0, 1.133, 28.36, 0, 1.4, -17.035, 0, 1.7, 7.67, 0, 2, -2.497, 0, 2.267, 2.02, 0, 2.4, 1.436, 0, 2.467, 1.741, 0, 2.767, -6.25, 0, 3.067, 7, 0, 3.367, -3.487, 0, 3.667, 4.657, 0, 3.933, -10.544, 0, 4.233, 17.782, 0, 4.5, -25.082, 0, 4.8, 17.656, 0, 5.133, -8.648, 0, 5.433, 1.561, 0, 5.567, 0.559, 0, 5.833, 4.917, 0, 6.1, -7.497, 0, 6.4, 4.979, 0, 6.467, 4.365, 0, 6.533, 4.664, 0, 6.833, -11.231, 0, 7.1, 11.399, 0, 7.433, -7.402, 0, 7.767, 4.907, 0, 8.167, -2.334, 0, 8.433, -0.138, 0, 8.533, -0.518, 0, 8.833, 3.629, 0, 9.2, -4.273, 0, 9.5, 0.801, 0, 9.633, 0.402, 0, 9.867, 2.501, 0, 10.4, -5.573, 0, 10.667, 6.552, 0, 10.933, -4.093, 0, 11.267, 2.099, 0, 11.467, -9.744, 0, 11.633, 14.516, 0, 11.933, -16.809, 0, 12.233, 12.98, 0, 12.5, -5.082, 0, 12.667, 1.615, 0, 12.867, -13.151, 0, 13.1, 25.012, 0, 13.4, -17.058, 0, 13.7, 7.476, 0, 14, -2.029, 0, 14.3, 1.762, 0, 14.567, -1.885, 0, 14.867, 0.526, 0, 15.167, -3.749, 0, 15.433, 9.88, 0, 15.7, -9.296, 0, 16, 4.805, 0, 16.3, -3.508, 0, 16.6, 3.366, 0, 17.167, -2.443, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param41", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, 12.136, 0, 0.733, -21.997, 0, 0.967, 23.973, 0, 1.233, -13.519, 0, 1.633, 3.475, 0, 1.767, 3.139, 0, 1.9, 3.397, 0, 2.567, -8.077, 0, 2.933, 7.918, 0, 3.3, 0.021, 0, 3.333, 0.044, 0, 3.733, -8.541, 0, 4.033, 14.116, 0, 4.333, -19.73, 0, 4.633, 15.327, 0, 5.033, -9.091, 0, 5.6, 9.01, 0, 5.933, -6.794, 0, 6.267, 2.376, 0, 6.633, -9.814, 0, 6.933, 11.308, 0, 7.333, -5.087, 0, 7.633, 1.917, 0, 8.067, -4.051, 0, 8.633, 6.663, 0, 9.067, -7.269, 0, 9.667, 6.432, 0, 10.2, -7.557, 0, 10.5, 5.227, 0, 10.8, -1.849, 0, 11.133, 0.657, 0, 11.367, -0.162, 0, 11.467, 14.161, 0, 11.733, -15.742, 0, 12.067, 12.422, 0, 12.667, -12.044, 0, 12.967, 21.495, 0, 13.233, -14.359, 0, 13.667, 5.04, 0, 14.433, -4.03, 0, 15.233, 9.686, 0, 15.533, -9.276, 0, 15.867, 0.506, 0, 16.067, -0.413, 0, 16.433, 5.204, 0, 17, -4.804, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param42", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -7.188, 0, 0.633, 17.052, 0, 0.867, -28.494, 0, 1.133, 28.36, 0, 1.4, -17.035, 0, 1.7, 7.67, 0, 2, -2.497, 0, 2.267, 2.02, 0, 2.4, 1.436, 0, 2.467, 1.741, 0, 2.767, -6.25, 0, 3.067, 7, 0, 3.367, -3.487, 0, 3.667, 4.657, 0, 3.933, -10.544, 0, 4.233, 17.782, 0, 4.5, -25.082, 0, 4.8, 17.656, 0, 5.133, -8.648, 0, 5.433, 1.561, 0, 5.567, 0.559, 0, 5.833, 4.917, 0, 6.1, -7.497, 0, 6.4, 4.979, 0, 6.467, 4.365, 0, 6.533, 4.664, 0, 6.833, -11.231, 0, 7.1, 11.399, 0, 7.433, -7.402, 0, 7.767, 4.907, 0, 8.167, -2.334, 0, 8.433, -0.138, 0, 8.533, -0.518, 0, 8.833, 3.629, 0, 9.2, -4.273, 0, 9.5, 0.801, 0, 9.633, 0.402, 0, 9.867, 2.501, 0, 10.4, -5.573, 0, 10.667, 6.552, 0, 10.933, -4.093, 0, 11.267, 2.099, 0, 11.467, -9.744, 0, 11.633, 14.516, 0, 11.933, -16.809, 0, 12.233, 12.98, 0, 12.5, -5.082, 0, 12.667, 1.615, 0, 12.867, -13.151, 0, 13.1, 25.012, 0, 13.4, -17.058, 0, 13.7, 7.476, 0, 14, -2.029, 0, 14.3, 1.762, 0, 14.567, -1.885, 0, 14.867, 0.526, 0, 15.167, -3.749, 0, 15.433, 9.88, 0, 15.7, -9.296, 0, 16, 4.805, 0, 16.3, -3.508, 0, 16.6, 3.366, 0, 17.167, -2.443, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param43", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, 12.136, 0, 0.733, -21.997, 0, 0.967, 23.973, 0, 1.233, -13.519, 0, 1.633, 3.475, 0, 1.767, 3.139, 0, 1.9, 3.397, 0, 2.567, -8.077, 0, 2.933, 7.918, 0, 3.3, 0.021, 0, 3.333, 0.044, 0, 3.733, -8.541, 0, 4.033, 14.116, 0, 4.333, -19.73, 0, 4.633, 15.327, 0, 5.033, -9.091, 0, 5.6, 9.01, 0, 5.933, -6.794, 0, 6.267, 2.376, 0, 6.633, -9.814, 0, 6.933, 11.308, 0, 7.333, -5.087, 0, 7.633, 1.917, 0, 8.067, -4.051, 0, 8.633, 6.663, 0, 9.067, -7.269, 0, 9.667, 6.432, 0, 10.2, -7.557, 0, 10.5, 5.227, 0, 10.8, -1.849, 0, 11.133, 0.657, 0, 11.367, -0.162, 0, 11.467, 14.161, 0, 11.733, -15.742, 0, 12.067, 12.422, 0, 12.667, -12.044, 0, 12.967, 21.495, 0, 13.233, -14.359, 0, 13.667, 5.04, 0, 14.433, -4.03, 0, 15.233, 9.686, 0, 15.533, -9.276, 0, 15.867, 0.506, 0, 16.067, -0.413, 0, 16.433, 5.204, 0, 17, -4.804, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param44", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -7.188, 0, 0.633, 17.052, 0, 0.867, -28.494, 0, 1.133, 28.36, 0, 1.4, -17.035, 0, 1.7, 7.67, 0, 2, -2.497, 0, 2.267, 2.02, 0, 2.4, 1.436, 0, 2.467, 1.741, 0, 2.767, -6.25, 0, 3.067, 7, 0, 3.367, -3.487, 0, 3.667, 4.657, 0, 3.933, -10.544, 0, 4.233, 17.782, 0, 4.5, -25.082, 0, 4.8, 17.656, 0, 5.133, -8.648, 0, 5.433, 1.561, 0, 5.567, 0.559, 0, 5.833, 4.917, 0, 6.1, -7.497, 0, 6.4, 4.979, 0, 6.467, 4.365, 0, 6.533, 4.664, 0, 6.833, -11.231, 0, 7.1, 11.399, 0, 7.433, -7.402, 0, 7.767, 4.907, 0, 8.167, -2.334, 0, 8.433, -0.138, 0, 8.533, -0.518, 0, 8.833, 3.629, 0, 9.2, -4.273, 0, 9.5, 0.801, 0, 9.633, 0.402, 0, 9.867, 2.501, 0, 10.4, -5.573, 0, 10.667, 6.552, 0, 10.933, -4.093, 0, 11.267, 2.099, 0, 11.467, -9.744, 0, 11.633, 14.516, 0, 11.933, -16.809, 0, 12.233, 12.98, 0, 12.5, -5.082, 0, 12.667, 1.615, 0, 12.867, -13.151, 0, 13.1, 25.012, 0, 13.4, -17.058, 0, 13.7, 7.476, 0, 14, -2.029, 0, 14.3, 1.762, 0, 14.567, -1.885, 0, 14.867, 0.526, 0, 15.167, -3.749, 0, 15.433, 9.88, 0, 15.7, -9.296, 0, 16, 4.805, 0, 16.3, -3.508, 0, 16.6, 3.366, 0, 17.167, -2.443, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param45", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, 12.136, 0, 0.733, -21.997, 0, 0.967, 23.973, 0, 1.233, -13.519, 0, 1.633, 3.475, 0, 1.767, 3.139, 0, 1.9, 3.397, 0, 2.567, -8.077, 0, 2.933, 7.918, 0, 3.3, 0.021, 0, 3.333, 0.044, 0, 3.733, -8.541, 0, 4.033, 14.116, 0, 4.333, -19.73, 0, 4.633, 15.327, 0, 5.033, -9.091, 0, 5.6, 9.01, 0, 5.933, -6.794, 0, 6.267, 2.376, 0, 6.633, -9.814, 0, 6.933, 11.308, 0, 7.333, -5.087, 0, 7.633, 1.917, 0, 8.067, -4.051, 0, 8.633, 6.663, 0, 9.067, -7.269, 0, 9.667, 6.432, 0, 10.2, -7.557, 0, 10.5, 5.227, 0, 10.8, -1.849, 0, 11.133, 0.657, 0, 11.367, -0.162, 0, 11.467, 14.161, 0, 11.733, -15.742, 0, 12.067, 12.422, 0, 12.667, -12.044, 0, 12.967, 21.495, 0, 13.233, -14.359, 0, 13.667, 5.04, 0, 14.433, -4.03, 0, 15.233, 9.686, 0, 15.533, -9.276, 0, 15.867, 0.506, 0, 16.067, -0.413, 0, 16.433, 5.204, 0, 17, -4.804, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param46", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -7.188, 0, 0.633, 17.052, 0, 0.867, -28.494, 0, 1.133, 28.36, 0, 1.4, -17.035, 0, 1.7, 7.67, 0, 2, -2.497, 0, 2.267, 2.02, 0, 2.4, 1.436, 0, 2.467, 1.741, 0, 2.767, -6.25, 0, 3.067, 7, 0, 3.367, -3.487, 0, 3.667, 4.657, 0, 3.933, -10.544, 0, 4.233, 17.782, 0, 4.5, -25.082, 0, 4.8, 17.656, 0, 5.133, -8.648, 0, 5.433, 1.561, 0, 5.567, 0.559, 0, 5.833, 4.917, 0, 6.1, -7.497, 0, 6.4, 4.979, 0, 6.467, 4.365, 0, 6.533, 4.664, 0, 6.833, -11.231, 0, 7.1, 11.399, 0, 7.433, -7.402, 0, 7.767, 4.907, 0, 8.167, -2.334, 0, 8.433, -0.138, 0, 8.533, -0.518, 0, 8.833, 3.629, 0, 9.2, -4.273, 0, 9.5, 0.801, 0, 9.633, 0.402, 0, 9.867, 2.501, 0, 10.4, -5.573, 0, 10.667, 6.552, 0, 10.933, -4.093, 0, 11.267, 2.099, 0, 11.467, -9.744, 0, 11.633, 14.516, 0, 11.933, -16.809, 0, 12.233, 12.98, 0, 12.5, -5.082, 0, 12.667, 1.615, 0, 12.867, -13.151, 0, 13.1, 25.012, 0, 13.4, -17.058, 0, 13.7, 7.476, 0, 14, -2.029, 0, 14.3, 1.762, 0, 14.567, -1.885, 0, 14.867, 0.526, 0, 15.167, -3.749, 0, 15.433, 9.88, 0, 15.7, -9.296, 0, 16, 4.805, 0, 16.3, -3.508, 0, 16.6, 3.366, 0, 17.167, -2.443, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param62", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, 12.136, 0, 0.733, -21.997, 0, 0.967, 23.973, 0, 1.233, -13.519, 0, 1.633, 3.475, 0, 1.767, 3.139, 0, 1.9, 3.397, 0, 2.567, -8.077, 0, 2.933, 7.918, 0, 3.3, 0.021, 0, 3.333, 0.044, 0, 3.733, -8.541, 0, 4.033, 14.116, 0, 4.333, -19.73, 0, 4.633, 15.327, 0, 5.033, -9.091, 0, 5.6, 9.01, 0, 5.933, -6.794, 0, 6.267, 2.376, 0, 6.633, -9.814, 0, 6.933, 11.308, 0, 7.333, -5.087, 0, 7.633, 1.917, 0, 8.067, -4.051, 0, 8.633, 6.663, 0, 9.067, -7.269, 0, 9.667, 6.432, 0, 10.2, -7.557, 0, 10.5, 5.227, 0, 10.8, -1.849, 0, 11.133, 0.657, 0, 11.367, -0.162, 0, 11.467, 14.161, 0, 11.733, -15.742, 0, 12.067, 12.422, 0, 12.667, -12.044, 0, 12.967, 11.996, 0, 13.233, -14.359, 0, 13.667, 10.348, 0, 14.433, -4.03, 0, 15.233, 9.686, 0, 15.533, -9.276, 0, 15.867, 0.506, 0, 16.067, -0.413, 0, 16.433, 5.204, 0, 17, -4.804, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param63", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -7.188, 0, 0.633, 17.052, 0, 0.867, -28.494, 0, 1.133, 28.36, 0, 1.4, -17.035, 0, 1.7, 7.67, 0, 2, -2.497, 0, 2.267, 2.02, 0, 2.4, 1.436, 0, 2.467, 1.741, 0, 2.767, -6.25, 0, 3.067, 7, 0, 3.367, -3.487, 0, 3.667, 4.657, 0, 3.933, -10.544, 0, 4.233, 17.782, 0, 4.5, -25.082, 0, 4.8, 17.656, 0, 5.133, -8.648, 0, 5.433, 1.561, 0, 5.567, 0.559, 0, 5.833, 4.917, 0, 6.1, -7.497, 0, 6.4, 4.979, 0, 6.467, 4.365, 0, 6.533, 4.664, 0, 6.833, -11.231, 0, 7.1, 11.399, 0, 7.433, -7.402, 0, 7.767, 4.907, 0, 8.167, -2.334, 0, 8.433, -0.138, 0, 8.533, -0.518, 0, 8.833, 3.629, 0, 9.2, -4.273, 0, 9.5, 0.801, 0, 9.633, 0.402, 0, 9.867, 2.501, 0, 10.4, -5.573, 0, 10.667, 6.552, 0, 10.933, -4.093, 0, 11.267, 2.099, 0, 11.467, -9.744, 0, 11.633, 14.516, 0, 11.933, -16.809, 0, 12.233, 12.98, 0, 12.867, -13.151, 0, 13.1, 15.513, 0, 13.4, -17.058, 0, 13.7, 12.784, 0, 14, -2.029, 0, 14.3, 1.762, 0, 14.567, -1.885, 0, 14.867, 0.526, 0, 15.167, -3.749, 0, 15.433, 9.88, 0, 15.7, -9.296, 0, 16, 4.805, 0, 16.3, -3.508, 0, 16.6, 3.366, 0, 17.167, -2.443, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param64", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, 12.136, 0, 0.733, -21.997, 0, 0.967, 23.973, 0, 1.233, -13.519, 0, 1.633, 3.475, 0, 1.767, 3.139, 0, 1.9, 3.397, 0, 2.567, -8.077, 0, 2.933, 7.918, 0, 3.3, 0.021, 0, 3.333, 0.044, 0, 3.733, -8.541, 0, 4.033, 14.116, 0, 4.333, -19.73, 0, 4.633, 15.327, 0, 5.033, -9.091, 0, 5.6, 9.01, 0, 5.933, -6.794, 0, 6.267, 2.376, 0, 6.633, -9.814, 0, 6.933, 11.308, 0, 7.333, -5.087, 0, 7.633, 1.917, 0, 8.067, -4.051, 0, 8.633, 6.663, 0, 9.067, -7.269, 0, 9.667, 6.432, 0, 10.2, -7.557, 0, 10.5, 5.227, 0, 10.8, -1.849, 0, 11.133, 0.657, 0, 11.367, -0.162, 0, 11.467, 14.161, 0, 11.733, -15.742, 0, 12.067, 12.422, 0, 12.667, -12.044, 0, 12.967, 11.996, 0, 13.233, -14.359, 0, 13.667, 10.348, 0, 14.433, -4.03, 0, 15.233, 9.686, 0, 15.533, -9.276, 0, 15.867, 0.506, 0, 16.067, -0.413, 0, 16.433, 5.204, 0, 17, -4.804, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param65", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -7.188, 0, 0.633, 17.052, 0, 0.867, -28.494, 0, 1.133, 28.36, 0, 1.4, -17.035, 0, 1.7, 7.67, 0, 2, -2.497, 0, 2.267, 2.02, 0, 2.4, 1.436, 0, 2.467, 1.741, 0, 2.767, -6.25, 0, 3.067, 7, 0, 3.367, -3.487, 0, 3.667, 4.657, 0, 3.933, -10.544, 0, 4.233, 17.782, 0, 4.5, -25.082, 0, 4.8, 17.656, 0, 5.133, -8.648, 0, 5.433, 1.561, 0, 5.567, 0.559, 0, 5.833, 4.917, 0, 6.1, -7.497, 0, 6.4, 4.979, 0, 6.467, 4.365, 0, 6.533, 4.664, 0, 6.833, -11.231, 0, 7.1, 11.399, 0, 7.433, -7.402, 0, 7.767, 4.907, 0, 8.167, -2.334, 0, 8.433, -0.138, 0, 8.533, -0.518, 0, 8.833, 3.629, 0, 9.2, -4.273, 0, 9.5, 0.801, 0, 9.633, 0.402, 0, 9.867, 2.501, 0, 10.4, -5.573, 0, 10.667, 6.552, 0, 10.933, -4.093, 0, 11.267, 2.099, 0, 11.467, -9.744, 0, 11.633, 14.516, 0, 11.933, -16.809, 0, 12.233, 12.98, 0, 12.867, -13.151, 0, 13.1, 15.513, 0, 13.4, -17.058, 0, 13.7, 12.784, 0, 14, -2.029, 0, 14.3, 1.762, 0, 14.567, -1.885, 0, 14.867, 0.526, 0, 15.167, -3.749, 0, 15.433, 9.88, 0, 15.7, -9.296, 0, 16, 4.805, 0, 16.3, -3.508, 0, 16.6, 3.366, 0, 17.167, -2.443, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param72", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, 12.136, 0, 0.733, -21.997, 0, 0.967, 23.973, 0, 1.233, -13.519, 0, 1.633, 3.475, 0, 1.767, 3.139, 0, 1.9, 3.397, 0, 2.567, -8.077, 0, 2.933, 7.918, 0, 3.3, 0.021, 0, 3.333, 0.044, 0, 3.733, -8.541, 0, 4.033, 14.116, 0, 4.333, -19.73, 0, 4.633, 15.327, 0, 5.033, -9.091, 0, 5.6, 9.01, 0, 5.933, -6.794, 0, 6.267, 2.376, 0, 6.633, -9.814, 0, 6.933, 11.308, 0, 7.333, -5.087, 0, 7.633, 1.917, 0, 8.067, -4.051, 0, 8.633, 6.663, 0, 9.067, -7.269, 0, 9.667, 6.432, 0, 10.2, -7.557, 0, 10.5, 5.227, 0, 10.8, -1.849, 0, 11.133, 0.657, 0, 11.367, -0.162, 0, 11.467, 14.161, 0, 11.733, -15.742, 0, 12.067, 12.422, 0, 12.667, -12.044, 0, 12.967, 21.495, 0, 13.233, -14.359, 0, 13.667, 5.04, 0, 14.433, -4.03, 0, 15.233, 9.686, 0, 15.533, -9.276, 0, 15.867, 0.506, 0, 16.067, -0.413, 0, 16.433, 5.204, 0, 17, -4.804, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param73", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -7.188, 0, 0.633, 17.052, 0, 0.867, -28.494, 0, 1.133, 28.36, 0, 1.4, -17.035, 0, 1.7, 7.67, 0, 2, -2.497, 0, 2.267, 2.02, 0, 2.4, 1.436, 0, 2.467, 1.741, 0, 2.767, -6.25, 0, 3.067, 7, 0, 3.367, -3.487, 0, 3.667, 4.657, 0, 3.933, -10.544, 0, 4.233, 17.782, 0, 4.5, -25.082, 0, 4.8, 17.656, 0, 5.133, -8.648, 0, 5.433, 1.561, 0, 5.567, 0.559, 0, 5.833, 4.917, 0, 6.1, -7.497, 0, 6.4, 4.979, 0, 6.467, 4.365, 0, 6.533, 4.664, 0, 6.833, -11.231, 0, 7.1, 11.399, 0, 7.433, -7.402, 0, 7.767, 4.907, 0, 8.167, -2.334, 0, 8.433, -0.138, 0, 8.533, -0.518, 0, 8.833, 3.629, 0, 9.2, -4.273, 0, 9.5, 0.801, 0, 9.633, 0.402, 0, 9.867, 2.501, 0, 10.4, -5.573, 0, 10.667, 6.552, 0, 10.933, -4.093, 0, 11.267, 2.099, 0, 11.467, -9.744, 0, 11.633, 14.516, 0, 11.933, -16.809, 0, 12.233, 12.98, 0, 12.5, -5.082, 0, 12.667, 1.615, 0, 12.867, -13.151, 0, 13.1, 25.012, 0, 13.4, -17.058, 0, 13.7, 7.476, 0, 14, -2.029, 0, 14.3, 1.762, 0, 14.567, -1.885, 0, 14.867, 0.526, 0, 15.167, -3.749, 0, 15.433, 9.88, 0, 15.7, -9.296, 0, 16, 4.805, 0, 16.3, -3.508, 0, 16.6, 3.366, 0, 17.167, -2.443, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param74", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, 12.136, 0, 0.733, -21.997, 0, 0.967, 23.973, 0, 1.233, -13.519, 0, 1.633, 3.475, 0, 1.767, 3.139, 0, 1.9, 3.397, 0, 2.567, -8.077, 0, 2.933, 7.918, 0, 3.3, 0.021, 0, 3.333, 0.044, 0, 3.733, -8.541, 0, 4.033, 14.116, 0, 4.333, -19.73, 0, 4.633, 15.327, 0, 5.033, -9.091, 0, 5.6, 9.01, 0, 5.933, -6.794, 0, 6.267, 2.376, 0, 6.633, -9.814, 0, 6.933, 11.308, 0, 7.333, -5.087, 0, 7.633, 1.917, 0, 8.067, -4.051, 0, 8.633, 6.663, 0, 9.067, -7.269, 0, 9.667, 6.432, 0, 10.2, -7.557, 0, 10.5, 5.227, 0, 10.8, -1.849, 0, 11.133, 0.657, 0, 11.367, -0.162, 0, 11.467, 14.161, 0, 11.733, -15.742, 0, 12.067, 12.422, 0, 12.667, -12.044, 0, 12.967, 21.495, 0, 13.233, -14.359, 0, 13.667, 5.04, 0, 14.433, -4.03, 0, 15.233, 9.686, 0, 15.533, -9.276, 0, 15.867, 0.506, 0, 16.067, -0.413, 0, 16.433, 5.204, 0, 17, -4.804, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param75", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -7.188, 0, 0.633, 17.052, 0, 0.867, -28.494, 0, 1.133, 28.36, 0, 1.4, -17.035, 0, 1.7, 7.67, 0, 2, -2.497, 0, 2.267, 2.02, 0, 2.4, 1.436, 0, 2.467, 1.741, 0, 2.767, -6.25, 0, 3.067, 7, 0, 3.367, -3.487, 0, 3.667, 4.657, 0, 3.933, -10.544, 0, 4.233, 17.782, 0, 4.5, -25.082, 0, 4.8, 17.656, 0, 5.133, -8.648, 0, 5.433, 1.561, 0, 5.567, 0.559, 0, 5.833, 4.917, 0, 6.1, -7.497, 0, 6.4, 4.979, 0, 6.467, 4.365, 0, 6.533, 4.664, 0, 6.833, -11.231, 0, 7.1, 11.399, 0, 7.433, -7.402, 0, 7.767, 4.907, 0, 8.167, -2.334, 0, 8.433, -0.138, 0, 8.533, -0.518, 0, 8.833, 3.629, 0, 9.2, -4.273, 0, 9.5, 0.801, 0, 9.633, 0.402, 0, 9.867, 2.501, 0, 10.4, -5.573, 0, 10.667, 6.552, 0, 10.933, -4.093, 0, 11.267, 2.099, 0, 11.467, -9.744, 0, 11.633, 14.516, 0, 11.933, -16.809, 0, 12.233, 12.98, 0, 12.5, -5.082, 0, 12.667, 1.615, 0, 12.867, -13.151, 0, 13.1, 25.012, 0, 13.4, -17.058, 0, 13.7, 7.476, 0, 14, -2.029, 0, 14.3, 1.762, 0, 14.567, -1.885, 0, 14.867, 0.526, 0, 15.167, -3.749, 0, 15.433, 9.88, 0, 15.7, -9.296, 0, 16, 4.805, 0, 16.3, -3.508, 0, 16.6, 3.366, 0, 17.167, -2.443, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param53", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, 12.136, 0, 0.733, -21.997, 0, 0.967, 23.973, 0, 1.233, -13.519, 0, 1.633, 3.475, 0, 1.767, 3.139, 0, 1.9, 3.397, 0, 2.567, -8.077, 0, 2.933, 7.918, 0, 3.3, 0.021, 0, 3.333, 0.044, 0, 3.733, -8.541, 0, 4.033, 14.116, 0, 4.333, -19.73, 0, 4.633, 15.327, 0, 5.033, -9.091, 0, 5.6, 9.01, 0, 5.933, -6.794, 0, 6.267, 2.376, 0, 6.633, -9.814, 0, 6.933, 11.308, 0, 7.333, -5.087, 0, 7.633, 1.917, 0, 8.067, -4.051, 0, 8.633, 6.663, 0, 9.067, -7.269, 0, 9.667, 6.432, 0, 10.2, -7.557, 0, 10.5, 5.227, 0, 10.8, -1.849, 0, 11.133, 0.657, 0, 11.367, -0.162, 0, 11.467, 14.161, 0, 11.733, -15.742, 0, 12.067, 12.422, 0, 12.667, -12.044, 0, 12.967, 21.495, 0, 13.233, -14.359, 0, 13.667, 5.04, 0, 14.433, -4.03, 0, 15.233, 9.686, 0, 15.533, -9.276, 0, 15.867, 0.506, 0, 16.067, -0.413, 0, 16.433, 5.204, 0, 17, -4.804, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param54", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -7.188, 0, 0.633, 17.052, 0, 0.867, -28.494, 0, 1.133, 28.36, 0, 1.4, -17.035, 0, 1.7, 7.67, 0, 2, -2.497, 0, 2.267, 2.02, 0, 2.4, 1.436, 0, 2.467, 1.741, 0, 2.767, -6.25, 0, 3.067, 7, 0, 3.367, -3.487, 0, 3.667, 4.657, 0, 3.933, -10.544, 0, 4.233, 17.782, 0, 4.5, -25.082, 0, 4.8, 17.656, 0, 5.133, -8.648, 0, 5.433, 1.561, 0, 5.567, 0.559, 0, 5.833, 4.917, 0, 6.1, -7.497, 0, 6.4, 4.979, 0, 6.467, 4.365, 0, 6.533, 4.664, 0, 6.833, -11.231, 0, 7.1, 11.399, 0, 7.433, -7.402, 0, 7.767, 4.907, 0, 8.167, -2.334, 0, 8.433, -0.138, 0, 8.533, -0.518, 0, 8.833, 3.629, 0, 9.2, -4.273, 0, 9.5, 0.801, 0, 9.633, 0.402, 0, 9.867, 2.501, 0, 10.4, -5.573, 0, 10.667, 6.552, 0, 10.933, -4.093, 0, 11.267, 2.099, 0, 11.467, -9.744, 0, 11.633, 14.516, 0, 11.933, -16.809, 0, 12.233, 12.98, 0, 12.5, -5.082, 0, 12.667, 1.615, 0, 12.867, -13.151, 0, 13.1, 25.012, 0, 13.4, -17.058, 0, 13.7, 7.476, 0, 14, -2.029, 0, 14.3, 1.762, 0, 14.567, -1.885, 0, 14.867, 0.526, 0, 15.167, -3.749, 0, 15.433, 9.88, 0, 15.7, -9.296, 0, 16, 4.805, 0, 16.3, -3.508, 0, 16.6, 3.366, 0, 17.167, -2.443, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param81", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, 12.136, 0, 0.733, -21.997, 0, 0.967, 23.973, 0, 1.233, -13.519, 0, 1.633, 3.475, 0, 1.767, 3.139, 0, 1.9, 3.397, 0, 2.567, -8.077, 0, 2.933, 7.918, 0, 3.3, 0.021, 0, 3.333, 0.044, 0, 3.733, -8.541, 0, 4.033, 14.116, 0, 4.333, -19.73, 0, 4.633, 15.327, 0, 5.033, -9.091, 0, 5.6, 9.01, 0, 5.933, -6.794, 0, 6.267, 2.376, 0, 6.633, -9.814, 0, 6.933, 11.308, 0, 7.333, -5.087, 0, 7.633, 1.917, 0, 8.067, -4.051, 0, 8.633, 6.663, 0, 9.067, -7.269, 0, 9.667, 6.432, 0, 10.2, -7.557, 0, 10.5, 5.227, 0, 10.8, -1.849, 0, 11.133, 0.657, 0, 11.367, -0.162, 0, 11.467, 14.161, 0, 11.733, -15.742, 0, 12.067, 12.422, 0, 12.667, -12.044, 0, 12.967, 21.495, 0, 13.233, -14.359, 0, 13.667, 5.04, 0, 14.433, -4.03, 0, 15.233, 9.686, 0, 15.533, -9.276, 0, 15.867, 0.506, 0, 16.067, -0.413, 0, 16.433, 5.204, 0, 17, -4.804, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param82", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -7.188, 0, 0.633, 17.052, 0, 0.867, -28.494, 0, 1.133, 28.36, 0, 1.4, -17.035, 0, 1.7, 7.67, 0, 2, -2.497, 0, 2.267, 2.02, 0, 2.4, 1.436, 0, 2.467, 1.741, 0, 2.767, -6.25, 0, 3.067, 7, 0, 3.367, -3.487, 0, 3.667, 4.657, 0, 3.933, -10.544, 0, 4.233, 17.782, 0, 4.5, -25.082, 0, 4.8, 17.656, 0, 5.133, -8.648, 0, 5.433, 1.561, 0, 5.567, 0.559, 0, 5.833, 4.917, 0, 6.1, -7.497, 0, 6.4, 4.979, 0, 6.467, 4.365, 0, 6.533, 4.664, 0, 6.833, -11.231, 0, 7.1, 11.399, 0, 7.433, -7.402, 0, 7.767, 4.907, 0, 8.167, -2.334, 0, 8.433, -0.138, 0, 8.533, -0.518, 0, 8.833, 3.629, 0, 9.2, -4.273, 0, 9.5, 0.801, 0, 9.633, 0.402, 0, 9.867, 2.501, 0, 10.4, -5.573, 0, 10.667, 6.552, 0, 10.933, -4.093, 0, 11.267, 2.099, 0, 11.467, -9.744, 0, 11.633, 14.516, 0, 11.933, -16.809, 0, 12.233, 12.98, 0, 12.5, -5.082, 0, 12.667, 1.615, 0, 12.867, -13.151, 0, 13.1, 25.012, 0, 13.4, -17.058, 0, 13.7, 7.476, 0, 14, -2.029, 0, 14.3, 1.762, 0, 14.567, -1.885, 0, 14.867, 0.526, 0, 15.167, -3.749, 0, 15.433, 9.88, 0, 15.7, -9.296, 0, 16, 4.805, 0, 16.3, -3.508, 0, 16.6, 3.366, 0, 17.167, -2.443, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param70", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, 12.136, 0, 0.733, -21.997, 0, 0.967, 23.973, 0, 1.233, -13.519, 0, 1.633, 3.475, 0, 1.767, 3.139, 0, 1.9, 3.397, 0, 2.567, -8.077, 0, 2.933, 7.918, 0, 3.3, 0.021, 0, 3.333, 0.044, 0, 3.733, -8.541, 0, 4.033, 14.116, 0, 4.333, -19.73, 0, 4.633, 15.327, 0, 5.033, -9.091, 0, 5.6, 9.01, 0, 5.933, -6.794, 0, 6.267, 2.376, 0, 6.633, -9.814, 0, 6.933, 11.308, 0, 7.333, -5.087, 0, 7.633, 1.917, 0, 8.067, -4.051, 0, 8.633, 6.663, 0, 9.067, -7.269, 0, 9.667, 6.432, 0, 10.2, -7.557, 0, 10.5, 5.227, 0, 10.8, -1.849, 0, 11.133, 0.657, 0, 11.367, -0.162, 0, 11.467, 14.161, 0, 11.733, -15.742, 0, 12.067, 12.422, 0, 12.667, -12.044, 0, 12.967, 21.495, 0, 13.233, -14.359, 0, 13.667, 5.04, 0, 14.433, -4.03, 0, 15.233, 9.686, 0, 15.533, -9.276, 0, 15.867, 0.506, 0, 16.067, -0.413, 0, 16.433, 5.204, 0, 17, -4.804, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param71", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -7.188, 0, 0.633, 17.052, 0, 0.867, -28.494, 0, 1.133, 28.36, 0, 1.4, -17.035, 0, 1.7, 7.67, 0, 2, -2.497, 0, 2.267, 2.02, 0, 2.4, 1.436, 0, 2.467, 1.741, 0, 2.767, -6.25, 0, 3.067, 7, 0, 3.367, -3.487, 0, 3.667, 4.657, 0, 3.933, -10.544, 0, 4.233, 17.782, 0, 4.5, -25.082, 0, 4.8, 17.656, 0, 5.133, -8.648, 0, 5.433, 1.561, 0, 5.567, 0.559, 0, 5.833, 4.917, 0, 6.1, -7.497, 0, 6.4, 4.979, 0, 6.467, 4.365, 0, 6.533, 4.664, 0, 6.833, -11.231, 0, 7.1, 11.399, 0, 7.433, -7.402, 0, 7.767, 4.907, 0, 8.167, -2.334, 0, 8.433, -0.138, 0, 8.533, -0.518, 0, 8.833, 3.629, 0, 9.2, -4.273, 0, 9.5, 0.801, 0, 9.633, 0.402, 0, 9.867, 2.501, 0, 10.4, -5.573, 0, 10.667, 6.552, 0, 10.933, -4.093, 0, 11.267, 2.099, 0, 11.467, -9.744, 0, 11.633, 14.516, 0, 11.933, -16.809, 0, 12.233, 12.98, 0, 12.5, -5.082, 0, 12.667, 1.615, 0, 12.867, -13.151, 0, 13.1, 25.012, 0, 13.4, -17.058, 0, 13.7, 7.476, 0, 14, -2.029, 0, 14.3, 1.762, 0, 14.567, -1.885, 0, 14.867, 0.526, 0, 15.167, -3.749, 0, 15.433, 9.88, 0, 15.7, -9.296, 0, 16, 4.805, 0, 16.3, -3.508, 0, 16.6, 3.366, 0, 17.167, -2.443, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param77", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, 12.136, 0, 0.733, -21.997, 0, 0.967, 23.973, 0, 1.233, -13.519, 0, 1.633, 3.475, 0, 1.767, 3.139, 0, 1.9, 3.397, 0, 2.567, -8.077, 0, 2.933, 7.918, 0, 3.3, 0.021, 0, 3.333, 0.044, 0, 3.733, -8.541, 0, 4.033, 14.116, 0, 4.333, -19.73, 0, 4.633, 15.327, 0, 5.033, -9.091, 0, 5.6, 9.01, 0, 5.933, -6.794, 0, 6.267, 2.376, 0, 6.633, -9.814, 0, 6.933, 11.308, 0, 7.333, -5.087, 0, 7.633, 1.917, 0, 8.067, -4.051, 0, 8.633, 6.663, 0, 9.067, -7.269, 0, 9.667, 6.432, 0, 10.2, -7.557, 0, 10.5, 5.227, 0, 10.8, -1.849, 0, 11.133, 0.657, 0, 11.367, -0.162, 0, 11.467, 14.161, 0, 11.733, -15.742, 0, 12.067, 12.422, 0, 12.667, -12.044, 0, 12.967, 21.495, 0, 13.233, -14.359, 0, 13.667, 5.04, 0, 14.433, -4.03, 0, 15.233, 9.686, 0, 15.533, -9.276, 0, 15.867, 0.506, 0, 16.067, -0.413, 0, 16.433, 5.204, 0, 17, -4.804, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param78", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -7.188, 0, 0.633, 17.052, 0, 0.867, -28.494, 0, 1.133, 28.36, 0, 1.4, -17.035, 0, 1.7, 7.67, 0, 2, -2.497, 0, 2.267, 2.02, 0, 2.4, 1.436, 0, 2.467, 1.741, 0, 2.767, -6.25, 0, 3.067, 7, 0, 3.367, -3.487, 0, 3.667, 4.657, 0, 3.933, -10.544, 0, 4.233, 17.782, 0, 4.5, -25.082, 0, 4.8, 17.656, 0, 5.133, -8.648, 0, 5.433, 1.561, 0, 5.567, 0.559, 0, 5.833, 4.917, 0, 6.1, -7.497, 0, 6.4, 4.979, 0, 6.467, 4.365, 0, 6.533, 4.664, 0, 6.833, -11.231, 0, 7.1, 11.399, 0, 7.433, -7.402, 0, 7.767, 4.907, 0, 8.167, -2.334, 0, 8.433, -0.138, 0, 8.533, -0.518, 0, 8.833, 3.629, 0, 9.2, -4.273, 0, 9.5, 0.801, 0, 9.633, 0.402, 0, 9.867, 2.501, 0, 10.4, -5.573, 0, 10.667, 6.552, 0, 10.933, -4.093, 0, 11.267, 2.099, 0, 11.467, -9.744, 0, 11.633, 14.516, 0, 11.933, -16.809, 0, 12.233, 12.98, 0, 12.5, -5.082, 0, 12.667, 1.615, 0, 12.867, -13.151, 0, 13.1, 25.012, 0, 13.4, -17.058, 0, 13.7, 7.476, 0, 14, -2.029, 0, 14.3, 1.762, 0, 14.567, -1.885, 0, 14.867, 0.526, 0, 15.167, -3.749, 0, 15.433, 9.88, 0, 15.7, -9.296, 0, 16, 4.805, 0, 16.3, -3.508, 0, 16.6, 3.366, 0, 17.167, -2.443, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param79", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, 12.136, 0, 0.733, -21.997, 0, 0.967, 23.973, 0, 1.233, -13.519, 0, 1.633, 3.475, 0, 1.767, 3.139, 0, 1.9, 3.397, 0, 2.567, -8.077, 0, 2.933, 7.918, 0, 3.3, 0.021, 0, 3.333, 0.044, 0, 3.733, -8.541, 0, 4.033, 14.116, 0, 4.333, -19.73, 0, 4.633, 15.327, 0, 5.033, -9.091, 0, 5.6, 9.01, 0, 5.933, -6.794, 0, 6.267, 2.376, 0, 6.633, -9.814, 0, 6.933, 11.308, 0, 7.333, -5.087, 0, 7.633, 1.917, 0, 8.067, -4.051, 0, 8.633, 6.663, 0, 9.067, -7.269, 0, 9.667, 6.432, 0, 10.2, -7.557, 0, 10.5, 5.227, 0, 10.8, -1.849, 0, 11.133, 0.657, 0, 11.367, -0.162, 0, 11.467, 14.161, 0, 11.733, -15.742, 0, 12.067, 12.422, 0, 12.667, -12.044, 0, 12.967, 21.495, 0, 13.233, -14.359, 0, 13.667, 5.04, 0, 14.433, -4.03, 0, 15.233, 9.686, 0, 15.533, -9.276, 0, 15.867, 0.506, 0, 16.067, -0.413, 0, 16.433, 5.204, 0, 17, -4.804, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param80", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -7.188, 0, 0.633, 17.052, 0, 0.867, -28.494, 0, 1.133, 28.36, 0, 1.4, -17.035, 0, 1.7, 7.67, 0, 2, -2.497, 0, 2.267, 2.02, 0, 2.4, 1.436, 0, 2.467, 1.741, 0, 2.767, -6.25, 0, 3.067, 7, 0, 3.367, -3.487, 0, 3.667, 4.657, 0, 3.933, -10.544, 0, 4.233, 17.782, 0, 4.5, -25.082, 0, 4.8, 17.656, 0, 5.133, -8.648, 0, 5.433, 1.561, 0, 5.567, 0.559, 0, 5.833, 4.917, 0, 6.1, -7.497, 0, 6.4, 4.979, 0, 6.467, 4.365, 0, 6.533, 4.664, 0, 6.833, -11.231, 0, 7.1, 11.399, 0, 7.433, -7.402, 0, 7.767, 4.907, 0, 8.167, -2.334, 0, 8.433, -0.138, 0, 8.533, -0.518, 0, 8.833, 3.629, 0, 9.2, -4.273, 0, 9.5, 0.801, 0, 9.633, 0.402, 0, 9.867, 2.501, 0, 10.4, -5.573, 0, 10.667, 6.552, 0, 10.933, -4.093, 0, 11.267, 2.099, 0, 11.467, -9.744, 0, 11.633, 14.516, 0, 11.933, -16.809, 0, 12.233, 12.98, 0, 12.5, -5.082, 0, 12.667, 1.615, 0, 12.867, -13.151, 0, 13.1, 25.012, 0, 13.4, -17.058, 0, 13.7, 7.476, 0, 14, -2.029, 0, 14.3, 1.762, 0, 14.567, -1.885, 0, 14.867, 0.526, 0, 15.167, -3.749, 0, 15.433, 9.88, 0, 15.7, -9.296, 0, 16, 4.805, 0, 16.3, -3.508, 0, 16.6, 3.366, 0, 17.167, -2.443, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param294", "Segments": [0, 1, 2, 0.333, 1, 0, 0.4, 0, 2, 17.133, 0, 0, 17.233, 1]}, {"Target": "Parameter", "Id": "Param47", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, 11.537, 0, 0.733, -21.032, 0, 0.967, 24.716, 0, 1.233, -18.384, 0, 1.533, 10.391, 0, 1.9, -1.366, 0, 2.167, 0.86, 0, 2.6, -6.878, 0, 2.933, 7.909, 0, 3.733, -11.63, 0, 4.033, 13.976, 0, 4.333, -17.496, 0, 4.633, 18.548, 0, 5, -11.571, 0, 5.433, 2.995, 0, 5.933, -1.737, 0, 6.167, -0.629, 0, 6.6, -7.504, 0, 6.967, 13.168, 0, 7.367, -7.683, 0, 7.633, 2.321, 0, 8.067, -6.125, 0, 8.6, 8.525, 0, 9.067, -9.264, 0, 9.7, 11.285, 0, 10.1, -8.997, 0, 10.467, 3.357, 0, 10.767, -1.228, 0, 11.067, 0.439, 0, 11.367, -0.133, 0, 11.467, 7.916, 0, 11.733, -20.184, 0, 12.067, 19.085, 0, 12.433, -8.479, 0, 12.533, -5.314, 0, 12.667, -7.442, 0, 12.967, 15.905, 0, 13.233, -10.662, 0, 13.6, 3.394, 0, 13.667, 3.342, 0, 13.8, 3.627, 0, 14.467, -5.765, 0, 15, 4.392, 0, 15.033, 4.349, 0, 15.233, 15.458, 0, 15.533, -14.149, 0, 15.867, 2.086, 0, 16.1, -0.961, 0, 16.4, 3.186, 0, 17.033, -4.13, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param48", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -6.866, 0, 0.633, 16.373, 0, 0.867, -27.663, 0, 1.133, 30, 0, 1.433, -22.968, 0, 1.7, 14.281, 0, 2, -6.848, 0, 2.333, 3.329, 0, 2.767, -6.216, 0, 3.067, 6.16, 0, 3.367, -2.559, 0, 3.667, 5.161, 0, 3.9, -12.714, 0, 4.2, 17.35, 0, 4.5, -23.733, 0, 4.8, 18.818, 0, 5.133, -13.073, 0, 5.467, 6.118, 0, 5.767, -1.46, 0, 5.933, -0.024, 0, 6.1, -0.557, 0, 6.367, 1.054, 0, 6.433, 0.903, 0, 6.533, 1.961, 0, 6.833, -7.864, 0, 7.133, 10.075, 0, 7.5, -8.923, 0, 7.8, 6.447, 0, 8.167, -3.861, 0, 8.467, 0.025, 0, 8.533, -0.103, 0, 8.833, 4.17, 0, 9.233, -5.058, 0, 9.5, 0.639, 0, 9.633, -0.72, 0, 9.9, 5.418, 0, 10.267, -8.057, 0, 10.567, 5.829, 0, 10.9, -3.267, 0, 11.2, 1.602, 0, 11.467, -6.176, 0, 11.633, 12.788, 0, 11.933, -20.07, 0, 12.233, 18.959, 0, 12.533, -11.723, 0, 12.733, 1.282, 0, 12.9, -6.785, 0, 13.1, 16.816, 0, 13.4, -12.999, 0, 13.667, 6.037, 0, 13.967, -1.544, 0, 14.267, 1.591, 0, 14.633, -2.208, 0, 14.833, -0.204, 0, 14.967, -1.289, 0, 15.067, 0.158, 0, 15.2, -3.84, 0, 15.433, 14.771, 0, 15.7, -14.478, 0, 15.967, 8.111, 0, 16.267, -4.758, 0, 16.567, 3.035, 0, 16.833, -0.758, 0, 17, 0.366, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param56", "Segments": [0, 0, 2, 0.333, 0, 1, 0.344, 0, 0.356, -0.113, 0.367, 0.106, 1, 0.411, 0.979, 0.456, 3.562, 0.5, 3.562, 0, 0.767, -6.936, 0, 1.033, 6.019, 0, 1.4, -4.92, 0, 1.867, 3.994, 0, 2.6, -3.587, 0, 3.1, 5.425, 0, 3.767, -7.356, 0, 4.133, 7.029, 0, 4.433, -3.636, 0, 4.8, 5.733, 0, 5.233, -5.721, 0, 5.733, 4.219, 0, 6.267, -2.843, 0, 6.467, -1.982, 0, 6.633, -2.399, 0, 7.067, 7.229, 0, 7.533, -5.373, 0, 7.967, 0.335, 0, 8.267, -1.151, 0, 8.733, 5.291, 0, 9.233, -7.375, 0, 9.767, 9.65, 0, 10.267, -7.883, 0, 10.767, 3.887, 0, 11.3, -1.86, 0, 11.5, 1.65, 0, 11.833, -7.668, 0, 12.233, 9.506, 0, 12.733, -9.039, 0, 13.067, 5.283, 0, 13.533, -3.14, 0, 14, 3.943, 0, 14.567, -5.28, 0, 15.267, 9.087, 0, 15.7, -8.377, 0, 16.3, 4.813, 0, 17, -2.956, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param57", "Segments": [0, 0, 2, 0.333, 0, 0, 0.367, 0.076, 0, 0.5, -2.433, 0, 0.7, 7.048, 0, 0.933, -11.551, 0, 1.233, 10.271, 0, 1.533, -7.907, 0, 1.967, 3.55, 0, 2.4, -0.293, 0, 2.533, -0.07, 0, 2.867, -4.314, 0, 3.3, 4.53, 0, 3.967, -8.572, 0, 4.3, 9.8, 0, 4.6, -8.289, 0, 4.967, 9.007, 0, 5.4, -5.422, 0, 5.9, 3.562, 0, 6.4, -1.619, 0, 6.6, -0.228, 0, 6.867, -4.726, 0, 7.3, 7.784, 0, 7.7, -4.23, 0, 8.067, 1.056, 0, 8.5, -3.064, 0, 8.967, 5.968, 0, 9.433, -6.613, 0, 10, 8.814, 0, 10.433, -6.44, 0, 10.933, 2.613, 0, 11.467, -3.192, 0, 11.7, 5.398, 0, 12, -11.674, 0, 12.4, 10.159, 0, 12.9, -10.426, 0, 13.233, 7.293, 0, 13.667, -3.546, 0, 14.2, 3.672, 0, 14.8, -4.111, 0, 15.467, 10.632, 0, 15.867, -6.869, 0, 16.533, 2.654, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param58", "Segments": [0, 0, 2, 0.333, 0, 0, 0.367, 0.04, 0, 0.567, -2.495, 0, 0.8, 8.815, 0, 1.067, -15.241, 0, 1.333, 16.237, 0, 1.667, -13.732, 0, 2, 7.956, 0, 2.367, -2.379, 0, 2.667, 1.183, 0, 2.967, -4.484, 0, 3.333, 5.167, 0, 3.667, -0.356, 0, 3.8, 0.143, 0, 4.067, -8.413, 0, 4.4, 13.453, 0, 4.7, -14.278, 0, 5.067, 13.341, 0, 5.433, -9.341, 0, 5.867, 4.575, 0, 6.367, -2.285, 0, 6.7, 1.737, 0, 7, -5.519, 0, 7.367, 8.877, 0, 7.733, -7.387, 0, 8.133, 4.447, 0, 8.533, -3.995, 0, 9.033, 6.26, 0, 9.467, -6.936, 0, 10.067, 7.871, 0, 10.5, -8.505, 0, 10.9, 4.879, 0, 11.533, -2.825, 0, 11.8, 7.993, 0, 12.1, -13.964, 0, 12.467, 14.16, 0, 12.967, -11.156, 0, 13.333, 12.114, 0, 13.7, -7.456, 0, 14.2, 3.328, 0, 14.8, -3.309, 0, 15.167, -0.381, 0, 15.267, -0.653, 0, 15.567, 9.855, 0, 15.933, -9.958, 0, 16.3, 3.869, 0, 17, -0.552, 1, 17.033, -0.552, 17.067, -0.567, 17.1, -0.467, 1, 17.144, -0.334, 17.189, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param59", "Segments": [0, 0, 2, 0.333, 0, 1, 0.344, 0, 0.356, -0.113, 0.367, 0.106, 1, 0.411, 0.979, 0.456, 3.562, 0.5, 3.562, 0, 0.767, -6.936, 0, 1.033, 6.019, 0, 1.4, -4.92, 0, 1.867, 3.994, 0, 2.6, -3.587, 0, 3.1, 5.425, 0, 3.767, -7.356, 0, 4.133, 7.029, 0, 4.433, -3.636, 0, 4.8, 5.733, 0, 5.233, -5.721, 0, 5.733, 4.219, 0, 6.267, -2.843, 0, 6.467, -1.982, 0, 6.633, -2.399, 0, 7.067, 7.229, 0, 7.533, -5.373, 0, 7.967, 0.335, 0, 8.267, -1.151, 0, 8.733, 5.291, 0, 9.233, -7.375, 0, 9.767, 9.65, 0, 10.267, -7.883, 0, 10.767, 3.887, 0, 11.3, -1.86, 0, 11.5, 1.65, 0, 11.833, -7.668, 0, 12.233, 9.506, 0, 12.733, -9.039, 0, 13.067, 5.283, 0, 13.533, -3.14, 0, 14, 3.943, 0, 14.567, -5.28, 0, 15.267, 9.087, 0, 15.7, -8.377, 0, 16.3, 4.813, 0, 17, -2.956, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param60", "Segments": [0, 0, 2, 0.333, 0, 0, 0.367, 0.076, 0, 0.5, -2.433, 0, 0.7, 7.048, 0, 0.933, -11.551, 0, 1.233, 10.271, 0, 1.533, -7.907, 0, 1.967, 3.55, 0, 2.4, -0.293, 0, 2.533, -0.07, 0, 2.867, -4.314, 0, 3.3, 4.53, 0, 3.967, -8.572, 0, 4.3, 9.8, 0, 4.6, -8.289, 0, 4.967, 9.007, 0, 5.4, -5.422, 0, 5.9, 3.562, 0, 6.4, -1.619, 0, 6.6, -0.228, 0, 6.867, -4.726, 0, 7.3, 7.784, 0, 7.7, -4.23, 0, 8.067, 1.056, 0, 8.5, -3.064, 0, 8.967, 5.968, 0, 9.433, -6.613, 0, 10, 8.814, 0, 10.433, -6.44, 0, 10.933, 2.613, 0, 11.467, -3.192, 0, 11.7, 5.398, 0, 12, -11.674, 0, 12.4, 10.159, 0, 12.9, -10.426, 0, 13.233, 7.293, 0, 13.667, -3.546, 0, 14.2, 3.672, 0, 14.8, -4.111, 0, 15.467, 10.632, 0, 15.867, -6.869, 0, 16.533, 2.654, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param61", "Segments": [0, 0, 2, 0.333, 0, 0, 0.367, 0.04, 0, 0.567, -2.495, 0, 0.8, 8.815, 0, 1.067, -15.241, 0, 1.333, 16.237, 0, 1.667, -13.732, 0, 2, 7.956, 0, 2.367, -2.379, 0, 2.667, 1.183, 0, 2.967, -4.484, 0, 3.333, 5.167, 0, 3.667, -0.356, 0, 3.8, 0.143, 0, 4.067, -8.413, 0, 4.4, 13.453, 0, 4.7, -14.278, 0, 5.067, 13.341, 0, 5.433, -9.341, 0, 5.867, 4.575, 0, 6.367, -2.285, 0, 6.7, 1.737, 0, 7, -5.519, 0, 7.367, 8.877, 0, 7.733, -7.387, 0, 8.133, 4.447, 0, 8.533, -3.995, 0, 9.033, 6.26, 0, 9.467, -6.936, 0, 10.067, 7.871, 0, 10.5, -8.505, 0, 10.9, 4.879, 0, 11.533, -2.825, 0, 11.8, 7.993, 0, 12.1, -13.964, 0, 12.467, 14.16, 0, 12.967, -11.156, 0, 13.333, 12.114, 0, 13.7, -7.456, 0, 14.2, 3.328, 0, 14.8, -3.309, 0, 15.167, -0.381, 0, 15.267, -0.653, 0, 15.567, 9.855, 0, 15.933, -9.958, 0, 16.3, 3.869, 0, 17, -0.552, 1, 17.033, -0.552, 17.067, -0.567, 17.1, -0.467, 1, 17.144, -0.334, 17.189, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param49", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, 11.537, 0, 0.733, -21.032, 0, 0.967, 24.716, 0, 1.233, -18.384, 0, 1.533, 10.391, 0, 1.9, -1.366, 0, 2.167, 0.86, 0, 2.6, -6.878, 0, 2.933, 7.909, 0, 3.733, -11.63, 0, 4.033, 13.976, 0, 4.333, -17.496, 0, 4.633, 18.548, 0, 5, -11.571, 0, 5.433, 2.995, 0, 5.933, -1.737, 0, 6.167, -0.629, 0, 6.6, -7.504, 0, 6.967, 13.168, 0, 7.367, -7.683, 0, 7.633, 2.321, 0, 8.067, -6.125, 0, 8.6, 8.525, 0, 9.067, -9.264, 0, 9.7, 11.285, 0, 10.1, -8.997, 0, 10.467, 3.357, 0, 10.767, -1.228, 0, 11.067, 0.439, 0, 11.367, -0.133, 0, 11.467, 7.916, 0, 11.733, -20.184, 0, 12.067, 19.085, 0, 12.433, -8.479, 0, 12.533, -5.314, 0, 12.667, -7.442, 0, 12.967, 15.905, 0, 13.233, -10.662, 0, 13.6, 3.394, 0, 13.667, 3.342, 0, 13.8, 3.627, 0, 14.467, -5.765, 0, 15, 4.392, 0, 15.033, 4.349, 0, 15.233, 15.458, 0, 15.533, -14.149, 0, 15.867, 2.086, 0, 16.1, -0.961, 0, 16.4, 3.186, 0, 17.033, -4.13, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param50", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -6.866, 0, 0.633, 16.373, 0, 0.867, -27.663, 0, 1.133, 30, 0, 1.433, -22.968, 0, 1.7, 14.281, 0, 2, -6.848, 0, 2.333, 3.329, 0, 2.767, -6.216, 0, 3.067, 6.16, 0, 3.367, -2.559, 0, 3.667, 5.161, 0, 3.9, -12.714, 0, 4.2, 17.35, 0, 4.5, -23.733, 0, 4.8, 18.818, 0, 5.133, -13.073, 0, 5.467, 6.118, 0, 5.767, -1.46, 0, 5.933, -0.024, 0, 6.1, -0.557, 0, 6.367, 1.054, 0, 6.433, 0.903, 0, 6.533, 1.961, 0, 6.833, -7.864, 0, 7.133, 10.075, 0, 7.5, -8.923, 0, 7.8, 6.447, 0, 8.167, -3.861, 0, 8.467, 0.025, 0, 8.533, -0.103, 0, 8.833, 4.17, 0, 9.233, -5.058, 0, 9.5, 0.639, 0, 9.633, -0.72, 0, 9.9, 5.418, 0, 10.267, -8.057, 0, 10.567, 5.829, 0, 10.9, -3.267, 0, 11.2, 1.602, 0, 11.467, -6.176, 0, 11.633, 12.788, 0, 11.933, -20.07, 0, 12.233, 18.959, 0, 12.533, -11.723, 0, 12.733, 1.282, 0, 12.9, -6.785, 0, 13.1, 16.816, 0, 13.4, -12.999, 0, 13.667, 6.037, 0, 13.967, -1.544, 0, 14.267, 1.591, 0, 14.633, -2.208, 0, 14.833, -0.204, 0, 14.967, -1.289, 0, 15.067, 0.158, 0, 15.2, -3.84, 0, 15.433, 14.771, 0, 15.7, -14.478, 0, 15.967, 8.111, 0, 16.267, -4.758, 0, 16.567, 3.035, 0, 16.833, -0.758, 0, 17, 0.366, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param51", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, 11.537, 0, 0.733, -21.032, 0, 0.967, 24.716, 0, 1.233, -18.384, 0, 1.533, 10.391, 0, 1.9, -1.366, 0, 2.167, 0.86, 0, 2.6, -6.878, 0, 2.933, 7.909, 0, 3.733, -11.63, 0, 4.033, 13.976, 0, 4.333, -17.496, 0, 4.633, 18.548, 0, 5, -11.571, 0, 5.433, 2.995, 0, 5.933, -1.737, 0, 6.167, -0.629, 0, 6.6, -7.504, 0, 6.967, 13.168, 0, 7.367, -7.683, 0, 7.633, 2.321, 0, 8.067, -6.125, 0, 8.6, 8.525, 0, 9.067, -9.264, 0, 9.7, 11.285, 0, 10.1, -8.997, 0, 10.467, 3.357, 0, 10.767, -1.228, 0, 11.067, 0.439, 0, 11.367, -0.133, 0, 11.467, 7.916, 0, 11.733, -20.184, 0, 12.067, 19.085, 0, 12.433, -8.479, 0, 12.533, -5.314, 0, 12.667, -7.442, 0, 12.967, 15.905, 0, 13.233, -10.662, 0, 13.6, 3.394, 0, 13.667, 3.342, 0, 13.8, 3.627, 0, 14.467, -5.765, 0, 15, 4.392, 0, 15.033, 4.349, 0, 15.233, 15.458, 0, 15.533, -14.149, 0, 15.867, 2.086, 0, 16.1, -0.961, 0, 16.4, 3.186, 0, 17.033, -4.13, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param52", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -6.866, 0, 0.633, 16.373, 0, 0.867, -27.663, 0, 1.133, 30, 0, 1.433, -22.968, 0, 1.7, 14.281, 0, 2, -6.848, 0, 2.333, 3.329, 0, 2.767, -6.216, 0, 3.067, 6.16, 0, 3.367, -2.559, 0, 3.667, 5.161, 0, 3.9, -12.714, 0, 4.2, 17.35, 0, 4.5, -23.733, 0, 4.8, 18.818, 0, 5.133, -13.073, 0, 5.467, 6.118, 0, 5.767, -1.46, 0, 5.933, -0.024, 0, 6.1, -0.557, 0, 6.367, 1.054, 0, 6.433, 0.903, 0, 6.533, 1.961, 0, 6.833, -7.864, 0, 7.133, 10.075, 0, 7.5, -8.923, 0, 7.8, 6.447, 0, 8.167, -3.861, 0, 8.467, 0.025, 0, 8.533, -0.103, 0, 8.833, 4.17, 0, 9.233, -5.058, 0, 9.5, 0.639, 0, 9.633, -0.72, 0, 9.9, 5.418, 0, 10.267, -8.057, 0, 10.567, 5.829, 0, 10.9, -3.267, 0, 11.2, 1.602, 0, 11.467, -6.176, 0, 11.633, 12.788, 0, 11.933, -20.07, 0, 12.233, 18.959, 0, 12.533, -11.723, 0, 12.733, 1.282, 0, 12.9, -6.785, 0, 13.1, 16.816, 0, 13.4, -12.999, 0, 13.667, 6.037, 0, 13.967, -1.544, 0, 14.267, 1.591, 0, 14.633, -2.208, 0, 14.833, -0.204, 0, 14.967, -1.289, 0, 15.067, 0.158, 0, 15.2, -3.84, 0, 15.433, 14.771, 0, 15.7, -14.478, 0, 15.967, 8.111, 0, 16.267, -4.758, 0, 16.567, 3.035, 0, 16.833, -0.758, 0, 17, 0.366, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation63", "Segments": [0, -0.675, 2, 0.333, -0.675, 1, 0.344, -0.675, 0.356, -0.589, 0.367, 0, 1, 0.411, 2.354, 0.456, 3.846, 0.5, 3.846, 0, 0.733, -7.011, 0, 0.967, 8.239, 0, 1.233, -6.128, 0, 1.533, 3.464, 0, 1.9, -0.455, 0, 2.167, 0.286, 0, 2.6, -2.292, 0, 2.933, 2.636, 0, 3.733, -3.877, 0, 4.033, 4.659, 0, 4.333, -5.832, 0, 4.633, 6.183, 0, 5, -3.857, 0, 5.433, 0.998, 0, 5.933, -0.579, 0, 6.167, -0.21, 0, 6.6, -2.501, 0, 6.967, 4.389, 0, 7.367, -2.561, 0, 7.633, 0.774, 0, 8.067, -2.042, 0, 8.6, 2.842, 0, 9.067, -3.088, 0, 9.7, 3.762, 0, 10.1, -2.999, 0, 10.467, 1.119, 0, 10.767, -0.409, 0, 11.067, 0.146, 0, 11.367, -0.044, 0, 11.467, 2.639, 0, 11.733, -6.728, 0, 12.067, 6.362, 0, 12.433, -2.826, 0, 12.533, -1.771, 0, 12.667, -2.481, 0, 12.967, 5.302, 0, 13.233, -3.554, 0, 13.6, 1.131, 0, 13.667, 1.114, 0, 13.8, 1.209, 0, 14.467, -1.922, 0, 15, 1.464, 0, 15.033, 1.45, 0, 15.233, 5.153, 0, 15.533, -4.716, 0, 15.867, 0.695, 0, 16.1, -0.32, 0, 16.4, 1.062, 0, 17.033, -1.377, 0, 17.233, -0.675]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation64", "Segments": [0, 2.951, 2, 0.333, 2.951, 1, 0.344, 2.951, 0.356, 0.888, 0.367, 0, 1, 0.4, -2.663, 0.434, -3.433, 0.467, -3.433, 0, 0.633, 8.187, 0, 0.867, -13.832, 0, 1.133, 15.237, 0, 1.433, -11.484, 0, 1.7, 7.141, 0, 2, -3.424, 0, 2.333, 1.664, 0, 2.767, -3.108, 0, 3.067, 3.08, 0, 3.367, -1.28, 0, 3.667, 2.581, 0, 3.9, -6.357, 0, 4.2, 8.675, 0, 4.5, -11.866, 0, 4.8, 9.409, 0, 5.133, -6.537, 0, 5.467, 3.059, 0, 5.767, -0.73, 0, 5.933, -0.012, 0, 6.1, -0.278, 0, 6.367, 0.527, 0, 6.433, 0.451, 0, 6.533, 0.981, 0, 6.833, -3.932, 0, 7.133, 5.038, 0, 7.5, -4.462, 0, 7.8, 3.223, 0, 8.167, -1.931, 0, 8.467, 0.013, 0, 8.533, -0.052, 0, 8.833, 2.085, 0, 9.233, -2.529, 0, 9.5, 0.319, 0, 9.633, -0.36, 0, 9.9, 2.709, 0, 10.267, -4.029, 0, 10.567, 2.915, 0, 10.9, -1.633, 0, 11.2, 0.801, 0, 11.467, -3.088, 0, 11.633, 6.394, 0, 11.933, -10.035, 0, 12.233, 9.479, 0, 12.533, -5.862, 0, 12.733, 0.641, 0, 12.9, -3.393, 0, 13.1, 8.408, 0, 13.4, -6.5, 0, 13.667, 3.018, 0, 13.967, -0.772, 0, 14.267, 0.796, 0, 14.633, -1.104, 0, 14.833, -0.102, 0, 14.967, -0.644, 0, 15.067, 0.079, 0, 15.2, -1.92, 0, 15.433, 7.385, 0, 15.7, -7.239, 0, 15.967, 4.056, 0, 16.267, -2.379, 0, 16.567, 1.518, 0, 16.833, -0.379, 1, 16.889, -0.379, 16.944, -0.45, 17, 0.183, 1, 17.078, 1.069, 17.155, 2.951, 17.233, 2.951]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation65", "Segments": [0, 4.849, 2, 0.333, 4.849, 1, 0.344, 4.849, 0.356, 0.472, 0.367, 0, 1, 0.422, -2.361, 0.478, -3.111, 0.533, -3.111, 0, 0.733, 10.302, 0, 0.967, -17.3, 0, 1.233, 20.95, 0, 1.533, -19.522, 0, 1.833, 14.374, 0, 2.133, -8.891, 0, 2.467, 5.626, 0, 2.833, -5.959, 0, 3.167, 6.337, 0, 3.5, -3.819, 0, 3.767, 4.319, 0, 4, -9.191, 0, 4.3, 14.37, 0, 4.6, -18.197, 0, 4.9, 17.599, 0, 5.233, -13.425, 0, 5.567, 8.207, 0, 5.9, -3.554, 0, 6.2, 0.909, 0, 6.433, 0.488, 0, 6.633, 1.085, 0, 6.9, -5.862, 0, 7.233, 9.313, 0, 7.567, -8.994, 0, 7.9, 7.523, 0, 8.233, -5.227, 0, 8.6, 1.484, 0, 8.7, 1.293, 0, 8.933, 2.802, 0, 9.3, -4.653, 0, 9.633, 1.489, 0, 9.767, 0.563, 0, 10.033, 3.881, 0, 10.367, -7.054, 0, 10.7, 6.409, 0, 11, -4.526, 0, 11.3, 2.69, 0, 11.533, -3.186, 0, 11.733, 7.949, 0, 12, -15.157, 0, 12.333, 17.166, 0, 12.633, -11.036, 0, 12.867, 0.24, 0, 13, -0.837, 0, 13.2, 10.561, 0, 13.5, -12.203, 0, 13.8, 7.681, 0, 14.1, -3.259, 0, 14.4, 2.359, 0, 14.733, -2.488, 0, 15.067, 0.21, 0, 15.267, -2.198, 0, 15.5, 9.694, 0, 15.8, -12.877, 0, 16.1, 9.318, 0, 16.4, -6.265, 0, 16.7, 4.418, 0, 17, -1.739, 1, 17.056, -1.739, 17.111, -1.586, 17.167, -0.62, 1, 17.189, -0.233, 17.211, 4.849, 17.233, 4.849]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation66", "Segments": [0, -11.659, 2, 0.333, -11.659, 0, 0.4, 0, 0, 0.6, -3.153, 0, 0.833, 13.482, 0, 1.067, -24.347, 0, 1.3, 28.512, 0, 1.633, -28.679, 0, 1.933, 23.796, 0, 2.267, -17.384, 0, 2.567, 12.873, 0, 2.933, -12.028, 0, 3.267, 12.177, 0, 3.6, -8.528, 0, 3.867, 7.759, 0, 4.1, -13.445, 0, 4.367, 21.497, 0, 4.667, -27.241, 0, 5, 27.151, 0, 5.333, -23.015, 0, 5.7, 16.155, 0, 6, -9.436, 0, 6.333, 4.386, 0, 6.6, -0.329, 0, 6.733, 0.308, 0, 7, -7.866, 0, 7.3, 14.799, 0, 7.667, -16.127, 0, 8, 14.653, 0, 8.333, -11.639, 0, 8.667, 5.332, 0, 8.933, 1.983, 0, 9.033, 2.4, 0, 9.4, -7.415, 0, 9.733, 4.124, 0, 9.933, 1.531, 0, 10.133, 4.472, 0, 10.467, -10.933, 0, 10.8, 11.99, 0, 11.1, -9.859, 0, 11.4, 6.689, 0, 11.633, -3.465, 0, 11.867, 9.556, 0, 12.1, -19.971, 0, 12.4, 26.156, 0, 12.767, -19.978, 0, 13.267, 12.356, 0, 13.6, -19.344, 0, 13.933, 15.187, 0, 14.233, -8.675, 0, 14.533, 6.007, 0, 14.833, -5.46, 0, 15.133, 1.877, 0, 15.367, -2.797, 0, 15.6, 12.515, 0, 15.9, -20.003, 0, 16.2, 17.005, 0, 16.533, -13.023, 0, 16.833, 9.774, 1, 16.933, 9.774, 17.033, 5.143, 17.133, -5.491, 1, 17.166, -9.035, 17.2, -11.659, 17.233, -11.659]}, {"Target": "Parameter", "Id": "Param55", "Segments": [0, -10.57, 2, 0.333, -10.57, 0, 0.467, 0.275, 0, 0.667, -3.301, 0, 0.9, 15.098, 0, 1.133, -30, 0, 1.367, 30, 2, 1.433, 30, 0, 1.667, -30, 2, 1.767, -30, 0, 2, 30, 2, 2.067, 30, 0, 2.367, -28.245, 0, 2.7, 23.319, 0, 3, -22.009, 0, 3.367, 21.318, 0, 3.7, -15.901, 0, 3.967, 12.483, 0, 4.2, -18.536, 0, 4.467, 30, 0, 4.7, -30, 2, 4.767, -30, 0, 5.033, 30, 2, 5.1, 30, 0, 5.4, -30, 2, 5.467, -30, 0, 5.8, 26.694, 0, 6.133, -18.428, 0, 6.433, 11.293, 0, 6.733, -3.752, 0, 6.867, -2.564, 0, 7.067, -8.713, 0, 7.4, 21.092, 0, 7.733, -25.123, 0, 8.1, 24.497, 0, 8.433, -21.317, 0, 8.8, 12.477, 0, 9.1, -0.19, 0, 9.2, 0.225, 0, 9.5, -9.97, 0, 9.833, 8.418, 0, 10.067, 1.535, 0, 10.233, 3.942, 0, 10.533, -15.411, 0, 10.867, 19.89, 0, 11.2, -18.159, 0, 11.533, 13.951, 0, 11.767, -5.23, 0, 11.967, 9.441, 0, 12.167, -24.194, 0, 12.467, 30, 2, 12.533, 30, 0, 12.833, -30, 0, 13.3, 17.751, 0, 13.667, -27.508, 0, 14.033, 25.197, 0, 14.333, -16.78, 0, 14.633, 12.694, 0, 14.933, -11.492, 0, 15.233, 5.487, 0, 15.467, -2.857, 0, 15.667, 14.753, 0, 15.967, -28.198, 0, 16.3, 27.132, 0, 16.633, -22.986, 0, 16.933, 18.68, 0, 17.233, -10.57]}, {"Target": "Parameter", "Id": "Param28", "Segments": [0, -7.12, 2, 0.333, -7.12, 0, 1.733, 7.36, 0, 3.133, -7.12, 0, 4.567, 7.36, 0, 5.967, -7.12, 0, 7.4, 7.36, 0, 8.8, -7.12, 0, 10.2, 7.36, 0, 11.633, -7.12, 0, 13.033, 7.36, 0, 14.467, -7.12, 0, 15.867, 7.36, 0, 17.233, -7.12]}, {"Target": "Parameter", "Id": "Param29", "Segments": [0, -1.278, 2, 0.333, -1.278, 0, 0.867, -4.74, 0, 2.3, 5.1, 0, 3.7, -4.74, 0, 5.133, 5.1, 1, 5.411, 5.1, 5.689, 2.577, 5.967, -1.278, 1, 6.156, -3.899, 6.344, -4.74, 6.533, -4.74, 0, 7.933, 5.1, 0, 9.367, -4.74, 0, 10.767, 5.1, 1, 11.056, 5.1, 11.344, 2.654, 11.633, -1.278, 1, 11.822, -3.849, 12.011, -4.74, 12.2, -4.74, 0, 13.6, 5.1, 0, 15, -4.74, 0, 16.433, 5.1, 0, 17.233, -1.278]}, {"Target": "Parameter", "Id": "Param30", "Segments": [0, -5.909, 2, 0.333, -5.909, 0, 1.567, 2.533, 0, 2.967, -6.287, 0, 4.367, 2.533, 0, 5.8, -6.287, 1, 5.856, -6.287, 5.911, -6.519, 5.967, -5.909, 1, 6.378, -1.393, 6.789, 2.533, 7.2, 2.533, 0, 8.633, -6.287, 0, 10.033, 2.533, 0, 11.433, -6.287, 1, 11.5, -6.287, 11.566, -6.572, 11.633, -5.909, 1, 12.044, -1.815, 12.456, 2.533, 12.867, 2.533, 0, 14.267, -6.287, 0, 15.7, 2.533, 0, 17.067, -6.287, 0, 17.233, -5.909]}, {"Target": "Parameter", "Id": "Param31", "Segments": [0, -0.551, 2, 0.333, -0.551, 0, 0.933, 3.018, 0, 2.333, -6.18, 0, 3.733, 3.018, 0, 5.167, -6.18, 1, 5.434, -6.18, 5.7, -4.03, 5.967, -0.551, 1, 6.167, 2.058, 6.367, 3.018, 6.567, 3.018, 0, 8, -6.18, 0, 9.4, 3.018, 0, 10.8, -6.18, 1, 11.078, -6.18, 11.355, -4.102, 11.633, -0.551, 1, 11.833, 2.005, 12.033, 3.018, 12.233, 3.018, 0, 13.633, -6.18, 0, 15.067, 3.018, 0, 16.467, -6.18, 0, 17.233, -0.551]}, {"Target": "Parameter", "Id": "Param32", "Segments": [0, -2.714, 2, 0.333, -2.714, 0, 1.633, 2.284, 0, 3.033, -2.796, 0, 4.467, 2.284, 0, 5.867, -2.796, 1, 5.9, -2.796, 5.934, -2.916, 5.967, -2.714, 1, 6.4, -0.085, 6.834, 2.284, 7.267, 2.284, 0, 8.7, -2.796, 0, 10.1, 2.284, 0, 11.533, -2.796, 1, 11.566, -2.796, 11.6, -2.916, 11.633, -2.714, 1, 12.066, -0.085, 12.5, 2.284, 12.933, 2.284, 0, 14.333, -2.796, 0, 15.767, 2.284, 0, 17.133, -2.796, 0, 17.233, -2.714]}, {"Target": "Parameter", "Id": "Param33", "Segments": [0, -2.392, 2, 0.333, -2.392, 0, 0.967, 3.279, 0, 2.367, -10.047, 0, 3.767, 3.279, 0, 5.2, -10.047, 1, 5.456, -10.047, 5.711, -7.241, 5.967, -2.392, 1, 6.178, 1.614, 6.389, 3.279, 6.6, 3.279, 0, 8.033, -10.047, 0, 9.433, 3.279, 0, 10.833, -10.047, 1, 11.1, -10.047, 11.366, -7.345, 11.633, -2.392, 1, 11.844, 1.529, 12.056, 3.279, 12.267, 3.279, 0, 13.667, -10.047, 0, 15.1, 3.279, 0, 16.5, -10.047, 0, 17.233, -2.392]}, {"Target": "Parameter", "Id": "Param34", "Segments": [0, -21.182, 2, 0.333, -21.182, 0, 1.667, 11.7, 0, 3.067, -21.42, 0, 4.5, 11.7, 0, 5.9, -21.42, 1, 5.922, -21.42, 5.945, -21.992, 5.967, -21.182, 1, 6.411, -4.975, 6.856, 11.7, 7.3, 11.7, 0, 8.733, -21.42, 0, 10.133, 11.7, 0, 11.567, -21.42, 1, 11.589, -21.42, 11.611, -21.992, 11.633, -21.182, 1, 12.078, -4.975, 12.522, 11.7, 12.967, 11.7, 0, 14.367, -21.42, 0, 15.8, 11.7, 0, 17.167, -21.42, 0, 17.233, -21.182]}, {"Target": "Parameter", "Id": "Param84", "Segments": [0, 0, 2, 0.333, 0, 0, 1.733, -6.9, 0, 3.133, 0, 0, 4.567, -6.9, 0, 5.967, 0, 0, 7.4, -6.9, 0, 8.8, 0, 0, 10.2, -6.9, 0, 11.633, 0, 0, 13.033, -6.9, 0, 14.467, 0, 0, 15.867, -6.9, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param85", "Segments": [0, 0, 2, 0.333, 0, 0, 1.033, 4.74, 0, 2.433, -4.74, 1, 2.666, -4.74, 2.9, -3.085, 3.133, 0, 1, 3.378, 3.232, 3.622, 4.74, 3.867, 4.74, 0, 5.267, -4.74, 1, 5.5, -4.74, 5.734, -3.16, 5.967, 0, 1, 6.2, 3.16, 6.434, 4.74, 6.667, 4.74, 0, 8.1, -4.74, 1, 8.333, -4.74, 8.567, -3.16, 8.8, 0, 1, 9.033, 3.16, 9.267, 4.74, 9.5, 4.74, 0, 10.933, -4.74, 1, 11.166, -4.74, 11.4, -3.16, 11.633, 0, 1, 11.866, 3.16, 12.1, 4.74, 12.333, 4.74, 0, 13.733, -4.74, 1, 13.978, -4.74, 14.222, -3.232, 14.467, 0, 1, 14.7, 3.085, 14.934, 4.74, 15.167, 4.74, 0, 16.567, -4.74, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param83", "Segments": [0, 0, 2, 0.333, 0, 0, 1.733, 3.96, 0, 3.133, 0, 0, 4.567, 3.96, 0, 5.967, 0, 0, 7.4, 3.96, 0, 8.8, 0, 0, 10.2, 3.96, 0, 11.633, 0, 0, 13.033, 3.96, 0, 14.467, 0, 0, 15.867, 3.96, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param86", "Segments": [0, 1, 2, 0.333, 1, 2, 2.333, 1, 0, 2.433, 0, 0, 2.567, 1, 2, 3.133, 1, 2, 5.167, 1, 0, 5.267, 0, 0, 5.4, 1, 2, 5.967, 1, 2, 8, 1, 0, 8.1, 0, 0, 8.233, 1, 2, 8.8, 1, 2, 10.8, 1, 0, 10.933, 0, 0, 11.067, 1, 2, 11.633, 1, 2, 13.633, 1, 0, 13.733, 0, 0, 13.9, 1, 2, 14.467, 1, 2, 16.467, 1, 0, 16.567, 0, 0, 16.7, 1, 2, 17.233, 1]}, {"Target": "Parameter", "Id": "Param95", "Segments": [0, 0, 2, 0.333, 0, 0, 1.733, 10, 0, 3.133, 0, 0, 4.567, 10, 0, 5.967, 0, 0, 7.4, 10, 0, 8.8, 0, 0, 10.2, 10, 0, 11.633, 0, 0, 13.033, 10, 0, 14.467, 0, 0, 15.867, 10, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param92", "Segments": [0, 0, 2, 0.333, 0, 0, 1.033, -12, 0, 2.433, 12, 1, 2.666, 12, 2.9, 7.809, 3.133, 0, 1, 3.378, -8.181, 3.622, -12, 3.867, -12, 0, 5.267, 12, 1, 5.5, 12, 5.734, 8, 5.967, 0, 1, 6.2, -8, 6.434, -12, 6.667, -12, 0, 8.1, 12, 1, 8.333, 12, 8.567, 8, 8.8, 0, 1, 9.033, -8, 9.267, -12, 9.5, -12, 0, 10.933, 12, 1, 11.166, 12, 11.4, 8, 11.633, 0, 1, 11.866, -8, 12.1, -12, 12.333, -12, 0, 13.733, 12, 1, 13.978, 12, 14.222, 8.181, 14.467, 0, 1, 14.7, -7.809, 14.934, -12, 15.167, -12, 0, 16.567, 12, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param93", "Segments": [0, 0, 2, 0.333, 0, 0, 1.733, -12, 0, 3.133, 0, 0, 4.567, -12, 0, 5.967, 0, 0, 7.4, -12, 0, 8.8, 0, 0, 10.2, -12, 0, 11.633, 0, 0, 13.033, -12, 0, 14.467, 0, 0, 15.867, -12, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param96", "Segments": [0, 0, 2, 0.333, 0, 0, 0.5, -16, 0, 0.667, 0, 0, 0.867, -16, 0, 1.1, 0, 2, 3.133, 0, 0, 3.333, -16, 0, 3.5, 0, 0, 3.7, -16, 0, 3.933, 0, 2, 5.967, 0, 0, 6.133, -16, 0, 6.333, 0, 0, 6.533, -16, 0, 6.733, 0, 2, 8.8, 0, 0, 8.967, -16, 0, 9.167, 0, 0, 9.367, -16, 0, 9.567, 0, 2, 11.633, 0, 0, 11.8, -16, 0, 11.967, 0, 0, 12.2, -16, 0, 12.4, 0, 2, 14.467, 0, 0, 14.633, -16, 0, 14.8, 0, 0, 15, -16, 0, 15.233, 0, 2, 17.233, 0]}, {"Target": "Parameter", "Id": "Param97", "Segments": [0, 0, 2, 0.333, 0, 0, 0.367, 14, 0, 0.6, -14, 1, 0.622, -14, 0.645, -5.57, 0.667, 0, 1, 0.711, 11.14, 0.756, 14, 0.8, 14, 0, 1, -14, 0, 1.1, 0, 2, 3.133, 0, 0, 3.167, 14, 0, 3.433, -14, 1, 3.455, -14, 3.478, -5.57, 3.5, 0, 1, 3.544, 11.14, 3.589, 14, 3.633, 14, 0, 3.8, -14, 0, 3.933, 0, 2, 5.967, 0, 0, 6, 14, 0, 6.267, -14, 1, 6.289, -14, 6.311, -5.57, 6.333, 0, 1, 6.378, 11.14, 6.422, 14, 6.467, 14, 0, 6.633, -14, 0, 6.733, 0, 2, 8.8, 0, 0, 8.833, 14, 0, 9.067, -14, 1, 9.1, -14, 9.134, -7.829, 9.167, 0, 1, 9.211, 10.439, 9.256, 14, 9.3, 14, 0, 9.467, -14, 0, 9.567, 0, 2, 11.633, 0, 0, 11.667, 14, 0, 11.9, -14, 1, 11.922, -14, 11.945, -4.467, 11.967, 0, 1, 12.022, 11.168, 12.078, 14, 12.133, 14, 0, 12.3, -14, 0, 12.4, 0, 2, 14.467, 0, 0, 14.5, 14, 0, 14.733, -14, 1, 14.755, -14, 14.778, -5.57, 14.8, 0, 1, 14.844, 11.14, 14.889, 14, 14.933, 14, 0, 15.133, -14, 0, 15.233, 0, 2, 17.233, 0]}, {"Target": "Parameter", "Id": "Param89", "Segments": [0, 0, 2, 0.333, 0, 0, 1.733, 18.72, 0, 3.133, 0, 0, 4.567, 18.72, 0, 5.967, 0, 0, 7.4, 18.72, 0, 8.8, 0, 0, 10.2, 18.72, 0, 11.633, 0, 0, 13.033, 18.72, 0, 14.467, 0, 0, 15.867, 18.72, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param90", "Segments": [0, 0, 2, 0.333, 0, 0, 1.033, -19, 0, 2.433, 19, 1, 2.666, 19, 2.9, 12.364, 3.133, 0, 1, 3.378, -12.953, 3.622, -19, 3.867, -19, 0, 5.267, 19, 1, 5.5, 19, 5.734, 12.667, 5.967, 0, 1, 6.2, -12.667, 6.434, -19, 6.667, -19, 0, 8.1, 19, 1, 8.333, 19, 8.567, 12.667, 8.8, 0, 1, 9.033, -12.667, 9.267, -19, 9.5, -19, 0, 10.933, 19, 1, 11.166, 19, 11.4, 12.667, 11.633, 0, 1, 11.866, -12.667, 12.1, -19, 12.333, -19, 0, 13.733, 19, 1, 13.978, 19, 14.222, 12.953, 14.467, 0, 1, 14.7, -12.364, 14.934, -19, 15.167, -19, 0, 16.567, 19, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation56", "Segments": [0, 0, 2, 0.333, 0, 0, 1.033, 9, 0, 1.733, -9, 0, 2.433, 9, 0, 3.133, 0, 0, 3.867, 9, 0, 4.567, -9, 0, 5.267, 9, 0, 5.967, 0, 0, 6.667, 9, 0, 7.4, -9, 0, 8.1, 9, 0, 8.8, 0, 0, 9.5, 9, 0, 10.2, -9, 0, 10.933, 9, 0, 11.633, 0, 0, 12.333, 9, 0, 13.033, -9, 0, 13.733, 9, 0, 14.467, 0, 0, 15.167, 9, 0, 15.867, -9, 0, 16.567, 9, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param98", "Segments": [0, -6, 2, 0.333, -6, 0, 0.667, 0, 0, 1.367, -12, 0, 2.1, 12, 0, 2.8, -12, 0, 3.5, 0, 0, 4.2, -12, 0, 4.9, 12, 0, 5.633, -12, 1, 5.744, -12, 5.856, -9.8, 5.967, -6, 1, 6.089, -1.82, 6.211, 0, 6.333, 0, 0, 7.033, -12, 0, 7.733, 12, 0, 8.433, -12, 0, 9.167, 0, 0, 9.867, -12, 0, 10.567, 12, 0, 11.267, -12, 1, 11.389, -12, 11.511, -10.18, 11.633, -6, 1, 11.744, -2.2, 11.856, 0, 11.967, 0, 0, 12.7, -12, 0, 13.4, 12, 0, 14.1, -12, 0, 14.8, 0, 0, 15.5, -12, 0, 16.233, 12, 0, 16.933, -12, 0, 17.233, -6]}, {"Target": "Parameter", "Id": "Param99", "Segments": [0, -10.921, 2, 0.333, -10.921, 0, 1, 0, 0, 1.7, -11, 0, 2.4, 11, 0, 3.1, -11, 0, 3.8, 0, 0, 4.533, -11, 0, 5.233, 11, 0, 5.933, -11, 1, 5.944, -11, 5.956, -11.195, 5.967, -10.921, 1, 6.189, -5.432, 6.411, 0, 6.633, 0, 0, 7.333, -11, 0, 8.067, 11, 0, 8.767, -11, 0, 9.467, 0, 0, 10.167, -11, 0, 10.867, 11, 0, 11.6, -11, 1, 11.611, -11, 11.622, -11.195, 11.633, -10.921, 1, 11.855, -5.432, 12.078, 0, 12.3, 0, 0, 13, -11, 0, 13.7, 11, 0, 14.4, -11, 0, 15.133, 0, 0, 15.833, -11, 0, 16.533, 11, 0, 17.2, -11, 0, 17.233, -10.921]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation67", "Segments": [0, -10.939, 2, 0.333, -10.939, 0, 0.567, -25, 0, 1.267, 0, 0, 1.967, -25, 0, 2.7, 25, 0, 3.4, -25, 0, 4.1, 0, 0, 4.8, -25, 0, 5.5, 25, 1, 5.656, 25, 5.811, 9.502, 5.967, -10.939, 1, 6.056, -22.62, 6.144, -25, 6.233, -25, 0, 6.933, 0, 0, 7.633, -25, 0, 8.333, 25, 0, 9.033, -25, 0, 9.767, 0, 0, 10.467, -25, 0, 11.167, 25, 1, 11.322, 25, 11.478, 10.955, 11.633, -10.939, 1, 11.711, -21.886, 11.789, -25, 11.867, -25, 0, 12.567, 0, 0, 13.3, -25, 0, 14, 25, 0, 14.7, -25, 0, 15.4, 0, 0, 16.1, -25, 0, 16.833, 25, 0, 17.233, -10.939]}, {"Target": "Parameter", "Id": "Param281", "Segments": [0, 0, 2, 0.333, 0, 0, 1.667, 1, 0, 3.1, 0, 0, 4.533, 1, 0, 5.967, 0, 0, 7.4, 1, 0, 8.733, 0, 0, 10.167, 1, 0, 11.6, 0, 0, 13.033, 1, 0, 14.467, 0, 0, 15.833, 1, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param197mao2", "Segments": [0, 0, 2, 0.333, 0, 0, 0.967, 2.894, 0, 2.5, -2.454, 1, 2.7, -2.454, 2.9, -1.646, 3.1, 0, 1, 3.333, 1.92, 3.567, 2.894, 3.8, 2.894, 0, 5.267, -2.454, 1, 5.5, -2.454, 5.734, -1.839, 5.967, 0, 1, 6.178, 1.664, 6.389, 2.894, 6.6, 2.894, 0, 8.133, -2.454, 1, 8.333, -2.454, 8.533, -1.604, 8.733, 0, 1, 8.978, 1.96, 9.222, 2.894, 9.467, 2.894, 0, 11, -2.454, 1, 11.2, -2.454, 11.4, -1.687, 11.6, 0, 1, 11.822, 1.875, 12.045, 2.894, 12.267, 2.894, 0, 13.767, -2.454, 1, 14, -2.454, 14.234, -1.839, 14.467, 0, 1, 14.678, 1.664, 14.889, 2.894, 15.1, 2.894, 0, 16.633, -2.454, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param216mao2", "Segments": [0, -8.88, 0, 0.333, -8.9, 2, 5.967, -8.9, 2, 11.633, -8.9, 0, 17.233, -8.88]}, {"Target": "Parameter", "Id": "Param212mao2", "Segments": [0, 0, 2, 0.333, 0, 0, 0.5, -16, 0, 0.667, 12.568, 0, 0.867, -16, 0, 1.1, 0, 2, 3.133, 0, 2, 5.967, 0, 0, 6.133, -16, 0, 6.333, 12.568, 0, 6.533, -16, 0, 6.733, 0, 2, 8.8, 0, 2, 11.633, 0, 0, 11.8, -16, 0, 11.967, 12.568, 0, 12.2, -16, 0, 12.4, 0, 2, 14.467, 0, 2, 17.233, 0]}, {"Target": "Parameter", "Id": "Param214mao2", "Segments": [0, 0, 2, 0.333, 0, 2, 1.6, 0, 0, 1.767, 16, 0, 1.933, 0, 0, 2.167, 16, 0, 2.367, 0, 2, 3.133, 0, 2, 5.967, 0, 2, 7.233, 0, 0, 7.433, 16, 0, 7.6, 0, 0, 7.8, 16, 0, 8.033, 0, 2, 8.8, 0, 2, 11.633, 0, 2, 12.9, 0, 0, 13.067, 16, 0, 13.233, 0, 0, 13.467, 16, 0, 13.667, 0, 2, 14.467, 0, 2, 17.233, 0]}, {"Target": "Parameter", "Id": "Param215mao2", "Segments": [0, 0, 2, 0.333, 0, 2, 1.6, 0, 0, 1.667, -14, 0, 1.867, 15, 0, 2.033, -8, 0, 2.233, 9, 0, 2.5, 0, 2, 3.133, 0, 2, 5.967, 0, 2, 7.233, 0, 0, 7.3, -14, 0, 7.533, 15, 0, 7.7, -8, 0, 7.867, 9, 0, 8.167, 0, 2, 8.8, 0, 2, 11.633, 0, 2, 12.9, 0, 0, 12.967, -14, 0, 13.167, 15, 0, 13.367, -8, 0, 13.533, 9, 0, 13.8, 0, 2, 14.467, 0, 2, 17.233, 0]}, {"Target": "Parameter", "Id": "Param198mao2", "Segments": [0, 0, 2, 0.333, 0, 0, 1.667, 30, 0, 3.133, 0, 0, 4.4, 30, 0, 5.967, 0, 0, 7.3, 30, 0, 8.8, 0, 0, 10.067, 30, 0, 11.633, 0, 0, 12.967, 30, 0, 14.467, 0, 0, 15.733, 30, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation143mao2", "Segments": [0, 0, 2, 0.333, 0, 0, 0.967, 17, 0, 1.733, 0, 0, 2.467, 17, 2, 3.133, 17, 2, 3.767, 17, 0, 4.567, 0, 0, 5.3, 17, 0, 5.967, 0, 0, 6.6, 17, 0, 7.4, 0, 0, 8.133, 17, 2, 8.8, 17, 2, 9.433, 17, 0, 10.2, 0, 0, 10.967, 17, 0, 11.633, 0, 0, 12.267, 17, 0, 13.033, 0, 0, 13.767, 17, 2, 14.467, 17, 2, 15.1, 17, 0, 15.867, 0, 0, 16.6, 17, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation144mao2", "Segments": [0, -10.82, 2, 0.333, -10.82, 0, 0.533, -19.438, 0, 1.167, 17.12, 0, 1.933, -19.438, 0, 2.7, 17.12, 1, 2.844, 17.12, 2.989, 3.8, 3.133, -10.82, 1, 3.211, -18.693, 3.289, -19.438, 3.367, -19.438, 0, 4, 17.12, 0, 4.767, -19.438, 0, 5.5, 17.12, 1, 5.656, 17.12, 5.811, 5.804, 5.967, -10.82, 1, 6.034, -17.945, 6.1, -19.438, 6.167, -19.438, 0, 6.833, 17.12, 0, 7.6, -19.438, 0, 8.333, 17.12, 1, 8.489, 17.12, 8.644, 5.804, 8.8, -10.82, 1, 8.867, -17.945, 8.933, -19.438, 9, -19.438, 0, 9.633, 17.12, 0, 10.433, -19.438, 0, 11.167, 17.12, 1, 11.322, 17.12, 11.478, 5.804, 11.633, -10.82, 1, 11.7, -17.945, 11.766, -19.438, 11.833, -19.438, 0, 12.467, 17.12, 0, 13.233, -19.438, 0, 14, 17.12, 1, 14.156, 17.12, 14.311, 5.804, 14.467, -10.82, 1, 14.534, -17.945, 14.6, -19.438, 14.667, -19.438, 0, 15.3, 17.12, 0, 16.067, -19.438, 0, 16.833, 17.12, 0, 17.233, -10.82]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation145mao2", "Segments": [0, 8.46, 2, 0.333, 8.46, 0, 0.767, -17.322, 0, 1.4, 16.411, 0, 2.2, -17.322, 0, 2.933, 16.411, 1, 3, 16.411, 3.066, 15.033, 3.133, 8.46, 1, 3.289, -6.877, 3.444, -17.322, 3.6, -17.322, 0, 4.233, 16.411, 0, 5, -17.322, 0, 5.767, 16.411, 1, 5.834, 16.411, 5.9, 15.033, 5.967, 8.46, 1, 6.122, -6.877, 6.278, -17.322, 6.433, -17.322, 0, 7.067, 16.411, 0, 7.833, -17.322, 0, 8.6, 16.411, 1, 8.667, 16.411, 8.733, 15.033, 8.8, 8.46, 1, 8.956, -6.877, 9.111, -17.322, 9.267, -17.322, 0, 9.9, 16.411, 0, 10.667, -17.322, 0, 11.4, 16.411, 1, 11.478, 16.411, 11.555, 15.721, 11.633, 8.46, 1, 11.778, -5.025, 11.922, -17.322, 12.067, -17.322, 0, 12.733, 16.411, 0, 13.5, -17.322, 0, 14.233, 16.411, 1, 14.311, 16.411, 14.389, 15.721, 14.467, 8.46, 1, 14.611, -5.025, 14.756, -17.322, 14.9, -17.322, 0, 15.533, 16.411, 0, 16.333, -17.322, 0, 17.067, 16.411, 0, 17.233, 8.46]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation146mao2", "Segments": [0, 28.83, 2, 0.333, 28.83, 0, 0.367, 29.224, 0, 1.033, -30, 0, 1.667, 29.224, 0, 2.433, -30, 1, 2.666, -30, 2.9, 0.52, 3.133, 28.83, 1, 3.144, 30.178, 3.156, 29.224, 3.167, 29.224, 0, 3.867, -30, 0, 4.5, 29.224, 0, 5.267, -30, 1, 5.5, -30, 5.734, 0.52, 5.967, 28.83, 1, 5.978, 30.178, 5.989, 29.224, 6, 29.224, 0, 6.667, -30, 0, 7.3, 29.224, 0, 8.1, -30, 1, 8.333, -30, 8.567, 0.52, 8.8, 28.83, 1, 8.811, 30.178, 8.822, 29.224, 8.833, 29.224, 0, 9.5, -30, 0, 10.133, 29.224, 0, 10.933, -30, 1, 11.166, -30, 11.4, 0.52, 11.633, 28.83, 1, 11.644, 30.178, 11.656, 29.224, 11.667, 29.224, 0, 12.333, -30, 0, 12.967, 29.224, 0, 13.733, -30, 1, 13.978, -30, 14.222, -0.272, 14.467, 28.83, 1, 14.478, 30.153, 14.489, 29.224, 14.5, 29.224, 0, 15.167, -30, 0, 15.8, 29.224, 0, 16.567, -30, 0, 17.233, 28.83]}, {"Target": "Parameter", "Id": "Param100", "Segments": [0, 0, 2, 0.333, 0, 0, 1.733, -2.04, 0, 3.133, 0, 0, 4.567, -2.04, 0, 5.967, 0, 0, 7.4, -2.04, 0, 8.8, 0, 0, 10.2, -2.04, 0, 11.633, 0, 0, 13.033, -2.04, 0, 14.467, 0, 0, 15.867, -2.04, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param105", "Segments": [0, 0, 2, 0.333, 0, 0, 1.033, 16, 0, 2.433, -16, 1, 2.666, -16, 2.9, -6.187, 3.133, 0, 1, 3.611, 12.668, 4.089, 16, 4.567, 16, 0, 5.967, 0, 0, 6.667, 16, 0, 8.1, -16, 1, 8.333, -16, 8.567, -6.327, 8.8, 0, 1, 9.267, 12.654, 9.733, 16, 10.2, 16, 0, 11.633, 0, 0, 12.333, 16, 0, 13.733, -16, 1, 13.978, -16, 14.222, -6.612, 14.467, 0, 1, 14.934, 12.624, 15.4, 16, 15.867, 16, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param108", "Segments": [0, 0, 2, 0.333, 0, 0, 1.733, 4, 0, 3.133, 0, 0, 4.567, 4, 0, 5.967, 0, 0, 7.4, 4, 0, 8.8, 0, 0, 10.2, 4, 0, 11.633, 0, 0, 13.033, 4, 0, 14.467, 0, 0, 15.867, 4, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param109", "Segments": [0, 0, 2, 0.333, 0, 0, 1.033, -4, 0, 2.433, 4.46, 1, 2.666, 4.46, 2.9, 2.737, 3.133, 0, 1, 3.378, -2.868, 3.622, -4, 3.867, -4, 0, 5.267, 4.46, 1, 5.5, 4.46, 5.734, 2.811, 5.967, 0, 1, 6.2, -2.811, 6.434, -4, 6.667, -4, 0, 8.1, 4.46, 1, 8.333, 4.46, 8.567, 2.811, 8.8, 0, 1, 9.033, -2.811, 9.267, -4, 9.5, -4, 0, 10.933, 4.46, 1, 11.166, 4.46, 11.4, 2.811, 11.633, 0, 1, 11.866, -2.811, 12.1, -4, 12.333, -4, 0, 13.733, 4.46, 1, 13.978, 4.46, 14.222, 2.883, 14.467, 0, 1, 14.7, -2.752, 14.934, -4, 15.167, -4, 0, 16.567, 4.46, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param101", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, -21, 0, 0.567, 0, 0, 0.667, -21, 0, 0.8, 0, 2, 3.133, 0, 0, 3.233, -21, 0, 3.4, 0, 0, 3.5, -21, 0, 3.633, 0, 0, 4.567, -21, 0, 5.967, 0, 0, 6.067, -21, 0, 6.233, 0, 0, 6.333, -21, 0, 6.467, 0, 2, 8.8, 0, 0, 8.9, -21, 0, 9.033, 0, 0, 9.167, -21, 0, 9.3, 0, 0, 10.2, -21, 0, 11.633, 0, 0, 11.733, -21, 0, 11.867, 0, 0, 11.967, -21, 0, 12.133, 0, 2, 14.467, 0, 0, 14.567, -21, 0, 14.7, 0, 0, 14.8, -21, 0, 14.933, 0, 0, 15.867, -21, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param106", "Segments": [0, 0, 2, 0.333, 0, 0, 1.733, 15.405, 0, 3.133, 0, 0, 4.567, 15.405, 0, 5.967, 0, 0, 7.4, 15.405, 0, 8.8, 0, 0, 10.2, 15.405, 0, 11.633, 0, 0, 13.033, 15.405, 0, 14.467, 0, 0, 15.867, 15.405, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param104", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, -16, 0, 0.567, 0, 0, 0.667, -16, 0, 0.8, 0, 2, 3.133, 0, 0, 3.233, -16, 0, 3.4, 0, 0, 3.5, -16, 0, 3.633, 0, 0, 4.567, -16, 0, 5.967, 0, 0, 6.067, -16, 0, 6.233, 0, 0, 6.333, -16, 0, 6.467, 0, 2, 8.8, 0, 0, 8.9, -16, 0, 9.033, 0, 0, 9.167, -16, 0, 9.3, 0, 0, 10.2, -16, 0, 11.633, 0, 0, 11.733, -16, 0, 11.867, 0, 0, 11.967, -16, 0, 12.133, 0, 2, 14.467, 0, 0, 14.567, -16, 0, 14.7, 0, 0, 14.8, -16, 0, 14.933, 0, 0, 15.867, -16, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param103", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, 27, 0, 0.567, 0, 0, 0.667, 27, 0, 0.8, 0, 2, 3.133, 0, 0, 3.233, 27, 0, 3.4, 0, 0, 3.5, 27, 0, 3.633, 0, 0, 4.567, 27, 0, 5.967, 0, 0, 6.067, 27, 0, 6.233, 0, 0, 6.333, 27, 0, 6.467, 0, 2, 8.8, 0, 0, 8.9, 27, 0, 9.033, 0, 0, 9.167, 27, 0, 9.3, 0, 0, 10.2, 27, 0, 11.633, 0, 0, 11.733, 27, 0, 11.867, 0, 0, 11.967, 27, 0, 12.133, 0, 2, 14.467, 0, 0, 14.567, 27, 0, 14.7, 0, 0, 14.8, 27, 0, 14.933, 0, 0, 15.867, 27, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param102", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, 15, 0, 0.567, 0, 0, 0.667, 15, 0, 0.8, 0, 2, 3.133, 0, 0, 3.233, 15, 0, 3.4, 0, 0, 3.5, 15, 0, 3.633, 0, 0, 4.567, 15, 0, 5.967, 0, 0, 6.067, 15, 0, 6.233, 0, 0, 6.333, 15, 0, 6.467, 0, 2, 8.8, 0, 0, 8.9, 15, 0, 9.033, 0, 0, 9.167, 15, 0, 9.3, 0, 0, 10.2, 15, 0, 11.633, 0, 0, 11.733, 15, 0, 11.867, 0, 0, 11.967, 15, 0, 12.133, 0, 2, 14.467, 0, 0, 14.567, 15, 0, 14.7, 0, 0, 14.8, 15, 0, 14.933, 0, 0, 15.867, 15, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation71", "Segments": [0, 0, 2, 0.333, 0, 0, 1.033, 18, 0, 1.733, -18, 0, 2.433, 18, 0, 3.133, 0, 0, 3.867, 18, 0, 4.567, -18, 0, 5.267, 18, 0, 5.967, 0, 0, 6.667, 18, 0, 7.4, -18, 0, 8.1, 18, 0, 8.8, 0, 0, 9.5, 18, 0, 10.2, -18, 0, 10.933, 18, 0, 11.633, 0, 0, 12.333, 18, 0, 13.033, -18, 0, 13.733, 18, 0, 14.467, 0, 0, 15.167, 18, 0, 15.867, -18, 0, 16.567, 18, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation72", "Segments": [0, 8.085, 2, 0.333, 8.085, 0, 0.633, 0, 0, 1.333, 19, 0, 2.033, -19, 0, 2.767, 19, 0, 3.467, 0, 0, 4.167, 19, 0, 4.867, -19, 0, 5.567, 19, 1, 5.7, 19, 5.834, 14.967, 5.967, 8.085, 1, 6.078, 2.351, 6.189, 0, 6.3, 0, 0, 7, 19, 0, 7.7, -19, 0, 8.4, 19, 0, 9.1, 0, 0, 9.833, 19, 0, 10.533, -19, 0, 11.233, 19, 1, 11.366, 19, 11.5, 15.323, 11.633, 8.085, 1, 11.733, 2.657, 11.833, 0, 11.933, 0, 0, 12.633, 19, 0, 13.367, -19, 0, 14.067, 19, 0, 14.767, 0, 0, 15.467, 19, 0, 16.167, -19, 0, 16.9, 19, 0, 17.233, 8.085]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation73", "Segments": [0, 11.915, 2, 0.333, 11.915, 0, 0.633, 0, 0, 1.333, 28, 0, 2.033, -28, 0, 2.767, 28, 0, 3.467, 0, 0, 4.167, 28, 0, 4.867, -28, 0, 5.567, 28, 1, 5.7, 28, 5.834, 22.057, 5.967, 11.915, 1, 6.078, 3.464, 6.189, 0, 6.3, 0, 0, 7, 28, 0, 7.7, -28, 0, 8.4, 28, 0, 9.1, 0, 0, 9.833, 28, 0, 10.533, -28, 0, 11.233, 28, 1, 11.366, 28, 11.5, 22.581, 11.633, 11.915, 1, 11.733, 3.915, 11.833, 0, 11.933, 0, 0, 12.633, 28, 0, 13.367, -28, 0, 14.067, 28, 0, 14.767, 0, 0, 15.467, 28, 0, 16.167, -28, 0, 16.9, 28, 0, 17.233, 11.915]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation74", "Segments": [0, 12.341, 2, 0.333, 12.341, 0, 0.633, 0, 0, 1.333, 29, 0, 2.033, -29, 0, 2.767, 29, 0, 3.467, 0, 0, 4.167, 29, 0, 4.867, -29, 0, 5.567, 29, 1, 5.7, 29, 5.834, 22.845, 5.967, 12.341, 1, 6.078, 3.587, 6.189, 0, 6.3, 0, 0, 7, 29, 0, 7.7, -29, 0, 8.4, 29, 0, 9.1, 0, 0, 9.833, 29, 0, 10.533, -29, 0, 11.233, 29, 1, 11.366, 29, 11.5, 23.388, 11.633, 12.341, 1, 11.733, 4.055, 11.833, 0, 11.933, 0, 0, 12.633, 29, 0, 13.367, -29, 0, 14.067, 29, 0, 14.767, 0, 0, 15.467, 29, 0, 16.167, -29, 0, 16.9, 29, 0, 17.233, 12.341]}, {"Target": "Parameter", "Id": "Param112", "Segments": [0, -30, 0, 0.333, 0, 2, 0.5, 0, 2, 1, 0, 2, 1.667, 0, 2, 2.333, 0, 2, 4.133, 0, 2, 4.4, 0, 2, 4.9, 0, 2, 6.567, 0, 2, 7.267, 0, 2, 7.833, 0, 2, 10, 0, 2, 11.333, 0, 2, 11.9, 0, 2, 12.767, 0, 2, 13, 0, 2, 13.6, 0, 2, 15.367, 0, 2, 16.167, 0, 0, 17.233, -30]}, {"Target": "Parameter", "Id": "Param272", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param293", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param273", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "ParamCheek", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param268", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param269", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param252", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param193", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param192", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param196", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param282", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param183", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param194", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param195", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param184", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param289", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param185", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param290", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param291", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param186", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param187", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param188", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param292", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param189", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param190", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param191", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param24", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param26", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param200", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param286", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param287", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param288", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param205", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param204", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param21", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param213", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param214", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param217", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param218", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param219", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param220", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param197", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param198", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param199", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param208", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param279", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param212", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param270", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param271", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param283", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param230", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param231", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param207", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param239", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param241", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param242", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param243", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param244", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param18", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param20", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param275", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param225", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param227", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param226", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param232", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param274", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param276", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param142", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param143", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param144", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param147", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param145", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param134", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param246", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param141", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param182", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param247", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param229", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param245", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param117", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param140", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param262", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param263", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param115", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param116", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param260", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param261", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param133", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param139", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param257", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param137", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param138", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param135", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param176", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param136", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param177", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param251", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param91", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param2", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param87", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param88", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param94", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param218mao2", "Segments": [0, 1, 0, 17.233, 1]}, {"Target": "Parameter", "Id": "Param211mao2", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param217mao2", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param202mao2", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param203mao2", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param204mao2", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param199mao2", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param200mao2", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param201mao2", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param208mao2", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param209mao2", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param210mao2", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param205mao2", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param206mao2", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param207mao2", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param213mao2", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param219mao2", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param264", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param107", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param284", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param148", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param146", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param110", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param111", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param248", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param249", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param206", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param114", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param113", "Segments": [0, 1, 0, 17.233, 1]}, {"Target": "Parameter", "Id": "Param253", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param254", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param158", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param149", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param150", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param151", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param255", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param256", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param153", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param152", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param155", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param154", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param181", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param180", "Segments": [0, 0.5, 0, 17.233, 0.5]}, {"Target": "Parameter", "Id": "Param156", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param157", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param159", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param160", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param119", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param265", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param118", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param120", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param121", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param161", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param259", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param123", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param124", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param130", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param131", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param178", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param179", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param125", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param126", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param127", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param128", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param171", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param173", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param172", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param122", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param170", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param129", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param132", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param250", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param258", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param162", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param169", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param174", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param175", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param163", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param164", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param165", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param166", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param167", "Segments": [0, 0, 0, 17.233, 0]}, {"Target": "Parameter", "Id": "Param168", "Segments": [0, 0, 0, 17.233, 0]}], "UserData": [{"Time": 0.0, "Value": ""}, {"Time": 0.333, "Value": ""}, {"Time": 16.733, "Value": ""}]}