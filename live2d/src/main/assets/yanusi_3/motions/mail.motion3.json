{"Version": 3, "Meta": {"Duration": 9.1, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 355, "TotalSegmentCount": 3125, "TotalPointCount": 3603, "UserDataCount": 3, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 2, 0.333, 0, 0, 0.533, 0.509, 0, 0.833, -1.91, 0, 1.4, 11.033, 1, 1.467, 11.033, 1.533, 10.659, 1.6, 10, 1, 1.811, 7.913, 2.022, 5.386, 2.233, 3.823, 1, 2.422, 2.424, 2.611, 0.69, 2.8, 0.509, 1, 3.078, 0.243, 3.355, 0.289, 3.633, 0, 1, 3.844, -0.22, 4.056, -4.649, 4.267, -4.649, 0, 4.967, 3.5, 0, 5.633, -3, 0, 6.133, 7, 1, 6.633, 7, 7.133, 6.414, 7.633, 4, 1, 7.755, 3.41, 7.878, -3.925, 8, -3.925, 0, 8.8, 0.632, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 2, 0.333, 0, 0, 0.533, 0.62, 1, 0.633, 0.62, 0.733, -12.443, 0.833, -14.218, 1, 1.022, -17.57, 1.211, -17.897, 1.4, -17.897, 1, 1.467, -17.897, 1.533, -17.896, 1.6, -17, 1, 1.811, -14.162, 2.022, -11.92, 2.233, -11.92, 0, 2.8, -25.48, 1, 2.989, -25.48, 3.178, -25.347, 3.367, -24, 1, 3.456, -23.366, 3.544, -14.756, 3.633, -13, 1, 4.111, -3.559, 4.589, 0, 5.067, 0, 0, 5.633, -6.14, 1, 5.8, -6.14, 5.966, -4.986, 6.133, 0, 1, 6.244, 3.324, 6.356, 7.746, 6.467, 7.746, 0, 6.8, -13, 0, 7.533, -4.843, 2, 8.067, -4.843, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 2, 0.333, 0, 0, 0.533, -0.488, 1, 0.633, -0.488, 0.733, 8.429, 0.833, 10.245, 1, 1.022, 13.674, 1.211, 14.08, 1.4, 14.08, 1, 1.467, 14.08, 1.533, 14.201, 1.6, 13.36, 1, 1.811, 10.698, 2.022, 8.064, 2.233, 8.064, 0, 2.8, 11.077, 0, 4.267, 6.084, 1, 4.356, 6.084, 4.444, 6.239, 4.533, 7.212, 1, 4.711, 9.159, 4.889, 12.913, 5.067, 14.08, 1, 5.256, 15.32, 5.444, 15.36, 5.633, 15.36, 1, 5.8, 15.36, 5.966, 13.896, 6.133, 12, 1, 6.244, 10.736, 6.356, 10.576, 6.467, 8.909, 1, 6.589, 7.075, 6.711, 1.658, 6.833, 1.658, 1, 6.9, 1.658, 6.966, 3.264, 7.033, 3.562, 1, 7.233, 4.455, 7.433, 4.758, 7.633, 5.707, 1, 7.755, 6.286, 7.878, 7.597, 8, 7.597, 0, 8.8, -0.488, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 2, 0.333, 1, 2, 0.467, 1, 0, 0.567, 0, 0, 0.667, 1, 2, 0.833, 1, 0, 1.4, 0.8, 0, 1.6, 1, 2, 1.833, 1, 0, 1.967, 0, 0, 2.1, 0.9, 2, 2.233, 0.9, 2, 2.367, 0.9, 0, 2.467, 0, 1, 2.5, 0, 2.534, 0.86, 2.567, 0.9, 1, 2.645, 0.993, 2.722, 1, 2.8, 1, 0, 3.367, 0.8, 0, 3.633, 1, 0, 4.267, 0.7, 2, 4.533, 0.7, 0, 5.067, 0, 2, 5.633, 0, 2, 5.767, 0, 0, 5.933, 0.9, 2, 6.567, 0.9, 0, 6.833, 0, 2, 7.167, 0, 2, 7.633, 0, 1, 7.833, 0, 8.033, 0.661, 8.233, 0.8, 1, 8.522, 1.001, 8.811, 1, 9.1, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 2, 0.333, 0, 2, 0.833, 0, 2, 1.4, 0, 2, 1.6, 0, 2, 2.233, 0, 2, 2.8, 0, 2, 3.367, 0, 2, 3.633, 0, 2, 4.267, 0, 2, 4.533, 0, 0, 5.067, 1, 2, 5.633, 1, 2, 6.133, 1, 2, 7.167, 1, 2, 7.633, 1, 0, 8.233, 0, 2, 9.1, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 2, 0.333, 1, 2, 0.467, 1, 0, 0.567, 0, 0, 0.667, 1, 2, 0.833, 1, 0, 1.4, 0.8, 0, 1.6, 1, 2, 1.833, 1, 0, 1.967, 0, 0, 2.1, 0.9, 2, 2.233, 0.9, 2, 2.367, 0.9, 0, 2.467, 0, 1, 2.5, 0, 2.534, 0.86, 2.567, 0.9, 1, 2.645, 0.993, 2.722, 1, 2.8, 1, 0, 3.367, 0.8, 0, 3.633, 1, 0, 4.267, 0.7, 2, 4.533, 0.7, 0, 5.067, 0, 2, 5.633, 0, 2, 5.767, 0, 0, 5.933, 0.8, 2, 6.567, 0.8, 0, 6.833, 0, 2, 7.167, 0, 2, 7.633, 0, 1, 7.833, 0, 8.033, 0.826, 8.233, 0.9, 1, 8.522, 1.007, 8.811, 1, 9.1, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 2, 0.333, 0, 2, 0.833, 0, 2, 1.4, 0, 2, 1.6, 0, 2, 2.233, 0, 2, 2.8, 0, 2, 3.367, 0, 2, 3.633, 0, 2, 4.267, 0, 2, 4.533, 0, 0, 5.067, 1, 2, 5.633, 1, 2, 6.133, 1, 2, 7.167, 1, 2, 7.633, 1, 0, 8.233, 0, 2, 9.1, 0]}, {"Target": "Parameter", "Id": "Param76", "Segments": [0, 0, 2, 0.333, 0, 0, 0.833, -30, 2, 1.4, -30, 2, 7.633, -30, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param273", "Segments": [0, 0, 2, 0.333, 0, 2, 3.633, 0, 0, 6.133, 1, 2, 7.167, 1, 2, 7.633, 1, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 2, 0.333, 0, 2, 0.833, 0, 0, 2, -0.2, 0, 2.233, 0, 0, 2.3, -0.6, 2, 2.467, -0.6, 0, 2.567, 0, 2, 2.8, 0, 2, 3.367, 0, 2, 3.633, 0, 2, 4.267, 0, 2, 4.533, 0, 2, 5.067, 0, 2, 5.633, 0, 0, 6.133, -0.2, 0, 7.167, 0, 2, 7.633, 0, 2, 9.1, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 2, 0.333, 0, 1, 0.5, 0, 0.666, 0.052, 0.833, 0.1, 1, 1.222, 0.212, 1.611, 0.287, 2, 0.4, 1, 2.078, 0.423, 2.155, 0.5, 2.233, 0.5, 0, 2.3, 0, 2, 2.467, 0, 1, 2.5, 0, 2.534, 0.361, 2.567, 0.4, 1, 2.645, 0.491, 2.722, 0.5, 2.8, 0.5, 2, 3.367, 0.5, 2, 3.633, 0.5, 2, 4.267, 0.5, 2, 4.533, 0.5, 2, 5.067, 0.5, 2, 5.633, 0.5, 2, 6.133, 0.5, 2, 7.167, 0.5, 2, 7.633, 0.5, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 2, 0.333, 0, 1, 0.5, 0, 0.666, -0.206, 0.833, -0.228, 1, 1.3, -0.291, 1.766, -0.3, 2.233, -0.3, 2, 2.8, -0.3, 2, 3.367, -0.3, 2, 3.633, -0.3, 0, 4.267, 0, 2, 4.533, 0, 2, 5.067, 0, 2, 5.633, 0, 0, 6.133, -0.3, 2, 7.167, -0.3, 2, 7.633, -0.3, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 2, 0.333, 0, 2, 0.567, 0, 0, 0.833, -0.2, 2, 2.233, -0.2, 2, 2.8, -0.2, 2, 3.367, -0.2, 1, 3.456, -0.2, 3.544, -0.134, 3.633, -0.1, 1, 3.844, -0.02, 4.056, 0, 4.267, 0, 2, 4.533, 0, 2, 5.067, 0, 2, 5.633, 0, 0, 6.133, -0.2, 2, 7.167, -0.2, 2, 7.633, -0.2, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 2, 0.333, 0, 1, 0.5, 0, 0.666, -0.198, 0.833, -0.222, 1, 1.3, -0.29, 1.766, -0.3, 2.233, -0.3, 2, 2.8, -0.3, 2, 3.367, -0.3, 2, 3.633, -0.3, 0, 4.267, 0, 2, 4.533, 0, 2, 5.067, 0, 2, 5.633, 0, 0, 6.133, -0.3, 2, 7.167, -0.3, 2, 7.633, -0.3, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 2, 0.333, 0, 2, 0.567, 0, 0, 0.833, -0.2, 2, 2.233, -0.2, 2, 2.8, -0.2, 2, 3.367, -0.2, 1, 3.456, -0.2, 3.544, -0.134, 3.633, -0.1, 1, 3.844, -0.02, 4.056, 0, 4.267, 0, 2, 4.533, 0, 2, 5.067, 0, 2, 5.633, 0, 0, 6.133, -0.2, 2, 7.167, -0.2, 2, 7.633, -0.2, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, -1, 2, 0.333, -1, 0, 0.833, 0, 2, 2.233, 0, 2, 2.8, 0, 2, 3.367, 0, 2, 3.633, 0, 0, 4.267, -0.7, 2, 4.533, -0.7, 0, 5.067, -0.4, 2, 5.633, -0.4, 0, 6.133, 0, 2, 7.167, 0, 2, 7.633, 0, 0, 9.1, -1]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, -1, 2, 0.333, -1, 0, 0.833, 0, 2, 2.233, 0, 2, 2.8, 0, 2, 3.367, 0, 2, 3.633, 0, 0, 4.267, -0.7, 2, 4.533, -0.7, 0, 5.067, -0.4, 2, 5.633, -0.4, 0, 6.133, 0, 2, 7.167, 0, 2, 7.633, 0, 0, 9.1, -1]}, {"Target": "Parameter", "Id": "Param", "Segments": [0, -30, 2, 0.333, -30, 0, 0.833, 0, 2, 1.4, 0, 2, 1.5, 0, 2, 1.6, 0, 2, 2.233, 0, 2, 2.8, 0, 2, 3.367, 0, 2, 3.633, 0, 2, 4.267, 0, 2, 4.533, 0, 2, 5.067, 0, 2, 5.633, 0, 2, 6.133, 0, 2, 7.167, 0, 2, 7.633, 0, 0, 9.1, -30]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 1, 2, 0.333, 1, 1, 0.366, 1, 0.4, 0.837, 0.433, 0.3, 1, 0.455, -0.058, 0.478, -0.4, 0.5, -0.4, 0, 0.633, 0.7, 1, 0.7, 0.7, 0.766, 0.259, 0.833, 0, 1, 0.978, -0.562, 1.122, -0.7, 1.267, -0.7, 1, 1.311, -0.7, 1.356, -0.096, 1.4, 0.2, 1, 1.433, 0.422, 1.467, 0.4, 1.5, 0.4, 0, 1.6, -0.5, 1, 1.622, -0.5, 1.645, -0.47, 1.667, -0.2, 1, 1.7, 0.205, 1.734, 0.6, 1.767, 0.6, 0, 1.867, 0.2, 0, 2.1, 0.3, 0, 2.233, 0, 2, 2.8, 0, 2, 3.367, 0, 2, 3.633, 0, 2, 4.267, 0, 1, 4.356, 0, 4.444, 0.114, 4.533, 0.4, 1, 4.566, 0.507, 4.6, 0.6, 4.633, 0.6, 0, 4.833, -0.6, 1, 4.878, -0.6, 4.922, -0.607, 4.967, -0.4, 1, 5.022, -0.141, 5.078, 0.3, 5.133, 0.3, 2, 5.267, 0.3, 1, 5.322, 0.3, 5.378, 0.333, 5.433, 0.4, 1, 5.489, 0.467, 5.544, 0.5, 5.6, 0.5, 0, 5.8, -1, 0, 6, 0.4, 0, 6.4, 0.3, 1, 6.444, 0.3, 6.489, 0.452, 6.533, 0.5, 1, 6.744, 0.727, 6.956, 0.8, 7.167, 0.8, 2, 7.633, 0.8, 0, 9.1, 1]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 2, 0.333, 0, 1, 0.366, 0, 0.4, 0.082, 0.433, 0.2, 1, 0.455, 0.278, 0.478, 0.333, 0.5, 0.4, 1, 0.544, 0.533, 0.589, 0.6, 0.633, 0.6, 0, 0.833, 0, 0, 1.267, 0.5, 2, 1.4, 0.5, 0, 1.5, 0.8, 0, 1.6, 0, 1, 1.622, 0, 1.645, 0.236, 1.667, 0.3, 1, 1.7, 0.397, 1.734, 0.4, 1.767, 0.4, 0, 1.867, 0.2, 2, 2.1, 0.2, 0, 2.233, 0, 2, 2.8, 0, 2, 3.367, 0, 2, 3.633, 0, 2, 4.267, 0, 1, 4.356, 0, 4.444, 0.029, 4.533, 0.2, 1, 4.566, 0.264, 4.6, 0.6, 4.633, 0.6, 0, 4.833, 0.3, 0, 4.967, 0.6, 0, 5.133, 0.2, 0, 5.267, 0.6, 0, 5.433, 0.3, 1, 5.489, 0.3, 5.544, 0.517, 5.6, 0.6, 1, 5.667, 0.699, 5.733, 0.7, 5.8, 0.7, 0, 5.9, 0.3, 0, 6, 0.5, 0, 6.133, 0.3, 0, 6.2, 0.7, 1, 6.267, 0.7, 6.333, 0.6, 6.4, 0.4, 1, 6.444, 0.267, 6.489, 0.2, 6.533, 0.2, 2, 6.8, 0.2, 0, 7.167, 0, 2, 7.633, 0, 2, 9.1, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 2, 0.333, 0, 0, 0.733, 10.192, 0, 1.033, -14.802, 0, 1.6, 13.486, 0, 1.7, 12.158, 0, 1.8, 13.486, 1, 2.011, 13.486, 2.222, -1.951, 2.433, -4, 1, 2.622, -5.834, 2.811, -5.213, 3, -7, 1, 3.189, -8.787, 3.378, -30, 3.567, -30, 1, 3.656, -30, 3.744, -30.683, 3.833, -26.948, 1, 4.044, -18.078, 4.256, -4, 4.467, -4, 1, 4.734, -4, 5, -5.064, 5.267, -12, 1, 5.456, -16.913, 5.644, -26, 5.833, -26, 0, 6.333, -3, 1, 6.433, -3, 6.533, -2.131, 6.633, -5.167, 1, 6.878, -12.589, 7.122, -29.794, 7.367, -29.794, 0, 7.833, -26.948, 0, 8.2, -28.311, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 2, 0.333, 0, 0, 0.733, 9.38, 0, 1.033, 1.64, 0, 1.6, 9.38, 0, 1.7, 9.02, 0, 1.8, 9.38, 0, 2.433, -24, 0, 3, 4, 0, 3.567, 2.459, 1, 3.656, 2.459, 3.744, 2.542, 3.833, 4, 1, 4.044, 7.462, 4.256, 10.36, 4.467, 10.36, 2, 4.733, 10.36, 0, 5.267, 15, 1, 5.456, 15, 5.644, 14.681, 5.833, 7.483, 1, 6, 1.133, 6.166, -15.686, 6.333, -15.686, 0, 6.633, -13.815, 0, 7.367, -29.83, 1, 7.522, -29.83, 7.678, -30.892, 7.833, -28, 1, 8.255, -20.151, 8.678, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 2, 0.333, 0, 0, 0.833, -0.395, 0, 1.4, 3.78, 0, 1.5, 3.594, 0, 1.6, 3.78, 0, 2.233, -2.25, 0, 2.8, 2.069, 0, 3.367, -2, 2, 3.633, -2, 2, 4.267, -2, 2, 4.533, -2, 2, 5.067, -2, 2, 5.633, -2, 2, 6.133, -2, 0, 7.167, -0.153, 1, 7.322, -0.153, 7.478, -1.354, 7.633, -1.998, 1, 7.733, -2.413, 7.833, -2.357, 7.933, -2.357, 0, 8.833, 0.181, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param267", "Segments": [0, 0, 2, 0.333, 0, 0, 0.533, -0.266, 0, 0.833, 6.194, 0, 1.4, -0.913, 1, 1.467, -0.913, 1.533, -0.748, 1.6, 0, 1, 1.811, 2.37, 2.022, 6.831, 2.233, 7.672, 1, 2.422, 8.424, 2.611, 8.32, 2.8, 8.32, 0, 3.367, 7, 2, 3.633, 7, 2, 4.267, 7, 2, 4.533, 7, 0, 5.067, -2.891, 1, 5.256, -2.891, 5.444, -3.019, 5.633, -2, 1, 5.8, -1.101, 5.966, 7, 6.133, 7, 0, 7.167, 5.221, 0, 7.633, 6.313, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param3", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, -0.779, 0, 0.833, 4.403, 0, 1.4, -5.989, 1, 1.467, -5.989, 1.533, -6.113, 1.6, -4.718, 1, 1.811, -0.3, 2.022, 3.763, 2.233, 3.763, 0, 2.8, -9.244, 0, 3.367, 0.5, 0, 3.633, -0.205, 2, 4.267, -0.205, 2, 4.533, -0.205, 0, 5.167, 10.277, 1, 5.322, 10.277, 5.478, 9.902, 5.633, 7.734, 1, 5.8, 5.413, 5.966, -0.369, 6.133, -1.784, 1, 6.478, -4.708, 6.822, -5.266, 7.167, -5.266, 2, 7.633, -5.266, 0, 7.867, -5.84, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param4", "Segments": [0, 0, 2, 0.333, 0, 0, 0.5, -2.183, 1, 0.611, -2.183, 0.722, 4.235, 0.833, 6.409, 1, 0.911, 7.931, 0.989, 7.698, 1.067, 7.698, 0, 1.4, 2.652, 0, 1.6, 4.117, 0, 2.233, -8, 0, 2.8, 3.697, 0, 3.367, 0, 2, 3.633, 0, 0, 4.267, 8.242, 0, 4.533, 6.27, 0, 5.067, 14.986, 1, 5.256, 14.986, 5.444, 15.201, 5.633, 12.825, 1, 5.8, 10.73, 5.966, -7.568, 6.133, -7.568, 0, 6.433, -5.843, 0, 7.167, -14.56, 0, 7.633, -13.499, 0, 7.933, -14.489, 0, 8.8, 1.316, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param266", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, -0.896, 0, 0.8, 6.908, 0, 1.4, -7.826, 1, 1.467, -7.826, 1.533, -7.558, 1.6, -4.736, 1, 1.811, 4.201, 2.022, 10.598, 2.233, 10.598, 0, 2.8, -8.019, 0, 3.367, 5.239, 2, 3.633, 5.239, 0, 4.267, 3.945, 2, 4.533, 3.945, 0, 5.067, 9.975, 1, 5.256, 9.975, 5.444, 10.07, 5.633, 9.298, 1, 5.8, 8.617, 5.966, 1.136, 6.133, -2.612, 1, 6.478, -10.359, 6.822, -13.93, 7.167, -13.93, 0, 7.633, -11.094, 0, 8, -12.633, 0, 8.8, 1.419, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param5", "Segments": [0, 0, 2, 0.333, 0, 1, 0.4, 0, 0.466, 0.276, 0.533, -0.669, 1, 0.633, -2.087, 0.733, -21.35, 0.833, -21.35, 1, 1.022, -21.35, 1.211, -19.118, 1.4, -16.35, 1, 1.467, -15.373, 1.533, -15.35, 1.6, -15.35, 0, 2.233, -30, 0, 2.8, -22, 2, 3.367, -22, 2, 3.633, -22, 0, 5.633, -26, 1, 5.8, -26, 5.966, -26.154, 6.133, -25.68, 1, 6.355, -25.048, 6.578, -7.128, 6.8, -7.128, 1, 6.922, -7.128, 7.045, -7.07, 7.167, -8.131, 1, 7.322, -9.482, 7.478, -12, 7.633, -12, 1, 8.022, -12, 8.411, -5.074, 8.8, -0.669, 1, 8.9, 0.464, 9, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param6", "Segments": [0, 0, 2, 0.333, 0, 0, 0.533, -1.184, 0, 0.833, 11, 0, 1.4, -13, 1, 1.467, -13, 1.533, -13.689, 1.6, -12, 1, 1.811, -6.652, 2.022, 23.234, 2.233, 27, 1, 2.422, 30.37, 2.611, 30, 2.8, 30, 2, 3.367, 30, 2, 3.633, 30, 1, 3.933, 30, 4.233, 28.405, 4.533, 21, 1, 4.9, 11.95, 5.266, 4, 5.633, 4, 1, 5.8, 4, 5.966, 6.238, 6.133, 11.418, 1, 6.355, 18.325, 6.578, 22.165, 6.8, 22.165, 1, 6.922, 22.165, 7.045, 22.411, 7.167, 21.133, 1, 7.322, 19.508, 7.478, 12, 7.633, 12, 0, 7.933, 13.004, 0, 8.8, -0.776, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 2, 0.333, 0, 0, 0.467, 0.1, 0, 0.833, 0, 2, 1.4, 0, 2, 1.5, 0, 2, 1.6, 0, 2, 2.233, 0, 0, 2.8, 0.5, 2, 3.367, 0.5, 2, 3.633, 0.5, 0, 4.267, 0, 2, 4.533, 0, 2, 5.067, 0, 2, 5.633, 0, 2, 6.133, 0, 2, 7.167, 0, 2, 7.633, 0, 2, 9.1, 0]}, {"Target": "Parameter", "Id": "Param8", "Segments": [0, 0, 2, 0.333, 0, 2, 0.833, 0, 2, 1.4, 0, 2, 1.5, 0, 2, 1.6, 0, 2, 2.233, 0, 2, 2.8, 0, 2, 3.367, 0, 2, 3.633, 0, 2, 4.267, 0, 2, 4.533, 0, 0, 5.067, 13, 2, 5.633, 13, 0, 6.133, 0, 2, 7.167, 0, 2, 7.633, 0, 2, 9.1, 0]}, {"Target": "Parameter", "Id": "Param25", "Segments": [0, 0, 2, 0.333, 0, 2, 0.833, 0, 2, 1.4, 0, 2, 1.5, 0, 2, 1.6, 0, 2, 2.233, 0, 2, 2.8, 0, 2, 3.367, 0, 2, 3.633, 0, 2, 4.267, 0, 2, 4.533, 0, 0, 5.067, 20, 2, 5.633, 20, 0, 6.133, 0, 2, 7.167, 0, 2, 7.633, 0, 2, 9.1, 0]}, {"Target": "Parameter", "Id": "Param201", "Segments": [0, 0, 2, 0.333, 0, 2, 0.467, 0, 0, 0.5, 1, 2, 0.833, 1, 2, 1.4, 1, 2, 1.5, 1, 2, 1.6, 1, 2, 2.233, 1, 2, 2.8, 1, 2, 3.367, 1, 2, 3.633, 1, 2, 4.267, 1, 2, 4.533, 1, 2, 5.067, 1, 2, 5.633, 1, 2, 6.133, 1, 2, 7.167, 1, 2, 7.633, 1, 2, 8.033, 1, 0, 8.067, 0, 2, 9.1, 0]}, {"Target": "Parameter", "Id": "Param202", "Segments": [0, 0, 2, 0.333, 0, 2, 0.467, 0, 0, 0.5, 1, 2, 0.833, 1, 2, 1.4, 1, 2, 1.5, 1, 2, 1.6, 1, 2, 2.233, 1, 2, 2.8, 1, 2, 3.367, 1, 2, 3.633, 1, 2, 4.267, 1, 2, 4.533, 1, 2, 5.067, 1, 2, 5.633, 1, 2, 6.133, 1, 2, 7.167, 1, 2, 7.633, 1, 2, 8.033, 1, 0, 8.067, 0, 2, 9.1, 0]}, {"Target": "Parameter", "Id": "Param209", "Segments": [0, 0, 2, 0.333, 0, 2, 0.467, 0, 0, 0.5, 1, 2, 0.833, 1, 2, 1.4, 1, 2, 1.5, 1, 2, 1.6, 1, 2, 2.233, 1, 2, 2.8, 1, 2, 3.367, 1, 2, 3.633, 1, 2, 4.267, 1, 2, 4.533, 1, 2, 5.067, 1, 2, 5.633, 1, 2, 6.133, 1, 2, 7.167, 1, 2, 7.633, 1, 2, 8.033, 1, 0, 8.067, 0, 2, 9.1, 0]}, {"Target": "Parameter", "Id": "Param214", "Segments": [0, 0, 2, 0.333, 0, 2, 0.467, 0, 2, 0.5, 0, 2, 0.833, 0, 2, 1.4, 0, 2, 1.5, 0, 2, 1.6, 0, 2, 2.233, 0, 2, 2.8, 0, 2, 3.367, 0, 2, 3.633, 0, 2, 3.8, 0, 0, 3.833, 1, 2, 4.267, 1, 2, 4.533, 1, 2, 4.733, 1, 0, 4.767, 0, 2, 5.067, 0, 2, 5.633, 0, 2, 6.133, 0, 2, 7.167, 0, 2, 7.633, 0, 2, 9.1, 0]}, {"Target": "Parameter", "Id": "Param215", "Segments": [0, 0, 2, 0.333, 0, 0, 0.5, 1, 2, 0.833, 1, 2, 1.4, 1, 2, 1.5, 1, 2, 1.6, 1, 2, 2.233, 1, 2, 2.8, 1, 2, 3.367, 1, 2, 3.633, 1, 2, 3.8, 1, 0, 3.833, 0, 2, 4.267, 0, 2, 4.533, 0, 2, 4.733, 0, 0, 4.767, 1, 2, 5.067, 1, 2, 5.633, 1, 2, 6.133, 1, 2, 7.167, 1, 2, 7.633, 1, 2, 8.033, 1, 0, 8.067, 0, 2, 9.1, 0]}, {"Target": "Parameter", "Id": "Param9", "Segments": [0, 0, 2, 0.333, 0, 0, 0.533, -6.86, 1, 0.589, -6.86, 0.644, -4.041, 0.7, -4.016, 1, 0.789, -3.977, 0.878, -3.98, 0.967, -3.98, 1, 1.111, -3.98, 1.256, -5.567, 1.4, -7.229, 1, 1.467, -7.996, 1.533, -8, 1.6, -8, 0, 2.233, -3.98, 0, 2.8, -7.481, 0, 3.367, -6.86, 2, 3.633, -6.86, 0, 4.267, -2.66, 2, 4.533, -2.66, 0, 5.067, -6.46, 0, 5.633, -3.667, 0, 7.167, -4.74, 2, 7.633, -4.74, 0, 8, -9.21, 0, 8.8, 0.19, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param10", "Segments": [0, 0, 2, 0.333, 0, 1, 0.411, 0, 0.489, 5.981, 0.567, 9.1, 1, 0.7, 14.446, 0.834, 15.52, 0.967, 15.52, 1, 1.111, 15.52, 1.256, 15.482, 1.4, 15.22, 1, 1.467, 15.099, 1.533, 14.665, 1.6, 14.665, 1, 1.811, 14.665, 2.022, 14.742, 2.233, 15, 1, 2.422, 15.231, 2.611, 15.791, 2.8, 15.84, 1, 2.989, 15.889, 3.178, 15.88, 3.367, 15.88, 2, 3.633, 15.88, 0, 4.267, 15.96, 2, 4.533, 15.96, 0, 5.067, -0.32, 0, 5.833, 4, 0, 6.333, 0.28, 0, 7.167, 1, 2, 7.633, 1, 0, 7.8, 0.455, 0, 8.033, 9.262, 1, 8.044, 9.262, 8.056, 7.868, 8.067, 7.53, 1, 8.111, 6.177, 8.156, 4.669, 8.2, 4, 1, 8.411, 0.82, 8.622, -0.39, 8.833, -0.39, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param11", "Segments": [0, 0, 2, 0.333, 0, 0, 0.567, -0.498, 0, 0.967, 30, 0, 1.4, 28.875, 2, 1.6, 28.875, 2, 2.233, 28.875, 2, 2.8, 28.875, 2, 3.367, 28.875, 2, 3.633, 28.875, 2, 4.267, 28.875, 2, 4.533, 28.875, 2, 5.067, 28.875, 2, 6.033, 28.875, 2, 6.533, 28.875, 1, 6.655, 28.875, 6.778, 28.87, 6.9, 29.091, 1, 6.989, 29.252, 7.078, 30, 7.167, 30, 2, 7.633, 30, 0, 8.7, -0.449, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param22", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, 1, 2, 8.667, 1, 0, 8.867, 0, 2, 9.1, 0]}, {"Target": "Parameter", "Id": "Param13", "Segments": [0, 0, 2, 0.333, 0, 2, 0.967, 0, 2, 1.4, 0, 2, 1.5, 0, 2, 1.6, 0, 1, 1.811, 0, 2.022, -1.98, 2.233, -8, 1, 2.422, -13.387, 2.611, -24.025, 2.8, -27, 1, 2.989, -29.975, 3.178, -30, 3.367, -30, 2, 3.633, -30, 0, 4.267, -13.38, 2, 4.533, -13.38, 1, 4.711, -13.38, 4.889, -2.934, 5.067, 0, 1, 5.256, 3.118, 5.444, 3, 5.633, 3, 0, 6.133, 0, 2, 7.167, 0, 2, 7.633, 0, 2, 9.1, 0]}, {"Target": "Parameter", "Id": "Param14", "Segments": [0, 0, 2, 0.333, 0, 0, 0.633, -30, 1, 0.678, -30, 0.722, -29.81, 0.767, -22.979, 1, 0.834, -12.733, 0.9, 0, 0.967, 0, 2, 1.4, 0, 2, 1.5, 0, 2, 1.6, 0, 2, 2.233, 0, 2, 2.8, 0, 2, 3.367, 0, 2, 3.633, 0, 2, 4.267, 0, 2, 4.533, 0, 2, 5.067, 0, 2, 5.633, 0, 2, 6.133, 0, 2, 7.167, 0, 2, 7.633, 0, 0, 8.067, -22, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param210", "Segments": [0, 0, 2, 0.333, 0, 0, 0.5, 13.859, 1, 0.511, 13.859, 0.522, 10.81, 0.533, 9.565, 1, 0.544, 8.32, 0.556, 7.476, 0.567, 7.067, 1, 0.611, 5.431, 0.656, 3.676, 0.7, 2.66, 1, 0.789, 0.628, 0.878, 0, 0.967, 0, 2, 1.4, 0, 2, 1.5, 0, 2, 1.6, 0, 0, 2.233, -1, 0, 2.8, 0.98, 0, 3.367, -3.16, 2, 3.633, -3.16, 1, 3.711, -3.16, 3.789, -0.445, 3.867, 0.606, 1, 4, 2.408, 4.134, 2.66, 4.267, 2.66, 0, 4.533, 1.4, 0, 4.767, 7.067, 1, 4.867, 7.067, 4.967, 0.701, 5.067, -0.02, 1, 5.256, -1.382, 5.444, -1.46, 5.633, -1.46, 1, 5.8, -1.46, 5.966, 1.481, 6.133, 2.06, 1, 6.478, 3.256, 6.822, 3.38, 7.167, 3.38, 2, 7.633, 3.38, 0, 8.167, 18, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param211", "Segments": [0, 0, 2, 0.333, 0, 1, 0.433, 0, 0.533, -0.591, 0.633, -6, 1, 0.744, -12.01, 0.856, -20, 0.967, -20, 0, 1.4, -6.42, 2, 1.5, -6.42, 2, 1.6, -6.42, 0, 2.233, 7.58, 0, 2.8, -1.6, 0, 3.2, 1.035, 1, 3.256, 1.035, 3.311, 1.037, 3.367, -0.4, 1, 3.456, -2.7, 3.544, -10.088, 3.633, -10.96, 1, 3.844, -13.03, 4.056, -13.36, 4.267, -13.36, 2, 4.533, -13.36, 0, 5.067, -6.7, 0, 5.633, -18.08, 0, 6.133, 2.54, 0, 7.167, -3.88, 2, 7.633, -3.88, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param212", "Segments": [0, 0, 2, 0.333, 0, 2, 0.967, 0, 2, 1.4, 0, 2, 1.5, 0, 2, 1.6, 0, 2, 2.233, 0, 0, 2.8, 15, 2, 3.367, 15, 2, 3.633, 15, 2, 4.267, 15, 2, 4.533, 15, 1, 4.711, 15, 4.889, 12.403, 5.067, 10.02, 1, 5.256, 7.488, 5.444, 7, 5.633, 7, 0, 6.133, 15, 2, 7.167, 15, 2, 7.633, 15, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param228", "Segments": [0, 0, 2, 0.333, 0, 0, 0.533, -13.644, 0, 0.967, 0, 2, 1.4, 0, 2, 1.5, 0, 2, 1.6, 0, 1, 1.811, 0, 2.022, -9.004, 2.233, -19, 1, 2.422, -27.944, 2.611, -30, 2.8, -30, 2, 3.367, -30, 2, 3.633, -30, 0, 4.267, -26, 2, 4.533, -26, 0, 5.067, -9, 1, 5.256, -9, 5.444, -11.265, 5.633, -14, 1, 5.8, -16.414, 5.966, -19.308, 6.133, -20.04, 1, 6.478, -21.553, 6.822, -21.78, 7.167, -21.78, 2, 7.633, -21.78, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param270", "Segments": [0, 0, 2, 0.333, 0, 2, 1.3, 0, 0, 1.4, 1, 2, 1.5, 1, 2, 1.6, 1, 2, 2.233, 1, 2, 2.8, 1, 2, 3.367, 1, 0, 3.633, 0, 2, 4.267, 0, 2, 4.533, 0, 2, 5.067, 0, 2, 5.633, 0, 2, 6.133, 0, 2, 7.167, 0, 2, 7.633, 0, 2, 9.1, 0]}, {"Target": "Parameter", "Id": "Param271", "Segments": [0, 0, 2, 0.333, 0, 0, 1.4, -28, 2, 1.5, -28, 2, 1.6, -28, 1, 1.811, -28, 2.022, -20.221, 2.233, 0, 1, 2.422, 18.093, 2.611, 30, 2.8, 30, 2, 3.367, 30, 2, 3.633, 30, 2, 4.267, 30, 2, 4.533, 30, 2, 5.067, 30, 2, 5.633, 30, 2, 6.133, 30, 2, 7.167, 30, 2, 7.633, 30, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param221", "Segments": [0, 0, 2, 0.333, 0, 2, 1.133, 0, 2, 1.4, 0, 2, 1.5, 0, 2, 1.6, 0, 2, 1.833, 0, 0, 1.867, 1, 2, 2.233, 1, 2, 2.8, 1, 2, 3.367, 1, 2, 3.633, 1, 2, 4.267, 1, 2, 4.533, 1, 2, 5.067, 1, 2, 5.633, 1, 2, 6.133, 1, 2, 7.167, 1, 2, 7.633, 1, 2, 8.267, 1, 0, 8.3, 0, 2, 9.1, 0]}, {"Target": "Parameter", "Id": "Param223", "Segments": [0, 0, 2, 0.333, 0, 2, 1.133, 0, 2, 1.4, 0, 2, 1.5, 0, 2, 1.6, 0, 2, 1.833, 0, 0, 1.867, 1, 2, 2.233, 1, 2, 2.8, 1, 2, 3.367, 1, 2, 3.633, 1, 2, 4.267, 1, 2, 4.533, 1, 2, 5.067, 1, 2, 5.633, 1, 2, 6.133, 1, 2, 7.167, 1, 2, 7.633, 1, 2, 8.267, 1, 0, 8.3, 0, 2, 9.1, 0]}, {"Target": "Parameter", "Id": "Param237", "Segments": [0, 0, 2, 0.333, 0, 2, 0.833, 0, 2, 1.4, 0, 2, 1.5, 0, 2, 1.6, 0, 2, 2.233, 0, 2, 2.8, 0, 2, 3.367, 0, 2, 3.633, 0, 2, 3.833, 0, 0, 3.867, 1, 2, 4.267, 1, 2, 4.533, 1, 2, 4.733, 1, 0, 4.767, 0, 2, 5.067, 0, 2, 5.633, 0, 2, 5.833, 0, 0, 5.867, 1, 2, 6.133, 1, 2, 7.167, 1, 2, 7.633, 1, 2, 8.267, 1, 0, 8.3, 0, 2, 9.1, 0]}, {"Target": "Parameter", "Id": "Param240", "Segments": [0, 0, 2, 0.333, 0, 2, 0.833, 0, 0, 1.4, 1, 2, 1.5, 1, 2, 1.6, 1, 2, 2.233, 1, 2, 2.8, 1, 2, 3.367, 1, 2, 3.633, 1, 2, 3.833, 1, 0, 3.867, 0, 2, 4.267, 0, 2, 4.533, 0, 2, 4.733, 0, 0, 4.767, 1, 2, 5.067, 1, 2, 5.633, 1, 2, 5.833, 1, 0, 5.867, 0, 2, 6.133, 0, 2, 7.167, 0, 2, 7.633, 0, 2, 9.1, 0]}, {"Target": "Parameter", "Id": "Param15", "Segments": [0, 0, 2, 0.333, 0, 0, 0.567, 5.897, 1, 0.656, 5.897, 0.744, 5.899, 0.833, 5.46, 1, 1.022, 4.527, 1.211, 3.62, 1.4, 3.62, 0, 1.5, 4.22, 0, 1.6, 3.5, 1, 1.811, 3.5, 2.022, 9.696, 2.233, 9.88, 1, 2.422, 10.045, 2.611, 10, 2.8, 10, 2, 3.367, 10, 2, 3.633, 10, 0, 4.267, 12.58, 2, 4.533, 12.58, 0, 5.067, 15.5, 0, 5.633, 8, 0, 6.133, 11, 0, 7.167, 7.34, 2, 7.633, 7.34, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param16", "Segments": [0, 0, 2, 0.333, 0, 1, 0.5, 0, 0.666, -3.402, 0.833, -8.28, 1, 1.022, -13.808, 1.211, -15.82, 1.4, -15.82, 0, 1.5, -14.04, 0, 1.6, -15.82, 0, 2.233, 0, 2, 2.8, 0, 2, 3.367, 0, 2, 3.633, 0, 2, 4.267, 0, 2, 4.533, 0, 0, 5.067, 0.12, 0, 5.9, -1.38, 0, 6.4, -0.06, 2, 7.167, -0.06, 0, 7.633, 10.553, 1, 7.855, 10.553, 8.078, 9.108, 8.3, 5.06, 1, 8.489, 1.62, 8.678, -0.848, 8.867, -0.848, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param19", "Segments": [0, 0, 2, 0.333, 0, 2, 0.833, 0, 2, 1.4, 0, 2, 1.5, 0, 2, 1.6, 0, 1, 1.811, 0, 2.022, -9.256, 2.233, -22, 1, 2.422, -33.403, 2.611, -45.141, 2.8, -52, 1, 2.989, -58.859, 3.178, -60, 3.367, -60, 2, 3.633, -60, 0, 4.267, -30, 2, 4.533, -30, 2, 5.067, -30, 2, 5.633, -30, 2, 6.133, -30, 2, 7.167, -30, 2, 7.633, -30, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param224", "Segments": [0, 0, 2, 0.333, 0, 2, 0.833, 0, 2, 1.4, 0, 2, 1.5, 0, 2, 1.6, 0, 2, 2.233, 0, 2, 2.8, 0, 0, 3.367, 0.3, 2, 3.633, 0.3, 0, 4.267, 0, 2, 4.533, 0, 2, 5.067, 0, 2, 5.633, 0, 2, 6.133, 0, 2, 7.167, 0, 2, 7.633, 0, 2, 9.1, 0]}, {"Target": "Parameter", "Id": "Param233", "Segments": [0, 0, 2, 0.333, 0, 1, 0.411, 0, 0.489, 8.415, 0.567, 8.57, 1, 0.656, 8.747, 0.744, 8.674, 0.833, 8.86, 1, 0.922, 9.046, 1.011, 15.412, 1.1, 20, 1, 1.2, 25.162, 1.3, 30, 1.4, 30, 0, 1.5, 26, 2, 1.6, 26, 0, 1.733, 28.945, 1, 1.9, 28.945, 2.066, 0.542, 2.233, -0.66, 1, 2.422, -2.022, 2.611, -2.21, 2.8, -3, 1, 2.989, -3.79, 3.178, -4.2, 3.367, -4.2, 2, 3.633, -4.2, 1, 3.766, -4.2, 3.9, -0.395, 4.033, 0.613, 1, 4.144, 1.454, 4.256, 1.387, 4.367, 2.024, 1, 4.422, 2.343, 4.478, 3.803, 4.533, 3.803, 0, 4.767, -0.767, 0, 5.067, 3.04, 1, 5.256, 3.04, 5.444, 2.448, 5.633, 1.24, 1, 5.8, 0.174, 5.966, -1.791, 6.133, -1.98, 1, 6.478, -2.371, 6.822, -2.4, 7.167, -2.4, 2, 7.633, -2.4, 1, 7.844, -2.4, 8.056, 0.831, 8.267, 6.507, 1, 8.422, 10.689, 8.578, 12.431, 8.733, 12.431, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param234", "Segments": [0, 0, 2, 0.333, 0, 0, 0.833, 7.675, 0, 1.4, 6, 2, 1.5, 6, 2, 1.6, 6, 0, 2.233, 3.75, 1, 2.422, 3.75, 2.611, 9.571, 2.8, 12.375, 1, 2.989, 15.179, 3.178, 15.225, 3.367, 15.225, 2, 3.633, 15.225, 0, 4.267, 6.9, 2, 4.533, 6.9, 0, 5.067, -16.275, 0, 5.633, 16, 0, 6.133, 1.14, 0, 6.533, 2.619, 1, 6.744, 2.619, 6.956, -1.49, 7.167, -4.6, 1, 7.322, -6.892, 7.478, -6.96, 7.633, -6.96, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param232", "Segments": [0, 0, 2, 0.333, 0, 2, 0.833, 0, 2, 1.4, 0, 2, 1.5, 0, 2, 1.6, 0, 2, 2.233, 0, 2, 2.8, 0, 2, 3.367, 0, 2, 3.633, 0, 2, 4.267, 0, 2, 4.533, 0, 0, 5.067, 3, 2, 5.633, 3, 2, 6.133, 3, 0, 6.333, 30, 0, 6.867, -30, 0, 7.167, 3, 2, 7.633, 3, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param235", "Segments": [0, 0, 2, 0.333, 0, 0, 0.833, -19, 0, 1.4, 7, 2, 1.5, 7, 2, 1.6, 7, 0, 1.933, -30, 0, 2.233, -21.5, 0, 2.8, -30, 2, 3.367, -30, 2, 3.633, -30, 0, 4.267, -22.32, 2, 4.533, -22.32, 0, 5.067, -25.68, 2, 5.633, -25.68, 2, 6.133, -25.68, 2, 7.167, -25.68, 2, 7.633, -25.68, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param277", "Segments": [0, 0, 2, 0.333, 0, 0, 0.6, 12.46, 0, 1.133, -9.549, 0, 1.567, 12.46, 0, 1.967, -9.549, 0, 2.4, 12.46, 0, 2.8, -9.549, 0, 3.233, 12.46, 0, 3.633, -9.549, 0, 4.067, 12.46, 0, 4.467, -9.549, 0, 4.9, 12.46, 0, 5.3, -9.549, 0, 5.733, 12.46, 0, 6.133, -9.549, 0, 6.567, 12.46, 0, 6.967, -9.549, 0, 7.4, 12.46, 0, 7.8, -9.549, 0, 8.233, 12.46, 0, 8.633, -9.549, 0, 9.067, 0, 2, 9.1, 0]}, {"Target": "Parameter", "Id": "Param278", "Segments": [0, 0, 2, 0.333, 0, 0, 0.6, 0.25, 0, 1.133, -0.17, 0, 1.567, 0.25, 0, 1.967, -0.17, 0, 2.4, 0.25, 0, 2.8, -0.17, 0, 3.233, 0.25, 0, 3.633, -0.17, 0, 4.067, 0.25, 0, 4.467, -0.17, 0, 4.9, 0.25, 0, 5.3, -0.17, 0, 5.733, 0.25, 0, 6.133, -0.17, 0, 6.567, 0.25, 0, 6.967, -0.17, 0, 7.4, 0.25, 0, 7.8, -0.17, 0, 8.233, 0.25, 0, 8.633, -0.17, 0, 9.067, 0, 2, 9.1, 0]}, {"Target": "Parameter", "Id": "Param280", "Segments": [0, 0, 2, 0.333, 0, 0, 0.6, 0.49, 0, 1.133, -0.407, 0, 1.567, 0.49, 0, 1.967, -0.407, 0, 2.4, 0.49, 0, 2.8, -0.407, 0, 3.233, 0.49, 0, 3.633, -0.407, 0, 4.067, 0.49, 0, 4.467, -0.407, 0, 4.9, 0.49, 0, 5.3, -0.407, 0, 5.733, 0.49, 0, 6.133, -0.407, 0, 6.567, 0.49, 0, 6.967, -0.407, 0, 7.4, 0.49, 0, 7.8, -0.407, 0, 8.233, 0.49, 0, 8.633, -0.407, 0, 9.067, 0, 2, 9.1, 0]}, {"Target": "Parameter", "Id": "Param7", "Segments": [0, 0, 2, 0.333, 0, 0, 1.8, -9, 0, 3.267, 0, 0, 4.733, -9, 0, 6.2, 0, 0, 7.667, -9, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param66", "Segments": [0, 0, 2, 0.333, 0, 0, 1.8, -19, 0, 3.267, 0, 0, 4.733, -19, 0, 6.2, 0, 0, 7.667, -19, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param67", "Segments": [0, 0, 2, 0.333, 0, 0, 1.067, 16, 0, 2.533, -16, 1, 2.778, -16, 3.022, -10.667, 3.267, 0, 1, 3.511, 10.667, 3.756, 16, 4, 16, 0, 5.467, -16, 1, 5.711, -16, 5.956, -10.667, 6.2, 0, 1, 6.444, 10.667, 6.689, 16, 6.933, 16, 0, 8.4, -16, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param68", "Segments": [0, 0, 2, 0.333, 0, 0, 1.8, 17, 0, 3.267, 0, 0, 4.733, 17, 0, 6.2, 0, 0, 7.667, 17, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param69", "Segments": [0, 0, 2, 0.333, 0, 0, 1.067, -16, 0, 2.533, 16, 1, 2.778, 16, 3.022, 10.667, 3.267, 0, 1, 3.511, -10.667, 3.756, -16, 4, -16, 0, 5.467, 16, 1, 5.711, 16, 5.956, 10.667, 6.2, 0, 1, 6.444, -10.667, 6.689, -16, 6.933, -16, 0, 8.4, 16, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param35", "Segments": [0, 0, 2, 0.333, 0, 0, 0.633, -2.866, 0, 0.967, 4.802, 0, 1.333, -3.854, 0, 1.733, 2.156, 0, 1.867, 1.321, 0, 2.167, 5.314, 0, 2.7, -7.639, 0, 3.133, 4.832, 0, 3.567, -1.599, 0, 3.833, -0.501, 0, 3.967, -0.686, 0, 4.567, 0.413, 0, 5.067, -1.114, 0, 5.533, 0.957, 0, 5.567, 0.953, 0, 6.1, 3.694, 0, 6.5, -3.611, 0, 6.967, 3.561, 0, 7.467, -1.752, 0, 7.767, -0.068, 0, 8.133, -1.655, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param36", "Segments": [0, 0, 2, 0.333, 0, 0, 0.567, 1.59, 0, 0.9, -4.497, 0, 1.2, 6.34, 0, 1.567, -5.14, 0, 1.9, 2.553, 0, 2.133, -1.044, 0, 2.5, 4.271, 0, 2.933, -6.199, 0, 3.367, 6.259, 0, 3.733, -3.555, 0, 4.1, 1.192, 0, 4.5, -0.5, 0, 4.933, 0.694, 0, 5.333, -1.219, 0, 5.633, 0.634, 0, 6, -1.213, 0, 6.4, 3.513, 0, 6.767, -4.67, 0, 7.167, 3.79, 0, 7.633, -2.526, 0, 8, 1.841, 0, 8.367, -1.064, 0, 8.767, 0.334, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param37", "Segments": [0, 0, 2, 0.333, 0, 0, 0.567, -2.994, 0, 0.833, 5.452, 0, 1.167, -5.998, 0, 1.533, 2.609, 0, 1.833, -0.562, 0, 2.133, 5.506, 0, 2.667, -4.645, 0, 3.133, 3.476, 0, 3.567, -2.174, 0, 3.833, 0.879, 0, 4.067, -0.561, 0, 4.3, -0.307, 0, 4.6, -0.798, 0, 4.833, -0.396, 0, 4.933, -0.449, 0, 5.367, 1.812, 0, 5.833, -0.687, 0, 6.167, 2.493, 0, 6.467, -3.051, 0, 6.733, 5, 0, 7.033, -1.265, 0, 7.267, 0.677, 0, 7.5, -0.787, 0, 7.833, 1.336, 0, 8.167, -3.077, 0, 8.5, -0.638, 0, 8.667, -0.776, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param39", "Segments": [0, 0, 2, 0.333, 0, 0, 0.5, 1.611, 0, 0.733, -4.293, 0, 1.033, 6.195, 0, 1.333, -6.126, 0, 1.667, 3.947, 0, 2, -4.194, 0, 2.3, 4.242, 0, 2.767, -1.467, 0, 2.9, -1.174, 0, 3.033, -1.315, 0, 3.267, 3.184, 0, 3.7, -1.873, 0, 3.967, 1.923, 0, 4.233, -0.856, 0, 4.5, 0.499, 0, 4.8, -0.382, 0, 4.967, 0.123, 0, 5.233, -0.534, 0, 5.533, 1.001, 0, 6, -1.239, 0, 6.367, 2.697, 0, 6.633, -4.951, 0, 6.9, 5.698, 0, 7.167, -3.626, 0, 7.433, 2.124, 0, 7.733, -1.243, 0, 8.033, 2.217, 0, 8.333, -2.237, 0, 8.6, 1.214, 0, 8.9, -0.809, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param38", "Segments": [0, 0, 2, 0.333, 0, 0, 0.567, -2.994, 0, 0.833, 5.452, 0, 1.167, -5.998, 0, 1.533, 2.609, 0, 1.833, -0.562, 0, 2.133, 5.506, 0, 2.667, -4.645, 0, 3.133, 3.476, 0, 3.567, -2.174, 0, 3.833, 0.879, 0, 4.067, -0.561, 0, 4.3, -0.307, 0, 4.6, -0.798, 0, 4.833, -0.396, 0, 4.933, -0.449, 0, 5.367, 1.812, 0, 5.833, -0.687, 0, 6.167, 2.493, 0, 6.467, -3.051, 0, 6.733, 5, 0, 7.033, -1.265, 0, 7.267, 0.677, 0, 7.5, -0.787, 0, 7.833, 1.336, 0, 8.167, -3.077, 0, 8.5, -0.638, 0, 8.667, -0.776, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param40", "Segments": [0, 0, 2, 0.333, 0, 0, 0.5, 1.611, 0, 0.733, -4.293, 0, 1.033, 6.195, 0, 1.333, -6.126, 0, 1.667, 3.947, 0, 2, -4.194, 0, 2.3, 4.242, 0, 2.767, -1.467, 0, 2.9, -1.174, 0, 3.033, -1.315, 0, 3.267, 3.184, 0, 3.7, -1.873, 0, 3.967, 1.923, 0, 4.233, -0.856, 0, 4.5, 0.499, 0, 4.8, -0.382, 0, 4.967, 0.123, 0, 5.233, -0.534, 0, 5.533, 1.001, 0, 6, -1.239, 0, 6.367, 2.697, 0, 6.633, -4.951, 0, 6.9, 5.698, 0, 7.167, -3.626, 0, 7.433, 2.124, 0, 7.733, -1.243, 0, 8.033, 2.217, 0, 8.333, -2.237, 0, 8.6, 1.214, 0, 8.9, -0.809, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param41", "Segments": [0, 0, 2, 0.333, 0, 0, 0.567, -2.994, 0, 0.833, 5.452, 0, 1.167, -5.998, 0, 1.533, 2.609, 0, 1.833, -0.562, 0, 2.133, 5.506, 0, 2.667, -4.645, 0, 3.133, 3.476, 0, 3.567, -2.174, 0, 3.833, 0.879, 0, 4.067, -0.561, 0, 4.3, -0.307, 0, 4.6, -0.798, 0, 4.833, -0.396, 0, 4.933, -0.449, 0, 5.367, 1.812, 0, 5.833, -0.687, 0, 6.167, 2.493, 0, 6.467, -3.051, 0, 6.733, 5, 0, 7.033, -1.265, 0, 7.267, 0.677, 0, 7.5, -0.787, 0, 7.833, 1.336, 0, 8.167, -3.077, 0, 8.5, -0.638, 0, 8.667, -0.776, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param42", "Segments": [0, 0, 2, 0.333, 0, 0, 0.5, 1.611, 0, 0.733, -4.293, 0, 1.033, 6.195, 0, 1.333, -6.126, 0, 1.667, 3.947, 0, 2, -4.194, 0, 2.3, 4.242, 0, 2.767, -1.467, 0, 2.9, -1.174, 0, 3.033, -1.315, 0, 3.267, 3.184, 0, 3.7, -1.873, 0, 3.967, 1.923, 0, 4.233, -0.856, 0, 4.5, 0.499, 0, 4.8, -0.382, 0, 4.967, 0.123, 0, 5.233, -0.534, 0, 5.533, 1.001, 0, 6, -1.239, 0, 6.367, 2.697, 0, 6.633, -4.951, 0, 6.9, 5.698, 0, 7.167, -3.626, 0, 7.433, 2.124, 0, 7.733, -1.243, 0, 8.033, 2.217, 0, 8.333, -2.237, 0, 8.6, 1.214, 0, 8.9, -0.809, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param43", "Segments": [0, 0, 2, 0.333, 0, 0, 0.567, -2.994, 0, 0.833, 5.452, 0, 1.167, -5.998, 0, 1.533, 2.609, 0, 1.833, -0.562, 0, 2.133, 5.506, 0, 2.667, -4.645, 0, 3.133, 3.476, 0, 3.567, -2.174, 0, 3.833, 0.879, 0, 4.067, -0.561, 0, 4.3, -0.307, 0, 4.6, -0.798, 0, 4.833, -0.396, 0, 4.933, -0.449, 0, 5.367, 1.812, 0, 5.833, -0.687, 0, 6.167, 2.493, 0, 6.467, -3.051, 0, 6.733, 5, 0, 7.033, -1.265, 0, 7.267, 0.677, 0, 7.5, -0.787, 0, 7.833, 1.336, 0, 8.167, -3.077, 0, 8.5, -0.638, 0, 8.667, -0.776, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param44", "Segments": [0, 0, 2, 0.333, 0, 0, 0.5, 1.611, 0, 0.733, -4.293, 0, 1.033, 6.195, 0, 1.333, -6.126, 0, 1.667, 3.947, 0, 2, -4.194, 0, 2.3, 4.242, 0, 2.767, -1.467, 0, 2.9, -1.174, 0, 3.033, -1.315, 0, 3.267, 3.184, 0, 3.7, -1.873, 0, 3.967, 1.923, 0, 4.233, -0.856, 0, 4.5, 0.499, 0, 4.8, -0.382, 0, 4.967, 0.123, 0, 5.233, -0.534, 0, 5.533, 1.001, 0, 6, -1.239, 0, 6.367, 2.697, 0, 6.633, -4.951, 0, 6.9, 5.698, 0, 7.167, -3.626, 0, 7.433, 2.124, 0, 7.733, -1.243, 0, 8.033, 2.217, 0, 8.333, -2.237, 0, 8.6, 1.214, 0, 8.9, -0.809, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param45", "Segments": [0, 0, 2, 0.333, 0, 0, 0.567, -2.994, 0, 0.833, 5.452, 0, 1.167, -5.998, 0, 1.533, 2.609, 0, 1.833, -0.562, 0, 2.133, 5.506, 0, 2.667, -4.645, 0, 3.133, 3.476, 0, 3.567, -2.174, 0, 3.833, 0.879, 0, 4.067, -0.561, 0, 4.3, -0.307, 0, 4.6, -0.798, 0, 4.833, -0.396, 0, 4.933, -0.449, 0, 5.367, 1.812, 0, 5.833, -0.687, 0, 6.167, 2.493, 0, 6.467, -3.051, 0, 6.733, 5, 0, 7.033, -1.265, 0, 7.267, 0.677, 0, 7.5, -0.787, 0, 7.833, 1.336, 0, 8.167, -3.077, 0, 8.5, -0.638, 0, 8.667, -0.776, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param46", "Segments": [0, 0, 2, 0.333, 0, 0, 0.5, 1.611, 0, 0.733, -4.293, 0, 1.033, 6.195, 0, 1.333, -6.126, 0, 1.667, 3.947, 0, 2, -4.194, 0, 2.3, 4.242, 0, 2.767, -1.467, 0, 2.9, -1.174, 0, 3.033, -1.315, 0, 3.267, 3.184, 0, 3.7, -1.873, 0, 3.967, 1.923, 0, 4.233, -0.856, 0, 4.5, 0.499, 0, 4.8, -0.382, 0, 4.967, 0.123, 0, 5.233, -0.534, 0, 5.533, 1.001, 0, 6, -1.239, 0, 6.367, 2.697, 0, 6.633, -4.951, 0, 6.9, 5.698, 0, 7.167, -3.626, 0, 7.433, 2.124, 0, 7.733, -1.243, 0, 8.033, 2.217, 0, 8.333, -2.237, 0, 8.6, 1.214, 0, 8.9, -0.809, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param62", "Segments": [0, 0, 2, 0.333, 0, 0, 0.567, -2.994, 0, 0.833, 5.452, 0, 1.167, -5.998, 0, 1.533, 2.609, 0, 1.833, -0.562, 0, 2.133, 5.506, 0, 2.667, -4.645, 0, 3.133, 3.476, 0, 3.567, -2.174, 0, 3.833, 0.879, 0, 4.067, -0.561, 0, 4.3, -0.307, 0, 4.6, -0.798, 0, 4.833, -0.396, 0, 4.933, -0.449, 0, 5.367, 1.812, 0, 5.833, -0.687, 0, 6.167, 2.493, 0, 6.467, -3.051, 0, 6.733, 5, 0, 7.033, -1.265, 0, 7.267, 0.677, 0, 7.5, -0.787, 0, 7.833, 1.336, 0, 8.167, -3.077, 0, 8.5, -0.638, 0, 8.667, -0.776, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param63", "Segments": [0, 0, 2, 0.333, 0, 0, 0.5, 1.611, 0, 0.733, -4.293, 0, 1.033, 6.195, 0, 1.333, -6.126, 0, 1.667, 3.947, 0, 2, -4.194, 0, 2.3, 4.242, 0, 2.767, -1.467, 0, 2.9, -1.174, 0, 3.033, -1.315, 0, 3.267, 3.184, 0, 3.7, -1.873, 0, 3.967, 1.923, 0, 4.233, -0.856, 0, 4.5, 0.499, 0, 4.8, -0.382, 0, 4.967, 0.123, 0, 5.233, -0.534, 0, 5.533, 1.001, 0, 6, -1.239, 0, 6.367, 2.697, 0, 6.633, -4.951, 0, 6.9, 5.698, 0, 7.167, -3.626, 0, 7.433, 2.124, 0, 7.733, -1.243, 0, 8.033, 2.217, 0, 8.333, -2.237, 0, 8.6, 1.214, 0, 8.9, -0.809, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param64", "Segments": [0, 0, 2, 0.333, 0, 0, 0.567, -2.994, 0, 0.833, 5.452, 0, 1.167, -5.998, 0, 1.533, 2.609, 0, 1.833, -0.562, 0, 2.133, 5.506, 0, 2.667, -4.645, 0, 3.133, 3.476, 0, 3.567, -2.174, 0, 3.833, 0.879, 0, 4.067, -0.561, 0, 4.3, -0.307, 0, 4.6, -0.798, 0, 4.833, -0.396, 0, 4.933, -0.449, 0, 5.367, 1.812, 0, 5.833, -0.687, 0, 6.167, 2.493, 0, 6.467, -3.051, 0, 6.733, 5, 0, 7.033, -1.265, 0, 7.267, 0.677, 0, 7.5, -0.787, 0, 7.833, 1.336, 0, 8.167, -3.077, 0, 8.5, -0.638, 0, 8.667, -0.776, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param65", "Segments": [0, 0, 2, 0.333, 0, 0, 0.5, 1.611, 0, 0.733, -4.293, 0, 1.033, 6.195, 0, 1.333, -6.126, 0, 1.667, 3.947, 0, 2, -4.194, 0, 2.3, 4.242, 0, 2.767, -1.467, 0, 2.9, -1.174, 0, 3.033, -1.315, 0, 3.267, 3.184, 0, 3.7, -1.873, 0, 3.967, 1.923, 0, 4.233, -0.856, 0, 4.5, 0.499, 0, 4.8, -0.382, 0, 4.967, 0.123, 0, 5.233, -0.534, 0, 5.533, 1.001, 0, 6, -1.239, 0, 6.367, 2.697, 0, 6.633, -4.951, 0, 6.9, 5.698, 0, 7.167, -3.626, 0, 7.433, 2.124, 0, 7.733, -1.243, 0, 8.033, 2.217, 0, 8.333, -2.237, 0, 8.6, 1.214, 0, 8.9, -0.809, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param72", "Segments": [0, 0, 2, 0.333, 0, 0, 0.567, -2.994, 0, 0.833, 5.452, 0, 1.167, -5.998, 0, 1.533, 2.609, 0, 1.833, -0.562, 0, 2.133, 5.506, 0, 2.667, -4.645, 0, 3.133, 3.476, 0, 3.567, -2.174, 0, 3.833, 0.879, 0, 4.067, -0.561, 0, 4.3, -0.307, 0, 4.6, -0.798, 0, 4.833, -0.396, 0, 4.933, -0.449, 0, 5.367, 1.812, 0, 5.833, -0.687, 0, 6.167, 2.493, 0, 6.467, -3.051, 0, 6.733, 5, 0, 7.033, -1.265, 0, 7.267, 0.677, 0, 7.5, -0.787, 0, 7.833, 1.336, 0, 8.167, -3.077, 0, 8.5, -0.638, 0, 8.667, -0.776, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param73", "Segments": [0, 0, 2, 0.333, 0, 0, 0.5, 1.611, 0, 0.733, -4.293, 0, 1.033, 6.195, 0, 1.333, -6.126, 0, 1.667, 3.947, 0, 2, -4.194, 0, 2.3, 4.242, 0, 2.767, -1.467, 0, 2.9, -1.174, 0, 3.033, -1.315, 0, 3.267, 3.184, 0, 3.7, -1.873, 0, 3.967, 1.923, 0, 4.233, -0.856, 0, 4.5, 0.499, 0, 4.8, -0.382, 0, 4.967, 0.123, 0, 5.233, -0.534, 0, 5.533, 1.001, 0, 6, -1.239, 0, 6.367, 2.697, 0, 6.633, -4.951, 0, 6.9, 5.698, 0, 7.167, -3.626, 0, 7.433, 2.124, 0, 7.733, -1.243, 0, 8.033, 2.217, 0, 8.333, -2.237, 0, 8.6, 1.214, 0, 8.9, -0.809, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param74", "Segments": [0, 0, 2, 0.333, 0, 0, 0.567, -2.994, 0, 0.833, 5.452, 0, 1.167, -5.998, 0, 1.533, 2.609, 0, 1.833, -0.562, 0, 2.133, 5.506, 0, 2.667, -4.645, 0, 3.133, 3.476, 0, 3.567, -2.174, 0, 3.833, 0.879, 0, 4.067, -0.561, 0, 4.3, -0.307, 0, 4.6, -0.798, 0, 4.833, -0.396, 0, 4.933, -0.449, 0, 5.367, 1.812, 0, 5.833, -0.687, 0, 6.167, 2.493, 0, 6.467, -3.051, 0, 6.733, 5, 0, 7.033, -1.265, 0, 7.267, 0.677, 0, 7.5, -0.787, 0, 7.833, 1.336, 0, 8.167, -3.077, 0, 8.5, -0.638, 0, 8.667, -0.776, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param75", "Segments": [0, 0, 2, 0.333, 0, 0, 0.5, 1.611, 0, 0.733, -4.293, 0, 1.033, 6.195, 0, 1.333, -6.126, 0, 1.667, 3.947, 0, 2, -4.194, 0, 2.3, 4.242, 0, 2.767, -1.467, 0, 2.9, -1.174, 0, 3.033, -1.315, 0, 3.267, 3.184, 0, 3.7, -1.873, 0, 3.967, 1.923, 0, 4.233, -0.856, 0, 4.5, 0.499, 0, 4.8, -0.382, 0, 4.967, 0.123, 0, 5.233, -0.534, 0, 5.533, 1.001, 0, 6, -1.239, 0, 6.367, 2.697, 0, 6.633, -4.951, 0, 6.9, 5.698, 0, 7.167, -3.626, 0, 7.433, 2.124, 0, 7.733, -1.243, 0, 8.033, 2.217, 0, 8.333, -2.237, 0, 8.6, 1.214, 0, 8.9, -0.809, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param53", "Segments": [0, 0, 2, 0.333, 0, 0, 0.567, -2.994, 0, 0.833, 5.452, 0, 1.167, -5.998, 0, 1.533, 2.609, 0, 1.833, -0.562, 0, 2.133, 5.506, 0, 2.667, -4.645, 0, 3.133, 3.476, 0, 3.567, -2.174, 0, 3.833, 0.879, 0, 4.067, -0.561, 0, 4.3, -0.307, 0, 4.6, -0.798, 0, 4.833, -0.396, 0, 4.933, -0.449, 0, 5.367, 1.812, 0, 5.833, -0.687, 0, 6.167, 2.493, 0, 6.467, -3.051, 0, 6.733, 5, 0, 7.033, -1.265, 0, 7.267, 0.677, 0, 7.5, -0.787, 0, 7.833, 1.336, 0, 8.167, -3.077, 0, 8.5, -0.638, 0, 8.667, -0.776, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param54", "Segments": [0, 0, 2, 0.333, 0, 0, 0.5, 1.611, 0, 0.733, -4.293, 0, 1.033, 6.195, 0, 1.333, -6.126, 0, 1.667, 3.947, 0, 2, -4.194, 0, 2.3, 4.242, 0, 2.767, -1.467, 0, 2.9, -1.174, 0, 3.033, -1.315, 0, 3.267, 3.184, 0, 3.7, -1.873, 0, 3.967, 1.923, 0, 4.233, -0.856, 0, 4.5, 0.499, 0, 4.8, -0.382, 0, 4.967, 0.123, 0, 5.233, -0.534, 0, 5.533, 1.001, 0, 6, -1.239, 0, 6.367, 2.697, 0, 6.633, -4.951, 0, 6.9, 5.698, 0, 7.167, -3.626, 0, 7.433, 2.124, 0, 7.733, -1.243, 0, 8.033, 2.217, 0, 8.333, -2.237, 0, 8.6, 1.214, 0, 8.9, -0.809, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param81", "Segments": [0, 0, 2, 0.333, 0, 0, 0.567, -2.994, 0, 0.833, 5.452, 0, 1.167, -5.998, 0, 1.533, 2.609, 0, 1.833, -0.562, 0, 2.133, 5.506, 0, 2.667, -4.645, 0, 3.133, 3.476, 0, 3.567, -2.174, 0, 3.833, 0.879, 0, 4.067, -0.561, 0, 4.3, -0.307, 0, 4.6, -0.798, 0, 4.833, -0.396, 0, 4.933, -0.449, 0, 5.367, 1.812, 0, 5.833, -0.687, 0, 6.167, 2.493, 0, 6.467, -3.051, 0, 6.733, 5, 0, 7.033, -1.265, 0, 7.267, 0.677, 0, 7.5, -0.787, 0, 7.833, 1.336, 0, 8.167, -3.077, 0, 8.5, -0.638, 0, 8.667, -0.776, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param82", "Segments": [0, 0, 2, 0.333, 0, 0, 0.5, 1.611, 0, 0.733, -4.293, 0, 1.033, 6.195, 0, 1.333, -6.126, 0, 1.667, 3.947, 0, 2, -4.194, 0, 2.3, 4.242, 0, 2.767, -1.467, 0, 2.9, -1.174, 0, 3.033, -1.315, 0, 3.267, 3.184, 0, 3.7, -1.873, 0, 3.967, 1.923, 0, 4.233, -0.856, 0, 4.5, 0.499, 0, 4.8, -0.382, 0, 4.967, 0.123, 0, 5.233, -0.534, 0, 5.533, 1.001, 0, 6, -1.239, 0, 6.367, 2.697, 0, 6.633, -4.951, 0, 6.9, 5.698, 0, 7.167, -3.626, 0, 7.433, 2.124, 0, 7.733, -1.243, 0, 8.033, 2.217, 0, 8.333, -2.237, 0, 8.6, 1.214, 0, 8.9, -0.809, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param70", "Segments": [0, 0, 2, 0.333, 0, 0, 0.567, -2.994, 0, 0.833, 5.452, 0, 1.167, -5.998, 0, 1.533, 2.609, 0, 1.833, -0.562, 0, 2.133, 5.506, 0, 2.667, -4.645, 0, 3.133, 3.476, 0, 3.567, -2.174, 0, 3.833, 0.879, 0, 4.067, -0.561, 0, 4.3, -0.307, 0, 4.6, -0.798, 0, 4.833, -0.396, 0, 4.933, -0.449, 0, 5.367, 1.812, 0, 5.833, -0.687, 0, 6.167, 2.493, 0, 6.467, -3.051, 0, 6.733, 5, 0, 7.033, -1.265, 0, 7.267, 0.677, 0, 7.5, -0.787, 0, 7.833, 1.336, 0, 8.167, -3.077, 0, 8.5, -0.638, 0, 8.667, -0.776, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param71", "Segments": [0, 0, 2, 0.333, 0, 0, 0.5, 1.611, 0, 0.733, -4.293, 0, 1.033, 6.195, 0, 1.333, -6.126, 0, 1.667, 3.947, 0, 2, -4.194, 0, 2.3, 4.242, 0, 2.767, -1.467, 0, 2.9, -1.174, 0, 3.033, -1.315, 0, 3.267, 3.184, 0, 3.7, -1.873, 0, 3.967, 1.923, 0, 4.233, -0.856, 0, 4.5, 0.499, 0, 4.8, -0.382, 0, 4.967, 0.123, 0, 5.233, -0.534, 0, 5.533, 1.001, 0, 6, -1.239, 0, 6.367, 2.697, 0, 6.633, -4.951, 0, 6.9, 5.698, 0, 7.167, -3.626, 0, 7.433, 2.124, 0, 7.733, -1.243, 0, 8.033, 2.217, 0, 8.333, -2.237, 0, 8.6, 1.214, 0, 8.9, -0.809, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param77", "Segments": [0, 0, 2, 0.333, 0, 0, 0.567, -2.994, 0, 0.833, 5.452, 0, 1.167, -5.998, 0, 1.533, 2.609, 0, 1.833, -0.562, 0, 2.133, 5.506, 0, 2.667, -4.645, 0, 3.133, 3.476, 0, 3.567, -2.174, 0, 3.833, 0.879, 0, 4.067, -0.561, 0, 4.3, -0.307, 0, 4.6, -0.798, 0, 4.833, -0.396, 0, 4.933, -0.449, 0, 5.367, 1.812, 0, 5.833, -0.687, 0, 6.167, 2.493, 0, 6.467, -3.051, 0, 6.733, 5, 0, 7.033, -1.265, 0, 7.267, 0.677, 0, 7.5, -0.787, 0, 7.833, 1.336, 0, 8.167, -3.077, 0, 8.5, -0.638, 0, 8.667, -0.776, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param78", "Segments": [0, 0, 2, 0.333, 0, 0, 0.5, 1.611, 0, 0.733, -4.293, 0, 1.033, 6.195, 0, 1.333, -6.126, 0, 1.667, 3.947, 0, 2, -4.194, 0, 2.3, 4.242, 0, 2.767, -1.467, 0, 2.9, -1.174, 0, 3.033, -1.315, 0, 3.267, 3.184, 0, 3.7, -1.873, 0, 3.967, 1.923, 0, 4.233, -0.856, 0, 4.5, 0.499, 0, 4.8, -0.382, 0, 4.967, 0.123, 0, 5.233, -0.534, 0, 5.533, 1.001, 0, 6, -1.239, 0, 6.367, 2.697, 0, 6.633, -4.951, 0, 6.9, 5.698, 0, 7.167, -3.626, 0, 7.433, 2.124, 0, 7.733, -1.243, 0, 8.033, 2.217, 0, 8.333, -2.237, 0, 8.6, 1.214, 0, 8.9, -0.809, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param79", "Segments": [0, 0, 2, 0.333, 0, 0, 0.567, -2.994, 0, 0.833, 5.452, 0, 1.167, -5.998, 0, 1.533, 2.609, 0, 1.833, -0.562, 0, 2.133, 5.506, 0, 2.667, -4.645, 0, 3.133, 3.476, 0, 3.567, -2.174, 0, 3.833, 0.879, 0, 4.067, -0.561, 0, 4.3, -0.307, 0, 4.6, -0.798, 0, 4.833, -0.396, 0, 4.933, -0.449, 0, 5.367, 1.812, 0, 5.833, -0.687, 0, 6.167, 2.493, 0, 6.467, -3.051, 0, 6.733, 5, 0, 7.033, -1.265, 0, 7.267, 0.677, 0, 7.5, -0.787, 0, 7.833, 1.336, 0, 8.167, -3.077, 0, 8.5, -0.638, 0, 8.667, -0.776, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param80", "Segments": [0, 0, 2, 0.333, 0, 0, 0.5, 1.611, 0, 0.733, -4.293, 0, 1.033, 6.195, 0, 1.333, -6.126, 0, 1.667, 3.947, 0, 2, -4.194, 0, 2.3, 4.242, 0, 2.767, -1.467, 0, 2.9, -1.174, 0, 3.033, -1.315, 0, 3.267, 3.184, 0, 3.7, -1.873, 0, 3.967, 1.923, 0, 4.233, -0.856, 0, 4.5, 0.499, 0, 4.8, -0.382, 0, 4.967, 0.123, 0, 5.233, -0.534, 0, 5.533, 1.001, 0, 6, -1.239, 0, 6.367, 2.697, 0, 6.633, -4.951, 0, 6.9, 5.698, 0, 7.167, -3.626, 0, 7.433, 2.124, 0, 7.733, -1.243, 0, 8.033, 2.217, 0, 8.333, -2.237, 0, 8.6, 1.214, 0, 8.9, -0.809, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param294", "Segments": [0, 1, 2, 0.333, 1, 0, 0.4, 0, 2, 9, 0, 0, 9.1, 1]}, {"Target": "Parameter", "Id": "Param47", "Segments": [0, 0, 2, 0.333, 0, 0, 0.567, -1.018, 0, 0.8, 1.937, 0, 1.1, -5.221, 0, 1.5, 3.681, 0, 1.833, -0.31, 0, 2.133, 0.685, 0, 2.367, 0.208, 0, 2.533, 0.303, 0, 2.9, -0.348, 0, 3.2, 0.134, 0, 3.467, 0.006, 0, 3.633, 0.058, 0, 3.667, 0, 0, 3.9, 1.537, 0, 4.533, -1.912, 0, 5.167, 1.826, 0, 5.9, -3.974, 0, 6.233, 3.26, 0, 6.533, -1.058, 0, 6.867, 0.522, 0, 7.167, 0.045, 0, 7.533, 0.319, 0, 7.667, 0.312, 0, 7.867, 3.942, 0, 8.167, -3.864, 0, 8.5, 0.564, 0, 8.733, -0.375, 0, 9, 0.791, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param48", "Segments": [0, 0, 2, 0.333, 0, 0, 0.567, 1.018, 0, 0.733, -1.387, 0, 1, 3.333, 0, 1.267, -4.055, 0, 1.633, 3.456, 0, 1.933, -2.077, 0, 2.267, 1.111, 0, 2.533, -0.512, 0, 2.833, 0.367, 0, 3.067, -0.371, 0, 3.367, 0.239, 0, 3.6, -0.209, 0, 3.667, 0, 0, 3.8, -0.958, 0, 4.067, 1.163, 0, 4.333, -0.258, 0, 4.467, -0.074, 0, 4.733, -0.545, 0, 5, -0.098, 0, 5.033, -0.1, 0, 5.333, 0.609, 0, 5.6, 0.003, 0, 5.8, 1.078, 0, 6.1, -2.717, 0, 6.4, 3.62, 0, 6.7, -2.4, 0, 7, 1.247, 0, 7.3, -0.595, 0, 7.6, 0.251, 0, 7.833, -1.783, 0, 8.067, 4.413, 0, 8.333, -4.161, 0, 8.6, 2.378, 0, 8.9, -1.436, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param56", "Segments": [0, 0, 2, 0.333, 0, 0, 0.567, -0.362, 0, 0.8, 0.866, 0, 1.167, -2.503, 0, 1.633, 2.117, 0, 2.267, -0.692, 0, 2.7, 0.43, 0, 3.167, -0.218, 0, 3.633, 0.204, 0, 3.667, 0.199, 0, 3.933, 0.685, 0, 4.567, -1.323, 0, 5.2, 1.524, 0, 5.9, -2.529, 0, 6.333, 1.825, 0, 6.933, -0.605, 0, 7.433, 0.491, 0, 7.667, 0.251, 0, 7.9, 1.695, 0, 8.233, -1.612, 0, 8.967, 0.904, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param57", "Segments": [0, 0, 2, 0.333, 0, 0, 0.567, 0.453, 0, 0.767, -0.821, 0, 1, 1.915, 0, 1.4, -2.563, 0, 1.767, 1.47, 0, 2.467, -0.467, 0, 2.867, 0.495, 0, 3.467, -0.297, 0, 3.7, 0.056, 0, 3.833, -0.162, 0, 4.133, 0.587, 0, 4.867, -1.015, 0, 5.4, 0.982, 0, 5.7, 0.491, 0, 5.767, 0.589, 0, 6.167, -2.484, 0, 6.467, 1.609, 0, 6.833, -0.1, 0, 6.9, -0.096, 0, 7.2, -0.377, 0, 7.6, 0.356, 0, 7.833, -0.799, 0, 8.1, 2.53, 0, 8.4, -1.548, 0, 8.7, -0.066, 0, 8.867, -0.246, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param58", "Segments": [0, 0, 2, 0.333, 0, 2, 0.7, 0, 0, 0.867, -0.797, 0, 1.133, 2.682, 0, 1.467, -3.651, 0, 1.833, 2.879, 0, 2.167, -1.005, 0, 2.433, -0.077, 0, 2.6, -0.348, 0, 2.933, 0.661, 0, 3.2, -0.402, 0, 3.467, 0.056, 0, 3.567, -0.139, 2, 3.6, -0.139, 0, 3.667, -0.231, 0, 3.8, -0.023, 0, 3.967, -0.315, 0, 4.267, 0.658, 0, 4.8, -0.7, 0, 5.467, 0.947, 0, 5.733, 0.128, 0, 5.9, 0.459, 0, 6.233, -2.646, 0, 6.567, 2.981, 0, 6.9, -1.445, 0, 7.2, 0.203, 0, 7.4, -0.215, 0, 7.667, 0.397, 0, 7.933, -1.221, 0, 8.2, 2.929, 0, 8.5, -2.878, 0, 8.8, 1.211, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param59", "Segments": [0, 0, 2, 0.333, 0, 0, 0.567, -0.362, 0, 0.8, 0.866, 0, 1.167, -2.503, 0, 1.633, 2.117, 0, 2.267, -0.692, 0, 2.7, 0.43, 0, 3.167, -0.218, 0, 3.633, 0.204, 0, 3.667, 0.199, 0, 3.933, 0.685, 0, 4.567, -1.323, 0, 5.2, 1.524, 0, 5.9, -2.529, 0, 6.333, 1.825, 0, 6.933, -0.605, 0, 7.433, 0.491, 0, 7.667, 0.251, 0, 7.9, 1.695, 0, 8.233, -1.612, 0, 8.967, 0.904, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param60", "Segments": [0, 0, 2, 0.333, 0, 0, 0.567, 0.453, 0, 0.767, -0.821, 0, 1, 1.915, 0, 1.4, -2.563, 0, 1.767, 1.47, 0, 2.467, -0.467, 0, 2.867, 0.495, 0, 3.467, -0.297, 0, 3.7, 0.056, 0, 3.833, -0.162, 0, 4.133, 0.587, 0, 4.867, -1.015, 0, 5.4, 0.982, 0, 5.7, 0.491, 0, 5.767, 0.589, 0, 6.167, -2.484, 0, 6.467, 1.609, 0, 6.833, -0.1, 0, 6.9, -0.096, 0, 7.2, -0.377, 0, 7.6, 0.356, 0, 7.833, -0.799, 0, 8.1, 2.53, 0, 8.4, -1.548, 0, 8.7, -0.066, 0, 8.867, -0.246, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param61", "Segments": [0, 0, 2, 0.333, 0, 2, 0.7, 0, 0, 0.867, -0.797, 0, 1.133, 2.682, 0, 1.467, -3.651, 0, 1.833, 2.879, 0, 2.167, -1.005, 0, 2.433, -0.077, 0, 2.6, -0.348, 0, 2.933, 0.661, 0, 3.2, -0.402, 0, 3.467, 0.056, 0, 3.567, -0.139, 2, 3.6, -0.139, 0, 3.667, -0.231, 0, 3.8, -0.023, 0, 3.967, -0.315, 0, 4.267, 0.658, 0, 4.8, -0.7, 0, 5.467, 0.947, 0, 5.733, 0.128, 0, 5.9, 0.459, 0, 6.233, -2.646, 0, 6.567, 2.981, 0, 6.9, -1.445, 0, 7.2, 0.203, 0, 7.4, -0.215, 0, 7.667, 0.397, 0, 7.933, -1.221, 0, 8.2, 2.929, 0, 8.5, -2.878, 0, 8.8, 1.211, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param49", "Segments": [0, 0, 2, 0.333, 0, 0, 0.567, -1.018, 0, 0.8, 1.937, 0, 1.1, -5.221, 0, 1.5, 3.681, 0, 1.833, -0.31, 0, 2.133, 0.685, 0, 2.367, 0.208, 0, 2.533, 0.303, 0, 2.9, -0.348, 0, 3.2, 0.134, 0, 3.467, 0.006, 0, 3.633, 0.058, 0, 3.667, 0, 0, 3.9, 1.537, 0, 4.533, -1.912, 0, 5.167, 1.826, 0, 5.9, -3.974, 0, 6.233, 3.26, 0, 6.533, -1.058, 0, 6.867, 0.522, 0, 7.167, 0.045, 0, 7.533, 0.319, 0, 7.667, 0.312, 0, 7.867, 3.942, 0, 8.167, -3.864, 0, 8.5, 0.564, 0, 8.733, -0.375, 0, 9, 0.791, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param50", "Segments": [0, 0, 2, 0.333, 0, 0, 0.567, 1.018, 0, 0.733, -1.387, 0, 1, 3.333, 0, 1.267, -4.055, 0, 1.633, 3.456, 0, 1.933, -2.077, 0, 2.267, 1.111, 0, 2.533, -0.512, 0, 2.833, 0.367, 0, 3.067, -0.371, 0, 3.367, 0.239, 0, 3.6, -0.209, 0, 3.667, 0, 0, 3.8, -0.958, 0, 4.067, 1.163, 0, 4.333, -0.258, 0, 4.467, -0.074, 0, 4.733, -0.545, 0, 5, -0.098, 0, 5.033, -0.1, 0, 5.333, 0.609, 0, 5.6, 0.003, 0, 5.8, 1.078, 0, 6.1, -2.717, 0, 6.4, 3.62, 0, 6.7, -2.4, 0, 7, 1.247, 0, 7.3, -0.595, 0, 7.6, 0.251, 0, 7.833, -1.783, 0, 8.067, 4.413, 0, 8.333, -4.161, 0, 8.6, 2.378, 0, 8.9, -1.436, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param51", "Segments": [0, 0, 2, 0.333, 0, 0, 0.567, -1.018, 0, 0.8, 1.937, 0, 1.1, -5.221, 0, 1.5, 3.681, 0, 1.833, -0.31, 0, 2.133, 0.685, 0, 2.367, 0.208, 0, 2.533, 0.303, 0, 2.9, -0.348, 0, 3.2, 0.134, 0, 3.467, 0.006, 0, 3.633, 0.058, 0, 3.667, 0, 0, 3.9, 1.537, 0, 4.533, -1.912, 0, 5.167, 1.826, 0, 5.9, -3.974, 0, 6.233, 3.26, 0, 6.533, -1.058, 0, 6.867, 0.522, 0, 7.167, 0.045, 0, 7.533, 0.319, 0, 7.667, 0.312, 0, 7.867, 3.942, 0, 8.167, -3.864, 0, 8.5, 0.564, 0, 8.733, -0.375, 0, 9, 0.791, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param52", "Segments": [0, 0, 2, 0.333, 0, 0, 0.567, 1.018, 0, 0.733, -1.387, 0, 1, 3.333, 0, 1.267, -4.055, 0, 1.633, 3.456, 0, 1.933, -2.077, 0, 2.267, 1.111, 0, 2.533, -0.512, 0, 2.833, 0.367, 0, 3.067, -0.371, 0, 3.367, 0.239, 0, 3.6, -0.209, 0, 3.667, 0, 0, 3.8, -0.958, 0, 4.067, 1.163, 0, 4.333, -0.258, 0, 4.467, -0.074, 0, 4.733, -0.545, 0, 5, -0.098, 0, 5.033, -0.1, 0, 5.333, 0.609, 0, 5.6, 0.003, 0, 5.8, 1.078, 0, 6.1, -2.717, 0, 6.4, 3.62, 0, 6.7, -2.4, 0, 7, 1.247, 0, 7.3, -0.595, 0, 7.6, 0.251, 0, 7.833, -1.783, 0, 8.067, 4.413, 0, 8.333, -4.161, 0, 8.6, 2.378, 0, 8.9, -1.436, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation63", "Segments": [0, -0.675, 2, 0.333, -0.675, 0, 0.833, 0, 2, 1.4, 0, 2, 1.5, 0, 2, 1.6, 0, 2, 2.233, 0, 2, 2.8, 0, 2, 3.367, 0, 2, 3.633, 0, 2, 4.267, 0, 2, 4.533, 0, 2, 5.067, 0, 2, 5.633, 0, 2, 6.133, 0, 2, 7.167, 0, 2, 7.633, 0, 0, 9.1, -0.675]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation64", "Segments": [0, 2.951, 2, 0.333, 2.951, 0, 0.567, -0.34, 0, 0.8, 0.646, 0, 1.1, -1.74, 0, 1.5, 1.227, 0, 1.833, -0.103, 0, 2.133, 0.228, 0, 2.367, 0.069, 0, 2.533, 0.101, 0, 2.9, -0.116, 0, 3.2, 0.044, 0, 3.467, 0.002, 0, 3.633, 0.019, 0, 3.667, 0, 0, 3.9, 0.512, 0, 4.533, -0.637, 0, 5.167, 0.609, 0, 5.9, -1.325, 0, 6.233, 1.087, 0, 6.533, -0.353, 0, 6.867, 0.174, 0, 7.167, 0.015, 0, 7.533, 0.106, 0, 7.667, 0.104, 0, 7.867, 1.314, 0, 8.167, -1.288, 0, 8.5, 0.188, 0, 8.733, -0.125, 1, 8.822, -0.125, 8.911, -0.082, 9, 0.264, 1, 9.033, 0.393, 9.067, 2.951, 9.1, 2.951]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation65", "Segments": [0, 4.849, 2, 0.333, 4.849, 1, 0.411, 4.849, 0.489, 2.216, 0.567, 0.509, 1, 0.622, -0.71, 0.678, -0.693, 0.733, -0.693, 0, 1, 1.666, 0, 1.267, -2.027, 0, 1.633, 1.728, 0, 1.933, -1.038, 0, 2.267, 0.555, 0, 2.533, -0.256, 0, 2.833, 0.183, 0, 3.067, -0.186, 0, 3.367, 0.12, 0, 3.6, -0.104, 0, 3.667, 0, 0, 3.8, -0.479, 0, 4.067, 0.581, 0, 4.333, -0.129, 0, 4.467, -0.037, 0, 4.733, -0.272, 0, 5, -0.049, 0, 5.033, -0.05, 0, 5.333, 0.305, 0, 5.6, 0.001, 0, 5.8, 0.539, 0, 6.1, -1.359, 0, 6.4, 1.81, 0, 6.7, -1.2, 0, 7, 0.624, 0, 7.3, -0.298, 0, 7.6, 0.126, 0, 7.833, -0.891, 0, 8.067, 2.207, 0, 8.333, -2.08, 0, 8.6, 1.189, 0, 8.9, -0.718, 0, 9.1, 4.849]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation66", "Segments": [0, -11.659, 2, 0.333, -11.659, 0, 0.7, 0, 0, 0.833, -0.732, 0, 1.1, 2.498, 0, 1.4, -3.88, 0, 1.733, 3.797, 0, 2.033, -2.763, 0, 2.367, 1.754, 0, 2.667, -0.945, 0, 2.933, 0.542, 0, 3.2, -0.371, 0, 3.467, 0.291, 0, 3.567, 0.192, 0, 3.6, 0.231, 0, 3.9, -0.778, 0, 4.2, 1.114, 0, 4.5, -0.531, 0, 4.733, -0.211, 0, 4.867, -0.259, 0, 5.433, 0.476, 0, 5.7, -0.092, 0, 5.9, 0.613, 0, 6.2, -2.298, 0, 6.5, 3.332, 0, 6.8, -2.86, 0, 7.1, 1.873, 0, 7.4, -1.068, 0, 7.7, 0.521, 0, 7.933, -1.072, 0, 8.167, 3.093, 0, 8.433, -3.83, 0, 8.733, 2.935, 1, 8.833, 2.935, 8.933, 1.807, 9.033, -1.974, 1, 9.055, -2.815, 9.078, -11.659, 9.1, -11.659]}, {"Target": "Parameter", "Id": "Param55", "Segments": [0, -10.57, 2, 0.333, -10.57, 0, 0.767, 0, 0, 0.9, -1.337, 0, 1.2, 4.355, 0, 1.5, -7.956, 0, 1.833, 8.913, 0, 2.133, -7.402, 0, 2.467, 5.36, 0, 2.767, -3.31, 0, 3.1, 2.126, 0, 3.367, -1.576, 0, 3.633, 0.932, 0, 4, -1.368, 0, 4.3, 2.304, 0, 4.633, -1.681, 0, 4.933, 0.108, 0, 5.133, -0.151, 0, 5.5, 0.885, 0, 5.8, -0.297, 0, 6, 0.81, 0, 6.3, -4.074, 0, 6.6, 6.776, 0, 6.9, -6.847, 0, 7.233, 5.365, 0, 7.533, -3.603, 0, 7.8, 1.772, 0, 8.033, -1.539, 0, 8.267, 4.923, 0, 8.567, -7.741, 0, 8.867, 7.293, 0, 9.1, -10.57]}, {"Target": "Parameter", "Id": "Param28", "Segments": [0, -7.12, 2, 0.333, -7.12, 0, 1.8, 7.36, 0, 3.267, -7.12, 0, 4.733, 7.36, 0, 6.2, -7.12, 0, 7.667, 7.36, 0, 9.1, -7.12]}, {"Target": "Parameter", "Id": "Param29", "Segments": [0, -1.278, 2, 0.333, -1.278, 0, 0.867, -4.74, 0, 2.333, 5.1, 0, 3.833, -4.74, 0, 5.3, 5.1, 1, 5.6, 5.1, 5.9, 2.726, 6.2, -1.278, 1, 6.389, -3.798, 6.578, -4.74, 6.767, -4.74, 0, 8.233, 5.1, 0, 9.1, -1.278]}, {"Target": "Parameter", "Id": "Param30", "Segments": [0, -5.909, 2, 0.333, -5.909, 0, 1.567, 2.533, 0, 3.033, -6.287, 0, 4.533, 2.533, 0, 6, -6.287, 1, 6.067, -6.287, 6.133, -6.565, 6.2, -5.909, 1, 6.622, -1.748, 7.045, 2.533, 7.467, 2.533, 0, 8.9, -6.287, 0, 9.1, -5.909]}, {"Target": "Parameter", "Id": "Param31", "Segments": [0, -0.551, 2, 0.333, -0.551, 0, 0.9, 3.018, 0, 2.367, -6.18, 0, 3.867, 3.018, 0, 5.333, -6.18, 1, 5.622, -6.18, 5.911, -4.168, 6.2, -0.551, 1, 6.4, 1.953, 6.6, 3.018, 6.8, 3.018, 0, 8.267, -6.18, 0, 9.1, -0.551]}, {"Target": "Parameter", "Id": "Param32", "Segments": [0, -2.714, 2, 0.333, -2.714, 0, 1.633, 2.284, 0, 3.133, -2.796, 0, 4.6, 2.284, 0, 6.067, -2.796, 1, 6.111, -2.796, 6.156, -2.941, 6.2, -2.714, 1, 6.644, -0.446, 7.089, 2.284, 7.533, 2.284, 0, 8.967, -2.796, 0, 9.1, -2.714]}, {"Target": "Parameter", "Id": "Param33", "Segments": [0, -2.392, 2, 0.333, -2.392, 0, 0.933, 3.279, 0, 2.433, -10.047, 0, 3.9, 3.279, 0, 5.367, -10.047, 1, 5.645, -10.047, 5.922, -7.439, 6.2, -2.392, 1, 6.411, 1.444, 6.622, 3.279, 6.833, 3.279, 0, 8.3, -10.047, 0, 9.1, -2.392]}, {"Target": "Parameter", "Id": "Param34", "Segments": [0, -21.182, 2, 0.333, -21.182, 0, 1.667, 11.7, 0, 3.167, -21.42, 0, 4.633, 11.7, 0, 6.1, -21.42, 1, 6.133, -21.42, 6.167, -22.094, 6.2, -21.182, 1, 6.656, -8.716, 7.111, 11.7, 7.567, 11.7, 0, 9, -21.42, 0, 9.1, -21.182]}, {"Target": "Parameter", "Id": "Param98", "Segments": [0, -6, 2, 0.333, -6, 0, 0.833, 0, 2, 1.4, 0, 2, 1.5, 0, 2, 1.6, 0, 2, 2.233, 0, 2, 2.8, 0, 2, 3.367, 0, 2, 3.633, 0, 2, 4.267, 0, 2, 4.533, 0, 2, 5.067, 0, 2, 5.633, 0, 2, 6.133, 0, 2, 7.167, 0, 2, 7.633, 0, 0, 9.1, -6]}, {"Target": "Parameter", "Id": "Param99", "Segments": [0, -10.921, 2, 0.333, -10.921, 0, 0.833, 0, 2, 1.4, 0, 2, 1.5, 0, 2, 1.6, 0, 2, 2.233, 0, 2, 2.8, 0, 2, 3.367, 0, 2, 3.633, 0, 2, 4.267, 0, 2, 4.533, 0, 2, 5.067, 0, 2, 5.633, 0, 2, 6.133, 0, 2, 7.167, 0, 2, 7.633, 0, 0, 9.1, -10.921]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation67", "Segments": [0, -10.939, 2, 0.333, -10.939, 0, 0.833, 0, 2, 1.4, 0, 2, 1.5, 0, 2, 1.6, 0, 2, 2.233, 0, 2, 2.8, 0, 2, 3.367, 0, 2, 3.633, 0, 2, 4.267, 0, 2, 4.533, 0, 2, 5.067, 0, 2, 5.633, 0, 2, 6.133, 0, 2, 7.167, 0, 2, 7.633, 0, 0, 9.1, -10.939]}, {"Target": "Parameter", "Id": "Param281", "Segments": [0, 0, 2, 0.333, 0, 0, 1.8, 1, 0, 3.267, 0, 0, 4.733, 1, 0, 6.2, 0, 0, 7.667, 1, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param197mao2", "Segments": [0, 0, 2, 0.333, 0, 0, 0.967, 2.894, 0, 2.6, -2.454, 1, 2.822, -2.454, 3.045, -1.77, 3.267, 0, 1, 3.489, 1.77, 3.711, 2.894, 3.933, 2.894, 0, 5.533, -2.454, 1, 5.755, -2.454, 5.978, -1.77, 6.2, 0, 1, 6.422, 1.77, 6.645, 2.894, 6.867, 2.894, 0, 8.433, -2.454, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation144mao2", "Segments": [0, -10.82, 2, 0.333, -10.82, 0, 0.833, 0, 2, 1.4, 0, 2, 1.5, 0, 2, 1.6, 0, 2, 2.233, 0, 2, 2.8, 0, 2, 3.367, 0, 2, 3.633, 0, 2, 4.267, 0, 2, 4.533, 0, 2, 5.067, 0, 2, 5.633, 0, 2, 6.133, 0, 2, 7.167, 0, 2, 7.633, 0, 0, 9.1, -10.82]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation145mao2", "Segments": [0, 8.46, 2, 0.333, 8.46, 0, 0.833, 0, 2, 1.4, 0, 2, 1.5, 0, 2, 1.6, 0, 2, 2.233, 0, 2, 2.8, 0, 2, 3.367, 0, 2, 3.633, 0, 2, 4.267, 0, 2, 4.533, 0, 2, 5.067, 0, 2, 5.633, 0, 2, 6.133, 0, 2, 7.167, 0, 2, 7.633, 0, 0, 9.1, 8.46]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation146mao2", "Segments": [0, 28.83, 2, 0.333, 28.83, 0, 0.833, 0, 2, 1.4, 0, 2, 1.5, 0, 2, 1.6, 0, 2, 2.233, 0, 2, 2.8, 0, 2, 3.367, 0, 2, 3.633, 0, 2, 4.267, 0, 2, 4.533, 0, 2, 5.067, 0, 2, 5.633, 0, 2, 6.133, 0, 2, 7.167, 0, 2, 7.633, 0, 0, 9.1, 28.83]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation72", "Segments": [0, 8.085, 2, 0.333, 8.085, 0, 0.833, 0, 2, 1.4, 0, 2, 1.5, 0, 2, 1.6, 0, 2, 2.233, 0, 2, 2.8, 0, 2, 3.367, 0, 2, 3.633, 0, 2, 4.267, 0, 2, 4.533, 0, 2, 5.067, 0, 2, 5.633, 0, 2, 6.133, 0, 2, 7.167, 0, 2, 7.633, 0, 0, 9.1, 8.085]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation73", "Segments": [0, 11.915, 2, 0.333, 11.915, 0, 0.833, 0, 2, 1.4, 0, 2, 1.5, 0, 2, 1.6, 0, 2, 2.233, 0, 2, 2.8, 0, 2, 3.367, 0, 2, 3.633, 0, 2, 4.267, 0, 2, 4.533, 0, 2, 5.067, 0, 2, 5.633, 0, 2, 6.133, 0, 2, 7.167, 0, 2, 7.633, 0, 0, 9.1, 11.915]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation74", "Segments": [0, 12.341, 2, 0.333, 12.341, 0, 0.833, 0, 2, 1.4, 0, 2, 1.5, 0, 2, 1.6, 0, 2, 2.233, 0, 2, 2.8, 0, 2, 3.367, 0, 2, 3.633, 0, 2, 4.267, 0, 2, 4.533, 0, 2, 5.067, 0, 2, 5.633, 0, 2, 6.133, 0, 2, 7.167, 0, 2, 7.633, 0, 0, 9.1, 12.341]}, {"Target": "Parameter", "Id": "Param272", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param293", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "ParamCheek", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param268", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param269", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param252", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param193", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param192", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param196", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param282", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param183", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param194", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param195", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param184", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param289", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param185", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param290", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param291", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param186", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param187", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param188", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param292", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param189", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param190", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param191", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param23", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param24", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param26", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param27", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param200", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param286", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param287", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param288", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param205", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param203", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param204", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param21", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param213", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param216", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param217", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param218", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param219", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param220", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param12", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param197", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param198", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param199", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param208", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param279", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param283", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param230", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param231", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param222", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param207", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param236", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param238", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param239", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param241", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param242", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param243", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param244", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param17", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param18", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param20", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param275", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param225", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param227", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param226", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param285", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param274", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param276", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param142", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param143", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param144", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param147", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param145", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param134", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param246", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param141", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param182", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param247", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param229", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param245", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param117", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param140", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param262", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param263", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param115", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param116", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param260", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param261", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param133", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param139", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param257", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param137", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param138", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param135", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param176", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param136", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param177", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param251", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param84", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param85", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param83", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param86", "Segments": [0, 1, 0, 9.1, 1]}, {"Target": "Parameter", "Id": "Param91", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param2", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param87", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param88", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param94", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param95", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param92", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param93", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param96", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param97", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param89", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param90", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation56", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param218mao2", "Segments": [0, 1, 0, 9.1, 1]}, {"Target": "Parameter", "Id": "Param211mao2", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param216mao2", "Segments": [0, -8.88, 0, 9.1, -8.88]}, {"Target": "Parameter", "Id": "Param217mao2", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param202mao2", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param203mao2", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param204mao2", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param199mao2", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param200mao2", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param201mao2", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param208mao2", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param209mao2", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param210mao2", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param205mao2", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param206mao2", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param207mao2", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param212mao2", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param213mao2", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param214mao2", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param215mao2", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param198mao2", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param219mao2", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation143mao2", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param264", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param100", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param105", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param108", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param109", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param107", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param284", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param101", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param106", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param104", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param103", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param102", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation71", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param148", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param146", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param110", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param111", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param248", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param112", "Segments": [0, -30, 0, 9.1, -30]}, {"Target": "Parameter", "Id": "Param249", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param206", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param114", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param113", "Segments": [0, 1, 0, 9.1, 1]}, {"Target": "Parameter", "Id": "Param253", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param254", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param158", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param149", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param150", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param151", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param255", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param256", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param153", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param152", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param155", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param154", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param181", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param180", "Segments": [0, 0.5, 0, 9.1, 0.5]}, {"Target": "Parameter", "Id": "Param156", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param157", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param159", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param160", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param119", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param265", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param118", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param120", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param121", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param161", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param259", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param123", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param124", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param130", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param131", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param178", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param179", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param125", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param126", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param127", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param128", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param171", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param173", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param172", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param122", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param170", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param129", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param132", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param250", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param258", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param162", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param169", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param174", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param175", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param163", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param164", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param165", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param166", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param167", "Segments": [0, 0, 0, 9.1, 0]}, {"Target": "Parameter", "Id": "Param168", "Segments": [0, 0, 0, 9.1, 0]}], "UserData": [{"Time": 0.0, "Value": ""}, {"Time": 0.333, "Value": ""}, {"Time": 8.6, "Value": ""}]}