{"Version": 3, "Meta": {"Duration": 27.333, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 490, "TotalSegmentCount": 7702, "TotalPointCount": 8366, "UserDataCount": 3, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 0, 11.8, 0.023, 1, 11.811, 0.023, 11.822, 0.039, 11.833, 0, 1, 12.278, -1.541, 12.722, -2.4, 13.167, -2.4, 1, 13.611, -2.4, 14.056, -1.918, 14.5, 0, 1, 14.844, 1.486, 15.189, 3, 15.533, 3, 1, 15.755, 3, 15.978, -6.998, 16.2, -9.024, 1, 16.522, -11.962, 16.845, -12, 17.167, -12, 1, 17.389, -12, 17.611, 3.731, 17.833, 11, 1, 18.133, 20.813, 18.433, 22, 18.733, 22, 1, 18.878, 22, 19.022, 20.199, 19.167, 15.908, 1, 19.189, 15.248, 19.211, 14.271, 19.233, 14.271, 0, 19.3, 15.518, 0, 19.4, 12, 0, 19.433, 13.803, 0, 19.533, 10.138, 0, 19.6, 10.995, 0, 19.667, 6.316, 0, 19.733, 7.486, 1, 19.755, 7.486, 19.778, 4.951, 19.8, 4.679, 1, 19.822, 4.407, 19.845, 4.445, 19.867, 4.445, 0, 20, 5.265, 0, 20.067, 5.22, 1, 20.289, 5.22, 20.511, 5.253, 20.733, 5.459, 1, 20.8, 5.52, 20.866, 10.605, 20.933, 10.605, 0, 21.467, 5.22, 2, 21.733, 5.22, 0, 22.033, 12, 0, 22.3, 1, 1, 22.522, 1, 22.745, 3.699, 22.967, 5.22, 1, 23.211, 6.893, 23.456, 7, 23.7, 7, 2, 25.833, 7, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 0, 11.8, -0.034, 0, 11.833, 0, 0, 12.5, -6.54, 0, 13.833, 6.54, 0, 14.5, 0, 2, 17.167, 0, 0, 17.833, -16, 2, 18.733, -16, 0, 19.1, -22, 1, 19.233, -22, 19.367, -20.016, 19.5, -18, 1, 19.667, -15.48, 19.833, -14.823, 20, -14.823, 0, 20.2, -18.593, 0, 20.7, 0, 0, 21, -20.276, 0, 21.2, -18, 0, 21.267, -18.478, 0, 21.333, -17.42, 0, 21.4, -18, 0, 21.467, -17.42, 0, 21.533, -18, 0, 21.6, -17.42, 0, 21.633, -18, 0, 21.7, -17.42, 1, 21.711, -17.42, 21.722, -17.843, 21.733, -18, 1, 21.833, -19.415, 21.933, -20, 22.033, -20, 0, 22.3, -15.38, 0, 22.567, -24, 1, 22.7, -24, 22.834, -24.209, 22.967, -20, 1, 23.211, -12.283, 23.456, -2, 23.7, -2, 1, 23.8, -2, 23.9, -10.244, 24, -10.698, 1, 24.611, -13.474, 25.222, -14.34, 25.833, -14.34, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 0, 11.8, 0.013, 1, 11.811, 0.013, 11.822, 0.016, 11.833, 0, 1, 12.278, -0.626, 12.722, -0.96, 13.167, -0.96, 1, 13.611, -0.96, 14.056, -1.072, 14.5, 0, 1, 14.844, 0.831, 15.189, 10.724, 15.533, 10.724, 1, 15.766, 10.724, 16, 5.662, 16.233, 2.274, 1, 16.544, -2.243, 16.856, -3, 17.167, -3, 1, 17.389, -3, 17.611, 1.808, 17.833, 6, 1, 18.133, 11.659, 18.433, 13, 18.733, 13, 1, 19.155, 13, 19.578, 11.532, 20, 10.022, 1, 20.022, 9.942, 20.045, 10, 20.067, 10, 2, 21.067, 10, 2, 21.467, 10, 2, 21.733, 10, 0, 22.033, 7.92, 0, 22.3, 10, 2, 22.967, 10, 0, 23.7, 5, 0, 25.833, 6.68, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 2, 10.667, 1, 2, 11.633, 1, 2, 11.8, 1, 2, 11.833, 1, 2, 12.267, 1, 0, 12.367, 0, 0, 12.5, 1, 2, 14.5, 1, 2, 14.933, 1, 0, 15.033, 0, 0, 15.167, 1, 2, 17.167, 1, 2, 17.667, 1, 0, 17.733, 0, 2, 17.767, 0, 0, 17.867, 1, 1, 18.222, 1, 18.578, 0.973, 18.933, 0.9, 1, 18.966, 0.893, 19, 0, 19.033, 0, 0, 19.2, 0.9, 2, 19.567, 0.9, 0, 19.667, 0, 0, 19.833, 1, 2, 20, 1, 0, 20.067, 0, 0, 20.167, 1, 2, 20.633, 1, 0, 20.8, 0, 2, 21.067, 0, 2, 21.467, 0, 2, 21.733, 0, 2, 23.067, 0, 0, 23.333, 1, 0, 23.467, 0.974, 0, 23.6, 1, 0, 23.733, 0.974, 0, 23.867, 1, 0, 24, 0.974, 0, 24.133, 1, 0, 24.267, 0.974, 0, 24.4, 1, 0, 24.533, 0.974, 0, 24.667, 1, 0, 24.8, 0.974, 0, 24.933, 1, 2, 25.833, 1, 0, 26, 0, 2, 26.067, 0, 1, 26.111, 0, 26.156, 0.891, 26.2, 0.9, 1, 26.578, 0.974, 26.955, 1, 27.333, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 11.833, 0, 2, 14.5, 0, 2, 17.167, 0, 2, 17.667, 0, 2, 17.867, 0, 2, 18.933, 0, 2, 20, 0, 0, 20.067, 1, 0, 20.167, 0.1, 2, 20.633, 0.1, 0, 20.8, 1, 2, 21.067, 1, 2, 21.467, 1, 2, 21.733, 1, 2, 23.067, 1, 0, 23.333, 0, 2, 23.6, 0, 2, 23.867, 0, 2, 24.133, 0, 2, 24.4, 0, 2, 24.667, 0, 2, 24.933, 0, 2, 25.833, 0, 2, 27.333, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 2, 10.667, 1, 2, 11.633, 1, 2, 11.8, 1, 2, 11.833, 1, 2, 12.267, 1, 0, 12.367, 0, 0, 12.5, 1, 2, 14.5, 1, 2, 14.933, 1, 0, 15.033, 0, 0, 15.167, 1, 2, 17.167, 1, 2, 17.667, 1, 0, 17.733, 0, 2, 17.767, 0, 0, 17.867, 1, 1, 18.222, 1, 18.578, 0.973, 18.933, 0.9, 1, 18.966, 0.893, 19, 0, 19.033, 0, 0, 19.2, 0.9, 2, 19.567, 0.9, 0, 19.667, 0, 0, 19.833, 1, 2, 20, 1, 0, 20.067, 0, 0, 20.167, 1, 2, 20.633, 1, 0, 20.8, 0, 2, 21.067, 0, 2, 21.467, 0, 2, 21.733, 0, 2, 23.067, 0, 0, 23.333, 1, 0, 23.467, 0.973, 0, 23.6, 1, 0, 23.733, 0.973, 0, 23.867, 1, 0, 24, 0.973, 0, 24.133, 1, 0, 24.267, 0.973, 0, 24.4, 1, 0, 24.533, 0.973, 0, 24.667, 1, 0, 24.8, 0.973, 0, 24.933, 1, 2, 25.833, 1, 0, 26, 0, 2, 26.067, 0, 1, 26.111, 0, 26.156, 0.891, 26.2, 0.9, 1, 26.578, 0.974, 26.955, 1, 27.333, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 11.833, 0, 2, 14.5, 0, 2, 17.167, 0, 2, 17.667, 0, 2, 17.867, 0, 2, 18.933, 0, 2, 20, 0, 0, 20.067, 1, 0, 20.167, 0, 2, 20.633, 0, 0, 20.8, 1, 2, 21.067, 1, 2, 21.467, 1, 2, 21.733, 1, 2, 23.067, 1, 0, 23.333, 0, 2, 25.833, 0, 2, 27.333, 0]}, {"Target": "Parameter", "Id": "Param76", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 11.833, 0, 2, 14.5, 0, 2, 16.9, 0, 0, 17, -30, 2, 18.933, -30, 2, 20, -30, 0, 21.067, 0, 2, 21.467, 0, 2, 21.733, 0, 2, 22.967, 0, 0, 23.233, 30, 0, 23.433, 15.5, 0, 23.633, 30, 2, 23.733, 30, 0, 23.933, 15.5, 0, 24.133, 30, 0, 24.333, 15.5, 0, 24.533, 30, 0, 24.733, 15.5, 0, 24.933, 30, 0, 25.133, 15.5, 0, 25.333, 30, 0, 25.5, 15.5, 0, 25.667, 30, 1, 25.722, 30, 25.778, 24.558, 25.833, 15.5, 1, 25.9, 4.63, 25.966, 0, 26.033, 0, 2, 27.333, 0]}, {"Target": "Parameter", "Id": "Param273", "Segments": [0, 0, 2, 22.967, 0, 0, 23.667, 1, 2, 23.7, 1, 2, 25.833, 1, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 11.833, 0, 2, 14.5, 0, 0, 17.167, -0.8, 1, 17.234, -0.8, 17.3, -0.258, 17.367, -0.2, 1, 17.478, -0.103, 17.589, -0.095, 17.7, -0.008, 1, 17.744, 0.027, 17.789, 0.5, 17.833, 0.5, 0, 17.9, 0.3, 0, 18.733, 0.6, 0, 18.8, 0.41, 2, 18.933, 0.41, 0, 19.033, 0.1, 0, 19.2, 0.2, 2, 19.367, 0.2, 0, 19.433, -0.6, 2, 19.6, -0.6, 0, 19.667, 0.6, 2, 19.867, 0.6, 0, 19.933, 0.1, 2, 20, 0.1, 2, 21.067, 0.1, 2, 21.467, 0.1, 2, 21.733, 0.1, 2, 22.967, 0.1, 1, 23.211, 0.1, 23.456, -0.161, 23.7, -0.17, 1, 24.411, -0.197, 25.122, -0.2, 25.833, -0.2, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 11.833, 0, 2, 14.5, 0, 0, 17.167, 0.1, 2, 17.367, 0.1, 1, 17.478, 0.1, 17.589, 0.091, 17.7, 0.02, 1, 17.744, -0.008, 17.789, -0.2, 17.833, -0.2, 2, 17.9, -0.2, 0, 18.733, 0, 2, 18.8, 0, 2, 18.933, 0, 0, 19.033, -0.2, 0, 19.167, 0.236, 0, 20, -0.2, 2, 21.067, -0.2, 2, 21.467, -0.2, 2, 21.733, -0.2, 2, 22.967, -0.2, 0, 23.7, 0.3, 1, 24.411, 0.3, 25.122, 0.291, 25.833, 0.2, 1, 26.333, 0.136, 26.833, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 11.833, 0, 2, 14.5, 0, 2, 17.167, 0, 2, 17.833, 0, 2, 18.933, 0, 0, 19.267, -0.3, 2, 20, -0.3, 0, 20.667, 0, 0, 20.8, -0.5, 2, 21.467, -0.5, 2, 21.733, -0.5, 2, 22.067, -0.5, 2, 23.067, -0.5, 2, 23.333, -0.5, 0, 23.467, -0.479, 0, 23.6, -0.5, 0, 23.733, -0.479, 0, 23.867, -0.5, 0, 24, -0.479, 0, 24.133, -0.5, 0, 24.267, -0.479, 0, 24.4, -0.5, 0, 24.533, -0.479, 0, 24.667, -0.5, 0, 24.8, -0.479, 0, 24.933, -0.5, 2, 25.833, -0.5, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 11.833, 0, 2, 14.5, 0, 2, 17.167, 0, 2, 17.833, 0, 2, 18.933, 0, 0, 19.267, -0.3, 2, 20, -0.3, 0, 20.667, 0, 0, 20.8, -0.4, 2, 21.467, -0.4, 2, 21.733, -0.4, 0, 22.067, -0.6, 2, 23.067, -0.6, 1, 23.156, -0.6, 23.244, -0.546, 23.333, -0.5, 1, 23.378, -0.477, 23.422, -0.479, 23.467, -0.479, 0, 23.6, -0.5, 0, 23.733, -0.479, 0, 23.867, -0.5, 0, 24, -0.479, 0, 24.133, -0.5, 0, 24.267, -0.479, 0, 24.4, -0.5, 0, 24.533, -0.479, 0, 24.667, -0.5, 0, 24.8, -0.479, 0, 24.933, -0.5, 2, 25.833, -0.5, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 11.833, 0, 2, 14.5, 0, 2, 17.167, 0, 2, 17.833, 0, 2, 18.933, 0, 2, 20, 0, 2, 20.667, 0, 0, 20.8, 0.2, 2, 21.467, 0.2, 2, 21.733, 0.2, 0, 22.067, -0.8, 2, 23.067, -0.8, 0, 23.333, 0.2, 2, 25.833, 0.2, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 11.833, 0, 2, 14.5, 0, 2, 17.167, 0, 2, 17.833, 0, 2, 18.933, 0, 2, 20, 0, 2, 20.667, 0, 2, 20.8, 0, 2, 21.467, 0, 2, 21.733, 0, 0, 22.067, -1, 2, 23.067, -1, 0, 23.333, 0, 2, 25.833, 0, 2, 27.333, 0]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, -1, 2, 10.667, -1, 2, 11.633, -1, 2, 11.8, -1, 2, 11.833, -1, 2, 14.5, -1, 2, 17.167, -1, 0, 17.833, -0.8, 0, 18.933, -1, 2, 20, -1, 2, 20.667, -1, 0, 20.8, -0.7, 2, 21.467, -0.7, 2, 21.733, -0.7, 2, 22.067, -0.7, 2, 23.067, -0.7, 2, 23.333, -0.7, 2, 25.833, -0.7, 0, 27.333, -1]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, -1, 2, 10.667, -1, 2, 11.633, -1, 2, 11.8, -1, 2, 11.833, -1, 2, 14.5, -1, 2, 17.167, -1, 0, 17.833, -0.7, 0, 18.933, -1, 2, 20, -1, 2, 20.667, -1, 0, 20.8, -0.7, 2, 21.467, -0.7, 2, 21.733, -0.7, 2, 22.067, -0.7, 2, 23.067, -0.7, 2, 23.333, -0.7, 2, 25.833, -0.7, 0, 27.333, -1]}, {"Target": "Parameter", "Id": "Param", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 1, 11.689, 0, 11.744, -0.001, 11.8, -0.005, 1, 11.811, -0.006, 11.822, -30, 11.833, -30, 2, 14.5, -30, 0, 15.967, 0, 0, 17.167, -30, 2, 17.833, -30, 2, 18.933, -30, 2, 20, -30, 0, 21.067, 30, 2, 21.467, 30, 2, 21.733, 30, 2, 22.967, 30, 2, 23.133, 30, 1, 23.278, 30, 23.422, 14.07, 23.567, 8, 1, 23.745, 0.529, 23.922, 0, 24.1, 0, 0, 24.267, 23, 2, 24.567, 23, 0, 24.8, 30, 2, 25.833, 30, 0, 27.333, -30]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 0, 11.8, -0.002, 0, 11.833, 1, 2, 14.5, 1, 0, 15.967, -1, 2, 17.167, -1, 2, 17.833, -1, 2, 18.933, -1, 0, 19.3, -0.4, 0, 20, -1, 2, 21.067, -1, 2, 21.467, -1, 2, 21.733, -1, 0, 23.133, -0.4, 1, 23.278, -0.4, 23.422, -0.401, 23.567, -0.5, 1, 23.689, -0.584, 23.811, -0.798, 23.933, -0.798, 0, 24.1, 0.1, 0, 24.267, -1.5, 2, 24.567, -1.5, 1, 24.989, -1.5, 25.411, -1.206, 25.833, -1, 1, 25.978, -0.929, 26.122, -0.999, 26.267, -0.9, 1, 26.622, -0.655, 26.978, 1, 27.333, 1]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 0, 11.8, 0.002, 0, 11.833, 0, 2, 14.5, 0, 0, 15.967, 0.4, 2, 17.167, 0.4, 0, 17.833, 0, 2, 18.933, 0, 0, 19.3, 0.4, 0, 20, 0, 0, 21.067, 0.5, 0, 21.467, 0, 2, 21.733, 0, 0, 21.933, 0.5, 0, 22.133, 0.3, 0, 22.333, 0.8, 0, 22.433, 0.4, 0, 22.567, 0.6, 0, 22.767, 0.4, 0, 23.133, 0.5, 0, 23.267, 0.1, 0, 23.4, 0.6, 1, 23.456, 0.6, 23.511, 0.383, 23.567, 0.3, 1, 23.634, 0.201, 23.7, 0.2, 23.767, 0.2, 0, 23.933, 0.7, 0, 24.1, 0.2, 0, 24.267, 0.4, 0, 24.367, 0.1, 1, 24.434, 0.1, 24.5, 0.315, 24.567, 0.4, 1, 24.645, 0.499, 24.722, 0.5, 24.8, 0.5, 0, 24.9, 0.426, 0, 25, 0.483, 0, 25.067, 0.402, 0, 25.133, 0.477, 0, 25.2, 0.418, 0, 25.267, 0.457, 0, 25.333, 0.411, 0, 25.4, 0.453, 0, 25.533, 0.413, 0, 25.6, 0.45, 0, 25.667, 0.393, 0, 25.767, 0.442, 0, 25.833, 0.4, 0, 25.967, 0.439, 0, 26.067, 0.4, 0, 26.167, 0.429, 0, 26.233, 0, 2, 27.333, 0]}, {"Target": "Parameter", "Id": "Param269", "Segments": [0, 0, 2, 14.5, 0, 2, 17.2, 0, 0, 17.833, 0.2, 2, 20, 0.2, 2, 20.667, 0.2, 2, 21.067, 0.2, 2, 21.467, 0.2, 2, 21.733, 0.2, 2, 22.967, 0.2, 2, 25.833, 0.2, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param252", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 11.833, 0, 2, 14.5, 0, 2, 17.833, 0, 2, 18.933, 0, 2, 20, 0, 2, 20.667, 0, 0, 21.067, 1, 2, 21.467, 1, 2, 21.733, 1, 2, 22.967, 1, 2, 25.833, 1, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param184", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 11.833, 0, 2, 14.5, 0, 2, 17.833, 0, 2, 18.933, 0, 2, 20, 0, 2, 20.667, 0, 0, 20.833, 1, 2, 25.833, 1, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param185", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 11.833, 0, 2, 14.5, 0, 2, 17.833, 0, 2, 18.933, 0, 2, 20, 0, 2, 22.967, 0, 0, 23.067, 0.7, 0, 23.167, 0, 0, 23.267, 0.7, 0, 23.4, 0, 0, 23.5, 0.7, 0, 23.633, 0, 0, 23.733, 0.7, 0, 23.833, 0, 0, 23.933, 0.7, 0, 24.067, 0, 0, 24.167, 0.7, 0, 24.3, 0, 0, 24.4, 0.7, 0, 24.5, 0, 0, 24.6, 0.7, 0, 24.733, 0, 0, 24.833, 0.7, 0, 24.967, 0, 0, 25.067, 0.7, 0, 25.167, 0, 0, 25.267, 0.7, 0, 25.4, 0, 0, 25.5, 0.7, 0, 25.6, 0, 0, 25.7, 0.7, 0, 25.8, 0, 0, 25.9, 0.7, 0, 26.033, 0, 2, 27.333, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 0, 11.8, -0.043, 0, 11.833, 0, 0, 12.5, -19, 0, 13.833, 3.788, 1, 14.055, 3.788, 14.278, 3.96, 14.5, 0, 1, 14.989, -8.712, 15.478, -18, 15.967, -18, 0, 16.5, -10.497, 0, 17.833, -20, 0, 18.667, -4, 2, 20, -4, 1, 20.122, -4, 20.245, -13.551, 20.367, -16.079, 1, 20.545, -19.756, 20.722, -20, 20.9, -20, 0, 21.467, -19, 2, 21.733, -19, 0, 21.867, -23.163, 0, 22.233, -11.117, 0, 22.7, -19, 1, 22.789, -19, 22.878, -19.461, 22.967, -18, 1, 23.211, -13.983, 23.456, -6.48, 23.7, -6.48, 1, 24.411, -6.48, 25.122, -8.974, 25.833, -16.079, 1, 25.933, -17.078, 26.033, -27.912, 26.133, -27.912, 0, 26.867, 3.788, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 11.833, 0, 0, 13.167, -6.9, 0, 14.5, 0, 0, 15.3, -19, 0, 15.733, 1, 1, 15.989, 1, 16.244, 1.317, 16.5, 0, 1, 16.944, -2.291, 17.389, -8, 17.833, -8, 2, 18.667, -8, 0, 19.2, -19, 1, 19.267, -19, 19.333, -16.032, 19.4, -14.707, 1, 19.556, -11.614, 19.711, -9.776, 19.867, -7.035, 1, 19.911, -6.252, 19.956, -5.449, 20, -5.449, 0, 20.267, -14, 0, 20.667, 1.914, 0, 20.9, -14, 1, 20.956, -14, 21.011, -12.4, 21.067, -11.27, 1, 21.2, -8.559, 21.334, -7.73, 21.467, -7.73, 2, 21.733, -7.73, 1, 21.844, -7.73, 21.956, -5.359, 22.067, -1.824, 1, 22.134, 0.298, 22.2, 0.91, 22.267, 0.91, 0, 22.967, -7.73, 0, 23.7, 0.61, 2, 25.833, 0.61, 0, 26.1, 3.863, 0, 26.7, -3.754, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 11.833, 0, 0, 12.5, 0.6, 0, 13.833, -0.6, 1, 14.055, -0.6, 14.278, -0.754, 14.5, 0, 1, 14.756, 0.867, 15.011, 5.94, 15.267, 5.94, 1, 15.478, 5.94, 15.689, 2.042, 15.9, 0.933, 1, 16.1, -0.118, 16.3, 0, 16.5, 0, 2, 17.067, 0, 1, 17.322, 0, 17.578, 0.908, 17.833, 1.86, 1, 18.111, 2.895, 18.389, 3.14, 18.667, 3.14, 2, 20, 3.14, 2, 21.067, 3.14, 2, 21.467, 3.14, 2, 21.733, 3.14, 2, 22.967, 3.14, 0, 23.7, 0, 2, 25.833, 0, 2, 27.333, 0]}, {"Target": "Parameter", "Id": "Param267", "Segments": [0, 0, 2, 11.8, 0, 2, 14.5, 0, 2, 16.5, 0, 0, 17.833, 2.46, 2, 18.667, 2.46, 2, 20, 2.46, 2, 21.067, 2.46, 2, 21.467, 2.46, 2, 21.733, 2.46, 2, 22.967, 2.46, 0, 23.7, 4.8, 2, 25.833, 4.8, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param3", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 11.833, 0, 0, 12.5, -16, 0, 13.4, 0, 2, 14.5, 0, 2, 16.5, 0, 2, 17.833, 0, 2, 18.667, 0, 2, 20, 0, 2, 21.067, 0, 2, 21.467, 0, 2, 21.733, 0, 2, 22.967, 0, 2, 25.833, 0, 0, 26.133, 1.611, 0, 26.833, -2.275, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param4", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 11.833, 0, 0, 12.5, -16, 0, 13.4, 0, 1, 13.767, 0, 14.133, -0.96, 14.5, -6.139, 1, 14.656, -8.337, 14.811, -16.762, 14.967, -16.762, 1, 15.478, -16.762, 15.989, -1.668, 16.5, -0.959, 1, 16.944, -0.343, 17.389, -0.192, 17.833, -0.063, 1, 18.111, 0.017, 18.389, 0, 18.667, 0, 0, 19.2, -3.65, 1, 19.278, -3.65, 19.355, -3.447, 19.433, -2.724, 1, 19.578, -1.381, 19.722, -0.503, 19.867, -0.503, 2, 20, -0.503, 0, 20.267, -1.643, 0, 20.667, 0.217, 0, 20.867, -2.903, 0, 21.067, -1.73, 2, 21.467, -1.73, 2, 21.733, -1.73, 0, 21.967, -3.65, 0, 22.267, -0.229, 0, 22.6, -3.207, 0, 22.967, -1.73, 2, 25.833, -1.73, 0, 26.2, -4.104, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param266", "Segments": [0, 0, 2, 11.8, 0, 0, 12.133, -12.41, 0, 12.633, 0.375, 0, 13.3, -13.161, 0, 13.933, 0, 0, 14.533, -12.629, 0, 15.2, 0, 0, 15.8, -12.629, 0, 16.5, 0, 2, 17.833, 0, 2, 18.667, 0, 2, 20, 0, 2, 21.067, 0, 2, 21.467, 0, 2, 21.733, 0, 2, 22.967, 0, 2, 25.833, 0, 0, 26.133, 3.976, 0, 26.867, -6.852, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param5", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 0, 11.8, -0.045, 0, 11.833, 0, 2, 14.5, 0, 2, 16.5, 0, 0, 17.833, -21.26, 0, 18.667, -13, 0, 19.3, -14.534, 0, 20, -10.354, 0, 20.667, -19.02, 0, 20.9, -5.983, 0, 21.067, -9, 2, 21.467, -9, 2, 21.733, -9, 0, 21.967, -6.18, 0, 22.133, -10.17, 0, 22.333, 1, 0, 22.6, -8.667, 0, 22.967, -5.64, 0, 23.733, -6, 0, 24, 1, 1, 24.089, 1, 24.178, -0.904, 24.267, -0.915, 1, 24.789, -0.981, 25.311, -1, 25.833, -1, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param6", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 0, 11.8, 0.064, 1, 11.811, 0.064, 11.822, 0.138, 11.833, 0, 1, 12.055, -2.755, 12.278, -4.68, 12.5, -4.68, 0, 13.833, 4.68, 0, 14.5, 0, 2, 16.5, 0, 1, 16.944, 0, 17.389, 12.377, 17.833, 16.74, 1, 18.111, 19.467, 18.389, 18.704, 18.667, 21, 1, 18.978, 23.572, 19.289, 30, 19.6, 30, 2, 20, 30, 0, 20.667, 16.98, 0, 20.9, 30, 0, 21.067, 26.111, 2, 21.467, 26.111, 2, 21.733, 26.111, 0, 21.967, 28.811, 0, 22.133, 23.51, 0, 22.333, 30, 0, 22.6, 26.432, 0, 22.967, 27.731, 0, 23.733, 25, 0, 24, 30, 0, 24.267, 29.34, 0, 25.833, 30, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 11.833, 0, 0, 13.167, 0.546, 0, 14.5, 0, 2, 16.5, 0, 2, 17.833, 0, 2, 18.933, 0, 2, 20, 0, 2, 21.067, 0, 0, 21.467, 0.4, 2, 21.733, 0.4, 2, 22.967, 0.4, 2, 25.833, 0.4, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param201", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 0, 11.733, 1, 2, 11.8, 1, 2, 14.5, 1, 2, 16.1, 1, 2, 17.833, 1, 2, 19.067, 1, 2, 20, 1, 2, 21.067, 1, 2, 21.467, 1, 2, 21.733, 1, 2, 22.967, 1, 2, 25.833, 1, 2, 26.333, 1, 0, 26.367, 0, 2, 27.2, 0, 2, 27.333, 0]}, {"Target": "Parameter", "Id": "Param209", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 0, 11.733, 1, 2, 11.8, 1, 2, 14.5, 1, 2, 16.1, 1, 2, 17.833, 1, 2, 19.067, 1, 2, 20, 1, 2, 21.067, 1, 2, 21.467, 1, 2, 21.733, 1, 2, 22.967, 1, 2, 25.833, 1, 2, 26.333, 1, 0, 26.367, 0, 2, 27.2, 0, 2, 27.333, 0]}, {"Target": "Parameter", "Id": "Param214", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 14.5, 0, 2, 16.1, 0, 2, 17.833, 0, 2, 19.067, 0, 0, 19.1, 1, 2, 20, 1, 2, 21.067, 1, 2, 21.467, 1, 2, 21.733, 1, 2, 22.967, 1, 2, 25.833, 1, 2, 26.333, 1, 0, 26.367, 0, 2, 27.2, 0, 2, 27.333, 0]}, {"Target": "Parameter", "Id": "Param219", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 0, 11.733, 1, 0, 11.8, 0, 2, 14.5, 0, 2, 16.1, 0, 2, 17.833, 0, 2, 19.067, 0, 2, 19.1, 0, 2, 20, 0, 2, 21.067, 0, 2, 21.467, 0, 2, 21.733, 0, 2, 22.967, 0, 2, 25.833, 0, 2, 27.2, 0, 2, 27.333, 0]}, {"Target": "Parameter", "Id": "Param220", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.733, 0, 0, 11.8, 1, 2, 14.5, 1, 2, 16.1, 1, 2, 17.833, 1, 2, 19.067, 1, 0, 19.1, 0, 2, 20, 0, 2, 21.067, 0, 2, 21.467, 0, 2, 21.733, 0, 2, 22.967, 0, 2, 25.833, 0, 2, 27.2, 0, 2, 27.333, 0]}, {"Target": "Parameter", "Id": "Param9", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 14.5, 0, 0, 14.833, -3.591, 0, 16.1, 0, 2, 17.833, 0, 1, 18.2, 0, 18.566, 0.007, 18.933, -0.18, 1, 19.155, -0.293, 19.378, -2.22, 19.6, -2.22, 2, 20, -2.22, 0, 20.667, -2.82, 0, 21, -2.22, 0, 21.3, -2.58, 2, 21.467, -2.58, 2, 21.733, -2.58, 0, 22.067, -3.42, 0, 22.3, -1.96, 1, 22.4, -1.96, 22.5, -2.929, 22.6, -3.023, 1, 22.722, -3.138, 22.845, -3.142, 22.967, -3.225, 1, 23.011, -3.256, 23.056, -3.352, 23.1, -3.352, 0, 23.233, -3.283, 0, 23.367, -3.352, 0, 23.5, -3.283, 0, 23.633, -3.352, 0, 23.767, -3.283, 0, 23.9, -3.352, 0, 24.033, -3.283, 0, 24.167, -3.352, 0, 24.3, -3.283, 0, 24.433, -3.352, 0, 24.567, -3.283, 0, 24.7, -3.352, 0, 24.833, -3.283, 0, 24.967, -3.352, 0, 25.1, -3.283, 0, 25.233, -3.352, 1, 25.278, -3.352, 25.322, -3.331, 25.367, -3.283, 1, 25.522, -3.112, 25.678, -3.023, 25.833, -3.023, 0, 26.5, -4.804, 0, 26.933, 0.123, 0, 27.067, 0, 2, 27.333, 0]}, {"Target": "Parameter", "Id": "Param10", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 1, 12.7, 0, 13.6, 1.149, 14.5, 2.93, 1, 14.711, 3.347, 14.922, 3.623, 15.133, 3.841, 1, 15.455, 4.172, 15.778, 4.26, 16.1, 4.26, 2, 17.833, 4.26, 2, 18.933, 4.26, 0, 19.6, -0.36, 2, 20, -0.36, 0, 20.667, -3.42, 0, 21.067, -0.36, 2, 21.467, -0.36, 2, 21.733, -0.36, 2, 22.967, -0.36, 2, 25.833, -0.36, 0, 26.133, -0.88, 0, 26.333, 13.346, 1, 26.355, 13.346, 26.378, 11.495, 26.4, 10.888, 1, 26.678, 3.307, 26.955, 0, 27.233, 0, 2, 27.333, 0]}, {"Target": "Parameter", "Id": "Param11", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 14.5, 0, 2, 15.133, 0, 2, 16.1, 0, 2, 17.833, 0, 2, 18.933, 0, 2, 20, 0, 2, 21.067, 0, 2, 21.467, 0, 2, 21.733, 0, 2, 22.967, 0, 2, 25.833, 0, 0, 26.4, 30, 0, 26.833, 5.704, 0, 27.067, 7.799, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param22", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 0, 11.833, 1, 2, 14.5, 1, 2, 15.133, 1, 2, 16.1, 1, 2, 17.833, 1, 2, 18.933, 1, 2, 20, 1, 2, 21.067, 1, 2, 21.467, 1, 2, 21.733, 1, 2, 22.967, 1, 2, 25.833, 1, 2, 27.2, 1, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param13", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 1, 12.7, 0, 13.6, 2.921, 14.5, 10.934, 1, 14.711, 12.814, 14.922, 15.9, 15.133, 15.9, 2, 16.1, 15.9, 2, 17.833, 15.9, 2, 18.933, 15.9, 0, 19.6, 6, 2, 20, 6, 2, 20.667, 6, 0, 21, 1.74, 0, 21.3, 4.433, 2, 21.467, 4.433, 2, 21.733, 4.433, 2, 22.967, 4.433, 0, 23.7, -2, 2, 25.833, -2, 0, 27.2, 0, 2, 27.333, 0]}, {"Target": "Parameter", "Id": "Param14", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 14.5, 0, 2, 15.133, 0, 2, 16.1, 0, 2, 17.833, 0, 2, 18.933, 0, 2, 20, 0, 2, 21.067, 0, 2, 21.467, 0, 2, 21.733, 0, 2, 22.967, 0, 2, 25.833, 0, 0, 26.467, -30, 0, 26.9, 0, 0, 27.2, -0.04, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param210", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.733, 0, 2, 11.8, 0, 1, 12.911, 0, 14.022, 1.664, 15.133, 1.7, 1, 16.033, 1.729, 16.933, 1.721, 17.833, 1.74, 1, 18.2, 1.748, 18.566, 2.318, 18.933, 2.7, 1, 19.011, 2.781, 19.089, 4.134, 19.167, 4.134, 0, 19.6, 0.12, 2, 20, 0.12, 0, 20.667, 0.48, 1, 20.778, 0.48, 20.889, 0.351, 21, 0.12, 1, 21.1, -0.088, 21.2, -0.18, 21.3, -0.18, 2, 21.467, -0.18, 2, 21.733, -0.18, 0, 22.067, 0.24, 0, 22.3, -0.19, 0, 22.6, 0.415, 1, 22.722, 0.415, 22.845, 0.209, 22.967, 0.069, 1, 23.011, 0.018, 23.056, 0.027, 23.1, -0.004, 1, 23.144, -0.036, 23.189, -0.071, 23.233, -0.071, 0, 23.367, -0.004, 0, 23.5, -0.071, 0, 23.633, -0.004, 0, 23.767, -0.071, 0, 23.9, -0.004, 0, 24.033, -0.071, 0, 24.167, -0.004, 0, 24.3, -0.071, 0, 24.433, -0.004, 0, 24.567, -0.071, 0, 24.7, -0.004, 0, 24.833, -0.071, 0, 24.967, -0.004, 0, 25.1, -0.071, 0, 25.233, -0.004, 0, 25.367, -0.071, 0, 25.833, 0.069, 0, 26.133, -0.778, 1, 26.244, -0.778, 26.356, 11.579, 26.467, 16.846, 1, 26.6, 16.846, 26.734, 16.739, 26.867, 13.091, 1, 26.978, 10.05, 27.089, 0, 27.2, 0, 2, 27.333, 0]}, {"Target": "Parameter", "Id": "Param211", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 0, 15.133, 8.196, 1, 15.311, 8.196, 15.489, -6.489, 15.667, -12, 1, 15.878, -18.545, 16.089, -18.9, 16.3, -18.9, 0, 17.833, -16.38, 0, 18.933, -18.48, 0, 19.133, -5.895, 0, 19.6, -12, 2, 20, -12, 0, 20.667, -9.48, 0, 21, -14.04, 2, 21.467, -14.04, 2, 21.733, -14.04, 1, 21.844, -14.04, 21.956, -14.565, 22.067, -15.6, 1, 22.145, -16.325, 22.222, -16.593, 22.3, -17.76, 1, 22.4, -19.26, 22.5, -22.483, 22.6, -22.483, 1, 22.722, -22.483, 22.845, -20.646, 22.967, -17.572, 1, 23.011, -16.454, 23.056, -14.791, 23.1, -14.689, 1, 23.144, -14.586, 23.189, -14.607, 23.233, -14.607, 0, 23.367, -14.689, 0, 23.5, -14.607, 0, 23.633, -14.689, 0, 23.767, -14.607, 0, 23.9, -14.689, 0, 24.033, -14.607, 0, 24.167, -14.689, 0, 24.3, -14.607, 0, 24.433, -14.689, 0, 24.567, -14.607, 0, 24.7, -14.689, 0, 24.833, -14.607, 0, 24.967, -14.689, 0, 25.1, -14.607, 0, 25.233, -14.689, 0, 25.367, -14.607, 1, 25.522, -14.607, 25.678, -15.452, 25.833, -17.572, 1, 25.989, -19.691, 26.144, -21, 26.3, -21, 0, 27.2, 0, 2, 27.333, 0]}, {"Target": "Parameter", "Id": "Param212", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 0, 15.133, 1.38, 0, 16, 0, 2, 17.833, 0, 2, 18.933, 0, 2, 20, 0, 2, 21.467, 0, 2, 21.733, 0, 2, 22.967, 0, 2, 25.833, 0, 2, 27.2, 0, 2, 27.333, 0]}, {"Target": "Parameter", "Id": "Param228", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 0, 15.133, -22, 0, 15.767, 0, 2, 17.833, 0, 0, 18.933, -3, 1, 19.055, -3, 19.178, -2.374, 19.3, -1.277, 1, 19.4, -0.379, 19.5, 0, 19.6, 0, 2, 20, 0, 2, 20.667, 0, 0, 21, 4.02, 2, 21.467, 4.02, 2, 21.733, 4.02, 2, 22.967, 4.02, 0, 23.467, -4.5, 2, 25.833, -4.5, 0, 26.333, -16, 0, 27.2, 0, 2, 27.333, 0]}, {"Target": "Parameter", "Id": "Param283", "Segments": [0, 0, 2, 19.267, 0, 0, 19.4, 30, 0, 20.033, -26, 0, 20.267, -2, 0, 20.5, -17.637, 0, 20.867, 30, 1, 20.978, 30, 21.089, 11.65, 21.2, 9, 1, 21.378, 4.76, 21.555, 4.271, 21.733, 0, 1, 21.844, -2.669, 21.956, -25, 22.067, -25, 0, 22.333, 30, 0, 22.633, 11.257, 0, 22.733, 14.708, 0, 22.833, 11.257, 0, 22.933, 14.113, 0, 23.033, 11.193, 0, 23.133, 14.708, 0, 23.233, 10.659, 0, 23.333, 13.062, 0, 23.4, 9.458, 0, 23.5, 13.062, 0, 23.6, 9.458, 0, 23.667, 11.56, 0, 23.733, 9.609, 0, 23.833, 11.26, 0, 23.9, 9.008, 0, 24.033, 11.26, 0, 24.133, 9.008, 0, 24.233, 11.56, 0, 24.333, 8.558, 0, 24.467, 11.861, 1, 24.489, 11.861, 24.511, 9.509, 24.533, 9.008, 1, 24.589, 7.756, 24.644, 7.507, 24.7, 7.507, 0, 24.833, 10.81, 0, 24.9, 9.008, 0, 25.033, 11.56, 0, 25.133, 6.906, 0, 25.267, 11.257, 0, 25.367, 7.507, 0, 25.467, 11.257, 0, 25.567, 7.056, 0, 25.667, 10.81, 0, 25.767, 8.558, 0, 25.867, 11.257, 0, 25.933, 8.708, 0, 26, 10.359, 1, 26.022, 10.359, 26.045, 8.092, 26.067, 7.807, 1, 26.489, 2.4, 26.911, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param221", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 14.5, 0, 2, 17.833, 0, 2, 19.067, 0, 0, 19.1, 1, 2, 20, 1, 2, 21.067, 1, 2, 21.467, 1, 2, 21.733, 1, 2, 22.967, 1, 2, 25.833, 1, 2, 26.5, 1, 0, 26.533, 0, 2, 27.333, 0]}, {"Target": "Parameter", "Id": "Param223", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 14.5, 0, 2, 17.833, 0, 2, 19.067, 0, 0, 19.1, 1, 2, 20, 1, 2, 21.067, 1, 2, 21.467, 1, 2, 21.733, 1, 2, 22.967, 1, 2, 25.833, 1, 2, 26.5, 1, 0, 26.533, 0, 2, 27.333, 0]}, {"Target": "Parameter", "Id": "Param238", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 14.5, 0, 2, 17.833, 0, 2, 19.067, 0, 0, 19.1, 1, 2, 20, 1, 2, 21.067, 1, 2, 21.467, 1, 2, 21.733, 1, 2, 22.967, 1, 2, 25.833, 1, 2, 26.5, 1, 0, 26.533, 0, 2, 27.333, 0]}, {"Target": "Parameter", "Id": "Param15", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 0, 11.8, 4.86, 2, 14.5, 4.86, 2, 17.833, 4.86, 2, 18.933, 4.86, 0, 19.1, 3.72, 0, 19.467, 6.96, 2, 20, 6.96, 0, 20.667, 7.74, 0, 20.933, 5.34, 0, 21.133, 6.375, 2, 21.467, 6.375, 2, 21.733, 6.375, 0, 21.933, 7.155, 0, 22.3, 5.79, 1, 22.522, 5.79, 22.745, 6.097, 22.967, 6.662, 1, 23.011, 6.775, 23.056, 6.81, 23.1, 6.81, 0, 23.233, 6.752, 0, 23.367, 6.928, 0, 23.5, 6.752, 0, 23.633, 6.81, 0, 23.767, 6.752, 0, 23.9, 6.928, 0, 24.033, 6.752, 0, 24.167, 6.81, 0, 24.3, 6.752, 0, 24.433, 6.928, 0, 24.567, 6.752, 0, 24.7, 6.81, 0, 24.833, 6.752, 0, 24.967, 6.928, 0, 25.1, 6.752, 0, 25.233, 6.81, 1, 25.278, 6.81, 25.322, 6.872, 25.367, 6.752, 1, 25.522, 6.334, 25.678, 5.175, 25.833, 5.175, 0, 26.5, 11, 0, 26.9, -0.939, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param16", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 1, 12.7, 0, 13.6, -2.053, 14.5, -5.117, 1, 15.033, -6.932, 15.567, -7.44, 16.1, -7.44, 1, 16.678, -7.44, 17.255, -5.489, 17.833, -2.493, 1, 18.2, -0.592, 18.566, 0, 18.933, 0, 0, 19.1, -3.9, 0, 19.467, 18.12, 2, 20, 18.12, 2, 21.067, 18.12, 2, 21.467, 18.12, 2, 21.733, 18.12, 2, 22.967, 18.12, 2, 25.833, 18.12, 1, 26.066, 18.12, 26.3, 14.744, 26.533, 5.854, 1, 26.644, 1.621, 26.756, -2, 26.867, -2, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param17", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 14.5, 0, 2, 17.833, 0, 2, 18.933, 0, 2, 19.1, 0, 2, 20, 0, 2, 21.067, 0, 2, 21.467, 0, 2, 21.733, 0, 2, 22.967, 0, 2, 25.833, 0, 1, 25.944, 0, 26.056, -4.5, 26.167, -8, 1, 26.278, -11.5, 26.389, -12, 26.5, -12, 0, 26.933, 1.372, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param18", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 14.5, 0, 2, 17.833, 0, 2, 18.933, 0, 2, 19.1, 0, 2, 20, 0, 2, 21.067, 0, 2, 21.467, 0, 2, 21.733, 0, 2, 22.967, 0, 2, 25.833, 0, 0, 25.967, -24.177, 0, 26.967, 11.705, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param19", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 0, 11.8, -30, 2, 14.5, -30, 2, 17.833, -30, 2, 18.933, -30, 2, 19.1, -30, 2, 19.467, -30, 2, 20, -30, 2, 21.067, -30, 2, 21.467, -30, 2, 21.733, -30, 2, 22.967, -30, 2, 23.7, -30, 2, 25.833, -30, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param233", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 0, 11.8, 1.8, 1, 12.7, 1.8, 13.6, 1.187, 14.5, 0.273, 1, 15.033, -0.268, 15.567, -0.42, 16.1, -0.42, 1, 16.678, -0.42, 17.255, -0.41, 17.833, 1.056, 1, 18.2, 1.987, 18.566, 9.078, 18.933, 13.708, 1, 18.989, 14.41, 19.044, 17.24, 19.1, 17.24, 1, 19.189, 17.24, 19.278, 1.86, 19.367, 0.273, 1, 19.445, -1.115, 19.522, -1.776, 19.6, -2.035, 1, 19.733, -2.48, 19.867, -2.818, 20, -3, 1, 20.222, -3.303, 20.445, -3.499, 20.667, -3.72, 1, 20.767, -3.819, 20.867, -3.847, 20.967, -3.847, 0, 21.3, -3.72, 2, 21.467, -3.72, 2, 21.733, -3.72, 1, 21.8, -3.72, 21.866, -4.108, 21.933, -4.14, 1, 22.055, -4.199, 22.178, -4.2, 22.3, -4.259, 1, 22.378, -4.296, 22.455, -4.678, 22.533, -4.724, 1, 22.678, -4.809, 22.822, -4.835, 22.967, -4.908, 1, 23.011, -4.93, 23.056, -4.997, 23.1, -4.997, 0, 23.233, -4.908, 0, 23.367, -4.997, 0, 23.5, -4.908, 0, 23.633, -4.997, 0, 23.767, -4.908, 0, 23.9, -4.997, 0, 24.033, -4.908, 0, 24.167, -4.997, 0, 24.3, -4.908, 0, 24.433, -4.997, 0, 24.567, -4.908, 0, 24.7, -4.997, 0, 24.833, -4.908, 0, 24.967, -4.997, 0, 25.1, -4.908, 0, 25.233, -4.997, 1, 25.278, -4.997, 25.322, -5.003, 25.367, -4.908, 1, 25.522, -4.575, 25.678, -4.304, 25.833, -4.304, 0, 26, -5.43, 1, 26.167, -5.43, 26.333, -1.09, 26.5, 5, 1, 26.6, 8.654, 26.7, 9.578, 26.8, 9.578, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param234", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 0, 11.8, 15, 1, 12.7, 15, 13.6, 7.541, 14.5, -3.568, 1, 15.033, -10.151, 15.567, -12, 16.1, -12, 1, 16.678, -12, 17.255, -4.909, 17.833, 5.953, 1, 18.2, 12.846, 18.566, 15, 18.933, 15, 2, 19.1, 15, 2, 20, 15, 2, 21.3, 15, 2, 21.467, 15, 2, 21.733, 15, 1, 21.844, 15, 21.956, 21.906, 22.067, 24, 1, 22.222, 26.931, 22.378, 27.12, 22.533, 27.12, 0, 22.967, 25.249, 1, 23.011, 25.249, 23.056, 25.332, 23.1, 25.367, 1, 23.144, 25.402, 23.189, 25.401, 23.233, 25.401, 0, 23.367, 25.367, 0, 23.5, 25.401, 0, 23.633, 25.367, 0, 23.767, 25.401, 0, 23.9, 25.367, 0, 24.033, 25.401, 0, 24.167, 25.367, 0, 24.3, 25.401, 0, 24.433, 25.367, 0, 24.567, 25.401, 0, 24.7, 25.367, 0, 24.833, 25.401, 0, 24.967, 25.367, 0, 25.1, 25.401, 0, 25.233, 25.367, 0, 25.367, 25.401, 1, 25.522, 25.401, 25.678, 25.549, 25.833, 25.08, 1, 26.033, 24.477, 26.233, -10.918, 26.433, -10.918, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param235", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 0, 11.8, -16, 1, 12.7, -16, 13.6, -9.924, 14.5, -0.87, 1, 15.033, 4.495, 15.567, 6, 16.1, 6, 1, 16.678, 6, 17.255, 4.282, 17.833, -1.464, 1, 18.2, -5.111, 18.566, -10.589, 18.933, -16, 1, 18.989, -16.82, 19.044, -30, 19.1, -30, 0, 20, -26.16, 0, 20.667, -30, 0, 20.967, -26.04, 0, 21.3, -26.16, 2, 21.467, -26.16, 2, 21.733, -26.16, 2, 22.967, -26.16, 2, 25.833, -26.16, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param142", "Segments": [0, -6.12, 1, 0.389, -6.12, 0.778, -6.014, 1.167, -4.86, 1, 1.322, -4.399, 1.478, 30, 1.633, 30, 2, 2.033, 30, 2, 2.5, 30, 2, 5.767, 30, 2, 10.667, 30, 2, 11.633, 30, 0, 11.667, 8.76, 2, 11.8, 8.76, 2, 14.5, 8.76, 2, 14.7, 8.76, 0, 16.1, 15.06, 2, 17.833, 15.06, 2, 18.933, 15.06, 2, 20, 15.06, 2, 21.067, 15.06, 2, 21.467, 15.06, 2, 21.733, 15.06, 2, 22.967, 15.06, 2, 25.833, 15.06, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param143", "Segments": [0, 0, 2, 1.167, 0, 2, 2, 0, 0, 2.033, -0.36, 2, 2.5, -0.36, 1, 3.244, -0.36, 3.989, -0.373, 4.733, -0.4, 1, 4.755, -0.401, 4.778, -0.58, 4.8, -0.58, 0, 4.9, -0.279, 0, 5, -0.402, 1, 5.033, -0.402, 5.067, -0.402, 5.1, -0.4, 1, 5.322, -0.387, 5.545, -0.381, 5.767, -0.36, 1, 5.889, -0.349, 6.011, 10.56, 6.133, 10.56, 2, 10.667, 10.56, 2, 11.633, 10.56, 0, 11.667, 0, 2, 11.8, 0, 2, 14.5, 0, 2, 14.7, 0, 2, 17.833, 0, 2, 18.933, 0, 2, 20, 0, 2, 21.067, 0, 2, 21.467, 0, 2, 21.733, 0, 2, 22.967, 0, 2, 25.833, 0, 2, 27.333, 0]}, {"Target": "Parameter", "Id": "Param144", "Segments": [0, -0.84, 2, 1.167, -0.84, 0, 1.467, 2.86, 0, 2.033, -10.96, 2, 2.5, -10.96, 2, 4.733, -10.96, 0, 4.8, -11.02, 0, 4.9, -10.9, 0, 5, -10.968, 0, 5.1, -10.96, 2, 5.767, -10.96, 2, 10.667, -10.96, 2, 11.633, -10.96, 0, 11.667, 2.04, 2, 11.8, 2.04, 1, 12.7, 2.04, 13.6, 2.027, 14.5, 2.004, 1, 14.567, 2.002, 14.633, 2.01, 14.7, 2, 1, 15.167, 1.928, 15.633, -0.58, 16.1, -0.58, 2, 16.767, -0.58, 0, 16.8, -0.5, 1, 17.144, -1.488, 17.489, -1.597, 17.833, -1.597, 1, 18.2, -1.99, 18.566, -2, 18.933, -2, 2, 20, -2, 2, 21.067, -2, 2, 21.467, -2, 2, 21.733, -2, 2, 22.967, -2, 2, 25.833, -2, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param147", "Segments": [0, 1, 2, 1.167, 1, 2, 2.033, 1, 2, 2.5, 1, 2, 5.767, 1, 2, 10.667, 1, 2, 11.633, 1, 0, 11.667, 0, 2, 11.8, 0, 2, 14.5, 0, 2, 17.833, 0, 2, 18.933, 0, 2, 20, 0, 2, 21.067, 0, 2, 21.467, 0, 2, 21.733, 0, 2, 22.967, 0, 2, 25.833, 0, 2, 27.333, 0]}, {"Target": "Parameter", "Id": "Param145", "Segments": [0, 1, 2, 1.167, 1, 2, 2.033, 1, 2, 2.5, 1, 2, 5.767, 1, 2, 10.667, 1, 2, 11.633, 1, 0, 11.667, 0, 2, 11.8, 0, 2, 14.5, 0, 2, 17.833, 0, 2, 18.933, 0, 2, 20, 0, 2, 21.067, 0, 2, 21.467, 0, 2, 21.733, 0, 2, 22.967, 0, 2, 25.833, 0, 2, 27.333, 0]}, {"Target": "Parameter", "Id": "Param134", "Segments": [0, 0, 2, 1.167, 0, 2, 2.033, 0, 2, 2.5, 0, 2, 5.767, 0, 2, 10.667, 0, 0, 10.7, 1, 2, 11.633, 1, 0, 11.667, 0, 2, 11.8, 0, 2, 14.5, 0, 2, 17.833, 0, 2, 18.933, 0, 2, 20, 0, 2, 21.067, 0, 2, 21.467, 0, 2, 21.733, 0, 2, 22.967, 0, 2, 25.833, 0, 2, 27.333, 0]}, {"Target": "Parameter", "Id": "Param246", "Segments": [0, 1, 2, 1.167, 1, 2, 2.033, 1, 2, 2.5, 1, 2, 5.767, 1, 2, 10.667, 1, 2, 10.7, 1, 2, 11.633, 1, 2, 11.667, 1, 2, 11.8, 1, 2, 14.5, 1, 2, 17.833, 1, 2, 18.933, 1, 2, 20, 1, 2, 21.067, 1, 2, 21.467, 1, 2, 21.733, 1, 2, 22.967, 1, 2, 25.833, 1, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param141", "Segments": [0, 0, 2, 1.167, 0, 2, 1.433, 0, 0, 1.567, 1, 2, 1.7, 1, 0, 2, 0, 2, 2.033, 0, 2, 2.5, 0, 2, 5.767, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 14.5, 0, 2, 17.833, 0, 2, 18.933, 0, 2, 20, 0, 2, 21.067, 0, 2, 21.467, 0, 2, 21.733, 0, 2, 22.967, 0, 2, 25.833, 0, 2, 27.333, 0]}, {"Target": "Parameter", "Id": "Param182", "Segments": [0, 0, 2, 1.167, 0, 0, 1.433, -1, 0, 1.8, 1, 0, 1.833, 0, 2, 2.033, 0, 2, 2.5, 0, 2, 5.767, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 14.5, 0, 2, 17.833, 0, 2, 18.933, 0, 2, 20, 0, 2, 21.067, 0, 2, 21.467, 0, 2, 21.733, 0, 2, 22.967, 0, 2, 25.833, 0, 2, 27.333, 0]}, {"Target": "Parameter", "Id": "Param247", "Segments": [0, 0, 0, 0.067, 1, 2, 2.033, 1, 2, 2.5, 1, 2, 5.767, 1, 2, 10.667, 1, 2, 11.633, 1, 0, 11.667, 0, 2, 11.8, 0, 2, 14.5, 0, 2, 17.833, 0, 2, 18.933, 0, 2, 20, 0, 2, 21.067, 0, 2, 21.467, 0, 2, 21.733, 0, 2, 22.967, 0, 2, 25.833, 0, 2, 27.333, 0]}, {"Target": "Parameter", "Id": "Param229", "Segments": [0, -0.26, 2, 1.1, -0.26, 2, 1.167, -0.26, 2, 1.467, -0.26, 2, 1.533, -0.26, 1, 1.678, -0.26, 1.822, -2.783, 1.967, -3.696, 1, 1.989, -3.836, 2.011, -3.74, 2.033, -3.74, 2, 4.767, -3.74, 2, 5.3, -3.74, 2, 5.767, -3.74, 0, 6.133, 14.44, 0, 6.267, 13.96, 0, 6.4, 14.44, 0, 6.533, 13.96, 0, 6.667, 14.44, 0, 6.8, 13.96, 0, 6.933, 14.44, 0, 7.067, 13.96, 0, 7.2, 14.44, 0, 7.333, 13.96, 0, 7.467, 14.44, 0, 7.6, 13.96, 0, 7.733, 14.44, 0, 7.867, 13.96, 0, 8, 14.44, 0, 8.133, 13.96, 0, 8.267, 14.44, 2, 10.667, 14.44, 2, 11.633, 14.44, 1, 11.644, 14.44, 11.656, -2.16, 11.667, -2.16, 2, 11.8, -2.16, 2, 14.5, -2.16, 2, 17.833, -2.16, 2, 18.933, -2.16, 2, 20, -2.16, 2, 21.067, -2.16, 2, 21.467, -2.16, 2, 21.733, -2.16, 2, 22.967, -2.16, 2, 25.833, -2.16, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param245", "Segments": [0, 0.72, 0, 1.167, 0.06, 2, 1.533, 0.06, 0, 2.033, -17.16, 2, 4.767, -17.16, 0, 4.833, -17.64, 0, 4.933, -16.98, 0, 5.033, -17.313, 0, 5.133, -17.16, 2, 5.3, -17.16, 2, 5.767, -17.16, 2, 6.133, -17.16, 0, 6.2, -17.52, 0, 6.267, -17.22, 0, 6.333, -17.7, 0, 6.4, -17.28, 0, 6.467, -17.52, 0, 6.533, -17.22, 0, 6.6, -17.7, 0, 6.667, -17.28, 0, 6.733, -17.52, 0, 6.8, -17.22, 0, 6.867, -17.7, 0, 6.933, -17.28, 0, 7, -17.52, 0, 7.067, -17.16, 0, 7.133, -17.52, 0, 7.2, -17.22, 0, 7.267, -17.7, 0, 7.333, -17.28, 0, 7.4, -17.52, 0, 7.467, -17.22, 0, 7.533, -17.7, 0, 7.6, -17.28, 0, 7.667, -17.52, 0, 7.733, -17.22, 0, 7.8, -17.7, 0, 7.867, -17.28, 0, 7.933, -17.52, 0, 8, -17.16, 0, 8.067, -17.52, 0, 8.133, -17.22, 0, 8.2, -17.7, 0, 8.267, -17.28, 0, 8.333, -17.52, 0, 8.4, -17.22, 0, 8.467, -17.7, 0, 8.533, -17.28, 0, 8.6, -17.52, 0, 8.667, -17.22, 0, 8.733, -17.7, 0, 8.8, -17.28, 0, 8.867, -17.52, 2, 9.267, -17.52, 0, 9.333, -17.04, 0, 9.4, -17.46, 0, 9.467, -17.1, 0, 9.533, -17.7, 0, 9.6, -17.46, 0, 9.667, -17.7, 0, 9.733, -17.46, 0, 9.8, -17.76, 0, 9.867, -17.46, 0, 9.933, -17.82, 0, 10, -17.58, 0, 10.067, -17.82, 0, 10.133, -17.58, 0, 10.2, -17.82, 0, 10.267, -17.58, 0, 10.333, -17.82, 0, 10.4, -17.58, 0, 10.467, -17.82, 0, 10.533, -17.58, 0, 10.633, -17.82, 1, 10.644, -17.82, 10.656, -17.78, 10.667, -17.7, 1, 10.678, -17.62, 10.689, -17.58, 10.7, -17.58, 2, 11.633, -17.58, 1, 11.644, -17.58, 11.656, -5.3, 11.667, -5.28, 1, 11.711, -5.201, 11.756, -5.15, 11.8, -5.088, 1, 12.467, -4.173, 13.133, -3.72, 13.8, -3.72, 1, 14.033, -3.72, 14.267, -3.862, 14.5, -4.035, 1, 14.567, -4.084, 14.633, -4.08, 14.7, -4.08, 0, 16.1, -1.5, 2, 17.833, -1.5, 2, 18.933, -1.5, 2, 20, -1.5, 2, 21.067, -1.5, 2, 21.467, -1.5, 2, 21.733, -1.5, 2, 22.967, -1.5, 2, 25.833, -1.5, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param117", "Segments": [0, 0.26, 1, 0.389, 0.26, 0.778, 0.624, 1.167, 1.04, 1, 1.289, 1.171, 1.411, 1.096, 1.533, 1.28, 1, 1.7, 1.531, 1.866, 12.86, 2.033, 12.86, 2, 5.3, 12.86, 2, 5.767, 12.86, 2, 10.667, 12.86, 2, 11.633, 12.86, 0, 11.667, 0, 2, 11.8, 0, 2, 14.5, 0, 2, 17.833, 0, 2, 18.933, 0, 2, 20, 0, 2, 21.067, 0, 2, 21.467, 0, 2, 21.733, 0, 2, 22.967, 0, 2, 25.833, 0, 2, 27.333, 0]}, {"Target": "Parameter", "Id": "Param140", "Segments": [0, 1, 2, 1.167, 1, 2, 5.3, 1, 2, 5.767, 1, 2, 10.667, 1, 2, 11.633, 1, 2, 11.667, 1, 2, 11.8, 1, 2, 14.5, 1, 2, 17.833, 1, 2, 18.933, 1, 2, 20, 1, 2, 21.067, 1, 2, 21.467, 1, 2, 21.733, 1, 2, 22.967, 1, 2, 25.833, 1, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param262", "Segments": [0, 0, 2, 11.633, 0, 0, 11.667, 1, 2, 11.8, 1, 2, 14.5, 1, 2, 17.833, 1, 2, 18.933, 1, 2, 20, 1, 2, 21.067, 1, 2, 21.467, 1, 2, 21.733, 1, 2, 22.967, 1, 2, 25.833, 1, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param263", "Segments": [0, 0, 2, 11.633, 0, 0, 11.667, -1, 2, 11.8, -1, 2, 12.267, -1, 1, 12.778, -1, 13.289, -0.621, 13.8, -0.5, 1, 14.033, -0.445, 14.267, -0.456, 14.5, -0.431, 1, 14.567, -0.424, 14.633, -0.429, 14.7, -0.4, 1, 15.167, -0.196, 15.633, 0.012, 16.1, 0.012, 0, 16.533, -1, 2, 16.733, -1, 0, 17.3, 0.5, 2, 17.833, 0.5, 2, 18.933, 0.5, 2, 20, 0.5, 2, 21.067, 0.5, 2, 21.467, 0.5, 2, 21.733, 0.5, 2, 22.967, 0.5, 2, 25.833, 0.5, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param115", "Segments": [0, 1, 2, 1.167, 1, 2, 5.3, 1, 2, 5.767, 1, 2, 10.667, 1, 0, 10.7, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 14.5, 0, 2, 17.833, 0, 2, 18.933, 0, 2, 20, 0, 2, 21.067, 0, 2, 21.467, 0, 2, 21.733, 0, 2, 22.967, 0, 2, 25.833, 0, 2, 27.333, 0]}, {"Target": "Parameter", "Id": "Param116", "Segments": [0, 0, 2, 4.4, 0, 2, 5.3, 0, 2, 5.767, 0, 2, 9, 0, 0, 9.133, 1, 2, 10.667, 1, 0, 10.7, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 14.5, 0, 2, 17.833, 0, 2, 18.933, 0, 2, 20, 0, 2, 21.067, 0, 2, 21.467, 0, 2, 21.733, 0, 2, 22.967, 0, 2, 25.833, 0, 2, 27.333, 0]}, {"Target": "Parameter", "Id": "Param260", "Segments": [0, -23.135, 2, 9, -23.135, 1, 9.544, -23.135, 10.089, -19.36, 10.633, 10.287, 2, 11.633, 10.287, 2, 11.8, 10.287, 2, 14.5, 10.287, 2, 17.833, 10.287, 2, 18.933, 10.287, 2, 20, 10.287, 2, 21.067, 10.287, 2, 21.467, 10.287, 2, 21.733, 10.287, 2, 22.967, 10.287, 2, 25.833, 10.287, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param261", "Segments": [0, 0.612, 2, 9.167, 0.612, 1, 9.567, 0.612, 9.967, 1.893, 10.367, 6.2, 1, 10.456, 7.157, 10.544, 12.262, 10.633, 12.262, 2, 11.633, 12.262, 2, 11.8, 12.262, 2, 14.5, 12.262, 2, 17.833, 12.262, 2, 18.933, 12.262, 2, 20, 12.262, 2, 21.067, 12.262, 2, 21.467, 12.262, 2, 21.733, 12.262, 2, 22.967, 12.262, 2, 25.833, 12.262, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param133", "Segments": [0, 1, 2, 1.533, 1, 2, 1.867, 1, 0, 2.067, 0, 2, 5.767, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 14.5, 0, 2, 17.833, 0, 2, 18.933, 0, 2, 20, 0, 2, 21.067, 0, 2, 21.467, 0, 2, 21.733, 0, 2, 22.967, 0, 2, 25.833, 0, 2, 27.333, 0]}, {"Target": "Parameter", "Id": "Param139", "Segments": [0, 0, 2, 1.533, 0, 0, 2.033, 1, 2, 5.3, 1, 2, 5.767, 1, 2, 10.667, 1, 2, 11.633, 1, 2, 11.8, 1, 2, 14.5, 1, 2, 17.833, 1, 2, 18.933, 1, 2, 20, 1, 2, 21.067, 1, 2, 21.467, 1, 2, 21.733, 1, 2, 22.967, 1, 2, 25.833, 1, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param257", "Segments": [0, 0, 2, 5.767, 0, 0, 6.133, 1, 2, 10.667, 1, 2, 11.633, 1, 2, 11.8, 1, 2, 14.5, 1, 2, 17.833, 1, 2, 18.933, 1, 2, 20, 1, 2, 21.067, 1, 2, 21.467, 1, 2, 21.733, 1, 2, 22.967, 1, 2, 25.833, 1, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param137", "Segments": [0, 0, 2, 5.3, 0, 2, 5.767, 0, 2, 9, 0, 1, 9.011, 0, 9.022, -30, 9.033, -30, 0, 9.267, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 14.5, 0, 2, 17.833, 0, 2, 18.933, 0, 2, 20, 0, 2, 21.067, 0, 2, 21.467, 0, 2, 21.733, 0, 2, 22.967, 0, 2, 25.833, 0, 2, 27.333, 0]}, {"Target": "Parameter", "Id": "Param135", "Segments": [0, 0, 2, 5.3, 0, 2, 5.767, 0, 2, 10.567, 0, 2, 10.667, 0.14, 2, 10.767, 0.31, 2, 10.867, 0.49, 2, 10.967, 0.7, 2, 11.067, 0.86, 2, 11.167, 0.14, 2, 11.3, 0.14, 1, 11.411, 0.093, 11.522, 0.047, 11.633, 0, 2, 11.8, 0, 2, 14.5, 0, 2, 17.833, 0, 2, 18.933, 0, 2, 20, 0, 2, 21.067, 0, 2, 21.467, 0, 2, 21.733, 0, 2, 22.967, 0, 2, 25.833, 0, 2, 27.333, 0]}, {"Target": "Parameter", "Id": "Param176", "Segments": [0, 0, 2, 5.3, 0, 2, 5.767, 0, 0, 5.867, 1, 2, 10.667, 1, 0, 10.7, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 14.5, 0, 2, 17.833, 0, 2, 18.933, 0, 2, 20, 0, 2, 21.067, 0, 2, 21.467, 0, 2, 21.733, 0, 2, 22.967, 0, 2, 25.833, 0, 2, 27.333, 0]}, {"Target": "Parameter", "Id": "Param136", "Segments": [0, 0, 2, 5.3, 0, 2, 5.767, 0, 0, 6.333, 0.5, 0, 6.9, 0, 0, 7.5, 0.5, 0, 8.167, 0, 0, 8.867, 0.5, 0, 9.567, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 14.5, 0, 2, 17.833, 0, 2, 18.933, 0, 2, 20, 0, 2, 21.067, 0, 2, 21.467, 0, 2, 21.733, 0, 2, 22.967, 0, 2, 25.833, 0, 2, 27.333, 0]}, {"Target": "Parameter", "Id": "Param177", "Segments": [0, 0, 2, 5.3, 0, 1, 5.456, 0.333, 5.611, 0.667, 5.767, 1, 1, 5.845, 0.667, 5.922, 0.333, 6, 0, 1, 6.011, 0.333, 6.022, 0.667, 6.033, 1, 0, 6.267, 0, 1, 6.278, 0.333, 6.289, 0.667, 6.3, 1, 0, 6.533, 0, 1, 6.544, 0.333, 6.556, 0.667, 6.567, 1, 0, 6.8, 0, 1, 6.811, 0.333, 6.822, 0.667, 6.833, 1, 0, 7.067, 0, 1, 7.078, 0.333, 7.089, 0.667, 7.1, 1, 0, 7.333, 0, 1, 7.344, 0.333, 7.356, 0.667, 7.367, 1, 0, 7.6, 0, 1, 7.611, 0.333, 7.622, 0.667, 7.633, 1, 0, 7.867, 0, 1, 7.878, 0.333, 7.889, 0.667, 7.9, 1, 0, 8.133, 0, 1, 8.144, 0.333, 8.156, 0.667, 8.167, 1, 0, 8.4, 0, 1, 8.411, 0.333, 8.422, 0.667, 8.433, 1, 0, 8.667, 0, 1, 8.678, 0.333, 8.689, 0.667, 8.7, 1, 0, 8.933, 0, 1, 8.944, 0.333, 8.956, 0.667, 8.967, 1, 0, 9.2, 0, 1, 9.211, 0.333, 9.222, 0.667, 9.233, 1, 0, 9.467, 0, 1, 9.478, 0.333, 9.489, 0.667, 9.5, 1, 0, 9.733, 0, 1, 9.744, 0.333, 9.756, 0.667, 9.767, 1, 2, 10.667, 1, 2, 11.633, 1, 2, 11.8, 1, 2, 14.5, 1, 2, 17.833, 1, 2, 18.933, 1, 2, 20, 1, 2, 21.067, 1, 2, 21.467, 1, 2, 21.733, 1, 2, 22.967, 1, 2, 25.833, 1, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param251", "Segments": [0, 0, 2, 5.3, 0, 2, 5.767, 0, 2, 9.1, 0, 1, 9.122, 0, 9.145, 0.439, 9.167, 0.5, 3, 9.267, 0.589, 3, 9.367, 0.5, 3, 9.467, 0.589, 3, 9.567, 0.5, 3, 9.667, 0.589, 3, 9.767, 0.5, 3, 9.867, 0.589, 3, 9.967, 0.5, 3, 10.067, 0.589, 3, 10.167, 0.5, 3, 10.267, 0.589, 3, 10.367, 0.5, 3, 10.467, 0.589, 3, 10.567, 0.5, 3, 10.667, 0.589, 3, 10.767, 0.5, 3, 10.867, 0.589, 3, 10.967, 0.5, 2, 11.633, 0.5, 2, 11.8, 0.5, 2, 14.5, 0.5, 2, 17.833, 0.5, 2, 18.933, 0.5, 2, 20, 0.5, 2, 21.067, 0.5, 2, 21.467, 0.5, 2, 21.733, 0.5, 2, 22.967, 0.5, 2, 25.833, 0.5, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param277", "Segments": [0, -17, 0, 12.933, 0, 0, 13.167, -22, 0, 13.433, 18, 0, 13.8, -16, 0, 14.2, 14.692, 0, 14.7, -19, 0, 15.267, 13.298, 0, 15.867, -20.804, 0, 16.267, 14.692, 0, 16.8, -19, 0, 17.2, 14.692, 0, 17.767, -16, 0, 18.233, 12.547, 0, 18.6, -13.995, 0, 18.967, 13.298, 0, 19.467, -13.995, 0, 19.867, 10.938, 0, 20.4, -12.225, 0, 20.767, 21.233, 0, 21.3, -12.225, 0, 21.733, 21.233, 0, 22.3, 1.287, 0, 22.833, 13.298, 0, 23.867, -22, 0, 24.4, 14.692, 0, 24.933, -12.225, 0, 25.367, 14.692, 0, 25.933, -12.225, 0, 26.533, 14.692, 0, 27.167, 0, 2, 27.333, 0]}, {"Target": "Parameter", "Id": "Param278", "Segments": [0, -0.6, 0, 12.933, 0, 0, 13.233, -0.421, 0, 13.667, 0.353, 0, 14.133, -0.421, 0, 14.6, 0.278, 0, 15.1, -0.421, 0, 15.4, 0.278, 0, 16.033, -0.421, 0, 16.5, 0.278, 0, 16.9, -0.6, 0, 17.467, 0.691, 0, 18.067, -0.6, 0, 18.667, 0.691, 0, 19.433, -0.536, 0, 19.8, 0.278, 0, 20.433, -0.45, 0, 20.867, 0.353, 0, 21.3, -0.158, 0, 21.933, 0.353, 0, 22.3, -0.421, 0, 22.7, 0.278, 0, 23.2, -0.315, 0, 23.767, 0.116, 0, 24.433, -0.23, 0, 24.967, 0.116, 0, 25.533, -0.23, 0, 26.167, 0.116, 0, 26.733, -0.211, 0, 27.3, 0, 2, 27.333, 0]}, {"Target": "Parameter", "Id": "Param280", "Segments": [0, 0, 2, 14.8, 0, 0, 15.2, -0.7, 0, 15.733, 0.528, 0, 16.267, -0.638, 0, 16.633, 0.125, 0, 17.1, -0.583, 0, 17.567, 0.553, 0, 18.3, -0.638, 0, 18.767, 0, 2, 19.733, 0, 2, 20.467, 0, 2, 21.333, 0, 2, 22.133, 0, 2, 22.667, 0, 2, 22.967, 0, 2, 25.833, 0, 0, 26.3, -0.323, 0, 26.867, 0.249, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param7", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 14.5, 0, 0, 15.167, -6, 0, 15.8, 7, 0, 16.733, -6, 0, 17.333, 2, 0, 17.833, -3, 0, 18.933, 4, 1, 19.1, 4, 19.266, -8.848, 19.433, -18, 1, 19.622, -28.372, 19.811, -30, 20, -30, 0, 20.5, 12, 0, 21.067, -23, 0, 21.467, 0, 0, 21.733, -13, 0, 22.233, 6, 1, 22.478, 6, 22.722, 4.929, 22.967, 0, 1, 23.1, -2.689, 23.234, -11.712, 23.367, -15, 1, 23.578, -20.206, 23.789, -27.696, 24, -28, 1, 24.611, -28.879, 25.222, -29, 25.833, -29, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param66", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 14.5, 0, 0, 17.467, -21, 0, 18.567, 22, 0, 18.933, 0, 0, 19.2, 11, 0, 20, 0, 2, 20.467, 0, 0, 20.667, -11, 0, 21.067, 18, 0, 21.467, 0, 0, 21.733, 11, 0, 22, -14, 0, 22.533, 12, 0, 22.967, -6, 1, 23.922, -6, 24.878, -4.515, 25.833, 0, 1, 25.989, 0.735, 26.144, 6, 26.3, 6, 0, 27, -2.66, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param67", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 14.5, 0, 0, 17.667, -21, 0, 18.767, 22, 0, 19.133, 0, 0, 19.4, 11, 0, 20.2, 0, 2, 20.667, 0, 0, 20.867, -11, 0, 21.267, 18, 0, 21.667, 0, 0, 21.933, 11, 0, 22.2, -14, 0, 22.733, 12, 0, 23.167, -6, 1, 24.122, -6, 25.078, -4.515, 26.033, 0, 1, 26.189, 0.735, 26.344, 6, 26.5, 6, 0, 27, -2.86, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param68", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 14.5, 0, 0, 17.467, -21, 0, 18.567, 22, 0, 18.933, 0, 0, 19.2, 11, 0, 20, 0, 2, 20.467, 0, 0, 20.667, 9, 0, 21.067, -19, 0, 21.467, 0, 1, 21.556, 0, 21.644, -6.76, 21.733, -12, 1, 21.822, -17.24, 21.911, -18, 22, -18, 0, 22.533, 16, 0, 22.967, -9, 1, 23.922, -9, 24.878, -6.684, 25.833, 0, 1, 25.989, 1.088, 26.144, 6, 26.3, 6, 0, 27, -2.76, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param69", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 14.5, 0, 0, 17.667, -21, 0, 18.767, 23, 0, 19.133, 0, 0, 19.4, 11, 0, 20.2, 0, 2, 20.667, 0, 0, 20.867, 9, 0, 21.267, -19, 0, 21.667, 0, 1, 21.756, 0, 21.844, -6.76, 21.933, -12, 1, 22.022, -17.24, 22.111, -18, 22.2, -18, 0, 22.733, 17, 0, 23.167, -11, 1, 24.122, -11, 25.078, -8.1, 26.033, 0, 1, 26.189, 1.319, 26.344, 6, 26.5, 6, 0, 27, -2.86, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param35", "Segments": [0, 0, 0, 0.267, -0.001, 0, 0.667, 0, 2, 1.067, 0, 2, 1.467, 0, 2, 1.533, 0, 2, 1.8, 0, 2, 1.833, 0, 2, 1.867, 0, 2, 1.967, 0, 2, 2, 0, 2, 2.067, 0, 2, 2.1, 0, 2, 2.133, 0, 2, 2.167, 0, 2, 2.2, 0, 2, 2.233, 0, 2, 2.467, 0, 2, 2.5, 0, 2, 2.6, 0, 2, 2.633, 0, 2, 2.733, 0, 2, 2.767, 0, 2, 11.867, 0, 0, 12.3, 0.433, 0, 13.533, -0.385, 0, 14.867, 2.474, 0, 15.567, -5.942, 0, 15.933, 4.168, 0, 16.333, -1.293, 0, 16.733, 0.866, 0, 17.433, -1.714, 0, 18.6, 1.18, 0, 18.7, 1.104, 0, 19, 3.122, 0, 19.433, -3.79, 0, 19.833, 0.565, 0, 20, -0.067, 0, 20.233, 3.928, 0, 20.567, -6.807, 0, 20.9, 10.815, 0, 21.2, -5.914, 0, 21.6, 2.36, 0, 22.033, -8.425, 0, 22.3, 13.4, 0, 22.633, -6.698, 0, 23.033, 2.519, 0, 23.433, -1.976, 0, 23.833, 1.148, 0, 24.233, -0.395, 0, 24.633, 1.18, 0, 25.033, -0.385, 0, 25.467, 0.433, 0, 26.1, -2.996, 0, 26.467, 3.554, 0, 26.9, -1.639, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param36", "Segments": [0, 0, 0, 0.1, 0.021, 0, 0.5, -0.008, 0, 0.9, 0.003, 0, 1.3, -0.001, 0, 1.7, 0, 2, 2.1, 0, 2, 2.5, 0, 2, 2.533, 0, 2, 2.733, 0, 2, 2.767, 0, 2, 2.9, 0, 2, 3, 0, 2, 3.033, 0, 2, 3.067, 0, 2, 3.233, 0, 2, 3.267, 0, 2, 3.3, 0, 2, 3.4, 0, 2, 3.433, 0, 2, 3.5, 0, 2, 3.533, 0, 2, 3.567, 0, 2, 3.6, 0, 2, 3.633, 0, 2, 3.667, 0, 2, 11.867, 0, 0, 12.133, -0.168, 0, 12.5, 0.162, 0, 12.867, -0.032, 0, 13.267, 0.104, 0, 13.7, -0.092, 0, 14.067, 0.006, 0, 14.767, -0.966, 0, 15.167, 1.526, 0, 15.367, 0.805, 0, 15.5, 1.683, 0, 15.833, -6.171, 0, 16.167, 5.589, 0, 16.533, -3.343, 0, 16.933, 1.694, 0, 17.5, -0.636, 0, 17.9, 0.2, 0, 18.3, -0.438, 0, 18.7, 0.395, 0, 18.933, -1.008, 0, 19.333, 3.069, 0, 19.667, -3.671, 0, 20, 2.15, 0, 20.2, -1.96, 0, 20.467, 5.666, 0, 20.833, -11.597, 0, 21.1, 13.307, 0, 21.467, -9.073, 0, 21.967, 6.966, 0, 22.233, -15.696, 0, 22.533, 14.507, 0, 22.9, -9.765, 0, 23.267, 6.205, 0, 23.667, -3.727, 0, 24.067, 2.297, 0, 24.467, -1.169, 0, 24.867, 3.865, 0, 25.3, -3.343, 0, 25.633, 1.526, 0, 26.333, -3.899, 0, 26.7, 3.865, 0, 27.1, -2.682, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param37", "Segments": [0, 0, 2, 0.033, 0, 2, 0.333, 0, 2, 0.667, 0, 2, 1, 0, 2, 1.7, 0, 2, 11.667, 0, 0, 11.8, -0.004, 0, 12.133, 0.922, 0, 12.667, -0.108, 0, 12.8, -0.086, 0, 13.367, -0.466, 0, 14.133, 0.198, 0, 14.533, -0.246, 0, 14.833, 1.988, 0, 15.567, -4.955, 0, 15.867, 5.613, 0, 16.2, -1.52, 0, 16.533, 0.843, 0, 16.867, 0.028, 0, 17, 0.17, 0, 17.467, -4.539, 0, 18.567, 2.209, 0, 18.7, 1.993, 0, 19, 5.228, 0, 19.333, -4.608, 0, 19.433, -3.346, 0, 19.467, -4.373, 0, 19.6, -0.147, 0, 19.633, -0.318, 0, 19.7, 2.997, 0, 19.8, 0.987, 0, 19.833, 1.237, 0, 20, -2.356, 0, 20.233, 5.289, 0, 20.5, -9.051, 0, 20.867, 13.218, 0, 21.2, -10.771, 0, 21.433, 4.534, 0, 21.733, -0.948, 0, 21.767, -0.9, 0, 22.033, -17.671, 0, 22.3, 29.906, 0, 22.533, -20.152, 0, 22.9, 4.005, 0, 23.267, -2.602, 0, 23.9, 1.768, 0, 24.167, -2.314, 0, 24.5, 2.315, 0, 24.8, -1.856, 0, 25.1, 1.42, 0, 25.433, -2.106, 0, 25.7, 0.004, 0, 26.1, -5.213, 0, 26.4, 5.516, 0, 26.767, -1.095, 0, 27.033, 0.43, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param39", "Segments": [0, 0, 0, 0.167, -0.004, 0, 0.467, 0.002, 0, 0.767, -0.001, 0, 1.067, 0, 2, 1.4, 0, 2, 1.733, 0, 2, 2.033, 0, 2, 2.7, 0, 2, 11.667, 0, 0, 11.8, 0.002, 0, 12, -0.362, 0, 12.3, 0.444, 0, 12.667, -0.176, 0, 13, 0.131, 0, 13.433, -0.071, 0, 13.733, -0.007, 0, 13.767, -0.009, 0, 13.8, -0.008, 0, 14.033, -0.109, 0, 14.3, 0.176, 0, 14.733, -0.918, 0, 15, 1.123, 0, 15.3, -0.268, 0, 15.5, 1.506, 0, 15.767, -4.758, 0, 16.033, 5.652, 0, 16.333, -3.546, 0, 16.667, 1.827, 0, 16.967, -0.861, 0, 17.233, 1.485, 0, 17.533, -1.177, 0, 17.833, 0.355, 0, 18.067, -0.872, 0, 18.367, 0.13, 0, 18.467, 0.013, 0, 18.7, 0.356, 0, 18.9, -1.497, 0, 19.2, 2.788, 0, 19.267, 2.398, 0, 19.333, 3.007, 0, 19.433, -2.187, 0, 19.467, -1.761, 0, 19.567, -4.704, 0, 19.633, -1.998, 0, 19.7, -2.109, 0, 19.8, 3.013, 0, 19.833, 2.894, 0, 19.9, 3.401, 0, 20.167, -5.848, 0, 20.4, 8.472, 0, 20.7, -9.805, 0, 21, 14.264, 0, 21.333, -13.834, 0, 21.6, 8.221, 0, 21.867, -2.386, 0, 22, 6.539, 0, 22.167, -23.886, 0, 22.467, 29.941, 0, 22.7, -24.255, 0, 23, 11.412, 0, 23.367, -4.994, 0, 23.7, 4.974, 0, 23.933, -4.242, 0, 24.1, 4.195, 0, 24.333, -5.274, 0, 24.633, 3.906, 0, 24.933, -5.294, 0, 25.233, 2.763, 0, 25.533, -4.495, 0, 26.033, 2.472, 0, 26.267, -5.556, 0, 26.533, 5.538, 0, 26.867, -3.208, 0, 27.167, 1.663, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param38", "Segments": [0, 0, 2, 0.033, 0, 2, 0.333, 0, 2, 0.667, 0, 2, 1, 0, 2, 1.7, 0, 2, 11.667, 0, 0, 11.8, -0.004, 0, 12.133, 0.922, 0, 12.667, -0.108, 0, 12.8, -0.086, 0, 13.367, -0.466, 0, 14.133, 0.198, 0, 14.533, -0.246, 0, 14.833, 1.988, 0, 15.567, -4.955, 0, 15.867, 5.613, 0, 16.2, -1.52, 0, 16.533, 0.843, 0, 16.867, 0.028, 0, 17, 0.17, 0, 17.467, -4.539, 0, 18.567, 2.209, 0, 18.7, 1.993, 0, 19, 5.228, 0, 19.333, -4.608, 0, 19.433, -3.346, 0, 19.467, -4.373, 0, 19.6, -0.147, 0, 19.633, -0.318, 0, 19.7, 2.997, 0, 19.8, 0.987, 0, 19.833, 1.237, 0, 20, -2.356, 0, 20.233, 5.289, 0, 20.5, -9.051, 0, 20.867, 13.218, 0, 21.2, -10.771, 0, 21.433, 4.534, 0, 21.733, -0.948, 0, 21.767, -0.9, 0, 22.033, -17.671, 0, 22.3, 29.906, 0, 22.533, -20.152, 0, 22.9, 4.005, 0, 23.267, -2.602, 0, 23.9, 1.768, 0, 24.167, -2.314, 0, 24.5, 2.315, 0, 24.8, -1.856, 0, 25.1, 1.42, 0, 25.433, -2.106, 0, 25.7, 0.004, 0, 26.1, -5.213, 0, 26.4, 5.516, 0, 26.767, -1.095, 0, 27.033, 0.43, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param40", "Segments": [0, 0, 0, 0.167, -0.004, 0, 0.467, 0.002, 0, 0.767, -0.001, 0, 1.067, 0, 2, 1.4, 0, 2, 1.733, 0, 2, 2.033, 0, 2, 2.7, 0, 2, 11.667, 0, 0, 11.8, 0.002, 0, 12, -0.362, 0, 12.3, 0.444, 0, 12.667, -0.176, 0, 13, 0.131, 0, 13.433, -0.071, 0, 13.733, -0.007, 0, 13.767, -0.009, 0, 13.8, -0.008, 0, 14.033, -0.109, 0, 14.3, 0.176, 0, 14.733, -0.918, 0, 15, 1.123, 0, 15.3, -0.268, 0, 15.5, 1.506, 0, 15.767, -4.758, 0, 16.033, 5.652, 0, 16.333, -3.546, 0, 16.667, 1.827, 0, 16.967, -0.861, 0, 17.233, 1.485, 0, 17.533, -1.177, 0, 17.833, 0.355, 0, 18.067, -0.872, 0, 18.367, 0.13, 0, 18.467, 0.013, 0, 18.7, 0.356, 0, 18.9, -1.497, 0, 19.2, 2.788, 0, 19.267, 2.398, 0, 19.333, 3.007, 0, 19.433, -2.187, 0, 19.467, -1.761, 0, 19.567, -4.704, 0, 19.633, -1.998, 0, 19.7, -2.109, 0, 19.8, 3.013, 0, 19.833, 2.894, 0, 19.9, 3.401, 0, 20.167, -5.848, 0, 20.4, 8.472, 0, 20.7, -9.805, 0, 21, 14.264, 0, 21.333, -13.834, 0, 21.6, 8.221, 0, 21.867, -2.386, 0, 22, 6.539, 0, 22.167, -23.886, 0, 22.467, 29.941, 0, 22.7, -24.255, 0, 23, 11.412, 0, 23.367, -4.994, 0, 23.7, 4.974, 0, 23.933, -4.242, 0, 24.1, 4.195, 0, 24.333, -5.274, 0, 24.633, 3.906, 0, 24.933, -5.294, 0, 25.233, 2.763, 0, 25.533, -4.495, 0, 26.033, 2.472, 0, 26.267, -5.556, 0, 26.533, 5.538, 0, 26.867, -3.208, 0, 27.167, 1.663, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param41", "Segments": [0, 0, 2, 0.033, 0, 2, 0.333, 0, 2, 0.667, 0, 2, 1, 0, 2, 1.7, 0, 2, 11.667, 0, 0, 11.8, -0.004, 0, 12.133, 0.922, 0, 12.667, -0.108, 0, 12.8, -0.086, 0, 13.367, -0.466, 0, 14.133, 0.198, 0, 14.533, -0.246, 0, 14.833, 1.988, 0, 15.567, -4.955, 0, 15.867, 5.613, 0, 16.2, -1.52, 0, 16.533, 0.843, 0, 16.867, 0.028, 0, 17, 0.17, 0, 17.467, -4.539, 0, 18.567, 2.209, 0, 18.7, 1.993, 0, 19, 5.228, 0, 19.333, -4.608, 0, 19.433, -3.346, 0, 19.467, -4.373, 0, 19.6, -0.147, 0, 19.633, -0.318, 0, 19.7, 2.997, 0, 19.8, 0.987, 0, 19.833, 1.237, 0, 20, -2.356, 0, 20.233, 5.289, 0, 20.5, -9.051, 0, 20.867, 13.218, 0, 21.2, -10.771, 0, 21.433, 4.534, 0, 21.733, -0.948, 0, 21.767, -0.9, 0, 22.033, -17.671, 0, 22.3, 29.906, 0, 22.533, -20.152, 0, 22.9, 4.005, 0, 23.267, -2.602, 0, 23.9, 1.768, 0, 24.167, -2.314, 0, 24.5, 2.315, 0, 24.8, -1.856, 0, 25.1, 1.42, 0, 25.433, -2.106, 0, 25.7, 0.004, 0, 26.1, -5.213, 0, 26.4, 5.516, 0, 26.767, -1.095, 0, 27.033, 0.43, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param42", "Segments": [0, 0, 0, 0.167, -0.004, 0, 0.467, 0.002, 0, 0.767, -0.001, 0, 1.067, 0, 2, 1.4, 0, 2, 1.733, 0, 2, 2.033, 0, 2, 2.7, 0, 2, 11.667, 0, 0, 11.8, 0.002, 0, 12, -0.362, 0, 12.3, 0.444, 0, 12.667, -0.176, 0, 13, 0.131, 0, 13.433, -0.071, 0, 13.733, -0.007, 0, 13.767, -0.009, 0, 13.8, -0.008, 0, 14.033, -0.109, 0, 14.3, 0.176, 0, 14.733, -0.918, 0, 15, 1.123, 0, 15.3, -0.268, 0, 15.5, 1.506, 0, 15.767, -4.758, 0, 16.033, 5.652, 0, 16.333, -3.546, 0, 16.667, 1.827, 0, 16.967, -0.861, 0, 17.233, 1.485, 0, 17.533, -1.177, 0, 17.833, 0.355, 0, 18.067, -0.872, 0, 18.367, 0.13, 0, 18.467, 0.013, 0, 18.7, 0.356, 0, 18.9, -1.497, 0, 19.2, 2.788, 0, 19.267, 2.398, 0, 19.333, 3.007, 0, 19.433, -2.187, 0, 19.467, -1.761, 0, 19.567, -4.704, 0, 19.633, -1.998, 0, 19.7, -2.109, 0, 19.8, 3.013, 0, 19.833, 2.894, 0, 19.9, 3.401, 0, 20.167, -5.848, 0, 20.4, 8.472, 0, 20.7, -9.805, 0, 21, 14.264, 0, 21.333, -13.834, 0, 21.6, 8.221, 0, 21.867, -2.386, 0, 22, 6.539, 0, 22.167, -23.886, 0, 22.467, 29.941, 0, 22.7, -24.255, 0, 23, 11.412, 0, 23.367, -4.994, 0, 23.7, 4.974, 0, 23.933, -4.242, 0, 24.1, 4.195, 0, 24.333, -5.274, 0, 24.633, 3.906, 0, 24.933, -5.294, 0, 25.233, 2.763, 0, 25.533, -4.495, 0, 26.033, 2.472, 0, 26.267, -5.556, 0, 26.533, 5.538, 0, 26.867, -3.208, 0, 27.167, 1.663, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param43", "Segments": [0, 0, 2, 0.033, 0, 2, 0.333, 0, 2, 0.667, 0, 2, 1, 0, 2, 1.7, 0, 2, 11.667, 0, 0, 11.8, -0.004, 0, 12.133, 0.922, 0, 12.667, -0.108, 0, 12.8, -0.086, 0, 13.367, -0.466, 0, 14.133, 0.198, 0, 14.533, -0.246, 0, 14.833, 1.988, 0, 15.567, -4.955, 0, 15.867, 5.613, 0, 16.2, -1.52, 0, 16.533, 0.843, 0, 16.867, 0.028, 0, 17, 0.17, 0, 17.467, -4.539, 0, 18.567, 2.209, 0, 18.7, 1.993, 0, 19, 5.228, 0, 19.333, -4.608, 0, 19.433, -3.346, 0, 19.467, -4.373, 0, 19.6, -0.147, 0, 19.633, -0.318, 0, 19.7, 2.997, 0, 19.8, 0.987, 0, 19.833, 1.237, 0, 20, -2.356, 0, 20.233, 5.289, 0, 20.5, -9.051, 0, 20.867, 13.218, 0, 21.2, -10.771, 0, 21.433, 4.534, 0, 21.733, -0.948, 0, 21.767, -0.9, 0, 22.033, -17.671, 0, 22.3, 29.906, 0, 22.533, -20.152, 0, 22.9, 4.005, 0, 23.267, -2.602, 0, 23.9, 1.768, 0, 24.167, -2.314, 0, 24.5, 2.315, 0, 24.8, -1.856, 0, 25.1, 1.42, 0, 25.433, -2.106, 0, 25.7, 0.004, 0, 26.1, -5.213, 0, 26.4, 5.516, 0, 26.767, -1.095, 0, 27.033, 0.43, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param44", "Segments": [0, 0, 0, 0.167, -0.004, 0, 0.467, 0.002, 0, 0.767, -0.001, 0, 1.067, 0, 2, 1.4, 0, 2, 1.733, 0, 2, 2.033, 0, 2, 2.7, 0, 2, 11.667, 0, 0, 11.8, 0.002, 0, 12, -0.362, 0, 12.3, 0.444, 0, 12.667, -0.176, 0, 13, 0.131, 0, 13.433, -0.071, 0, 13.733, -0.007, 0, 13.767, -0.009, 0, 13.8, -0.008, 0, 14.033, -0.109, 0, 14.3, 0.176, 0, 14.733, -0.918, 0, 15, 1.123, 0, 15.3, -0.268, 0, 15.5, 1.506, 0, 15.767, -4.758, 0, 16.033, 5.652, 0, 16.333, -3.546, 0, 16.667, 1.827, 0, 16.967, -0.861, 0, 17.233, 1.485, 0, 17.533, -1.177, 0, 17.833, 0.355, 0, 18.067, -0.872, 0, 18.367, 0.13, 0, 18.467, 0.013, 0, 18.7, 0.356, 0, 18.9, -1.497, 0, 19.2, 2.788, 0, 19.267, 2.398, 0, 19.333, 3.007, 0, 19.433, -2.187, 0, 19.467, -1.761, 0, 19.567, -4.704, 0, 19.633, -1.998, 0, 19.7, -2.109, 0, 19.8, 3.013, 0, 19.833, 2.894, 0, 19.9, 3.401, 0, 20.167, -5.848, 0, 20.4, 8.472, 0, 20.7, -9.805, 0, 21, 14.264, 0, 21.333, -13.834, 0, 21.6, 8.221, 0, 21.867, -2.386, 0, 22, 6.539, 0, 22.167, -23.886, 0, 22.467, 29.941, 0, 22.7, -24.255, 0, 23, 11.412, 0, 23.367, -4.994, 0, 23.7, 4.974, 0, 23.933, -4.242, 0, 24.1, 4.195, 0, 24.333, -5.274, 0, 24.633, 3.906, 0, 24.933, -5.294, 0, 25.233, 2.763, 0, 25.533, -4.495, 0, 26.033, 2.472, 0, 26.267, -5.556, 0, 26.533, 5.538, 0, 26.867, -3.208, 0, 27.167, 1.663, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param45", "Segments": [0, 0, 2, 0.033, 0, 2, 0.333, 0, 2, 0.667, 0, 2, 1, 0, 2, 1.7, 0, 2, 11.667, 0, 0, 11.8, -0.004, 0, 12.133, 0.922, 0, 12.667, -0.108, 0, 12.8, -0.086, 0, 13.367, -0.466, 0, 14.133, 0.198, 0, 14.533, -0.246, 0, 14.833, 1.988, 0, 15.567, -4.955, 0, 15.867, 5.613, 0, 16.2, -1.52, 0, 16.533, 0.843, 0, 16.867, 0.028, 0, 17, 0.17, 0, 17.467, -4.539, 0, 18.567, 2.209, 0, 18.7, 1.993, 0, 19, 5.228, 0, 19.333, -4.608, 0, 19.433, -3.346, 0, 19.467, -4.373, 0, 19.6, -0.147, 0, 19.633, -0.318, 0, 19.7, 2.997, 0, 19.8, 0.987, 0, 19.833, 1.237, 0, 20, -2.356, 0, 20.233, 5.289, 0, 20.5, -9.051, 0, 20.867, 13.218, 0, 21.2, -10.771, 0, 21.433, 4.534, 0, 21.733, -0.948, 0, 21.767, -0.9, 0, 22.033, -17.671, 0, 22.3, 29.906, 0, 22.533, -20.152, 0, 22.9, 4.005, 0, 23.267, -2.602, 0, 23.9, 1.768, 0, 24.167, -2.314, 0, 24.5, 2.315, 0, 24.8, -1.856, 0, 25.1, 1.42, 0, 25.433, -2.106, 0, 25.7, 0.004, 0, 26.1, -5.213, 0, 26.4, 5.516, 0, 26.767, -1.095, 0, 27.033, 0.43, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param46", "Segments": [0, 0, 0, 0.167, -0.004, 0, 0.467, 0.002, 0, 0.767, -0.001, 0, 1.067, 0, 2, 1.4, 0, 2, 1.733, 0, 2, 2.033, 0, 2, 2.7, 0, 2, 11.667, 0, 0, 11.8, 0.002, 0, 12, -0.362, 0, 12.3, 0.444, 0, 12.667, -0.176, 0, 13, 0.131, 0, 13.433, -0.071, 0, 13.733, -0.007, 0, 13.767, -0.009, 0, 13.8, -0.008, 0, 14.033, -0.109, 0, 14.3, 0.176, 0, 14.733, -0.918, 0, 15, 1.123, 0, 15.3, -0.268, 0, 15.5, 1.506, 0, 15.767, -4.758, 0, 16.033, 5.652, 0, 16.333, -3.546, 0, 16.667, 1.827, 0, 16.967, -0.861, 0, 17.233, 1.485, 0, 17.533, -1.177, 0, 17.833, 0.355, 0, 18.067, -0.872, 0, 18.367, 0.13, 0, 18.467, 0.013, 0, 18.7, 0.356, 0, 18.9, -1.497, 0, 19.2, 2.788, 0, 19.267, 2.398, 0, 19.333, 3.007, 0, 19.433, -2.187, 0, 19.467, -1.761, 0, 19.567, -4.704, 0, 19.633, -1.998, 0, 19.7, -2.109, 0, 19.8, 3.013, 0, 19.833, 2.894, 0, 19.9, 3.401, 0, 20.167, -5.848, 0, 20.4, 8.472, 0, 20.7, -9.805, 0, 21, 14.264, 0, 21.333, -13.834, 0, 21.6, 8.221, 0, 21.867, -2.386, 0, 22, 6.539, 0, 22.167, -23.886, 0, 22.467, 29.941, 0, 22.7, -24.255, 0, 23, 11.412, 0, 23.367, -4.994, 0, 23.7, 4.974, 0, 23.933, -4.242, 0, 24.1, 4.195, 0, 24.333, -5.274, 0, 24.633, 3.906, 0, 24.933, -5.294, 0, 25.233, 2.763, 0, 25.533, -4.495, 0, 26.033, 2.472, 0, 26.267, -5.556, 0, 26.533, 5.538, 0, 26.867, -3.208, 0, 27.167, 1.663, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param62", "Segments": [0, 0, 2, 0.033, 0, 2, 0.333, 0, 2, 0.667, 0, 2, 1, 0, 2, 1.7, 0, 2, 11.667, 0, 0, 11.8, -0.004, 0, 12.133, 0.922, 0, 12.667, -0.108, 0, 12.8, -0.086, 0, 13.367, -0.466, 0, 14.133, 0.198, 0, 14.533, -0.246, 0, 14.833, 1.988, 0, 15.567, -4.955, 0, 15.867, 5.613, 0, 16.2, -1.52, 0, 16.533, 0.843, 0, 16.867, 0.028, 0, 17, 0.17, 0, 17.467, -4.539, 0, 18.567, 2.209, 0, 18.7, 1.993, 0, 19, 5.228, 0, 19.333, -4.608, 0, 19.433, -3.346, 0, 19.467, -4.373, 0, 19.6, -0.147, 0, 19.633, -0.318, 0, 19.7, 2.997, 0, 19.8, 0.987, 0, 19.833, 1.237, 0, 20, -2.356, 0, 20.233, 5.289, 0, 20.5, -9.051, 0, 20.867, 13.218, 0, 21.2, -10.771, 0, 21.433, 4.534, 0, 21.733, -0.948, 0, 21.767, -0.9, 0, 22.033, -17.671, 0, 22.3, 29.906, 0, 22.533, -20.152, 0, 22.9, 4.005, 0, 23.267, -2.602, 0, 23.9, 1.768, 0, 24.167, -2.314, 0, 24.5, 2.315, 0, 24.8, -1.856, 0, 25.1, 1.42, 0, 25.433, -2.106, 0, 25.7, 0.004, 0, 26.1, -5.213, 0, 26.4, 5.516, 0, 26.767, -1.095, 0, 27.033, 0.43, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param63", "Segments": [0, 0, 0, 0.167, -0.004, 0, 0.467, 0.002, 0, 0.767, -0.001, 0, 1.067, 0, 2, 1.4, 0, 2, 1.733, 0, 2, 2.033, 0, 2, 2.7, 0, 2, 11.667, 0, 0, 11.8, 0.002, 0, 12, -0.362, 0, 12.3, 0.444, 0, 12.667, -0.176, 0, 13, 0.131, 0, 13.433, -0.071, 0, 13.733, -0.007, 0, 13.767, -0.009, 0, 13.8, -0.008, 0, 14.033, -0.109, 0, 14.3, 0.176, 0, 14.733, -0.918, 0, 15, 1.123, 0, 15.3, -0.268, 0, 15.5, 1.506, 0, 15.767, -4.758, 0, 16.033, 5.652, 0, 16.333, -3.546, 0, 16.667, 1.827, 0, 16.967, -0.861, 0, 17.233, 1.485, 0, 17.533, -1.177, 0, 17.833, 0.355, 0, 18.067, -0.872, 0, 18.367, 0.13, 0, 18.467, 0.013, 0, 18.7, 0.356, 0, 18.9, -1.497, 0, 19.2, 2.788, 0, 19.267, 2.398, 0, 19.333, 3.007, 0, 19.433, -2.187, 0, 19.467, -1.761, 0, 19.567, -4.704, 0, 19.633, -1.998, 0, 19.7, -2.109, 0, 19.8, 3.013, 0, 19.833, 2.894, 0, 19.9, 3.401, 0, 20.167, -5.848, 0, 20.4, 8.472, 0, 20.7, -9.805, 0, 21, 14.264, 0, 21.333, -13.834, 0, 21.6, 8.221, 0, 21.867, -2.386, 0, 22, 6.539, 0, 22.167, -23.886, 0, 22.467, 29.941, 0, 22.7, -24.255, 0, 23, 11.412, 0, 23.367, -4.994, 0, 23.7, 4.974, 0, 23.933, -4.242, 0, 24.1, 4.195, 0, 24.333, -5.274, 0, 24.633, 3.906, 0, 24.933, -5.294, 0, 25.233, 2.763, 0, 25.533, -4.495, 0, 26.033, 2.472, 0, 26.267, -5.556, 0, 26.533, 5.538, 0, 26.867, -3.208, 0, 27.167, 1.663, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param64", "Segments": [0, 0, 2, 0.033, 0, 2, 0.333, 0, 2, 0.667, 0, 2, 1, 0, 2, 1.7, 0, 2, 11.667, 0, 0, 11.8, -0.004, 0, 12.133, 0.922, 0, 12.667, -0.108, 0, 12.8, -0.086, 0, 13.367, -0.466, 0, 14.133, 0.198, 0, 14.533, -0.246, 0, 14.833, 1.988, 0, 15.567, -4.955, 0, 15.867, 5.613, 0, 16.2, -1.52, 0, 16.533, 0.843, 0, 16.867, 0.028, 0, 17, 0.17, 0, 17.467, -4.539, 0, 18.567, 2.209, 0, 18.7, 1.993, 0, 19, 5.228, 0, 19.333, -4.608, 0, 19.433, -3.346, 0, 19.467, -4.373, 0, 19.6, -0.147, 0, 19.633, -0.318, 0, 19.7, 2.997, 0, 19.8, 0.987, 0, 19.833, 1.237, 0, 20, -2.356, 0, 20.233, 5.289, 0, 20.5, -9.051, 0, 20.867, 13.218, 0, 21.2, -10.771, 0, 21.433, 4.534, 0, 21.733, -0.948, 0, 21.767, -0.9, 0, 22.033, -17.671, 0, 22.3, 29.906, 0, 22.533, -20.152, 0, 22.9, 4.005, 0, 23.267, -2.602, 0, 23.9, 1.768, 0, 24.167, -2.314, 0, 24.5, 2.315, 0, 24.8, -1.856, 0, 25.1, 1.42, 0, 25.433, -2.106, 0, 25.7, 0.004, 0, 26.1, -5.213, 0, 26.4, 5.516, 0, 26.767, -1.095, 0, 27.033, 0.43, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param65", "Segments": [0, 0, 0, 0.167, -0.004, 0, 0.467, 0.002, 0, 0.767, -0.001, 0, 1.067, 0, 2, 1.4, 0, 2, 1.733, 0, 2, 2.033, 0, 2, 2.7, 0, 2, 11.667, 0, 0, 11.8, 0.002, 0, 12, -0.362, 0, 12.3, 0.444, 0, 12.667, -0.176, 0, 13, 0.131, 0, 13.433, -0.071, 0, 13.733, -0.007, 0, 13.767, -0.009, 0, 13.8, -0.008, 0, 14.033, -0.109, 0, 14.3, 0.176, 0, 14.733, -0.918, 0, 15, 1.123, 0, 15.3, -0.268, 0, 15.5, 1.506, 0, 15.767, -4.758, 0, 16.033, 5.652, 0, 16.333, -3.546, 0, 16.667, 1.827, 0, 16.967, -0.861, 0, 17.233, 1.485, 0, 17.533, -1.177, 0, 17.833, 0.355, 0, 18.067, -0.872, 0, 18.367, 0.13, 0, 18.467, 0.013, 0, 18.7, 0.356, 0, 18.9, -1.497, 0, 19.2, 2.788, 0, 19.267, 2.398, 0, 19.333, 3.007, 0, 19.433, -2.187, 0, 19.467, -1.761, 0, 19.567, -4.704, 0, 19.633, -1.998, 0, 19.7, -2.109, 0, 19.8, 3.013, 0, 19.833, 2.894, 0, 19.9, 3.401, 0, 20.167, -5.848, 0, 20.4, 8.472, 0, 20.7, -9.805, 0, 21, 14.264, 0, 21.333, -13.834, 0, 21.6, 8.221, 0, 21.867, -2.386, 0, 22, 6.539, 0, 22.167, -23.886, 0, 22.467, 29.941, 0, 22.7, -24.255, 0, 23, 11.412, 0, 23.367, -4.994, 0, 23.7, 4.974, 0, 23.933, -4.242, 0, 24.1, 4.195, 0, 24.333, -5.274, 0, 24.633, 3.906, 0, 24.933, -5.294, 0, 25.233, 2.763, 0, 25.533, -4.495, 0, 26.033, 2.472, 0, 26.267, -5.556, 0, 26.533, 5.538, 0, 26.867, -3.208, 0, 27.167, 1.663, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param72", "Segments": [0, 0, 2, 0.033, 0, 2, 0.333, 0, 2, 0.667, 0, 2, 1, 0, 2, 1.7, 0, 2, 11.667, 0, 0, 11.8, -0.004, 0, 12.133, 0.922, 0, 12.667, -0.108, 0, 12.8, -0.086, 0, 13.367, -0.466, 0, 14.133, 0.198, 0, 14.533, -0.246, 0, 14.833, 1.988, 0, 15.567, -4.955, 0, 15.867, 5.613, 0, 16.2, -1.52, 0, 16.533, 0.843, 0, 16.867, 0.028, 0, 17, 0.17, 0, 17.467, -4.539, 0, 18.567, 2.209, 0, 18.7, 1.993, 0, 19, 5.228, 0, 19.333, -4.608, 0, 19.433, -3.346, 0, 19.467, -4.373, 0, 19.6, -0.147, 0, 19.633, -0.318, 0, 19.7, 2.997, 0, 19.8, 0.987, 0, 19.833, 1.237, 0, 20, -2.356, 0, 20.233, 5.289, 0, 20.5, -9.051, 0, 20.867, 13.218, 0, 21.2, -10.771, 0, 21.433, 4.534, 0, 21.733, -0.948, 0, 21.767, -0.9, 0, 22.033, -17.671, 0, 22.3, 29.906, 0, 22.533, -20.152, 0, 22.9, 4.005, 0, 23.267, -2.602, 0, 23.9, 1.768, 0, 24.167, -2.314, 0, 24.5, 2.315, 0, 24.8, -1.856, 0, 25.1, 1.42, 0, 25.433, -2.106, 0, 25.7, 0.004, 0, 26.1, -5.213, 0, 26.4, 5.516, 0, 26.767, -1.095, 0, 27.033, 0.43, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param73", "Segments": [0, 0, 0, 0.167, -0.004, 0, 0.467, 0.002, 0, 0.767, -0.001, 0, 1.067, 0, 2, 1.4, 0, 2, 1.733, 0, 2, 2.033, 0, 2, 2.7, 0, 2, 11.667, 0, 0, 11.8, 0.002, 0, 12, -0.362, 0, 12.3, 0.444, 0, 12.667, -0.176, 0, 13, 0.131, 0, 13.433, -0.071, 0, 13.733, -0.007, 0, 13.767, -0.009, 0, 13.8, -0.008, 0, 14.033, -0.109, 0, 14.3, 0.176, 0, 14.733, -0.918, 0, 15, 1.123, 0, 15.3, -0.268, 0, 15.5, 1.506, 0, 15.767, -4.758, 0, 16.033, 5.652, 0, 16.333, -3.546, 0, 16.667, 1.827, 0, 16.967, -0.861, 0, 17.233, 1.485, 0, 17.533, -1.177, 0, 17.833, 0.355, 0, 18.067, -0.872, 0, 18.367, 0.13, 0, 18.467, 0.013, 0, 18.7, 0.356, 0, 18.9, -1.497, 0, 19.2, 2.788, 0, 19.267, 2.398, 0, 19.333, 3.007, 0, 19.433, -2.187, 0, 19.467, -1.761, 0, 19.567, -4.704, 0, 19.633, -1.998, 0, 19.7, -2.109, 0, 19.8, 3.013, 0, 19.833, 2.894, 0, 19.9, 3.401, 0, 20.167, -5.848, 0, 20.4, 8.472, 0, 20.7, -9.805, 0, 21, 14.264, 0, 21.333, -13.834, 0, 21.6, 8.221, 0, 21.867, -2.386, 0, 22, 6.539, 0, 22.167, -23.886, 0, 22.467, 29.941, 0, 22.7, -24.255, 0, 23, 11.412, 0, 23.367, -4.994, 0, 23.7, 4.974, 0, 23.933, -4.242, 0, 24.1, 4.195, 0, 24.333, -5.274, 0, 24.633, 3.906, 0, 24.933, -5.294, 0, 25.233, 2.763, 0, 25.533, -4.495, 0, 26.033, 2.472, 0, 26.267, -5.556, 0, 26.533, 5.538, 0, 26.867, -3.208, 0, 27.167, 1.663, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param74", "Segments": [0, 0, 2, 0.033, 0, 2, 0.333, 0, 2, 0.667, 0, 2, 1, 0, 2, 1.7, 0, 2, 11.667, 0, 0, 11.8, -0.004, 0, 12.133, 0.922, 0, 12.667, -0.108, 0, 12.8, -0.086, 0, 13.367, -0.466, 0, 14.133, 0.198, 0, 14.533, -0.246, 0, 14.833, 1.988, 0, 15.567, -4.955, 0, 15.867, 5.613, 0, 16.2, -1.52, 0, 16.533, 0.843, 0, 16.867, 0.028, 0, 17, 0.17, 0, 17.467, -4.539, 0, 18.567, 2.209, 0, 18.7, 1.993, 0, 19, 5.228, 0, 19.333, -4.608, 0, 19.433, -3.346, 0, 19.467, -4.373, 0, 19.6, -0.147, 0, 19.633, -0.318, 0, 19.7, 2.997, 0, 19.8, 0.987, 0, 19.833, 1.237, 0, 20, -2.356, 0, 20.233, 5.289, 0, 20.5, -9.051, 0, 20.867, 13.218, 0, 21.2, -10.771, 0, 21.433, 4.534, 0, 21.733, -0.948, 0, 21.767, -0.9, 0, 22.033, -17.671, 0, 22.3, 29.906, 0, 22.533, -20.152, 0, 22.9, 4.005, 0, 23.267, -2.602, 0, 23.9, 1.768, 0, 24.167, -2.314, 0, 24.5, 2.315, 0, 24.8, -1.856, 0, 25.1, 1.42, 0, 25.433, -2.106, 0, 25.7, 0.004, 0, 26.1, -5.213, 0, 26.4, 5.516, 0, 26.767, -1.095, 0, 27.033, 0.43, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param75", "Segments": [0, 0, 0, 0.167, -0.004, 0, 0.467, 0.002, 0, 0.767, -0.001, 0, 1.067, 0, 2, 1.4, 0, 2, 1.733, 0, 2, 2.033, 0, 2, 2.7, 0, 2, 11.667, 0, 0, 11.8, 0.002, 0, 12, -0.362, 0, 12.3, 0.444, 0, 12.667, -0.176, 0, 13, 0.131, 0, 13.433, -0.071, 0, 13.733, -0.007, 0, 13.767, -0.009, 0, 13.8, -0.008, 0, 14.033, -0.109, 0, 14.3, 0.176, 0, 14.733, -0.918, 0, 15, 1.123, 0, 15.3, -0.268, 0, 15.5, 1.506, 0, 15.767, -4.758, 0, 16.033, 5.652, 0, 16.333, -3.546, 0, 16.667, 1.827, 0, 16.967, -0.861, 0, 17.233, 1.485, 0, 17.533, -1.177, 0, 17.833, 0.355, 0, 18.067, -0.872, 0, 18.367, 0.13, 0, 18.467, 0.013, 0, 18.7, 0.356, 0, 18.9, -1.497, 0, 19.2, 2.788, 0, 19.267, 2.398, 0, 19.333, 3.007, 0, 19.433, -2.187, 0, 19.467, -1.761, 0, 19.567, -4.704, 0, 19.633, -1.998, 0, 19.7, -2.109, 0, 19.8, 3.013, 0, 19.833, 2.894, 0, 19.9, 3.401, 0, 20.167, -5.848, 0, 20.4, 8.472, 0, 20.7, -9.805, 0, 21, 14.264, 0, 21.333, -13.834, 0, 21.6, 8.221, 0, 21.867, -2.386, 0, 22, 6.539, 0, 22.167, -23.886, 0, 22.467, 29.941, 0, 22.7, -24.255, 0, 23, 11.412, 0, 23.367, -4.994, 0, 23.7, 4.974, 0, 23.933, -4.242, 0, 24.1, 4.195, 0, 24.333, -5.274, 0, 24.633, 3.906, 0, 24.933, -5.294, 0, 25.233, 2.763, 0, 25.533, -4.495, 0, 26.033, 2.472, 0, 26.267, -5.556, 0, 26.533, 5.538, 0, 26.867, -3.208, 0, 27.167, 1.663, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param53", "Segments": [0, 0, 2, 0.033, 0, 2, 0.333, 0, 2, 0.667, 0, 2, 1, 0, 2, 1.7, 0, 2, 11.667, 0, 0, 11.8, -0.004, 0, 12.133, 0.922, 0, 12.667, -0.108, 0, 12.8, -0.086, 0, 13.367, -0.466, 0, 14.133, 0.198, 0, 14.533, -0.246, 0, 14.833, 1.988, 0, 15.567, -4.955, 0, 15.867, 5.613, 0, 16.2, -1.52, 0, 16.533, 0.843, 0, 16.867, 0.028, 0, 17, 0.17, 0, 17.467, -4.539, 0, 18.567, 2.209, 0, 18.7, 1.993, 0, 19, 5.228, 0, 19.333, -4.608, 0, 19.433, -3.346, 0, 19.467, -4.373, 0, 19.6, -0.147, 0, 19.633, -0.318, 0, 19.7, 2.997, 0, 19.8, 0.987, 0, 19.833, 1.237, 0, 20, -2.356, 0, 20.233, 5.289, 0, 20.5, -9.051, 0, 20.867, 13.218, 0, 21.2, -10.771, 0, 21.433, 4.534, 0, 21.733, -0.948, 0, 21.767, -0.9, 0, 22.033, -17.671, 0, 22.3, 29.906, 0, 22.533, -20.152, 0, 22.9, 4.005, 0, 23.267, -2.602, 0, 23.9, 1.768, 0, 24.167, -2.314, 0, 24.5, 2.315, 0, 24.8, -1.856, 0, 25.1, 1.42, 0, 25.433, -2.106, 0, 25.7, 0.004, 0, 26.1, -5.213, 0, 26.4, 5.516, 0, 26.767, -1.095, 0, 27.033, 0.43, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param54", "Segments": [0, 0, 0, 0.167, -0.004, 0, 0.467, 0.002, 0, 0.767, -0.001, 0, 1.067, 0, 2, 1.4, 0, 2, 1.733, 0, 2, 2.033, 0, 2, 2.7, 0, 2, 11.667, 0, 0, 11.8, 0.002, 0, 12, -0.362, 0, 12.3, 0.444, 0, 12.667, -0.176, 0, 13, 0.131, 0, 13.433, -0.071, 0, 13.733, -0.007, 0, 13.767, -0.009, 0, 13.8, -0.008, 0, 14.033, -0.109, 0, 14.3, 0.176, 0, 14.733, -0.918, 0, 15, 1.123, 0, 15.3, -0.268, 0, 15.5, 1.506, 0, 15.767, -4.758, 0, 16.033, 5.652, 0, 16.333, -3.546, 0, 16.667, 1.827, 0, 16.967, -0.861, 0, 17.233, 1.485, 0, 17.533, -1.177, 0, 17.833, 0.355, 0, 18.067, -0.872, 0, 18.367, 0.13, 0, 18.467, 0.013, 0, 18.7, 0.356, 0, 18.9, -1.497, 0, 19.2, 2.788, 0, 19.267, 2.398, 0, 19.333, 3.007, 0, 19.433, -2.187, 0, 19.467, -1.761, 0, 19.567, -4.704, 0, 19.633, -1.998, 0, 19.7, -2.109, 0, 19.8, 3.013, 0, 19.833, 2.894, 0, 19.9, 3.401, 0, 20.167, -5.848, 0, 20.4, 8.472, 0, 20.7, -9.805, 0, 21, 14.264, 0, 21.333, -13.834, 0, 21.6, 8.221, 0, 21.867, -2.386, 0, 22, 6.539, 0, 22.167, -23.886, 0, 22.467, 29.941, 0, 22.7, -24.255, 0, 23, 11.412, 0, 23.367, -4.994, 0, 23.7, 4.974, 0, 23.933, -4.242, 0, 24.1, 4.195, 0, 24.333, -5.274, 0, 24.633, 3.906, 0, 24.933, -5.294, 0, 25.233, 2.763, 0, 25.533, -4.495, 0, 26.033, 2.472, 0, 26.267, -5.556, 0, 26.533, 5.538, 0, 26.867, -3.208, 0, 27.167, 1.663, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param81", "Segments": [0, 0, 2, 0.033, 0, 2, 0.333, 0, 2, 0.667, 0, 2, 1, 0, 2, 1.7, 0, 2, 11.667, 0, 0, 11.8, -0.004, 0, 12.133, 0.922, 0, 12.667, -0.108, 0, 12.8, -0.086, 0, 13.367, -0.466, 0, 14.133, 0.198, 0, 14.533, -0.246, 0, 14.833, 1.988, 0, 15.567, -4.955, 0, 15.867, 5.613, 0, 16.2, -1.52, 0, 16.533, 0.843, 0, 16.867, 0.028, 0, 17, 0.17, 0, 17.467, -4.539, 0, 18.567, 2.209, 0, 18.7, 1.993, 0, 19, 5.228, 0, 19.333, -4.608, 0, 19.433, -3.346, 0, 19.467, -4.373, 0, 19.6, -0.147, 0, 19.633, -0.318, 0, 19.7, 2.997, 0, 19.8, 0.987, 0, 19.833, 1.237, 0, 20, -2.356, 0, 20.233, 5.289, 0, 20.5, -9.051, 0, 20.867, 13.218, 0, 21.2, -10.771, 0, 21.433, 4.534, 0, 21.733, -0.948, 0, 21.767, -0.9, 0, 22.033, -17.671, 0, 22.3, 29.906, 0, 22.533, -20.152, 0, 22.9, 4.005, 0, 23.267, -2.602, 0, 23.9, 1.768, 0, 24.167, -2.314, 0, 24.5, 2.315, 0, 24.8, -1.856, 0, 25.1, 1.42, 0, 25.433, -2.106, 0, 25.7, 0.004, 0, 26.1, -5.213, 0, 26.4, 5.516, 0, 26.767, -1.095, 0, 27.033, 0.43, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param82", "Segments": [0, 0, 0, 0.167, -0.004, 0, 0.467, 0.002, 0, 0.767, -0.001, 0, 1.067, 0, 2, 1.4, 0, 2, 1.733, 0, 2, 2.033, 0, 2, 2.7, 0, 2, 11.667, 0, 0, 11.8, 0.002, 0, 12, -0.362, 0, 12.3, 0.444, 0, 12.667, -0.176, 0, 13, 0.131, 0, 13.433, -0.071, 0, 13.733, -0.007, 0, 13.767, -0.009, 0, 13.8, -0.008, 0, 14.033, -0.109, 0, 14.3, 0.176, 0, 14.733, -0.918, 0, 15, 1.123, 0, 15.3, -0.268, 0, 15.5, 1.506, 0, 15.767, -4.758, 0, 16.033, 5.652, 0, 16.333, -3.546, 0, 16.667, 1.827, 0, 16.967, -0.861, 0, 17.233, 1.485, 0, 17.533, -1.177, 0, 17.833, 0.355, 0, 18.067, -0.872, 0, 18.367, 0.13, 0, 18.467, 0.013, 0, 18.7, 0.356, 0, 18.9, -1.497, 0, 19.2, 2.788, 0, 19.267, 2.398, 0, 19.333, 3.007, 0, 19.433, -2.187, 0, 19.467, -1.761, 0, 19.567, -4.704, 0, 19.633, -1.998, 0, 19.7, -2.109, 0, 19.8, 3.013, 0, 19.833, 2.894, 0, 19.9, 3.401, 0, 20.167, -5.848, 0, 20.4, 8.472, 0, 20.7, -9.805, 0, 21, 14.264, 0, 21.333, -13.834, 0, 21.6, 8.221, 0, 21.867, -2.386, 0, 22, 6.539, 0, 22.167, -23.886, 0, 22.467, 29.941, 0, 22.7, -24.255, 0, 23, 11.412, 0, 23.367, -4.994, 0, 23.7, 4.974, 0, 23.933, -4.242, 0, 24.1, 4.195, 0, 24.333, -5.274, 0, 24.633, 3.906, 0, 24.933, -5.294, 0, 25.233, 2.763, 0, 25.533, -4.495, 0, 26.033, 2.472, 0, 26.267, -5.556, 0, 26.533, 5.538, 0, 26.867, -3.208, 0, 27.167, 1.663, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param70", "Segments": [0, 0, 2, 0.033, 0, 2, 0.333, 0, 2, 0.667, 0, 2, 1, 0, 2, 1.7, 0, 2, 11.667, 0, 0, 11.8, -0.004, 0, 12.133, 0.922, 0, 12.667, -0.108, 0, 12.8, -0.086, 0, 13.367, -0.466, 0, 14.133, 0.198, 0, 14.533, -0.246, 0, 14.833, 1.988, 0, 15.567, -4.955, 0, 15.867, 5.613, 0, 16.2, -1.52, 0, 16.533, 0.843, 0, 16.867, 0.028, 0, 17, 0.17, 0, 17.467, -4.539, 0, 18.567, 2.209, 0, 18.7, 1.993, 0, 19, 5.228, 0, 19.333, -4.608, 0, 19.433, -3.346, 0, 19.467, -4.373, 0, 19.6, -0.147, 0, 19.633, -0.318, 0, 19.7, 2.997, 0, 19.8, 0.987, 0, 19.833, 1.237, 0, 20, -2.356, 0, 20.233, 5.289, 0, 20.5, -9.051, 0, 20.867, 13.218, 0, 21.2, -10.771, 0, 21.433, 4.534, 0, 21.733, -0.948, 0, 21.767, -0.9, 0, 22.033, -17.671, 0, 22.3, 29.906, 0, 22.533, -20.152, 0, 22.9, 4.005, 0, 23.267, -2.602, 0, 23.9, 1.768, 0, 24.167, -2.314, 0, 24.5, 2.315, 0, 24.8, -1.856, 0, 25.1, 1.42, 0, 25.433, -2.106, 0, 25.7, 0.004, 0, 26.1, -5.213, 0, 26.4, 5.516, 0, 26.767, -1.095, 0, 27.033, 0.43, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param71", "Segments": [0, 0, 0, 0.167, -0.004, 0, 0.467, 0.002, 0, 0.767, -0.001, 0, 1.067, 0, 2, 1.4, 0, 2, 1.733, 0, 2, 2.033, 0, 2, 2.7, 0, 2, 11.667, 0, 0, 11.8, 0.002, 0, 12, -0.362, 0, 12.3, 0.444, 0, 12.667, -0.176, 0, 13, 0.131, 0, 13.433, -0.071, 0, 13.733, -0.007, 0, 13.767, -0.009, 0, 13.8, -0.008, 0, 14.033, -0.109, 0, 14.3, 0.176, 0, 14.733, -0.918, 0, 15, 1.123, 0, 15.3, -0.268, 0, 15.5, 1.506, 0, 15.767, -4.758, 0, 16.033, 5.652, 0, 16.333, -3.546, 0, 16.667, 1.827, 0, 16.967, -0.861, 0, 17.233, 1.485, 0, 17.533, -1.177, 0, 17.833, 0.355, 0, 18.067, -0.872, 0, 18.367, 0.13, 0, 18.467, 0.013, 0, 18.7, 0.356, 0, 18.9, -1.497, 0, 19.2, 2.788, 0, 19.267, 2.398, 0, 19.333, 3.007, 0, 19.433, -2.187, 0, 19.467, -1.761, 0, 19.567, -4.704, 0, 19.633, -1.998, 0, 19.7, -2.109, 0, 19.8, 3.013, 0, 19.833, 2.894, 0, 19.9, 3.401, 0, 20.167, -5.848, 0, 20.4, 8.472, 0, 20.7, -9.805, 0, 21, 14.264, 0, 21.333, -13.834, 0, 21.6, 8.221, 0, 21.867, -2.386, 0, 22, 6.539, 0, 22.167, -23.886, 0, 22.467, 29.941, 0, 22.7, -24.255, 0, 23, 11.412, 0, 23.367, -4.994, 0, 23.7, 4.974, 0, 23.933, -4.242, 0, 24.1, 4.195, 0, 24.333, -5.274, 0, 24.633, 3.906, 0, 24.933, -5.294, 0, 25.233, 2.763, 0, 25.533, -4.495, 0, 26.033, 2.472, 0, 26.267, -5.556, 0, 26.533, 5.538, 0, 26.867, -3.208, 0, 27.167, 1.663, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param77", "Segments": [0, 0, 2, 0.033, 0, 2, 0.333, 0, 2, 0.667, 0, 2, 1, 0, 2, 1.7, 0, 2, 11.667, 0, 0, 11.8, -0.004, 0, 12.133, 0.922, 0, 12.667, -0.108, 0, 12.8, -0.086, 0, 13.367, -0.466, 0, 14.133, 0.198, 0, 14.533, -0.246, 0, 14.833, 1.988, 0, 15.567, -4.955, 0, 15.867, 5.613, 0, 16.2, -1.52, 0, 16.533, 0.843, 0, 16.867, 0.028, 0, 17, 0.17, 0, 17.467, -4.539, 0, 18.567, 2.209, 0, 18.7, 1.993, 0, 19, 5.228, 0, 19.333, -4.608, 0, 19.433, -3.346, 0, 19.467, -4.373, 0, 19.6, -0.147, 0, 19.633, -0.318, 0, 19.7, 2.997, 0, 19.8, 0.987, 0, 19.833, 1.237, 0, 20, -2.356, 0, 20.233, 5.289, 0, 20.5, -9.051, 0, 20.867, 13.218, 0, 21.2, -10.771, 0, 21.433, 4.534, 0, 21.733, -0.948, 0, 21.767, -0.9, 0, 22.033, -17.671, 0, 22.3, 29.906, 0, 22.533, -20.152, 0, 22.9, 4.005, 0, 23.267, -2.602, 0, 23.9, 1.768, 0, 24.167, -2.314, 0, 24.5, 2.315, 0, 24.8, -1.856, 0, 25.1, 1.42, 0, 25.433, -2.106, 0, 25.7, 0.004, 0, 26.1, -5.213, 0, 26.4, 5.516, 0, 26.767, -1.095, 0, 27.033, 0.43, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param78", "Segments": [0, 0, 0, 0.167, -0.004, 0, 0.467, 0.002, 0, 0.767, -0.001, 0, 1.067, 0, 2, 1.4, 0, 2, 1.733, 0, 2, 2.033, 0, 2, 2.7, 0, 2, 11.667, 0, 0, 11.8, 0.002, 0, 12, -0.362, 0, 12.3, 0.444, 0, 12.667, -0.176, 0, 13, 0.131, 0, 13.433, -0.071, 0, 13.733, -0.007, 0, 13.767, -0.009, 0, 13.8, -0.008, 0, 14.033, -0.109, 0, 14.3, 0.176, 0, 14.733, -0.918, 0, 15, 1.123, 0, 15.3, -0.268, 0, 15.5, 1.506, 0, 15.767, -4.758, 0, 16.033, 5.652, 0, 16.333, -3.546, 0, 16.667, 1.827, 0, 16.967, -0.861, 0, 17.233, 1.485, 0, 17.533, -1.177, 0, 17.833, 0.355, 0, 18.067, -0.872, 0, 18.367, 0.13, 0, 18.467, 0.013, 0, 18.7, 0.356, 0, 18.9, -1.497, 0, 19.2, 2.788, 0, 19.267, 2.398, 0, 19.333, 3.007, 0, 19.433, -2.187, 0, 19.467, -1.761, 0, 19.567, -4.704, 0, 19.633, -1.998, 0, 19.7, -2.109, 0, 19.8, 3.013, 0, 19.833, 2.894, 0, 19.9, 3.401, 0, 20.167, -5.848, 0, 20.4, 8.472, 0, 20.7, -9.805, 0, 21, 14.264, 0, 21.333, -13.834, 0, 21.6, 8.221, 0, 21.867, -2.386, 0, 22, 6.539, 0, 22.167, -23.886, 0, 22.467, 29.941, 0, 22.7, -24.255, 0, 23, 11.412, 0, 23.367, -4.994, 0, 23.7, 4.974, 0, 23.933, -4.242, 0, 24.1, 4.195, 0, 24.333, -5.274, 0, 24.633, 3.906, 0, 24.933, -5.294, 0, 25.233, 2.763, 0, 25.533, -4.495, 0, 26.033, 2.472, 0, 26.267, -5.556, 0, 26.533, 5.538, 0, 26.867, -3.208, 0, 27.167, 1.663, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param79", "Segments": [0, 0, 2, 0.033, 0, 2, 0.333, 0, 2, 0.667, 0, 2, 1, 0, 2, 1.7, 0, 2, 11.667, 0, 0, 11.8, -0.004, 0, 12.133, 0.922, 0, 12.667, -0.108, 0, 12.8, -0.086, 0, 13.367, -0.466, 0, 14.133, 0.198, 0, 14.533, -0.246, 0, 14.833, 1.988, 0, 15.567, -4.955, 0, 15.867, 5.613, 0, 16.2, -1.52, 0, 16.533, 0.843, 0, 16.867, 0.028, 0, 17, 0.17, 0, 17.467, -4.539, 0, 18.567, 2.209, 0, 18.7, 1.993, 0, 19, 5.228, 0, 19.333, -4.608, 0, 19.433, -3.346, 0, 19.467, -4.373, 0, 19.6, -0.147, 0, 19.633, -0.318, 0, 19.7, 2.997, 0, 19.8, 0.987, 0, 19.833, 1.237, 0, 20, -2.356, 0, 20.233, 5.289, 0, 20.5, -9.051, 0, 20.867, 13.218, 0, 21.2, -10.771, 0, 21.433, 4.534, 0, 21.733, -0.948, 0, 21.767, -0.9, 0, 22.033, -17.671, 0, 22.3, 29.906, 0, 22.533, -20.152, 0, 22.9, 4.005, 0, 23.267, -2.602, 0, 23.9, 1.768, 0, 24.167, -2.314, 0, 24.5, 2.315, 0, 24.8, -1.856, 0, 25.1, 1.42, 0, 25.433, -2.106, 0, 25.7, 0.004, 0, 26.1, -5.213, 0, 26.4, 5.516, 0, 26.767, -1.095, 0, 27.033, 0.43, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param80", "Segments": [0, 0, 0, 0.167, -0.004, 0, 0.467, 0.002, 0, 0.767, -0.001, 0, 1.067, 0, 2, 1.4, 0, 2, 1.733, 0, 2, 2.033, 0, 2, 2.7, 0, 2, 11.667, 0, 0, 11.8, 0.002, 0, 12, -0.362, 0, 12.3, 0.444, 0, 12.667, -0.176, 0, 13, 0.131, 0, 13.433, -0.071, 0, 13.733, -0.007, 0, 13.767, -0.009, 0, 13.8, -0.008, 0, 14.033, -0.109, 0, 14.3, 0.176, 0, 14.733, -0.918, 0, 15, 1.123, 0, 15.3, -0.268, 0, 15.5, 1.506, 0, 15.767, -4.758, 0, 16.033, 5.652, 0, 16.333, -3.546, 0, 16.667, 1.827, 0, 16.967, -0.861, 0, 17.233, 1.485, 0, 17.533, -1.177, 0, 17.833, 0.355, 0, 18.067, -0.872, 0, 18.367, 0.13, 0, 18.467, 0.013, 0, 18.7, 0.356, 0, 18.9, -1.497, 0, 19.2, 2.788, 0, 19.267, 2.398, 0, 19.333, 3.007, 0, 19.433, -2.187, 0, 19.467, -1.761, 0, 19.567, -4.704, 0, 19.633, -1.998, 0, 19.7, -2.109, 0, 19.8, 3.013, 0, 19.833, 2.894, 0, 19.9, 3.401, 0, 20.167, -5.848, 0, 20.4, 8.472, 0, 20.7, -9.805, 0, 21, 14.264, 0, 21.333, -13.834, 0, 21.6, 8.221, 0, 21.867, -2.386, 0, 22, 6.539, 0, 22.167, -23.886, 0, 22.467, 29.941, 0, 22.7, -24.255, 0, 23, 11.412, 0, 23.367, -4.994, 0, 23.7, 4.974, 0, 23.933, -4.242, 0, 24.1, 4.195, 0, 24.333, -5.274, 0, 24.633, 3.906, 0, 24.933, -5.294, 0, 25.233, 2.763, 0, 25.533, -4.495, 0, 26.033, 2.472, 0, 26.267, -5.556, 0, 26.533, 5.538, 0, 26.867, -3.208, 0, 27.167, 1.663, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param294", "Segments": [0, 0, 2, 27.2, 0, 0, 27.333, 1]}, {"Target": "Parameter", "Id": "Param47", "Segments": [0, 0, 2, 0.033, 0, 2, 0.3, 0, 2, 0.333, 0, 2, 0.633, 0, 2, 0.667, 0, 2, 0.9, 0, 2, 1, 0, 2, 1.133, 0, 2, 1.167, 0, 2, 1.3, 0, 2, 11.667, 0, 0, 11.8, -0.026, 0, 12, 0.512, 0, 12.333, -0.042, 0, 12.6, 0.096, 0, 14.567, -0.291, 0, 15.833, 2.819, 0, 16.267, -0.802, 0, 16.567, 0.682, 0, 17.467, -7.234, 0, 18.533, 1.949, 0, 18.767, 1.427, 0, 19.167, 3.505, 0, 19.2, 3.501, 0, 19.233, 3.555, 0, 19.333, -2.964, 0, 19.4, 1.312, 0, 19.467, -3.872, 0, 19.567, 1.889, 0, 19.633, -0.992, 0, 19.7, 6.045, 0, 19.767, 0.377, 0, 19.833, 2.63, 0, 20, -3.563, 0, 20.3, 1.338, 0, 20.6, -0.521, 0, 20.767, -0.161, 0, 20.9, -4.74, 0, 21.1, 13.196, 0, 21.233, -7.447, 0, 21.4, 2.123, 0, 21.5, -1.95, 0, 21.733, 0.549, 0, 22, -13.434, 0, 22.233, 29.473, 0, 22.533, -17.291, 0, 22.867, 3.581, 0, 23.267, -0.797, 0, 23.567, 6.045, 0, 23.867, -1.051, 0, 24.233, 4.546, 0, 24.533, -1.051, 0, 24.9, 5.331, 0, 25.333, -1.051, 0, 25.633, 4.546, 0, 26.1, -2.69, 0, 26.367, 3.293, 0, 26.7, 0.107, 0, 26.967, 0.826, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param48", "Segments": [0, 0, 0, 0.133, -0.003, 0, 0.467, 0.001, 0, 0.767, 0, 2, 1.067, 0, 2, 1.367, 0, 2, 1.4, 0, 2, 1.7, 0, 2, 1.733, 0, 2, 1.967, 0, 2, 11.667, 0, 0, 11.767, 0.017, 0, 11.933, -0.305, 0, 12.2, 0.371, 0, 12.467, -0.256, 0, 12.767, 0.143, 0, 13.067, -0.061, 0, 13.367, 0.022, 0, 13.767, 0.003, 0, 13.8, 0.004, 2, 13.9, 0.004, 0, 13.967, 0.006, 2, 14.033, 0.006, 0, 14.133, 0.009, 0, 14.267, 0.007, 0, 14.3, 0.01, 0, 14.433, 0.006, 0, 14.467, 0.01, 0, 14.733, -0.054, 0, 15.033, 0.024, 0, 15.333, -0.034, 0, 15.567, -0.011, 0, 15.733, -1.022, 0, 16.033, 1.455, 0, 16.4, -1.289, 0, 16.7, 0.96, 0, 17, -0.445, 0, 17.333, 1.615, 0, 17.633, -2.26, 0, 18, 0.757, 0, 18.367, -0.787, 0, 18.667, 0.787, 0, 18.967, -0.96, 0, 19.333, 5.178, 0, 19.4, -1.001, 0, 19.467, 2.575, 0, 19.567, -4.188, 0, 19.633, -0.36, 0, 19.7, -5.027, 0, 19.767, 2.929, 0, 19.833, 1.381, 0, 19.933, 3.962, 0, 20.167, -4.327, 0, 20.467, 2.738, 0, 20.767, -1.412, 0, 20.9, 2.559, 0, 21.067, -11.377, 0, 21.2, 14.187, 0, 21.4, -7.627, 0, 21.967, 6.349, 0, 22.167, -19.882, 0, 22.367, 30, 2, 22.433, 30, 0, 22.7, -20.164, 0, 22.967, 9.516, 0, 23.3, -3.741, 0, 23.667, 6.349, 0, 24, -4.188, 0, 24.333, 7.642, 0, 24.733, -5.027, 0, 25.2, 7.642, 0, 25.6, -7.627, 0, 25.933, 6.349, 0, 26.3, -3.112, 0, 26.533, 3.121, 0, 26.833, -1.838, 0, 27.133, 1.063, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param56", "Segments": [0, 0, 0, 0.267, -0.033, 0, 0.8, 0.016, 0, 1.3, -0.008, 0, 1.833, 0.004, 0, 2.333, -0.002, 0, 2.867, 0.001, 0, 3.367, 0, 2, 3.4, 0, 2, 3.9, 0, 2, 4.4, 0, 2, 4.433, 0, 2, 4.933, 0, 2, 5.433, 0, 2, 5.467, 0, 2, 5.9, 0, 2, 6, 0, 2, 6.033, 0, 2, 6.067, 0, 2, 6.433, 0, 2, 6.533, 0, 2, 6.9, 0, 2, 7, 0, 2, 7.033, 0, 2, 7.067, 0, 2, 7.133, 0, 2, 7.167, 0, 2, 7.2, 0, 2, 7.233, 0, 2, 7.267, 0, 2, 7.3, 0, 2, 7.333, 0, 2, 7.367, 0, 2, 7.4, 0, 2, 7.433, 0, 2, 7.467, 0, 2, 11.667, 0, 0, 11.8, -0.011, 0, 12.033, 0.229, 0, 12.7, -0.042, 0, 13.1, 0.007, 0, 14.567, -0.146, 0, 15.9, 1.497, 0, 16.4, -0.666, 0, 16.933, 0.193, 0, 17.533, -4.257, 0, 18.267, 1.301, 0, 18.767, 0.641, 0, 19.233, 1.952, 0, 19.333, -0.507, 0, 19.367, 0.865, 0, 19.467, -1.468, 0, 19.567, 0.042, 0, 19.633, -1.154, 0, 19.7, 1.449, 0, 19.767, -0.273, 0, 19.833, 0.852, 0, 20.033, -1.118, 0, 20.4, 0.132, 0, 20.6, 0.085, 0, 20.7, 0.091, 0, 20.933, -2.121, 0, 21.1, 4.785, 0, 21.233, -1.683, 0, 21.4, 0.795, 0, 21.5, -1.045, 0, 21.767, -0.278, 0, 22, -5.634, 0, 22.267, 13.312, 0, 22.667, -6.138, 0, 23.267, 3.105, 0, 23.8, -1.517, 0, 24.3, 2.188, 0, 24.8, -2.041, 0, 25.333, 1.378, 0, 26.1, -1.29, 0, 26.467, 1.5, 0, 27.267, -0.457, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param57", "Segments": [0, 0, 0, 0.433, -0.022, 0, 0.967, 0.011, 0, 1.467, -0.005, 0, 2, 0.003, 0, 2.5, -0.001, 0, 3.033, 0.001, 0, 3.533, 0, 2, 4.033, 0, 2, 4.1, 0, 2, 4.567, 0, 2, 4.6, 0, 2, 5.033, 0, 2, 5.067, 0, 2, 5.1, 0, 2, 5.133, 0, 2, 5.433, 0, 2, 5.467, 0, 2, 5.533, 0, 2, 5.567, 0, 2, 5.6, 0, 2, 5.633, 0, 2, 5.667, 0, 2, 5.7, 0, 2, 5.733, 0, 2, 5.767, 0, 2, 5.8, 0, 2, 5.833, 0, 2, 5.867, 0, 2, 6.033, 0, 2, 6.1, 0, 2, 6.3, 0, 2, 6.367, 0, 2, 6.667, 0, 2, 6.8, 0, 2, 6.833, 0, 2, 7.033, 0, 2, 7.133, 0, 2, 7.233, 0, 2, 7.267, 0, 2, 7.3, 0, 2, 7.367, 0, 2, 7.4, 0, 2, 7.433, 0, 2, 7.467, 0, 2, 11.667, 0, 0, 11.767, 0.008, 0, 11.967, -0.156, 0, 12.233, 0.217, 0, 12.567, -0.008, 0, 12.767, 0.015, 0, 13, -0.008, 0, 13.3, 0.023, 0, 13.933, -0.022, 2, 13.967, -0.022, 0, 14.1, -0.026, 0, 14.133, -0.025, 0, 14.8, -0.107, 0, 15.567, -0.025, 0, 15.733, -0.54, 0, 16.133, 1.27, 0, 16.533, -0.461, 0, 17.333, 0.868, 0, 17.733, -2.837, 0, 18.367, -0.027, 0, 18.4, -0.029, 0, 18.7, 0.358, 0, 18.967, -0.124, 0, 19.333, 3.275, 0, 19.4, 0.81, 0, 19.467, 2.485, 0, 19.567, -0.876, 0, 19.633, 0.29, 0, 19.7, -2.46, 0, 19.767, 0.624, 0, 19.833, -0.088, 0, 19.967, 1.889, 0, 20.233, -1.326, 0, 20.5, 0.541, 0, 20.767, -0.171, 0, 20.9, 1.466, 0, 21.067, -5.985, 0, 21.233, 5.796, 0, 21.4, -1.764, 0, 21.5, 0.062, 0, 21.7, -0.469, 0, 21.967, 3.197, 0, 22.167, -11.214, 0, 22.433, 17.076, 0, 22.767, -6.926, 0, 23.5, 1.81, 0, 23.933, -1.206, 0, 24.433, 1.953, 0, 24.967, -1.92, 0, 25.5, 1.324, 0, 25.867, -1.558, 0, 26.033, 0.613, 0, 26.3, -1.924, 0, 26.633, 1.276, 0, 26.933, 0.267, 0, 27.067, 0.334, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param58", "Segments": [0, 0, 0, 0.367, -0.034, 0, 0.933, 0.014, 0, 1.433, -0.007, 0, 1.967, 0.004, 0, 2.467, -0.002, 0, 3, 0.001, 0, 3.5, 0, 2, 4.033, 0, 2, 4.533, 0, 2, 5, 0, 2, 5.133, 0, 2, 5.167, 0, 2, 5.5, 0, 2, 5.533, 0, 2, 5.6, 0, 2, 5.8, 0, 2, 5.833, 0, 2, 5.933, 0, 2, 5.967, 0, 2, 6, 0, 2, 6.033, 0, 2, 6.1, 0, 2, 6.367, 0, 2, 6.6, 0, 2, 6.8, 0, 2, 6.9, 0, 2, 6.933, 0, 2, 6.967, 0, 2, 7, 0, 2, 7.2, 0, 2, 7.433, 0, 2, 7.467, 0, 2, 7.5, 0, 2, 7.533, 0, 2, 7.567, 0, 2, 7.6, 0, 2, 7.733, 0, 2, 7.767, 0, 2, 7.867, 0, 2, 11.667, 0, 0, 11.833, 0.007, 0, 12.067, -0.174, 0, 12.367, 0.263, 0, 12.667, -0.162, 0, 12.967, 0.056, 0, 13.2, -0.014, 0, 13.467, 0.027, 0, 13.8, -0.011, 0, 14.2, 0.013, 0, 14.9, -0.051, 0, 15.233, 0.015, 0, 15.867, -0.769, 0, 16.2, 1.381, 0, 16.567, -1.169, 0, 16.933, 0.542, 0, 17.1, 0.243, 0, 17.433, 1.38, 0, 17.8, -2.324, 0, 18.233, 0.94, 0, 18.567, -0.266, 0, 18.8, 0.18, 0, 19.1, -0.717, 0, 19.467, 1.837, 0, 19.767, -2.476, 0, 20.067, 2.75, 0, 20.333, -2.449, 0, 20.633, 1.58, 0, 20.867, -0.057, 0, 20.967, 0.715, 0, 21.167, -4.823, 0, 21.367, 5.16, 0, 21.667, -2.417, 0, 22.033, 4.361, 0, 22.3, -12.815, 0, 22.567, 18.877, 0, 22.867, -13.797, 0, 23.2, 4.934, 0, 23.5, 0.005, 0, 23.7, 0.765, 0, 24.033, -1.628, 0, 24.4, 2.455, 0, 24.867, -1.997, 0, 25.5, 1.36, 0, 25.9, -1.587, 0, 26.133, 0.785, 0, 26.433, -2.272, 0, 26.733, 2.234, 0, 27.033, -0.934, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param59", "Segments": [0, 0, 0, 0.267, -0.033, 0, 0.8, 0.016, 0, 1.3, -0.008, 0, 1.833, 0.004, 0, 2.333, -0.002, 0, 2.867, 0.001, 0, 3.367, 0, 2, 3.4, 0, 2, 3.9, 0, 2, 4.4, 0, 2, 4.433, 0, 2, 4.933, 0, 2, 5.433, 0, 2, 5.467, 0, 2, 5.9, 0, 2, 6, 0, 2, 6.033, 0, 2, 6.067, 0, 2, 6.433, 0, 2, 6.533, 0, 2, 6.9, 0, 2, 7, 0, 2, 7.033, 0, 2, 7.067, 0, 2, 7.133, 0, 2, 7.167, 0, 2, 7.2, 0, 2, 7.233, 0, 2, 7.267, 0, 2, 7.3, 0, 2, 7.333, 0, 2, 7.367, 0, 2, 7.4, 0, 2, 7.433, 0, 2, 7.467, 0, 2, 11.667, 0, 0, 11.8, -0.011, 0, 12.033, 0.229, 0, 12.7, -0.042, 0, 13.1, 0.007, 0, 14.567, -0.146, 0, 15.9, 1.497, 0, 16.4, -0.666, 0, 16.933, 0.193, 0, 17.533, -4.257, 0, 18.267, 1.301, 0, 18.767, 0.641, 0, 19.233, 1.952, 0, 19.333, -0.507, 0, 19.367, 0.865, 0, 19.467, -1.468, 0, 19.567, 0.042, 0, 19.633, -1.154, 0, 19.7, 1.449, 0, 19.767, -0.273, 0, 19.833, 0.852, 0, 20.033, -1.118, 0, 20.4, 0.132, 0, 20.6, 0.085, 0, 20.7, 0.091, 0, 20.933, -2.121, 0, 21.1, 4.785, 0, 21.233, -1.683, 0, 21.4, 0.795, 0, 21.5, -1.045, 0, 21.767, -0.278, 0, 22, -5.634, 0, 22.267, 13.312, 0, 22.667, -6.138, 0, 23.267, 3.105, 0, 23.8, -1.517, 0, 24.3, 2.188, 0, 24.8, -2.041, 0, 25.333, 1.378, 0, 26.1, -1.29, 0, 26.467, 1.5, 0, 27.267, -0.457, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param60", "Segments": [0, 0, 0, 0.433, -0.022, 0, 0.967, 0.011, 0, 1.467, -0.005, 0, 2, 0.003, 0, 2.5, -0.001, 0, 3.033, 0.001, 0, 3.533, 0, 2, 4.033, 0, 2, 4.1, 0, 2, 4.567, 0, 2, 4.6, 0, 2, 5.033, 0, 2, 5.067, 0, 2, 5.1, 0, 2, 5.133, 0, 2, 5.433, 0, 2, 5.467, 0, 2, 5.533, 0, 2, 5.567, 0, 2, 5.6, 0, 2, 5.633, 0, 2, 5.667, 0, 2, 5.7, 0, 2, 5.733, 0, 2, 5.767, 0, 2, 5.8, 0, 2, 5.833, 0, 2, 5.867, 0, 2, 6.033, 0, 2, 6.1, 0, 2, 6.3, 0, 2, 6.367, 0, 2, 6.667, 0, 2, 6.8, 0, 2, 6.833, 0, 2, 7.033, 0, 2, 7.133, 0, 2, 7.233, 0, 2, 7.267, 0, 2, 7.3, 0, 2, 7.367, 0, 2, 7.4, 0, 2, 7.433, 0, 2, 7.467, 0, 2, 11.667, 0, 0, 11.767, 0.008, 0, 11.967, -0.156, 0, 12.233, 0.217, 0, 12.567, -0.008, 0, 12.767, 0.015, 0, 13, -0.008, 0, 13.3, 0.023, 0, 13.933, -0.022, 2, 13.967, -0.022, 0, 14.1, -0.026, 0, 14.133, -0.025, 0, 14.8, -0.107, 0, 15.567, -0.025, 0, 15.733, -0.54, 0, 16.133, 1.27, 0, 16.533, -0.461, 0, 17.333, 0.868, 0, 17.733, -2.837, 0, 18.367, -0.027, 0, 18.4, -0.029, 0, 18.7, 0.358, 0, 18.967, -0.124, 0, 19.333, 3.275, 0, 19.4, 0.81, 0, 19.467, 2.485, 0, 19.567, -0.876, 0, 19.633, 0.29, 0, 19.7, -2.46, 0, 19.767, 0.624, 0, 19.833, -0.088, 0, 19.967, 1.889, 0, 20.233, -1.326, 0, 20.5, 0.541, 0, 20.767, -0.171, 0, 20.9, 1.466, 0, 21.067, -5.985, 0, 21.233, 5.796, 0, 21.4, -1.764, 0, 21.5, 0.062, 0, 21.7, -0.469, 0, 21.967, 3.197, 0, 22.167, -11.214, 0, 22.433, 17.076, 0, 22.767, -6.926, 0, 23.5, 1.81, 0, 23.933, -1.206, 0, 24.433, 1.953, 0, 24.967, -1.92, 0, 25.5, 1.324, 0, 25.867, -1.558, 0, 26.033, 0.613, 0, 26.3, -1.924, 0, 26.633, 1.276, 0, 26.933, 0.267, 0, 27.067, 0.334, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param61", "Segments": [0, 0, 0, 0.367, -0.034, 0, 0.933, 0.014, 0, 1.433, -0.007, 0, 1.967, 0.004, 0, 2.467, -0.002, 0, 3, 0.001, 0, 3.5, 0, 2, 4.033, 0, 2, 4.533, 0, 2, 5, 0, 2, 5.133, 0, 2, 5.167, 0, 2, 5.5, 0, 2, 5.533, 0, 2, 5.6, 0, 2, 5.8, 0, 2, 5.833, 0, 2, 5.933, 0, 2, 5.967, 0, 2, 6, 0, 2, 6.033, 0, 2, 6.1, 0, 2, 6.367, 0, 2, 6.6, 0, 2, 6.8, 0, 2, 6.9, 0, 2, 6.933, 0, 2, 6.967, 0, 2, 7, 0, 2, 7.2, 0, 2, 7.433, 0, 2, 7.467, 0, 2, 7.5, 0, 2, 7.533, 0, 2, 7.567, 0, 2, 7.6, 0, 2, 7.733, 0, 2, 7.767, 0, 2, 7.867, 0, 2, 11.667, 0, 0, 11.833, 0.007, 0, 12.067, -0.174, 0, 12.367, 0.263, 0, 12.667, -0.162, 0, 12.967, 0.056, 0, 13.2, -0.014, 0, 13.467, 0.027, 0, 13.8, -0.011, 0, 14.2, 0.013, 0, 14.9, -0.051, 0, 15.233, 0.015, 0, 15.867, -0.769, 0, 16.2, 1.381, 0, 16.567, -1.169, 0, 16.933, 0.542, 0, 17.1, 0.243, 0, 17.433, 1.38, 0, 17.8, -2.324, 0, 18.233, 0.94, 0, 18.567, -0.266, 0, 18.8, 0.18, 0, 19.1, -0.717, 0, 19.467, 1.837, 0, 19.767, -2.476, 0, 20.067, 2.75, 0, 20.333, -2.449, 0, 20.633, 1.58, 0, 20.867, -0.057, 0, 20.967, 0.715, 0, 21.167, -4.823, 0, 21.367, 5.16, 0, 21.667, -2.417, 0, 22.033, 4.361, 0, 22.3, -12.815, 0, 22.567, 18.877, 0, 22.867, -13.797, 0, 23.2, 4.934, 0, 23.5, 0.005, 0, 23.7, 0.765, 0, 24.033, -1.628, 0, 24.4, 2.455, 0, 24.867, -1.997, 0, 25.5, 1.36, 0, 25.9, -1.587, 0, 26.133, 0.785, 0, 26.433, -2.272, 0, 26.733, 2.234, 0, 27.033, -0.934, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param49", "Segments": [0, 0, 2, 0.033, 0, 2, 0.3, 0, 2, 0.333, 0, 2, 0.633, 0, 2, 0.667, 0, 2, 0.9, 0, 2, 1, 0, 2, 1.133, 0, 2, 1.167, 0, 2, 1.3, 0, 2, 11.667, 0, 0, 11.8, -0.026, 0, 12, 0.512, 0, 12.333, -0.042, 0, 12.6, 0.096, 0, 14.567, -0.291, 0, 15.833, 2.819, 0, 16.267, -0.802, 0, 16.567, 0.682, 0, 17.467, -7.234, 0, 18.533, 1.949, 0, 18.767, 1.427, 0, 19.167, 3.505, 0, 19.2, 3.501, 0, 19.233, 3.555, 0, 19.333, -2.964, 0, 19.4, 1.312, 0, 19.467, -3.872, 0, 19.567, 1.889, 0, 19.633, -0.992, 0, 19.7, 6.045, 0, 19.767, 0.377, 0, 19.833, 2.63, 0, 20, -3.563, 0, 20.3, 1.338, 0, 20.6, -0.521, 0, 20.767, -0.161, 0, 20.9, -4.74, 0, 21.1, 13.196, 0, 21.233, -7.447, 0, 21.4, 2.123, 0, 21.5, -1.95, 0, 21.733, 0.549, 0, 22, -13.434, 0, 22.233, 29.473, 0, 22.533, -17.291, 0, 22.867, 3.581, 0, 23.267, -0.797, 0, 23.567, 4.055, 0, 23.9, -3.19, 0, 24.2, 3.859, 0, 24.533, -4.204, 0, 24.833, 2.593, 0, 25.133, -4.439, 0, 25.433, 1.48, 0, 25.733, 0, 2, 25.767, 0, 2, 25.867, 0, 0, 26.1, -2.69, 0, 26.367, 3.293, 0, 26.7, 0.107, 0, 26.967, 0.826, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param50", "Segments": [0, 0, 0, 0.133, -0.003, 0, 0.467, 0.001, 0, 0.767, 0, 2, 1.067, 0, 2, 1.367, 0, 2, 1.4, 0, 2, 1.7, 0, 2, 1.733, 0, 2, 1.967, 0, 2, 11.667, 0, 0, 11.767, 0.017, 0, 11.933, -0.305, 0, 12.2, 0.371, 0, 12.467, -0.256, 0, 12.767, 0.143, 0, 13.067, -0.061, 0, 13.367, 0.022, 0, 13.767, 0.003, 0, 13.8, 0.004, 2, 13.9, 0.004, 0, 13.967, 0.006, 2, 14.033, 0.006, 0, 14.133, 0.009, 0, 14.267, 0.007, 0, 14.3, 0.01, 0, 14.433, 0.006, 0, 14.467, 0.01, 0, 14.733, -0.054, 0, 15.033, 0.024, 0, 15.333, -0.034, 0, 15.567, -0.011, 0, 15.733, -1.022, 0, 16.033, 1.455, 0, 16.4, -1.289, 0, 16.7, 0.96, 0, 17, -0.445, 0, 17.333, 1.615, 0, 17.633, -2.26, 0, 18, 0.757, 0, 18.367, -0.787, 0, 18.667, 0.787, 0, 18.967, -0.96, 0, 19.333, 5.178, 0, 19.4, -1.001, 0, 19.467, 2.575, 0, 19.567, -4.188, 0, 19.633, -0.36, 0, 19.7, -5.027, 0, 19.767, 2.929, 0, 19.833, 1.381, 0, 19.933, 3.962, 0, 20.167, -4.327, 0, 20.467, 2.738, 0, 20.767, -1.412, 0, 20.9, 2.559, 0, 21.067, -11.377, 0, 21.2, 14.187, 0, 21.4, -7.627, 0, 21.967, 6.349, 0, 22.167, -19.882, 0, 22.367, 30, 2, 22.433, 30, 0, 22.7, -20.164, 0, 22.967, 9.516, 0, 23.3, -3.741, 0, 23.633, 8.074, 0, 23.967, -6.614, 0, 24.267, 6.223, 0, 24.6, -7.607, 0, 24.9, 3.965, 0, 25.233, -7.883, 0, 25.533, 1.485, 0, 25.833, -0.002, 0, 26, 1.257, 0, 26.3, -3.112, 0, 26.533, 3.121, 0, 26.833, -1.838, 0, 27.133, 1.063, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param51", "Segments": [0, 0, 2, 0.033, 0, 2, 0.3, 0, 2, 0.333, 0, 2, 0.633, 0, 2, 0.667, 0, 2, 0.9, 0, 2, 1, 0, 2, 1.133, 0, 2, 1.167, 0, 2, 1.3, 0, 2, 11.667, 0, 0, 11.8, -0.026, 0, 12, 0.512, 0, 12.333, -0.042, 0, 12.6, 0.096, 0, 14.567, -0.291, 0, 15.833, 2.819, 0, 16.267, -0.802, 0, 16.567, 0.682, 0, 17.467, -7.234, 0, 18.533, 1.949, 0, 18.767, 1.427, 0, 19.167, 3.505, 0, 19.2, 3.501, 0, 19.233, 3.555, 0, 19.333, -2.964, 0, 19.4, 1.312, 0, 19.467, -3.872, 0, 19.567, 1.889, 0, 19.633, -0.992, 0, 19.7, 6.045, 0, 19.767, 0.377, 0, 19.833, 2.63, 0, 20, -3.563, 0, 20.3, 1.338, 0, 20.6, -0.521, 0, 20.767, -0.161, 0, 20.9, -4.74, 0, 21.1, 13.196, 0, 21.233, -7.447, 0, 21.4, 2.123, 0, 21.5, -1.95, 0, 21.733, 0.549, 0, 22, -13.434, 0, 22.233, 29.473, 0, 22.533, -17.291, 0, 22.867, 3.581, 0, 23.267, -0.797, 0, 23.567, 4.055, 0, 23.9, -3.19, 0, 24.2, 3.859, 0, 24.533, -4.204, 0, 24.833, 2.593, 0, 25.133, -4.439, 0, 25.433, 1.48, 0, 25.733, 0, 2, 25.767, 0, 2, 25.867, 0, 0, 26.1, -2.69, 0, 26.367, 3.293, 0, 26.7, 0.107, 0, 26.967, 0.826, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param52", "Segments": [0, 0, 0, 0.133, -0.003, 0, 0.467, 0.001, 0, 0.767, 0, 2, 1.067, 0, 2, 1.367, 0, 2, 1.4, 0, 2, 1.7, 0, 2, 1.733, 0, 2, 1.967, 0, 2, 11.667, 0, 0, 11.767, 0.017, 0, 11.933, -0.305, 0, 12.2, 0.371, 0, 12.467, -0.256, 0, 12.767, 0.143, 0, 13.067, -0.061, 0, 13.367, 0.022, 0, 13.767, 0.003, 0, 13.8, 0.004, 2, 13.9, 0.004, 0, 13.967, 0.006, 2, 14.033, 0.006, 0, 14.133, 0.009, 0, 14.267, 0.007, 0, 14.3, 0.01, 0, 14.433, 0.006, 0, 14.467, 0.01, 0, 14.733, -0.054, 0, 15.033, 0.024, 0, 15.333, -0.034, 0, 15.567, -0.011, 0, 15.733, -1.022, 0, 16.033, 1.455, 0, 16.4, -1.289, 0, 16.7, 0.96, 0, 17, -0.445, 0, 17.333, 1.615, 0, 17.633, -2.26, 0, 18, 0.757, 0, 18.367, -0.787, 0, 18.667, 0.787, 0, 18.967, -0.96, 0, 19.333, 5.178, 0, 19.4, -1.001, 0, 19.467, 2.575, 0, 19.567, -4.188, 0, 19.633, -0.36, 0, 19.7, -5.027, 0, 19.767, 2.929, 0, 19.833, 1.381, 0, 19.933, 3.962, 0, 20.167, -4.327, 0, 20.467, 2.738, 0, 20.767, -1.412, 0, 20.9, 2.559, 0, 21.067, -11.377, 0, 21.2, 14.187, 0, 21.4, -7.627, 0, 21.967, 6.349, 0, 22.167, -19.882, 0, 22.367, 30, 2, 22.433, 30, 0, 22.7, -20.164, 0, 22.967, 9.516, 0, 23.3, -3.741, 0, 23.633, 8.074, 0, 23.967, -6.614, 0, 24.267, 6.223, 0, 24.6, -7.607, 0, 24.9, 3.965, 0, 25.233, -7.883, 0, 25.533, 1.485, 0, 25.833, -0.002, 0, 26, 1.257, 0, 26.3, -3.112, 0, 26.533, 3.121, 0, 26.833, -1.838, 0, 27.133, 1.063, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation63", "Segments": [0, 1.196, 2, 10.667, 1.196, 2, 11.633, 1.196, 2, 11.8, 1.196, 2, 14.5, 1.196, 2, 17.833, 1.196, 2, 18.933, 1.196, 2, 20, 1.196, 2, 21.067, 1.196, 2, 21.467, 1.196, 2, 21.733, 1.196, 2, 22.967, 1.196, 2, 25.833, 1.196, 0, 27.333, -0.675]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation64", "Segments": [0, 3.747, 0, 0.033, 0, 2, 0.3, 0, 2, 0.333, 0, 2, 0.633, 0, 2, 0.667, 0, 2, 0.9, 0, 2, 1, 0, 2, 1.133, 0, 2, 1.167, 0, 2, 1.3, 0, 2, 11.667, 0, 0, 11.8, -0.009, 0, 12, 0.171, 0, 12.333, -0.014, 0, 12.6, 0.032, 0, 14.567, -0.097, 0, 15.833, 0.94, 0, 16.267, -0.267, 0, 16.567, 0.227, 0, 17.467, -2.411, 0, 18.533, 0.65, 0, 18.767, 0.476, 0, 19.167, 1.168, 0, 19.2, 1.167, 0, 19.233, 1.185, 0, 19.333, -0.988, 0, 19.4, 0.437, 0, 19.467, -1.291, 0, 19.567, 0.63, 0, 19.633, -0.331, 0, 19.7, 2.015, 0, 19.767, 0.126, 0, 19.833, 0.877, 0, 20, -1.188, 0, 20.3, 0.446, 0, 20.6, -0.174, 0, 20.767, -0.054, 0, 20.9, -1.58, 0, 21.1, 4.399, 0, 21.233, -2.482, 0, 21.4, 0.708, 0, 21.5, -0.65, 0, 21.733, 0.183, 0, 22, -4.478, 0, 22.233, 9.824, 0, 22.533, -5.764, 0, 22.867, 1.194, 0, 23.267, -0.266, 0, 23.567, 0.119, 0, 23.9, -0.036, 0, 24.2, 0.013, 0, 24.533, -0.004, 0, 24.833, 0.002, 0, 25.133, -0.001, 0, 25.433, 0, 2, 25.733, 0, 2, 25.767, 0, 2, 25.867, 0, 0, 26.1, -0.897, 0, 26.367, 1.098, 0, 26.7, 0.036, 1, 26.789, 0.036, 26.878, -0.047, 26.967, 0.275, 1, 27.089, 0.718, 27.211, 2.951, 27.333, 2.951]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation65", "Segments": [0, 0, 0, 0.133, -0.002, 0, 0.467, 0.001, 0, 0.767, 0, 2, 1.067, 0, 2, 1.367, 0, 2, 1.4, 0, 2, 1.7, 0, 2, 1.733, 0, 2, 1.967, 0, 2, 11.667, 0, 0, 11.767, 0.008, 0, 11.933, -0.152, 0, 12.2, 0.186, 0, 12.467, -0.128, 0, 12.767, 0.071, 0, 13.067, -0.031, 0, 13.367, 0.011, 0, 13.767, 0.002, 2, 13.8, 0.002, 2, 13.9, 0.002, 0, 13.967, 0.003, 2, 14.033, 0.003, 0, 14.133, 0.005, 0, 14.267, 0.004, 0, 14.3, 0.005, 0, 14.433, 0.003, 0, 14.467, 0.005, 0, 14.733, -0.027, 0, 15.033, 0.012, 0, 15.333, -0.017, 0, 15.567, -0.005, 0, 15.733, -0.511, 0, 16.033, 0.728, 0, 16.4, -0.644, 0, 16.7, 0.48, 0, 17, -0.222, 0, 17.333, 0.808, 0, 17.633, -1.13, 0, 18, 0.379, 0, 18.367, -0.393, 0, 18.667, 0.394, 0, 18.967, -0.48, 0, 19.333, 2.589, 0, 19.4, -0.501, 0, 19.467, 1.287, 0, 19.567, -2.094, 0, 19.633, -0.18, 0, 19.7, -2.514, 0, 19.767, 1.464, 0, 19.833, 0.691, 0, 19.933, 1.981, 0, 20.167, -2.163, 0, 20.467, 1.369, 0, 20.767, -0.706, 0, 20.9, 1.28, 0, 21.067, -5.689, 0, 21.2, 7.093, 0, 21.4, -3.813, 0, 21.967, 3.175, 0, 22.167, -9.941, 0, 22.4, 17.799, 0, 22.7, -10.082, 0, 22.967, 4.758, 0, 23.3, -1.87, 0, 23.633, 0.782, 0, 23.967, -0.319, 0, 24.267, 0.127, 0, 24.6, -0.051, 0, 24.9, 0.02, 0, 25.233, -0.008, 0, 25.533, 0.003, 0, 25.833, -0.001, 0, 26, 0.628, 0, 26.3, -1.556, 0, 26.533, 1.56, 0, 26.833, -0.919, 1, 26.933, -0.919, 27.033, -0.835, 27.133, 0.531, 1, 27.2, 1.443, 27.266, 4.849, 27.333, 4.849]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation66", "Segments": [0, 0, 0, 0.267, -0.014, 0, 0.567, 0.006, 0, 0.9, -0.002, 0, 1.2, 0.001, 0, 1.5, 0, 2, 1.8, 0, 2, 2.1, 0, 2, 2.133, 0, 2, 2.433, 0, 2, 2.467, 0, 2, 2.733, 0, 2, 2.767, 0, 2, 2.8, 0, 2, 2.833, 0, 2, 2.967, 0, 2, 3, 0, 2, 3.1, 0, 2, 11.667, 0, 0, 11.833, 0.007, 0, 12.033, -0.166, 0, 12.3, 0.302, 0, 12.6, -0.279, 0, 12.9, 0.197, 0, 13.2, -0.112, 0, 13.5, 0.056, 0, 13.8, -0.016, 0, 14.167, 0.011, 0, 14.467, 0.003, 0, 14.567, 0.004, 0, 14.867, -0.041, 0, 15.133, 0.029, 0, 15.467, -0.037, 0, 15.567, -0.024, 0, 15.833, -0.664, 0, 16.133, 1.333, 0, 16.5, -1.35, 0, 16.8, 1.154, 0, 17.1, -0.609, 0, 17.4, 1.363, 0, 17.733, -2.182, 0, 18.067, 1.254, 0, 18.433, -0.979, 0, 18.767, 0.898, 0, 19.067, -1.003, 0, 19.4, 2.021, 0, 19.733, -3.017, 0, 20.033, 3.76, 0, 20.3, -4.144, 0, 20.6, 3.264, 0, 20.833, -1.539, 0, 21, -0.018, 0, 21.133, -5.069, 0, 21.333, 7.524, 0, 21.6, -4.845, 0, 22, 4.637, 0, 22.233, -12.768, 0, 22.5, 21.835, 0, 22.8, -18.197, 0, 23.133, 11.132, 0, 23.433, -6.139, 0, 23.767, 3.166, 0, 24.067, -1.57, 0, 24.367, 0.722, 0, 24.7, -0.327, 0, 25, 0.142, 0, 25.333, -0.061, 0, 25.633, 0.026, 0, 25.867, -0.006, 0, 26.1, 0.767, 0, 26.367, -2.342, 0, 26.667, 2.964, 0, 26.967, -2.284, 0, 27.267, 1.563, 0, 27.333, -11.659]}, {"Target": "Parameter", "Id": "Param55", "Segments": [0, 0, 0, 0.1, 0.205, 0, 0.4, -0.095, 0, 0.7, 0.043, 0, 1, -0.019, 0, 1.3, 0.008, 0, 1.633, -0.003, 0, 1.933, 0.002, 0, 2.233, -0.001, 0, 2.533, 0, 2, 2.567, 0, 2, 2.867, 0, 2, 3.2, 0, 2, 3.233, 0, 2, 3.5, 0, 2, 3.533, 0, 2, 3.567, 0, 2, 3.6, 0, 2, 3.733, 0, 2, 3.767, 0, 2, 3.867, 0, 2, 11.667, 0, 0, 11.867, 0.006, 0, 12.133, -0.244, 0, 12.4, 0.549, 0, 12.7, -0.632, 0, 13, 0.536, 0, 13.3, -0.362, 0, 13.633, 0.218, 0, 13.933, -0.101, 0, 14.233, 0.054, 0, 14.567, -0.011, 0, 14.667, -0.007, 0, 14.967, -0.066, 0, 15.267, 0.066, 0, 15.567, -0.088, 0, 15.633, -0.083, 0, 15.933, -1.018, 0, 16.233, 2.603, 0, 16.567, -3.136, 0, 16.9, 2.973, 0, 17.2, -1.843, 0, 17.5, 2.928, 0, 17.833, -4.714, 0, 18.167, 3.651, 0, 18.5, -2.838, 0, 18.833, 2.387, 0, 19.167, -2.474, 0, 19.5, 4.062, 0, 19.8, -5.914, 0, 20.133, 7.966, 0, 20.4, -9.043, 0, 20.7, 8.158, 0, 21, -4.555, 0, 21.1, -4.063, 0, 21.2, -4.871, 0, 21.433, 11.528, 0, 21.733, -11.075, 0, 22.067, 11.593, 0, 22.3, -22.967, 0, 22.567, 30, 2, 22.6, 30, 0, 22.867, -30, 2, 22.933, -30, 0, 23.233, 24.57, 0, 23.567, -16.367, 0, 23.867, 10.342, 0, 24.167, -5.954, 0, 24.5, 3.249, 0, 24.8, -1.663, 0, 25.133, 0.802, 0, 25.433, -0.383, 0, 25.733, 0.175, 0, 25.933, 0.003, 0, 26.2, 1.115, 0, 26.467, -4.055, 0, 26.767, 6.105, 1, 26.867, 6.105, 26.967, -0.727, 27.067, -5.709, 1, 27.156, -10.137, 27.244, -10.57, 27.333, -10.57]}, {"Target": "Parameter", "Id": "Param28", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 0, 12.4, -7.12, 0, 13.733, 7.36, 0, 15.067, -7.12, 0, 16.4, 7.36, 0, 17.733, -7.12, 0, 17.833, 0, 2, 18.933, 0, 2, 20, 0, 2, 21.067, 0, 2, 21.467, 0, 2, 21.733, 0, 2, 22.967, 0, 2, 25.833, 0, 0, 27.333, -7.12]}, {"Target": "Parameter", "Id": "Param29", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 1, 12, 0, 12.2, -0.038, 12.4, -1.278, 1, 12.578, -2.38, 12.755, -4.74, 12.933, -4.74, 0, 14.267, 5.1, 0, 15.6, -4.74, 0, 16.933, 5.1, 0, 17.733, -1.278, 0, 17.833, 0, 2, 18.933, 0, 2, 20, 0, 2, 21.067, 0, 2, 21.467, 0, 2, 21.733, 0, 2, 22.967, 0, 2, 25.833, 0, 0, 27.333, -1.278]}, {"Target": "Parameter", "Id": "Param30", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 0, 12.4, -5.909, 0, 13.567, 2.533, 0, 14.9, -6.287, 0, 16.233, 2.533, 0, 17.567, -6.287, 1, 17.622, -6.287, 17.678, -6.291, 17.733, -5.909, 1, 17.766, -5.679, 17.8, 0, 17.833, 0, 2, 18.933, 0, 2, 20, 0, 2, 21.067, 0, 2, 21.467, 0, 2, 21.733, 0, 2, 22.967, 0, 2, 25.833, 0, 0, 27.333, -5.909]}, {"Target": "Parameter", "Id": "Param31", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 0, 12.4, -0.551, 0, 12.967, 3.018, 0, 14.3, -6.18, 0, 15.633, 3.018, 0, 16.967, -6.18, 1, 17.222, -6.18, 17.478, -4.163, 17.733, -0.551, 1, 17.766, -0.08, 17.8, 0, 17.833, 0, 2, 18.933, 0, 2, 20, 0, 2, 21.067, 0, 2, 21.467, 0, 2, 21.733, 0, 2, 22.967, 0, 2, 25.833, 0, 0, 27.333, -0.551]}, {"Target": "Parameter", "Id": "Param32", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 0, 12.4, -2.714, 0, 13.633, 2.284, 0, 14.967, -2.796, 0, 16.3, 2.284, 0, 17.633, -2.796, 1, 17.666, -2.796, 17.7, -2.818, 17.733, -2.714, 1, 17.766, -2.61, 17.8, 0, 17.833, 0, 2, 18.933, 0, 2, 20, 0, 2, 21.067, 0, 2, 21.467, 0, 2, 21.733, 0, 2, 22.967, 0, 2, 25.833, 0, 0, 27.333, -2.714]}, {"Target": "Parameter", "Id": "Param33", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 0, 12.4, -2.392, 0, 13, 3.279, 0, 14.333, -10.047, 0, 15.667, 3.279, 0, 17, -10.047, 1, 17.244, -10.047, 17.489, -7.835, 17.733, -2.392, 1, 17.766, -1.65, 17.8, 0, 17.833, 0, 2, 18.933, 0, 2, 20, 0, 2, 21.067, 0, 2, 21.467, 0, 2, 21.733, 0, 2, 22.967, 0, 2, 25.833, 0, 0, 27.333, -2.392]}, {"Target": "Parameter", "Id": "Param34", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 0, 12.4, -21.182, 0, 13.667, 11.7, 0, 15, -21.42, 0, 16.333, 11.7, 0, 17.667, -21.42, 1, 17.689, -21.42, 17.711, -21.524, 17.733, -21.182, 1, 17.766, -20.669, 17.8, 0, 17.833, 0, 2, 18.933, 0, 2, 20, 0, 2, 21.067, 0, 2, 21.467, 0, 2, 21.733, 0, 2, 22.967, 0, 2, 25.833, 0, 0, 27.333, -21.182]}, {"Target": "Parameter", "Id": "Param83", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 0, 11.933, -9, 0, 14.033, 5, 1, 14.189, 5, 14.344, 5.05, 14.5, 4.873, 1, 15.611, 3.608, 16.722, 1.634, 17.833, 0.401, 1, 18.2, -0.006, 18.566, 0, 18.933, 0, 2, 20, 0, 2, 21.067, 0, 2, 21.467, 0, 2, 21.733, 0, 2, 22.967, 0, 2, 25.833, 0, 2, 27.333, 0]}, {"Target": "Parameter", "Id": "Param86", "Segments": [0, 1, 2, 10.667, 1, 2, 11.633, 1, 2, 11.8, 1, 0, 11.933, 0, 0, 14.033, 1, 2, 14.5, 1, 2, 17.833, 1, 2, 18.933, 1, 2, 20, 1, 2, 21.067, 1, 2, 21.467, 1, 2, 21.733, 1, 2, 22.967, 1, 2, 25.833, 1, 2, 27.333, 1]}, {"Target": "Parameter", "Id": "Param98", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 14.5, 0, 2, 17.833, 0, 2, 18.933, 0, 2, 20, 0, 2, 21.067, 0, 2, 21.467, 0, 2, 21.733, 0, 2, 22.967, 0, 2, 25.833, 0, 0, 27.333, -6]}, {"Target": "Parameter", "Id": "Param99", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 14.5, 0, 2, 17.833, 0, 2, 18.933, 0, 2, 20, 0, 2, 21.067, 0, 2, 21.467, 0, 2, 21.733, 0, 2, 22.967, 0, 2, 25.833, 0, 0, 27.333, -10.921]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation67", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 14.5, 0, 2, 17.833, 0, 2, 18.933, 0, 2, 20, 0, 2, 21.067, 0, 2, 21.467, 0, 2, 21.733, 0, 2, 22.967, 0, 2, 25.833, 0, 0, 27.333, -10.939]}, {"Target": "Parameter", "Id": "Param218mao2", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 0, 11.667, 1, 2, 11.8, 1, 2, 14.5, 1, 2, 17.833, 1, 2, 18.933, 1, 2, 20, 1, 2, 21.067, 1, 2, 21.467, 1, 2, 21.733, 1, 2, 22.967, 1, 2, 25.833, 1, 2, 27.333, 1]}, {"Target": "Parameter", "Id": "Param281", "Segments": [0, 0, 2, 11.833, 0, 0, 14.367, 1, 2, 14.4, 1, 0, 16.733, 0, 2, 27.333, 0]}, {"Target": "Parameter", "Id": "Param197mao2", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 0, 12.533, 21, 0, 13.633, -5, 1, 13.922, -5, 14.211, -4.975, 14.5, -4.642, 1, 15.611, -3.365, 16.722, -1.537, 17.833, -0.372, 1, 18.2, 0.012, 18.566, 0, 18.933, 0, 2, 20, 0, 2, 21.067, 0, 2, 21.467, 0, 2, 21.733, 0, 2, 22.967, 0, 2, 25.833, 0, 2, 27.333, 0]}, {"Target": "Parameter", "Id": "Param216mao2", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 0, 11.667, -10.5, 2, 11.8, -10.5, 2, 14.5, -10.5, 2, 17.833, -10.5, 2, 18.933, -10.5, 2, 20, -10.5, 2, 21.067, -10.5, 2, 21.467, -10.5, 2, 21.733, -10.5, 2, 22.967, -10.5, 2, 25.833, -10.5, 0, 27.333, -8.88]}, {"Target": "Parameter", "Id": "Param212mao2", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 11.867, 0, 0, 12.033, -16, 0, 12.2, 12.568, 0, 12.4, -16, 0, 12.6, 0, 2, 14.567, 0, 0, 14.733, -16, 0, 14.9, 12.568, 0, 15.1, -16, 0, 15.3, 0, 2, 17.233, 0, 2, 17.833, 0, 2, 18.933, 0, 2, 20, 0, 2, 21.067, 0, 2, 21.467, 0, 2, 21.733, 0, 2, 22.967, 0, 2, 25.833, 0, 2, 27.333, 0]}, {"Target": "Parameter", "Id": "Param214mao2", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 11.867, 0, 2, 13.067, 0, 0, 13.233, 16, 0, 13.4, 0, 0, 13.6, 16, 0, 13.8, 0, 2, 14.567, 0, 2, 15.767, 0, 0, 15.933, 16, 0, 16.1, 0, 0, 16.3, 16, 0, 16.5, 0, 2, 17.233, 0, 2, 17.833, 0, 2, 18.933, 0, 2, 20, 0, 2, 21.067, 0, 2, 21.467, 0, 2, 21.733, 0, 2, 22.967, 0, 2, 25.833, 0, 2, 27.333, 0]}, {"Target": "Parameter", "Id": "Param215mao2", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 11.867, 0, 2, 13.067, 0, 0, 13.133, -14, 0, 13.333, 15, 0, 13.5, -8, 0, 13.667, 9, 0, 13.933, 0, 2, 14.567, 0, 2, 15.767, 0, 0, 15.833, -14, 0, 16.033, 15, 0, 16.2, -8, 0, 16.367, 9, 0, 16.633, 0, 2, 17.233, 0, 2, 17.833, 0, 2, 18.933, 0, 2, 20, 0, 2, 21.067, 0, 2, 21.467, 0, 2, 21.733, 0, 2, 22.967, 0, 2, 25.833, 0, 2, 27.333, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation144mao2", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 14.5, 0, 2, 17.833, 0, 2, 18.933, 0, 2, 20, 0, 2, 21.067, 0, 2, 21.467, 0, 2, 21.733, 0, 2, 22.967, 0, 2, 25.833, 0, 0, 27.333, -10.82]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation145mao2", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 14.5, 0, 2, 17.833, 0, 2, 18.933, 0, 2, 20, 0, 2, 21.067, 0, 2, 21.467, 0, 2, 21.733, 0, 2, 22.967, 0, 2, 25.833, 0, 0, 27.333, 8.46]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation146mao2", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 14.5, 0, 2, 17.833, 0, 2, 18.933, 0, 2, 20, 0, 2, 21.067, 0, 2, 21.467, 0, 2, 21.733, 0, 2, 22.967, 0, 2, 25.833, 0, 0, 27.333, 28.83]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation72", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 14.5, 0, 2, 17.833, 0, 2, 18.933, 0, 2, 20, 0, 2, 21.067, 0, 2, 21.467, 0, 2, 21.733, 0, 2, 22.967, 0, 2, 25.833, 0, 0, 27.333, 8.085]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation73", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 14.5, 0, 2, 17.833, 0, 2, 18.933, 0, 2, 20, 0, 2, 21.067, 0, 2, 21.467, 0, 2, 21.733, 0, 2, 22.967, 0, 2, 25.833, 0, 0, 27.333, 11.915]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation74", "Segments": [0, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 14.5, 0, 2, 17.833, 0, 2, 18.933, 0, 2, 20, 0, 2, 21.067, 0, 2, 21.467, 0, 2, 21.733, 0, 2, 22.967, 0, 2, 25.833, 0, 0, 27.333, 12.341]}, {"Target": "Parameter", "Id": "Param146", "Segments": [0, 1, 2, 1.167, 1, 2, 2, 1, 0, 2.033, 0, 2, 2.5, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 14.5, 0, 2, 17.833, 0, 2, 18.933, 0, 2, 20, 0, 2, 21.067, 0, 2, 21.467, 0, 2, 21.733, 0, 2, 22.967, 0, 2, 25.833, 0, 2, 27.333, 0]}, {"Target": "Parameter", "Id": "Param110", "Segments": [0, 0, 2, 1.167, 0, 0, 2, 19, 0, 2.1, 0, 2, 2.5, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 14.5, 0, 2, 17.833, 0, 2, 18.933, 0, 2, 20, 0, 2, 21.067, 0, 2, 21.467, 0, 2, 21.733, 0, 2, 22.967, 0, 2, 25.833, 0, 2, 27.333, 0]}, {"Target": "Parameter", "Id": "Param111", "Segments": [0, 0, 2, 1.167, 0, 2, 2, 0, 0, 2.1, 22, 0, 2.233, -28, 0, 2.367, 11, 1, 2.411, 11, 2.456, 7.236, 2.5, 0, 1, 2.533, -5.427, 2.567, -8, 2.6, -8, 0, 2.8, 10, 0, 3, -8, 0, 3.2, 10, 0, 3.4, -8, 1, 3.467, -8, 3.533, 2.482, 3.6, 10, 1, 3.644, 15.012, 3.689, 15, 3.733, 15, 2, 4, 15, 0, 4.2, -8, 0, 4.367, 4, 0, 4.533, 0, 2, 4.767, 0, 0, 4.833, -8.58, 0, 4.967, 6, 0, 5.1, -3, 0, 5.233, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 14.5, 0, 2, 17.833, 0, 2, 18.933, 0, 2, 20, 0, 2, 21.067, 0, 2, 21.467, 0, 2, 21.733, 0, 2, 22.967, 0, 2, 25.833, 0, 2, 27.333, 0]}, {"Target": "Parameter", "Id": "Param248", "Segments": [0, 0, 2, 2.5, 0, 0, 2.633, -15, 0, 2.833, 8, 0, 3.067, -7, 0, 3.267, 4, 0, 3.533, -7, 1, 3.578, -7, 3.622, 19.272, 3.667, 22, 1, 3.778, 28.82, 3.889, 30, 4, 30, 0, 4.2, -30, 2, 10.667, -30, 2, 11.633, -30, 2, 11.8, -30, 2, 14.5, -30, 2, 17.833, -30, 2, 18.933, -30, 2, 20, -30, 2, 21.067, -30, 2, 21.467, -30, 2, 21.733, -30, 2, 22.967, -30, 2, 25.833, -30, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param112", "Segments": [0, -30, 2, 1.167, -30, 0, 2.033, -19, 2, 2.5, -19, 1, 2.556, -19, 2.611, 0.749, 2.667, 6, 1, 2.722, 11.251, 2.778, 11, 2.833, 11, 0, 3.033, 1, 0, 3.233, 11, 0, 3.433, 1, 1, 3.5, 1, 3.566, 9.523, 3.633, 11, 1, 3.755, 13.709, 3.878, 14, 4, 14, 0, 4.2, -7.22, 0, 4.333, -5, 0, 4.7, -6.68, 0, 10.667, -5, 2, 11.633, -5, 2, 11.8, -5, 2, 14.5, -5, 2, 17.833, -5, 2, 18.933, -5, 2, 20, -5, 2, 21.067, -5, 2, 21.467, -5, 2, 21.733, -5, 2, 22.967, -5, 2, 25.833, -5, 0, 27.333, -30]}, {"Target": "Parameter", "Id": "Param249", "Segments": [0, 0, 2, 2.033, 0, 0, 2.167, -30, 0, 2.5, 0, 0, 2.8, -30, 0, 3, -12, 0, 3.167, -30, 0, 3.367, -12, 1, 3.434, -12, 3.5, -20.104, 3.567, -22.681, 1, 3.722, -28.693, 3.878, -30, 4.033, -30, 0, 4.233, -1.206, 2, 10.667, -1.206, 2, 11.633, -1.206, 2, 11.8, -1.206, 2, 14.5, -1.206, 2, 17.833, -1.206, 2, 18.933, -1.206, 2, 20, -1.206, 2, 21.067, -1.206, 2, 21.467, -1.206, 2, 21.733, -1.206, 2, 22.967, -1.206, 2, 25.833, -1.206, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param206", "Segments": [0, 0, 2, 1.167, 0, 0, 2.033, -30, 2, 2.5, -30, 0, 2.667, 0, 0, 2.933, -30, 0, 3.133, 3, 0, 3.333, -30, 0, 3.533, 3, 2, 10.667, 3, 2, 11.633, 3, 2, 11.8, 3, 2, 14.5, 3, 2, 17.833, 3, 2, 18.933, 3, 2, 20, 3, 2, 21.067, 3, 2, 21.467, 3, 2, 21.733, 3, 2, 22.967, 3, 2, 25.833, 3, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param113", "Segments": [0, 1, 2, 1.167, 1, 2, 2.033, 1, 0, 2.167, 0, 0, 2.333, 1, 2, 2.5, 1, 2, 2.8, 1, 2, 3.9, 1, 0, 4.133, 0, 0, 4.267, 1, 2, 10.667, 1, 2, 11.633, 1, 2, 11.8, 1, 2, 14.5, 1, 2, 17.833, 1, 2, 18.933, 1, 2, 20, 1, 2, 21.067, 1, 2, 21.467, 1, 2, 21.733, 1, 2, 22.967, 1, 2, 25.833, 1, 2, 27.333, 1]}, {"Target": "Parameter", "Id": "Param253", "Segments": [0, 0, 2, 4.767, 0, 0, 4.833, 1, 2, 10.667, 1, 2, 11.633, 1, 2, 11.8, 1, 2, 14.5, 1, 2, 17.833, 1, 2, 18.933, 1, 2, 20, 1, 2, 21.067, 1, 2, 21.467, 1, 2, 21.733, 1, 2, 22.967, 1, 2, 25.833, 1, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param254", "Segments": [0, 0, 2, 4.767, 0, 0, 5.133, 0.8, 2, 5.967, 0.8, 0, 6.133, 1, 2, 10.667, 1, 2, 11.633, 1, 2, 11.8, 1, 2, 14.5, 1, 2, 17.833, 1, 2, 18.933, 1, 2, 20, 1, 2, 21.067, 1, 2, 21.467, 1, 2, 21.733, 1, 2, 22.967, 1, 2, 25.833, 1, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param158", "Segments": [0, 1, 2, 0.167, 1, 0, 0.2, 0, 2, 1.167, 0, 2, 1.867, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 14.5, 0, 2, 17.833, 0, 2, 18.933, 0, 2, 20, 0, 2, 21.067, 0, 2, 21.467, 0, 2, 21.733, 0, 2, 22.967, 0, 2, 25.833, 0, 2, 27.333, 0]}, {"Target": "Parameter", "Id": "Param149", "Segments": [0, 0, 0, 0.2, 12, 0, 0.367, -8, 0, 0.533, -0.138, 0, 0.7, -6, 0, 0.867, 0, 2, 1, 0, 2, 1.167, 0, 2, 1.767, 0, 0, 1.867, 6.274, 0, 1.9, 4.847, 1, 1.922, 4.847, 1.945, 4.817, 1.967, 5.346, 1, 2, 6.139, 2.034, 7.473, 2.067, 7.473, 0, 2.167, 5.686, 0, 2.267, 6.939, 0, 2.367, 6.274, 2, 2.467, 6.274, 2, 2.6, 6.274, 2, 10.667, 6.274, 2, 11.633, 6.274, 2, 11.8, 6.274, 2, 14.5, 6.274, 2, 17.833, 6.274, 2, 18.933, 6.274, 2, 20, 6.274, 2, 21.067, 6.274, 2, 21.467, 6.274, 2, 21.733, 6.274, 2, 22.967, 6.274, 2, 25.833, 6.274, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param150", "Segments": [0, 0, 0, 0.2, -30, 0, 0.533, 30, 0, 0.867, -13, 0, 1.067, 10.8, 0, 1.233, 0, 2, 1.867, 0, 0, 1.9, -7.562, 0, 2.067, 9.12, 0, 2.267, -7.562, 0, 2.467, 5.147, 0, 2.7, -1.56, 0, 2.967, 1.62, 0, 3.367, -1.56, 0, 3.767, 1.62, 0, 4.233, -1.56, 0, 4.7, 1.62, 0, 4.867, -22, 0, 5.167, 21, 0, 5.467, -14, 0, 5.7, 10.456, 0, 5.967, -7.562, 0, 6.167, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 14.5, 0, 2, 17.833, 0, 2, 18.933, 0, 2, 20, 0, 2, 21.067, 0, 2, 21.467, 0, 2, 21.733, 0, 2, 22.967, 0, 2, 25.833, 0, 2, 27.333, 0]}, {"Target": "Parameter", "Id": "Param151", "Segments": [0, 0, 0, 0.133, -9, 0, 0.333, 14, 0, 0.7, -20, 1, 0.756, -20, 0.811, -14.887, 0.867, -7.618, 1, 0.911, -1.803, 0.956, 0, 1, 0, 2, 1.167, 0, 2, 1.867, 0, 0, 1.9, 7.844, 0, 2.067, -4.74, 0, 2.233, 7.844, 0, 2.4, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 14.5, 0, 2, 17.833, 0, 2, 18.933, 0, 2, 20, 0, 2, 21.067, 0, 2, 21.467, 0, 2, 21.733, 0, 2, 22.967, 0, 2, 25.833, 0, 2, 27.333, 0]}, {"Target": "Parameter", "Id": "Param255", "Segments": [0, 0, 2, 4.7, 0, 0, 4.867, 1, 2, 10.667, 1, 2, 11.633, 1, 2, 11.8, 1, 2, 14.5, 1, 2, 17.833, 1, 2, 18.933, 1, 2, 20, 1, 2, 21.067, 1, 2, 21.467, 1, 2, 21.733, 1, 2, 22.967, 1, 2, 25.833, 1, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param256", "Segments": [0, 0, 2, 4.7, 0, 0, 4.9, 1, 2, 10.667, 1, 2, 11.633, 1, 2, 11.8, 1, 2, 14.5, 1, 2, 17.833, 1, 2, 18.933, 1, 2, 20, 1, 2, 21.067, 1, 2, 21.467, 1, 2, 21.733, 1, 2, 22.967, 1, 2, 25.833, 1, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param152", "Segments": [0, -30, 0, 0.2, 20.313, 0, 0.433, -1.585, 0, 0.567, 0, 2, 1.167, 0, 2, 1.867, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 14.5, 0, 2, 17.833, 0, 2, 18.933, 0, 2, 20, 0, 2, 21.067, 0, 2, 21.467, 0, 2, 21.733, 0, 2, 22.967, 0, 2, 25.833, 0, 2, 27.333, 0]}, {"Target": "Parameter", "Id": "Param155", "Segments": [0, 1, 0, 0.033, 0, 2, 1.167, 0, 2, 1.867, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 14.5, 0, 2, 17.833, 0, 2, 18.933, 0, 2, 20, 0, 2, 21.067, 0, 2, 21.467, 0, 2, 21.733, 0, 2, 22.967, 0, 2, 25.833, 0, 2, 27.333, 0]}, {"Target": "Parameter", "Id": "Param154", "Segments": [0, -30, 2, 0.067, -30, 0, 0.267, 20.313, 0, 0.433, -3.507, 0, 0.567, 2.967, 0, 0.667, 0, 2, 1.167, 0, 2, 1.867, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 14.5, 0, 2, 17.833, 0, 2, 18.933, 0, 2, 20, 0, 2, 21.067, 0, 2, 21.467, 0, 2, 21.733, 0, 2, 22.967, 0, 2, 25.833, 0, 2, 27.333, 0]}, {"Target": "Parameter", "Id": "Param181", "Segments": [0, 1, 2, 1.167, 1, 2, 1.867, 1, 2, 10.667, 1, 2, 11.633, 1, 2, 11.8, 1, 2, 14.5, 1, 2, 17.833, 1, 2, 18.933, 1, 2, 20, 1, 2, 21.067, 1, 2, 21.467, 1, 2, 21.733, 1, 2, 22.967, 1, 2, 25.833, 1, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param180", "Segments": [0, 0, 2, 1.167, 0, 2, 1.867, 0, 0, 10.667, 0.5, 2, 27.333, 0.5]}, {"Target": "Parameter", "Id": "Param156", "Segments": [0, 0, 0, 0.333, -30, 0, 0.467, 24, 0, 0.667, -3.215, 0, 0.8, 0, 2, 1.167, 0, 2, 1.867, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 14.5, 0, 2, 17.833, 0, 2, 18.933, 0, 2, 20, 0, 2, 21.067, 0, 2, 21.467, 0, 2, 21.733, 0, 2, 22.967, 0, 2, 25.833, 0, 2, 27.333, 0]}, {"Target": "Parameter", "Id": "Param157", "Segments": [0, 1, 2, 0.333, 1, 0, 0.367, 0, 2, 1.167, 0, 2, 1.867, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 14.5, 0, 2, 17.833, 0, 2, 18.933, 0, 2, 20, 0, 2, 21.067, 0, 2, 21.467, 0, 2, 21.733, 0, 2, 22.967, 0, 2, 25.833, 0, 2, 27.333, 0]}, {"Target": "Parameter", "Id": "Param159", "Segments": [0, 1, 2, 0.067, 1, 0, 0.2, 0, 2, 1.167, 0, 0, 1.867, 0.2, 0, 2.033, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 14.5, 0, 2, 17.833, 0, 2, 18.933, 0, 2, 20, 0, 2, 21.067, 0, 2, 21.467, 0, 2, 21.733, 0, 2, 22.967, 0, 2, 25.833, 0, 2, 27.333, 0]}, {"Target": "Parameter", "Id": "Param160", "Segments": [0, 0, 2, 0.067, 0, 0, 0.2, 14, 0, 0.267, -13, 0, 0.433, 12, 0, 0.667, -2, 0, 0.833, 0, 2, 1.167, 0, 0, 1.867, 1, 0, 2.033, 0, 0, 2.167, 0.54, 0, 2.3, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 14.5, 0, 2, 17.833, 0, 2, 18.933, 0, 2, 20, 0, 2, 21.067, 0, 2, 21.467, 0, 2, 21.733, 0, 2, 22.967, 0, 2, 25.833, 0, 2, 27.333, 0]}, {"Target": "Parameter", "Id": "Param119", "Segments": [0, 0, 2, 4.333, 0, 0, 4.4, 1, 2, 4.9, 1, 0, 4.933, 0, 2, 6, 0, 0, 6.033, 1, 2, 10.667, 1, 0, 10.7, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 14.5, 0, 2, 17.833, 0, 2, 18.933, 0, 2, 20, 0, 2, 21.067, 0, 2, 21.467, 0, 2, 21.733, 0, 2, 22.967, 0, 2, 25.833, 0, 2, 27.333, 0]}, {"Target": "Parameter", "Id": "Param265", "Segments": [0, 0, 2, 4.333, 0, 0, 4.4, 1, 2, 5.3, 1, 0, 5.333, 0, 2, 11.8, 0, 2, 14.5, 0, 2, 17.833, 0, 2, 20, 0, 2, 21.067, 0, 2, 21.467, 0, 2, 21.733, 0, 2, 22.967, 0, 2, 25.833, 0, 2, 27.333, 0]}, {"Target": "Parameter", "Id": "Param118", "Segments": [0, 0, 1, 1.467, 0, 2.933, -3.785, 4.4, -12.836, 1, 4.5, -13.454, 4.6, -23, 4.7, -23, 2, 5.3, -23, 2, 5.767, -23, 0, 6, 6.54, 1, 6.067, 6.54, 6.133, 6.671, 6.2, 6.152, 1, 6.378, 4.771, 6.555, 1.384, 6.733, -0.594, 1, 7.044, -4.055, 7.356, -5.454, 7.667, -5.454, 2, 10.667, -5.454, 2, 11.633, -5.454, 2, 11.8, -5.454, 2, 14.5, -5.454, 2, 17.833, -5.454, 2, 18.933, -5.454, 2, 20, -5.454, 2, 21.067, -5.454, 2, 21.467, -5.454, 2, 21.733, -5.454, 2, 22.967, -5.454, 2, 25.833, -5.454, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param120", "Segments": [0, 0, 0, 4.4, 9, 1, 4.567, -3.333, 4.733, -15.667, 4.9, -28, 2, 5.3, -28, 0, 5.767, -18, 2, 6.2, -18, 0, 7.033, -12.995, 0, 8, -15.177, 0, 9.067, -13.59, 2, 10.667, -13.59, 2, 11.633, -13.59, 2, 11.8, -13.59, 2, 14.5, -13.59, 2, 17.833, -13.59, 2, 18.933, -13.59, 2, 20, -13.59, 2, 21.067, -13.59, 2, 21.467, -13.59, 2, 21.733, -13.59, 2, 22.967, -13.59, 2, 25.833, -13.59, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param121", "Segments": [0, 0, 1, 1.467, 0, 2.933, -2.454, 4.4, -8, 1, 4.5, -8.46, 4.6, -8.92, 4.7, -9.38, 2, 5.3, -9.38, 1, 5.456, -9.38, 5.611, -21.561, 5.767, -26, 1, 5.911, -30.122, 6.056, -30, 6.2, -30, 1, 6.278, -30, 6.355, -22.266, 6.433, -19.94, 1, 6.522, -17.282, 6.611, -15.942, 6.7, -14.54, 1, 6.833, -12.436, 6.967, -11.78, 7.1, -11.78, 0, 7.467, -13.04, 0, 7.833, -11.78, 0, 8.2, -13.04, 0, 8.633, -11.78, 0, 9, -13.04, 0, 9.567, -11.467, 0, 10.667, -13.04, 2, 11.633, -13.04, 2, 11.8, -13.04, 2, 14.5, -13.04, 2, 17.833, -13.04, 2, 18.933, -13.04, 2, 20, -13.04, 2, 21.067, -13.04, 2, 21.467, -13.04, 2, 21.733, -13.04, 2, 22.967, -13.04, 2, 25.833, -13.04, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param259", "Segments": [0, 0, 2, 8.233, 0, 0, 8.367, -15, 0, 8.533, 14, 0, 8.667, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 14.5, 0, 2, 17.833, 0, 2, 18.933, 0, 2, 20, 0, 2, 21.067, 0, 2, 21.467, 0, 2, 21.733, 0, 2, 22.967, 0, 2, 25.833, 0, 2, 27.333, 0]}, {"Target": "Parameter", "Id": "Param124", "Segments": [0, 0, 2, 5.3, 0, 2, 5.767, 0, 0, 6.167, 6.66, 2, 10.667, 6.66, 2, 11.633, 6.66, 2, 11.8, 6.66, 2, 14.5, 6.66, 2, 17.833, 6.66, 2, 18.933, 6.66, 2, 20, 6.66, 2, 21.067, 6.66, 2, 21.467, 6.66, 2, 21.733, 6.66, 2, 22.967, 6.66, 2, 25.833, 6.66, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param130", "Segments": [0, 0, 0, 4.4, 8, 2, 5.3, 8, 2, 5.767, 8, 2, 10.667, 8, 2, 11.633, 8, 2, 11.8, 8, 2, 14.5, 8, 2, 17.833, 8, 2, 18.933, 8, 2, 20, 8, 2, 21.067, 8, 2, 21.467, 8, 2, 21.733, 8, 2, 22.967, 8, 2, 25.833, 8, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param131", "Segments": [0, 0, 0, 4.4, 26, 2, 5.3, 26, 2, 5.767, 26, 2, 10.667, 26, 2, 11.633, 26, 2, 11.8, 26, 2, 14.5, 26, 2, 17.833, 26, 2, 18.933, 26, 2, 20, 26, 2, 21.067, 26, 2, 21.467, 26, 2, 21.733, 26, 2, 22.967, 26, 2, 25.833, 26, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param125", "Segments": [0, 0, 0, 4.4, 24, 2, 5.3, 24, 2, 5.767, 24, 2, 10.667, 24, 2, 11.633, 24, 2, 11.8, 24, 2, 14.5, 24, 2, 17.833, 24, 2, 18.933, 24, 2, 20, 24, 2, 21.067, 24, 2, 21.467, 24, 2, 21.733, 24, 2, 22.967, 24, 2, 25.833, 24, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param126", "Segments": [0, 0, 0, 4.4, -28, 2, 5.267, -28, 2, 5.767, -28, 2, 10.667, -28, 2, 11.633, -28, 2, 11.8, -28, 2, 14.5, -28, 2, 17.833, -28, 2, 18.933, -28, 2, 20, -28, 2, 21.067, -28, 2, 21.467, -28, 2, 21.733, -28, 2, 22.967, -28, 2, 25.833, -28, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param127", "Segments": [0, 0, 0, 4.4, 20, 2, 5.3, 20, 2, 5.767, 20, 2, 10.667, 20, 2, 11.633, 20, 2, 11.8, 20, 2, 14.5, 20, 2, 17.833, 20, 2, 18.933, 20, 2, 20, 20, 2, 21.067, 20, 2, 21.467, 20, 2, 21.733, 20, 2, 22.967, 20, 2, 25.833, 20, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param128", "Segments": [0, 0, 0, 4.4, -20, 2, 5.3, -20, 2, 5.767, -20, 2, 10.667, -20, 2, 11.633, -20, 2, 11.8, -20, 2, 14.5, -20, 2, 17.833, -20, 2, 18.933, -20, 2, 20, -20, 2, 21.067, -20, 2, 21.467, -20, 2, 21.733, -20, 2, 22.967, -20, 2, 25.833, -20, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param171", "Segments": [0, 0, 0, 4.4, 1, 2, 5.3, 1, 2, 5.767, 1, 2, 6.7, 1, 2, 8.467, 1, 2, 10.667, 1, 2, 11.633, 1, 2, 11.8, 1, 2, 14.5, 1, 2, 17.833, 1, 2, 18.933, 1, 2, 20, 1, 2, 21.067, 1, 2, 21.467, 1, 2, 21.733, 1, 2, 22.967, 1, 2, 25.833, 1, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param172", "Segments": [0, 0, 2, 4.4, 0, 2, 5.3, 0, 2, 5.767, 0, 0, 6.7, 1, 2, 8.333, 1, 0, 8.467, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 14.5, 0, 2, 17.833, 0, 2, 18.933, 0, 2, 20, 0, 2, 21.067, 0, 2, 21.467, 0, 2, 21.733, 0, 2, 22.967, 0, 2, 25.833, 0, 2, 27.333, 0]}, {"Target": "Parameter", "Id": "Param122", "Segments": [0, 0, 0, 4.4, 1, 2, 5.3, 1, 2, 5.767, 1, 0, 6.7, 0, 2, 10.667, 0, 2, 11.633, 0, 2, 11.8, 0, 2, 14.5, 0, 2, 17.833, 0, 2, 18.933, 0, 2, 20, 0, 2, 21.067, 0, 2, 21.467, 0, 2, 21.733, 0, 2, 22.967, 0, 2, 25.833, 0, 2, 27.333, 0]}, {"Target": "Parameter", "Id": "Param170", "Segments": [0, 0, 2, 5.3, 0, 2, 5.767, 0, 2, 8.367, 0, 0, 8.467, 1, 2, 10.667, 1, 2, 11.633, 1, 2, 11.8, 1, 2, 14.5, 1, 2, 17.833, 1, 2, 18.933, 1, 2, 20, 1, 2, 21.067, 1, 2, 21.467, 1, 2, 21.733, 1, 2, 22.967, 1, 2, 25.833, 1, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param132", "Segments": [0, 0, 2, 5.3, 0, 2, 5.767, 0, 0, 5.867, 27.193, 0, 5.967, 0.193, 0, 6.067, 27.193, 0, 6.167, 0.193, 0, 6.267, 27.193, 0, 6.367, 0.193, 0, 6.467, 27.193, 0, 6.567, 0.193, 0, 6.667, 27.193, 0, 6.767, 0.193, 0, 6.867, 27.193, 0, 6.967, 0.193, 0, 7.067, 27.193, 0, 7.167, 0.193, 0, 7.267, 27.193, 0, 7.367, 0.193, 0, 7.467, 27.193, 0, 7.567, 0.193, 0, 7.667, 27.193, 0, 7.767, 0.193, 0, 7.867, 27.193, 0, 7.967, 0.193, 0, 8.067, 27.193, 0, 8.167, 0.193, 0, 8.267, 27.193, 0, 8.367, 0.193, 0, 8.467, 27.193, 0, 8.567, 0.193, 0, 8.667, 27.193, 0, 8.767, 0.193, 0, 8.867, 27.193, 0, 8.967, 0.193, 0, 9.067, 27.193, 0, 9.167, 0.193, 0, 9.267, 27.193, 0, 9.367, 0.193, 0, 9.467, 27.193, 0, 9.567, 0.193, 0, 9.667, 27.193, 0, 9.767, 0.193, 2, 10.667, 0.193, 2, 11.633, 0.193, 2, 11.8, 0.193, 2, 14.5, 0.193, 2, 17.833, 0.193, 2, 18.933, 0.193, 2, 20, 0.193, 2, 21.067, 0.193, 2, 21.467, 0.193, 2, 21.733, 0.193, 2, 22.967, 0.193, 2, 25.833, 0.193, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param250", "Segments": [0, 0, 2, 5.3, 0, 2, 5.767, 0, 2, 8.333, 0, 1, 8.389, 0.333, 8.444, 0.667, 8.5, 1, 1, 8.556, 0.914, 8.611, 0.829, 8.667, 0.743, 1, 8.734, 0.829, 8.8, 0.914, 8.867, 1, 2, 10.667, 1, 2, 11.633, 1, 2, 11.8, 1, 2, 14.5, 1, 2, 17.833, 1, 2, 18.933, 1, 2, 20, 1, 2, 21.067, 1, 2, 21.467, 1, 2, 21.733, 1, 2, 22.967, 1, 2, 25.833, 1, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param258", "Segments": [0, 0, 2, 5.767, 0, 0, 5.867, 18, 0, 5.967, -9, 0, 6.067, 18, 0, 6.167, -9, 0, 6.267, 18, 0, 6.367, -9, 0, 6.467, 18, 0, 6.567, -9, 0, 6.667, 18, 0, 6.767, -9, 0, 6.867, 18, 0, 6.967, -9, 0, 7.067, 18, 0, 7.167, -9, 0, 7.267, 18, 0, 7.367, -9, 0, 7.467, 18, 0, 7.567, -9, 0, 7.667, 18, 0, 7.767, -9, 0, 7.867, 18, 0, 7.967, -9, 0, 8.067, 18, 0, 8.167, -9, 0, 8.267, 18, 0, 8.367, -9, 0, 8.467, 18, 0, 8.567, -9, 0, 8.667, 18, 0, 8.767, -9, 0, 8.867, 18, 0, 8.967, -9, 0, 9.067, 18, 0, 9.167, -9, 0, 9.267, 18, 0, 9.367, -9, 0, 9.467, 18, 0, 9.567, -9, 0, 9.667, 18, 0, 9.767, -9, 2, 10.667, -9, 2, 11.633, -9, 2, 11.8, -9, 2, 14.5, -9, 2, 17.833, -9, 2, 18.933, -9, 2, 20, -9, 2, 21.067, -9, 2, 21.467, -9, 2, 21.733, -9, 2, 22.967, -9, 2, 25.833, -9, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param162", "Segments": [0, 0, 2, 5.3, 0, 2, 5.767, 0, 0, 5.867, 18, 0, 5.967, -9, 0, 6.067, 18, 0, 6.167, -9, 0, 6.267, 18, 0, 6.367, -9, 0, 6.467, 18, 0, 6.567, -9, 0, 6.667, 18, 0, 6.767, -9, 0, 6.867, 18, 0, 6.967, -9, 0, 7.067, 18, 0, 7.167, -9, 0, 7.267, 18, 0, 7.367, -9, 0, 7.467, 18, 0, 7.567, -9, 0, 7.667, 18, 0, 7.767, -9, 0, 7.867, 18, 0, 7.967, -9, 0, 8.067, 18, 0, 8.167, -9, 0, 8.267, 18, 0, 8.367, -9, 0, 8.467, 18, 0, 8.567, -9, 0, 8.667, 18, 0, 8.767, -9, 0, 8.867, 18, 0, 8.967, -9, 0, 9.067, 18, 0, 9.167, -9, 0, 9.267, 18, 0, 9.367, -9, 0, 9.467, 18, 0, 9.567, -9, 0, 9.667, 18, 0, 9.767, -9, 2, 10.667, -9, 2, 11.633, -9, 2, 11.8, -9, 2, 14.5, -9, 2, 17.833, -9, 2, 18.933, -9, 2, 20, -9, 2, 21.067, -9, 2, 21.467, -9, 2, 21.733, -9, 2, 22.967, -9, 2, 25.833, -9, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param169", "Segments": [0, 0, 2, 5.3, 0, 2, 5.767, 0, 0, 5.833, -6, 0, 5.933, 30, 0, 6.033, -17, 0, 6.133, 30, 0, 6.233, -17, 0, 6.333, 30, 0, 6.433, -17, 0, 6.533, 30, 0, 6.633, -17, 0, 6.733, 30, 0, 6.833, -17, 0, 6.933, 30, 0, 7.033, -17, 0, 7.133, 30, 0, 7.233, -17, 0, 7.333, 30, 0, 7.433, -17, 0, 7.533, 30, 0, 7.633, -17, 0, 7.733, 30, 0, 7.833, -17, 0, 7.933, 30, 0, 8.033, -17, 0, 8.133, 30, 0, 8.233, -17, 0, 8.333, 30, 0, 8.433, -17, 0, 8.533, 30, 0, 8.633, -17, 0, 8.733, 30, 0, 8.833, -17, 0, 8.933, 30, 0, 9.033, -17, 0, 9.133, 30, 0, 9.233, -17, 0, 9.333, 30, 0, 9.433, -17, 0, 9.533, 30, 0, 9.633, -17, 0, 9.733, 30, 0, 9.833, -17, 2, 10.667, -17, 2, 11.633, -17, 2, 11.8, -17, 2, 14.5, -17, 2, 17.833, -17, 2, 18.933, -17, 2, 20, -17, 2, 21.067, -17, 2, 21.467, -17, 2, 21.733, -17, 2, 22.967, -17, 2, 25.833, -17, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param174", "Segments": [0, 0, 2, 5.3, 0, 2, 5.767, 0, 0, 5.9, 18, 0, 6, -9, 0, 6.1, 18, 0, 6.2, -9, 0, 6.3, 18, 0, 6.4, -9, 0, 6.5, 18, 0, 6.6, -9, 0, 6.7, 18, 0, 6.8, -9, 0, 6.9, 18, 0, 7, -9, 0, 7.1, 18, 0, 7.2, -9, 0, 7.3, 18, 0, 7.4, -9, 0, 7.5, 18, 0, 7.6, -9, 0, 7.7, 18, 0, 7.8, -9, 0, 7.9, 18, 0, 8, -9, 0, 8.1, 18, 0, 8.2, -9, 0, 8.3, 18, 0, 8.4, -9, 0, 8.5, 18, 0, 8.6, -9, 0, 8.7, 18, 0, 8.8, -9, 0, 8.9, 18, 0, 9, -9, 0, 9.1, 18, 0, 9.2, -9, 0, 9.3, 18, 0, 9.4, -9, 0, 9.5, 18, 0, 9.6, -9, 0, 9.7, 18, 0, 9.8, -9, 2, 10.667, -9, 2, 11.633, -9, 2, 11.8, -9, 2, 14.5, -9, 2, 17.833, -9, 2, 18.933, -9, 2, 20, -9, 2, 21.067, -9, 2, 21.467, -9, 2, 21.733, -9, 2, 22.967, -9, 2, 25.833, -9, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param175", "Segments": [0, 0, 2, 5.3, 0, 2, 5.767, 0, 0, 5.867, -6, 0, 5.967, 30, 0, 6.067, -17, 0, 6.167, 30, 0, 6.267, -17, 0, 6.367, 30, 0, 6.467, -17, 0, 6.567, 30, 0, 6.667, -17, 0, 6.767, 30, 0, 6.867, -17, 0, 6.967, 30, 0, 7.067, -17, 0, 7.167, 30, 0, 7.267, -17, 0, 7.367, 30, 0, 7.467, -17, 0, 7.567, 30, 0, 7.667, -17, 0, 7.767, 30, 0, 7.867, -17, 0, 7.967, 30, 0, 8.067, -17, 0, 8.167, 30, 0, 8.267, -17, 0, 8.367, 30, 0, 8.467, -17, 0, 8.567, 30, 0, 8.667, -17, 0, 8.767, 30, 0, 8.867, -17, 0, 8.967, 30, 0, 9.067, -17, 0, 9.167, 30, 0, 9.267, -17, 0, 9.367, 30, 0, 9.467, -17, 0, 9.567, 30, 0, 9.667, -17, 0, 9.767, 30, 0, 9.867, -17, 2, 10.667, -17, 2, 11.633, -17, 2, 11.8, -17, 2, 14.5, -17, 2, 17.833, -17, 2, 18.933, -17, 2, 20, -17, 2, 21.067, -17, 2, 21.467, -17, 2, 21.733, -17, 2, 22.967, -17, 2, 25.833, -17, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param163", "Segments": [0, 0, 1, 1.467, 0, 2.933, 2.693, 4.4, 7, 1, 4.7, 7.881, 5, 8.105, 5.3, 8.92, 1, 5.456, 9.342, 5.611, 11.216, 5.767, 11.605, 1, 5.789, 11.661, 5.811, 11.611, 5.833, 11.639, 1, 5.866, 11.68, 5.9, 13.446, 5.933, 13.446, 0, 6.033, 11.639, 0, 6.133, 13.446, 0, 6.233, 11.639, 0, 6.333, 13.446, 0, 6.433, 11.639, 0, 6.533, 13.446, 0, 6.633, 11.639, 0, 6.733, 13.446, 0, 6.833, 11.639, 0, 6.933, 13.446, 0, 7.033, 11.639, 0, 7.133, 13.446, 0, 7.233, 11.639, 0, 7.333, 13.446, 0, 7.433, 11.639, 0, 7.533, 13.446, 0, 7.633, 11.639, 0, 7.733, 13.446, 0, 7.833, 11.639, 0, 7.933, 13.446, 0, 8.033, 11.639, 0, 8.133, 13.446, 0, 8.233, 11.639, 0, 8.333, 13.446, 0, 8.433, 11.639, 0, 8.533, 13.446, 0, 8.633, 11.639, 0, 8.733, 13.446, 0, 8.833, 11.639, 0, 8.933, 13.446, 0, 9.033, 11.639, 0, 9.133, 13.446, 0, 9.233, 11.639, 0, 9.333, 13.446, 0, 9.433, 11.639, 0, 9.533, 13.446, 0, 9.633, 11.639, 2, 10.667, 11.639, 2, 11.633, 11.639, 2, 11.8, 11.639, 2, 14.5, 11.639, 2, 17.833, 11.639, 2, 18.933, 11.639, 2, 20, 11.639, 2, 21.067, 11.639, 2, 21.467, 11.639, 2, 21.733, 11.639, 2, 22.967, 11.639, 2, 25.833, 11.639, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param164", "Segments": [0, 0, 0, 4.4, 15, 2, 5.3, 15, 2, 5.767, 15, 0, 5.9, -9, 0, 6, 20, 0, 6.1, -9, 0, 6.2, 20, 0, 6.3, -9, 0, 6.4, 20, 0, 6.5, -9, 0, 6.6, 20, 0, 6.7, -9, 0, 6.8, 20, 0, 6.9, -9, 0, 7, 20, 0, 7.1, -9, 0, 7.2, 20, 0, 7.3, -9, 0, 7.4, 20, 0, 7.5, -9, 0, 7.6, 20, 0, 7.7, -9, 0, 7.8, 20, 0, 7.9, -9, 0, 8, 20, 0, 8.1, -9, 0, 8.2, 20, 0, 8.3, -9, 0, 8.4, 20, 0, 8.5, -9, 0, 8.6, 20, 0, 8.7, -9, 0, 8.8, 20, 0, 8.9, -9, 0, 9, 20, 0, 9.1, -9, 0, 9.2, 20, 0, 9.3, -9, 0, 9.4, 20, 0, 9.5, -9, 0, 9.6, 20, 0, 9.7, -9, 0, 9.8, 20, 2, 10.667, 20, 2, 11.633, 20, 2, 11.8, 20, 2, 14.5, 20, 2, 17.833, 20, 2, 18.933, 20, 2, 20, 20, 2, 21.067, 20, 2, 21.467, 20, 2, 21.733, 20, 2, 22.967, 20, 2, 25.833, 20, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param165", "Segments": [0, 0, 0, 4.4, 18, 2, 5.3, 18, 2, 5.767, 18, 0, 5.933, -15, 0, 6.033, 30, 0, 6.167, -15, 0, 6.267, 30, 0, 6.367, -15, 0, 6.467, 30, 0, 6.567, -15, 0, 6.667, 30, 1, 6.689, 30, 6.711, -15, 6.733, -15, 0, 6.833, 30, 0, 6.967, -15, 0, 7.067, 30, 0, 7.167, -15, 0, 7.267, 30, 0, 7.367, -15, 0, 7.467, 30, 1, 7.489, 30, 7.511, -15, 7.533, -15, 0, 7.633, 30, 1, 7.678, 30, 7.722, -15, 7.767, -15, 0, 7.867, 30, 0, 7.967, -15, 0, 8.067, 30, 0, 8.167, -15, 0, 8.267, 30, 1, 8.289, 30, 8.311, -15, 8.333, -15, 0, 8.433, 30, 0, 8.567, -15, 0, 8.667, 30, 0, 8.767, -15, 0, 8.867, 30, 0, 8.967, -15, 0, 9.067, 30, 1, 9.089, 30, 9.111, -15, 9.133, -15, 0, 9.233, 30, 0, 9.367, -15, 0, 9.467, 30, 0, 9.567, -15, 0, 9.667, 30, 0, 9.767, -15, 0, 9.867, 30, 2, 10.667, 30, 2, 11.633, 30, 2, 11.8, 30, 2, 14.5, 30, 2, 17.833, 30, 2, 18.933, 30, 2, 20, 30, 2, 21.067, 30, 2, 21.467, 30, 2, 21.733, 30, 2, 22.967, 30, 2, 25.833, 30, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param166", "Segments": [0, 0, 0, 4.4, 12, 2, 5.3, 12, 2, 5.767, 12, 0, 5.9, 15, 2, 10.667, 15, 2, 11.633, 15, 2, 11.8, 15, 2, 14.5, 15, 2, 17.833, 15, 2, 18.933, 15, 2, 20, 15, 2, 21.067, 15, 2, 21.467, 15, 2, 21.733, 15, 2, 22.967, 15, 2, 25.833, 15, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param167", "Segments": [0, 0, 2, 5.3, 0, 2, 5.767, 0, 0, 5.9, 8.582, 0, 6, -10.601, 0, 6.1, 8.582, 0, 6.2, -10.601, 0, 6.3, 8.582, 0, 6.4, -10.601, 0, 6.5, 8.582, 0, 6.6, -10.601, 0, 6.7, 8.582, 0, 6.8, -10.601, 0, 6.9, 8.582, 0, 7, -10.601, 0, 7.1, 8.582, 0, 7.2, -10.601, 0, 7.3, 8.582, 0, 7.4, -10.601, 0, 7.5, 8.582, 0, 7.6, -10.601, 0, 7.7, 8.582, 0, 7.8, -10.601, 0, 7.9, 8.582, 0, 8, -10.601, 0, 8.1, 8.582, 0, 8.2, -10.601, 0, 8.3, 8.582, 0, 8.4, -10.601, 0, 8.5, 8.582, 0, 8.6, -10.601, 0, 8.7, 8.582, 0, 8.8, -10.601, 0, 8.9, 8.582, 0, 9, -10.601, 0, 9.1, 8.582, 0, 9.2, -10.601, 0, 9.3, 8.582, 0, 9.4, -10.601, 0, 9.5, 8.582, 0, 9.6, -10.601, 0, 9.7, 8.582, 0, 9.8, -10.601, 0, 9.9, 8.582, 2, 10.667, 8.582, 2, 11.633, 8.582, 2, 11.8, 8.582, 2, 14.5, 8.582, 2, 17.833, 8.582, 2, 18.933, 8.582, 2, 20, 8.582, 2, 21.067, 8.582, 2, 21.467, 8.582, 2, 21.733, 8.582, 2, 22.967, 8.582, 2, 25.833, 8.582, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param168", "Segments": [0, 0, 2, 5.3, 0, 2, 5.767, 0, 0, 5.833, -12.408, 0, 5.967, 10.372, 0, 6.067, -12.408, 0, 6.167, 10.372, 0, 6.267, -12.408, 0, 6.367, 10.372, 0, 6.467, -12.408, 0, 6.567, 10.372, 0, 6.667, -12.408, 0, 6.767, 10.372, 0, 6.867, -12.408, 0, 6.967, 10.372, 0, 7.067, -12.408, 0, 7.167, 10.372, 0, 7.267, -12.408, 0, 7.367, 10.372, 0, 7.467, -12.408, 0, 7.567, 10.372, 0, 7.667, -12.408, 0, 7.767, 10.372, 0, 7.867, -12.408, 0, 7.967, 10.372, 0, 8.067, -12.408, 0, 8.167, 10.372, 0, 8.267, -12.408, 0, 8.367, 10.372, 0, 8.467, -12.408, 0, 8.567, 10.372, 0, 8.667, -12.408, 0, 8.767, 10.372, 0, 8.867, -12.408, 0, 8.967, 10.372, 0, 9.067, -12.408, 0, 9.167, 10.372, 0, 9.267, -12.408, 0, 9.367, 10.372, 0, 9.467, -12.408, 0, 9.567, 10.372, 0, 9.667, -12.408, 0, 9.767, 10.372, 0, 9.867, -12.408, 2, 10.667, -12.408, 2, 11.633, -12.408, 2, 11.8, -12.408, 2, 14.5, -12.408, 2, 17.833, -12.408, 2, 18.933, -12.408, 2, 20, -12.408, 2, 21.067, -12.408, 2, 21.467, -12.408, 2, 21.733, -12.408, 2, 22.967, -12.408, 2, 25.833, -12.408, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param272", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param293", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "ParamCheek", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param268", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param193", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param192", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param196", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param282", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param183", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param194", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param195", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param289", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param290", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param291", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param186", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param187", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param188", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param292", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param189", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param190", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param191", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param8", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param23", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param24", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param25", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param26", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param27", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param200", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param286", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param287", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param288", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param202", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param205", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param203", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param204", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param21", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param213", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param215", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param216", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param217", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param218", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param12", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param197", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param198", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param199", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param208", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param279", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param270", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param271", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param230", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param231", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param222", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param207", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param236", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param237", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param239", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param240", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param241", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param242", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param243", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param244", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param20", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param275", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param224", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param225", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param227", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param226", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param232", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param285", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param274", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param276", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param138", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param84", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param85", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param91", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param2", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param87", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param88", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param94", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param95", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param92", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param93", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param96", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param97", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param89", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param90", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation56", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param211mao2", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param217mao2", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param202mao2", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param203mao2", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param204mao2", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param199mao2", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param200mao2", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param201mao2", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param208mao2", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param209mao2", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param210mao2", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param205mao2", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param206mao2", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param207mao2", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param213mao2", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param198mao2", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param219mao2", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation143mao2", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param264", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param100", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param105", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param108", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param109", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param107", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param284", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param101", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param106", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param104", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param103", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param102", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation71", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param148", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param114", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param153", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param161", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param123", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param178", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param179", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param173", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "Parameter", "Id": "Param129", "Segments": [0, 0, 0, 27.333, 0]}, {"Target": "PartOpacity", "Id": "Part37", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part51", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part67", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part36", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part40", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part90", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part41", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part42", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "fashi_L", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "hair_L2_Skinning", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part44", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part53", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part54", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part55", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part56", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part57", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part49", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part50", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "hand_L2", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "body", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "hand_R1", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "fashi_R", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "hair_R2_Skinning", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part38", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part15", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part45", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part46", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part61", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part62", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part63", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "ArtMesh57_Skinning", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part65", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part66", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part23", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part71", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part72", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part78", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part81", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part82", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part83", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part84", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part104", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part105", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part106", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part107", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part108", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part85", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part86", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part87", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part88", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part79", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part2", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part69", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part64", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part29", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part39", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part43", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part47", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part48", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part52", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part70", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part58", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part59", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part60", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part76", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part74", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part5", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part30", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part73", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part77", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part75", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part31", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part6", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "jian<PERSON>", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "toushi", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part16", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part3", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part68", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "eyeR", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "eyeL", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part19", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part17", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "toufa", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part25", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "cefaL", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "cefaL1_Skinning", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "cefaL2_Skinning", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "cefaL3_Skinning", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "cefaL4_Skinning", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "cefaL5_Skinning", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "cefaL6_Skinning", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part24", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "yinying2_Skinning", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part4", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "ma<PERSON><PERSON>", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "maoerL", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "maoerR", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part14", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part21", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part18", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part20", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part22", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part9", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part10", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "yinying", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "xiong", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part28", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part26", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part27", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part13", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "bozi", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part12", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "mao2", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "weiba2_Skinning", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "qunzi", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "mao1", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part7", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part8", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "<PERSON><PERSON>_Skinning", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "tui", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "shouL", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part34", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part80", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part33", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part35", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part32", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "chibang", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "deng", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part11", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "weiba3_Skinning", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "beijing", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "nangua", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "jiujiu", "Segments": [0, 1, 0, 27.333, 1]}, {"Target": "PartOpacity", "Id": "Part89", "Segments": [0, 1, 0, 27.333, 1]}], "UserData": [{"Time": 0.0, "Value": ""}, {"Time": 12.27, "Value": ""}, {"Time": 26.833, "Value": ""}]}