{"Version": 3, "Meta": {"Duration": 9.6, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 355, "TotalSegmentCount": 3538, "TotalPointCount": 4088, "UserDataCount": 3, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 2, 0.333, 0, 0, 0.6, -0.266, 0, 0.9, 0.994, 1, 0.922, 0.994, 0.945, 0.425, 0.967, 0, 1, 1.089, -2.337, 1.211, -3.3, 1.333, -3.3, 2, 1.467, -3.3, 2, 1.667, -3.3, 2, 1.933, -3.3, 2, 2.467, -3.3, 2, 2.867, -3.3, 2, 7.2, -3.3, 2, 7.667, -3.3, 0, 8.4, -5.497, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 2, 0.333, 0, 0, 0.6, -1.034, 1, 0.7, -1.034, 0.8, -0.276, 0.9, 2.568, 1, 0.944, 3.832, 0.989, 6.073, 1.033, 6.073, 1, 1.055, 6.073, 1.078, 6.521, 1.1, 4.764, 1, 1.233, -5.778, 1.367, -14.94, 1.5, -14.94, 0, 1.7, -9.616, 0, 2, -17.1, 1, 2.067, -17.1, 2.133, -16.548, 2.2, -14.94, 1, 2.289, -12.796, 2.378, -11.408, 2.467, -11.408, 0, 2.7, -15.976, 0, 2.933, -14.22, 0, 3.533, -17.615, 0, 4.033, -13.332, 0, 4.7, -17.615, 0, 5.1, -8.658, 0, 5.567, -17.1, 0, 5.9, -9.616, 0, 6.4, -15.949, 1, 6.544, -15.949, 6.689, -13.996, 6.833, -9.616, 1, 6.933, -6.584, 7.033, -4.804, 7.133, -4.804, 0, 7.6, -14.94, 0, 7.933, -11.408, 2, 8.033, -11.408, 0, 8.967, 4.764, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 2, 0.333, 0, 0, 0.6, -0.509, 1, 0.7, -0.509, 0.8, -0.014, 0.9, 1.246, 1, 0.922, 1.526, 0.945, 2.178, 0.967, 2.545, 1, 1.034, 3.648, 1.1, 4.121, 1.167, 4.121, 0, 1.333, 1.867, 2, 1.467, 1.867, 2, 1.667, 1.867, 2, 2.467, 1.867, 0, 3.167, -3.52, 2, 7.2, -3.52, 2, 7.667, -3.52, 0, 8.2, -3.736, 1, 8.344, -3.736, 8.489, -0.397, 8.633, 1.542, 1, 8.766, 3.332, 8.9, 3.38, 9.033, 3.38, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 2, 0.333, 1, 2, 0.767, 1, 0, 0.833, 0, 0, 0.933, 1, 2, 1.033, 1, 0, 1.267, 0.7, 2, 1.667, 0.7, 0, 1.867, 0, 0, 2.467, 0.7, 0, 2.8, 0.6, 2, 4.967, 0.6, 0, 5.067, 0, 0, 5.233, 0.6, 2, 7.133, 0.6, 2, 7.6, 0.6, 2, 8.067, 0.6, 2, 8.333, 0.6, 2, 8.567, 0.6, 0, 8.667, 0, 0, 8.867, 1, 2, 9.6, 1]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 2, 0.333, 1, 2, 0.767, 1, 0, 0.833, 0, 0, 0.933, 1, 2, 1.033, 1, 0, 1.267, 0.8, 2, 1.667, 0.8, 0, 1.867, 0, 0, 2.467, 0.8, 0, 2.8, 0.6, 2, 4.967, 0.6, 0, 5.067, 0, 0, 5.233, 0.6, 2, 7.133, 0.6, 2, 7.6, 0.6, 2, 8.067, 0.6, 2, 8.333, 0.6, 2, 8.567, 0.6, 0, 8.667, 0, 0, 8.867, 1, 2, 9.6, 1]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 2, 0.333, 0, 2, 0.9, 0, 0, 0.933, -0.5, 2, 1.267, -0.5, 2, 1.667, -0.5, 2, 1.867, -0.5, 2, 2.467, -0.5, 2, 2.8, -0.5, 2, 7.133, -0.5, 2, 7.6, -0.5, 2, 8.067, -0.5, 2, 8.333, -0.5, 2, 8.667, -0.5, 0, 8.733, 0, 2, 9.6, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 2, 0.333, 0, 2, 0.9, 0, 0, 0.933, -0.4, 2, 1.267, -0.4, 2, 1.667, -0.4, 2, 1.867, -0.4, 2, 2.467, -0.4, 2, 2.8, -0.4, 2, 7.133, -0.4, 2, 7.6, -0.4, 2, 8.067, -0.4, 2, 8.333, -0.4, 2, 8.667, -0.4, 0, 8.733, 0, 2, 9.6, 0]}, {"Target": "Parameter", "Id": "Param", "Segments": [0, -30, 0, 0.333, 0, 2, 0.9, 0, 2, 1.267, 0, 2, 1.667, 0, 2, 2.467, 0, 2, 2.8, 0, 2, 7.133, 0, 2, 7.6, 0, 2, 8.067, 0, 2, 8.333, 0, 0, 9.6, -30]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 1, 0, 0.333, 0, 2, 0.9, 0, 0, 1.267, -0.8, 2, 1.667, -0.8, 2, 2.467, -0.8, 0, 2.8, 0, 2, 7.133, 0, 2, 7.6, 0, 2, 8.067, 0, 2, 8.333, 0, 0, 9.6, 1]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 2, 0.333, 0, 1, 0.7, 0, 1.066, 0.039, 1.433, 0.174, 1, 1.5, 0.199, 1.566, 0.556, 1.633, 0.556, 0, 1.7, 0.137, 0, 1.767, 0.482, 0, 1.967, 0.301, 0, 2.033, 0.39, 0, 2.1, 0.363, 0, 2.233, 0.502, 0, 2.367, 0, 0, 2.5, 0.472, 0, 2.633, 0.274, 0, 2.7, 0.472, 0, 2.767, 0.417, 0, 2.833, 0.447, 2, 2.9, 0.447, 0, 2.967, 0.489, 0, 3.167, 0.053, 0, 3.233, 0.065, 0, 3.433, 0, 2, 3.7, 0, 0, 3.833, 0.482, 0, 3.9, 0.472, 0, 3.967, 0.524, 0, 4.033, 0, 0, 4.367, 0.509, 0, 4.433, 0.452, 0, 4.5, 0.537, 0, 4.767, 0.006, 0, 4.833, 0.112, 0, 5.167, 0, 2, 5.233, 0, 0, 5.5, 0.574, 0, 5.567, 0.306, 0, 5.633, 0.556, 0, 5.767, 0.011, 0, 5.9, 0.48, 0, 6.033, 0.013, 0, 6.1, 0.427, 0, 6.233, 0.234, 0, 6.3, 0.46, 0, 6.433, 0.021, 0, 6.5, 0.442, 0, 6.633, 0.018, 0, 6.7, 0.465, 0, 6.767, 0.182, 0, 6.833, 0.472, 0, 6.9, 0.415, 0, 6.967, 0.559, 0, 7.1, 0.209, 0, 7.3, 0.519, 0, 7.633, 0, 2, 8.667, 0, 2, 9.6, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 2, 0.333, 0, 1, 0.422, 0, 0.511, -0.139, 0.6, -1.5, 1, 0.667, -2.521, 0.733, -4.68, 0.8, -4.68, 0, 0.9, 5.34, 1, 0.944, 5.34, 0.989, 5.883, 1.033, 3.857, 1, 1.111, 0.312, 1.189, -23, 1.267, -23, 1, 1.4, -23, 1.534, -19.871, 1.667, -19.58, 1, 1.822, -19.24, 1.978, -19.287, 2.133, -19.287, 1, 2.255, -19.287, 2.378, -19.266, 2.5, -21.26, 1, 2.6, -22.891, 2.7, -30, 2.8, -30, 1, 3.067, -30, 3.333, -29.752, 3.6, -27.359, 1, 3.744, -26.063, 3.889, -20.838, 4.033, -20.838, 0, 4.7, -24.095, 0, 5.1, -21.26, 0, 5.567, -24.095, 0, 5.9, -19.884, 0, 6.4, -22.663, 0, 7.133, -18.78, 0, 7.6, -21.96, 2, 8.067, -21.96, 0, 8.333, -23.402, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 2, 0.333, 0, 0, 0.6, 0.7, 0, 0.8, -1.8, 0, 0.9, 4.68, 0, 1.267, -8.342, 0, 1.667, -1.082, 0, 1.867, -6.353, 0, 2.133, 3.875, 1, 2.244, 3.875, 2.356, 3.996, 2.467, 3.358, 1, 2.578, 2.72, 2.689, -8.084, 2.8, -8.084, 1, 3.111, -8.084, 3.422, -7.914, 3.733, -6.058, 1, 3.889, -5.13, 4.044, 0.231, 4.2, 0.231, 0, 4.867, -6.058, 0, 5.267, 0, 0, 5.733, -5.079, 0, 6.067, 1.226, 0, 6.567, -3.541, 0, 7.133, 1.92, 1, 7.289, 1.92, 7.444, -4.177, 7.6, -5.289, 1, 7.767, -6.481, 7.933, -6.353, 8.1, -6.353, 1, 8.178, -6.353, 8.255, -3.968, 8.333, -3.483, 1, 8.755, -0.853, 9.178, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 2, 0.333, 0, 0, 0.6, 0.234, 0, 0.8, -0.48, 1, 0.833, -0.48, 0.867, -0.273, 0.9, 0, 1, 1.022, 1.002, 1.145, 1.446, 1.267, 1.446, 0, 1.667, 0.486, 2, 2.467, 0.486, 0, 2.8, 0.787, 0, 3.6, -0.827, 0, 4.933, 0.266, 0, 5.633, -0.705, 0, 7.133, 1.207, 2, 7.6, 1.207, 2, 8.067, 1.207, 0, 8.333, -0.627, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param267", "Segments": [0, 0, 2, 0.333, 0, 0, 0.6, 0.534, 0, 0.8, -0.9, 1, 1.089, -0.9, 1.378, 1.539, 1.667, 4.328, 1, 1.878, 6.366, 2.089, 8.697, 2.3, 9.143, 1, 2.467, 9.495, 2.633, 9.415, 2.8, 9.697, 1, 3.067, 10.147, 3.333, 10.705, 3.6, 10.705, 0, 4.933, 9.725, 0, 5.633, 10.447, 0, 7.133, 10.237, 2, 7.6, 10.237, 2, 8.067, 10.237, 0, 8.4, 14.151, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param3", "Segments": [0, 0, 2, 0.333, 0, 0, 0.6, 0.978, 0, 0.8, -1.68, 1, 0.833, -1.68, 0.867, -0.843, 0.9, 0, 1, 1.022, 3.092, 1.145, 4.356, 1.267, 4.356, 0, 1.867, -9, 0, 2.467, 1.311, 1, 3.189, 1.311, 3.911, -7.782, 4.633, -8.4, 1, 5.466, -9.113, 6.3, -9, 7.133, -9, 2, 7.6, -9, 2, 8.067, -9, 0, 8.333, -11.219, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param4", "Segments": [0, 0, 2, 0.333, 0, 0, 0.6, 1.672, 0, 0.8, -4.86, 0, 1, 7.573, 1, 1.089, 7.573, 1.178, 2.644, 1.267, 0.281, 1, 1.356, -2.083, 1.444, -2.164, 1.533, -2.164, 0, 2, 4.241, 1, 2.144, 4.241, 2.289, -6.335, 2.433, -12.531, 1, 2.555, -17.774, 2.678, -22.426, 2.8, -23.34, 1, 3.033, -25.085, 3.267, -25.248, 3.5, -25.248, 0, 4.033, -23.34, 0, 4.7, -25, 0, 5.1, -22.891, 0, 5.567, -25, 0, 5.9, -22.891, 0, 6.4, -25, 0, 7.1, -22.891, 0, 7.433, -29.132, 1, 7.511, -29.132, 7.589, -27.203, 7.667, -25.955, 1, 7.8, -23.816, 7.934, -23.34, 8.067, -23.34, 0, 8.333, -27.606, 0, 9.133, 1.714, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param266", "Segments": [0, 0, 2, 0.333, 0, 0, 0.6, 0.486, 0, 0.8, -5.22, 0, 0.9, 0, 0, 1.267, -6.54, 2, 1.667, -6.54, 2, 1.867, -6.54, 2, 2.467, -6.54, 0, 2.8, -4.26, 0, 3.6, -5.736, 0, 4.933, -4.714, 0, 5.633, -7.182, 0, 7.133, -6, 2, 7.6, -6, 2, 8.067, -6, 0, 8.333, -7.619, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param5", "Segments": [0, 0, 2, 0.333, 0, 0, 0.767, -6.195, 0, 0.9, -1.215, 1, 0.967, -1.215, 1.033, -5.046, 1.1, -7.891, 1, 1.378, -19.744, 1.655, -24.354, 1.933, -24.354, 2, 2.5, -24.354, 0, 2.933, -30, 0, 3.3, -24.354, 0, 3.8, -25.266, 0, 4.433, -24.183, 0, 4.933, -25.708, 1, 5.166, -25.708, 5.4, -25.06, 5.633, -24.764, 1, 6.133, -24.129, 6.633, -24, 7.133, -24, 2, 7.6, -24, 2, 8.067, -24, 1, 8.156, -24, 8.244, -22.594, 8.333, -19.811, 1, 8.755, -6.594, 9.178, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param6", "Segments": [0, 0, 2, 0.333, 0, 1, 0.478, 0, 0.622, 2.225, 0.767, 5.993, 1, 0.811, 7.152, 0.856, 7.607, 0.9, 8.94, 1, 0.944, 10.273, 0.989, 12.257, 1.033, 12.257, 2, 1.267, 12.257, 2, 1.667, 12.257, 2, 2.467, 12.257, 2, 2.8, 12.257, 2, 7.133, 12.257, 2, 7.6, 12.257, 2, 8.067, 12.257, 2, 8.333, 12.257, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param201", "Segments": [0, 0, 2, 0.333, 0, 2, 0.9, 0, 2, 1.733, 0, 2, 2.467, 0, 2, 2.8, 0, 2, 7.133, 0, 2, 7.6, 0, 2, 8.067, 0, 2, 8.3, 0, 2, 8.467, 0, 2, 8.567, 0, 2, 9.6, 0]}, {"Target": "Parameter", "Id": "Param202", "Segments": [0, 0, 2, 0.333, 0, 2, 0.9, 0, 2, 1, 1, 2, 1.733, 1, 2, 2.467, 1, 2, 2.8, 1, 2, 7.133, 1, 2, 7.6, 1, 2, 8.067, 1, 2, 8.3, 1, 2, 8.467, 1, 2, 8.567, 0, 2, 9.6, 0]}, {"Target": "Parameter", "Id": "Param205", "Segments": [0, 0, 2, 0.333, 0, 2, 0.9, 0, 2, 1, 1, 2, 1.733, 1, 2, 2.467, 1, 2, 2.8, 1, 2, 7.133, 1, 2, 7.6, 1, 2, 8.067, 1, 2, 8.3, 1, 2, 8.467, 1, 2, 8.567, 0, 2, 9.6, 0]}, {"Target": "Parameter", "Id": "Param203", "Segments": [0, 0, 2, 0.333, 0, 2, 0.9, 0, 2, 1.733, 0, 2, 2.467, 0, 2, 2.8, 0, 2, 7.133, 0, 2, 7.6, 0, 2, 8.067, 0, 2, 8.3, 0, 2, 8.467, 0, 2, 8.567, 0, 2, 9.6, 0]}, {"Target": "Parameter", "Id": "Param204", "Segments": [0, 0, 2, 0.333, 0, 2, 0.9, 0, 2, 1.733, 0, 2, 2.467, 0, 2, 2.8, 0, 2, 7.133, 0, 2, 7.6, 0, 2, 8.067, 0, 2, 8.3, 0, 2, 8.467, 0, 2, 8.567, 0, 2, 9.6, 0]}, {"Target": "Parameter", "Id": "Param209", "Segments": [0, 0, 2, 0.333, 0, 2, 0.9, 0, 2, 1.733, 0, 2, 2.467, 0, 2, 2.8, 0, 2, 7.133, 0, 2, 7.6, 0, 2, 8.067, 0, 2, 8.3, 0, 2, 8.467, 0, 2, 8.567, 0, 2, 9.6, 0]}, {"Target": "Parameter", "Id": "Param21", "Segments": [0, 0, 2, 0.333, 0, 2, 0.9, 0, 2, 1.733, 0, 2, 2.467, 0, 2, 2.8, 0, 2, 7.133, 0, 2, 7.6, 0, 2, 8.067, 0, 2, 8.3, 0, 2, 8.467, 0, 2, 8.567, 0, 2, 9.6, 0]}, {"Target": "Parameter", "Id": "Param213", "Segments": [0, 0, 2, 0.333, 0, 2, 0.9, 0, 2, 1.733, 0, 2, 2.467, 0, 2, 2.8, 0, 2, 7.133, 0, 2, 7.6, 0, 2, 8.067, 0, 2, 8.3, 0, 2, 8.467, 0, 2, 8.567, 0, 2, 9.6, 0]}, {"Target": "Parameter", "Id": "Param214", "Segments": [0, 0, 2, 0.333, 0, 2, 0.9, 0, 2, 1.733, 0, 2, 2.467, 0, 2, 2.8, 0, 2, 7.133, 0, 2, 7.6, 0, 2, 8.067, 0, 2, 8.3, 0, 2, 8.467, 0, 2, 8.567, 0, 2, 9.6, 0]}, {"Target": "Parameter", "Id": "Param215", "Segments": [0, 0, 2, 0.333, 0, 2, 0.9, 0, 2, 1.733, 0, 2, 2.467, 0, 2, 2.8, 0, 2, 7.133, 0, 2, 7.6, 0, 2, 8.067, 0, 2, 8.3, 0, 2, 8.467, 0, 2, 8.567, 0, 2, 9.6, 0]}, {"Target": "Parameter", "Id": "Param216", "Segments": [0, 0, 2, 0.333, 0, 2, 0.9, 0, 2, 1.733, 0, 2, 2.467, 0, 2, 2.8, 0, 2, 7.133, 0, 2, 7.6, 0, 2, 8.067, 0, 2, 8.3, 0, 2, 8.467, 0, 2, 8.567, 0, 2, 9.6, 0]}, {"Target": "Parameter", "Id": "Param217", "Segments": [0, 0, 2, 0.333, 0, 2, 0.9, 0, 2, 1.733, 0, 2, 2.467, 0, 2, 2.8, 0, 2, 7.133, 0, 2, 7.6, 0, 2, 8.067, 0, 2, 8.3, 0, 2, 8.467, 0, 2, 8.567, 0, 2, 9.6, 0]}, {"Target": "Parameter", "Id": "Param218", "Segments": [0, 0, 2, 0.333, 0, 2, 0.9, 0, 2, 1.733, 0, 2, 2.467, 0, 2, 2.8, 0, 2, 7.133, 0, 2, 7.6, 0, 2, 8.067, 0, 2, 8.3, 0, 2, 8.467, 0, 2, 8.567, 0, 2, 9.6, 0]}, {"Target": "Parameter", "Id": "Param219", "Segments": [0, 0, 2, 0.333, 0, 2, 0.9, 0, 2, 1.733, 0, 2, 2.467, 0, 2, 2.8, 0, 2, 7.133, 0, 2, 7.6, 0, 2, 8.067, 0, 2, 8.3, 0, 2, 8.467, 0, 2, 8.567, 0, 2, 9.6, 0]}, {"Target": "Parameter", "Id": "Param220", "Segments": [0, 0, 2, 0.333, 0, 2, 0.9, 0, 2, 1.733, 0, 2, 2.467, 0, 2, 2.8, 0, 2, 7.133, 0, 2, 7.6, 0, 2, 8.067, 0, 2, 8.3, 0, 2, 8.467, 0, 2, 8.567, 0, 2, 9.6, 0]}, {"Target": "Parameter", "Id": "Param9", "Segments": [0, 0, 2, 0.333, 0, 0, 0.733, 0.6, 1, 0.944, 0.6, 1.156, -4.368, 1.367, -7.417, 1, 1.434, -8.379, 1.5, -8.055, 1.567, -8.055, 0, 2.8, -5.52, 0, 3.3, -6, 0, 3.8, -5.635, 0, 4.433, -6.032, 0, 4.933, -5.783, 0, 5.633, -6.54, 0, 6.667, -5.52, 2, 7.6, -5.52, 2, 8.067, -5.52, 0, 8.3, -6.759, 1, 8.478, -6.759, 8.655, -4.462, 8.833, -2.82, 1, 9.089, -0.46, 9.344, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param10", "Segments": [0, 0, 2, 0.333, 0, 1, 0.466, 0, 0.6, 0.16, 0.733, -0.36, 1, 0.933, -1.14, 1.133, -6.06, 1.333, -6.06, 1, 1.822, -6.06, 2.311, -5.476, 2.8, -4.058, 1, 2.967, -3.574, 3.133, -3.171, 3.3, -3.171, 0, 3.8, -4.02, 0, 4.433, -3.171, 0, 4.933, -4.02, 0, 5.633, -2.511, 0, 7.133, -3.541, 1, 7.289, -3.541, 7.444, -3.445, 7.6, -3.278, 1, 7.756, -3.11, 7.911, -3.038, 8.067, -3.038, 0, 8.3, -4.77, 0, 8.833, 2.88, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param11", "Segments": [0, 0, 2, 0.333, 0, 2, 0.733, 0, 0, 0.9, 21.36, 0, 1.333, 0, 2, 1.733, 0, 2, 2.8, 0, 2, 7.133, 0, 2, 7.6, 0, 2, 8.067, 0, 1, 8.145, 0, 8.222, -0.879, 8.3, -1.236, 1, 8.478, -2.053, 8.655, -2.22, 8.833, -2.22, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param12", "Segments": [0, 0, 2, 0.333, 0, 2, 0.733, 0, 2, 1.333, 0, 2, 1.733, 0, 2, 2.8, 0, 2, 7.133, 0, 2, 7.6, 0, 2, 8.067, 0, 2, 8.3, 0, 0, 9, 19.8, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param197", "Segments": [0, 0, 2, 0.333, 0, 2, 0.733, 0, 0, 1.333, 22.44, 0, 1.733, 0, 2, 2.8, 0, 0, 3.3, 7.804, 0, 3.8, 0.688, 0, 4.433, 7.804, 0, 5.2, 0, 0, 5.967, 7.804, 0, 7.133, 0, 1, 7.289, 0, 7.444, 1.727, 7.6, 5.319, 1, 7.756, 8.911, 7.911, 10.779, 8.067, 10.779, 1, 8.145, 10.779, 8.222, 4.352, 8.3, 3.159, 1, 8.478, 0.433, 8.655, 0, 8.833, 0, 2, 9.6, 0]}, {"Target": "Parameter", "Id": "Param198", "Segments": [0, 0, 2, 0.333, 0, 2, 0.733, 0, 2, 1.333, 0, 2, 1.733, 0, 2, 2.8, 0, 0, 3.3, -12.12, 0, 3.8, -1.356, 0, 4.433, -15.001, 0, 5.2, -4.86, 0, 5.967, -11.626, 0, 7.133, 0, 2, 7.6, 0, 2, 8.067, 0, 2, 8.3, 0, 2, 8.833, 0, 2, 9.6, 0]}, {"Target": "Parameter", "Id": "Param22", "Segments": [0, 0, 2, 0.333, 0, 0, 0.8, 1, 2, 0.967, 1, 2, 1.733, 1, 2, 2.8, 1, 2, 8.3, 1, 2, 8.833, 1, 2, 9.4, 1, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param13", "Segments": [0, 0, 2, 0.333, 0, 2, 0.9, 0, 2, 1.733, 0, 0, 2.8, 1.82, 2, 7.133, 1.82, 2, 8.067, 1.82, 0, 8.833, 0, 2, 9.6, 0]}, {"Target": "Parameter", "Id": "Param14", "Segments": [0, 0, 2, 0.333, 0, 2, 0.9, 0, 2, 1.733, 0, 0, 2.8, -27.14, 2, 7.133, -27.14, 2, 7.6, -27.14, 2, 8.3, -27.14, 0, 8.833, 0, 2, 9.6, 0]}, {"Target": "Parameter", "Id": "Param221", "Segments": [0, 0, 2, 0.333, 0, 2, 0.9, 0, 2, 1.033, 0, 2, 1.067, 1, 2, 1.433, 1, 2, 2.467, 1, 2, 2.8, 1, 2, 7.133, 1, 2, 7.6, 1, 2, 8.067, 1, 2, 8.333, 1, 2, 8.733, 1, 2, 8.767, 0, 2, 9.6, 0]}, {"Target": "Parameter", "Id": "Param230", "Segments": [0, 0, 2, 0.333, 0, 2, 0.9, 0, 2, 1.033, 0, 2, 1.067, 0, 2, 1.433, 0, 2, 2.467, 0, 2, 2.8, 0, 2, 7.133, 0, 2, 7.6, 0, 2, 8.067, 0, 2, 8.333, 0, 2, 8.733, 0, 2, 8.767, 0, 2, 9.6, 0]}, {"Target": "Parameter", "Id": "Param231", "Segments": [0, 0, 2, 0.333, 0, 2, 0.9, 0, 2, 1.033, 0, 2, 1.067, 0, 2, 1.433, 0, 2, 2.467, 0, 2, 2.8, 0, 2, 7.133, 0, 2, 7.6, 0, 2, 8.067, 0, 2, 8.333, 0, 2, 8.733, 0, 2, 8.767, 0, 2, 9.6, 0]}, {"Target": "Parameter", "Id": "Param222", "Segments": [0, 0, 2, 0.333, 0, 2, 0.9, 0, 2, 1.033, 0, 2, 1.067, 0, 2, 1.433, 0, 2, 2.467, 0, 2, 2.8, 0, 2, 7.133, 0, 2, 7.6, 0, 2, 8.067, 0, 2, 8.333, 0, 2, 8.733, 0, 2, 8.767, 0, 2, 9.6, 0]}, {"Target": "Parameter", "Id": "Param223", "Segments": [0, 0, 2, 0.333, 0, 2, 0.9, 0, 2, 1.033, 0, 2, 1.067, 1, 2, 1.433, 1, 2, 2.467, 1, 2, 2.8, 1, 2, 7.133, 1, 2, 7.6, 1, 2, 8.067, 1, 2, 8.333, 1, 2, 8.733, 1, 2, 8.767, 0, 2, 9.6, 0]}, {"Target": "Parameter", "Id": "Param207", "Segments": [0, 0, 2, 0.333, 0, 2, 0.9, 0, 2, 1.033, 0, 2, 1.067, 0, 2, 1.433, 0, 2, 2.467, 0, 2, 2.8, 0, 2, 7.133, 0, 2, 7.6, 0, 2, 8.067, 0, 2, 8.333, 0, 2, 8.733, 0, 2, 8.767, 0, 2, 9.6, 0]}, {"Target": "Parameter", "Id": "Param236", "Segments": [0, 0, 2, 0.333, 0, 2, 0.9, 0, 2, 1.033, 0, 2, 1.067, 1, 2, 1.433, 1, 2, 2.467, 1, 2, 2.8, 1, 2, 7.133, 1, 2, 7.6, 1, 2, 8.067, 1, 2, 8.333, 1, 2, 8.733, 1, 2, 8.767, 0, 2, 9.6, 0]}, {"Target": "Parameter", "Id": "Param237", "Segments": [0, 0, 2, 0.333, 0, 2, 0.9, 0, 2, 1.033, 0, 2, 1.067, 0, 2, 1.433, 0, 2, 2.467, 0, 2, 2.8, 0, 2, 7.133, 0, 2, 7.6, 0, 2, 8.067, 0, 2, 8.333, 0, 2, 8.733, 0, 2, 8.767, 0, 2, 9.6, 0]}, {"Target": "Parameter", "Id": "Param238", "Segments": [0, 0, 2, 0.333, 0, 2, 0.9, 0, 2, 1.033, 0, 2, 1.067, 0, 2, 1.433, 0, 2, 2.467, 0, 2, 2.8, 0, 2, 7.133, 0, 2, 7.6, 0, 2, 8.067, 0, 2, 8.333, 0, 2, 8.733, 0, 2, 8.767, 0, 2, 9.6, 0]}, {"Target": "Parameter", "Id": "Param239", "Segments": [0, 0, 2, 0.333, 0, 2, 0.9, 0, 2, 1.033, 0, 2, 1.067, 0, 2, 1.433, 0, 2, 2.467, 0, 2, 2.8, 0, 2, 7.133, 0, 2, 7.6, 0, 2, 8.067, 0, 2, 8.333, 0, 2, 8.733, 0, 2, 8.767, 0, 2, 9.6, 0]}, {"Target": "Parameter", "Id": "Param240", "Segments": [0, 0, 2, 0.333, 0, 2, 0.9, 0, 2, 1.033, 0, 2, 1.067, 0, 2, 1.433, 0, 2, 2.467, 0, 2, 2.8, 0, 2, 7.133, 0, 2, 7.6, 0, 2, 8.067, 0, 2, 8.333, 0, 2, 8.733, 0, 2, 8.767, 0, 2, 9.6, 0]}, {"Target": "Parameter", "Id": "Param241", "Segments": [0, 0, 2, 0.333, 0, 2, 0.9, 0, 2, 1.033, 0, 2, 1.067, 0, 2, 1.433, 0, 2, 2.467, 0, 2, 2.8, 0, 2, 7.133, 0, 2, 7.6, 0, 2, 8.067, 0, 2, 8.333, 0, 2, 8.733, 0, 2, 8.767, 0, 2, 9.6, 0]}, {"Target": "Parameter", "Id": "Param242", "Segments": [0, 0, 2, 0.333, 0, 2, 0.9, 0, 2, 1.033, 0, 2, 1.067, 0, 2, 1.433, 0, 2, 2.467, 0, 2, 2.8, 0, 2, 7.133, 0, 2, 7.6, 0, 2, 8.067, 0, 2, 8.333, 0, 2, 8.733, 0, 2, 8.767, 0, 2, 9.6, 0]}, {"Target": "Parameter", "Id": "Param243", "Segments": [0, 0, 2, 0.333, 0, 2, 0.9, 0, 2, 1.033, 0, 2, 1.067, 0, 2, 1.433, 0, 2, 2.467, 0, 2, 2.8, 0, 2, 7.133, 0, 2, 7.6, 0, 2, 8.067, 0, 2, 8.333, 0, 2, 8.733, 0, 2, 8.767, 0, 2, 9.6, 0]}, {"Target": "Parameter", "Id": "Param244", "Segments": [0, 0, 2, 0.333, 0, 2, 0.9, 0, 2, 1.033, 0, 2, 1.067, 0, 2, 1.433, 0, 2, 2.467, 0, 2, 2.8, 0, 2, 7.133, 0, 2, 7.6, 0, 2, 8.067, 0, 2, 8.333, 0, 2, 8.733, 0, 2, 8.767, 0, 2, 9.6, 0]}, {"Target": "Parameter", "Id": "Param15", "Segments": [0, 0, 2, 0.333, 0, 0, 0.633, -1.44, 0, 0.933, 6.274, 1, 0.989, 6.274, 1.044, 6.336, 1.1, 6.12, 1, 1.222, 5.645, 1.345, 4.68, 1.467, 4.68, 0, 2.033, 7.02, 0, 2.467, 6.384, 2, 2.8, 6.384, 0, 3.6, 8.652, 0, 4.633, 7.928, 0, 5.5, 9.161, 1, 5.767, 9.161, 6.033, 8.95, 6.3, 8.483, 1, 6.4, 8.308, 6.5, 8.194, 6.6, 8.194, 0, 7.133, 12.06, 1, 7.289, 12.06, 7.444, 11.117, 7.6, 10.74, 1, 7.756, 10.363, 7.911, 10.38, 8.067, 10.38, 0, 8.333, 10.92, 1, 8.5, 10.92, 8.666, 6.672, 8.833, 4.282, 1, 9.089, 0.618, 9.344, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param16", "Segments": [0, 0, 2, 0.333, 0, 1, 0.433, 0, 0.533, 0.055, 0.633, 1.2, 1, 0.722, 2.218, 0.811, 4.962, 0.9, 7.429, 1, 0.967, 9.279, 1.033, 12.751, 1.1, 15, 1, 1.144, 16.499, 1.189, 16.944, 1.233, 16.944, 0, 1.467, -30, 1, 1.656, -30, 1.844, -29.947, 2.033, -29, 1, 2.178, -28.276, 2.322, -26.54, 2.467, -26.54, 2, 2.8, -26.54, 2, 7.133, -26.54, 2, 7.6, -26.54, 2, 8.067, -26.54, 2, 8.333, -26.54, 0, 8.567, 12, 1, 8.667, 12, 8.767, 0.47, 8.867, -0.503, 1, 8.989, -1.692, 9.111, -1.589, 9.233, -1.589, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param17", "Segments": [0, 0, 2, 0.333, 0, 0, 0.633, -3.06, 1, 0.722, -3.06, 0.811, -3.034, 0.9, 0, 1, 0.967, 2.275, 1.033, 19.74, 1.1, 19.74, 0, 1.467, 0, 2, 2.467, 0, 2, 2.8, 0, 2, 7.133, 0, 2, 7.6, 0, 2, 8.067, 0, 2, 8.333, 0, 0, 8.8, -2.75, 0, 9.3, 2.689, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param19", "Segments": [0, 0, 2, 0.333, 0, 2, 0.9, 0, 2, 1.467, 0, 2, 2.467, 0, 2, 2.8, 0, 0, 7.133, -21, 2, 7.6, -21, 2, 8.067, -21, 2, 8.333, -21, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param20", "Segments": [0, 0, 2, 0.333, 0, 2, 0.9, 0, 0, 1.233, 12.04, 0, 1.467, 0, 2, 2.467, 0, 2, 2.8, 0, 2, 7.133, 0, 2, 7.6, 0, 2, 8.067, 0, 2, 8.333, 0, 2, 9.6, 0]}, {"Target": "Parameter", "Id": "Param224", "Segments": [0, 0, 2, 0.333, 0, 1, 0.522, 0, 0.711, 14.091, 0.9, 21.06, 1, 1, 24.749, 1.1, 24.15, 1.2, 24.15, 1, 1.211, 24.15, 1.222, -27.84, 1.233, -27.84, 2, 1.367, -27.84, 2, 2, -27.84, 2, 2.467, -27.84, 0, 2.767, -30, 0, 2.8, -27.84, 2, 7.133, -27.84, 2, 7.6, -27.84, 2, 8.067, -27.84, 2, 8.333, -27.84, 0, 8.933, 28.6, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param225", "Segments": [0, 0, 2, 0.333, 0, 1, 0.522, 0, 0.711, 23.012, 0.9, 28.08, 1, 1, 30.763, 1.1, 30, 1.2, 30, 1, 1.211, 30, 1.222, 0, 1.233, 0, 2, 1.367, 0, 2, 2, 0, 2, 2.467, 0, 2, 2.8, 0, 2, 7.133, 0, 2, 7.6, 0, 2, 8.067, 0, 2, 8.333, 0, 2, 9.6, 0]}, {"Target": "Parameter", "Id": "Param226", "Segments": [0, 0, 2, 0.333, 0, 2, 0.9, 0, 0, 1.233, -6.9, 2, 1.367, -6.9, 2, 2, -6.9, 2, 2.467, -6.9, 2, 2.8, -6.9, 2, 7.133, -6.9, 2, 7.6, -6.9, 2, 8.067, -6.9, 2, 8.333, -6.9, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param233", "Segments": [0, 0, 2, 0.333, 0, 0, 0.9, 9, 1, 0.933, 9, 0.967, 4.737, 1, 4.071, 1, 1.156, 0.962, 1.311, 0, 1.467, 0, 2, 2.033, 0, 2, 2.467, 0, 2, 2.8, 0, 2, 3.1, 0, 2, 7.133, 0, 2, 7.633, 0, 2, 8.367, 0, 1, 8.556, 0, 8.744, 5.735, 8.933, 18.857, 1, 8.978, 21.945, 9.022, 24.6, 9.067, 24.6, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param234", "Segments": [0, 0, 2, 0.333, 0, 2, 0.9, 0, 2, 1.233, 0, 0, 1.467, -16.05, 0, 2.033, -12.375, 0, 2.467, -20.175, 2, 2.8, -20.175, 2, 3.1, -20.175, 0, 3.6, -17.55, 0, 4.633, -19.967, 0, 5.5, -17.41, 0, 6.6, -20.683, 0, 7.133, -16.8, 1, 7.289, -16.8, 7.444, -18.378, 7.6, -20.175, 1, 7.756, -21.972, 7.911, -22.425, 8.067, -22.425, 0, 8.333, -20.175, 0, 8.8, -26.928, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param285", "Segments": [0, 0, 2, 0.333, 0, 0, 0.9, 30, 2, 1.133, 30, 0, 1.233, 0, 2, 1.467, 0, 2, 2.033, 0, 2, 2.467, 0, 2, 2.8, 0, 0, 3.6, -19.302, 0, 4.633, -3.257, 0, 5.5, -19.302, 0, 6.6, -0.044, 0, 7.133, -13.574, 0, 7.6, 0, 2, 8.067, 0, 2, 8.333, 0, 2, 9.6, 0]}, {"Target": "Parameter", "Id": "Param235", "Segments": [0, 0, 2, 0.333, 0, 2, 0.9, 0, 2, 1.233, 0, 0, 1.467, -11, 2, 2.033, -11, 2, 2.467, -11, 2, 2.8, -11, 2, 7.133, -11, 2, 7.6, -11, 2, 8.067, -11, 2, 8.333, -11, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param277", "Segments": [0, 0, 2, 0.333, 0, 0, 0.6, 12.46, 0, 1.133, -9.549, 0, 1.567, 12.46, 0, 1.967, -9.549, 0, 2.4, 12.46, 0, 2.8, -9.549, 0, 3.233, 12.46, 0, 3.633, -9.549, 0, 4.067, 12.46, 0, 4.467, -9.549, 0, 4.9, 12.46, 0, 5.3, -9.549, 0, 5.733, 12.46, 0, 6.133, -9.549, 0, 6.567, 12.46, 0, 6.967, -9.549, 0, 7.4, 12.46, 0, 7.8, -9.549, 0, 8.233, 12.46, 0, 8.633, -9.549, 1, 8.778, -9.549, 8.922, -7.835, 9.067, 0, 1, 9.156, 4.822, 9.244, 12.46, 9.333, 12.46, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param278", "Segments": [0, 0, 2, 0.333, 0, 0, 0.567, -0.266, 0, 0.9, 0.27, 0, 1.367, -0.266, 0, 1.9, 0.27, 0, 2.467, -0.161, 0, 2.833, 0.27, 0, 3.467, -0.266, 0, 4.167, 0.27, 0, 4.7, -0.266, 0, 5.333, 0.27, 0, 6.3, -0.266, 0, 7.133, 0.27, 0, 7.6, -0.266, 0, 8.067, 0.27, 0, 8.333, -0.161, 0, 8.967, 0.27, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param280", "Segments": [0, 0, 2, 0.333, 0, 0, 0.567, -0.266, 0, 0.9, 0.27, 0, 1.367, -0.266, 0, 1.9, 0.27, 0, 2.467, -0.161, 0, 2.833, 0.27, 0, 3.467, -0.266, 0, 4.167, 0.27, 0, 4.7, -0.266, 0, 5.333, 0.27, 0, 6.3, -0.266, 0, 7.133, 0.27, 0, 7.6, -0.266, 0, 8.067, 0.27, 0, 8.333, -0.161, 0, 8.967, 0.27, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param7", "Segments": [0, 0, 2, 0.333, 0, 0, 1.433, -9, 0, 2.533, 0, 0, 3.633, -9, 0, 4.733, 0, 0, 5.867, -9, 0, 6.967, 0, 0, 8.067, -9, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param66", "Segments": [0, 0, 2, 0.333, 0, 0, 1.433, -19, 0, 2.533, 0, 0, 3.633, -19, 0, 4.733, 0, 0, 5.867, -19, 0, 6.967, 0, 0, 8.067, -19, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param67", "Segments": [0, 0, 2, 0.333, 0, 0, 0.867, 16, 0, 1.967, -16, 1, 2.156, -16, 2.344, -10.667, 2.533, 0, 1, 2.722, 10.667, 2.911, 16, 3.1, 16, 0, 4.2, -16, 1, 4.378, -16, 4.555, -10.332, 4.733, 0, 1, 4.922, 10.978, 5.111, 16, 5.3, 16, 0, 6.4, -16, 1, 6.589, -16, 6.778, -10.978, 6.967, 0, 1, 7.145, 10.332, 7.322, 16, 7.5, 16, 0, 8.733, -16, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param68", "Segments": [0, 0, 2, 0.333, 0, 0, 1.433, 17, 0, 2.533, 0, 0, 3.633, 17, 0, 4.733, 0, 0, 5.867, 17, 0, 6.967, 0, 0, 8.067, 17, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param69", "Segments": [0, 0, 2, 0.333, 0, 0, 0.867, -16, 0, 1.967, 16, 1, 2.156, 16, 2.344, 10.667, 2.533, 0, 1, 2.722, -10.667, 2.911, -16, 3.1, -16, 0, 4.2, 16, 1, 4.378, 16, 4.555, 10.332, 4.733, 0, 1, 4.922, -10.978, 5.111, -16, 5.3, -16, 0, 6.4, 16, 1, 6.589, 16, 6.778, 10.978, 6.967, 0, 1, 7.145, -10.332, 7.322, -16, 7.5, -16, 0, 8.733, 16, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param35", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.567, 3.223, 0, 0.9, -8.402, 0, 1.2, 12.318, 0, 1.6, -10.826, 0, 1.933, 4.987, 0, 2.133, 0.215, 0, 2.367, 2.138, 0, 2.833, -1.762, 0, 3.2, 3.515, 0, 3.733, -3.711, 0, 4.467, 5.457, 0, 5.033, -5.505, 0, 5.533, 5.023, 0, 6, -2.891, 0, 6.333, 2.432, 0, 6.767, -3.74, 0, 7.433, 6.479, 0, 7.867, -7.53, 0, 8.3, 8.461, 0, 8.733, -7.521, 0, 9.367, 5.55, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param36", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.533, -2.128, 0, 0.8, 5.422, 0, 1.133, -13.094, 0, 1.467, 16.066, 0, 1.833, -15.302, 0, 2.133, 8.773, 0, 2.467, -1.373, 0, 2.733, -0.096, 0, 3.033, -2.313, 0, 3.433, 3.345, 0, 3.867, -2.444, 0, 4.167, -0.796, 0, 4.333, -1.331, 0, 4.733, 4.212, 0, 5.267, -4.168, 0, 5.733, 4.693, 0, 6.2, -5.07, 0, 6.567, 4.654, 0, 7, -3.646, 0, 7.7, 6.419, 0, 8.1, -8.315, 0, 8.567, 12.435, 0, 9.133, -10.993, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param37", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.533, 9.342, 0, 0.8, -19.438, 0, 1.1, 21.327, 0, 1.5, -12.859, 0, 2.067, 6.835, 0, 2.7, -10.529, 0, 3.033, 11.417, 0, 3.7, -5.045, 0, 4.433, 7.736, 0, 4.867, -7.856, 0, 5.467, 4.139, 0, 5.833, 1.653, 0, 5.867, 1.675, 0, 6.567, -3.782, 0, 7.4, 9.139, 0, 7.8, -12.156, 0, 8.267, 17.325, 0, 8.633, -17.344, 0, 9.167, 9.237, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param39", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, -5.207, 0, 0.733, 14.251, 0, 1, -23.378, 0, 1.267, 22.011, 0, 1.6, -12.663, 0, 1.933, 3.91, 0, 2.167, 1.023, 0, 2.3, 1.189, 0, 2.5, 0.571, 0, 2.633, 2.224, 0, 2.9, -9.815, 0, 3.167, 9.884, 0, 3.467, -4.188, 0, 3.733, 0.736, 0, 4, -1.231, 0, 4.2, -0.605, 0, 4.333, -1.232, 0, 4.633, 4.233, 0, 5, -4.446, 0, 5.3, 1.161, 0, 5.467, 0.282, 0, 5.633, 1.156, 0, 5.9, -0.763, 0, 6.233, 0.906, 0, 6.733, -1.075, 0, 7.033, 0.026, 0, 7.333, -2.692, 0, 7.6, 6.723, 0, 7.967, -8.954, 0, 8.433, 17.69, 0, 8.833, -17.326, 0, 9.367, 10.622, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param38", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.533, 9.342, 0, 0.8, -19.438, 0, 1.1, 21.327, 0, 1.5, -12.859, 0, 2.067, 6.835, 0, 2.7, -10.529, 0, 3.033, 11.417, 0, 3.7, -5.045, 0, 4.433, 7.736, 0, 4.867, -7.856, 0, 5.467, 4.139, 0, 5.833, 1.653, 0, 5.867, 1.675, 0, 6.567, -3.782, 0, 7.4, 9.139, 0, 7.8, -12.156, 0, 8.267, 17.325, 0, 8.633, -17.344, 0, 9.167, 9.237, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param40", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, -5.207, 0, 0.733, 14.251, 0, 1, -23.378, 0, 1.267, 22.011, 0, 1.6, -12.663, 0, 1.933, 3.91, 0, 2.167, 1.023, 0, 2.3, 1.189, 0, 2.5, 0.571, 0, 2.633, 2.224, 0, 2.9, -9.815, 0, 3.167, 9.884, 0, 3.467, -4.188, 0, 3.733, 0.736, 0, 4, -1.231, 0, 4.2, -0.605, 0, 4.333, -1.232, 0, 4.633, 4.233, 0, 5, -4.446, 0, 5.3, 1.161, 0, 5.467, 0.282, 0, 5.633, 1.156, 0, 5.9, -0.763, 0, 6.233, 0.906, 0, 6.733, -1.075, 0, 7.033, 0.026, 0, 7.333, -2.692, 0, 7.6, 6.723, 0, 7.967, -8.954, 0, 8.433, 17.69, 0, 8.833, -17.326, 0, 9.367, 10.622, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param41", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.533, 9.342, 0, 0.8, -19.438, 0, 1.1, 21.327, 0, 1.5, -12.859, 0, 2.067, 6.835, 0, 2.7, -10.529, 0, 3.033, 11.417, 0, 3.7, -5.045, 0, 4.433, 7.736, 0, 4.867, -7.856, 0, 5.467, 4.139, 0, 5.833, 1.653, 0, 5.867, 1.675, 0, 6.567, -3.782, 0, 7.4, 9.139, 0, 7.8, -12.156, 0, 8.267, 17.325, 0, 8.633, -17.344, 0, 9.167, 9.237, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param42", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, -5.207, 0, 0.733, 14.251, 0, 1, -23.378, 0, 1.267, 22.011, 0, 1.6, -12.663, 0, 1.933, 3.91, 0, 2.167, 1.023, 0, 2.3, 1.189, 0, 2.5, 0.571, 0, 2.633, 2.224, 0, 2.9, -9.815, 0, 3.167, 9.884, 0, 3.467, -4.188, 0, 3.733, 0.736, 0, 4, -1.231, 0, 4.2, -0.605, 0, 4.333, -1.232, 0, 4.633, 4.233, 0, 5, -4.446, 0, 5.3, 1.161, 0, 5.467, 0.282, 0, 5.633, 1.156, 0, 5.9, -0.763, 0, 6.233, 0.906, 0, 6.733, -1.075, 0, 7.033, 0.026, 0, 7.333, -2.692, 0, 7.6, 6.723, 0, 7.967, -8.954, 0, 8.433, 17.69, 0, 8.833, -17.326, 0, 9.367, 10.622, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param43", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.533, 9.342, 0, 0.8, -19.438, 0, 1.1, 21.327, 0, 1.5, -12.859, 0, 2.067, 6.835, 0, 2.7, -10.529, 0, 3.033, 11.417, 0, 3.7, -5.045, 0, 4.433, 7.736, 0, 4.867, -7.856, 0, 5.467, 4.139, 0, 5.833, 1.653, 0, 5.867, 1.675, 0, 6.567, -3.782, 0, 7.4, 9.139, 0, 7.8, -12.156, 0, 8.267, 17.325, 0, 8.633, -17.344, 0, 9.167, 9.237, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param44", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, -5.207, 0, 0.733, 14.251, 0, 1, -23.378, 0, 1.267, 22.011, 0, 1.6, -12.663, 0, 1.933, 3.91, 0, 2.167, 1.023, 0, 2.3, 1.189, 0, 2.5, 0.571, 0, 2.633, 2.224, 0, 2.9, -9.815, 0, 3.167, 9.884, 0, 3.467, -4.188, 0, 3.733, 0.736, 0, 4, -1.231, 0, 4.2, -0.605, 0, 4.333, -1.232, 0, 4.633, 4.233, 0, 5, -4.446, 0, 5.3, 1.161, 0, 5.467, 0.282, 0, 5.633, 1.156, 0, 5.9, -0.763, 0, 6.233, 0.906, 0, 6.733, -1.075, 0, 7.033, 0.026, 0, 7.333, -2.692, 0, 7.6, 6.723, 0, 7.967, -8.954, 0, 8.433, 17.69, 0, 8.833, -17.326, 0, 9.367, 10.622, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param45", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.533, 9.342, 0, 0.8, -19.438, 0, 1.1, 21.327, 0, 1.5, -12.859, 0, 2.067, 6.835, 0, 2.7, -10.529, 0, 3.033, 11.417, 0, 3.7, -5.045, 0, 4.433, 7.736, 0, 4.867, -7.856, 0, 5.467, 4.139, 0, 5.833, 1.653, 0, 5.867, 1.675, 0, 6.567, -3.782, 0, 7.4, 9.139, 0, 7.8, -12.156, 0, 8.267, 17.325, 0, 8.633, -17.344, 0, 9.167, 9.237, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param46", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, -5.207, 0, 0.733, 14.251, 0, 1, -23.378, 0, 1.267, 22.011, 0, 1.6, -12.663, 0, 1.933, 3.91, 0, 2.167, 1.023, 0, 2.3, 1.189, 0, 2.5, 0.571, 0, 2.633, 2.224, 0, 2.9, -9.815, 0, 3.167, 9.884, 0, 3.467, -4.188, 0, 3.733, 0.736, 0, 4, -1.231, 0, 4.2, -0.605, 0, 4.333, -1.232, 0, 4.633, 4.233, 0, 5, -4.446, 0, 5.3, 1.161, 0, 5.467, 0.282, 0, 5.633, 1.156, 0, 5.9, -0.763, 0, 6.233, 0.906, 0, 6.733, -1.075, 0, 7.033, 0.026, 0, 7.333, -2.692, 0, 7.6, 6.723, 0, 7.967, -8.954, 0, 8.433, 17.69, 0, 8.833, -17.326, 0, 9.367, 10.622, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param62", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.533, 9.342, 0, 0.8, -19.438, 0, 1.1, 21.327, 0, 1.5, -12.859, 0, 2.067, 6.835, 0, 2.7, -10.529, 0, 3.033, 11.417, 0, 3.7, -5.045, 0, 4.433, 7.736, 0, 4.867, -7.856, 0, 5.467, 4.139, 0, 5.833, 1.653, 0, 5.867, 1.675, 0, 6.567, -3.782, 0, 7.4, 9.139, 0, 7.8, -12.156, 0, 8.267, 17.325, 0, 8.633, -17.344, 0, 9.167, 9.237, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param63", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, -5.207, 0, 0.733, 14.251, 0, 1, -23.378, 0, 1.267, 22.011, 0, 1.6, -12.663, 0, 1.933, 3.91, 0, 2.167, 1.023, 0, 2.3, 1.189, 0, 2.5, 0.571, 0, 2.633, 2.224, 0, 2.9, -9.815, 0, 3.167, 9.884, 0, 3.467, -4.188, 0, 3.733, 0.736, 0, 4, -1.231, 0, 4.2, -0.605, 0, 4.333, -1.232, 0, 4.633, 4.233, 0, 5, -4.446, 0, 5.3, 1.161, 0, 5.467, 0.282, 0, 5.633, 1.156, 0, 5.9, -0.763, 0, 6.233, 0.906, 0, 6.733, -1.075, 0, 7.033, 0.026, 0, 7.333, -2.692, 0, 7.6, 6.723, 0, 7.967, -8.954, 0, 8.433, 17.69, 0, 8.833, -17.326, 0, 9.367, 10.622, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param64", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.533, 9.342, 0, 0.8, -19.438, 0, 1.1, 21.327, 0, 1.5, -12.859, 0, 2.067, 6.835, 0, 2.7, -10.529, 0, 3.033, 11.417, 0, 3.7, -5.045, 0, 4.433, 7.736, 0, 4.867, -7.856, 0, 5.467, 4.139, 0, 5.833, 1.653, 0, 5.867, 1.675, 0, 6.567, -3.782, 0, 7.4, 9.139, 0, 7.8, -12.156, 0, 8.267, 17.325, 0, 8.633, -17.344, 0, 9.167, 9.237, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param65", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, -5.207, 0, 0.733, 14.251, 0, 1, -23.378, 0, 1.267, 22.011, 0, 1.6, -12.663, 0, 1.933, 3.91, 0, 2.167, 1.023, 0, 2.3, 1.189, 0, 2.5, 0.571, 0, 2.633, 2.224, 0, 2.9, -9.815, 0, 3.167, 9.884, 0, 3.467, -4.188, 0, 3.733, 0.736, 0, 4, -1.231, 0, 4.2, -0.605, 0, 4.333, -1.232, 0, 4.633, 4.233, 0, 5, -4.446, 0, 5.3, 1.161, 0, 5.467, 0.282, 0, 5.633, 1.156, 0, 5.9, -0.763, 0, 6.233, 0.906, 0, 6.733, -1.075, 0, 7.033, 0.026, 0, 7.333, -2.692, 0, 7.6, 6.723, 0, 7.967, -8.954, 0, 8.433, 17.69, 0, 8.833, -17.326, 0, 9.367, 10.622, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param72", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.533, 9.342, 0, 0.8, -19.438, 0, 1.1, 21.327, 0, 1.5, -12.859, 0, 2.067, 6.835, 0, 2.7, -10.529, 0, 3.033, 11.417, 0, 3.7, -5.045, 0, 4.433, 7.736, 0, 4.867, -7.856, 0, 5.467, 4.139, 0, 5.833, 1.653, 0, 5.867, 1.675, 0, 6.567, -3.782, 0, 7.4, 9.139, 0, 7.8, -12.156, 0, 8.267, 17.325, 0, 8.633, -17.344, 0, 9.167, 9.237, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param73", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, -5.207, 0, 0.733, 14.251, 0, 1, -23.378, 0, 1.267, 22.011, 0, 1.6, -12.663, 0, 1.933, 3.91, 0, 2.167, 1.023, 0, 2.3, 1.189, 0, 2.5, 0.571, 0, 2.633, 2.224, 0, 2.9, -9.815, 0, 3.167, 9.884, 0, 3.467, -4.188, 0, 3.733, 0.736, 0, 4, -1.231, 0, 4.2, -0.605, 0, 4.333, -1.232, 0, 4.633, 4.233, 0, 5, -4.446, 0, 5.3, 1.161, 0, 5.467, 0.282, 0, 5.633, 1.156, 0, 5.9, -0.763, 0, 6.233, 0.906, 0, 6.733, -1.075, 0, 7.033, 0.026, 0, 7.333, -2.692, 0, 7.6, 6.723, 0, 7.967, -8.954, 0, 8.433, 17.69, 0, 8.833, -17.326, 0, 9.367, 10.622, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param74", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.533, 9.342, 0, 0.8, -19.438, 0, 1.1, 21.327, 0, 1.5, -12.859, 0, 2.067, 6.835, 0, 2.7, -10.529, 0, 3.033, 11.417, 0, 3.7, -5.045, 0, 4.433, 7.736, 0, 4.867, -7.856, 0, 5.467, 4.139, 0, 5.833, 1.653, 0, 5.867, 1.675, 0, 6.567, -3.782, 0, 7.4, 9.139, 0, 7.8, -12.156, 0, 8.267, 17.325, 0, 8.633, -17.344, 0, 9.167, 9.237, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param75", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, -5.207, 0, 0.733, 14.251, 0, 1, -23.378, 0, 1.267, 22.011, 0, 1.6, -12.663, 0, 1.933, 3.91, 0, 2.167, 1.023, 0, 2.3, 1.189, 0, 2.5, 0.571, 0, 2.633, 2.224, 0, 2.9, -9.815, 0, 3.167, 9.884, 0, 3.467, -4.188, 0, 3.733, 0.736, 0, 4, -1.231, 0, 4.2, -0.605, 0, 4.333, -1.232, 0, 4.633, 4.233, 0, 5, -4.446, 0, 5.3, 1.161, 0, 5.467, 0.282, 0, 5.633, 1.156, 0, 5.9, -0.763, 0, 6.233, 0.906, 0, 6.733, -1.075, 0, 7.033, 0.026, 0, 7.333, -2.692, 0, 7.6, 6.723, 0, 7.967, -8.954, 0, 8.433, 17.69, 0, 8.833, -17.326, 0, 9.367, 10.622, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param53", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.533, 9.342, 0, 0.8, -19.438, 0, 1.1, 21.327, 0, 1.5, -12.859, 0, 2.067, 6.835, 0, 2.7, -10.529, 0, 3.033, 11.417, 0, 3.7, -5.045, 0, 4.433, 7.736, 0, 4.867, -7.856, 0, 5.467, 4.139, 0, 5.833, 1.653, 0, 5.867, 1.675, 0, 6.567, -3.782, 0, 7.4, 9.139, 0, 7.8, -12.156, 0, 8.267, 17.325, 0, 8.633, -17.344, 0, 9.167, 9.237, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param54", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, -5.207, 0, 0.733, 14.251, 0, 1, -23.378, 0, 1.267, 22.011, 0, 1.6, -12.663, 0, 1.933, 3.91, 0, 2.167, 1.023, 0, 2.3, 1.189, 0, 2.5, 0.571, 0, 2.633, 2.224, 0, 2.9, -9.815, 0, 3.167, 9.884, 0, 3.467, -4.188, 0, 3.733, 0.736, 0, 4, -1.231, 0, 4.2, -0.605, 0, 4.333, -1.232, 0, 4.633, 4.233, 0, 5, -4.446, 0, 5.3, 1.161, 0, 5.467, 0.282, 0, 5.633, 1.156, 0, 5.9, -0.763, 0, 6.233, 0.906, 0, 6.733, -1.075, 0, 7.033, 0.026, 0, 7.333, -2.692, 0, 7.6, 6.723, 0, 7.967, -8.954, 0, 8.433, 17.69, 0, 8.833, -17.326, 0, 9.367, 10.622, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param81", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.533, 9.342, 0, 0.8, -19.438, 0, 1.1, 21.327, 0, 1.5, -12.859, 0, 2.067, 6.835, 0, 2.7, -10.529, 0, 3.033, 11.417, 0, 3.7, -5.045, 0, 4.433, 7.736, 0, 4.867, -7.856, 0, 5.467, 4.139, 0, 5.833, 1.653, 0, 5.867, 1.675, 0, 6.567, -3.782, 0, 7.4, 9.139, 0, 7.8, -12.156, 0, 8.267, 17.325, 0, 8.633, -17.344, 0, 9.167, 9.237, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param82", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, -5.207, 0, 0.733, 14.251, 0, 1, -23.378, 0, 1.267, 22.011, 0, 1.6, -12.663, 0, 1.933, 3.91, 0, 2.167, 1.023, 0, 2.3, 1.189, 0, 2.5, 0.571, 0, 2.633, 2.224, 0, 2.9, -9.815, 0, 3.167, 9.884, 0, 3.467, -4.188, 0, 3.733, 0.736, 0, 4, -1.231, 0, 4.2, -0.605, 0, 4.333, -1.232, 0, 4.633, 4.233, 0, 5, -4.446, 0, 5.3, 1.161, 0, 5.467, 0.282, 0, 5.633, 1.156, 0, 5.9, -0.763, 0, 6.233, 0.906, 0, 6.733, -1.075, 0, 7.033, 0.026, 0, 7.333, -2.692, 0, 7.6, 6.723, 0, 7.967, -8.954, 0, 8.433, 17.69, 0, 8.833, -17.326, 0, 9.367, 10.622, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param70", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.533, 9.342, 0, 0.8, -19.438, 0, 1.1, 21.327, 0, 1.5, -12.859, 0, 2.067, 6.835, 0, 2.7, -10.529, 0, 3.033, 11.417, 0, 3.7, -5.045, 0, 4.433, 7.736, 0, 4.867, -7.856, 0, 5.467, 4.139, 0, 5.833, 1.653, 0, 5.867, 1.675, 0, 6.567, -3.782, 0, 7.4, 9.139, 0, 7.8, -12.156, 0, 8.267, 17.325, 0, 8.633, -17.344, 0, 9.167, 9.237, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param71", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, -5.207, 0, 0.733, 14.251, 0, 1, -23.378, 0, 1.267, 22.011, 0, 1.6, -12.663, 0, 1.933, 3.91, 0, 2.167, 1.023, 0, 2.3, 1.189, 0, 2.5, 0.571, 0, 2.633, 2.224, 0, 2.9, -9.815, 0, 3.167, 9.884, 0, 3.467, -4.188, 0, 3.733, 0.736, 0, 4, -1.231, 0, 4.2, -0.605, 0, 4.333, -1.232, 0, 4.633, 4.233, 0, 5, -4.446, 0, 5.3, 1.161, 0, 5.467, 0.282, 0, 5.633, 1.156, 0, 5.9, -0.763, 0, 6.233, 0.906, 0, 6.733, -1.075, 0, 7.033, 0.026, 0, 7.333, -2.692, 0, 7.6, 6.723, 0, 7.967, -8.954, 0, 8.433, 17.69, 0, 8.833, -17.326, 0, 9.367, 10.622, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param77", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.533, 9.342, 0, 0.8, -19.438, 0, 1.1, 21.327, 0, 1.5, -12.859, 0, 2.067, 6.835, 0, 2.7, -10.529, 0, 3.033, 11.417, 0, 3.7, -5.045, 0, 4.433, 7.736, 0, 4.867, -7.856, 0, 5.467, 4.139, 0, 5.833, 1.653, 0, 5.867, 1.675, 0, 6.567, -3.782, 0, 7.4, 9.139, 0, 7.8, -12.156, 0, 8.267, 17.325, 0, 8.633, -17.344, 0, 9.167, 9.237, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param78", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, -5.207, 0, 0.733, 14.251, 0, 1, -23.378, 0, 1.267, 22.011, 0, 1.6, -12.663, 0, 1.933, 3.91, 0, 2.167, 1.023, 0, 2.3, 1.189, 0, 2.5, 0.571, 0, 2.633, 2.224, 0, 2.9, -9.815, 0, 3.167, 9.884, 0, 3.467, -4.188, 0, 3.733, 0.736, 0, 4, -1.231, 0, 4.2, -0.605, 0, 4.333, -1.232, 0, 4.633, 4.233, 0, 5, -4.446, 0, 5.3, 1.161, 0, 5.467, 0.282, 0, 5.633, 1.156, 0, 5.9, -0.763, 0, 6.233, 0.906, 0, 6.733, -1.075, 0, 7.033, 0.026, 0, 7.333, -2.692, 0, 7.6, 6.723, 0, 7.967, -8.954, 0, 8.433, 17.69, 0, 8.833, -17.326, 0, 9.367, 10.622, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param79", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.533, 9.342, 0, 0.8, -19.438, 0, 1.1, 21.327, 0, 1.5, -12.859, 0, 2.067, 6.835, 0, 2.7, -10.529, 0, 3.033, 11.417, 0, 3.7, -5.045, 0, 4.433, 7.736, 0, 4.867, -7.856, 0, 5.467, 4.139, 0, 5.833, 1.653, 0, 5.867, 1.675, 0, 6.567, -3.782, 0, 7.4, 9.139, 0, 7.8, -12.156, 0, 8.267, 17.325, 0, 8.633, -17.344, 0, 9.167, 9.237, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param80", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, -5.207, 0, 0.733, 14.251, 0, 1, -23.378, 0, 1.267, 22.011, 0, 1.6, -12.663, 0, 1.933, 3.91, 0, 2.167, 1.023, 0, 2.3, 1.189, 0, 2.5, 0.571, 0, 2.633, 2.224, 0, 2.9, -9.815, 0, 3.167, 9.884, 0, 3.467, -4.188, 0, 3.733, 0.736, 0, 4, -1.231, 0, 4.2, -0.605, 0, 4.333, -1.232, 0, 4.633, 4.233, 0, 5, -4.446, 0, 5.3, 1.161, 0, 5.467, 0.282, 0, 5.633, 1.156, 0, 5.9, -0.763, 0, 6.233, 0.906, 0, 6.733, -1.075, 0, 7.033, 0.026, 0, 7.333, -2.692, 0, 7.6, 6.723, 0, 7.967, -8.954, 0, 8.433, 17.69, 0, 8.833, -17.326, 0, 9.367, 10.622, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param294", "Segments": [0, 1, 2, 0.333, 1, 0, 0.4, 0, 2, 9.5, 0, 0, 9.6, 1]}, {"Target": "Parameter", "Id": "Param47", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.533, 3.497, 0, 0.8, -12.657, 0, 1.133, 18.237, 0, 1.5, -14.655, 0, 1.9, 9.047, 0, 2.267, -0.12, 0, 2.367, -0.012, 0, 2.7, -8.502, 0, 3.033, 8.854, 0, 3.7, -3.574, 0, 4.433, 5.543, 0, 4.9, -5.911, 0, 5.5, 3.427, 0, 5.833, 1.449, 0, 5.867, 1.457, 0, 6.567, -2.675, 0, 7.4, 4.667, 0, 7.8, -5.718, 0, 8.267, 9.862, 0, 8.633, -10.448, 0, 9.167, 5.075, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param48", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, -1.866, 0, 0.733, 7.88, 0, 1, -15.888, 0, 1.3, 17.664, 0, 1.633, -13.208, 0, 2.033, 8.688, 0, 2.333, -4.399, 0, 2.633, 4.849, 0, 2.9, -9.169, 0, 3.167, 8.392, 0, 3.467, -3.786, 0, 3.733, 1.029, 0, 4, -0.992, 0, 4.2, -0.533, 0, 4.333, -0.8, 0, 4.633, 2.964, 0, 5, -3.014, 0, 5.333, 0.601, 0, 5.433, 0.214, 0, 5.633, 1.041, 0, 5.9, -0.634, 0, 6.233, 0.755, 0, 6.7, -0.75, 0, 6.967, 0.041, 0, 7.3, -1.242, 0, 7.6, 3.389, 0, 7.933, -4.374, 0, 8.133, -0.986, 0, 8.2, -1.118, 0, 8.433, 10.292, 0, 8.867, -10.35, 0, 9.367, 6.61, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param56", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.567, 1.528, 0, 0.833, -5.527, 0, 1.2, 8.068, 0, 1.633, -7.554, 0, 2.133, 5.41, 0, 2.733, -6.856, 0, 3.167, 5.541, 0, 3.733, -4.767, 0, 4.4, 4.539, 0, 4.967, -4.9, 0, 5.533, 3.915, 0, 6.233, -1.186, 0, 6.367, -1.137, 0, 6.533, -1.191, 0, 7.4, 2.819, 0, 7.867, -3.626, 0, 8.3, 5.538, 0, 8.833, -4.154, 0, 9.567, 2.508, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param57", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, -1.034, 0, 0.733, 4.344, 0, 1.033, -9.056, 0, 1.4, 9.798, 0, 1.833, -7.469, 0, 2.333, 3.278, 0, 2.933, -7.256, 0, 3.3, 4.349, 0, 3.967, -3.181, 0, 4.667, 3.506, 0, 5.167, -3.379, 0, 5.733, 2.361, 0, 6.3, -0.123, 0, 6.467, -0.052, 0, 6.967, -0.824, 0, 7.167, -0.726, 0, 7.267, -0.828, 0, 7.667, 3.203, 0, 8.067, -3.411, 0, 8.1, -3.405, 0, 8.167, -3.582, 0, 8.467, 6.645, 0, 9.067, -4.424, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param58", "Segments": [0, 0, 2, 0.333, 0, 2, 0.4, 0, 0, 0.6, -1.45, 0, 0.867, 5.502, 0, 1.133, -11.292, 0, 1.467, 13.477, 0, 1.867, -11.068, 0, 2.233, 6.226, 0, 2.567, 0.311, 0, 2.7, 1.208, 0, 3.033, -7.78, 0, 3.367, 7.419, 0, 3.767, -1.89, 0, 3.833, -1.845, 0, 4.033, -2.225, 0, 4.767, 2.965, 0, 5.2, -3.654, 0, 5.767, 2.323, 0, 6.167, -0.966, 0, 6.567, 0.426, 0, 6.967, -0.519, 0, 7.167, -0.401, 0, 7.4, -0.81, 0, 7.767, 3.379, 0, 8.133, -4.668, 0, 8.633, 8.585, 0, 9.133, -7.644, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param59", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.567, 1.528, 0, 0.833, -5.527, 0, 1.2, 8.068, 0, 1.633, -7.554, 0, 2.133, 5.41, 0, 2.733, -6.856, 0, 3.167, 5.541, 0, 3.733, -4.767, 0, 4.4, 4.539, 0, 4.967, -4.9, 0, 5.533, 3.915, 0, 6.233, -1.186, 0, 6.367, -1.137, 0, 6.533, -1.191, 0, 7.4, 2.819, 0, 7.867, -3.626, 0, 8.3, 5.538, 0, 8.833, -4.154, 0, 9.567, 2.508, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param60", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, -1.034, 0, 0.733, 4.344, 0, 1.033, -9.056, 0, 1.4, 9.798, 0, 1.833, -7.469, 0, 2.333, 3.278, 0, 2.933, -7.256, 0, 3.3, 4.349, 0, 3.967, -3.181, 0, 4.667, 3.506, 0, 5.167, -3.379, 0, 5.733, 2.361, 0, 6.3, -0.123, 0, 6.467, -0.052, 0, 6.967, -0.824, 0, 7.167, -0.726, 0, 7.267, -0.828, 0, 7.667, 3.203, 0, 8.067, -3.411, 0, 8.1, -3.405, 0, 8.167, -3.582, 0, 8.467, 6.645, 0, 9.067, -4.424, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param61", "Segments": [0, 0, 2, 0.333, 0, 2, 0.4, 0, 0, 0.6, -1.45, 0, 0.867, 5.502, 0, 1.133, -11.292, 0, 1.467, 13.477, 0, 1.867, -11.068, 0, 2.233, 6.226, 0, 2.567, 0.311, 0, 2.7, 1.208, 0, 3.033, -7.78, 0, 3.367, 7.419, 0, 3.767, -1.89, 0, 3.833, -1.845, 0, 4.033, -2.225, 0, 4.767, 2.965, 0, 5.2, -3.654, 0, 5.767, 2.323, 0, 6.167, -0.966, 0, 6.567, 0.426, 0, 6.967, -0.519, 0, 7.167, -0.401, 0, 7.4, -0.81, 0, 7.767, 3.379, 0, 8.133, -4.668, 0, 8.633, 8.585, 0, 9.133, -7.644, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param49", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.533, 3.497, 0, 0.8, -12.657, 0, 1.133, 18.237, 0, 1.5, -14.655, 0, 1.9, 9.047, 0, 2.267, -0.12, 0, 2.367, -0.012, 0, 2.7, -8.502, 0, 3.033, 8.854, 0, 3.7, -3.574, 0, 4.433, 5.543, 0, 4.9, -5.911, 0, 5.5, 3.427, 0, 5.833, 1.449, 0, 5.867, 1.457, 0, 6.567, -2.675, 0, 7.4, 4.667, 0, 7.8, -5.718, 0, 8.267, 9.862, 0, 8.633, -10.448, 0, 9.167, 5.075, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param50", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, -1.866, 0, 0.733, 7.88, 0, 1, -15.888, 0, 1.3, 17.664, 0, 1.633, -13.208, 0, 2.033, 8.688, 0, 2.333, -4.399, 0, 2.633, 4.849, 0, 2.9, -9.169, 0, 3.167, 8.392, 0, 3.467, -3.786, 0, 3.733, 1.029, 0, 4, -0.992, 0, 4.2, -0.533, 0, 4.333, -0.8, 0, 4.633, 2.964, 0, 5, -3.014, 0, 5.333, 0.601, 0, 5.433, 0.214, 0, 5.633, 1.041, 0, 5.9, -0.634, 0, 6.233, 0.755, 0, 6.7, -0.75, 0, 6.967, 0.041, 0, 7.3, -1.242, 0, 7.6, 3.389, 0, 7.933, -4.374, 0, 8.133, -0.986, 0, 8.2, -1.118, 0, 8.433, 10.292, 0, 8.867, -10.35, 0, 9.367, 6.61, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param51", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.533, 3.497, 0, 0.8, -12.657, 0, 1.133, 18.237, 0, 1.5, -14.655, 0, 1.9, 9.047, 0, 2.267, -0.12, 0, 2.367, -0.012, 0, 2.7, -8.502, 0, 3.033, 8.854, 0, 3.7, -3.574, 0, 4.433, 5.543, 0, 4.9, -5.911, 0, 5.5, 3.427, 0, 5.833, 1.449, 0, 5.867, 1.457, 0, 6.567, -2.675, 0, 7.4, 4.667, 0, 7.8, -5.718, 0, 8.267, 9.862, 0, 8.633, -10.448, 0, 9.167, 5.075, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param52", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, -1.866, 0, 0.733, 7.88, 0, 1, -15.888, 0, 1.3, 17.664, 0, 1.633, -13.208, 0, 2.033, 8.688, 0, 2.333, -4.399, 0, 2.633, 4.849, 0, 2.9, -9.169, 0, 3.167, 8.392, 0, 3.467, -3.786, 0, 3.733, 1.029, 0, 4, -0.992, 0, 4.2, -0.533, 0, 4.333, -0.8, 0, 4.633, 2.964, 0, 5, -3.014, 0, 5.333, 0.601, 0, 5.433, 0.214, 0, 5.633, 1.041, 0, 5.9, -0.634, 0, 6.233, 0.755, 0, 6.7, -0.75, 0, 6.967, 0.041, 0, 7.3, -1.242, 0, 7.6, 3.389, 0, 7.933, -4.374, 0, 8.133, -0.986, 0, 8.2, -1.118, 0, 8.433, 10.292, 0, 8.867, -10.35, 0, 9.367, 6.61, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation63", "Segments": [0, -0.675, 2, 0.333, -0.675, 1, 0.344, -0.675, 0.356, -0.173, 0.367, 0, 1, 0.422, 0.865, 0.478, 1.166, 0.533, 1.166, 0, 0.8, -4.219, 0, 1.133, 6.079, 0, 1.5, -4.885, 0, 1.9, 3.015, 0, 2.267, -0.04, 0, 2.367, -0.004, 0, 2.7, -2.834, 0, 3.033, 2.951, 0, 3.7, -1.191, 0, 4.433, 1.848, 0, 4.9, -1.97, 0, 5.5, 1.142, 0, 5.833, 0.483, 0, 5.867, 0.486, 0, 6.567, -0.892, 0, 7.4, 1.556, 0, 7.8, -1.906, 0, 8.267, 3.287, 0, 8.633, -3.483, 0, 9.167, 1.692, 0, 9.6, -0.675]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation64", "Segments": [0, 2.951, 2, 0.333, 2.951, 1, 0.344, 2.951, 0.356, 0.185, 0.367, 0, 1, 0.411, -0.739, 0.456, -0.933, 0.5, -0.933, 0, 0.733, 3.94, 0, 1, -7.944, 0, 1.3, 8.832, 0, 1.633, -6.604, 0, 2.033, 4.344, 0, 2.333, -2.199, 0, 2.633, 2.425, 0, 2.9, -4.585, 0, 3.167, 4.196, 0, 3.467, -1.893, 0, 3.733, 0.514, 0, 4, -0.496, 0, 4.2, -0.267, 0, 4.333, -0.4, 0, 4.633, 1.482, 0, 5, -1.507, 0, 5.333, 0.301, 0, 5.433, 0.107, 0, 5.633, 0.52, 0, 5.9, -0.317, 0, 6.233, 0.378, 0, 6.7, -0.375, 0, 6.967, 0.021, 0, 7.3, -0.621, 0, 7.6, 1.695, 0, 7.933, -2.187, 0, 8.133, -0.493, 0, 8.2, -0.559, 0, 8.433, 5.146, 0, 8.867, -5.175, 0, 9.367, 3.305, 0, 9.6, 2.951]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation65", "Segments": [0, 4.849, 2, 0.333, 4.849, 1, 0.355, 4.849, 0.378, 0.378, 0.4, 0, 1, 0.456, -0.946, 0.511, -1.096, 0.567, -1.096, 0, 0.833, 5.312, 0, 1.1, -11.416, 0, 1.4, 15.412, 0, 1.733, -13.536, 0, 2.1, 9.642, 0, 2.433, -5.983, 0, 2.733, 5.196, 0, 3, -7.65, 0, 3.3, 8.429, 0, 3.6, -5.21, 0, 3.9, 2.268, 0, 4.2, -1.539, 0, 4.7, 2.392, 0, 5.1, -2.957, 0, 5.433, 1.306, 0, 6, -0.524, 0, 6.333, 0.778, 0, 6.733, -0.734, 0, 7.067, 0.222, 0, 7.367, -0.883, 0, 7.7, 2.941, 0, 8.033, -4.27, 0, 8.567, 7.014, 0, 9.033, -9.613, 0, 9.533, 7.767, 0, 9.6, 4.849]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation66", "Segments": [0, -11.659, 2, 0.333, -11.659, 0, 0.467, 0, 0, 0.633, -1.28, 0, 0.9, 7.034, 0, 1.167, -16.31, 0, 1.467, 23.24, 0, 1.833, -22.346, 0, 2.2, 17.724, 0, 2.533, -12.539, 0, 2.833, 10.642, 0, 3.1, -12.845, 0, 3.4, 14.839, 0, 3.733, -11.137, 0, 4.033, 6.319, 0, 4.333, -4.301, 0, 4.733, 3.952, 0, 5.167, -5.072, 0, 5.533, 3.345, 0, 5.9, -0.738, 0, 6.433, 1.294, 0, 6.8, -1.417, 0, 7.167, 0.702, 0, 7.467, -1.413, 0, 7.8, 4.588, 0, 8.133, -7.441, 0, 8.633, 10.132, 0, 9.133, -15.644, 0, 9.6, -11.659]}, {"Target": "Parameter", "Id": "Param55", "Segments": [0, -10.57, 2, 0.333, -10.57, 0, 0.533, 0, 0, 0.733, -1.447, 0, 1, 8.869, 0, 1.233, -21.597, 0, 1.533, 28.986, 0, 1.9, -29.379, 0, 2.267, 27.533, 0, 2.633, -21.633, 0, 2.933, 18.439, 0, 3.233, -20.464, 0, 3.5, 23.588, 0, 3.833, -20.212, 0, 4.133, 13.476, 0, 4.433, -9.997, 0, 4.8, 8.528, 0, 5.233, -8.267, 0, 5.633, 6.678, 0, 5.967, -3.087, 0, 6.5, 1.75, 0, 6.9, -2.474, 0, 7.233, 1.624, 0, 7.567, -2.32, 0, 7.867, 6.847, 0, 8.2, -12.101, 0, 8.733, 15.278, 0, 9.267, -23.216, 0, 9.6, -10.57]}, {"Target": "Parameter", "Id": "Param28", "Segments": [0, -7.12, 2, 0.333, -7.12, 0, 1.433, 7.36, 0, 2.533, -7.12, 0, 3.633, 7.36, 0, 4.733, -7.12, 0, 5.867, 7.36, 0, 6.967, -7.12, 0, 8.067, 7.36, 0, 9.6, -7.12]}, {"Target": "Parameter", "Id": "Param29", "Segments": [0, -1.278, 2, 0.333, -1.278, 0, 0.767, -4.74, 0, 1.867, 5.1, 0, 2.967, -4.74, 0, 4.067, 5.1, 1, 4.289, 5.1, 4.511, 2.517, 4.733, -1.278, 1, 4.889, -3.934, 5.044, -4.74, 5.2, -4.74, 0, 6.3, 5.1, 0, 7.4, -4.74, 0, 8.567, 5.1, 0, 9.6, -1.278]}, {"Target": "Parameter", "Id": "Param30", "Segments": [0, -5.909, 2, 0.333, -5.909, 0, 1.3, 2.533, 0, 2.4, -6.287, 0, 3.5, 2.533, 0, 4.6, -6.287, 1, 4.644, -6.287, 4.689, -6.523, 4.733, -5.909, 1, 5.055, -1.45, 5.378, 2.533, 5.7, 2.533, 0, 6.833, -6.287, 0, 7.933, 2.533, 0, 9.4, -6.287, 0, 9.6, -5.909]}, {"Target": "Parameter", "Id": "Param31", "Segments": [0, -0.551, 2, 0.333, -0.551, 0, 0.8, 3.018, 0, 1.9, -6.18, 0, 3, 3.018, 0, 4.1, -6.18, 1, 4.311, -6.18, 4.522, -4.061, 4.733, -0.551, 1, 4.889, 2.035, 5.044, 3.018, 5.2, 3.018, 0, 6.333, -6.18, 0, 7.433, 3.018, 0, 8.6, -6.18, 0, 9.6, -0.551]}, {"Target": "Parameter", "Id": "Param32", "Segments": [0, -2.714, 2, 0.333, -2.714, 0, 1.333, 2.284, 0, 2.433, -2.796, 0, 3.567, 2.284, 0, 4.667, -2.796, 1, 4.689, -2.796, 4.711, -2.896, 4.733, -2.714, 1, 5.078, 0.106, 5.422, 2.284, 5.767, 2.284, 0, 6.867, -2.796, 0, 7.967, 2.284, 0, 9.533, -2.796, 0, 9.6, -2.714]}, {"Target": "Parameter", "Id": "Param33", "Segments": [0, -2.392, 2, 0.333, -2.392, 0, 0.8, 3.279, 0, 1.933, -10.047, 0, 3.033, 3.279, 0, 4.133, -10.047, 1, 4.333, -10.047, 4.533, -7.219, 4.733, -2.392, 1, 4.9, 1.631, 5.066, 3.279, 5.233, 3.279, 0, 6.333, -10.047, 0, 7.467, 3.279, 0, 8.633, -10.047, 0, 9.6, -2.392]}, {"Target": "Parameter", "Id": "Param34", "Segments": [0, -21.182, 2, 0.333, -21.182, 0, 1.367, 11.7, 0, 2.467, -21.42, 0, 3.567, 11.7, 0, 4.7, -21.42, 1, 4.711, -21.42, 4.722, -21.789, 4.733, -21.182, 1, 5.089, -1.744, 5.444, 11.7, 5.8, 11.7, 0, 6.9, -21.42, 0, 8, 11.7, 0, 9.533, -21.42, 0, 9.6, -21.182]}, {"Target": "Parameter", "Id": "Param84", "Segments": [0, 0, 2, 0.333, 0, 0, 1.467, -6.9, 0, 2.633, 0, 0, 3.8, -6.9, 0, 4.967, 0, 0, 6.133, -6.9, 0, 7.267, 0, 0, 8.433, -6.9, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param85", "Segments": [0, 0, 2, 0.333, 0, 0, 0.9, 4.74, 0, 2.067, -4.74, 1, 2.256, -4.74, 2.444, -3.067, 2.633, 0, 1, 2.833, 3.247, 3.033, 4.74, 3.233, 4.74, 0, 4.367, -4.74, 1, 4.567, -4.74, 4.767, -3.247, 4.967, 0, 1, 5.156, 3.067, 5.344, 4.74, 5.533, 4.74, 0, 6.7, -4.74, 1, 6.889, -4.74, 7.078, -3.067, 7.267, 0, 1, 7.467, 3.247, 7.667, 4.74, 7.867, 4.74, 0, 9.033, -4.74, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param83", "Segments": [0, 0, 2, 0.333, 0, 0, 1.467, 3.96, 0, 2.633, 0, 0, 3.8, 3.96, 0, 4.967, 0, 0, 6.133, 3.96, 0, 7.267, 0, 0, 8.433, 3.96, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param86", "Segments": [0, 1, 2, 0.333, 1, 2, 1.967, 1, 0, 2.067, 0, 0, 2.167, 1, 2, 2.633, 1, 2, 4.3, 1, 0, 4.367, 0, 0, 4.5, 1, 2, 4.967, 1, 2, 6.6, 1, 0, 6.7, 0, 0, 6.8, 1, 2, 7.267, 1, 2, 8.933, 1, 0, 9.033, 0, 0, 9.133, 1, 2, 9.6, 1]}, {"Target": "Parameter", "Id": "Param95", "Segments": [0, 0, 2, 0.333, 0, 0, 1.467, 10, 0, 2.633, 0, 0, 3.8, 10, 0, 4.967, 0, 0, 6.133, 10, 0, 7.267, 0, 0, 8.433, 10, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param92", "Segments": [0, 0, 2, 0.333, 0, 0, 0.9, -12, 0, 2.067, 12, 1, 2.256, 12, 2.444, 7.764, 2.633, 0, 1, 2.833, -8.221, 3.033, -12, 3.233, -12, 0, 4.367, 12, 1, 4.567, 12, 4.767, 8.221, 4.967, 0, 1, 5.156, -7.764, 5.344, -12, 5.533, -12, 0, 6.7, 12, 1, 6.889, 12, 7.078, 7.764, 7.267, 0, 1, 7.467, -8.221, 7.667, -12, 7.867, -12, 0, 9.033, 12, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param93", "Segments": [0, 0, 2, 0.333, 0, 0, 1.467, -12, 0, 2.633, 0, 0, 3.8, -12, 0, 4.967, 0, 0, 6.133, -12, 0, 7.267, 0, 0, 8.433, -12, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param96", "Segments": [0, 0, 2, 0.333, 0, 0, 0.467, -16, 0, 0.6, 0, 0, 0.767, -16, 0, 0.967, 0, 2, 2.633, 0, 0, 2.767, -16, 0, 2.933, 0, 0, 3.1, -16, 0, 3.267, 0, 2, 4.967, 0, 0, 5.1, -16, 0, 5.233, 0, 0, 5.433, -16, 0, 5.6, 0, 2, 7.267, 0, 0, 7.433, -16, 0, 7.567, 0, 0, 7.733, -16, 0, 7.9, 0, 2, 9.6, 0]}, {"Target": "Parameter", "Id": "Param97", "Segments": [0, 0, 2, 0.333, 0, 0, 0.533, -14, 1, 0.555, -14, 0.578, -5.57, 0.6, 0, 1, 0.644, 11.14, 0.689, 14, 0.733, 14, 0, 0.867, -14, 0, 0.967, 0, 2, 2.633, 0, 0, 2.667, 14, 0, 2.867, -14, 1, 2.889, -14, 2.911, -7.166, 2.933, 0, 1, 2.966, 10.749, 3, 14, 3.033, 14, 0, 3.2, -14, 0, 3.267, 0, 2, 4.967, 0, 0, 5, 14, 0, 5.2, -14, 1, 5.211, -14, 5.222, -2.722, 5.233, 0, 1, 5.278, 10.888, 5.322, 14, 5.367, 14, 0, 5.5, -14, 0, 5.6, 0, 2, 7.267, 0, 0, 7.3, 14, 0, 7.5, -14, 1, 7.522, -14, 7.545, -7.166, 7.567, 0, 1, 7.6, 10.749, 7.634, 14, 7.667, 14, 0, 7.833, -14, 0, 7.9, 0, 2, 9.6, 0]}, {"Target": "Parameter", "Id": "Param89", "Segments": [0, 0, 2, 0.333, 0, 0, 1.467, 18.72, 0, 2.633, 0, 0, 3.8, 18.72, 0, 4.967, 0, 0, 6.133, 18.72, 0, 7.267, 0, 0, 8.433, 18.72, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param90", "Segments": [0, 0, 2, 0.333, 0, 0, 0.9, -19, 0, 2.067, 19, 1, 2.256, 19, 2.444, 12.293, 2.633, 0, 1, 2.833, -13.016, 3.033, -19, 3.233, -19, 0, 4.367, 19, 1, 4.567, 19, 4.767, 13.016, 4.967, 0, 1, 5.156, -12.293, 5.344, -19, 5.533, -19, 0, 6.7, 19, 1, 6.889, 19, 7.078, 12.293, 7.267, 0, 1, 7.467, -13.016, 7.667, -19, 7.867, -19, 0, 9.033, 19, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation56", "Segments": [0, 0, 2, 0.333, 0, 0, 0.9, 9, 0, 1.467, -9, 0, 2.067, 9, 0, 2.633, 0, 0, 3.233, 9, 0, 3.8, -9, 0, 4.367, 9, 0, 4.967, 0, 0, 5.533, 9, 0, 6.133, -9, 0, 6.7, 9, 0, 7.267, 0, 0, 7.867, 9, 0, 8.433, -9, 0, 9.033, 9, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param98", "Segments": [0, -6, 2, 0.333, -6, 0, 0.6, 0, 0, 1.2, -12, 0, 1.767, 12, 0, 2.333, -12, 0, 2.933, 0, 0, 3.5, -12, 0, 4.1, 12, 0, 4.667, -12, 1, 4.767, -12, 4.867, -10.218, 4.967, -6, 1, 5.056, -2.25, 5.144, 0, 5.233, 0, 0, 5.833, -12, 0, 6.4, 12, 0, 7, -12, 0, 7.567, 0, 0, 8.133, -12, 0, 8.733, 12, 0, 9.3, -12, 0, 9.6, -6]}, {"Target": "Parameter", "Id": "Param99", "Segments": [0, -10.921, 2, 0.333, -10.921, 0, 0.867, 0, 0, 1.433, -11, 0, 2.033, 11, 0, 2.6, -11, 0, 3.2, 0, 0, 3.767, -11, 0, 4.333, 11, 0, 4.933, -11, 1, 4.944, -11, 4.956, -11.217, 4.967, -10.921, 1, 5.145, -6.188, 5.322, 0, 5.5, 0, 0, 6.1, -11, 0, 6.667, 11, 0, 7.233, -11, 0, 7.833, 0, 0, 8.4, -11, 0, 9, 11, 0, 9.567, -11, 0, 9.6, -10.921]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation67", "Segments": [0, -10.939, 2, 0.333, -10.939, 0, 0.533, -25, 0, 1.1, 0, 0, 1.667, -25, 0, 2.267, 25, 0, 2.833, -25, 0, 3.433, 0, 0, 4, -25, 0, 4.567, 25, 1, 4.7, 25, 4.834, 10.959, 4.967, -10.939, 1, 5.034, -21.888, 5.1, -25, 5.167, -25, 0, 5.733, 0, 0, 6.333, -25, 0, 6.9, 25, 0, 7.467, -25, 0, 8.067, 0, 0, 8.633, -25, 0, 9.233, 25, 0, 9.6, -10.939]}, {"Target": "Parameter", "Id": "Param218mao2", "Segments": [0, 10, 2, 0.667, 10, 1, 0.745, 10, 0.822, 6.786, 0.9, 1, 2, 0.933, 0, 2, 8.333, 0, 2, 8.667, 0, 2, 8.7, 1, 1, 8.744, 5.708, 8.789, 10, 8.833, 10, 2, 9.6, 10]}, {"Target": "Parameter", "Id": "Param211mao2", "Segments": [0, 0, 2, 0.333, 0, 0, 0.533, 12.494, 2, 0.567, 12.494, 1, 0.578, 12.494, 0.589, 4.455, 0.6, 2.246, 1, 0.633, -4.381, 0.667, -8.215, 0.7, -14.19, 1, 0.756, -24.149, 0.811, -30, 0.867, -30, 2, 0.967, -30, 2, 1.167, -30, 2, 2.467, -30, 2, 2.8, -30, 2, 7.133, -30, 2, 7.6, -30, 2, 8.067, -30, 0, 8.333, -24.753, 0, 8.7, -27.172, 0, 9.2, 0, 2, 9.6, 0]}, {"Target": "Parameter", "Id": "Param197mao2", "Segments": [0, 0, 2, 0.333, 0, 0, 0.367, -5.172, 0, 0.533, -0.385, 2, 0.567, -0.385, 0, 0.833, 30, 0, 0.967, -6, 0, 1.167, -4.153, 2, 2.467, -4.153, 0, 2.8, 30, 2, 7.133, 30, 2, 7.6, 30, 2, 8.067, 30, 1, 8.156, 30, 8.244, 28.232, 8.333, 24.753, 1, 8.755, 8.232, 9.178, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param216mao2", "Segments": [0, -8.88, 1, 0.111, -8.88, 0.222, -8.879, 0.333, -8.9, 1, 0.4, -8.913, 0.466, -14.06, 0.533, -14.06, 2, 0.567, -14.06, 2, 0.633, -14.06, 1, 0.655, -14.06, 0.678, -10.267, 0.7, -8.648, 1, 0.789, -2.172, 0.878, 0, 0.967, 0, 2, 1.167, 0, 2, 2.467, 0, 2, 2.8, 0, 2, 7.133, 0, 2, 7.6, 0, 2, 8.067, 0, 1, 8.156, 0, 8.244, -1.493, 8.333, -1.536, 1, 8.455, -1.595, 8.578, -1.575, 8.7, -1.64, 1, 8.811, -1.699, 8.922, -5.995, 9.033, -6.951, 1, 9.222, -8.576, 9.411, -8.88, 9.6, -8.88]}, {"Target": "Parameter", "Id": "Param217mao2", "Segments": [0, 0, 2, 0.333, 0, 0, 0.533, -3.718, 2, 0.567, -3.718, 0, 0.833, 8.243, 1, 0.878, 8.243, 0.922, 0.604, 0.967, 0.003, 1, 1.034, -0.899, 1.1, -0.896, 1.167, -0.896, 2, 2.467, -0.896, 0, 2.8, -3.24, 2, 7.133, -3.24, 2, 7.6, -3.24, 2, 8.067, -3.24, 1, 8.156, -3.24, 8.244, -2.71, 8.333, -2.683, 1, 8.455, -2.646, 8.578, -2.656, 8.7, -2.616, 1, 8.811, -2.579, 8.922, -2.187, 9.033, -1.729, 1, 9.222, -0.95, 9.411, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param202mao2", "Segments": [0, 0, 2, 0.333, 0, 0, 0.533, 30, 2, 0.567, 30, 1, 0.578, 30, 0.589, 22.236, 0.6, 15.107, 1, 0.633, -6.28, 0.667, -14.244, 0.7, -14.244, 1, 0.744, -14.244, 0.789, 5.371, 0.833, 21.936, 1, 0.855, 30.219, 0.878, 29.7, 0.9, 29.7, 1, 0.922, 29.7, 0.945, 24.156, 0.967, 24, 1, 1.034, 23.532, 1.1, 23.455, 1.167, 23.455, 2, 2.467, 23.455, 0, 2.8, 13.38, 2, 7.133, 13.38, 2, 7.6, 13.38, 2, 8.067, 13.38, 0, 8.333, 11.061, 0, 8.7, 30, 0, 8.867, -8.697, 1, 8.922, -8.697, 8.978, -8.032, 9.033, -6.714, 1, 9.222, -2.234, 9.411, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param203mao2", "Segments": [0, 0, 2, 0.333, 0, 0, 0.533, 9, 2, 0.567, 9, 0, 0.7, -7.08, 2, 0.833, -7.08, 0, 1.167, -14.309, 2, 2.467, -14.309, 0, 2.8, -23.64, 2, 7.133, -23.64, 2, 7.6, -23.64, 2, 8.067, -23.64, 1, 8.156, -23.64, 8.244, -24.116, 8.333, -19.515, 1, 8.566, -7.437, 8.8, 6.21, 9.033, 6.21, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param204mao2", "Segments": [0, 0, 2, 0.333, 0, 0, 0.533, -13, 2, 0.567, -13, 0, 0.7, -14.88, 0, 0.833, 0, 0, 1.167, -0.921, 2, 2.467, -0.921, 0, 2.8, -6.96, 2, 7.133, -6.96, 2, 7.6, -6.96, 2, 8.067, -6.96, 1, 8.156, -6.96, 8.244, -6.568, 8.333, -5.761, 1, 8.755, -1.928, 9.178, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param199mao2", "Segments": [0, 0, 2, 0.333, 0, 0, 0.533, 30, 2, 0.567, 30, 1, 0.578, 30, 0.589, 17.926, 0.6, 9.35, 1, 0.633, -16.377, 0.667, -24.978, 0.7, -24.978, 1, 0.744, -24.978, 0.789, 3.047, 0.833, 16.362, 1, 0.855, 23.019, 0.878, 21.48, 0.9, 21.48, 1, 0.922, 21.48, 0.945, 21.394, 0.967, 21, 1, 1.034, 19.817, 1.1, 19.073, 1.167, 19.073, 2, 2.467, 19.073, 0, 2.8, -16.56, 2, 7.133, -16.56, 2, 7.6, -16.56, 2, 8.067, -16.56, 1, 8.156, -16.56, 8.244, -17.246, 8.333, -13.683, 1, 8.455, -8.783, 8.578, 28.236, 8.7, 28.236, 1, 8.811, 28.236, 8.922, 4.604, 9.033, 2.94, 1, 9.222, 0.112, 9.411, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param200mao2", "Segments": [0, 0, 2, 0.333, 0, 0, 0.533, 1, 2, 0.567, 1, 0, 0.7, -12.703, 0, 0.9, -4.302, 0, 0.967, -11, 0, 1.167, -10.743, 2, 2.467, -10.743, 0, 2.8, -6, 2, 7.133, -6, 2, 7.6, -6, 2, 8.067, -6, 1, 8.156, -6, 8.244, -5.663, 8.333, -4.967, 1, 8.755, -1.663, 9.178, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param201mao2", "Segments": [0, 0, 2, 0.333, 0, 0, 0.533, -28, 2, 0.567, -28, 0, 0.9, -11.977, 1, 0.922, -11.977, 0.945, -11.98, 0.967, -12, 1, 1.034, -12.061, 1.1, -12.103, 1.167, -12.103, 2, 2.467, -12.103, 0, 2.8, -14, 2, 7.133, -14, 2, 7.6, -14, 2, 8.067, -14, 1, 8.156, -14, 8.244, -13.196, 8.333, -11.572, 1, 8.755, -3.862, 9.178, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param208mao2", "Segments": [0, 0, 2, 0.333, 0, 0, 0.533, 12.18, 2, 0.567, 12.18, 1, 0.611, 12.18, 0.656, 8.195, 0.7, 7.276, 1, 0.856, 4.061, 1.011, 3.182, 1.167, 3.182, 2, 2.467, 3.182, 0, 2.8, -2, 2, 7.133, -2, 2, 7.6, -2, 2, 8.067, -2, 1, 8.156, -2, 8.244, -2.142, 8.333, -1.656, 1, 8.455, -0.988, 8.578, 5.474, 8.7, 5.474, 0, 9.033, -3.361, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param209mao2", "Segments": [0, 0, 2, 0.333, 0, 0, 0.533, 21.6, 2, 0.567, 21.6, 0, 1.167, 17.475, 2, 2.467, 17.475, 0, 2.8, 26, 2, 7.133, 26, 2, 7.6, 26, 2, 8.067, 26, 1, 8.156, 26, 8.244, 22.992, 8.333, 21.459, 1, 8.455, 19.35, 8.578, 18.825, 8.7, 16.709, 1, 8.811, 14.785, 8.922, 10.116, 9.033, 7.178, 1, 9.222, 2.184, 9.411, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param210mao2", "Segments": [0, 0, 2, 0.333, 0, 0, 0.533, 4.3, 2, 0.567, 4.3, 0, 0.7, -1.461, 0, 1.167, -1.135, 2, 2.467, -1.135, 0, 2.8, 1, 2, 7.133, 1, 2, 7.6, 1, 2, 8.067, 1, 0, 8.333, 0.828, 0, 8.7, 14.021, 1, 8.811, 14.021, 8.922, 14.428, 9.033, 12.981, 1, 9.222, 10.521, 9.411, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param205mao2", "Segments": [0, 0, 2, 0.333, 0, 0, 0.533, 29.88, 2, 0.567, 29.88, 1, 0.611, 29.88, 0.656, 6.661, 0.7, 4.512, 1, 0.856, -3.01, 1.011, -4.886, 1.167, -4.886, 2, 2.467, -4.886, 0, 2.8, -17, 2, 7.133, -17, 2, 7.6, -17, 2, 8.067, -17, 1, 8.156, -17, 8.244, -16.987, 8.333, -14.045, 1, 8.455, -10.001, 8.578, -2.527, 8.7, 6.47, 1, 8.811, 14.649, 8.922, 26.487, 9.033, 26.487, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param206mao2", "Segments": [0, 0, 2, 0.333, 0, 0, 0.533, 13.08, 2, 0.567, 13.08, 0, 0.7, -3.583, 0, 1.167, -1.257, 2, 2.467, -1.257, 0, 2.8, 14, 2, 7.133, 14, 2, 7.6, 14, 2, 8.067, 14, 0, 8.333, 11.572, 0, 8.7, 14.003, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param207mao2", "Segments": [0, 0, 2, 0.333, 0, 0, 0.533, -26, 2, 0.567, -26, 0, 1.167, -21.366, 2, 2.467, -21.366, 0, 2.8, -9, 2, 7.133, -9, 2, 7.6, -9, 2, 8.067, -9, 1, 8.156, -9, 8.244, -8.49, 8.333, -7.447, 1, 8.755, -2.49, 9.178, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param212mao2", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, 27, 0, 0.567, 0, 0, 0.667, 27, 0, 0.8, 0, 2, 3, 0, 0, 3.1, 27, 0, 3.233, 0, 0, 3.333, 27, 0, 3.467, 0, 0, 4.333, 27, 0, 5.667, 0, 2, 7.133, 0, 2, 7.6, 0, 2, 8.067, 0, 2, 8.333, 0, 2, 9.6, 0]}, {"Target": "Parameter", "Id": "Param213mao2", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, 15, 0, 0.567, 0, 0, 0.667, 15, 0, 0.8, 0, 2, 3, 0, 0, 3.1, 15, 0, 3.233, 0, 0, 3.333, 15, 0, 3.467, 0, 0, 4.333, 15, 0, 5.667, 0, 2, 7.133, 0, 2, 7.6, 0, 2, 8.067, 0, 2, 8.333, 0, 2, 9.6, 0]}, {"Target": "Parameter", "Id": "Param214mao2", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, -21, 0, 0.567, 0, 0, 0.667, -21, 0, 0.8, 0, 2, 3, 0, 0, 3.1, -21, 0, 3.233, 0, 0, 3.333, -21, 0, 3.467, 0, 0, 4.333, -21, 0, 5.667, 0, 2, 7.133, 0, 2, 7.6, 0, 2, 8.067, 0, 2, 8.333, 0, 2, 9.6, 0]}, {"Target": "Parameter", "Id": "Param215mao2", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, -16, 0, 0.567, 0, 0, 0.667, -16, 0, 0.8, 0, 2, 3, 0, 0, 3.1, -16, 0, 3.233, 0, 0, 3.333, -16, 0, 3.467, 0, 0, 4.333, -16, 0, 5.667, 0, 2, 7.133, 0, 2, 7.6, 0, 2, 8.067, 0, 2, 8.333, 0, 2, 9.6, 0]}, {"Target": "Parameter", "Id": "Param264", "Segments": [0, 0, 2, 0.9, 0, 2, 0.933, 1, 1, 1.011, 6.786, 1.089, 10, 1.167, 10, 2, 8.333, 10, 1, 8.444, 10, 8.556, 7.054, 8.667, 1, 2, 8.7, 0, 2, 9.6, 0]}, {"Target": "Parameter", "Id": "Param100", "Segments": [0, 0, 2, 0.333, 0, 2, 0.4, 0, 2, 0.567, 0, 0, 0.833, -29.82, 0, 1.167, 0, 2, 2.467, 0, 2, 2.8, 0, 0, 3.233, 21.06, 0, 3.9, 5.265, 0, 4.633, 21.06, 0, 5.5, 5.265, 0, 6.133, 21.06, 1, 6.322, 21.06, 6.511, 11.849, 6.7, 5.265, 1, 6.844, 0.23, 6.989, 0, 7.133, 0, 2, 7.6, 0, 2, 8.067, 0, 2, 8.333, 0, 2, 9.6, 0]}, {"Target": "Parameter", "Id": "Param108", "Segments": [0, 0, 2, 0.333, 0, 2, 0.4, 0, 2, 0.567, 0, 2, 0.833, 0, 2, 1.167, 0, 2, 1.3, 0, 0, 1.5, 9, 0, 1.733, 0, 0, 1.967, 9, 0, 2.167, 0, 0, 2.333, 9, 0, 2.467, 0, 2, 2.8, 0, 2, 3.233, 0, 1, 3.322, 0, 3.411, 6.662, 3.5, 7.594, 1, 3.633, 8.992, 3.767, 9, 3.9, 9, 0, 4.3, -4.39, 1, 4.411, -4.39, 4.522, -3.292, 4.633, 0, 1, 4.922, 8.558, 5.211, 13.5, 5.5, 13.5, 0, 5.767, -12, 1, 5.889, -12, 6.011, -5.505, 6.133, 0, 1, 6.222, 4.004, 6.311, 8.345, 6.4, 8.679, 1, 6.5, 9.056, 6.6, 9, 6.7, 9, 0, 7.133, 0, 2, 7.6, 0, 2, 8.067, 0, 0, 8.333, 5.82, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param109", "Segments": [0, 0, 2, 0.333, 0, 2, 0.4, 0, 2, 0.567, 0, 2, 0.833, 0, 2, 1.167, 0, 0, 1.3, 30, 0, 1.5, 9.588, 0, 1.733, 30, 0, 1.967, 9.588, 0, 2.167, 30, 1, 2.222, 30, 2.278, 19.893, 2.333, 9.588, 1, 2.378, 1.344, 2.422, 0, 2.467, 0, 2, 2.8, 0, 0, 3.233, 30, 1, 3.322, 30, 3.411, 14.827, 3.5, 12.777, 1, 3.633, 9.703, 3.767, 9.588, 3.9, 9.588, 0, 4.633, 30, 0, 5.5, 9.588, 0, 6.133, 30, 1, 6.222, 30, 6.311, 19.976, 6.4, 16.822, 1, 6.5, 13.273, 6.6, 12.844, 6.7, 9.588, 1, 6.844, 4.885, 6.989, 0, 7.133, 0, 2, 7.6, 0, 2, 8.067, 0, 2, 8.333, 0, 2, 9.6, 0]}, {"Target": "Parameter", "Id": "Param107", "Segments": [0, 0, 2, 0.333, 0, 2, 0.4, 0, 2, 0.567, 0, 2, 0.833, 0, 0, 1, 0.169, 2, 1.167, 0.169, 0, 1.3, 0.145, 1, 1.522, 0.145, 1.745, 0.143, 1.967, 0.169, 1, 2.134, 0.188, 2.3, 0.307, 2.467, 0.307, 2, 2.8, 0.307, 0, 3.233, 0.243, 1, 3.322, 0.243, 3.411, 0.29, 3.5, 0.297, 1, 3.633, 0.307, 3.767, 0.307, 3.9, 0.307, 0, 4.633, 0.243, 0, 5.5, 0.307, 0, 6.133, 0.243, 1, 6.222, 0.243, 6.311, 0.289, 6.4, 0.298, 1, 6.5, 0.308, 6.6, 0.307, 6.7, 0.307, 0, 7.133, 0.243, 0, 7.4, 0.249, 0, 7.6, 0.154, 2, 8.067, 0.154, 0, 8.333, 0.213, 0, 8.733, 0, 0, 9.267, 0.695, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param284", "Segments": [0, 0, 2, 0.333, 0, 0, 1, 11, 0, 1.3, 7.053, 1, 1.522, 7.053, 1.745, 8.365, 1.967, 11, 1, 2.134, 12.977, 2.3, 13.971, 2.467, 13.971, 2, 2.8, 13.971, 0, 3.233, 10.369, 1, 3.322, 10.369, 3.411, 13.026, 3.5, 13.408, 1, 3.633, 13.981, 3.767, 13.971, 3.9, 13.971, 0, 4.633, 10.369, 0, 5.5, 13.971, 0, 6.133, 10.369, 1, 6.222, 10.369, 6.311, 12.972, 6.4, 13.466, 1, 6.5, 14.023, 6.6, 13.971, 6.7, 13.971, 1, 6.844, 13.971, 6.989, 12.688, 7.133, 10.369, 1, 7.444, 5.374, 7.756, 2.993, 8.067, 2.993, 0, 8.333, 3.732, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param101", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, -21, 0, 0.567, 0, 0, 0.667, -21, 0, 0.8, 0, 2, 3, 0, 0, 3.1, -21, 0, 3.233, 0, 0, 3.333, -21, 0, 3.467, 0, 0, 4.333, -21, 0, 5.667, 0, 2, 7.133, 0, 2, 7.6, 0, 2, 8.067, 0, 2, 8.333, 0, 2, 9.6, 0]}, {"Target": "Parameter", "Id": "Param106", "Segments": [0, 0, 2, 0.333, 0, 0, 1.667, 15.405, 0, 3, 0, 0, 4.333, 15.405, 0, 5.667, 0, 2, 7.133, 0, 2, 7.6, 0, 2, 8.067, 0, 2, 8.333, 0, 2, 9.6, 0]}, {"Target": "Parameter", "Id": "Param104", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, -16, 0, 0.567, 0, 0, 0.667, -16, 0, 0.8, 0, 2, 3, 0, 0, 3.1, -16, 0, 3.233, 0, 0, 3.333, -16, 0, 3.467, 0, 0, 4.333, -16, 0, 5.667, 0, 2, 7.133, 0, 2, 7.6, 0, 2, 8.067, 0, 2, 8.333, 0, 2, 9.6, 0]}, {"Target": "Parameter", "Id": "Param103", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, 27, 0, 0.567, 0, 0, 0.667, 27, 0, 0.8, 0, 2, 3, 0, 0, 3.1, 27, 0, 3.233, 0, 0, 3.333, 27, 0, 3.467, 0, 0, 4.333, 27, 0, 5.667, 0, 2, 7.133, 0, 2, 7.6, 0, 2, 8.067, 0, 2, 8.333, 0, 2, 9.6, 0]}, {"Target": "Parameter", "Id": "Param102", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, 15, 0, 0.567, 0, 0, 0.667, 15, 0, 0.8, 0, 2, 3, 0, 0, 3.1, 15, 0, 3.233, 0, 0, 3.333, 15, 0, 3.467, 0, 0, 4.333, 15, 0, 5.667, 0, 2, 7.133, 0, 2, 7.6, 0, 2, 8.067, 0, 2, 8.333, 0, 2, 9.6, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation71", "Segments": [0, 0, 2, 0.333, 0, 0, 0.867, 18, 0, 1.4, -18, 0, 1.933, 18, 0, 2.467, 0, 0, 3, 18, 0, 3.533, -18, 0, 4.067, 18, 0, 4.6, 0, 0, 5.133, 18, 0, 5.667, -18, 0, 6.2, 18, 0, 6.733, 0, 0, 7.267, 18, 0, 7.8, -18, 0, 8.633, 18, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation72", "Segments": [0, 8.085, 2, 0.333, 8.085, 0, 0.567, 0, 0, 1.1, 19, 0, 1.633, -19, 0, 2.167, 19, 0, 2.7, 0, 0, 3.233, 19, 0, 3.767, -19, 0, 4.3, 19, 1, 4.4, 19, 4.5, 15.205, 4.6, 8.085, 1, 4.678, 2.547, 4.755, 0, 4.833, 0, 0, 5.367, 19, 0, 5.9, -19, 0, 6.433, 19, 0, 6.967, 0, 0, 7.5, 19, 0, 8.267, -19, 0, 8.967, 19, 0, 9.6, 8.085]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation73", "Segments": [0, 11.915, 2, 0.333, 11.915, 0, 0.567, 0, 0, 1.1, 28, 0, 1.633, -28, 0, 2.167, 28, 0, 2.7, 0, 0, 3.233, 28, 0, 3.767, -28, 0, 4.3, 28, 1, 4.4, 28, 4.5, 22.408, 4.6, 11.915, 1, 4.678, 3.754, 4.755, 0, 4.833, 0, 0, 5.367, 28, 0, 5.9, -28, 0, 6.433, 28, 0, 6.967, 0, 0, 7.5, 28, 0, 8.267, -28, 0, 8.967, 28, 0, 9.6, 11.915]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation74", "Segments": [0, 12.341, 2, 0.333, 12.341, 0, 0.567, 0, 0, 1.1, 29, 0, 1.633, -29, 0, 2.167, 29, 0, 2.7, 0, 0, 3.233, 29, 0, 3.767, -29, 0, 4.3, 29, 1, 4.4, 29, 4.5, 23.209, 4.6, 12.341, 1, 4.678, 3.888, 4.755, 0, 4.833, 0, 0, 5.367, 29, 0, 5.9, -29, 0, 6.433, 29, 0, 6.967, 0, 0, 7.5, 29, 0, 8.267, -29, 0, 8.967, 29, 0, 9.6, 12.341]}, {"Target": "Parameter", "Id": "Param112", "Segments": [0, -30, 0, 0.333, 0, 2, 0.567, 0, 2, 1.333, 0, 2, 2.467, 0, 2, 2.8, 0, 2, 7.133, 0, 2, 7.6, 0, 2, 8.067, 0, 2, 8.333, 0, 0, 9.6, -30]}, {"Target": "Parameter", "Id": "Param272", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param76", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param293", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param273", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, -1, 0, 9.6, -1]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, -1, 0, 9.6, -1]}, {"Target": "Parameter", "Id": "ParamCheek", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param268", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param269", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param252", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param193", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param192", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param196", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param282", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param183", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param194", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param195", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param184", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param289", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param185", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param290", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param291", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param186", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param187", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param188", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param292", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param189", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param190", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param191", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param8", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param23", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param24", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param25", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param26", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param27", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param200", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param286", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param287", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param288", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param199", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param208", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param279", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param210", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param211", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param212", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param228", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param270", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param271", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param283", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param18", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param275", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param227", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param232", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param274", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param276", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param142", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param143", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param144", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param147", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param145", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param134", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param246", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param141", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param182", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param247", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param229", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param245", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param117", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param140", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param262", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param263", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param115", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param116", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param260", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param261", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param133", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param139", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param257", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param137", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param138", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param135", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param176", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param136", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param177", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param251", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param91", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param2", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param87", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param88", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param94", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param281", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param198mao2", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param219mao2", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation143mao2", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation144mao2", "Segments": [0, -10.82, 0, 9.6, -10.82]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation145mao2", "Segments": [0, 8.46, 0, 9.6, 8.46]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation146mao2", "Segments": [0, 28.83, 0, 9.6, 28.83]}, {"Target": "Parameter", "Id": "Param105", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param148", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param146", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param110", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param111", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param248", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param249", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param206", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param114", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param113", "Segments": [0, 1, 0, 9.6, 1]}, {"Target": "Parameter", "Id": "Param253", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param254", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param158", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param149", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param150", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param151", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param255", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param256", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param153", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param152", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param155", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param154", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param181", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param180", "Segments": [0, 0.5, 0, 9.6, 0.5]}, {"Target": "Parameter", "Id": "Param156", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param157", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param159", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param160", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param119", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param265", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param118", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param120", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param121", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param161", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param259", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param123", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param124", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param130", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param131", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param178", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param179", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param125", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param126", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param127", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param128", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param171", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param173", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param172", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param122", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param170", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param129", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param132", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param250", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param258", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param162", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param169", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param174", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param175", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param163", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param164", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param165", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param166", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param167", "Segments": [0, 0, 0, 9.6, 0]}, {"Target": "Parameter", "Id": "Param168", "Segments": [0, 0, 0, 9.6, 0]}], "UserData": [{"Time": 0.0, "Value": ""}, {"Time": 1.433, "Value": ""}, {"Time": 9.1, "Value": ""}]}