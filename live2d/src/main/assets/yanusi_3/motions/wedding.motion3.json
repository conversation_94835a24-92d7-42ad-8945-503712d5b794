{"Version": 3, "Meta": {"Duration": 24.267, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 355, "TotalSegmentCount": 7226, "TotalPointCount": 8412, "UserDataCount": 3, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 2, 0.333, 0, 1, 0.466, 0, 0.6, 1.913, 0.733, 7.11, 1, 0.8, 9.709, 0.866, 12, 0.933, 12, 2, 1.867, 12, 1, 2.7, 12, 3.534, 12.004, 4.367, 12.024, 1, 4.6, 12.03, 4.834, 12.507, 5.067, 12.507, 0, 5.733, 2, 0, 6.267, 4.46, 0, 6.533, 4.096, 0, 7, 9, 2, 10.2, 9, 2, 10.767, 9, 0, 10.967, -8, 0, 11.167, 12.4, 0, 11.367, -3, 0, 11.567, 11, 0, 11.8, -0.22, 0, 12.033, 7.917, 0, 12.467, 1.924, 0, 12.967, 10, 0, 13.267, 3, 2, 14.033, 3, 0, 14.8, 13.94, 0, 15.467, 8.6, 0, 16.2, 18.488, 0, 16.867, 10, 2, 18.5, 10, 2, 21.567, 10, 2, 23.3, 10, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 2, 0.333, 0, 0, 0.567, -8, 0, 0.833, 2.612, 0, 1.4, -6, 1, 1.556, -6, 1.711, -6.102, 1.867, -5.668, 1, 2.034, -5.203, 2.2, 3, 2.367, 3, 0, 3.333, -6, 0, 4.433, -3.54, 0, 4.6, -11.16, 0, 4.767, 5.367, 1, 4.878, 5.367, 4.989, 0.564, 5.1, -4.131, 1, 5.311, -13.052, 5.522, -16, 5.733, -16, 2, 6.267, -16, 0, 6.4, -18.262, 0, 6.9, -2, 0, 7.767, -5.72, 0, 9.333, -4.16, 2, 10.2, -4.16, 0, 10.333, -9, 0, 10.5, -4, 0, 10.767, -5.26, 2, 12.467, -5.26, 0, 12.667, -9, 1, 12.734, -9, 12.8, -0.723, 12.867, 1.319, 1, 12.945, 3.702, 13.022, 3.711, 13.1, 3.711, 0, 13.367, -14, 1, 13.489, -14, 13.611, -8.702, 13.733, -6, 1, 13.833, -3.79, 13.933, -3.844, 14.033, -3.844, 1, 14.155, -3.844, 14.278, -4.559, 14.4, -8, 1, 14.6, -13.631, 14.8, -18, 15, -18, 2, 15.333, -18, 0, 15.833, -15.72, 0, 16.4, -26.078, 0, 16.867, -7.02, 0, 17.467, -11.16, 0, 17.8, -5, 0, 18.033, -10.169, 1, 18.078, -10.169, 18.122, -8.337, 18.167, -8, 1, 18.278, -7.157, 18.389, -7.02, 18.5, -7.02, 1, 18.578, -7.02, 18.655, -7.31, 18.733, -10.169, 1, 18.789, -12.212, 18.844, -17.817, 18.9, -17.817, 0, 19.367, 2.612, 0, 19.7, -3.54, 1, 19.789, -3.54, 19.878, -2.567, 19.967, -1.226, 1, 20.034, -0.22, 20.1, 0.062, 20.167, 0.062, 0, 20.4, -6.326, 1, 20.489, -6.326, 20.578, -3.321, 20.667, 0.216, 1, 20.767, 4.196, 20.867, 5.367, 20.967, 5.367, 0, 21.267, -6, 1, 21.311, -6, 21.356, -2.754, 21.4, -1.659, 1, 21.5, 0.805, 21.6, 1.319, 21.7, 1.319, 0, 21.967, -3.298, 0, 22.2, 11.759, 0, 22.6, -2, 0, 22.9, 1.128, 2, 23.3, 1.128, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 2, 0.333, 0, 0, 0.8, 11.065, 2, 1.867, 11.065, 0, 3.333, 14.305, 2, 4.433, 14.305, 0, 4.633, 15.484, 0, 4.933, 12.545, 2, 5.067, 12.545, 0, 5.733, 8.585, 2, 6.267, 8.585, 0, 6.4, 7.411, 0, 6.9, 13, 0, 8.6, 11.573, 0, 10.2, 13, 1, 10.3, 13, 10.4, 11.229, 10.5, 10.44, 1, 10.589, 9.739, 10.678, 9.777, 10.767, 9.777, 0, 11.2, 10.456, 0, 12.467, 10.44, 0, 12.7, 12.56, 1, 12.822, 12.56, 12.945, 9.304, 13.067, 5.88, 1, 13.156, 3.39, 13.244, 3, 13.333, 3, 0, 13.633, 4, 2, 14.033, 4, 1, 14.355, 4, 14.678, 10.741, 15, 15.96, 1, 15.089, 17.4, 15.178, 16.959, 15.267, 16.959, 1, 15.334, 16.959, 15.4, 17.016, 15.467, 16.18, 1, 15.589, 14.647, 15.711, 12.658, 15.833, 12.658, 0, 16.233, 15.401, 1, 16.444, 15.401, 16.656, 13.73, 16.867, 8.961, 1, 16.956, 6.953, 17.044, 4.801, 17.133, 4.801, 1, 17.344, 4.801, 17.556, 6.101, 17.767, 8.585, 1, 17.856, 9.631, 17.944, 10.904, 18.033, 11.613, 1, 18.089, 12.057, 18.144, 12.066, 18.2, 12.066, 1, 18.3, 12.066, 18.4, 12.025, 18.5, 11.065, 1, 18.567, 10.426, 18.633, 7.865, 18.7, 7.865, 0, 19.167, 11.825, 1, 19.278, 11.825, 19.389, 10.649, 19.5, 9.265, 1, 19.7, 6.775, 19.9, 5.88, 20.1, 5.88, 1, 20.322, 5.88, 20.545, 6.744, 20.767, 8.585, 1, 20.911, 9.782, 21.056, 10.44, 21.2, 10.44, 2, 21.567, 10.44, 0, 22.2, 7.23, 0, 22.533, 16.959, 1, 22.611, 16.959, 22.689, 15.941, 22.767, 15.484, 1, 22.934, 14.505, 23.1, 14.305, 23.267, 14.305, 2, 23.333, 14.305, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param272", "Segments": [0, 0, 2, 0.333, 0, 2, 1.867, 0, 2, 4.433, 0, 2, 5.067, 0, 2, 6.267, 0, 2, 10.2, 0, 2, 10.767, 0, 2, 12.467, 0, 2, 14.033, 0, 0, 14.333, -3.9, 0, 14.733, 0, 2, 15.933, 0, 0, 16.4, -6.84, 2, 18.5, -6.84, 2, 21.567, -6.84, 2, 23.3, -6.84, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 2, 0.333, 1, 0, 0.433, 0, 2, 0.533, 0, 1, 0.578, 0, 0.622, 0.728, 0.667, 0.8, 1, 0.778, 0.979, 0.889, 1, 1, 1, 2, 1.867, 1, 0, 1.967, 0, 1, 2.011, 0, 2.056, 0.791, 2.1, 0.799, 1, 2.867, 0.941, 3.633, 1, 4.4, 1, 1, 4.433, 1, 4.467, 0.99, 4.5, 0.936, 1, 4.511, 0.918, 4.522, 0.83, 4.533, 0.7, 1, 4.544, 0.57, 4.556, 0, 4.567, 0, 2, 4.7, 0, 1, 4.733, 0, 4.767, 0.851, 4.8, 0.9, 1, 4.867, 0.997, 4.933, 1, 5, 1, 2, 5.133, 1, 0, 5.233, 0, 0, 5.333, 1, 1, 5.389, 1, 5.444, 0.991, 5.5, 0.9, 1, 5.522, 0.863, 5.545, 0, 5.567, 0, 1, 5.622, 0, 5.678, 0.647, 5.733, 0.7, 1, 5.911, 0.87, 6.089, 0.9, 6.267, 0.9, 0, 6.367, 0, 2, 6.467, 0, 1, 6.534, 0, 6.6, 0.604, 6.667, 0.7, 1, 6.745, 0.812, 6.822, 0.838, 6.9, 0.9, 1, 6.989, 0.971, 7.078, 1, 7.167, 1, 0, 7.333, 0.985, 0, 7.467, 1, 0, 7.567, 0.985, 0, 7.667, 1, 0, 7.8, 0.985, 0, 8.333, 1, 0, 8.5, 0.975, 0, 8.633, 1, 0, 8.767, 0.976, 0, 8.9, 1, 0, 9.033, 0.971, 0, 9.233, 1, 0, 9.4, 0.975, 0, 9.6, 1, 0, 9.767, 0.979, 0, 9.933, 1, 2, 10.2, 1, 0, 10.333, 0, 0, 10.5, 0.849, 2, 10.767, 0.849, 0, 10.967, 0, 2, 12.467, 0, 0, 12.667, 0.8, 0, 12.9, 0.7, 2, 13.2, 0.7, 1, 13.222, 0.7, 13.245, 0.695, 13.267, 0.6, 1, 13.278, 0.553, 13.289, 0, 13.3, 0, 2, 14.3, 0, 0, 14.667, 1, 0, 15.367, 0.615, 0, 15.9, 0.87, 0, 16.067, 0, 2, 16.5, 0, 0, 16.767, 0.9, 2, 17.067, 0.9, 0, 17.633, 1, 0, 18.1, 0.806, 0, 18.5, 0.9, 0, 18.7, 0, 2, 19, 0, 0, 19.3, 0.8, 2, 21.567, 0.8, 0, 22.267, 0.9, 0, 22.467, 0, 2, 23.3, 0, 0, 24.267, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, 1, 2, 0.533, 1, 2, 0.667, 1, 0, 1, 0, 2, 1.867, 0, 0, 2.1, 1, 0, 4.4, 0, 2, 4.5, 0, 2, 4.567, 0, 2, 4.7, 0, 0, 4.8, 1, 0, 5, 0, 2, 5.133, 0, 2, 5.567, 0, 0, 5.733, 1, 2, 6.267, 1, 0, 6.367, 0, 2, 6.467, 0, 2, 6.9, 0, 2, 10.2, 0, 2, 10.767, 0, 0, 10.967, 1, 2, 12.467, 1, 0, 12.9, 0, 2, 13.2, 0, 2, 13.267, 0, 0, 13.3, 1, 2, 14.3, 1, 2, 14.667, 1, 2, 15.9, 1, 2, 16.5, 1, 2, 18.1, 1, 2, 18.5, 1, 0, 18.7, 0, 2, 19, 0, 0, 19.3, 1, 2, 21.567, 1, 2, 23.3, 1, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 2, 0.333, 1, 0, 0.433, 0, 2, 0.533, 0, 1, 0.578, 0, 0.622, 0.728, 0.667, 0.8, 1, 0.778, 0.979, 0.889, 1, 1, 1, 1, 1.289, 1, 1.578, 0.993, 1.867, 0.973, 1, 1.9, 0.971, 1.934, 0, 1.967, 0, 1, 2.011, 0, 2.056, 0.79, 2.1, 0.798, 1, 2.867, 0.94, 3.633, 1, 4.4, 1, 1, 4.433, 1, 4.467, 0.979, 4.5, 0.898, 1, 4.511, 0.871, 4.522, 0.812, 4.533, 0.7, 1, 4.544, 0.588, 4.556, 0, 4.567, 0, 2, 4.7, 0, 1, 4.733, 0, 4.767, 0.851, 4.8, 0.9, 1, 4.867, 0.997, 4.933, 1, 5, 1, 2, 5.133, 1, 0, 5.233, 0, 0, 5.333, 1, 1, 5.389, 1, 5.444, 0.991, 5.5, 0.9, 1, 5.522, 0.863, 5.545, 0, 5.567, 0, 1, 5.622, 0, 5.678, 0.733, 5.733, 0.768, 1, 5.911, 0.882, 6.089, 0.9, 6.267, 0.9, 0, 6.367, 0, 2, 6.467, 0, 1, 6.534, 0, 6.6, 0.604, 6.667, 0.7, 1, 6.745, 0.812, 6.822, 0.838, 6.9, 0.9, 1, 6.989, 0.971, 7.078, 1, 7.167, 1, 0, 7.3, 0.977, 0, 7.433, 0.989, 0, 7.567, 0.962, 0, 7.7, 1, 0, 7.833, 0.98, 0, 8.333, 1, 0, 8.533, 0.983, 0, 8.667, 1, 0, 8.8, 0.98, 0, 8.933, 1, 0, 9.1, 0.987, 0, 9.3, 1, 0, 9.433, 0.989, 0, 9.633, 1, 0, 9.8, 0.969, 0, 9.967, 1, 2, 10.2, 1, 0, 10.333, 0, 0, 10.5, 0.858, 2, 10.767, 0.858, 0, 10.967, 0, 2, 12.467, 0, 0, 12.667, 0.8, 2, 12.9, 0.8, 2, 13.2, 0.8, 1, 13.222, 0.8, 13.245, 0.782, 13.267, 0.6, 1, 13.278, 0.509, 13.289, 0, 13.3, 0, 2, 14.033, 0, 2, 16.5, 0, 0, 16.767, 0.9, 2, 17.067, 0.9, 0, 17.633, 1, 0, 18.1, 0.823, 0, 18.5, 0.9, 0, 18.7, 0, 2, 19, 0, 0, 19.3, 0.8, 2, 21.567, 0.8, 0, 22.267, 0.9, 0, 22.467, 0, 2, 23.3, 0, 0, 24.267, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, 1, 2, 0.533, 1, 1, 0.578, 1, 0.622, 1.038, 0.667, 0.9, 1, 0.778, 0.556, 0.889, 0, 1, 0, 2, 1.867, 0, 0, 2.1, 1, 0, 4.4, 0, 2, 4.5, 0, 2, 4.567, 0, 2, 4.7, 0, 0, 4.8, 1, 0, 5, 0, 2, 5.133, 0, 2, 5.567, 0, 0, 5.733, 1, 2, 6.267, 1, 0, 6.367, 0, 2, 6.467, 0, 2, 6.9, 0, 2, 10.2, 0, 2, 10.767, 0, 0, 10.967, 1, 2, 12.467, 1, 0, 12.9, 0, 2, 13.2, 0, 2, 13.267, 0, 0, 13.3, 1, 2, 14.033, 1, 2, 16.5, 1, 2, 18.1, 1, 2, 18.5, 1, 0, 18.7, 0, 2, 19, 0, 0, 19.3, 1, 2, 21.567, 1, 2, 23.3, 1, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param76", "Segments": [0, 0, 2, 0.333, 0, 2, 1.867, 0, 0, 2.1, -30, 2, 4.433, -30, 0, 4.767, 0, 2, 5, 0, 2, 5.133, 0, 2, 6.267, 0, 2, 6.933, 0, 0, 7.1, -3.42, 0, 7.233, 0, 0, 7.367, -3.42, 0, 7.5, 0, 0, 7.633, -3.42, 0, 7.767, 0, 0, 7.9, -3.42, 0, 8.033, 0, 0, 8.167, -3.42, 0, 8.3, 0, 0, 8.433, -3.42, 0, 8.567, 0, 0, 8.733, -3.42, 0, 8.867, 0, 0, 9, -3.42, 0, 9.133, 0, 0, 9.267, -3.42, 0, 9.4, 0, 0, 9.533, -3.42, 0, 9.667, 0, 0, 9.833, -3.42, 0, 9.967, 0, 0, 10.1, -3.42, 0, 10.233, 0, 2, 10.767, 0, 2, 12.467, 0, 0, 12.667, -30, 2, 14.2, -30, 2, 18.5, -30, 2, 21.567, -30, 2, 23.3, -30, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param293", "Segments": [0, 0, 2, 0.333, 0, 2, 0.567, 0, 2, 1.867, 0, 2, 2.1, 0, 2, 4.433, 0, 1, 4.544, 0, 4.656, -0.001, 4.767, -0.1, 1, 4.845, -0.169, 4.922, -0.444, 5, -0.444, 2, 5.133, -0.444, 0, 5.333, 0, 2, 6.267, 0, 2, 10.2, 0, 0, 10.5, -0.35, 2, 10.767, -0.35, 0, 11, 0, 2, 12.467, 0, 2, 14.2, 0, 2, 18.5, 0, 2, 21.567, 0, 2, 23.3, 0, 2, 24.267, 0]}, {"Target": "Parameter", "Id": "Param273", "Segments": [0, 0, 2, 0.333, 0, 2, 1.867, 0, 2, 4.433, 0, 2, 5, 0, 2, 5.133, 0, 0, 5.333, 0.4, 2, 6.267, 0.4, 0, 6.9, 0, 2, 7.4, 0, 2, 10.2, 0, 2, 10.767, 0, 2, 12.467, 0, 0, 12.9, 0.6, 2, 14.2, 0.6, 1, 14.9, 0.6, 15.6, 0.58, 16.3, 0.5, 1, 16.456, 0.482, 16.611, 0.3, 16.767, 0.3, 2, 18.5, 0.3, 2, 21.567, 0.3, 2, 23.3, 0.3, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 2, 0.333, 0, 2, 0.633, 0, 0, 0.8, -0.379, 0, 2.467, -0.178, 0, 2.567, -0.195, 0, 2.633, -0.123, 0, 2.733, -0.153, 0, 2.8, -0.075, 0, 2.9, -0.145, 0, 3, -0.123, 0, 3.067, -0.153, 0, 3.167, -0.075, 0, 3.233, -0.145, 0, 3.333, -0.123, 0, 3.433, -0.153, 0, 3.5, -0.075, 0, 3.6, -0.145, 0, 3.667, -0.123, 0, 3.767, -0.153, 0, 3.867, -0.075, 0, 3.933, -0.145, 0, 4.033, -0.123, 0, 4.1, -0.153, 0, 4.433, -0.075, 0, 4.733, -0.5, 0, 4.9, -0.2, 2, 5.067, -0.2, 0, 5.333, -0.8, 2, 5.533, -0.8, 0, 5.6, -0.6, 2, 5.733, -0.6, 2, 5.967, -0.6, 0, 6.033, -0.682, 2, 6.267, -0.682, 0, 6.9, -0.1, 2, 10.2, -0.1, 2, 10.767, -0.1, 2, 12.467, -0.1, 2, 14.2, -0.1, 0, 14.733, -0.2, 2, 18.5, -0.2, 0, 19.267, -0.1, 2, 21.567, -0.1, 2, 23.3, -0.1, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 2, 0.333, 0, 0, 0.633, -0.4, 1, 0.689, -0.4, 0.744, -0.107, 0.8, -0.1, 1, 1.356, -0.027, 1.911, 0, 2.467, 0, 0, 2.567, -0.008, 0, 2.633, 0.012, 0, 2.733, -0.08, 2, 2.8, -0.08, 0, 2.9, -0.114, 0, 3, 0.012, 0, 3.067, -0.08, 2, 3.167, -0.08, 0, 3.233, -0.114, 0, 3.333, 0.012, 0, 3.433, -0.08, 2, 3.5, -0.08, 0, 3.6, -0.114, 0, 3.667, 0.012, 0, 3.767, -0.08, 2, 3.867, -0.08, 0, 3.933, -0.114, 0, 4.033, 0.012, 0, 4.1, -0.08, 2, 4.433, -0.08, 0, 4.733, -0.1, 0, 4.9, 0, 2, 5.067, 0, 0, 5.333, -0.4, 2, 5.533, -0.4, 2, 5.6, -0.4, 2, 5.733, -0.4, 2, 5.967, -0.4, 2, 6.033, -0.4, 2, 6.267, -0.4, 0, 6.9, 0, 2, 10.2, 0, 2, 10.767, 0, 2, 12.467, 0, 2, 14.2, 0, 2, 14.733, 0, 2, 18.5, 0, 0, 19.267, -0.2, 2, 21.567, -0.2, 0, 22.167, 0, 0, 23.3, -0.2, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 2, 0.333, 0, 0, 0.467, -0.3, 2, 0.533, -0.3, 2, 0.967, -0.3, 2, 1.867, -0.3, 0, 3.367, -0.148, 2, 4.433, -0.148, 0, 5.067, -0.3, 2, 6.267, -0.3, 0, 6.9, -0.158, 2, 10.2, -0.158, 2, 10.767, -0.158, 2, 12.467, -0.158, 0, 12.733, -0.196, 2, 14.2, -0.196, 2, 16.367, -0.196, 2, 16.667, -0.196, 2, 18.5, -0.196, 2, 21.567, -0.196, 2, 23.3, -0.196, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 2, 0.333, 0, 0, 0.467, -0.5, 2, 0.533, -0.5, 0, 0.967, 0, 1, 1.267, 0, 1.567, -0.019, 1.867, -0.072, 1, 1.9, -0.078, 1.934, -0.314, 1.967, -0.314, 1, 2.789, -0.314, 3.611, -0.299, 4.433, -0.234, 1, 4.644, -0.217, 4.856, -0.072, 5.067, -0.072, 0, 5.767, -0.242, 2, 6.267, -0.242, 0, 6.5, -0.472, 0, 6.9, -0.24, 2, 10.2, -0.24, 2, 10.767, -0.24, 2, 12.467, -0.24, 0, 12.733, 0, 2, 14.2, 0, 0, 14.833, -0.19, 2, 16.367, -0.19, 0, 16.667, 0, 2, 18.5, 0, 0, 18.8, -0.192, 2, 19.033, -0.192, 0, 19.333, 0, 2, 21.567, 0, 2, 23.3, 0, 2, 24.267, 0]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 2, 0.333, 0, 0, 0.467, -0.3, 2, 0.533, -0.3, 0, 0.967, -0.2, 2, 1.867, -0.2, 0, 3.367, -0.058, 2, 4.433, -0.058, 0, 5.067, -0.2, 2, 6.267, -0.2, 0, 6.9, -0.126, 2, 10.2, -0.126, 2, 10.767, -0.126, 2, 12.467, -0.126, 0, 12.733, -0.188, 2, 14.2, -0.188, 2, 16.367, -0.188, 2, 16.667, -0.188, 2, 18.5, -0.188, 2, 21.567, -0.188, 2, 23.3, -0.188, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 2, 0.333, 0, 0, 0.467, -0.5, 2, 0.533, -0.5, 0, 0.967, 0, 1, 1.267, 0, 1.567, -0.028, 1.867, -0.106, 1, 1.9, -0.115, 1.934, -0.326, 1.967, -0.326, 1, 2.789, -0.326, 3.611, -0.308, 4.433, -0.234, 1, 4.644, -0.215, 4.856, -0.106, 5.067, -0.106, 0, 5.767, -0.268, 2, 6.267, -0.268, 0, 6.5, -0.504, 0, 6.9, -0.228, 2, 10.2, -0.228, 2, 10.767, -0.228, 2, 12.467, -0.228, 0, 12.733, 0, 2, 14.2, 0, 0, 14.833, -0.2, 2, 16.367, -0.2, 0, 16.667, 0, 2, 18.5, 0, 0, 18.8, -0.178, 2, 19.033, -0.178, 0, 19.333, 0, 2, 21.567, 0, 2, 23.3, 0, 2, 24.267, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 2, 0.333, 0, 0, 0.467, -0.7, 1, 0.556, -0.7, 0.644, -0.042, 0.733, 0, 1, 1.111, 0.178, 1.489, 0.22, 1.867, 0.22, 0, 1.967, 0, 2, 3.367, 0, 2, 4.433, 0, 0, 5.067, 0.22, 2, 6.267, 0.22, 0, 6.9, 0, 2, 10.2, 0, 2, 10.767, 0, 2, 12.467, 0, 2, 12.9, 0, 0, 14.2, 0.242, 2, 16.367, 0.242, 0, 16.667, 0.1, 2, 18.5, 0.1, 2, 19.033, 0.1, 0, 19.333, 0.2, 2, 21.567, 0.2, 2, 23.3, 0.2, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 2, 0.333, 0, 0, 0.467, -0.9, 0, 0.733, 0, 2, 1.867, 0, 2, 4.433, 0, 2, 5.067, 0, 2, 6.267, 0, 2, 10.2, 0, 2, 10.767, 0, 2, 12.467, 0, 2, 12.9, 0, 0, 14.2, 0.244, 2, 16.367, 0.244, 0, 16.667, 0.2, 2, 18.5, 0.2, 2, 19.033, 0.2, 2, 19.333, 0.2, 2, 21.567, 0.2, 2, 23.3, 0.2, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, -1, 2, 0.333, -1, 1, 0.378, -1, 0.422, -0.971, 0.467, -0.8, 1, 0.556, -0.457, 0.644, -0.2, 0.733, -0.2, 2, 1.867, -0.2, 0, 1.967, 0, 0, 2.467, -0.2, 0, 4.433, 1, 1, 4.555, 1, 4.678, -0.64, 4.8, -0.7, 1, 5.011, -0.803, 5.222, -0.8, 5.433, -0.8, 2, 6.267, -0.8, 0, 6.9, -0.9, 0, 7.033, -0.788, 0, 7.167, -0.982, 0, 7.3, -0.788, 0, 7.433, -0.982, 0, 7.567, -0.788, 0, 7.7, -0.982, 0, 7.867, -0.788, 0, 8, -0.982, 0, 8.167, -0.788, 0, 8.3, -0.982, 2, 10.2, -0.982, 2, 10.767, -0.982, 2, 12.467, -0.982, 1, 12.611, -0.982, 12.756, -1.02, 12.9, -0.9, 1, 13.333, -0.54, 13.767, 0, 14.2, 0, 0, 14.833, -0.6, 2, 16.367, -0.6, 0, 16.667, 0, 2, 18.5, 0, 2, 21.567, 0, 2, 23.3, 0, 0, 24.267, -1]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, -1, 2, 0.333, -1, 1, 0.378, -1, 0.422, -0.876, 0.467, -0.7, 1, 0.556, -0.347, 0.644, -0.2, 0.733, -0.2, 2, 1.867, -0.2, 0, 1.967, 0, 0, 2.467, -0.2, 0, 4.433, 0.6, 0, 4.8, -1, 0, 5.433, -0.8, 2, 6.267, -0.8, 2, 6.9, -0.8, 0, 7.033, -0.702, 0, 7.167, -0.878, 0, 7.3, -0.702, 0, 7.433, -0.878, 0, 7.567, -0.702, 0, 7.7, -0.878, 0, 7.867, -0.702, 0, 8, -0.878, 0, 8.167, -0.702, 0, 8.3, -0.878, 2, 10.2, -0.878, 2, 10.767, -0.878, 2, 12.467, -0.878, 0, 12.9, -1, 0, 14.2, 0, 0, 14.833, -0.7, 2, 16.367, -0.7, 0, 16.667, 0, 2, 18.5, 0, 2, 21.567, 0, 2, 23.3, 0, 0, 24.267, -1]}, {"Target": "Parameter", "Id": "Param", "Segments": [0, -30, 2, 0.333, -30, 0, 0.567, 0, 0, 0.633, -4, 0, 0.933, 0, 0, 1.867, -14, 0, 2.433, 0, 2, 2.667, 0, 0, 2.933, 2, 0, 3.3, 0, 0, 4.433, 13, 0, 4.767, 0, 2, 4.833, 0, 0, 5.033, -12, 1, 5.444, -12, 5.856, -9.29, 6.267, 0, 1, 6.378, 2.511, 6.489, 12, 6.6, 12, 1, 6.711, 12, 6.822, 2.149, 6.933, 2, 1, 8.022, 0.536, 9.111, 0, 10.2, 0, 0, 10.5, 30, 2, 10.767, 30, 0, 10.967, -11, 2, 11.167, -11, 0, 11.267, -1, 0, 12.467, -11, 0, 12.7, 0, 2, 12.9, 0, 2, 13.2, 0, 0, 14.2, -11, 2, 14.5, -11, 0, 14.667, 0, 0, 16.2, -3.872, 0, 16.867, 0, 1, 16.922, 0, 16.978, 0.239, 17.033, -0.609, 1, 17.3, -4.68, 17.566, -8.643, 17.833, -8.643, 0, 18, 0, 0, 18.2, -1, 2, 18.5, -1, 1, 18.611, -1, 18.722, -0.965, 18.833, -1.328, 1, 19.022, -1.946, 19.211, -3.357, 19.4, -4.365, 1, 19.567, -5.254, 19.733, -6.167, 19.9, -7.021, 1, 19.956, -7.306, 20.011, -7.445, 20.067, -7.793, 1, 20.245, -8.908, 20.422, -9.675, 20.6, -9.675, 0, 20.733, 0, 0, 20.967, -2.663, 1, 21.034, -2.663, 21.1, -2.569, 21.167, -2, 1, 21.3, -0.861, 21.434, 0, 21.567, 0, 2, 22.033, 0, 2, 22.6, 0, 2, 23.3, 0, 0, 24.267, -30]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 1, 2, 0.333, 1, 0, 0.567, -1.5, 1, 0.689, -1.5, 0.811, -1.135, 0.933, -1.1, 1, 1.244, -1.011, 1.556, -1, 1.867, -1, 2, 2.433, -1, 2, 2.667, -1, 2, 2.933, -1, 2, 3.3, -1, 2, 4.433, -1, 0, 4.767, -1.5, 2, 4.833, -1.5, 0, 5.033, -1, 0, 6.267, -1.5, 0, 6.6, -0.5, 0, 6.733, -1, 0, 6.833, -0.3, 0, 6.933, -1, 2, 7.067, -1, 0, 7.2, -0.6, 1, 7.322, -0.6, 7.445, -0.948, 7.567, -1, 1, 8.445, -1.371, 9.322, -1.5, 10.2, -1.5, 2, 10.767, -1.5, 0, 10.967, 0.4, 2, 11.167, 0.4, 0, 11.267, -0.8, 1, 11.322, -0.8, 11.378, 0.023, 11.433, 0.1, 1, 11.511, 0.208, 11.589, 0.233, 11.667, 0.3, 1, 11.745, 0.367, 11.822, 0.41, 11.9, 0.5, 1, 11.944, 0.551, 11.989, 0.7, 12.033, 0.7, 2, 12.133, 0.7, 2, 12.467, 0.7, 0, 12.7, -0.4, 1, 12.767, -0.4, 12.833, -0.396, 12.9, -0.1, 1, 12.944, 0.097, 12.989, 1, 13.033, 1, 0, 13.367, 0.6, 0, 13.833, 0.8, 2, 14.2, 0.8, 1, 14.3, 0.8, 14.4, 0.801, 14.5, 0.662, 1, 14.556, 0.585, 14.611, -0.9, 14.667, -0.9, 0, 14.833, 0.5, 0, 15.1, -0.1, 0, 15.2, 0.6, 1, 15.533, 0.6, 15.867, 0.595, 16.2, 0.578, 1, 16.256, 0.575, 16.311, -0.1, 16.367, -0.1, 0, 16.5, 0.8, 1, 16.544, 0.8, 16.589, 0.752, 16.633, 0.4, 1, 16.655, 0.224, 16.678, -0.4, 16.7, -0.4, 0, 16.867, 0, 0, 17.033, -1.1, 1, 17.133, -1.1, 17.233, -0.281, 17.333, 0.2, 1, 17.433, 0.681, 17.533, 0.7, 17.633, 0.7, 1, 17.7, 0.7, 17.766, 0.706, 17.833, 0.673, 1, 17.889, 0.646, 17.944, -0.8, 18, -0.8, 1, 18.067, -0.8, 18.133, 0.132, 18.2, 0.3, 1, 18.3, 0.552, 18.4, 0.682, 18.5, 0.8, 1, 18.578, 0.892, 18.655, 0.9, 18.733, 0.9, 1, 18.766, 0.9, 18.8, 0.901, 18.833, 0.898, 1, 18.9, 0.893, 18.966, 0.2, 19.033, 0.2, 0, 19.233, 0.8, 1, 19.289, 0.8, 19.344, 0.762, 19.4, 0.6, 1, 19.478, 0.374, 19.555, 0.2, 19.633, 0.2, 0, 19.9, 0.6, 1, 19.956, 0.6, 20.011, 0.601, 20.067, 0.597, 1, 20.245, 0.585, 20.422, 0.568, 20.6, 0.547, 1, 20.644, 0.541, 20.689, -0.2, 20.733, -0.2, 0, 20.967, -0.066, 0, 21.067, -0.9, 1, 21.1, -0.9, 21.134, 0.16, 21.167, 0.3, 1, 21.3, 0.86, 21.434, 1, 21.567, 1, 0, 21.767, 0.3, 0, 22.033, 0.9, 0, 22.6, 0.5, 2, 23.3, 0.5, 0, 24.267, 1]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 2, 0.333, 0, 1, 0.411, 0, 0.489, 0.152, 0.567, 0.3, 1, 0.689, 0.533, 0.811, 0.6, 0.933, 0.6, 1, 1.155, 0.6, 1.378, 0.411, 1.6, 0.2, 1, 1.778, 0.032, 1.955, 0, 2.133, 0, 0, 2.433, 0.4, 0, 2.667, 0, 1, 2.756, 0, 2.844, 0.176, 2.933, 0.3, 1, 3.055, 0.47, 3.178, 0.5, 3.3, 0.5, 1, 3.678, 0.5, 4.055, 0.444, 4.433, 0.3, 1, 4.466, 0.287, 4.5, 0, 4.533, 0, 0, 4.833, 0.4, 0, 5.033, 0, 0, 5.367, 0.4, 0, 5.533, 0.2, 0, 5.7, 0.5, 0, 5.933, 0.2, 2, 6.267, 0.2, 0, 6.4, 0, 0, 6.6, 0.6, 0, 6.667, 0.1, 0, 6.733, 0.3, 0, 6.833, 0.2, 0, 6.933, 0.4, 0, 7.067, 0.2, 0, 7.2, 0.6, 0, 7.367, 0.2, 0, 7.567, 0.7, 1, 7.656, 0.7, 7.744, 0.451, 7.833, 0.4, 1, 8.022, 0.291, 8.211, 0.279, 8.4, 0.279, 0, 10.2, 0.4, 2, 10.767, 0.4, 0, 10.833, 0, 0, 10.967, 0.5, 1, 11.011, 0.5, 11.056, 0.471, 11.1, 0.3, 1, 11.122, 0.214, 11.145, 0, 11.167, 0, 0, 11.267, 0.5, 0, 11.433, 0.1, 0, 11.567, 0.5, 0, 11.667, 0.3, 1, 11.7, 0.3, 11.734, 0.432, 11.767, 0.5, 1, 11.811, 0.591, 11.856, 0.6, 11.9, 0.6, 0, 12.033, 0.2, 2, 12.133, 0.2, 0, 12.467, 0.3, 0, 12.5, 0, 0, 12.7, 0.9, 0, 12.833, 0.3, 0, 12.9, 0.5, 0, 13.033, 0.3, 0, 13.2, 0.7, 0, 13.367, 0.2, 2, 13.833, 0.2, 2, 14.2, 0.2, 0, 14.5, 0, 0, 14.667, 0.6, 0, 14.833, 0.3, 0, 15.033, 0.9, 1, 15.055, 0.9, 15.078, 0.905, 15.1, 0.7, 1, 15.122, 0.495, 15.145, 0, 15.167, 0, 1, 15.178, 0, 15.189, 0.295, 15.2, 0.4, 1, 15.211, 0.505, 15.222, 0.5, 15.233, 0.5, 0, 15.333, 0.3, 0, 15.5, 0.7, 1, 15.578, 0.7, 15.655, 0.639, 15.733, 0.4, 1, 15.8, 0.195, 15.866, 0, 15.933, 0, 1, 16.022, 0, 16.111, -0.001, 16.2, 0.015, 1, 16.256, 0.025, 16.311, 0.5, 16.367, 0.5, 0, 16.5, 0.2, 0, 16.633, 0.5, 0, 16.7, 0.3, 1, 16.756, 0.3, 16.811, 0.5, 16.867, 0.6, 1, 16.922, 0.7, 16.978, 0.7, 17.033, 0.7, 0, 17.167, 0.2, 0, 17.333, 0.6, 0, 17.467, 0.3, 0, 17.633, 0.7, 0, 17.833, 0.4, 1, 17.889, 0.4, 17.944, 0.617, 18, 0.7, 1, 18.067, 0.799, 18.133, 0.8, 18.2, 0.8, 1, 18.256, 0.8, 18.311, 0.686, 18.367, 0.335, 1, 18.378, 0.264, 18.389, 0, 18.4, 0, 2, 18.5, 0, 1, 18.611, 0, 18.722, -0.001, 18.833, 0.016, 1, 18.9, 0.027, 18.966, 0.5, 19.033, 0.5, 0, 19.233, 0.2, 0, 19.4, 0.6, 0, 19.5, 0.3, 2, 19.633, 0.3, 2, 19.733, 0.3, 0, 19.767, 0, 2, 19.9, 0, 1, 19.956, 0, 20.011, -0.003, 20.067, 0.014, 1, 20.111, 0.027, 20.156, 0.6, 20.2, 0.6, 0, 20.3, 0.3, 0, 20.433, 0.8, 0, 20.6, 0.4, 0, 20.733, 0.7, 0, 20.867, 0.4, 0, 20.967, 0.8, 0, 21.067, 0.5, 0, 21.167, 0.8, 1, 21.211, 0.8, 21.256, 0.712, 21.3, 0.4, 1, 21.311, 0.322, 21.322, 0, 21.333, 0, 2, 21.4, 0, 1, 21.456, 0, 21.511, -0.005, 21.567, 0.013, 1, 21.634, 0.034, 21.7, 0.5, 21.767, 0.5, 0, 21.833, 0, 0, 22.033, 0.7, 1, 22.078, 0.7, 22.122, 0.508, 22.167, 0.2, 1, 22.189, 0.046, 22.211, 0, 22.233, 0, 0, 22.367, 0.7, 0, 22.5, 0.2, 2, 22.6, 0.2, 0, 23.3, 0.24, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param252", "Segments": [0, 0, 2, 0.333, 0, 2, 1.867, 0, 2, 4.433, 0, 2, 6.267, 0, 0, 9.267, 1, 2, 21.567, 1, 2, 23.3, 1, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param196", "Segments": [0, 0, 2, 0.333, 0, 2, 1.867, 0, 2, 3.033, 0, 0, 3.1, 0.3, 0, 3.167, 0, 0, 3.233, 0.3, 0, 3.3, 0, 0, 3.367, 0.3, 0, 3.433, 0, 0, 3.5, 0.3, 0, 3.567, 0, 0, 3.633, 0.3, 0, 3.7, 0, 0, 3.767, 0.3, 0, 3.833, 0, 0, 3.9, 0.3, 0, 3.967, 0, 0, 4.033, 0.3, 0, 4.1, 0, 0, 4.167, 0.3, 0, 4.233, 0, 0, 4.3, 0.3, 0, 4.367, 0, 0, 4.433, 0.3, 0, 4.5, 0, 0, 4.567, 0.3, 0, 4.633, 0, 2, 5.067, 0, 0, 5.167, 1, 0, 5.267, 0, 0, 5.367, 1, 0, 5.467, 0, 0, 5.567, 1, 0, 5.667, 0, 0, 5.767, 1, 0, 5.867, 0, 0, 5.967, 1, 0, 6.067, 0, 0, 6.167, 1, 0, 6.267, 0, 0, 6.367, 1, 0, 6.467, 0, 0, 6.567, 1, 0, 6.667, 0, 0, 6.767, 1, 0, 6.867, 0, 0, 6.967, 1, 2, 10.2, 1, 2, 10.767, 1, 2, 12.467, 1, 2, 14.2, 1, 2, 18.9, 1, 0, 19, -0.032, 0, 19.1, 1, 0, 19.2, -0.032, 0, 19.3, 1, 0, 19.4, -0.032, 0, 19.5, 1, 0, 19.6, -0.032, 0, 19.7, 1, 0, 19.8, -0.032, 0, 19.9, 1, 0, 20, -0.032, 0, 20.1, 1, 0, 20.2, -0.032, 0, 20.3, 1, 0, 20.4, -0.032, 0, 20.5, 1, 0, 20.6, -0.032, 0, 20.7, 1, 0, 20.8, -0.032, 0, 20.9, 1, 0, 21, -0.032, 0, 21.1, 1, 0, 21.2, -0.032, 0, 21.333, 1, 0, 21.433, -0.032, 0, 21.533, 1, 0, 21.633, -0.032, 0, 21.733, 1, 0, 21.833, -0.032, 0, 21.933, 1, 0, 22.033, -0.032, 0, 22.167, 1, 0, 22.267, -0.032, 0, 22.367, 1, 0, 22.467, -0.032, 2, 23.3, -0.032, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param194", "Segments": [0, 0, 2, 0.333, 0, 2, 1.867, 0, 2, 2.833, 0, 0, 4.5, 1, 0, 4.567, 0, 2, 6.267, 0, 2, 6.4, 0, 0, 6.967, 1, 2, 10.2, 1, 2, 10.767, 1, 0, 10.933, 0, 2, 12.467, 0, 0, 12.767, 1, 2, 13.267, 1, 0, 13.3, 0, 0, 14.633, 1, 2, 16.433, 1, 0, 16.733, 0.6, 2, 18.5, 0.6, 2, 20.7, 0.6, 2, 21.567, 0.6, 0, 23.3, 0, 2, 24.267, 0]}, {"Target": "Parameter", "Id": "Param195", "Segments": [0, 0, 2, 0.333, 0, 2, 1.867, 0, 2, 4.433, 0, 2, 6.267, 0, 2, 6.433, 0, 0, 7.3, 30, 3, 7.333, 0, 0, 7.4, 1.08, 0, 7.5, 0, 2, 7.567, 0, 0, 7.667, 0.51, 0, 7.733, 0, 0, 7.833, 0.51, 0, 7.933, 0, 0, 8, 0.51, 0, 8.1, 0, 0, 8.167, 0.51, 0, 8.267, 0, 0, 8.367, 0.51, 0, 8.433, 0, 0, 8.533, 0.51, 0, 8.6, 0, 0, 8.7, 0.51, 0, 8.8, 0, 0, 8.867, 0.51, 0, 8.967, 0, 0, 9.067, 0.51, 0, 9.167, 0, 0, 9.267, 0.51, 0, 9.367, 0, 0, 9.467, 0.51, 0, 9.567, 0, 0, 9.667, 0.51, 0, 9.767, 0, 0, 9.867, 0.51, 0, 9.967, 0, 0, 10.067, 0.51, 2, 10.2, 0.51, 2, 10.767, 0.51, 2, 12.467, 0.51, 2, 13.267, 0.51, 2, 14.2, 0.51, 2, 18.5, 0.51, 2, 21.567, 0.51, 2, 23.3, 0.51, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param184", "Segments": [0, 0, 2, 0.333, 0, 2, 1.867, 0, 2, 4.433, 0, 2, 6.267, 0, 2, 7.433, 0, 0, 8.133, 1, 2, 10.2, 1, 2, 10.767, 1, 2, 12.467, 1, 2, 14.2, 1, 2, 16, 1, 0, 16.333, 0, 2, 18.5, 0, 2, 21.567, 0, 2, 23.3, 0, 2, 24.267, 0]}, {"Target": "Parameter", "Id": "Param289", "Segments": [0, 0, 2, 0.333, 0, 2, 1.867, 0, 2, 4.433, 0, 2, 6.267, 0, 0, 7.433, -0.9, 0, 8.133, 0, 2, 10.2, 0, 2, 10.767, 0, 2, 12.467, 0, 2, 14.2, 0, 2, 18.5, 0, 2, 21.567, 0, 2, 23.3, 0, 2, 24.267, 0]}, {"Target": "Parameter", "Id": "Param185", "Segments": [0, 0, 2, 0.333, 0, 2, 1.867, 0, 2, 4.433, 0, 2, 6.267, 0, 2, 7.433, 0, 0, 7.533, 0.676, 0, 7.667, 0, 0, 7.8, 0.676, 0, 7.933, 0, 0, 8.067, 0.676, 0, 8.2, 0, 0, 8.367, 0.753, 0, 8.5, -0.1, 0, 8.633, 0.618, 0, 8.733, 0, 0, 8.867, 0.719, 0, 9, 0, 0, 9.133, 0.719, 0, 9.267, 0, 0, 9.4, 0.591, 0, 9.533, 0, 0, 9.667, 0.676, 0, 9.8, 0, 0, 9.967, 0.784, 0, 10.1, 0, 1, 10.133, 0, 10.167, 0.254, 10.2, 0.57, 1, 10.211, 0.675, 10.222, 0.676, 10.233, 0.676, 0, 10.367, 0, 0, 10.5, 0.591, 0, 10.633, 0, 0, 10.767, 0.676, 0, 10.9, 0, 2, 12.467, 0, 2, 14.2, 0, 2, 18.5, 0, 2, 21.567, 0, 2, 23.3, 0, 2, 24.267, 0]}, {"Target": "Parameter", "Id": "Param290", "Segments": [0, 0, 2, 0.333, 0, 2, 1.867, 0, 2, 4.433, 0, 2, 6.267, 0, 0, 7.9, 0.1, 0, 8.467, 0, 0, 8.533, 1, 2, 9.1, 1, 0, 9.167, 0, 2, 10.2, 0, 2, 10.767, 0, 2, 12.467, 0, 2, 14.2, 0, 2, 18.5, 0, 2, 21.567, 0, 2, 23.3, 0, 2, 24.267, 0]}, {"Target": "Parameter", "Id": "Param291", "Segments": [0, 0, 2, 0.333, 0, 2, 1.867, 0, 2, 4.433, 0, 2, 6.267, 0, 2, 7.9, 0, 2, 8.467, 0, 0, 8.533, 1, 2, 10.2, 1, 2, 10.767, 1, 2, 12.467, 1, 2, 14.2, 1, 2, 14.933, 1, 0, 15.467, 0, 2, 18.5, 0, 2, 21.567, 0, 2, 23.3, 0, 2, 24.267, 0]}, {"Target": "Parameter", "Id": "Param186", "Segments": [0, 0, 2, 0.333, 0, 2, 1.867, 0, 2, 4.433, 0, 2, 6.267, 0, 0, 7.9, 6, 0, 8.467, 0, 1, 8.7, 10, 8.934, 20, 9.167, 30, 2, 10.2, 30, 2, 10.767, 30, 2, 12.467, 30, 2, 14.2, 30, 2, 18.5, 30, 2, 21.567, 30, 2, 23.3, 30, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param187", "Segments": [0, 0, 2, 0.333, 0, 2, 1.867, 0, 2, 4.433, 0, 2, 6.267, 0, 2, 7.9, 0, 2, 8.467, 0, 2, 9.1, 0, 0, 9.167, 1, 2, 10.2, 1, 2, 10.767, 1, 0, 11.133, 0, 2, 12.467, 0, 2, 14.2, 0, 2, 18.5, 0, 2, 21.567, 0, 2, 23.3, 0, 2, 24.267, 0]}, {"Target": "Parameter", "Id": "Param188", "Segments": [0, 0, 2, 0.333, 0, 2, 1.867, 0, 2, 4.433, 0, 2, 6.267, 0, 2, 7.9, 0, 2, 8.467, 0, 2, 9.1, 0, 0, 9.3, 13.659, 2, 10.2, 13.659, 2, 10.767, 13.659, 2, 12.467, 13.659, 2, 14.2, 13.659, 2, 18.5, 13.659, 2, 21.567, 13.659, 2, 23.3, 13.659, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param292", "Segments": [0, 0, 2, 0.333, 0, 2, 1.867, 0, 2, 4.433, 0, 2, 6.267, 0, 2, 7.9, 0, 2, 9.1, 0, 0, 9.5, 1.721, 1, 9.511, 1.721, 9.522, 0.102, 9.533, 0.019, 1, 9.633, -0.724, 9.733, -1, 9.833, -1, 0, 10.033, -0.069, 1, 10.089, -0.069, 10.144, -0.254, 10.2, -0.468, 1, 10.211, -0.51, 10.222, -0.5, 10.233, -0.5, 2, 10.767, -0.5, 2, 12.467, -0.5, 2, 14.2, -0.5, 2, 18.5, -0.5, 2, 21.567, -0.5, 2, 23.3, -0.5, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param189", "Segments": [0, 0, 2, 0.333, 0, 2, 1.867, 0, 2, 4.433, 0, 2, 6.267, 0, 2, 7.9, 0, 2, 9.5, 0, 0, 9.533, 1, 2, 9.867, 1, 0, 10, 0, 2, 10.2, 0, 2, 10.767, 0, 2, 12.467, 0, 2, 14.2, 0, 2, 18.5, 0, 2, 21.567, 0, 2, 23.3, 0, 2, 24.267, 0]}, {"Target": "Parameter", "Id": "Param190", "Segments": [0, 0, 2, 0.333, 0, 2, 1.867, 0, 2, 4.433, 0, 2, 6.267, 0, 2, 7.9, 0, 0, 9.533, 20.7, 2, 10.2, 20.7, 2, 10.767, 20.7, 2, 12.467, 20.7, 2, 14.2, 20.7, 2, 18.5, 20.7, 2, 21.567, 20.7, 2, 23.3, 20.7, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param191", "Segments": [0, 0, 2, 0.333, 0, 2, 1.867, 0, 2, 4.433, 0, 2, 6.267, 0, 2, 7.9, 0, 1, 8.444, 0, 8.989, -0.165, 9.533, -1.08, 1, 9.689, -1.342, 9.844, -14, 10, -14, 2, 10.2, -14, 2, 10.767, -14, 2, 12.467, -14, 2, 14.2, -14, 2, 18.5, -14, 2, 21.567, -14, 2, 23.3, -14, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 2, 0.333, 0, 0, 0.8, -21, 2, 1.867, -21, 2, 4.433, -21, 0, 5, -12, 2, 5.1, -12, 0, 5.9, -23.52, 0, 6.267, -19.8, 0, 6.733, -30, 0, 7.233, -21, 0, 7.8, -24.552, 0, 10.067, -22.5, 2, 10.2, -22.5, 2, 10.7, -22.5, 1, 10.722, -22.5, 10.745, -22.475, 10.767, -19.922, 1, 10.834, -12.264, 10.9, -6, 10.967, -6, 2, 12.467, -6, 0, 13.033, -21, 2, 14.1, -21, 0, 14.8, 1, 1, 15.078, 1, 15.355, -1.67, 15.633, -10, 1, 15.778, -14.332, 15.922, -19, 16.067, -19, 1, 16.256, -19, 16.444, -3.278, 16.633, 0.332, 1, 16.8, 3.518, 16.966, 3.152, 17.133, 3.152, 2, 18.5, 3.152, 2, 21.567, 3.152, 2, 23.3, 3.152, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 2, 0.333, 0, 0, 0.567, -3.48, 0, 0.8, 1, 0, 1.133, -3.92, 1, 1.378, -3.92, 1.622, -2.898, 1.867, 1, 1, 2.1, 4.72, 2.334, 8, 2.567, 8, 0, 3.467, -9, 0, 4.1, -8.1, 2, 4.433, -8.1, 0, 4.633, -21, 0, 4.833, 4.82, 0, 5.1, -5.598, 2, 6.267, -5.598, 0, 6.733, -14.976, 0, 7.133, -9, 0, 7.633, -12.44, 0, 8.967, -3.48, 0, 10.067, -13.74, 2, 10.2, -13.74, 0, 10.333, -16.327, 0, 10.533, -6.66, 1, 10.589, -6.66, 10.644, -7.876, 10.7, -9, 1, 10.722, -9.45, 10.745, -9.106, 10.767, -9.819, 1, 10.945, -15.516, 11.122, -21, 11.3, -21, 0, 12.167, -2.099, 1, 12.267, -2.099, 12.367, -2.564, 12.467, -4.379, 1, 12.511, -5.186, 12.556, -6.66, 12.6, -6.66, 1, 12.689, -6.66, 12.778, 5.991, 12.867, 11.769, 1, 12.945, 16.825, 13.022, 16.783, 13.1, 16.783, 0, 13.4, -16, 1, 13.5, -16, 13.6, -14.877, 13.7, -12.88, 1, 13.833, -10.217, 13.967, -9, 14.1, -9, 0, 14.4, -18.88, 1, 14.533, -18.88, 14.667, -12.751, 14.8, -8, 1, 14.867, -5.625, 14.933, -5.278, 15, -3.56, 1, 15.167, 0.736, 15.333, 3.34, 15.5, 3.34, 0, 16.133, -17.6, 0, 17, -6, 0, 17.467, -18, 0, 17.8, 1, 0, 18.2, -14.976, 0, 18.5, -10.671, 0, 18.7, -14.976, 0, 19.3, -1, 1, 19.467, -1, 19.633, -10.323, 19.8, -11, 1, 19.911, -11.451, 20.022, -11.272, 20.133, -11.59, 1, 20.211, -11.813, 20.289, -14.976, 20.367, -14.976, 0, 20.767, -8.484, 0, 21.267, -18.484, 0, 21.567, -13.872, 0, 21.9, -20, 0, 22.267, 4.82, 0, 22.6, -5.598, 1, 22.833, -5.598, 23.067, -3.205, 23.3, -1.636, 1, 23.333, -1.412, 23.367, -1.561, 23.4, -1.458, 1, 23.689, -0.568, 23.978, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 2, 0.333, 0, 2, 1.867, 0, 2, 4.433, 0, 2, 5.1, 0, 2, 6.267, 0, 2, 10.2, 0, 2, 10.7, 0, 2, 10.767, 0, 2, 12.467, 0, 2, 14.1, 0, 2, 18.5, 0, 0, 18.8, 1.92, 0, 19.433, 1.32, 2, 21.567, 1.32, 0, 21.9, 3.84, 1, 22.033, 3.84, 22.167, 2.189, 22.3, 0.776, 1, 22.444, -0.754, 22.589, -1.02, 22.733, -1.02, 2, 23.3, -1.02, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param267", "Segments": [0, 0, 2, 0.333, 0, 0, 0.567, 6.84, 1, 0.645, 6.84, 0.722, -0.481, 0.8, -0.6, 1, 1.156, -1.145, 1.511, -1.277, 1.867, -1.277, 0, 3.367, 1.436, 0, 4.367, 0.958, 2, 4.433, 0.958, 0, 5.1, -1.277, 2, 6.267, -1.277, 0, 6.767, 2.743, 1, 7.089, 2.743, 7.411, 1.79, 7.733, 1.436, 1, 8.155, 0.973, 8.578, 0.958, 9, 0.958, 2, 10.2, 0.958, 2, 10.7, 0.958, 2, 10.767, 0.958, 2, 12.467, 0.958, 2, 14.1, 0.958, 0, 14.967, 7.557, 0, 15.533, 5.817, 0, 16.2, 8.3, 0, 16.9, 3.611, 1, 17, 3.611, 17.1, 4.107, 17.2, 5.071, 1, 17.3, 6.036, 17.4, 6.506, 17.5, 6.506, 0, 17.867, 4.677, 0, 18.267, 5.817, 0, 18.5, 5.315, 0, 18.8, 5.781, 0, 19.333, -0.003, 1, 19.533, -0.003, 19.733, -0.007, 19.933, 0, 1, 20.422, 0.017, 20.911, 3.837, 21.4, 3.837, 2, 21.567, 3.837, 2, 23.3, 3.837, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param3", "Segments": [0, 0, 2, 0.333, 0, 0, 0.8, 14.64, 2, 1.867, 14.64, 0, 3.467, 4.68, 0, 4.1, 5.534, 2, 4.433, 5.534, 1, 4.633, 5.534, 4.833, 3.246, 5.033, -0.106, 1, 5.055, -0.478, 5.078, -0.56, 5.1, -0.684, 1, 5.289, -1.741, 5.478, -2.206, 5.667, -2.206, 2, 6.267, -2.206, 0, 7.333, -3.336, 0, 10.2, -1.596, 1, 10.289, -1.596, 10.378, -3.561, 10.467, -5.126, 1, 10.545, -6.496, 10.622, -6.636, 10.7, -6.636, 2, 10.767, -6.636, 2, 12.467, -6.636, 2, 14.1, -6.636, 0, 14.833, -13.476, 0, 15.567, -7.476, 0, 16.2, -7.5, 2, 17.033, -7.5, 0, 17.8, -5, 0, 18.1, -9.657, 0, 18.5, -5.133, 0, 19.8, -14.733, 2, 21.567, -14.733, 2, 23.3, -14.733, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param4", "Segments": [0, 0, 2, 0.333, 0, 0, 0.533, -1.453, 0, 0.8, 1.472, 0, 1.067, 0.22, 0, 1.867, 1.48, 0, 3.467, -4.64, 0, 4.1, -3.44, 2, 4.433, -3.44, 0, 4.567, -5.467, 0, 4.733, -0.652, 0, 4.9, -2.328, 0, 5.667, 2.012, 1, 5.867, 2.012, 6.067, 2.009, 6.267, 1.472, 1, 6.378, 1.174, 6.489, -3.148, 6.6, -3.148, 0, 8.333, -0.363, 1, 8.955, -0.363, 9.578, -0.996, 10.2, -2.522, 1, 10.244, -2.631, 10.289, -3.662, 10.333, -3.662, 0, 10.467, -2.328, 1, 10.545, -2.328, 10.622, -2.383, 10.7, -2.584, 1, 10.722, -2.641, 10.745, -2.816, 10.767, -3.041, 1, 10.978, -5.177, 11.189, -6.415, 11.4, -6.415, 0, 12.167, -3.581, 2, 12.467, -3.581, 0, 12.667, -5.981, 1, 12.745, -5.981, 12.822, -1.886, 12.9, 0.149, 1, 12.967, 1.893, 13.033, 1.879, 13.1, 1.879, 0, 13.333, -6.346, 1, 13.466, -6.346, 13.6, -2.982, 13.733, -1.875, 1, 13.855, -0.861, 13.978, -0.957, 14.1, -0.957, 0, 14.8, -10.635, 0, 15.533, -6.503, 0, 16.133, -10.635, 0, 16.767, -5.467, 0, 17.3, -8.589, 1, 17.422, -8.589, 17.545, -4.016, 17.667, -3.018, 1, 17.722, -2.564, 17.778, -2.746, 17.833, -2.746, 0, 18.1, -4.902, 1, 18.156, -4.902, 18.211, -4.694, 18.267, -4.251, 1, 18.334, -3.719, 18.4, -3.44, 18.467, -3.44, 2, 18.5, -3.44, 0, 18.8, -6.32, 0, 19.3, 0, 0, 19.8, -2.46, 0, 20.167, -1.453, 0, 20.367, -3.12, 0, 20.8, 0.954, 0, 21.267, -3.644, 0, 21.567, -0.86, 0, 21.9, -4.22, 0, 22.267, 1.66, 0, 22.533, -2.66, 0, 22.767, -1.7, 2, 23.3, -1.7, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param266", "Segments": [0, 0, 2, 0.333, 0, 0, 0.467, -11, 0, 0.833, 5.843, 0, 1.333, 2.956, 0, 1.833, 3.849, 2, 1.867, 3.849, 0, 2.5, 6, 0, 4.1, -3.951, 2, 4.433, -3.951, 0, 4.767, -15.711, 0, 4.967, -8.556, 0, 5.3, -10.244, 0, 5.7, -5.471, 0, 6.033, -9.662, 0, 6.3, -6.112, 0, 6.6, -9.662, 0, 7.4, -3.951, 0, 9.067, -10.244, 2, 10.2, -10.244, 0, 10.7, -3.951, 2, 10.767, -3.951, 2, 12.467, -3.951, 2, 14.1, -3.951, 0, 14.867, -7.611, 0, 15.567, -0.831, 2, 17.033, -0.831, 1, 17.189, -0.831, 17.344, -0.834, 17.5, -0.8, 1, 17.6, -0.778, 17.7, 2.073, 17.8, 2.073, 0, 18.133, -6.83, 0, 18.5, -3.033, 2, 21.567, -3.033, 2, 23.3, -3.033, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param5", "Segments": [0, 0, 2, 0.333, 0, 0, 0.533, 6, 0, 0.7, 2, 0, 1.067, 3.44, 2, 1.867, 3.44, 0, 2.5, 6.048, 1, 2.744, 6.048, 2.989, -0.848, 3.233, -2.383, 1, 3.633, -4.895, 4.033, -5.02, 4.433, -5.02, 0, 4.667, 0.92, 1, 4.778, 0.92, 4.889, -4.532, 5, -10.72, 1, 5.033, -12.576, 5.067, -12.46, 5.1, -12.46, 0, 5.667, -5.62, 1, 5.867, -5.62, 6.067, -5.54, 6.267, -7.96, 1, 6.478, -10.515, 6.689, -16.6, 6.9, -16.6, 0, 7.2, -15.52, 1, 7.789, -15.52, 8.378, -19.793, 8.967, -22.6, 1, 9.378, -24.559, 9.789, -24.465, 10.2, -24.465, 0, 10.3, -21.705, 0, 10.467, -25.065, 2, 10.7, -25.065, 2, 10.767, -25.065, 0, 11.067, -29.174, 0, 12.467, -22.24, 0, 13, -30, 0, 13.4, -16, 0, 13.7, -17.92, 2, 14.1, -17.92, 0, 14.867, -8, 1, 15.134, -8, 15.4, -10.485, 15.667, -12.62, 1, 15.856, -14.132, 16.044, -14.338, 16.233, -15.8, 1, 16.355, -16.746, 16.478, -21.498, 16.6, -23, 1, 16.744, -24.775, 16.889, -25.04, 17.033, -25.04, 0, 17.533, -19.34, 0, 17.8, -22.24, 1, 17.878, -22.24, 17.955, -20.707, 18.033, -18.564, 1, 18.089, -17.033, 18.144, -16.6, 18.2, -16.6, 0, 18.4, -17.92, 2, 18.5, -17.92, 0, 19.233, -28, 0, 19.7, -22.54, 2, 20.167, -22.54, 0, 20.433, -20.015, 0, 20.733, -21.686, 0, 21.567, -21.67, 1, 21.678, -21.67, 21.789, -23.459, 21.9, -24.44, 1, 21.978, -25.127, 22.055, -25.065, 22.133, -25.065, 0, 22.467, -4.88, 1, 22.534, -4.88, 22.6, -6.643, 22.667, -6.92, 1, 22.878, -7.798, 23.089, -8.607, 23.3, -9.033, 1, 23.344, -9.123, 23.389, -9.08, 23.433, -9.08, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param6", "Segments": [0, 0, 2, 0.333, 0, 0, 0.533, 24, 0, 0.7, 16, 0, 1.067, 17.56, 2, 1.867, 17.56, 0, 2.5, 25.669, 1, 2.744, 25.669, 2.989, 18.992, 3.233, 16.726, 1, 3.633, 13.017, 4.033, 12.64, 4.433, 12.64, 0, 4.667, 18.4, 1, 4.778, 18.4, 4.889, 17.02, 5, 14.44, 1, 5.033, 13.666, 5.067, 13.36, 5.1, 13.36, 0, 5.667, 24.34, 1, 5.867, 24.34, 6.067, 24.077, 6.267, 20.5, 1, 6.478, 16.724, 6.689, 11, 6.9, 11, 0, 7.2, 12.64, 1, 7.789, 12.64, 8.378, 4.838, 8.967, 4.06, 1, 9.378, 3.517, 9.789, 3.695, 10.2, 3.695, 0, 10.3, 6.095, 0, 10.467, 3.095, 2, 10.7, 3.095, 2, 10.767, 3.095, 0, 11.067, -3.205, 0, 12.467, 5.74, 0, 13, -0.2, 0, 13.4, 13, 0, 13.7, 10.24, 2, 14.1, 10.24, 0, 15, 25.12, 1, 15.167, 25.12, 15.333, 23.632, 15.5, 20.14, 1, 15.567, 18.743, 15.633, 17.726, 15.7, 17.726, 0, 16.267, 30, 0, 16.9, 11.52, 0, 17.533, 15.24, 0, 17.8, 12.64, 1, 17.878, 12.64, 17.955, 15.108, 18.033, 17.201, 1, 18.078, 18.397, 18.122, 18.4, 18.167, 18.4, 0, 18.4, 16, 2, 18.5, 16, 0, 19.233, 0, 0, 19.7, 4.68, 2, 20.167, 4.68, 0, 20.433, 6.647, 0, 20.733, 5.279, 1, 21.011, 5.279, 21.289, 5.444, 21.567, 6.33, 1, 21.678, 6.685, 21.789, 7.92, 21.9, 7.92, 0, 22.133, 0, 0, 22.467, 22.98, 1, 22.534, 22.98, 22.6, 20.155, 22.667, 19.98, 1, 22.878, 19.427, 23.089, 18.847, 23.3, 18.686, 1, 23.344, 18.652, 23.389, 18.719, 23.433, 18.66, 1, 23.711, 18.288, 23.989, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 2, 0.333, 0, 2, 0.967, 0, 0, 1.867, 0.6, 0, 2.6, 0, 0, 4.033, 0.565, 0, 5.1, 0, 2, 6.267, 0, 0, 7.767, 0.627, 0, 10.067, 0, 2, 10.2, 0, 2, 10.7, 0, 2, 10.767, 0, 2, 12.467, 0, 2, 14.1, 0, 2, 18.5, 0, 2, 21.567, 0, 2, 22.7, 0, 0, 23.3, 0.184, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param201", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.467, 1, 2, 1.867, 1, 2, 4.433, 1, 2, 6.267, 1, 2, 10.2, 1, 2, 10.767, 1, 2, 12.467, 1, 2, 14.2, 1, 2, 18.767, 1, 2, 18.8, 0, 2, 21.567, 0, 2, 23.4, 0, 2, 23.433, 0, 2, 24.267, 0]}, {"Target": "Parameter", "Id": "Param202", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.467, 0, 2, 1.867, 0, 2, 4.433, 0, 2, 6.267, 0, 2, 10.2, 0, 2, 10.767, 0, 2, 12.467, 0, 2, 14.2, 0, 2, 18.767, 1, 2, 18.8, 1, 2, 21.567, 1, 2, 23.4, 1, 2, 23.433, 0, 2, 24.267, 0]}, {"Target": "Parameter", "Id": "Param205", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.467, 0, 2, 1.867, 0, 2, 4.433, 0, 2, 6.267, 0, 2, 10.2, 0, 2, 10.767, 0, 2, 12.467, 0, 2, 14.2, 0, 2, 18.767, 0, 2, 18.8, 0, 2, 21.567, 0, 2, 23.4, 0, 2, 23.433, 0, 2, 24.267, 0]}, {"Target": "Parameter", "Id": "Param203", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.467, 0, 2, 1.867, 0, 2, 4.433, 0, 2, 6.267, 0, 2, 10.2, 0, 2, 10.767, 0, 2, 12.467, 0, 2, 14.2, 0, 2, 18.767, 0, 2, 18.8, 0, 2, 21.567, 0, 2, 23.4, 0, 2, 23.433, 0, 2, 24.267, 0]}, {"Target": "Parameter", "Id": "Param204", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.467, 0, 2, 1.867, 0, 2, 4.433, 0, 2, 6.267, 0, 2, 10.2, 0, 2, 10.767, 0, 2, 12.467, 0, 2, 14.2, 0, 2, 18.767, 1, 2, 18.8, 1, 2, 21.567, 1, 2, 23.4, 1, 2, 23.433, 0, 2, 24.267, 0]}, {"Target": "Parameter", "Id": "Param209", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.467, 1, 2, 1.867, 1, 2, 4.433, 1, 2, 6.267, 1, 2, 10.2, 1, 2, 10.767, 1, 2, 12.467, 1, 2, 14.2, 1, 2, 18.767, 1, 2, 18.8, 0, 2, 21.567, 0, 2, 23.4, 0, 2, 23.433, 0, 2, 24.267, 0]}, {"Target": "Parameter", "Id": "Param21", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.467, 0, 2, 1.867, 0, 2, 4.433, 0, 2, 6.267, 0, 2, 10.2, 0, 2, 10.767, 0, 2, 12.467, 0, 2, 14.2, 0, 2, 18.767, 0, 2, 21.567, 0, 2, 23.4, 0, 2, 23.433, 0, 2, 24.267, 0]}, {"Target": "Parameter", "Id": "Param213", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.467, 0, 2, 1.867, 0, 2, 4.433, 0, 2, 6.267, 0, 2, 10.2, 0, 2, 10.767, 0, 2, 12.467, 0, 2, 14.2, 0, 2, 18.767, 0, 2, 21.567, 0, 2, 23.4, 0, 2, 23.433, 0, 2, 24.267, 0]}, {"Target": "Parameter", "Id": "Param214", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.467, 1, 2, 1.867, 1, 2, 4.767, 1, 2, 6.267, 1, 2, 10.2, 1, 2, 10.767, 1, 2, 11.067, 0, 2, 12.9, 0, 2, 12.933, 0, 2, 14.5, 0, 2, 14.533, 1, 2, 18.767, 1, 2, 18.8, 0, 2, 21.567, 0, 2, 23.4, 0, 2, 23.433, 0, 2, 24.267, 0]}, {"Target": "Parameter", "Id": "Param215", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.467, 0, 2, 1.867, 0, 2, 4.767, 0, 2, 6.267, 0, 2, 10.2, 0, 2, 10.767, 0, 2, 12.9, 0, 2, 14.5, 0, 2, 18.767, 0, 2, 21.567, 0, 2, 23.4, 0, 2, 23.433, 0, 2, 24.267, 0]}, {"Target": "Parameter", "Id": "Param216", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.467, 0, 2, 1.867, 0, 2, 4.433, 0, 2, 6.267, 0, 2, 10.2, 0, 2, 10.767, 0, 2, 12.9, 0, 2, 12.933, 1, 2, 14.5, 1, 2, 14.533, 0, 2, 18.767, 0, 2, 21.567, 0, 2, 23.4, 0, 2, 23.433, 0, 2, 24.267, 0]}, {"Target": "Parameter", "Id": "Param217", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.467, 0, 2, 1.867, 0, 2, 4.433, 0, 2, 6.267, 0, 2, 10.2, 0, 2, 10.767, 0, 2, 11.067, 1, 2, 12.9, 1, 2, 12.933, 0, 2, 14.2, 0, 2, 18.767, 0, 2, 21.567, 0, 2, 23.4, 0, 2, 23.433, 0, 2, 24.267, 0]}, {"Target": "Parameter", "Id": "Param218", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.467, 0, 2, 1.867, 0, 2, 4.433, 0, 2, 6.267, 0, 2, 10.2, 0, 2, 10.767, 0, 2, 12.467, 0, 2, 14.2, 0, 2, 18.767, 0, 2, 21.567, 0, 2, 23.4, 0, 2, 23.433, 0, 2, 24.267, 0]}, {"Target": "Parameter", "Id": "Param219", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.467, 0, 2, 1.867, 0, 2, 4.433, 0, 2, 6.267, 0, 2, 10.2, 0, 2, 10.767, 0, 2, 12.467, 0, 2, 14.2, 0, 2, 18.767, 0, 2, 21.567, 0, 2, 23.4, 0, 2, 23.433, 0, 2, 24.267, 0]}, {"Target": "Parameter", "Id": "Param220", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.467, 0, 2, 1.867, 0, 2, 4.433, 0, 2, 6.267, 0, 2, 10.2, 0, 2, 10.767, 0, 2, 12.467, 0, 2, 14.2, 0, 2, 18.767, 0, 2, 21.567, 0, 2, 23.4, 0, 2, 23.433, 0, 2, 24.267, 0]}, {"Target": "Parameter", "Id": "Param9", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, -5.019, 0, 0.567, -0.459, 1, 0.645, -0.459, 0.722, -0.819, 0.8, -1.346, 1, 0.867, -1.797, 0.933, -2.481, 1, -2.547, 1, 1.289, -2.833, 1.578, -2.907, 1.867, -2.907, 2, 2.367, -2.907, 0, 3.1, -3.087, 0, 4.433, -2.787, 0, 4.667, -5.547, 0, 5, -1.887, 2, 5.133, -1.887, 0, 5.733, -3.567, 2, 6.267, -3.567, 0, 6.9, -4.407, 0, 7.467, -3.747, 0, 9.2, -4.527, 0, 10.2, -4.168, 0, 10.333, -4.528, 0, 10.533, -3.628, 2, 10.767, -3.628, 0, 11, -3.028, 0, 11.233, -4.048, 0, 11.467, -3.328, 0, 11.7, -4.047, 0, 11.967, -3.567, 0, 12.333, -3.687, 2, 12.467, -3.687, 0, 13.067, -2.427, 0, 13.433, -5.547, 0, 13.667, -4.107, 2, 14.2, -4.107, 0, 15, -5.787, 0, 15.8, -3.507, 1, 15.922, -3.507, 16.045, -4.272, 16.167, -4.587, 1, 16.422, -5.246, 16.678, -5.367, 16.933, -5.367, 0, 17.833, -3.087, 0, 18.2, -4.397, 0, 18.5, -3.635, 0, 18.833, -5.787, 1, 18.911, -5.787, 18.989, -2.988, 19.067, -2.907, 1, 19.234, -2.734, 19.4, -2.581, 19.567, -2.547, 1, 20.234, -2.412, 20.9, -2.375, 21.567, -2.375, 2, 23.3, -2.375, 0, 23.633, 0, 2, 24.267, 0]}, {"Target": "Parameter", "Id": "Param10", "Segments": [0, 0, 2, 0.333, 0, 1, 0.355, 0, 0.378, 1.918, 0.4, 6.253, 1, 0.411, 8.42, 0.422, 9.789, 0.433, 9.789, 0, 1, 8.04, 2, 1.867, 8.04, 2, 2.367, 8.04, 2, 3.1, 8.04, 2, 4.433, 8.04, 2, 5.133, 8.04, 2, 6.267, 8.04, 0, 6.9, 8.94, 2, 10.2, 8.94, 2, 10.767, 8.94, 2, 12.467, 8.94, 2, 14.2, 8.94, 2, 16.933, 8.94, 2, 17.833, 8.94, 2, 18.2, 8.94, 2, 18.5, 8.94, 0, 18.767, 11.45, 1, 18.778, 11.45, 18.789, 10.737, 18.8, 10.412, 1, 18.878, 8.136, 18.955, 4.62, 19.033, 4.018, 1, 19.3, 1.953, 19.566, 1.436, 19.833, 1.436, 1, 20.411, 1.436, 20.989, 1.519, 21.567, 1.8, 1, 21.678, 1.854, 21.789, 2.28, 21.9, 2.28, 1, 22.033, 2.28, 22.167, 2.024, 22.3, 1.8, 1, 22.4, 1.632, 22.5, 1.62, 22.6, 1.62, 2, 23.3, 1.62, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param11", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, -5.52, 0, 1, 0, 2, 1.867, 0, 2, 2.367, 0, 2, 3.1, 0, 2, 4.433, 0, 2, 5.133, 0, 2, 6.267, 0, 2, 10.2, 0, 2, 10.767, 0, 2, 12.467, 0, 2, 14.2, 0, 2, 16.933, 0, 2, 17.833, 0, 2, 18.2, 0, 2, 18.5, 0, 2, 19.567, 0, 2, 21.567, 0, 2, 23.3, 0, 2, 24.267, 0]}, {"Target": "Parameter", "Id": "Param12", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, -21.6, 0, 1, 0, 2, 1.867, 0, 2, 2.367, 0, 2, 3.1, 0, 2, 4.433, 0, 2, 5.133, 0, 2, 6.267, 0, 2, 10.2, 0, 2, 10.767, 0, 2, 12.467, 0, 2, 14.2, 0, 2, 16.933, 0, 2, 17.833, 0, 2, 18.2, 0, 2, 18.5, 0, 2, 21.567, 0, 2, 23.3, 0, 2, 24.267, 0]}, {"Target": "Parameter", "Id": "Param199", "Segments": [0, 0, 2, 0.333, 0, 2, 1, 0, 2, 1.867, 0, 2, 2.367, 0, 2, 3.1, 0, 2, 4.433, 0, 2, 5.133, 0, 2, 6.267, 0, 2, 10.2, 0, 2, 10.767, 0, 2, 12.467, 0, 2, 14.2, 0, 2, 16.933, 0, 2, 17.833, 0, 2, 18.2, 0, 2, 18.5, 0, 0, 18.8, 22.683, 0, 19.567, 19, 2, 23.3, 19, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param22", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, 1, 2, 1, 1, 2, 1.867, 1, 2, 2.367, 1, 2, 3.1, 1, 2, 4.433, 1, 2, 5.133, 1, 2, 6.267, 1, 2, 10.2, 1, 2, 10.767, 1, 2, 12.467, 1, 2, 14.2, 1, 2, 16.933, 1, 2, 17.833, 1, 2, 18.2, 1, 2, 18.5, 1, 2, 21.567, 1, 2, 23.3, 1, 2, 23.5, 1, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param13", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, -3.984, 0, 1, -0.06, 2, 1.867, -0.06, 2, 2.367, -0.06, 2, 3.1, -0.06, 2, 4.433, -0.06, 2, 5.133, -0.06, 0, 5.733, -14.4, 2, 6.267, -14.4, 0, 6.9, -6, 2, 10.2, -6, 2, 10.767, -6, 2, 12.467, -6, 2, 13.067, -6, 0, 13.433, -2.28, 2, 14.2, -2.28, 2, 16.933, -2.28, 2, 17.833, -2.28, 2, 18.2, -2.28, 2, 18.5, -2.28, 0, 18.8, -5.76, 0, 21.567, -2.28, 0, 21.933, -2.3, 1, 22.055, -2.3, 22.178, 1.545, 22.3, 3.58, 1, 22.4, 5.245, 22.5, 5.2, 22.6, 5.2, 0, 23.1, 3.34, 2, 23.3, 3.34, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param14", "Segments": [0, 0, 2, 0.333, 0, 1, 0.355, 0, 0.378, -9.375, 0.4, -14.964, 1, 0.411, -17.758, 0.422, -17.28, 0.433, -17.28, 0, 1, 0, 2, 1.867, 0, 2, 2.367, 0, 2, 3.1, 0, 2, 4.433, 0, 2, 5.133, 0, 2, 6.267, 0, 2, 10.2, 0, 2, 10.767, 0, 2, 12.467, 0, 2, 14.2, 0, 2, 16.933, 0, 2, 17.833, 0, 2, 18.2, 0, 2, 18.5, 0, 0, 18.8, -24.84, 1, 18.867, -24.84, 18.933, -23.383, 19, -17, 1, 19.1, -7.425, 19.2, 0, 19.3, 0, 2, 23.3, 0, 2, 24.267, 0]}, {"Target": "Parameter", "Id": "Param210", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, 4.997, 1, 0.555, 4.997, 0.678, -0.244, 0.8, -0.892, 1, 0.867, -1.246, 0.933, -1.215, 1, -1.323, 1, 1.289, -1.79, 1.578, -1.985, 1.867, -1.985, 1, 2.034, -1.985, 2.2, -2.159, 2.367, -1.507, 1, 2.611, -0.551, 2.856, 3.715, 3.1, 3.715, 0, 4.433, 3.535, 0, 4.667, 4.028, 0, 5, -1.385, 1, 5.056, -1.385, 5.111, -1.194, 5.167, -0.862, 1, 5.211, -0.596, 5.256, -0.485, 5.3, -0.485, 0, 5.733, -0.905, 2, 6.267, -0.905, 0, 6.9, 1.975, 0, 7.467, 1.495, 0, 9.2, 1.795, 0, 10.2, 1.376, 0, 10.333, 1.915, 0, 10.533, 1.315, 2, 10.767, 1.315, 0, 11, -11.765, 0, 11.233, -4.685, 0, 11.467, -10.92, 0, 11.7, -6.202, 0, 11.967, -9.527, 0, 12.333, -6.887, 2, 12.467, -6.887, 0, 12.7, -8.316, 1, 12.822, -8.316, 12.945, 3.452, 13.067, 4.393, 1, 13.189, 5.334, 13.311, 5.173, 13.433, 5.173, 2, 14.2, 5.173, 0, 14.367, 5.71, 1, 14.578, 5.71, 14.789, 1.43, 15, 0.313, 1, 15.267, -1.098, 15.533, -1.067, 15.8, -1.067, 1, 15.922, -1.067, 16.045, -0.504, 16.167, 0.193, 1, 16.311, 1.016, 16.456, 1.522, 16.6, 2.353, 1, 16.711, 2.992, 16.822, 3.553, 16.933, 3.553, 0, 17.833, 1.693, 0, 18.2, 2.761, 0, 18.5, 2.14, 0, 19.233, 30, 2, 21.567, 30, 2, 23.3, 30, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param211", "Segments": [0, 0, 2, 0.333, 0, 0, 0.8, 0.775, 0, 1, -5.438, 0, 1.867, -3.758, 1, 2.034, -3.758, 2.2, -6.096, 2.367, -6.786, 1, 2.611, -7.798, 2.856, -7.838, 3.1, -7.838, 2, 4.433, -7.838, 1, 4.511, -7.838, 4.589, -9.683, 4.667, -10.014, 1, 4.778, -10.487, 4.889, -10.478, 5, -10.478, 2, 5.167, -10.478, 0, 5.733, -17.678, 2, 6.267, -17.678, 0, 9.2, -17.198, 2, 10.2, -17.198, 2, 10.767, -17.198, 0, 11, 8.002, 2, 11.233, 8.002, 2, 11.467, 8.002, 2, 11.7, 8.002, 2, 11.967, 8.002, 2, 12.467, 8.002, 1, 12.667, 8.002, 12.867, -5.548, 13.067, -18.278, 1, 13.122, -21.814, 13.178, -21.158, 13.233, -21.158, 0, 13.433, -12.878, 2, 14.2, -12.878, 0, 15, -20.258, 2, 16.933, -20.258, 2, 17.833, -20.258, 0, 18.2, -24.398, 0, 18.5, -20.258, 2, 21.567, -20.258, 2, 23.3, -20.258, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param228", "Segments": [0, 0, 2, 0.333, 0, 0, 0.467, -15.433, 0, 1, -9.306, 1, 1.289, -9.306, 1.578, -9.172, 1.867, -10.326, 1, 2.278, -11.968, 2.689, -15.186, 3.1, -15.186, 2, 4.433, -15.186, 0, 4.667, -15.203, 0, 5, -13.446, 2, 5.167, -13.446, 0, 5.733, -7.386, 2, 6.267, -7.386, 2, 10.2, -7.386, 2, 10.767, -7.386, 0, 11, -15.126, 2, 11.233, -15.126, 2, 11.467, -15.126, 2, 11.7, -15.126, 2, 11.967, -15.126, 2, 12.467, -15.126, 0, 12.7, -18.471, 0, 13.067, 1, 0, 13.233, -5.84, 0, 13.433, -4.76, 2, 14.2, -4.76, 1, 14.467, -4.76, 14.733, -5.784, 15, -10.04, 1, 15.644, -20.325, 16.289, -26.96, 16.933, -26.96, 0, 17.833, -11.96, 0, 18.2, -20.577, 0, 18.5, -15.568, 2, 21.567, -15.568, 2, 23.3, -15.568, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param283", "Segments": [0, 0, 2, 0.333, 0, 0, 1, 30, 1, 1.289, 30, 1.578, 21.878, 1.867, 0, 1, 2.045, -13.463, 2.222, -24, 2.4, -24, 0, 3.1, 30, 0, 4.433, 0, 0, 4.867, 30, 0, 5.167, 0, 0, 5.8, 30, 2, 6.267, 30, 0, 6.967, 9, 0, 9.6, 30, 2, 10.2, 30, 2, 10.767, 30, 2, 12.467, 30, 0, 13.067, -30, 0, 13.433, -13, 2, 14.2, -13, 0, 15, 30, 2, 16.933, 30, 0, 17.833, -6, 0, 18.2, 30, 0, 18.5, 21, 2, 21.567, 21, 2, 23.3, 21, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param221", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.467, 1, 2, 1.867, 1, 2, 4.433, 1, 2, 6.267, 1, 2, 10.2, 1, 2, 10.767, 1, 2, 12.467, 1, 2, 14.2, 1, 2, 18.5, 1, 2, 21.567, 1, 2, 23.3, 1, 2, 23.567, 1, 2, 23.6, 0, 2, 24.267, 0]}, {"Target": "Parameter", "Id": "Param230", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.467, 0, 2, 1.867, 0, 2, 4.433, 0, 2, 6.267, 0, 2, 10.2, 0, 2, 10.767, 0, 2, 12.467, 0, 2, 14.2, 0, 2, 18.5, 0, 2, 21.567, 0, 2, 23.3, 0, 2, 23.567, 0, 2, 23.6, 0, 2, 24.267, 0]}, {"Target": "Parameter", "Id": "Param231", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.467, 0, 2, 1.867, 0, 2, 4.433, 0, 2, 6.267, 0, 2, 10.2, 0, 2, 10.767, 0, 2, 12.467, 0, 2, 14.2, 0, 2, 18.5, 0, 2, 21.567, 0, 2, 23.3, 0, 2, 23.567, 0, 2, 23.6, 0, 2, 24.267, 0]}, {"Target": "Parameter", "Id": "Param222", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.467, 0, 2, 1.867, 0, 2, 4.433, 0, 2, 6.267, 0, 2, 10.2, 0, 2, 10.767, 0, 2, 12.467, 0, 2, 14.2, 0, 2, 18.867, 0, 2, 18.9, 1, 2, 21.567, 1, 2, 23.3, 1, 2, 23.567, 1, 2, 23.6, 0, 2, 24.267, 0]}, {"Target": "Parameter", "Id": "Param223", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.467, 1, 2, 1.867, 1, 2, 4.433, 1, 2, 6.267, 1, 2, 10.2, 1, 2, 10.767, 1, 2, 12.467, 1, 2, 14.2, 1, 2, 18.867, 1, 2, 18.9, 0, 2, 21.567, 0, 2, 23.3, 0, 2, 23.567, 0, 2, 23.6, 0, 2, 24.267, 0]}, {"Target": "Parameter", "Id": "Param207", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.467, 0, 2, 1.867, 0, 2, 4.433, 0, 2, 6.267, 0, 2, 10.2, 0, 2, 10.767, 0, 2, 12.467, 0, 2, 14.2, 0, 2, 18.867, 0, 2, 21.567, 0, 2, 23.3, 0, 2, 23.567, 0, 2, 23.6, 0, 2, 24.267, 0]}, {"Target": "Parameter", "Id": "Param236", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.467, 0, 2, 1.867, 0, 2, 4.433, 0, 2, 6.267, 0, 2, 10.2, 0, 2, 10.767, 0, 2, 12.467, 0, 2, 14.2, 0, 2, 18.867, 0, 2, 21.567, 0, 2, 23.3, 0, 2, 23.567, 0, 2, 23.6, 0, 2, 24.267, 0]}, {"Target": "Parameter", "Id": "Param237", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.467, 0, 2, 1.867, 0, 2, 4.433, 0, 2, 6.267, 0, 2, 10.2, 0, 2, 10.767, 0, 2, 12.9, 0, 2, 12.933, 1, 2, 14.567, 1, 2, 14.6, 0, 2, 18.867, 0, 2, 21.567, 0, 2, 23.3, 0, 2, 23.567, 0, 2, 23.6, 0, 2, 24.267, 0]}, {"Target": "Parameter", "Id": "Param238", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.467, 1, 2, 1.867, 1, 2, 4.433, 1, 2, 6.267, 1, 2, 10.2, 1, 2, 10.767, 1, 2, 11.067, 0, 2, 12.9, 0, 2, 12.933, 0, 2, 18.867, 0, 2, 21.567, 0, 2, 23.3, 0, 2, 23.567, 0, 2, 23.6, 0, 2, 24.267, 0]}, {"Target": "Parameter", "Id": "Param239", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.467, 0, 2, 1.867, 0, 2, 4.433, 0, 2, 6.267, 0, 2, 10.2, 0, 2, 10.767, 0, 2, 11.067, 0, 2, 18.867, 0, 2, 21.567, 0, 2, 23.3, 0, 2, 23.567, 0, 2, 23.6, 0, 2, 24.267, 0]}, {"Target": "Parameter", "Id": "Param240", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.467, 0, 2, 1.867, 0, 2, 4.433, 0, 2, 6.267, 0, 2, 10.2, 0, 2, 10.767, 0, 2, 12.467, 0, 2, 14.567, 0, 2, 14.6, 1, 2, 18.867, 1, 2, 18.9, 0, 2, 21.567, 0, 2, 23.3, 0, 2, 23.567, 0, 2, 23.6, 0, 2, 24.267, 0]}, {"Target": "Parameter", "Id": "Param241", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.467, 0, 2, 1.867, 0, 2, 4.433, 0, 2, 6.267, 0, 2, 10.2, 0, 2, 10.767, 0, 2, 12.467, 0, 2, 14.567, 0, 2, 14.6, 0, 2, 18.5, 0, 2, 21.567, 0, 2, 23.3, 0, 2, 23.567, 0, 2, 23.6, 0, 2, 24.267, 0]}, {"Target": "Parameter", "Id": "Param242", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.467, 0, 2, 1.867, 0, 2, 4.433, 0, 2, 6.267, 0, 2, 10.2, 0, 2, 10.767, 0, 2, 12.467, 0, 2, 14.2, 0, 2, 18.5, 0, 2, 21.567, 0, 2, 23.3, 0, 2, 23.567, 0, 2, 23.6, 0, 2, 24.267, 0]}, {"Target": "Parameter", "Id": "Param243", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.467, 0, 2, 1.867, 0, 2, 4.433, 0, 2, 6.267, 0, 2, 10.2, 0, 2, 10.767, 0, 2, 11.067, 1, 2, 12.9, 1, 2, 12.933, 0, 2, 14.2, 0, 2, 18.5, 0, 2, 21.567, 0, 2, 23.3, 0, 2, 23.567, 0, 2, 23.6, 0, 2, 24.267, 0]}, {"Target": "Parameter", "Id": "Param244", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.467, 0, 2, 1.867, 0, 2, 4.433, 0, 2, 6.267, 0, 2, 10.2, 0, 2, 10.767, 0, 2, 12.9, 0, 2, 12.933, 0, 2, 14.2, 0, 2, 18.5, 0, 2, 21.567, 0, 2, 23.3, 0, 2, 23.567, 0, 2, 23.6, 0, 2, 24.267, 0]}, {"Target": "Parameter", "Id": "Param15", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, 5.33, 0, 0.633, 4.447, 0, 1.033, 4.631, 2, 1.867, 4.631, 0, 2.367, 5.351, 0, 3.1, 4.631, 2, 4.433, 4.631, 0, 4.667, 6.371, 0, 5, 5.33, 2, 5.167, 5.33, 0, 5.733, 4.19, 2, 6.267, 4.19, 0, 6.9, 6.35, 0, 7.733, 5.69, 0, 8.967, 6.41, 0, 10.2, 5.728, 0, 10.3, 6.388, 0, 10.467, 5.968, 2, 10.767, 5.968, 0, 11, 5.668, 0, 11.233, 6.988, 0, 11.467, 5.668, 1, 11.545, 5.668, 11.622, 6.859, 11.7, 6.988, 1, 11.8, 7.154, 11.9, 7.149, 12, 7.288, 1, 12.111, 7.443, 12.222, 7.648, 12.333, 7.648, 2, 12.467, 7.648, 0, 13.067, 6.928, 0, 13.433, 9.388, 0, 13.667, 8.188, 2, 14.2, 8.188, 0, 15, 9.868, 2, 15.5, 9.868, 0, 15.867, 8.668, 0, 16.3, 9.448, 0, 16.667, 8.608, 1, 16.822, 8.608, 16.978, 9.309, 17.133, 9.928, 1, 17.255, 10.414, 17.378, 10.458, 17.5, 10.458, 0, 17.833, 8.788, 0, 18.2, 10.823, 1, 18.3, 10.823, 18.4, 10.8, 18.5, 10.148, 1, 18.578, 9.64, 18.655, 8.287, 18.733, 8.287, 0, 18.867, 11.467, 1, 18.945, 11.467, 19.022, 8.728, 19.1, 8.7, 1, 19.922, 8.399, 20.745, 8.287, 21.567, 8.287, 2, 23.3, 8.287, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param16", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, 7.68, 2, 0.633, 7.68, 2, 1.033, 7.68, 2, 1.867, 7.68, 2, 3.1, 7.68, 2, 4.433, 7.68, 2, 5.167, 7.68, 2, 6.267, 7.68, 0, 6.9, 5.88, 2, 10.2, 5.88, 2, 10.767, 5.88, 2, 12.467, 5.88, 2, 14.2, 5.88, 2, 15.5, 5.88, 2, 16.667, 5.88, 2, 17.833, 5.88, 2, 18.5, 5.88, 2, 18.7, 5.88, 2, 21.567, 5.88, 2, 23.3, 5.88, 0, 23.333, -19, 1, 23.422, -19, 23.511, -13.564, 23.6, -8.871, 1, 23.744, -1.245, 23.889, 0.88, 24.033, 0.88, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param17", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, 5.52, 0, 0.633, 0, 2, 1.033, 0, 2, 1.867, 0, 2, 3.1, 0, 2, 4.433, 0, 2, 5.167, 0, 2, 6.267, 0, 2, 10.2, 0, 2, 10.767, 0, 2, 12.467, 0, 2, 14.2, 0, 2, 15.5, 0, 2, 16.667, 0, 2, 17.833, 0, 2, 18.5, 0, 2, 18.7, 0, 2, 21.567, 0, 2, 23.3, 0, 0, 23.633, 17.76, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param18", "Segments": [0, 0, 2, 0.333, 0, 1, 0.355, 0, 0.378, 30, 0.4, 30, 0, 0.633, 0, 2, 1.033, 0, 2, 1.867, 0, 2, 3.1, 0, 2, 4.433, 0, 2, 5.167, 0, 2, 6.267, 0, 2, 10.2, 0, 2, 10.767, 0, 2, 12.467, 0, 2, 14.2, 0, 2, 15.5, 0, 2, 16.667, 0, 2, 17.833, 0, 2, 18.5, 0, 2, 18.7, 0, 2, 21.567, 0, 2, 23.3, 0, 0, 23.6, 20.46, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param19", "Segments": [0, 0, 2, 0.333, 0, 1, 0.389, 0, 0.444, -19.712, 0.5, -22.58, 1, 0.544, -24.875, 0.589, -26.079, 0.633, -26.46, 1, 0.766, -27.602, 0.9, -27.861, 1.033, -27.861, 2, 1.867, -27.861, 2, 3.1, -27.861, 2, 4.433, -27.861, 2, 5.167, -27.861, 0, 5.733, -33.981, 2, 6.267, -33.981, 2, 10.2, -33.981, 2, 10.767, -33.981, 0, 11, -40.281, 2, 11.233, -40.281, 2, 11.467, -40.281, 2, 11.7, -40.281, 2, 12.467, -40.281, 1, 12.667, -40.281, 12.867, -39.587, 13.067, -34, 1, 13.189, -30.586, 13.311, -19.69, 13.433, -19.69, 0, 13.667, -25.9, 2, 14.2, -25.9, 0, 15, -57.58, 2, 15.5, -57.58, 0, 15.867, -60, 2, 16.433, -60, 0, 17.133, -41, 2, 17.833, -41, 2, 18.5, -41, 0, 19.267, -17, 2, 21.567, -17, 0, 21.933, -21.14, 1, 22.011, -21.14, 22.089, -19.786, 22.167, -15.668, 1, 22.211, -13.315, 22.256, -8.154, 22.3, -6.38, 1, 22.4, -2.389, 22.5, -1.267, 22.6, -1.267, 2, 23.1, -1.267, 2, 23.3, -1.267, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param20", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 0, 0.633, -30, 2, 1.033, -30, 2, 1.867, -30, 2, 3.1, -30, 2, 4.433, -30, 2, 5.167, -30, 2, 6.267, -30, 2, 10.2, -30, 2, 10.767, -30, 2, 12.467, -30, 2, 14.2, -30, 2, 15.5, -30, 2, 16.667, -30, 2, 17.833, -30, 2, 18.5, -30, 2, 18.7, -30, 2, 21.567, -30, 2, 23.3, -30, 1, 23.411, -30, 23.522, -10.63, 23.633, -7.243, 1, 23.844, -0.807, 24.056, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param224", "Segments": [0, 0, 2, 0.333, 0, 2, 0.633, 0, 2, 1.867, 0, 2, 4.433, 0, 2, 6.267, 0, 2, 10.2, 0, 2, 10.767, 0, 2, 12.467, 0, 2, 14.2, 0, 2, 18.5, 0, 1, 18.567, 0, 18.633, -0.421, 18.7, -5.497, 1, 18.744, -8.881, 18.789, -21.596, 18.833, -21.596, 1, 18.855, -21.596, 18.878, -19.19, 18.9, -16.02, 1, 18.989, -3.338, 19.078, 2.34, 19.167, 2.34, 0, 19.833, 1.8, 1, 20.411, 1.8, 20.989, 1.913, 21.567, 2.34, 1, 21.689, 2.43, 21.811, 3.54, 21.933, 3.54, 1, 22.055, 3.54, 22.178, 3.226, 22.3, 2.46, 1, 22.4, 1.833, 22.5, 1.44, 22.6, 1.44, 2, 23.3, 1.44, 0, 23.733, 18.171, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param226", "Segments": [0, 0, 2, 0.333, 0, 2, 0.633, 0, 2, 1.867, 0, 2, 4.433, 0, 2, 6.267, 0, 2, 10.2, 0, 2, 10.767, 0, 2, 12.467, 0, 2, 14.2, 0, 2, 18.5, 0, 2, 18.7, 0, 1, 18.711, 0, 18.722, -24.78, 18.733, -24.78, 1, 18.789, -24.78, 18.844, -21.073, 18.9, -18.053, 1, 19.211, -1.143, 19.522, 6, 19.833, 6, 0, 21.567, 0, 2, 23.3, 0, 2, 24.267, 0]}, {"Target": "Parameter", "Id": "Param233", "Segments": [0, 0, 2, 0.333, 0, 0, 0.467, 2.26, 1, 0.5, 2.26, 0.534, 1.228, 0.567, 0.889, 1, 0.722, -0.692, 0.878, -1.213, 1.033, -1.213, 1, 1.311, -1.213, 1.589, -1.155, 1.867, -0.853, 1, 2.034, -0.672, 2.2, -0.373, 2.367, -0.373, 0, 3.1, -3.073, 0, 4.433, -2.533, 0, 4.667, -3.552, 0, 5, -2.233, 0, 5.167, -2.311, 0, 5.7, -0.673, 2, 6.267, -0.673, 0, 6.9, -2.593, 0, 7.733, -2.233, 1, 8.144, -2.233, 8.556, -2.435, 8.967, -2.593, 1, 9.378, -2.751, 9.789, -2.82, 10.2, -2.947, 1, 10.233, -2.957, 10.267, -3.487, 10.3, -3.487, 0, 10.467, -3.067, 2, 10.767, -3.067, 0, 11, 4, 0, 11.233, -0.14, 0, 11.467, 4, 0, 11.7, -0.14, 0, 12, 1.6, 0, 12.267, 0.668, 1, 12.334, 0.668, 12.4, 0.605, 12.467, 1.06, 1, 12.545, 1.591, 12.622, 3.399, 12.7, 3.399, 1, 12.822, 3.399, 12.945, 1.575, 13.067, -0.26, 1, 13.189, -2.095, 13.311, -2.48, 13.433, -2.48, 0, 13.667, -2.374, 0, 14.2, -2.494, 1, 14.378, -2.494, 14.555, -1.513, 14.733, -0.994, 1, 14.989, -0.247, 15.244, -0.154, 15.5, -0.154, 0, 15.833, -0.803, 1, 15.978, -0.803, 16.122, -0.762, 16.267, -0.454, 1, 16.4, -0.169, 16.534, 0.206, 16.667, 0.206, 1, 16.822, 0.206, 16.978, -0.929, 17.133, -1.834, 1, 17.255, -2.545, 17.378, -2.584, 17.5, -2.584, 0, 17.833, -1.774, 0, 18.2, -3.2, 1, 18.3, -3.2, 18.4, -3.094, 18.5, -2.547, 1, 18.533, -2.365, 18.567, -1.213, 18.6, -1.213, 1, 18.644, -1.213, 18.689, -1.317, 18.733, -2.533, 1, 18.778, -3.749, 18.822, -6.542, 18.867, -8.888, 1, 18.878, -9.475, 18.889, -12.164, 18.9, -12.266, 1, 19.789, -20.469, 20.678, -24.447, 21.567, -24.447, 0, 21.933, -24.207, 2, 23.3, -24.207, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param234", "Segments": [0, 0, 2, 0.333, 0, 0, 0.467, -5.55, 1, 0.522, -5.55, 0.578, 7.029, 0.633, 8.875, 1, 0.766, 13.305, 0.9, 14.098, 1.033, 14.098, 2, 1.867, 14.098, 0, 3.1, 19.423, 1, 3.544, 19.423, 3.989, 18.476, 4.433, 15.823, 1, 4.511, 15.359, 4.589, 14.023, 4.667, 14.023, 0, 5, 18.373, 2, 5.167, 18.373, 2, 6.267, 18.373, 1, 6.478, 18.373, 6.689, 18.347, 6.9, 18.973, 1, 7.178, 19.797, 7.455, 21.148, 7.733, 21.148, 0, 8.967, 19.723, 0, 10.2, 22.493, 2, 10.767, 22.493, 0, 11, 0, 2, 11.233, 0, 2, 11.467, 0, 2, 11.7, 0, 2, 12.467, 0, 1, 12.667, 0, 12.867, 0.064, 13.067, 1.619, 1, 13.189, 2.569, 13.311, 12.876, 13.433, 13.725, 1, 13.511, 14.265, 13.589, 14.098, 13.667, 14.098, 1, 13.845, 14.098, 14.022, 14.063, 14.2, 12.841, 1, 14.3, 12.154, 14.4, 4.5, 14.5, 4.5, 1, 14.578, 4.5, 14.655, 6.128, 14.733, 7.361, 1, 14.989, 11.412, 15.244, 14.602, 15.5, 18.375, 1, 15.556, 19.195, 15.611, 19.788, 15.667, 19.788, 0, 15.967, 15.209, 0, 16.4, 22.935, 2, 16.667, 22.935, 0, 17, 14.91, 1, 17.278, 14.91, 17.555, 17.39, 17.833, 25.14, 1, 17.955, 28.55, 18.078, 32.798, 18.2, 32.798, 0, 18.5, 30.884, 2, 18.7, 30.884, 2, 21.567, 30.884, 2, 23.3, 30.884, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param232", "Segments": [0, 0, 2, 0.333, 0, 2, 1.033, 0, 2, 1.867, 0, 2, 3.1, 0, 2, 4.433, 0, 2, 5.167, 0, 2, 6.267, 0, 2, 10.2, 0, 2, 10.767, 0, 2, 12.467, 0, 0, 12.7, 30, 0, 13.067, -30, 0, 13.467, -4.128, 0, 14.2, -30, 2, 14.733, -30, 2, 15.5, -30, 2, 16.667, -30, 2, 17.833, -30, 0, 18.233, 0, 0, 18.5, -9.492, 2, 18.7, -9.492, 2, 21.567, -9.492, 2, 23.3, -9.492, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param235", "Segments": [0, 0, 2, 0.333, 0, 0, 0.5, -30, 0, 0.667, -8.65, 0, 1.033, -12.575, 2, 1.867, -12.575, 2, 2.367, -12.575, 0, 3.1, -17.795, 0, 4.433, -15.035, 1, 4.5, -15.035, 4.566, -17.7, 4.633, -20.915, 1, 4.755, -26.81, 4.878, -29, 5, -29, 2, 5.167, -29, 2, 6.267, -29, 2, 10.2, -29, 2, 10.767, -29, 2, 12.467, -29, 2, 12.7, -29, 0, 13.067, -15.32, 0, 13.433, -25.22, 0, 13.667, -22.76, 0, 14.2, -23.42, 0, 14.733, 7, 2, 15.5, 7, 0, 15.833, 4.16, 0, 16.267, 15.1, 2, 16.667, 15.1, 1, 16.822, 15.1, 16.978, 2.636, 17.133, -11, 1, 17.255, -21.714, 17.378, -23.78, 17.5, -23.78, 0, 17.833, -15.92, 0, 18.2, -23.36, 0, 18.5, -19.896, 2, 18.7, -19.896, 0, 18.733, -15, 0, 18.867, -30, 0, 21.567, -15, 2, 23.3, -15, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param274", "Segments": [0, 0, 2, 0.333, 0, 0, 0.467, -0.9, 2, 1.033, -0.9, 2, 1.867, -0.9, 2, 3.1, -0.9, 2, 4.433, -0.9, 2, 5.167, -0.9, 2, 6.267, -0.9, 2, 10.2, -0.9, 2, 10.767, -0.9, 2, 12.467, -0.9, 2, 12.7, -0.9, 0, 13.067, -0.2, 2, 14.2, -0.2, 2, 14.733, -0.2, 2, 15.5, -0.2, 0, 15.533, 0, 0, 15.667, -0.8, 2, 17.833, -0.8, 2, 18.5, -0.8, 2, 21.567, -0.8, 2, 23.3, -0.8, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param142", "Segments": [0, 0, 2, 0.333, 0, 1, 0.766, 0.912, 1.2, 1.783, 1.633, 1.954, 1, 2.844, 2.201, 4.056, 2.365, 5.267, 2.509, 1, 6.7, 2.68, 8.134, 2.864, 9.567, 2.94, 1, 12.211, 3.08, 14.856, 3.107, 17.5, 3.107, 2, 21.967, 3.107, 1, 22.411, 3.107, 22.856, 2.839, 23.3, 2.275, 1, 23.311, 2.26, 23.322, 2.199, 23.333, 2.148, 1, 23.644, 0.726, 23.956, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param144", "Segments": [0, 0, 2, 0.333, 0, 1, 0.766, 0, 1.2, -0.43, 1.633, -0.453, 1, 2.844, -0.516, 4.056, -0.558, 5.267, -0.596, 1, 6.7, -0.641, 8.134, -0.686, 9.567, -0.712, 1, 12.211, -0.759, 14.856, -0.772, 17.5, -0.772, 1, 18.989, -0.772, 20.478, -0.761, 21.967, -0.712, 1, 22.411, -0.697, 22.856, -0.59, 23.3, -0.527, 1, 23.311, -0.525, 23.322, -0.426, 23.333, -0.416, 1, 23.644, -0.131, 23.956, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param134", "Segments": [0, 0, 2, 0.333, 0, 2, 7.9, 0, 2, 22.9, 0, 0, 23.3, 1, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param246", "Segments": [0, 0, 2, 0.333, 0, 0, 1.033, 1, 2, 23.3, 1, 0, 23.333, 0, 2, 24.267, 0]}, {"Target": "Parameter", "Id": "Param182", "Segments": [0, 0, 2, 0.333, 0, 1, 0.766, -0.103, 1.2, -0.247, 1.633, -0.278, 1, 2.622, -0.321, 3.611, -0.346, 4.6, -0.371, 1, 6.278, -0.415, 7.955, -0.43, 9.633, -0.43, 2, 12.267, -0.43, 2, 23.3, -0.43, 1, 23.311, -0.43, 23.322, -0.254, 23.333, -0.248, 1, 23.644, -0.077, 23.956, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param229", "Segments": [0, 0, 2, 0.333, 0, 0, 0.367, 1.32, 2, 23.3, 1.32, 1, 23.311, 1.32, 23.322, 0.781, 23.333, 0.762, 1, 23.644, 0.237, 23.956, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param245", "Segments": [0, 0, 2, 0.333, 0, 0, 0.367, -0.6, 2, 23.3, -0.6, 1, 23.311, -0.6, 23.322, -0.355, 23.333, -0.346, 1, 23.644, -0.108, 23.956, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param117", "Segments": [0, 0, 2, 0.333, 0, 0, 0.7, 0.66, 2, 23.3, 0.66, 1, 23.311, 0.66, 23.322, 0.39, 23.333, 0.381, 1, 23.644, 0.118, 23.956, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param140", "Segments": [0, 0, 2, 0.333, 0, 0, 0.533, 1, 2, 1.433, 1, 2, 23.3, 1, 1, 23.311, 1, 23.322, 0.592, 23.333, 0.577, 1, 23.644, 0.18, 23.956, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param262", "Segments": [0, 0, 2, 0.333, 0, 0, 0.367, 1, 2, 1.433, 1, 2, 23.3, 1, 1, 23.311, 1, 23.322, 0.592, 23.333, 0.577, 1, 23.644, 0.18, 23.956, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param263", "Segments": [0, 0, 2, 0.333, 0, 0, 0.367, 0.5, 1, 0.767, 0.318, 1.167, 0.2, 1.567, 0.2, 2, 23.3, 0.2, 1, 23.311, 0.2, 23.322, 0.118, 23.333, 0.116, 1, 23.644, 0.036, 23.956, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param277", "Segments": [0, 0, 2, 0.333, 0, 2, 0.667, 0, 0, 0.933, 12.46, 0, 1.467, -9.549, 0, 1.9, 12.46, 0, 2.3, -9.549, 0, 2.733, 12.46, 0, 3.133, -9.549, 0, 3.567, 12.46, 0, 3.967, -9.549, 0, 4.4, 12.46, 0, 4.8, -9.549, 0, 5.233, 12.46, 0, 5.633, -9.549, 0, 6.067, 12.46, 0, 6.467, -9.549, 0, 6.9, 12.46, 0, 7.3, -9.549, 0, 7.733, 12.46, 0, 8.133, -9.549, 0, 8.567, 12.46, 0, 8.967, -9.549, 0, 9.4, 0, 2, 9.733, 0, 0, 10, 12.46, 0, 10.533, -9.549, 0, 10.967, 12.46, 0, 11.367, -9.549, 0, 11.8, 12.46, 0, 12.2, -9.549, 0, 12.633, 12.46, 0, 13.033, -9.549, 0, 13.467, 12.46, 0, 13.867, -9.549, 0, 14.3, 12.46, 0, 14.7, -9.549, 0, 15.133, 12.46, 0, 15.533, -9.549, 0, 15.967, 12.46, 0, 16.367, -9.549, 0, 16.8, 12.46, 0, 17.2, -9.549, 0, 17.633, 12.46, 0, 18.033, -9.549, 0, 18.733, 12.46, 0, 19.267, -9.549, 0, 19.7, 12.46, 0, 20.1, -9.549, 0, 20.533, 12.46, 0, 20.933, -9.549, 0, 21.367, 12.46, 0, 21.767, -9.549, 0, 22.2, 12.46, 0, 22.6, -9.549, 0, 23.033, 12.46, 0, 23.433, -9.549, 0, 23.867, 12.46, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param278", "Segments": [0, 0, 2, 0.333, 0, 2, 0.667, 0, 0, 0.933, 0.25, 0, 1.467, -0.17, 0, 1.9, 0.25, 0, 2.3, -0.17, 0, 2.733, 0.25, 0, 3.133, -0.17, 0, 3.567, 0.25, 0, 3.967, -0.17, 0, 4.4, 0.25, 0, 4.8, -0.17, 0, 5.233, 0.25, 0, 5.633, -0.17, 0, 6.067, 0.25, 0, 6.467, -0.17, 0, 6.9, 0.25, 0, 7.3, -0.17, 0, 7.733, 0.25, 0, 8.133, -0.17, 0, 8.567, 0.25, 0, 8.967, -0.17, 0, 9.4, 0, 2, 9.733, 0, 0, 10, 0.25, 0, 10.533, -0.17, 0, 10.967, 0.25, 0, 11.367, -0.17, 0, 11.8, 0.25, 0, 12.2, -0.17, 0, 12.633, 0.25, 0, 13.033, -0.17, 0, 13.467, 0.25, 0, 13.867, -0.17, 0, 14.3, 0.25, 0, 14.7, -0.17, 0, 15.133, 0.25, 0, 15.533, -0.17, 0, 15.967, 0.25, 0, 16.367, -0.17, 0, 16.8, 0.25, 0, 17.2, -0.17, 0, 17.633, 0.25, 0, 18.033, -0.17, 0, 18.733, 0.25, 0, 19.267, -0.17, 0, 19.7, 0.25, 0, 20.1, -0.17, 0, 20.533, 0.25, 0, 20.933, -0.17, 0, 21.367, 0.25, 0, 21.767, -0.17, 0, 22.2, 0.25, 0, 22.6, -0.17, 0, 23.033, 0.25, 0, 23.433, -0.17, 0, 23.867, 0.25, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param280", "Segments": [0, 0, 2, 0.333, 0, 0, 0.567, 0.789, 0, 0.933, -0.509, 0, 1.333, 0.375, 0, 1.767, -0.28, 0, 2.2, 0.375, 0, 2.7, -0.28, 0, 3.133, 0.375, 0, 3.667, -0.28, 0, 4.1, 0.375, 1, 4.211, 0.375, 4.322, 0.321, 4.433, -0.026, 1, 4.489, -0.199, 4.544, -0.67, 4.6, -0.67, 0, 4.867, 0.789, 0, 5.3, -0.351, 0, 5.733, 0.375, 0, 6.267, -0.221, 0, 6.867, 0.375, 0, 7.5, -0.303, 0, 8.167, 0.375, 0, 8.933, -0.303, 0, 9.667, 0.375, 1, 9.856, 0.375, 10.044, 0.177, 10.233, -0.303, 1, 10.289, -0.444, 10.344, -0.569, 10.4, -0.569, 0, 10.767, 0.625, 0, 11.033, -0.351, 0, 11.167, 0.375, 0, 11.367, -0.351, 0, 11.533, 0.366, 0, 11.733, -0.351, 0, 11.933, 0.447, 0, 12.133, -0.28, 0, 12.5, 0.412, 0, 12.767, -0.275, 1, 12.845, -0.275, 12.922, 0.115, 13, 0.551, 1, 13.033, 0.737, 13.067, 0.739, 13.1, 0.739, 0, 13.333, -0.509, 0, 13.633, 0.625, 0, 13.933, -0.502, 0, 14.2, 0.515, 0, 14.633, -0.303, 0, 14.967, 0.551, 0, 15.367, -0.28, 0, 15.8, 0.375, 0, 16.2, -0.348, 0, 16.6, 0.427, 0, 17.067, -0.326, 0, 17.567, 0.375, 0, 18.033, -0.326, 0, 18.433, 0.375, 0, 18.833, -0.303, 0, 19.367, 0.789, 0, 19.8, -0.28, 0, 20.3, 0.375, 0, 20.8, -0.303, 0, 21.3, 0.556, 0, 21.833, -0.394, 1, 21.911, -0.394, 21.989, -0.322, 22.067, 0.019, 1, 22.134, 0.311, 22.2, 0.625, 22.267, 0.625, 0, 22.6, -0.67, 0, 22.9, 0.576, 1, 23, 0.576, 23.1, -0.088, 23.2, -0.351, 1, 23.278, -0.555, 23.355, -0.528, 23.433, -0.528, 0, 24.033, 0.324, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param7", "Segments": [0, 0, 2, 0.333, 0, 0, 1.667, -9, 0, 3, 0, 0, 4.333, -9, 0, 5.667, 0, 0, 7, -9, 0, 8.333, 0, 0, 9.667, -9, 0, 11, 0, 0, 12.333, -9, 0, 13.667, 0, 0, 15, -9, 0, 16.333, 0, 0, 17.667, -9, 0, 19, 0, 0, 20.333, -9, 0, 21.667, 0, 0, 23, -9, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param66", "Segments": [0, 0, 2, 0.333, 0, 0, 1.667, -19, 0, 3, 0, 0, 4.333, -19, 0, 5.667, 0, 0, 7, -19, 0, 8.333, 0, 0, 9.667, -19, 0, 11, 0, 0, 12.333, -19, 0, 13.667, 0, 0, 15, -19, 0, 16.333, 0, 0, 17.667, -19, 0, 19, 0, 0, 20.333, -19, 0, 21.667, 0, 0, 23, -19, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param67", "Segments": [0, 0, 2, 0.333, 0, 0, 1, 16, 0, 2.333, -16, 1, 2.555, -16, 2.778, -10.667, 3, 0, 1, 3.222, 10.667, 3.445, 16, 3.667, 16, 0, 5, -16, 1, 5.222, -16, 5.445, -10.667, 5.667, 0, 1, 5.889, 10.667, 6.111, 16, 6.333, 16, 0, 7.667, -16, 1, 7.889, -16, 8.111, -10.667, 8.333, 0, 1, 8.555, 10.667, 8.778, 16, 9, 16, 0, 10.333, -16, 1, 10.555, -16, 10.778, -10.667, 11, 0, 1, 11.222, 10.667, 11.445, 16, 11.667, 16, 0, 13, -16, 1, 13.222, -16, 13.445, -10.667, 13.667, 0, 1, 13.889, 10.667, 14.111, 16, 14.333, 16, 0, 15.667, -16, 1, 15.889, -16, 16.111, -10.667, 16.333, 0, 1, 16.555, 10.667, 16.778, 16, 17, 16, 0, 18.333, -16, 1, 18.555, -16, 18.778, -10.667, 19, 0, 1, 19.222, 10.667, 19.445, 16, 19.667, 16, 0, 21, -16, 1, 21.222, -16, 21.445, -10.667, 21.667, 0, 1, 21.889, 10.667, 22.111, 16, 22.333, 16, 0, 23.667, -16, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param68", "Segments": [0, 0, 2, 0.333, 0, 0, 1.667, 17, 0, 3, 0, 0, 4.333, 17, 0, 5.667, 0, 0, 7, 17, 0, 8.333, 0, 0, 9.667, 17, 0, 11, 0, 0, 12.333, 17, 0, 13.667, 0, 0, 15, 17, 0, 16.333, 0, 0, 17.667, 17, 0, 19, 0, 0, 20.333, 17, 0, 21.667, 0, 0, 23, 17, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param69", "Segments": [0, 0, 2, 0.333, 0, 0, 1, -16, 0, 2.333, 16, 1, 2.555, 16, 2.778, 10.667, 3, 0, 1, 3.222, -10.667, 3.445, -16, 3.667, -16, 0, 5, 16, 1, 5.222, 16, 5.445, 10.667, 5.667, 0, 1, 5.889, -10.667, 6.111, -16, 6.333, -16, 0, 7.667, 16, 1, 7.889, 16, 8.111, 10.667, 8.333, 0, 1, 8.555, -10.667, 8.778, -16, 9, -16, 0, 10.333, 16, 1, 10.555, 16, 10.778, 10.667, 11, 0, 1, 11.222, -10.667, 11.445, -16, 11.667, -16, 0, 13, 16, 1, 13.222, 16, 13.445, 10.667, 13.667, 0, 1, 13.889, -10.667, 14.111, -16, 14.333, -16, 0, 15.667, 16, 1, 15.889, 16, 16.111, 10.667, 16.333, 0, 1, 16.555, -10.667, 16.778, -16, 17, -16, 0, 18.333, 16, 1, 18.555, 16, 18.778, 10.667, 19, 0, 1, 19.222, -10.667, 19.445, -16, 19.667, -16, 0, 21, 16, 1, 21.222, 16, 21.445, 10.667, 21.667, 0, 1, 21.889, -10.667, 22.111, -16, 22.333, -16, 0, 23.667, 16, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param35", "Segments": [0, 0, 2, 0.367, 0, 0, 0.567, -8.904, 0, 0.867, 15.791, 0, 1.233, -15.522, 0, 1.6, 11.934, 0, 2.033, -11.516, 0, 2.467, 9.4, 0, 2.933, -7.064, 0, 3.367, 8.219, 0, 3.9, -7.893, 0, 4.6, 14.085, 0, 4.8, -30, 2, 4.867, -30, 0, 5.167, 21.538, 0, 5.567, -15.107, 0, 5.967, 10.17, 0, 6.433, -5.14, 0, 6.767, -0.234, 0, 6.867, -0.333, 0, 7.267, 4.535, 0, 7.767, -5.361, 0, 8.367, 3.145, 0, 9.2, -2.469, 0, 9.867, 2.672, 0, 10.067, 2.4, 0, 10.333, 4.745, 0, 10.633, -15.46, 0, 11, 22.619, 0, 11.2, -11.85, 0, 11.4, 3.424, 0, 11.567, -12.208, 0, 11.767, 8.868, 0, 11.967, -11.389, 0, 12.167, 10.542, 0, 12.467, -8.062, 0, 12.7, 9.128, 0, 13.033, -17.407, 0, 13.3, 30, 2, 13.367, 30, 0, 13.633, -21.929, 0, 13.933, 15.67, 0, 14.2, -14.809, 0, 14.467, 12.18, 0, 14.867, -16.83, 0, 15.233, 14.309, 0, 15.633, -10.723, 0, 16.033, 13.212, 0, 16.433, -14.498, 0, 16.867, 11.623, 0, 17.3, -6.582, 0, 18, 10.443, 0, 18.367, -12.965, 0, 18.7, 14.116, 0, 19.1, -15.27, 0, 19.6, 16.349, 0, 20.033, -12.825, 0, 20.467, 8.242, 0, 21.033, -5.07, 0, 21.667, 7.266, 0, 22.167, -20.023, 0, 22.533, 27.24, 0, 22.867, -25.023, 0, 23.2, 18.084, 0, 23.633, -9.997, 0, 24.233, 7.493, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param36", "Segments": [0, 0, 2, 0.367, 0, 0, 0.533, 5.803, 0, 0.767, -13.762, 0, 1.1, 20.55, 0, 1.467, -21.692, 0, 1.867, 18.606, 0, 2.267, -16.319, 0, 2.667, 12.495, 0, 3.167, -10.368, 0, 3.567, 9.635, 0, 4.133, -8.349, 0, 4.467, 4.553, 0, 4.6, -1.871, 0, 4.767, 23.689, 0, 5, -30, 2, 5.1, -30, 0, 5.4, 30, 0, 5.8, -22.883, 0, 6.2, 15.259, 0, 6.6, -9.094, 0, 6.967, 3.205, 0, 7.267, -1.405, 0, 7.6, 3.715, 0, 8, -4.146, 0, 8.5, 2.287, 0, 8.9, -0.377, 0, 9.133, 0.223, 0, 9.5, -1.193, 0, 10, 0.958, 0, 10.333, -1.952, 0, 10.533, 11.117, 0, 10.9, -21.435, 0, 11.167, 30, 2, 11.2, 30, 0, 11.4, -10.969, 0, 11.533, 0.635, 0, 11.7, -14.255, 0, 11.933, 16.431, 0, 12.133, -12.072, 0, 12.4, 10.471, 0, 12.667, -13.101, 0, 12.933, 15.901, 0, 13.233, -28.363, 0, 13.5, 30, 2, 13.6, 30, 0, 13.833, -30, 2, 13.867, -30, 0, 14.167, 24.335, 0, 14.433, -17.276, 0, 14.767, 17.8, 0, 15.1, -21.848, 0, 15.467, 21.002, 0, 15.867, -18.434, 0, 16.267, 18.95, 0, 16.667, -19.194, 0, 17.1, 15.625, 0, 17.5, -9.566, 0, 17.767, -0.376, 0, 17.9, -2.408, 0, 18.233, 12.416, 0, 18.6, -18.88, 0, 18.967, 21.086, 0, 19.367, -18.911, 0, 19.833, 19.124, 0, 20.233, -16.793, 0, 20.667, 11.876, 0, 21.133, -5.132, 0, 22.033, 7.716, 0, 22.4, -26.101, 0, 22.7, 30, 2, 22.8, 30, 0, 23.033, -30, 2, 23.133, -30, 0, 23.433, 25.178, 0, 23.833, -14.219, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param37", "Segments": [0, 0, 2, 0.367, 0, 0, 0.533, -15.352, 0, 0.833, 21.871, 0, 1.167, -21.332, 0, 1.5, 16.365, 0, 1.933, -12.921, 0, 2.4, 10.636, 0, 2.9, -8.132, 0, 3.3, 10.017, 0, 3.9, -8.907, 0, 4.3, 11.181, 0, 4.7, -14.568, 0, 5, 29.116, 0, 5.333, -28.939, 0, 5.667, 22.823, 0, 6.367, -14.207, 0, 6.767, 13.964, 0, 7.4, -12.847, 0, 7.867, 11.275, 0, 8.667, -10.602, 0, 9.167, 8.732, 0, 10.3, -16.489, 0, 10.533, 22.451, 0, 10.767, -23.509, 0, 11, 27.213, 0, 11.2, -27.841, 0, 11.4, 19.387, 0, 11.567, -22.696, 0, 11.767, 18.105, 0, 11.967, -19.621, 0, 12.167, 16.042, 0, 12.433, -12.018, 0, 12.7, 17.646, 0, 12.967, -27.259, 0, 13.333, 29.961, 0, 13.6, -29.875, 0, 13.867, 29.455, 0, 14.167, -29.702, 0, 14.433, 23.901, 0, 14.8, -21.744, 0, 15.167, 20.901, 0, 15.533, -16.179, 0, 15.967, 14.566, 0, 16.4, -15.909, 0, 16.8, 13.172, 0, 17.233, -7.812, 0, 17.567, -1.038, 0, 17.633, -1.302, 0, 17.933, 12.472, 0, 18.3, -17.066, 0, 18.667, 18.658, 0, 19.033, -19.459, 0, 19.567, 18.672, 0, 19.967, -15.939, 0, 20.4, 9.476, 0, 21.033, -6.339, 0, 21.633, 8.677, 0, 22.1, -19.644, 0, 22.367, 22.513, 1, 22.411, 22.513, 22.456, -16.019, 22.5, -16.019, 0, 22.733, 19.089, 0, 23.067, -23.635, 0, 23.433, 20.842, 0, 23.833, -14.846, 0, 24.2, 15.762, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param39", "Segments": [0, 0, 2, 0.367, 0, 0, 0.5, 7.715, 0, 0.7, -18.919, 0, 1, 25.013, 0, 1.333, -23.204, 0, 1.667, 17.673, 0, 2.033, -9.456, 0, 2.5, 6.463, 0, 3.1, -4.699, 0, 3.467, 6.92, 0, 3.733, -2.138, 0, 3.867, -0.973, 0, 4.133, -4.921, 0, 4.467, 10.322, 0, 4.867, -18.935, 0, 5.167, 29.568, 0, 5.5, -29.659, 0, 5.8, 22.072, 0, 6.1, -8.978, 0, 6.333, 4.901, 0, 6.6, -8.676, 0, 6.9, 9.638, 0, 7.2, -1.88, 0, 7.367, 0.392, 0, 7.6, -5.736, 0, 8, 6.577, 0, 8.3, -2.228, 0, 8.567, 2.974, 0, 8.867, -5.208, 0, 9.267, 4.533, 0, 9.567, -2.203, 0, 9.9, 2.254, 0, 10.133, 0.145, 0, 10.3, 3.438, 0, 10.433, -19.062, 0, 10.7, 29.333, 0, 10.933, -29.473, 0, 11.167, 29.547, 0, 11.333, -28.258, 0, 11.533, 26.387, 0, 11.733, -23.392, 0, 11.933, 23.333, 0, 12.133, -23.132, 0, 12.333, 19.521, 0, 12.633, -19.901, 0, 12.867, 26.464, 0, 13.167, -29.721, 0, 13.533, 29.987, 0, 13.8, -29.943, 0, 14.067, 29.876, 0, 14.333, -29.82, 0, 14.6, 28.962, 0, 14.967, -22.947, 0, 15.333, 20.772, 0, 15.667, -16.34, 0, 16.133, 9.692, 0, 16.533, -11.141, 0, 16.933, 10.375, 0, 17.333, -5.959, 0, 17.633, 3.363, 0, 17.867, -5.198, 0, 18.133, 12.707, 0, 18.467, -17.25, 0, 18.8, 18.732, 0, 19.167, -14.327, 0, 19.767, 9.582, 0, 20.1, -12.925, 0, 20.5, 7.744, 0, 20.8, -3.684, 0, 21.033, 1.583, 0, 21.233, -2.937, 0, 21.433, 0.056, 0, 21.533, -0.125, 0, 21.933, 5.393, 0, 22.267, -21.755, 0, 22.5, 29.423, 0, 22.667, -20.315, 0, 22.933, 21.207, 0, 23.267, -24.554, 0, 23.567, 20.707, 0, 23.967, -13.439, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param38", "Segments": [0, 0, 2, 0.367, 0, 0, 0.533, -15.352, 0, 0.833, 21.871, 0, 1.167, -21.332, 0, 1.5, 16.365, 0, 1.933, -12.921, 0, 2.4, 10.636, 0, 2.9, -8.132, 0, 3.3, 10.017, 0, 3.9, -8.907, 0, 4.3, 11.181, 0, 4.7, -14.568, 0, 5, 29.116, 0, 5.333, -28.939, 0, 5.667, 22.823, 0, 6.367, -14.207, 0, 6.767, 13.964, 0, 7.4, -12.847, 0, 7.867, 11.275, 0, 8.667, -10.602, 0, 9.167, 8.732, 0, 10.3, -16.489, 0, 10.533, 22.451, 0, 10.767, -23.509, 0, 11, 27.213, 0, 11.2, -27.841, 0, 11.4, 19.387, 0, 11.567, -22.696, 0, 11.767, 18.105, 0, 11.967, -19.621, 0, 12.167, 16.042, 0, 12.433, -12.018, 0, 12.7, 17.646, 0, 12.967, -27.259, 0, 13.333, 29.961, 0, 13.6, -29.875, 0, 13.867, 29.455, 0, 14.167, -29.702, 0, 14.433, 23.901, 0, 14.8, -21.744, 0, 15.167, 20.901, 0, 15.533, -16.179, 0, 15.967, 14.566, 0, 16.4, -15.909, 0, 16.8, 13.172, 0, 17.233, -7.812, 0, 17.567, -1.038, 0, 17.633, -1.302, 0, 17.933, 12.472, 0, 18.3, -17.066, 0, 18.667, 18.658, 0, 19.033, -19.459, 0, 19.567, 18.672, 0, 19.967, -15.939, 0, 20.4, 9.476, 0, 21.033, -6.339, 0, 21.633, 8.677, 0, 22.1, -19.644, 0, 22.367, 22.513, 1, 22.411, 22.513, 22.456, -16.019, 22.5, -16.019, 0, 22.733, 19.089, 0, 23.067, -23.635, 0, 23.433, 20.842, 0, 23.833, -14.846, 0, 24.2, 15.762, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param40", "Segments": [0, 0, 2, 0.367, 0, 0, 0.5, 7.715, 0, 0.7, -18.919, 0, 1, 25.013, 0, 1.333, -23.204, 0, 1.667, 17.673, 0, 2.033, -9.456, 0, 2.5, 6.463, 0, 3.1, -4.699, 0, 3.467, 6.92, 0, 3.733, -2.138, 0, 3.867, -0.973, 0, 4.133, -4.921, 0, 4.467, 10.322, 0, 4.867, -18.935, 0, 5.167, 29.568, 0, 5.5, -29.659, 0, 5.8, 22.072, 0, 6.1, -8.978, 0, 6.333, 4.901, 0, 6.6, -8.676, 0, 6.9, 9.638, 0, 7.2, -1.88, 0, 7.367, 0.392, 0, 7.6, -5.736, 0, 8, 6.577, 0, 8.3, -2.228, 0, 8.567, 2.974, 0, 8.867, -5.208, 0, 9.267, 4.533, 0, 9.567, -2.203, 0, 9.9, 2.254, 0, 10.133, 0.145, 0, 10.3, 3.438, 0, 10.433, -19.062, 0, 10.7, 29.333, 0, 10.933, -29.473, 0, 11.167, 29.547, 0, 11.333, -28.258, 0, 11.533, 26.387, 0, 11.733, -23.392, 0, 11.933, 23.333, 0, 12.133, -23.132, 0, 12.333, 19.521, 0, 12.633, -19.901, 0, 12.867, 26.464, 0, 13.167, -29.721, 0, 13.533, 29.987, 0, 13.8, -29.943, 0, 14.067, 29.876, 0, 14.333, -29.82, 0, 14.6, 28.962, 0, 14.967, -22.947, 0, 15.333, 20.772, 0, 15.667, -16.34, 0, 16.133, 9.692, 0, 16.533, -11.141, 0, 16.933, 10.375, 0, 17.333, -5.959, 0, 17.633, 3.363, 0, 17.867, -5.198, 0, 18.133, 12.707, 0, 18.467, -17.25, 0, 18.8, 18.732, 0, 19.167, -14.327, 0, 19.767, 9.582, 0, 20.1, -12.925, 0, 20.5, 7.744, 0, 20.8, -3.684, 0, 21.033, 1.583, 0, 21.233, -2.937, 0, 21.433, 0.056, 0, 21.533, -0.125, 0, 21.933, 5.393, 0, 22.267, -21.755, 0, 22.5, 29.423, 0, 22.667, -20.315, 0, 22.933, 21.207, 0, 23.267, -24.554, 0, 23.567, 20.707, 0, 23.967, -13.439, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param41", "Segments": [0, 0, 2, 0.367, 0, 0, 0.533, -15.352, 0, 0.833, 21.871, 0, 1.167, -21.332, 0, 1.5, 16.365, 0, 1.933, -12.921, 0, 2.4, 10.636, 0, 2.9, -8.132, 0, 3.3, 10.017, 0, 3.9, -8.907, 0, 4.3, 11.181, 0, 4.7, -14.568, 0, 5, 29.116, 0, 5.333, -28.939, 0, 5.667, 22.823, 0, 6.367, -14.207, 0, 6.767, 13.964, 0, 7.4, -12.847, 0, 7.867, 11.275, 0, 8.667, -10.602, 0, 9.167, 8.732, 0, 10.3, -16.489, 0, 10.533, 22.451, 0, 10.767, -23.509, 0, 11, 27.213, 0, 11.2, -27.841, 0, 11.4, 19.387, 0, 11.567, -22.696, 0, 11.767, 18.105, 0, 11.967, -19.621, 0, 12.167, 16.042, 0, 12.433, -12.018, 0, 12.7, 17.646, 0, 12.967, -27.259, 0, 13.333, 29.961, 0, 13.6, -29.875, 0, 13.867, 29.455, 0, 14.167, -29.702, 0, 14.433, 23.901, 0, 14.8, -21.744, 0, 15.167, 20.901, 0, 15.533, -16.179, 0, 15.967, 14.566, 0, 16.4, -15.909, 0, 16.8, 13.172, 0, 17.233, -7.812, 0, 17.567, -1.038, 0, 17.633, -1.302, 0, 17.933, 12.472, 0, 18.3, -17.066, 0, 18.667, 18.658, 0, 19.033, -19.459, 0, 19.567, 18.672, 0, 19.967, -15.939, 0, 20.4, 9.476, 0, 21.033, -6.339, 0, 21.633, 8.677, 0, 22.1, -19.644, 0, 22.367, 22.513, 1, 22.411, 22.513, 22.456, -16.019, 22.5, -16.019, 0, 22.733, 19.089, 0, 23.067, -23.635, 0, 23.433, 20.842, 0, 23.833, -14.846, 0, 24.2, 15.762, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param42", "Segments": [0, 0, 2, 0.367, 0, 0, 0.5, 7.715, 0, 0.7, -18.919, 0, 1, 25.013, 0, 1.333, -23.204, 0, 1.667, 17.673, 0, 2.033, -9.456, 0, 2.5, 6.463, 0, 3.1, -4.699, 0, 3.467, 6.92, 0, 3.733, -2.138, 0, 3.867, -0.973, 0, 4.133, -4.921, 0, 4.467, 10.322, 0, 4.867, -18.935, 0, 5.167, 29.568, 0, 5.5, -29.659, 0, 5.8, 22.072, 0, 6.1, -8.978, 0, 6.333, 4.901, 0, 6.6, -8.676, 0, 6.9, 9.638, 0, 7.2, -1.88, 0, 7.367, 0.392, 0, 7.6, -5.736, 0, 8, 6.577, 0, 8.3, -2.228, 0, 8.567, 2.974, 0, 8.867, -5.208, 0, 9.267, 4.533, 0, 9.567, -2.203, 0, 9.9, 2.254, 0, 10.133, 0.145, 0, 10.3, 3.438, 0, 10.433, -19.062, 0, 10.7, 29.333, 0, 10.933, -29.473, 0, 11.167, 29.547, 0, 11.333, -28.258, 0, 11.533, 26.387, 0, 11.733, -23.392, 0, 11.933, 23.333, 0, 12.133, -23.132, 0, 12.333, 19.521, 0, 12.633, -19.901, 0, 12.867, 26.464, 0, 13.167, -29.721, 0, 13.533, 29.987, 0, 13.8, -29.943, 0, 14.067, 29.876, 0, 14.333, -29.82, 0, 14.6, 28.962, 0, 14.967, -22.947, 0, 15.333, 20.772, 0, 15.667, -16.34, 0, 16.133, 9.692, 0, 16.533, -11.141, 0, 16.933, 10.375, 0, 17.333, -5.959, 0, 17.633, 3.363, 0, 17.867, -5.198, 0, 18.133, 12.707, 0, 18.467, -17.25, 0, 18.8, 18.732, 0, 19.167, -14.327, 0, 19.767, 9.582, 0, 20.1, -12.925, 0, 20.5, 7.744, 0, 20.8, -3.684, 0, 21.033, 1.583, 0, 21.233, -2.937, 0, 21.433, 0.056, 0, 21.533, -0.125, 0, 21.933, 5.393, 0, 22.267, -21.755, 0, 22.5, 29.423, 0, 22.667, -20.315, 0, 22.933, 21.207, 0, 23.267, -24.554, 0, 23.567, 20.707, 0, 23.967, -13.439, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param43", "Segments": [0, 0, 2, 0.367, 0, 0, 0.533, -15.352, 0, 0.833, 21.871, 0, 1.167, -21.332, 0, 1.5, 16.365, 0, 1.933, -12.921, 0, 2.4, 10.636, 0, 2.9, -8.132, 0, 3.3, 10.017, 0, 3.9, -8.907, 0, 4.3, 11.181, 0, 4.7, -14.568, 0, 5, 29.116, 0, 5.333, -28.939, 0, 5.667, 22.823, 0, 6.367, -14.207, 0, 6.767, 13.964, 0, 7.4, -12.847, 0, 7.867, 11.275, 0, 8.667, -10.602, 0, 9.167, 8.732, 0, 10.3, -16.489, 0, 10.533, 22.451, 0, 10.767, -23.509, 0, 11, 27.213, 0, 11.2, -27.841, 0, 11.4, 19.387, 0, 11.567, -22.696, 0, 11.767, 18.105, 0, 11.967, -19.621, 0, 12.167, 16.042, 0, 12.433, -12.018, 0, 12.7, 17.646, 0, 12.967, -27.259, 0, 13.333, 29.961, 0, 13.6, -29.875, 0, 13.867, 29.455, 0, 14.167, -29.702, 0, 14.433, 23.901, 0, 14.8, -21.744, 0, 15.167, 20.901, 0, 15.533, -16.179, 0, 15.967, 14.566, 0, 16.4, -15.909, 0, 16.8, 13.172, 0, 17.233, -7.812, 0, 17.567, -1.038, 0, 17.633, -1.302, 0, 17.933, 12.472, 0, 18.3, -17.066, 0, 18.667, 18.658, 0, 19.033, -19.459, 0, 19.567, 18.672, 0, 19.967, -15.939, 0, 20.4, 9.476, 0, 21.033, -6.339, 0, 21.633, 8.677, 0, 22.1, -19.644, 0, 22.367, 22.513, 1, 22.411, 22.513, 22.456, -16.019, 22.5, -16.019, 0, 22.733, 19.089, 0, 23.067, -23.635, 0, 23.433, 20.842, 0, 23.833, -14.846, 0, 24.2, 15.762, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param44", "Segments": [0, 0, 2, 0.367, 0, 0, 0.5, 7.715, 0, 0.7, -18.919, 0, 1, 25.013, 0, 1.333, -23.204, 0, 1.667, 17.673, 0, 2.033, -9.456, 0, 2.5, 6.463, 0, 3.1, -4.699, 0, 3.467, 6.92, 0, 3.733, -2.138, 0, 3.867, -0.973, 0, 4.133, -4.921, 0, 4.467, 10.322, 0, 4.867, -18.935, 0, 5.167, 29.568, 0, 5.5, -29.659, 0, 5.8, 22.072, 0, 6.1, -8.978, 0, 6.333, 4.901, 0, 6.6, -8.676, 0, 6.9, 9.638, 0, 7.2, -1.88, 0, 7.367, 0.392, 0, 7.6, -5.736, 0, 8, 6.577, 0, 8.3, -2.228, 0, 8.567, 2.974, 0, 8.867, -5.208, 0, 9.267, 4.533, 0, 9.567, -2.203, 0, 9.9, 2.254, 0, 10.133, 0.145, 0, 10.3, 3.438, 0, 10.433, -19.062, 0, 10.7, 29.333, 0, 10.933, -29.473, 0, 11.167, 29.547, 0, 11.333, -28.258, 0, 11.533, 26.387, 0, 11.733, -23.392, 0, 11.933, 23.333, 0, 12.133, -23.132, 0, 12.333, 19.521, 0, 12.633, -19.901, 0, 12.867, 26.464, 0, 13.167, -29.721, 0, 13.533, 29.987, 0, 13.8, -29.943, 0, 14.067, 29.876, 0, 14.333, -29.82, 0, 14.6, 28.962, 0, 14.967, -22.947, 0, 15.333, 20.772, 0, 15.667, -16.34, 0, 16.133, 9.692, 0, 16.533, -11.141, 0, 16.933, 10.375, 0, 17.333, -5.959, 0, 17.633, 3.363, 0, 17.867, -5.198, 0, 18.133, 12.707, 0, 18.467, -17.25, 0, 18.8, 18.732, 0, 19.167, -14.327, 0, 19.767, 9.582, 0, 20.1, -12.925, 0, 20.5, 7.744, 0, 20.8, -3.684, 0, 21.033, 1.583, 0, 21.233, -2.937, 0, 21.433, 0.056, 0, 21.533, -0.125, 0, 21.933, 5.393, 0, 22.267, -21.755, 0, 22.5, 29.423, 0, 22.667, -20.315, 0, 22.933, 21.207, 0, 23.267, -24.554, 0, 23.567, 20.707, 0, 23.967, -13.439, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param45", "Segments": [0, 0, 2, 0.367, 0, 0, 0.533, -15.352, 0, 0.833, 21.871, 0, 1.167, -21.332, 0, 1.5, 16.365, 0, 1.933, -12.921, 0, 2.4, 10.636, 0, 2.9, -8.132, 0, 3.3, 10.017, 0, 3.9, -8.907, 0, 4.3, 11.181, 0, 4.7, -14.568, 0, 5, 29.116, 0, 5.333, -28.939, 0, 5.667, 22.823, 0, 6.367, -14.207, 0, 6.767, 13.964, 0, 7.4, -12.847, 0, 7.867, 11.275, 0, 8.667, -10.602, 0, 9.167, 8.732, 0, 10.3, -16.489, 0, 10.533, 22.451, 0, 10.767, -23.509, 0, 11, 27.213, 0, 11.2, -27.841, 0, 11.4, 19.387, 0, 11.567, -22.696, 0, 11.767, 18.105, 0, 11.967, -19.621, 0, 12.167, 16.042, 0, 12.433, -12.018, 0, 12.7, 17.646, 0, 12.967, -27.259, 0, 13.333, 29.961, 0, 13.6, -29.875, 0, 13.867, 29.455, 0, 14.167, -29.702, 0, 14.433, 23.901, 0, 14.8, -21.744, 0, 15.167, 20.901, 0, 15.533, -16.179, 0, 15.967, 14.566, 0, 16.4, -15.909, 0, 16.8, 13.172, 0, 17.233, -7.812, 0, 17.567, -1.038, 0, 17.633, -1.302, 0, 17.933, 12.472, 0, 18.3, -17.066, 0, 18.667, 18.658, 0, 19.033, -19.459, 0, 19.567, 18.672, 0, 19.967, -15.939, 0, 20.4, 9.476, 0, 21.033, -6.339, 0, 21.633, 8.677, 0, 22.1, -19.644, 0, 22.367, 22.513, 1, 22.411, 22.513, 22.456, -16.019, 22.5, -16.019, 0, 22.733, 19.089, 0, 23.067, -23.635, 0, 23.433, 20.842, 0, 23.833, -14.846, 0, 24.2, 15.762, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param46", "Segments": [0, 0, 2, 0.367, 0, 0, 0.5, 7.715, 0, 0.7, -18.919, 0, 1, 25.013, 0, 1.333, -23.204, 0, 1.667, 17.673, 0, 2.033, -9.456, 0, 2.5, 6.463, 0, 3.1, -4.699, 0, 3.467, 6.92, 0, 3.733, -2.138, 0, 3.867, -0.973, 0, 4.133, -4.921, 0, 4.467, 10.322, 0, 4.867, -18.935, 0, 5.167, 29.568, 0, 5.5, -29.659, 0, 5.8, 22.072, 0, 6.1, -8.978, 0, 6.333, 4.901, 0, 6.6, -8.676, 0, 6.9, 9.638, 0, 7.2, -1.88, 0, 7.367, 0.392, 0, 7.6, -5.736, 0, 8, 6.577, 0, 8.3, -2.228, 0, 8.567, 2.974, 0, 8.867, -5.208, 0, 9.267, 4.533, 0, 9.567, -2.203, 0, 9.9, 2.254, 0, 10.133, 0.145, 0, 10.3, 3.438, 0, 10.433, -19.062, 0, 10.7, 29.333, 0, 10.933, -29.473, 0, 11.167, 29.547, 0, 11.333, -28.258, 0, 11.533, 26.387, 0, 11.733, -23.392, 0, 11.933, 23.333, 0, 12.133, -23.132, 0, 12.333, 19.521, 0, 12.633, -19.901, 0, 12.867, 26.464, 0, 13.167, -29.721, 0, 13.533, 29.987, 0, 13.8, -29.943, 0, 14.067, 29.876, 0, 14.333, -29.82, 0, 14.6, 28.962, 0, 14.967, -22.947, 0, 15.333, 20.772, 0, 15.667, -16.34, 0, 16.133, 9.692, 0, 16.533, -11.141, 0, 16.933, 10.375, 0, 17.333, -5.959, 0, 17.633, 3.363, 0, 17.867, -5.198, 0, 18.133, 12.707, 0, 18.467, -17.25, 0, 18.8, 18.732, 0, 19.167, -14.327, 0, 19.767, 9.582, 0, 20.1, -12.925, 0, 20.5, 7.744, 0, 20.8, -3.684, 0, 21.033, 1.583, 0, 21.233, -2.937, 0, 21.433, 0.056, 0, 21.533, -0.125, 0, 21.933, 5.393, 0, 22.267, -21.755, 0, 22.5, 29.423, 0, 22.667, -20.315, 0, 22.933, 21.207, 0, 23.267, -24.554, 0, 23.567, 20.707, 0, 23.967, -13.439, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param62", "Segments": [0, 0, 2, 0.367, 0, 0, 0.533, -15.352, 0, 0.833, 21.871, 0, 1.167, -21.332, 0, 1.5, 16.365, 0, 1.933, -12.921, 0, 2.4, 10.636, 0, 2.9, -8.132, 0, 3.3, 10.017, 0, 3.9, -8.907, 0, 4.3, 11.181, 0, 4.7, -14.568, 0, 5, 29.116, 0, 5.333, -28.939, 0, 5.667, 22.823, 0, 6.367, -14.207, 0, 6.767, 13.964, 0, 7.4, -12.847, 0, 7.867, 11.275, 0, 8.667, -10.602, 0, 9.167, 8.732, 0, 10.3, -16.489, 0, 10.533, 22.451, 0, 10.767, -23.509, 0, 11, 27.213, 0, 11.2, -27.841, 0, 11.4, 19.387, 0, 11.567, -22.696, 0, 11.767, 18.105, 0, 11.967, -19.621, 0, 12.167, 16.042, 0, 12.433, -12.018, 0, 12.7, 17.646, 0, 12.967, -27.259, 0, 13.333, 29.961, 0, 13.6, -29.875, 0, 13.867, 29.455, 0, 14.167, -29.702, 0, 14.433, 23.901, 0, 14.8, -21.744, 0, 15.167, 20.901, 0, 15.533, -16.179, 0, 15.967, 14.566, 0, 16.4, -15.909, 0, 16.8, 13.172, 0, 17.233, -7.812, 0, 17.567, -1.038, 0, 17.633, -1.302, 0, 17.933, 12.472, 0, 18.3, -17.066, 0, 18.667, 18.658, 0, 19.033, -19.459, 0, 19.567, 18.672, 0, 19.967, -15.939, 0, 20.4, 9.476, 0, 21.033, -6.339, 0, 21.633, 8.677, 0, 22.1, -19.644, 0, 22.367, 22.513, 1, 22.411, 22.513, 22.456, -16.019, 22.5, -16.019, 0, 22.733, 19.089, 0, 23.067, -23.635, 0, 23.433, 20.842, 0, 23.833, -14.846, 0, 24.2, 15.762, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param63", "Segments": [0, 0, 2, 0.367, 0, 0, 0.5, 7.715, 0, 0.7, -18.919, 0, 1, 25.013, 0, 1.333, -23.204, 0, 1.667, 17.673, 0, 2.033, -9.456, 0, 2.5, 6.463, 0, 3.1, -4.699, 0, 3.467, 6.92, 0, 3.733, -2.138, 0, 3.867, -0.973, 0, 4.133, -4.921, 0, 4.467, 10.322, 0, 4.867, -18.935, 0, 5.167, 29.568, 0, 5.5, -29.659, 0, 5.8, 22.072, 0, 6.1, -8.978, 0, 6.333, 4.901, 0, 6.6, -8.676, 0, 6.9, 9.638, 0, 7.2, -1.88, 0, 7.367, 0.392, 0, 7.6, -5.736, 0, 8, 6.577, 0, 8.3, -2.228, 0, 8.567, 2.974, 0, 8.867, -5.208, 0, 9.267, 4.533, 0, 9.567, -2.203, 0, 9.9, 2.254, 0, 10.133, 0.145, 0, 10.3, 3.438, 0, 10.433, -19.062, 0, 10.7, 29.333, 0, 10.933, -29.473, 0, 11.167, 29.547, 0, 11.333, -28.258, 0, 11.533, 26.387, 0, 11.733, -23.392, 0, 11.933, 23.333, 0, 12.133, -23.132, 0, 12.333, 19.521, 0, 12.633, -19.901, 0, 12.867, 26.464, 0, 13.167, -29.721, 0, 13.533, 29.987, 0, 13.8, -29.943, 0, 14.067, 29.876, 0, 14.333, -29.82, 0, 14.6, 28.962, 0, 14.967, -22.947, 0, 15.333, 20.772, 0, 15.667, -16.34, 0, 16.133, 9.692, 0, 16.533, -11.141, 0, 16.933, 10.375, 0, 17.333, -5.959, 0, 17.633, 3.363, 0, 17.867, -5.198, 0, 18.133, 12.707, 0, 18.467, -17.25, 0, 18.8, 18.732, 0, 19.167, -14.327, 0, 19.767, 9.582, 0, 20.1, -12.925, 0, 20.5, 7.744, 0, 20.8, -3.684, 0, 21.033, 1.583, 0, 21.233, -2.937, 0, 21.433, 0.056, 0, 21.533, -0.125, 0, 21.933, 5.393, 0, 22.267, -21.755, 0, 22.5, 29.423, 0, 22.667, -20.315, 0, 22.933, 21.207, 0, 23.267, -24.554, 0, 23.567, 20.707, 0, 23.967, -13.439, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param64", "Segments": [0, 0, 2, 0.367, 0, 0, 0.533, -15.352, 0, 0.833, 21.871, 0, 1.167, -21.332, 0, 1.5, 16.365, 0, 1.933, -12.921, 0, 2.4, 10.636, 0, 2.9, -8.132, 0, 3.3, 10.017, 0, 3.9, -8.907, 0, 4.3, 11.181, 0, 4.7, -14.568, 0, 5, 29.116, 0, 5.333, -28.939, 0, 5.667, 22.823, 0, 6.367, -14.207, 0, 6.767, 13.964, 0, 7.4, -12.847, 0, 7.867, 11.275, 0, 8.667, -10.602, 0, 9.167, 8.732, 0, 10.3, -16.489, 0, 10.533, 22.451, 0, 10.767, -23.509, 0, 11, 27.213, 0, 11.2, -27.841, 0, 11.4, 19.387, 0, 11.567, -22.696, 0, 11.767, 18.105, 0, 11.967, -19.621, 0, 12.167, 16.042, 0, 12.433, -12.018, 0, 12.7, 17.646, 0, 12.967, -27.259, 0, 13.333, 29.961, 0, 13.6, -29.875, 0, 13.867, 29.455, 0, 14.167, -29.702, 0, 14.433, 23.901, 0, 14.8, -21.744, 0, 15.167, 20.901, 0, 15.533, -16.179, 0, 15.967, 14.566, 0, 16.4, -15.909, 0, 16.8, 13.172, 0, 17.233, -7.812, 0, 17.567, -1.038, 0, 17.633, -1.302, 0, 17.933, 12.472, 0, 18.3, -17.066, 0, 18.667, 18.658, 0, 19.033, -19.459, 0, 19.567, 18.672, 0, 19.967, -15.939, 0, 20.4, 9.476, 0, 21.033, -6.339, 0, 21.633, 8.677, 0, 22.1, -19.644, 0, 22.367, 22.513, 1, 22.411, 22.513, 22.456, -16.019, 22.5, -16.019, 0, 22.733, 19.089, 0, 23.067, -23.635, 0, 23.433, 20.842, 0, 23.833, -14.846, 0, 24.2, 15.762, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param65", "Segments": [0, 0, 2, 0.367, 0, 0, 0.5, 7.715, 0, 0.7, -18.919, 0, 1, 25.013, 0, 1.333, -23.204, 0, 1.667, 17.673, 0, 2.033, -9.456, 0, 2.5, 6.463, 0, 3.1, -4.699, 0, 3.467, 6.92, 0, 3.733, -2.138, 0, 3.867, -0.973, 0, 4.133, -4.921, 0, 4.467, 10.322, 0, 4.867, -18.935, 0, 5.167, 29.568, 0, 5.5, -29.659, 0, 5.8, 22.072, 0, 6.1, -8.978, 0, 6.333, 4.901, 0, 6.6, -8.676, 0, 6.9, 9.638, 0, 7.2, -1.88, 0, 7.367, 0.392, 0, 7.6, -5.736, 0, 8, 6.577, 0, 8.3, -2.228, 0, 8.567, 2.974, 0, 8.867, -5.208, 0, 9.267, 4.533, 0, 9.567, -2.203, 0, 9.9, 2.254, 0, 10.133, 0.145, 0, 10.3, 3.438, 0, 10.433, -19.062, 0, 10.7, 29.333, 0, 10.933, -29.473, 0, 11.167, 29.547, 0, 11.333, -28.258, 0, 11.533, 26.387, 0, 11.733, -23.392, 0, 11.933, 23.333, 0, 12.133, -23.132, 0, 12.333, 19.521, 0, 12.633, -19.901, 0, 12.867, 26.464, 0, 13.167, -29.721, 0, 13.533, 29.987, 0, 13.8, -29.943, 0, 14.067, 29.876, 0, 14.333, -29.82, 0, 14.6, 28.962, 0, 14.967, -22.947, 0, 15.333, 20.772, 0, 15.667, -16.34, 0, 16.133, 9.692, 0, 16.533, -11.141, 0, 16.933, 10.375, 0, 17.333, -5.959, 0, 17.633, 3.363, 0, 17.867, -5.198, 0, 18.133, 12.707, 0, 18.467, -17.25, 0, 18.8, 18.732, 0, 19.167, -14.327, 0, 19.767, 9.582, 0, 20.1, -12.925, 0, 20.5, 7.744, 0, 20.8, -3.684, 0, 21.033, 1.583, 0, 21.233, -2.937, 0, 21.433, 0.056, 0, 21.533, -0.125, 0, 21.933, 5.393, 0, 22.267, -21.755, 0, 22.5, 29.423, 0, 22.667, -20.315, 0, 22.933, 21.207, 0, 23.267, -24.554, 0, 23.567, 20.707, 0, 23.967, -13.439, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param72", "Segments": [0, 0, 2, 0.367, 0, 0, 0.533, -15.352, 0, 0.833, 21.871, 0, 1.167, -21.332, 0, 1.5, 16.365, 0, 1.933, -12.921, 0, 2.4, 10.636, 0, 2.9, -8.132, 0, 3.3, 10.017, 0, 3.9, -8.907, 0, 4.3, 11.181, 0, 4.7, -14.568, 0, 5, 29.116, 0, 5.333, -28.939, 0, 5.667, 22.823, 0, 6.367, -14.207, 0, 6.767, 13.964, 0, 7.4, -12.847, 0, 7.867, 11.275, 0, 8.667, -10.602, 0, 9.167, 8.732, 0, 10.3, -16.489, 0, 10.533, 22.451, 0, 10.767, -23.509, 0, 11, 27.213, 0, 11.2, -27.841, 0, 11.4, 19.387, 0, 11.567, -22.696, 0, 11.767, 18.105, 0, 11.967, -19.621, 0, 12.167, 16.042, 0, 12.433, -12.018, 0, 12.7, 17.646, 0, 12.967, -27.259, 0, 13.333, 29.961, 0, 13.6, -29.875, 0, 13.867, 29.455, 0, 14.167, -29.702, 0, 14.433, 23.901, 0, 14.8, -21.744, 0, 15.167, 20.901, 0, 15.533, -16.179, 0, 15.967, 14.566, 0, 16.4, -15.909, 0, 16.8, 13.172, 0, 17.233, -7.812, 0, 17.567, -1.038, 0, 17.633, -1.302, 0, 17.933, 12.472, 0, 18.3, -17.066, 0, 18.667, 18.658, 0, 19.033, -19.459, 0, 19.567, 18.672, 0, 19.967, -15.939, 0, 20.4, 9.476, 0, 21.033, -6.339, 0, 21.633, 8.677, 0, 22.1, -19.644, 0, 22.367, 22.513, 1, 22.411, 22.513, 22.456, -16.019, 22.5, -16.019, 0, 22.733, 19.089, 0, 23.067, -23.635, 0, 23.433, 20.842, 0, 23.833, -14.846, 0, 24.2, 15.762, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param73", "Segments": [0, 0, 2, 0.367, 0, 0, 0.5, 7.715, 0, 0.7, -18.919, 0, 1, 25.013, 0, 1.333, -23.204, 0, 1.667, 17.673, 0, 2.033, -9.456, 0, 2.5, 6.463, 0, 3.1, -4.699, 0, 3.467, 6.92, 0, 3.733, -2.138, 0, 3.867, -0.973, 0, 4.133, -4.921, 0, 4.467, 10.322, 0, 4.867, -18.935, 0, 5.167, 29.568, 0, 5.5, -29.659, 0, 5.8, 22.072, 0, 6.1, -8.978, 0, 6.333, 4.901, 0, 6.6, -8.676, 0, 6.9, 9.638, 0, 7.2, -1.88, 0, 7.367, 0.392, 0, 7.6, -5.736, 0, 8, 6.577, 0, 8.3, -2.228, 0, 8.567, 2.974, 0, 8.867, -5.208, 0, 9.267, 4.533, 0, 9.567, -2.203, 0, 9.9, 2.254, 0, 10.133, 0.145, 0, 10.3, 3.438, 0, 10.433, -19.062, 0, 10.7, 29.333, 0, 10.933, -29.473, 0, 11.167, 29.547, 0, 11.333, -28.258, 0, 11.533, 26.387, 0, 11.733, -23.392, 0, 11.933, 23.333, 0, 12.133, -23.132, 0, 12.333, 19.521, 0, 12.633, -19.901, 0, 12.867, 26.464, 0, 13.167, -29.721, 0, 13.533, 29.987, 0, 13.8, -29.943, 0, 14.067, 29.876, 0, 14.333, -29.82, 0, 14.6, 28.962, 0, 14.967, -22.947, 0, 15.333, 20.772, 0, 15.667, -16.34, 0, 16.133, 9.692, 0, 16.533, -11.141, 0, 16.933, 10.375, 0, 17.333, -5.959, 0, 17.633, 3.363, 0, 17.867, -5.198, 0, 18.133, 12.707, 0, 18.467, -17.25, 0, 18.8, 18.732, 0, 19.167, -14.327, 0, 19.767, 9.582, 0, 20.1, -12.925, 0, 20.5, 7.744, 0, 20.8, -3.684, 0, 21.033, 1.583, 0, 21.233, -2.937, 0, 21.433, 0.056, 0, 21.533, -0.125, 0, 21.933, 5.393, 0, 22.267, -21.755, 0, 22.5, 29.423, 0, 22.667, -20.315, 0, 22.933, 21.207, 0, 23.267, -24.554, 0, 23.567, 20.707, 0, 23.967, -13.439, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param74", "Segments": [0, 0, 2, 0.367, 0, 0, 0.533, -15.352, 0, 0.833, 21.871, 0, 1.167, -21.332, 0, 1.5, 16.365, 0, 1.933, -12.921, 0, 2.4, 10.636, 0, 2.9, -8.132, 0, 3.3, 10.017, 0, 3.9, -8.907, 0, 4.3, 11.181, 0, 4.7, -14.568, 0, 5, 29.116, 0, 5.333, -28.939, 0, 5.667, 22.823, 0, 6.367, -14.207, 0, 6.767, 13.964, 0, 7.4, -12.847, 0, 7.867, 11.275, 0, 8.667, -10.602, 0, 9.167, 8.732, 0, 10.3, -16.489, 0, 10.533, 22.451, 0, 10.767, -23.509, 0, 11, 27.213, 0, 11.2, -27.841, 0, 11.4, 19.387, 0, 11.567, -22.696, 0, 11.767, 18.105, 0, 11.967, -19.621, 0, 12.167, 16.042, 0, 12.433, -12.018, 0, 12.7, 17.646, 0, 12.967, -27.259, 0, 13.333, 29.961, 0, 13.6, -29.875, 0, 13.867, 29.455, 0, 14.167, -29.702, 0, 14.433, 23.901, 0, 14.8, -21.744, 0, 15.167, 20.901, 0, 15.533, -16.179, 0, 15.967, 14.566, 0, 16.4, -15.909, 0, 16.8, 13.172, 0, 17.233, -7.812, 0, 17.567, -1.038, 0, 17.633, -1.302, 0, 17.933, 12.472, 0, 18.3, -17.066, 0, 18.667, 18.658, 0, 19.033, -19.459, 0, 19.567, 18.672, 0, 19.967, -15.939, 0, 20.4, 9.476, 0, 21.033, -6.339, 0, 21.633, 8.677, 0, 22.1, -19.644, 0, 22.367, 22.513, 1, 22.411, 22.513, 22.456, -16.019, 22.5, -16.019, 0, 22.733, 19.089, 0, 23.067, -23.635, 0, 23.433, 20.842, 0, 23.833, -14.846, 0, 24.2, 15.762, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param75", "Segments": [0, 0, 2, 0.367, 0, 0, 0.5, 7.715, 0, 0.7, -18.919, 0, 1, 25.013, 0, 1.333, -23.204, 0, 1.667, 17.673, 0, 2.033, -9.456, 0, 2.5, 6.463, 0, 3.1, -4.699, 0, 3.467, 6.92, 0, 3.733, -2.138, 0, 3.867, -0.973, 0, 4.133, -4.921, 0, 4.467, 10.322, 0, 4.867, -18.935, 0, 5.167, 29.568, 0, 5.5, -29.659, 0, 5.8, 22.072, 0, 6.1, -8.978, 0, 6.333, 4.901, 0, 6.6, -8.676, 0, 6.9, 9.638, 0, 7.2, -1.88, 0, 7.367, 0.392, 0, 7.6, -5.736, 0, 8, 6.577, 0, 8.3, -2.228, 0, 8.567, 2.974, 0, 8.867, -5.208, 0, 9.267, 4.533, 0, 9.567, -2.203, 0, 9.9, 2.254, 0, 10.133, 0.145, 0, 10.3, 3.438, 0, 10.433, -19.062, 0, 10.7, 29.333, 0, 10.933, -29.473, 0, 11.167, 29.547, 0, 11.333, -28.258, 0, 11.533, 26.387, 0, 11.733, -23.392, 0, 11.933, 23.333, 0, 12.133, -23.132, 0, 12.333, 19.521, 0, 12.633, -19.901, 0, 12.867, 26.464, 0, 13.167, -29.721, 0, 13.533, 29.987, 0, 13.8, -29.943, 0, 14.067, 29.876, 0, 14.333, -29.82, 0, 14.6, 28.962, 0, 14.967, -22.947, 0, 15.333, 20.772, 0, 15.667, -16.34, 0, 16.133, 9.692, 0, 16.533, -11.141, 0, 16.933, 10.375, 0, 17.333, -5.959, 0, 17.633, 3.363, 0, 17.867, -5.198, 0, 18.133, 12.707, 0, 18.467, -17.25, 0, 18.8, 18.732, 0, 19.167, -14.327, 0, 19.767, 9.582, 0, 20.1, -12.925, 0, 20.5, 7.744, 0, 20.8, -3.684, 0, 21.033, 1.583, 0, 21.233, -2.937, 0, 21.433, 0.056, 0, 21.533, -0.125, 0, 21.933, 5.393, 0, 22.267, -21.755, 0, 22.5, 29.423, 0, 22.667, -20.315, 0, 22.933, 21.207, 0, 23.267, -24.554, 0, 23.567, 20.707, 0, 23.967, -13.439, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param53", "Segments": [0, 0, 2, 0.367, 0, 0, 0.533, -15.352, 0, 0.833, 21.871, 0, 1.167, -21.332, 0, 1.5, 16.365, 0, 1.933, -12.921, 0, 2.4, 10.636, 0, 2.9, -8.132, 0, 3.3, 10.017, 0, 3.9, -8.907, 0, 4.3, 11.181, 0, 4.7, -14.568, 0, 5, 29.116, 0, 5.333, -28.939, 0, 5.667, 22.823, 0, 6.367, -14.207, 0, 6.767, 13.964, 0, 7.4, -12.847, 0, 7.867, 11.275, 0, 8.667, -10.602, 0, 9.167, 8.732, 0, 10.3, -16.489, 0, 10.533, 22.451, 0, 10.767, -23.509, 0, 11, 27.213, 0, 11.2, -27.841, 0, 11.4, 19.387, 0, 11.567, -22.696, 0, 11.767, 18.105, 0, 11.967, -19.621, 0, 12.167, 16.042, 0, 12.433, -12.018, 0, 12.7, 17.646, 0, 12.967, -27.259, 0, 13.333, 29.961, 0, 13.6, -29.875, 0, 13.867, 29.455, 0, 14.167, -29.702, 0, 14.433, 23.901, 0, 14.8, -21.744, 0, 15.167, 20.901, 0, 15.533, -16.179, 0, 15.967, 14.566, 0, 16.4, -15.909, 0, 16.8, 13.172, 0, 17.233, -7.812, 0, 17.567, -1.038, 0, 17.633, -1.302, 0, 17.933, 12.472, 0, 18.3, -17.066, 0, 18.667, 18.658, 0, 19.033, -19.459, 0, 19.567, 18.672, 0, 19.967, -15.939, 0, 20.4, 9.476, 0, 21.033, -6.339, 0, 21.633, 8.677, 0, 22.1, -19.644, 0, 22.367, 22.513, 1, 22.411, 22.513, 22.456, -16.019, 22.5, -16.019, 0, 22.733, 19.089, 0, 23.067, -23.635, 0, 23.433, 20.842, 0, 23.833, -14.846, 0, 24.2, 15.762, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param54", "Segments": [0, 0, 2, 0.367, 0, 0, 0.5, 7.715, 0, 0.7, -18.919, 0, 1, 25.013, 0, 1.333, -23.204, 0, 1.667, 17.673, 0, 2.033, -9.456, 0, 2.5, 6.463, 0, 3.1, -4.699, 0, 3.467, 6.92, 0, 3.733, -2.138, 0, 3.867, -0.973, 0, 4.133, -4.921, 0, 4.467, 10.322, 0, 4.867, -18.935, 0, 5.167, 29.568, 0, 5.5, -29.659, 0, 5.8, 22.072, 0, 6.1, -8.978, 0, 6.333, 4.901, 0, 6.6, -8.676, 0, 6.9, 9.638, 0, 7.2, -1.88, 0, 7.367, 0.392, 0, 7.6, -5.736, 0, 8, 6.577, 0, 8.3, -2.228, 0, 8.567, 2.974, 0, 8.867, -5.208, 0, 9.267, 4.533, 0, 9.567, -2.203, 0, 9.9, 2.254, 0, 10.133, 0.145, 0, 10.3, 3.438, 0, 10.433, -19.062, 0, 10.7, 29.333, 0, 10.933, -29.473, 0, 11.167, 29.547, 0, 11.333, -28.258, 0, 11.533, 26.387, 0, 11.733, -23.392, 0, 11.933, 23.333, 0, 12.133, -23.132, 0, 12.333, 19.521, 0, 12.633, -19.901, 0, 12.867, 26.464, 0, 13.167, -29.721, 0, 13.533, 29.987, 0, 13.8, -29.943, 0, 14.067, 29.876, 0, 14.333, -29.82, 0, 14.6, 28.962, 0, 14.967, -22.947, 0, 15.333, 20.772, 0, 15.667, -16.34, 0, 16.133, 9.692, 0, 16.533, -11.141, 0, 16.933, 10.375, 0, 17.333, -5.959, 0, 17.633, 3.363, 0, 17.867, -5.198, 0, 18.133, 12.707, 0, 18.467, -17.25, 0, 18.8, 18.732, 0, 19.167, -14.327, 0, 19.767, 9.582, 0, 20.1, -12.925, 0, 20.5, 7.744, 0, 20.8, -3.684, 0, 21.033, 1.583, 0, 21.233, -2.937, 0, 21.433, 0.056, 0, 21.533, -0.125, 0, 21.933, 5.393, 0, 22.267, -21.755, 0, 22.5, 29.423, 0, 22.667, -20.315, 0, 22.933, 21.207, 0, 23.267, -24.554, 0, 23.567, 20.707, 0, 23.967, -13.439, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param81", "Segments": [0, 0, 2, 0.367, 0, 0, 0.533, -15.352, 0, 0.833, 21.871, 0, 1.167, -21.332, 0, 1.5, 16.365, 0, 1.933, -12.921, 0, 2.4, 10.636, 0, 2.9, -8.132, 0, 3.3, 10.017, 0, 3.9, -8.907, 0, 4.3, 11.181, 0, 4.7, -14.568, 0, 5, 29.116, 0, 5.333, -28.939, 0, 5.667, 22.823, 0, 6.367, -14.207, 0, 6.767, 13.964, 0, 7.4, -12.847, 0, 7.867, 11.275, 0, 8.667, -10.602, 0, 9.167, 8.732, 0, 10.3, -16.489, 0, 10.533, 22.451, 0, 10.767, -23.509, 0, 11, 27.213, 0, 11.2, -27.841, 0, 11.4, 19.387, 0, 11.567, -22.696, 0, 11.767, 18.105, 0, 11.967, -19.621, 0, 12.167, 16.042, 0, 12.433, -12.018, 0, 12.7, 17.646, 0, 12.967, -27.259, 0, 13.333, 29.961, 0, 13.6, -29.875, 0, 13.867, 29.455, 0, 14.167, -29.702, 0, 14.433, 23.901, 0, 14.8, -21.744, 0, 15.167, 20.901, 0, 15.533, -16.179, 0, 15.967, 14.566, 0, 16.4, -15.909, 0, 16.8, 13.172, 0, 17.233, -7.812, 0, 17.567, -1.038, 0, 17.633, -1.302, 0, 17.933, 12.472, 0, 18.3, -17.066, 0, 18.667, 18.658, 0, 19.033, -19.459, 0, 19.567, 18.672, 0, 19.967, -15.939, 0, 20.4, 9.476, 0, 21.033, -6.339, 0, 21.633, 8.677, 0, 22.1, -19.644, 0, 22.367, 22.513, 1, 22.411, 22.513, 22.456, -16.019, 22.5, -16.019, 0, 22.733, 19.089, 0, 23.067, -23.635, 0, 23.433, 20.842, 0, 23.833, -14.846, 0, 24.2, 15.762, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param82", "Segments": [0, 0, 2, 0.367, 0, 0, 0.5, 7.715, 0, 0.7, -18.919, 0, 1, 25.013, 0, 1.333, -23.204, 0, 1.667, 17.673, 0, 2.033, -9.456, 0, 2.5, 6.463, 0, 3.1, -4.699, 0, 3.467, 6.92, 0, 3.733, -2.138, 0, 3.867, -0.973, 0, 4.133, -4.921, 0, 4.467, 10.322, 0, 4.867, -18.935, 0, 5.167, 29.568, 0, 5.5, -29.659, 0, 5.8, 22.072, 0, 6.1, -8.978, 0, 6.333, 4.901, 0, 6.6, -8.676, 0, 6.9, 9.638, 0, 7.2, -1.88, 0, 7.367, 0.392, 0, 7.6, -5.736, 0, 8, 6.577, 0, 8.3, -2.228, 0, 8.567, 2.974, 0, 8.867, -5.208, 0, 9.267, 4.533, 0, 9.567, -2.203, 0, 9.9, 2.254, 0, 10.133, 0.145, 0, 10.3, 3.438, 0, 10.433, -19.062, 0, 10.7, 29.333, 0, 10.933, -29.473, 0, 11.167, 29.547, 0, 11.333, -28.258, 0, 11.533, 26.387, 0, 11.733, -23.392, 0, 11.933, 23.333, 0, 12.133, -23.132, 0, 12.333, 19.521, 0, 12.633, -19.901, 0, 12.867, 26.464, 0, 13.167, -29.721, 0, 13.533, 29.987, 0, 13.8, -29.943, 0, 14.067, 29.876, 0, 14.333, -29.82, 0, 14.6, 28.962, 0, 14.967, -22.947, 0, 15.333, 20.772, 0, 15.667, -16.34, 0, 16.133, 9.692, 0, 16.533, -11.141, 0, 16.933, 10.375, 0, 17.333, -5.959, 0, 17.633, 3.363, 0, 17.867, -5.198, 0, 18.133, 12.707, 0, 18.467, -17.25, 0, 18.8, 18.732, 0, 19.167, -14.327, 0, 19.767, 9.582, 0, 20.1, -12.925, 0, 20.5, 7.744, 0, 20.8, -3.684, 0, 21.033, 1.583, 0, 21.233, -2.937, 0, 21.433, 0.056, 0, 21.533, -0.125, 0, 21.933, 5.393, 0, 22.267, -21.755, 0, 22.5, 29.423, 0, 22.667, -20.315, 0, 22.933, 21.207, 0, 23.267, -24.554, 0, 23.567, 20.707, 0, 23.967, -13.439, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param70", "Segments": [0, 0, 2, 0.367, 0, 0, 0.533, -15.352, 0, 0.833, 21.871, 0, 1.167, -21.332, 0, 1.5, 16.365, 0, 1.933, -12.921, 0, 2.4, 10.636, 0, 2.9, -8.132, 0, 3.3, 10.017, 0, 3.9, -8.907, 0, 4.3, 11.181, 0, 4.7, -14.568, 0, 5, 29.116, 0, 5.333, -28.939, 0, 5.667, 22.823, 0, 6.367, -14.207, 0, 6.767, 13.964, 0, 7.4, -12.847, 0, 7.867, 11.275, 0, 8.667, -10.602, 0, 9.167, 8.732, 0, 10.3, -16.489, 0, 10.533, 22.451, 0, 10.767, -23.509, 0, 11, 27.213, 0, 11.2, -27.841, 0, 11.4, 19.387, 0, 11.567, -22.696, 0, 11.767, 18.105, 0, 11.967, -19.621, 0, 12.167, 16.042, 0, 12.433, -12.018, 0, 12.7, 17.646, 0, 12.967, -27.259, 0, 13.333, 29.961, 0, 13.6, -29.875, 0, 13.867, 29.455, 0, 14.167, -29.702, 0, 14.433, 23.901, 0, 14.8, -21.744, 0, 15.167, 20.901, 0, 15.533, -16.179, 0, 15.967, 14.566, 0, 16.4, -15.909, 0, 16.8, 13.172, 0, 17.233, -7.812, 0, 17.567, -1.038, 0, 17.633, -1.302, 0, 17.933, 12.472, 0, 18.3, -17.066, 0, 18.667, 18.658, 0, 19.033, -19.459, 0, 19.567, 18.672, 0, 19.967, -15.939, 0, 20.4, 9.476, 0, 21.033, -6.339, 0, 21.633, 8.677, 0, 22.1, -19.644, 0, 22.367, 22.513, 1, 22.411, 22.513, 22.456, -16.019, 22.5, -16.019, 0, 22.733, 19.089, 0, 23.067, -23.635, 0, 23.433, 20.842, 0, 23.833, -14.846, 0, 24.2, 15.762, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param71", "Segments": [0, 0, 2, 0.367, 0, 0, 0.5, 7.715, 0, 0.7, -18.919, 0, 1, 25.013, 0, 1.333, -23.204, 0, 1.667, 17.673, 0, 2.033, -9.456, 0, 2.5, 6.463, 0, 3.1, -4.699, 0, 3.467, 6.92, 0, 3.733, -2.138, 0, 3.867, -0.973, 0, 4.133, -4.921, 0, 4.467, 10.322, 0, 4.867, -18.935, 0, 5.167, 29.568, 0, 5.5, -29.659, 0, 5.8, 22.072, 0, 6.1, -8.978, 0, 6.333, 4.901, 0, 6.6, -8.676, 0, 6.9, 9.638, 0, 7.2, -1.88, 0, 7.367, 0.392, 0, 7.6, -5.736, 0, 8, 6.577, 0, 8.3, -2.228, 0, 8.567, 2.974, 0, 8.867, -5.208, 0, 9.267, 4.533, 0, 9.567, -2.203, 0, 9.9, 2.254, 0, 10.133, 0.145, 0, 10.3, 3.438, 0, 10.433, -19.062, 0, 10.7, 29.333, 0, 10.933, -29.473, 0, 11.167, 29.547, 0, 11.333, -28.258, 0, 11.533, 26.387, 0, 11.733, -23.392, 0, 11.933, 23.333, 0, 12.133, -23.132, 0, 12.333, 19.521, 0, 12.633, -19.901, 0, 12.867, 26.464, 0, 13.167, -29.721, 0, 13.533, 29.987, 0, 13.8, -29.943, 0, 14.067, 29.876, 0, 14.333, -29.82, 0, 14.6, 28.962, 0, 14.967, -22.947, 0, 15.333, 20.772, 0, 15.667, -16.34, 0, 16.133, 9.692, 0, 16.533, -11.141, 0, 16.933, 10.375, 0, 17.333, -5.959, 0, 17.633, 3.363, 0, 17.867, -5.198, 0, 18.133, 12.707, 0, 18.467, -17.25, 0, 18.8, 18.732, 0, 19.167, -14.327, 0, 19.767, 9.582, 0, 20.1, -12.925, 0, 20.5, 7.744, 0, 20.8, -3.684, 0, 21.033, 1.583, 0, 21.233, -2.937, 0, 21.433, 0.056, 0, 21.533, -0.125, 0, 21.933, 5.393, 0, 22.267, -21.755, 0, 22.5, 29.423, 0, 22.667, -20.315, 0, 22.933, 21.207, 0, 23.267, -24.554, 0, 23.567, 20.707, 0, 23.967, -13.439, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param77", "Segments": [0, 0, 2, 0.367, 0, 0, 0.533, -15.352, 0, 0.833, 21.871, 0, 1.167, -21.332, 0, 1.5, 16.365, 0, 1.933, -12.921, 0, 2.4, 10.636, 0, 2.9, -8.132, 0, 3.3, 10.017, 0, 3.9, -8.907, 0, 4.3, 11.181, 0, 4.7, -14.568, 0, 5, 29.116, 0, 5.333, -28.939, 0, 5.667, 22.823, 0, 6.367, -14.207, 0, 6.767, 13.964, 0, 7.4, -12.847, 0, 7.867, 11.275, 0, 8.667, -10.602, 0, 9.167, 8.732, 0, 10.3, -16.489, 0, 10.533, 22.451, 0, 10.767, -23.509, 0, 11, 27.213, 0, 11.2, -27.841, 0, 11.4, 19.387, 0, 11.567, -22.696, 0, 11.767, 18.105, 0, 11.967, -19.621, 0, 12.167, 16.042, 0, 12.433, -12.018, 0, 12.7, 17.646, 0, 12.967, -27.259, 0, 13.333, 29.961, 0, 13.6, -29.875, 0, 13.867, 29.455, 0, 14.167, -29.702, 0, 14.433, 23.901, 0, 14.8, -21.744, 0, 15.167, 20.901, 0, 15.533, -16.179, 0, 15.967, 14.566, 0, 16.4, -15.909, 0, 16.8, 13.172, 0, 17.233, -7.812, 0, 17.567, -1.038, 0, 17.633, -1.302, 0, 17.933, 12.472, 0, 18.3, -17.066, 0, 18.667, 18.658, 0, 19.033, -19.459, 0, 19.567, 18.672, 0, 19.967, -15.939, 0, 20.4, 9.476, 0, 21.033, -6.339, 0, 21.633, 8.677, 0, 22.1, -19.644, 0, 22.367, 22.513, 1, 22.411, 22.513, 22.456, -16.019, 22.5, -16.019, 0, 22.733, 19.089, 0, 23.067, -23.635, 0, 23.433, 20.842, 0, 23.833, -14.846, 0, 24.2, 15.762, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param78", "Segments": [0, 0, 2, 0.367, 0, 0, 0.5, 7.715, 0, 0.7, -18.919, 0, 1, 25.013, 0, 1.333, -23.204, 0, 1.667, 17.673, 0, 2.033, -9.456, 0, 2.5, 6.463, 0, 3.1, -4.699, 0, 3.467, 6.92, 0, 3.733, -2.138, 0, 3.867, -0.973, 0, 4.133, -4.921, 0, 4.467, 10.322, 0, 4.867, -18.935, 0, 5.167, 29.568, 0, 5.5, -29.659, 0, 5.8, 22.072, 0, 6.1, -8.978, 0, 6.333, 4.901, 0, 6.6, -8.676, 0, 6.9, 9.638, 0, 7.2, -1.88, 0, 7.367, 0.392, 0, 7.6, -5.736, 0, 8, 6.577, 0, 8.3, -2.228, 0, 8.567, 2.974, 0, 8.867, -5.208, 0, 9.267, 4.533, 0, 9.567, -2.203, 0, 9.9, 2.254, 0, 10.133, 0.145, 0, 10.3, 3.438, 0, 10.433, -19.062, 0, 10.7, 29.333, 0, 10.933, -29.473, 0, 11.167, 29.547, 0, 11.333, -28.258, 0, 11.533, 26.387, 0, 11.733, -23.392, 0, 11.933, 23.333, 0, 12.133, -23.132, 0, 12.333, 19.521, 0, 12.633, -19.901, 0, 12.867, 26.464, 0, 13.167, -29.721, 0, 13.533, 29.987, 0, 13.8, -29.943, 0, 14.067, 29.876, 0, 14.333, -29.82, 0, 14.6, 28.962, 0, 14.967, -22.947, 0, 15.333, 20.772, 0, 15.667, -16.34, 0, 16.133, 9.692, 0, 16.533, -11.141, 0, 16.933, 10.375, 0, 17.333, -5.959, 0, 17.633, 3.363, 0, 17.867, -5.198, 0, 18.133, 12.707, 0, 18.467, -17.25, 0, 18.8, 18.732, 0, 19.167, -14.327, 0, 19.767, 9.582, 0, 20.1, -12.925, 0, 20.5, 7.744, 0, 20.8, -3.684, 0, 21.033, 1.583, 0, 21.233, -2.937, 0, 21.433, 0.056, 0, 21.533, -0.125, 0, 21.933, 5.393, 0, 22.267, -21.755, 0, 22.5, 29.423, 0, 22.667, -20.315, 0, 22.933, 21.207, 0, 23.267, -24.554, 0, 23.567, 20.707, 0, 23.967, -13.439, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param79", "Segments": [0, 0, 2, 0.367, 0, 0, 0.533, -15.352, 0, 0.833, 21.871, 0, 1.167, -21.332, 0, 1.5, 16.365, 0, 1.933, -12.921, 0, 2.4, 10.636, 0, 2.9, -8.132, 0, 3.3, 10.017, 0, 3.9, -8.907, 0, 4.3, 11.181, 0, 4.7, -14.568, 0, 5, 29.116, 0, 5.333, -28.939, 0, 5.667, 22.823, 0, 6.367, -14.207, 0, 6.767, 13.964, 0, 7.4, -12.847, 0, 7.867, 11.275, 0, 8.667, -10.602, 0, 9.167, 8.732, 0, 10.3, -16.489, 0, 10.533, 22.451, 0, 10.767, -23.509, 0, 11, 27.213, 0, 11.2, -27.841, 0, 11.4, 19.387, 0, 11.567, -22.696, 0, 11.767, 18.105, 0, 11.967, -19.621, 0, 12.167, 16.042, 0, 12.433, -12.018, 0, 12.7, 17.646, 0, 12.967, -27.259, 0, 13.333, 29.961, 0, 13.6, -29.875, 0, 13.867, 29.455, 0, 14.167, -29.702, 0, 14.433, 23.901, 0, 14.8, -21.744, 0, 15.167, 20.901, 0, 15.533, -16.179, 0, 15.967, 14.566, 0, 16.4, -15.909, 0, 16.8, 13.172, 0, 17.233, -7.812, 0, 17.567, -1.038, 0, 17.633, -1.302, 0, 17.933, 12.472, 0, 18.3, -17.066, 0, 18.667, 18.658, 0, 19.033, -19.459, 0, 19.567, 18.672, 0, 19.967, -15.939, 0, 20.4, 9.476, 0, 21.033, -6.339, 0, 21.633, 8.677, 0, 22.1, -19.644, 0, 22.367, 22.513, 1, 22.411, 22.513, 22.456, -16.019, 22.5, -16.019, 0, 22.733, 19.089, 0, 23.067, -23.635, 0, 23.433, 20.842, 0, 23.833, -14.846, 0, 24.2, 15.762, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param80", "Segments": [0, 0, 2, 0.367, 0, 0, 0.5, 7.715, 0, 0.7, -18.919, 0, 1, 25.013, 0, 1.333, -23.204, 0, 1.667, 17.673, 0, 2.033, -9.456, 0, 2.5, 6.463, 0, 3.1, -4.699, 0, 3.467, 6.92, 0, 3.733, -2.138, 0, 3.867, -0.973, 0, 4.133, -4.921, 0, 4.467, 10.322, 0, 4.867, -18.935, 0, 5.167, 29.568, 0, 5.5, -29.659, 0, 5.8, 22.072, 0, 6.1, -8.978, 0, 6.333, 4.901, 0, 6.6, -8.676, 0, 6.9, 9.638, 0, 7.2, -1.88, 0, 7.367, 0.392, 0, 7.6, -5.736, 0, 8, 6.577, 0, 8.3, -2.228, 0, 8.567, 2.974, 0, 8.867, -5.208, 0, 9.267, 4.533, 0, 9.567, -2.203, 0, 9.9, 2.254, 0, 10.133, 0.145, 0, 10.3, 3.438, 0, 10.433, -19.062, 0, 10.7, 29.333, 0, 10.933, -29.473, 0, 11.167, 29.547, 0, 11.333, -28.258, 0, 11.533, 26.387, 0, 11.733, -23.392, 0, 11.933, 23.333, 0, 12.133, -23.132, 0, 12.333, 19.521, 0, 12.633, -19.901, 0, 12.867, 26.464, 0, 13.167, -29.721, 0, 13.533, 29.987, 0, 13.8, -29.943, 0, 14.067, 29.876, 0, 14.333, -29.82, 0, 14.6, 28.962, 0, 14.967, -22.947, 0, 15.333, 20.772, 0, 15.667, -16.34, 0, 16.133, 9.692, 0, 16.533, -11.141, 0, 16.933, 10.375, 0, 17.333, -5.959, 0, 17.633, 3.363, 0, 17.867, -5.198, 0, 18.133, 12.707, 0, 18.467, -17.25, 0, 18.8, 18.732, 0, 19.167, -14.327, 0, 19.767, 9.582, 0, 20.1, -12.925, 0, 20.5, 7.744, 0, 20.8, -3.684, 0, 21.033, 1.583, 0, 21.233, -2.937, 0, 21.433, 0.056, 0, 21.533, -0.125, 0, 21.933, 5.393, 0, 22.267, -21.755, 0, 22.5, 29.423, 0, 22.667, -20.315, 0, 22.933, 21.207, 0, 23.267, -24.554, 0, 23.567, 20.707, 0, 23.967, -13.439, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param294", "Segments": [0, 1, 2, 0.333, 1, 0, 0.4, 0, 2, 24.167, 0, 0, 24.267, 1]}, {"Target": "Parameter", "Id": "Param47", "Segments": [0, 0, 2, 0.367, 0, 0, 0.533, -24.974, 0, 0.8, 30, 0, 1.133, -28.714, 0, 1.5, 22.579, 0, 1.933, -17.416, 0, 2.367, 15.318, 0, 2.9, -14.385, 0, 3.3, 14.666, 0, 3.867, -13.556, 0, 4.233, 11.748, 0, 4.4, 8.843, 0, 4.567, 19.825, 0, 4.733, -30, 2, 4.867, -30, 0, 5.1, 30, 0, 5.467, -22.538, 0, 5.867, 14.297, 0, 6.433, -7.699, 0, 7.1, 9.934, 0, 7.7, -7.556, 0, 8.367, 6.372, 0, 9.167, -6.328, 0, 9.833, 4.574, 0, 9.933, 4.459, 0, 10.3, 8.34, 0, 10.6, -28.689, 0, 10.9, 30, 2, 11, 30, 0, 11.133, -30, 2, 11.2, -30, 0, 11.367, 24.435, 0, 11.5, -30, 2, 11.567, -30, 0, 11.733, 27.451, 0, 11.933, -30, 2, 11.967, -30, 0, 12.133, 26.616, 0, 12.4, -21.284, 0, 12.7, 26.667, 0, 12.967, -30, 2, 13.033, -30, 0, 13.233, 30, 2, 13.333, 30, 0, 13.533, -30, 2, 13.6, -30, 0, 13.8, 30, 2, 13.9, 30, 0, 14.1, -30, 2, 14.2, -30, 0, 14.433, 25.04, 0, 14.833, -26.42, 0, 15.167, 27.622, 0, 15.533, -22.273, 0, 16, 18.607, 0, 16.4, -20.324, 0, 16.767, 20.66, 0, 17.233, -15.003, 0, 17.767, 14.235, 0, 18.233, -17.994, 0, 18.633, 20.466, 0, 19.033, -20.378, 0, 19.567, 20.784, 0, 19.967, -19.485, 0, 20.467, 12.348, 0, 21, -15.094, 0, 21.5, 16.26, 0, 22, -18.197, 0, 22.3, 25.971, 0, 22.467, -30, 2, 22.5, -30, 0, 22.733, 27.105, 0, 23.067, -30, 0, 23.433, 26.332, 0, 23.833, -20.439, 0, 24.2, 20.994, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param48", "Segments": [0, 0, 2, 0.367, 0, 0, 0.467, 11.075, 0, 0.667, -29.285, 0, 0.933, 30, 2, 1, 30, 0, 1.3, -28.269, 0, 1.667, 20.452, 0, 2.067, -12.315, 0, 2.5, 9.954, 0, 3.1, -7.737, 0, 3.433, 9.935, 0, 3.733, -1.993, 0, 3.833, -0.978, 0, 4.1, -7.576, 0, 4.367, 7.989, 0, 4.567, -8.171, 0, 4.7, 20.965, 0, 4.9, -30, 2, 5.067, -30, 0, 5.3, 29.182, 0, 5.633, -21.218, 0, 6, 12.863, 0, 6.3, -4.058, 0, 6.7, -0.014, 0, 6.967, -2.847, 0, 7.267, 5.31, 0, 7.567, -1.181, 0, 7.633, -1.037, 0, 7.867, -1.807, 0, 8.133, -0.37, 0, 8.233, -0.661, 0, 8.5, 2.049, 0, 8.767, -0.332, 0, 9.033, 1.326, 0, 9.333, -2.301, 0, 9.633, 0.078, 0, 9.733, -0.302, 0, 9.933, 0.913, 0, 10.2, -1.449, 0, 10.5, 12.73, 0, 10.8, -29.139, 0, 11.033, 30, 2, 11.1, 30, 1, 11.111, 30, 11.122, -30, 11.133, -30, 0, 11.2, -12.384, 0, 11.267, -30, 2, 11.367, -30, 0, 11.5, 19.209, 0, 11.633, -30, 2, 11.733, -30, 0, 11.867, 24.92, 0, 12.033, -30, 2, 12.1, -30, 0, 12.3, 27.916, 0, 12.567, -27.75, 0, 12.833, 30, 2, 12.9, 30, 0, 13.1, -30, 2, 13.2, -30, 0, 13.367, 30, 2, 13.567, 30, 1, 13.611, 30, 13.656, -30, 13.7, -30, 2, 13.733, -30, 0, 13.967, 30, 2, 14.067, 30, 0, 14.267, -30, 2, 14.367, -30, 0, 14.6, 27.355, 0, 15, -29.008, 0, 15.333, 25.877, 0, 15.7, -18.469, 0, 16.167, 13.285, 0, 16.567, -17.051, 0, 16.9, 16.512, 0, 17.333, -8.724, 0, 17.967, 6.932, 0, 18.4, -12.798, 0, 18.767, 16.751, 0, 19.167, -13.908, 0, 19.433, 0.955, 0, 19.467, 0.878, 0, 19.767, 11.534, 0, 20.1, -15.036, 0, 20.467, 4.526, 0, 20.5, 4.49, 0, 20.533, 4.492, 0, 21.2, -7.639, 0, 21.633, 8.253, 0, 21.9, 0.762, 0, 21.933, 0.882, 0, 22.2, -17.622, 0, 22.4, 30, 2, 22.467, 30, 0, 22.633, -26.218, 0, 22.9, 30, 2, 22.933, 30, 0, 23.233, -30, 2, 23.267, -30, 0, 23.567, 23.524, 0, 24, -17.835, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param56", "Segments": [0, 0, 2, 0.367, 0, 0, 0.567, -11.352, 0, 0.867, 14.388, 0, 1.233, -11.695, 0, 1.667, 8.508, 0, 2.1, -8.212, 0, 2.533, 7.964, 0, 3, -9.54, 0, 3.467, 8.6, 0, 3.967, -10.153, 0, 4.567, 14.174, 0, 4.867, -20.176, 0, 5.233, 10.639, 0, 5.633, -7.638, 0, 6.1, 6.018, 0, 6.633, -6.959, 0, 7.167, 8.224, 0, 7.767, -7.488, 0, 8.367, 6.239, 0, 9.133, -4.521, 0, 9.867, 3.736, 0, 10.133, 3.367, 0, 10.3, 3.753, 0, 10.667, -15.811, 0, 11, 19.808, 0, 11.2, -9.918, 0, 11.367, 15.364, 0, 11.567, -12.095, 0, 11.767, 11.993, 0, 11.967, -12.484, 0, 12.167, 8.691, 0, 12.433, -7.578, 0, 12.733, 9.954, 0, 13.033, -13.572, 0, 13.333, 18.18, 0, 13.6, -12.743, 0, 13.9, 13.757, 0, 14.2, -14.264, 0, 14.533, 8.443, 0, 14.9, -10.608, 0, 15.267, 10.842, 0, 15.7, -9.601, 0, 16.1, 9.155, 0, 16.5, -8.503, 0, 16.9, 8.452, 0, 17.4, -8.947, 0, 17.867, 10.138, 0, 18.333, -11.19, 0, 18.733, 10.044, 0, 19.167, -10.214, 0, 19.667, 13.802, 0, 20.133, -10.228, 0, 20.633, 9.634, 0, 21.1, -10.954, 0, 21.6, 11.726, 0, 22.067, -12.196, 0, 22.367, 6.168, 0, 22.5, -11.71, 0, 22.767, 14.151, 0, 23.167, -14.124, 0, 23.567, 10.452, 0, 23.967, -8.308, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param57", "Segments": [0, 0, 2, 0.367, 0, 0, 0.5, 6.084, 0, 0.7, -16.418, 0, 1.033, 21.068, 0, 1.4, -15.883, 0, 1.833, 9.921, 0, 2.267, -9.032, 0, 2.767, 7.272, 0, 3.2, -9.854, 0, 3.667, 6.797, 0, 4.167, -9.953, 0, 4.467, 1.886, 0, 4.567, -0.604, 0, 4.7, 19.53, 0, 5, -30, 2, 5.067, -30, 0, 5.4, 17.402, 0, 5.8, -9.399, 0, 6.333, 4.841, 0, 6.9, -5.35, 0, 7.367, 5.905, 0, 7.967, -4.605, 0, 8.6, 3.588, 0, 9.4, -2.67, 0, 10.033, 1.446, 0, 10.267, 0.754, 0, 10.533, 8.398, 0, 10.867, -23.207, 0, 11.1, 29.822, 0, 11.133, 12, 0, 11.2, 17.904, 0, 11.367, -19.228, 0, 11.567, 0.462, 0, 11.7, -14.526, 0, 11.9, 17.684, 0, 12.1, -19.449, 0, 12.333, 13.071, 0, 12.633, -13.897, 0, 12.9, 16.828, 0, 13.2, -24.259, 0, 13.5, 30, 0, 13.767, -22.029, 0, 14.067, 25.438, 0, 14.367, -22.04, 0, 14.733, 14.026, 0, 15.067, -16.758, 0, 15.433, 14.579, 0, 15.9, -10.591, 0, 16.3, 11.298, 0, 16.7, -11.34, 0, 17.1, 8.827, 0, 17.6, -7.739, 0, 18.1, 9.613, 0, 18.533, -12.488, 0, 18.933, 11.992, 0, 19.4, -9.464, 0, 19.867, 14.603, 0, 20.3, -8.138, 0, 20.867, 8.287, 0, 21.333, -9.787, 0, 21.833, 9.207, 0, 22.267, -16.332, 0, 22.467, 16.186, 0, 22.667, -11.656, 0, 22.967, 18.991, 0, 23.333, -19.931, 0, 23.767, 12.766, 0, 24.133, -11.574, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param58", "Segments": [0, 0, 2, 0.367, 0, 0, 0.6, 7.912, 0, 0.8, -16.36, 0, 1.133, 23.683, 0, 1.5, -20.44, 0, 1.9, 14.647, 0, 2.333, -11.745, 0, 2.733, 9.185, 0, 3.267, -11.041, 0, 3.633, 9.394, 0, 4.233, -10.48, 0, 4.5, 4.857, 0, 4.667, 0.062, 0, 4.767, 15.964, 0, 5.1, -26.863, 1, 5.111, -26.863, 5.122, -12, 5.133, -12, 2, 5.167, -12, 0, 5.533, 18.804, 0, 5.867, -14.223, 0, 6.233, 8.32, 0, 6.933, -4.449, 0, 7.4, 6.625, 0, 7.967, -3.807, 0, 8.633, 3.129, 0, 9, -0.706, 0, 9.2, -0.047, 0, 9.5, -1.975, 0, 10.067, 1.041, 0, 10.333, -0.996, 0, 10.6, 9.118, 0, 10.933, -22.153, 0, 11.033, -13.179, 0, 11.133, -24.223, 0, 11.3, 24.148, 0, 11.567, -3.016, 0, 11.6, -1.157, 0, 11.8, -13.91, 0, 12.033, 25.541, 0, 12.233, -21.758, 0, 12.5, 17.186, 0, 12.767, -17.957, 0, 13.033, 19.255, 0, 13.3, -24.199, 0, 13.6, 28.926, 0, 13.867, -24.556, 0, 14.167, 27.298, 0, 14.467, -25.488, 0, 14.8, 20.144, 0, 15.167, -20.738, 0, 15.533, 18.981, 0, 15.933, -14.337, 0, 16.367, 14.038, 0, 16.767, -14.857, 0, 17.133, 12.46, 0, 17.6, -8.848, 0, 18.133, 10.523, 0, 18.6, -14.298, 0, 19, 15.096, 0, 19.4, -11.506, 0, 19.933, 15.062, 0, 20.3, -11.969, 0, 20.9, 8.39, 0, 21.367, -10.657, 0, 21.833, 9.869, 0, 22.333, -17.425, 0, 22.567, 21.103, 0, 22.8, -17.198, 0, 23.067, 19.376, 0, 23.433, -23.152, 0, 23.833, 17.17, 0, 24.233, -15.339, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param59", "Segments": [0, 0, 2, 0.367, 0, 0, 0.567, -11.352, 0, 0.867, 14.388, 0, 1.233, -11.695, 0, 1.667, 8.508, 0, 2.1, -8.212, 0, 2.533, 7.964, 0, 3, -9.54, 0, 3.467, 8.6, 0, 3.967, -10.153, 0, 4.567, 14.174, 0, 4.867, -20.176, 0, 5.233, 10.639, 0, 5.633, -7.638, 0, 6.1, 6.018, 0, 6.633, -6.959, 0, 7.167, 8.224, 0, 7.767, -7.488, 0, 8.367, 6.239, 0, 9.133, -4.521, 0, 9.867, 3.736, 0, 10.133, 3.367, 0, 10.3, 3.753, 0, 10.667, -15.811, 0, 11, 19.808, 0, 11.2, -9.918, 0, 11.367, 15.364, 0, 11.567, -12.095, 0, 11.767, 11.993, 0, 11.967, -12.484, 0, 12.167, 8.691, 0, 12.433, -7.578, 0, 12.733, 9.954, 0, 13.033, -13.572, 0, 13.333, 18.18, 0, 13.6, -12.743, 0, 13.9, 13.757, 0, 14.2, -14.264, 0, 14.533, 8.443, 0, 14.9, -10.608, 0, 15.267, 10.842, 0, 15.7, -9.601, 0, 16.1, 9.155, 0, 16.5, -8.503, 0, 16.9, 8.452, 0, 17.4, -8.947, 0, 17.867, 10.138, 0, 18.333, -11.19, 0, 18.733, 10.044, 0, 19.167, -10.214, 0, 19.667, 13.802, 0, 20.133, -10.228, 0, 20.633, 9.634, 0, 21.1, -10.954, 0, 21.6, 11.726, 0, 22.067, -12.196, 0, 22.367, 6.168, 0, 22.5, -11.71, 0, 22.767, 14.151, 0, 23.167, -14.124, 0, 23.567, 10.452, 0, 23.967, -8.308, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param60", "Segments": [0, 0, 2, 0.367, 0, 0, 0.5, 6.084, 0, 0.7, -16.418, 0, 1.033, 21.068, 0, 1.4, -15.883, 0, 1.833, 9.921, 0, 2.267, -9.032, 0, 2.767, 7.272, 0, 3.2, -9.854, 0, 3.667, 6.797, 0, 4.167, -9.953, 0, 4.467, 1.886, 0, 4.567, -0.604, 0, 4.7, 19.53, 0, 5, -30, 2, 5.067, -30, 0, 5.4, 17.402, 0, 5.8, -9.399, 0, 6.333, 4.841, 0, 6.9, -5.35, 0, 7.367, 5.905, 0, 7.967, -4.605, 0, 8.6, 3.588, 0, 9.4, -2.67, 0, 10.033, 1.446, 0, 10.267, 0.754, 0, 10.533, 8.398, 0, 10.867, -23.207, 0, 11.1, 29.822, 0, 11.133, 12, 0, 11.2, 17.904, 0, 11.367, -19.228, 0, 11.567, 0.462, 0, 11.7, -14.526, 0, 11.9, 17.684, 0, 12.1, -19.449, 0, 12.333, 13.071, 0, 12.633, -13.897, 0, 12.9, 16.828, 0, 13.2, -24.259, 0, 13.5, 30, 0, 13.767, -22.029, 0, 14.067, 25.438, 0, 14.367, -22.04, 0, 14.733, 14.026, 0, 15.067, -16.758, 0, 15.433, 14.579, 0, 15.9, -10.591, 0, 16.3, 11.298, 0, 16.7, -11.34, 0, 17.1, 8.827, 0, 17.6, -7.739, 0, 18.1, 9.613, 0, 18.533, -12.488, 0, 18.933, 11.992, 0, 19.4, -9.464, 0, 19.867, 14.603, 0, 20.3, -8.138, 0, 20.867, 8.287, 0, 21.333, -9.787, 0, 21.833, 9.207, 0, 22.267, -16.332, 0, 22.467, 16.186, 0, 22.667, -11.656, 0, 22.967, 18.991, 0, 23.333, -19.931, 0, 23.767, 12.766, 0, 24.133, -11.574, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param61", "Segments": [0, 0, 2, 0.367, 0, 0, 0.6, 7.912, 0, 0.8, -16.36, 0, 1.133, 23.683, 0, 1.5, -20.44, 0, 1.9, 14.647, 0, 2.333, -11.745, 0, 2.733, 9.185, 0, 3.267, -11.041, 0, 3.633, 9.394, 0, 4.233, -10.48, 0, 4.5, 4.857, 0, 4.667, 0.062, 0, 4.767, 15.964, 0, 5.1, -26.863, 1, 5.111, -26.863, 5.122, -12, 5.133, -12, 2, 5.167, -12, 0, 5.533, 18.804, 0, 5.867, -14.223, 0, 6.233, 8.32, 0, 6.933, -4.449, 0, 7.4, 6.625, 0, 7.967, -3.807, 0, 8.633, 3.129, 0, 9, -0.706, 0, 9.2, -0.047, 0, 9.5, -1.975, 0, 10.067, 1.041, 0, 10.333, -0.996, 0, 10.6, 9.118, 0, 10.933, -22.153, 0, 11.033, -13.179, 0, 11.133, -24.223, 0, 11.3, 24.148, 0, 11.567, -3.016, 0, 11.6, -1.157, 0, 11.8, -13.91, 0, 12.033, 25.541, 0, 12.233, -21.758, 0, 12.5, 17.186, 0, 12.767, -17.957, 0, 13.033, 19.255, 0, 13.3, -24.199, 0, 13.6, 28.926, 0, 13.867, -24.556, 0, 14.167, 27.298, 0, 14.467, -25.488, 0, 14.8, 20.144, 0, 15.167, -20.738, 0, 15.533, 18.981, 0, 15.933, -14.337, 0, 16.367, 14.038, 0, 16.767, -14.857, 0, 17.133, 12.46, 0, 17.6, -8.848, 0, 18.133, 10.523, 0, 18.6, -14.298, 0, 19, 15.096, 0, 19.4, -11.506, 0, 19.933, 15.062, 0, 20.3, -11.969, 0, 20.9, 8.39, 0, 21.367, -10.657, 0, 21.833, 9.869, 0, 22.333, -17.425, 0, 22.567, 21.103, 0, 22.8, -17.198, 0, 23.067, 19.376, 0, 23.433, -23.152, 0, 23.833, 17.17, 0, 24.233, -15.339, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param49", "Segments": [0, 0, 2, 0.367, 0, 0, 0.533, -24.974, 0, 0.8, 30, 0, 1.133, -28.714, 0, 1.5, 22.579, 0, 1.933, -17.416, 0, 2.367, 15.318, 0, 2.9, -14.385, 0, 3.3, 14.666, 0, 3.867, -13.556, 0, 4.233, 11.748, 0, 4.4, 8.843, 0, 4.567, 19.825, 0, 4.733, -30, 2, 4.867, -30, 0, 5.1, 30, 0, 5.467, -22.538, 0, 5.867, 14.297, 0, 6.433, -7.699, 0, 7.1, 9.934, 0, 7.7, -7.556, 0, 8.367, 6.372, 0, 9.167, -6.328, 0, 9.833, 4.574, 0, 9.933, 4.459, 0, 10.3, 8.34, 0, 10.6, -28.689, 0, 10.9, 30, 2, 11, 30, 0, 11.133, -30, 2, 11.2, -30, 0, 11.367, 24.435, 0, 11.5, -30, 2, 11.567, -30, 0, 11.733, 27.451, 0, 11.933, -30, 2, 11.967, -30, 0, 12.133, 26.616, 0, 12.4, -21.284, 0, 12.7, 26.667, 0, 12.967, -30, 2, 13.033, -30, 0, 13.233, 30, 2, 13.333, 30, 0, 13.533, -30, 2, 13.6, -30, 0, 13.8, 30, 2, 13.9, 30, 0, 14.1, -30, 2, 14.2, -30, 0, 14.433, 25.04, 0, 14.833, -26.42, 0, 15.167, 27.622, 0, 15.533, -22.273, 0, 16, 18.607, 0, 16.4, -20.324, 0, 16.767, 20.66, 0, 17.233, -15.003, 0, 17.767, 14.235, 0, 18.233, -17.994, 0, 18.633, 20.466, 0, 19.033, -20.378, 0, 19.567, 20.784, 0, 19.967, -19.485, 0, 20.467, 12.348, 0, 21, -15.094, 0, 21.5, 16.26, 0, 22, -18.197, 0, 22.3, 25.971, 0, 22.467, -30, 2, 22.5, -30, 0, 22.733, 27.105, 0, 23.067, -30, 0, 23.433, 26.332, 0, 23.833, -20.439, 0, 24.2, 20.994, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param50", "Segments": [0, 0, 2, 0.367, 0, 0, 0.467, 11.075, 0, 0.667, -29.285, 0, 0.933, 30, 2, 1, 30, 0, 1.3, -28.269, 0, 1.667, 20.452, 0, 2.067, -12.315, 0, 2.5, 9.954, 0, 3.1, -7.737, 0, 3.433, 9.935, 0, 3.733, -1.993, 0, 3.833, -0.978, 0, 4.1, -7.576, 0, 4.367, 7.989, 0, 4.567, -8.171, 0, 4.7, 20.965, 0, 4.9, -30, 2, 5.067, -30, 0, 5.3, 29.182, 0, 5.633, -21.218, 0, 6, 12.863, 0, 6.3, -4.058, 0, 6.7, -0.014, 0, 6.967, -2.847, 0, 7.267, 5.31, 0, 7.567, -1.181, 0, 7.633, -1.037, 0, 7.867, -1.807, 0, 8.133, -0.37, 0, 8.233, -0.661, 0, 8.5, 2.049, 0, 8.767, -0.332, 0, 9.033, 1.326, 0, 9.333, -2.301, 0, 9.633, 0.078, 0, 9.733, -0.302, 0, 9.933, 0.913, 0, 10.2, -1.449, 0, 10.5, 12.73, 0, 10.8, -29.139, 0, 11.033, 30, 2, 11.1, 30, 1, 11.111, 30, 11.122, -30, 11.133, -30, 0, 11.2, -12.384, 0, 11.267, -30, 2, 11.367, -30, 0, 11.5, 19.209, 0, 11.633, -30, 2, 11.733, -30, 0, 11.867, 24.92, 0, 12.033, -30, 2, 12.1, -30, 0, 12.3, 27.916, 0, 12.567, -27.75, 0, 12.833, 30, 2, 12.9, 30, 0, 13.1, -30, 2, 13.2, -30, 0, 13.367, 30, 2, 13.567, 30, 1, 13.611, 30, 13.656, -30, 13.7, -30, 2, 13.733, -30, 0, 13.967, 30, 2, 14.067, 30, 0, 14.267, -30, 2, 14.367, -30, 0, 14.6, 27.355, 0, 15, -29.008, 0, 15.333, 25.877, 0, 15.7, -18.469, 0, 16.167, 13.285, 0, 16.567, -17.051, 0, 16.9, 16.512, 0, 17.333, -8.724, 0, 17.967, 6.932, 0, 18.4, -12.798, 0, 18.767, 16.751, 0, 19.167, -13.908, 0, 19.433, 0.955, 0, 19.467, 0.878, 0, 19.767, 11.534, 0, 20.1, -15.036, 0, 20.467, 4.526, 0, 20.5, 4.49, 0, 20.533, 4.492, 0, 21.2, -7.639, 0, 21.633, 8.253, 0, 21.9, 0.762, 0, 21.933, 0.882, 0, 22.2, -17.622, 0, 22.4, 30, 2, 22.467, 30, 0, 22.633, -26.218, 0, 22.9, 30, 2, 22.933, 30, 0, 23.233, -30, 2, 23.267, -30, 0, 23.567, 23.524, 0, 24, -17.835, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param51", "Segments": [0, 0, 2, 0.367, 0, 0, 0.533, -24.974, 0, 0.8, 30, 0, 1.133, -28.714, 0, 1.5, 22.579, 0, 1.933, -17.416, 0, 2.367, 15.318, 0, 2.9, -14.385, 0, 3.3, 14.666, 0, 3.867, -13.556, 0, 4.233, 11.748, 0, 4.4, 8.843, 0, 4.567, 19.825, 0, 4.733, -30, 2, 4.867, -30, 0, 5.1, 30, 0, 5.467, -22.538, 0, 5.867, 14.297, 0, 6.433, -7.699, 0, 7.1, 9.934, 0, 7.7, -7.556, 0, 8.367, 6.372, 0, 9.167, -6.328, 0, 9.833, 4.574, 0, 9.933, 4.459, 0, 10.3, 8.34, 0, 10.6, -28.689, 0, 10.9, 30, 2, 11, 30, 0, 11.133, -30, 2, 11.2, -30, 0, 11.367, 24.435, 0, 11.5, -30, 2, 11.567, -30, 0, 11.733, 27.451, 0, 11.933, -30, 2, 11.967, -30, 0, 12.133, 26.616, 0, 12.4, -21.284, 0, 12.7, 26.667, 0, 12.967, -30, 2, 13.033, -30, 0, 13.233, 30, 2, 13.333, 30, 0, 13.533, -30, 2, 13.6, -30, 0, 13.8, 30, 2, 13.9, 30, 0, 14.1, -30, 2, 14.2, -30, 0, 14.433, 25.04, 0, 14.833, -26.42, 0, 15.167, 27.622, 0, 15.533, -22.273, 0, 16, 18.607, 0, 16.4, -20.324, 0, 16.767, 20.66, 0, 17.233, -15.003, 0, 17.767, 14.235, 0, 18.233, -17.994, 0, 18.633, 20.466, 0, 19.033, -20.378, 0, 19.567, 20.784, 0, 19.967, -19.485, 0, 20.467, 12.348, 0, 21, -15.094, 0, 21.5, 16.26, 0, 22, -18.197, 0, 22.3, 25.971, 0, 22.467, -30, 2, 22.5, -30, 0, 22.733, 27.105, 0, 23.067, -30, 0, 23.433, 26.332, 0, 23.833, -20.439, 0, 24.2, 20.994, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param52", "Segments": [0, 0, 2, 0.367, 0, 0, 0.467, 11.075, 0, 0.667, -29.285, 0, 0.933, 30, 2, 1, 30, 0, 1.3, -28.269, 0, 1.667, 20.452, 0, 2.067, -12.315, 0, 2.5, 9.954, 0, 3.1, -7.737, 0, 3.433, 9.935, 0, 3.733, -1.993, 0, 3.833, -0.978, 0, 4.1, -7.576, 0, 4.367, 7.989, 0, 4.567, -8.171, 0, 4.7, 20.965, 0, 4.9, -30, 2, 5.067, -30, 0, 5.3, 29.182, 0, 5.633, -21.218, 0, 6, 12.863, 0, 6.3, -4.058, 0, 6.7, -0.014, 0, 6.967, -2.847, 0, 7.267, 5.31, 0, 7.567, -1.181, 0, 7.633, -1.037, 0, 7.867, -1.807, 0, 8.133, -0.37, 0, 8.233, -0.661, 0, 8.5, 2.049, 0, 8.767, -0.332, 0, 9.033, 1.326, 0, 9.333, -2.301, 0, 9.633, 0.078, 0, 9.733, -0.302, 0, 9.933, 0.913, 0, 10.2, -1.449, 0, 10.5, 12.73, 0, 10.8, -29.139, 0, 11.033, 30, 2, 11.1, 30, 1, 11.111, 30, 11.122, -30, 11.133, -30, 0, 11.2, -12.384, 0, 11.267, -30, 2, 11.367, -30, 0, 11.5, 19.209, 0, 11.633, -30, 2, 11.733, -30, 0, 11.867, 24.92, 0, 12.033, -30, 2, 12.1, -30, 0, 12.3, 27.916, 0, 12.567, -27.75, 0, 12.833, 30, 2, 12.9, 30, 0, 13.1, -30, 2, 13.2, -30, 0, 13.367, 30, 2, 13.567, 30, 1, 13.611, 30, 13.656, -30, 13.7, -30, 2, 13.733, -30, 0, 13.967, 30, 2, 14.067, 30, 0, 14.267, -30, 2, 14.367, -30, 0, 14.6, 27.355, 0, 15, -29.008, 0, 15.333, 25.877, 0, 15.7, -18.469, 0, 16.167, 13.285, 0, 16.567, -17.051, 0, 16.9, 16.512, 0, 17.333, -8.724, 0, 17.967, 6.932, 0, 18.4, -12.798, 0, 18.767, 16.751, 0, 19.167, -13.908, 0, 19.433, 0.955, 0, 19.467, 0.878, 0, 19.767, 11.534, 0, 20.1, -15.036, 0, 20.467, 4.526, 0, 20.5, 4.49, 0, 20.533, 4.492, 0, 21.2, -7.639, 0, 21.633, 8.253, 0, 21.9, 0.762, 0, 21.933, 0.882, 0, 22.2, -17.622, 0, 22.4, 30, 2, 22.467, 30, 0, 22.633, -26.218, 0, 22.9, 30, 2, 22.933, 30, 0, 23.233, -30, 2, 23.267, -30, 0, 23.567, 23.524, 0, 24, -17.835, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation64", "Segments": [0, 2.951, 1, 0.122, 2.951, 0.245, 2.561, 0.367, 0, 1, 0.422, -1.164, 0.478, -8.325, 0.533, -8.325, 0, 0.8, 10.392, 0, 1.133, -9.571, 0, 1.5, 7.526, 0, 1.933, -5.805, 0, 2.367, 5.106, 0, 2.9, -4.795, 0, 3.3, 4.889, 0, 3.867, -4.519, 0, 4.233, 3.916, 0, 4.4, 2.947, 0, 4.567, 6.608, 0, 4.8, -13.65, 0, 5.1, 10.299, 0, 5.467, -7.513, 0, 5.867, 4.766, 0, 6.433, -2.567, 0, 7.1, 3.311, 0, 7.7, -2.518, 0, 8.367, 2.124, 0, 9.167, -2.109, 0, 9.833, 1.525, 0, 9.933, 1.486, 0, 10.3, 2.78, 0, 10.6, -9.563, 0, 10.967, 13.833, 0, 11.2, -13.917, 0, 11.367, 8.145, 0, 11.533, -12.491, 0, 11.733, 9.15, 0, 11.933, -10.824, 0, 12.133, 8.872, 0, 12.4, -7.095, 0, 12.7, 8.889, 0, 13, -10.621, 0, 13.3, 13.696, 0, 13.567, -11.84, 0, 13.833, 11.904, 0, 14.133, -12.326, 0, 14.433, 8.347, 0, 14.833, -8.807, 0, 15.167, 9.207, 0, 15.533, -7.425, 0, 16, 6.202, 0, 16.4, -6.775, 0, 16.767, 6.886, 0, 17.233, -5.001, 0, 17.767, 4.745, 0, 18.233, -5.998, 0, 18.633, 6.822, 0, 19.033, -6.793, 0, 19.567, 6.928, 0, 19.967, -6.495, 0, 20.467, 4.116, 0, 21, -5.032, 0, 21.5, 5.42, 0, 22, -6.066, 0, 22.3, 8.657, 0, 22.5, -10.593, 0, 22.733, 9.035, 0, 23.067, -10.182, 0, 23.433, 8.777, 0, 23.833, -6.813, 0, 24.2, 6.998, 0, 24.267, 2.951]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation65", "Segments": [0, 4.849, 0, 0.367, 0, 0, 0.467, 5.537, 0, 0.667, -14.643, 0, 0.967, 18.054, 0, 1.3, -14.134, 0, 1.667, 10.226, 0, 2.067, -6.158, 0, 2.5, 4.977, 0, 3.1, -3.868, 0, 3.433, 4.967, 0, 3.733, -0.997, 0, 3.833, -0.489, 0, 4.1, -3.788, 0, 4.367, 3.995, 0, 4.567, -4.085, 0, 4.7, 10.483, 0, 4.933, -30, 2, 5, -30, 0, 5.3, 14.591, 0, 5.633, -10.609, 0, 6, 6.431, 0, 6.3, -2.029, 0, 6.7, -0.007, 0, 6.967, -1.424, 0, 7.267, 2.655, 0, 7.567, -0.591, 0, 7.633, -0.519, 0, 7.867, -0.904, 0, 8.133, -0.185, 0, 8.233, -0.331, 0, 8.5, 1.024, 0, 8.767, -0.166, 0, 9.033, 0.663, 0, 9.333, -1.151, 0, 9.633, 0.039, 0, 9.733, -0.151, 0, 9.933, 0.457, 0, 10.2, -0.724, 0, 10.5, 6.365, 0, 10.8, -14.569, 0, 11.067, 30, 2, 11.1, 30, 1, 11.111, 30, 11.122, -24.801, 11.133, -24.801, 0, 11.2, -6.192, 0, 11.3, -28.715, 0, 11.5, 9.605, 0, 11.667, -26.121, 0, 11.867, 12.46, 0, 12.067, -20.357, 0, 12.3, 13.958, 0, 12.567, -13.875, 0, 12.867, 18.038, 0, 13.133, -22.589, 0, 13.433, 30, 2, 13.467, 30, 0, 13.733, -16.611, 0, 14, 24.608, 0, 14.3, -23.164, 0, 14.6, 13.678, 0, 15, -14.504, 0, 15.333, 12.938, 0, 15.7, -9.235, 0, 16.167, 6.643, 0, 16.567, -8.526, 0, 16.9, 8.256, 0, 17.333, -4.362, 0, 17.967, 3.466, 0, 18.4, -6.399, 0, 18.767, 8.375, 0, 19.167, -6.954, 0, 19.433, 0.477, 0, 19.467, 0.439, 0, 19.767, 5.767, 0, 20.1, -7.518, 0, 20.467, 2.263, 0, 20.5, 2.245, 0, 20.533, 2.246, 0, 21.2, -3.819, 0, 21.633, 4.127, 0, 21.9, 0.381, 0, 21.933, 0.441, 0, 22.2, -8.811, 0, 22.433, 20.488, 0, 22.633, -13.109, 0, 22.9, 15.934, 0, 23.233, -16.035, 0, 23.567, 11.762, 0, 24, -8.918, 0, 24.267, 4.849]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation66", "Segments": [0, -11.659, 1, 0.122, -11.659, 0.245, -7.6, 0.367, 0, 1, 0.445, 4.836, 0.522, 7.011, 0.6, 7.011, 0, 0.767, -15.636, 0, 1.033, 26.292, 0, 1.4, -23.773, 0, 1.767, 18.892, 0, 2.133, -12.934, 0, 2.567, 9.522, 0, 3.167, -5.047, 0, 3.533, 8.829, 0, 3.867, -3.577, 0, 4, -2.836, 0, 4.167, -4.449, 0, 4.467, 6.585, 0, 4.633, -4.2, 0, 4.767, 8.96, 0, 4.867, 6.159, 0, 4.9, 6.55, 0, 5.033, -30, 2, 5.1, -30, 1, 5.111, -30, 5.122, 30, 5.133, 30, 2, 5.167, 30, 0, 5.333, 6.536, 0, 5.4, 7.982, 0, 5.733, -17.465, 0, 6.1, 13.154, 0, 6.433, -6.274, 0, 6.767, 1.723, 0, 7.067, -2.607, 0, 7.367, 4.87, 0, 7.7, -2.476, 0, 8.2, -0.186, 0, 8.3, -0.254, 0, 8.6, 1.647, 0, 8.9, -0.671, 0, 9.167, 1.048, 0, 9.467, -2.123, 0, 9.767, 0.634, 0, 9.933, 0.43, 0, 9.967, 0.435, 0, 10.3, -1.179, 0, 10.567, 8.273, 0, 10.867, -21.253, 0, 11, -7.45, 0, 11.033, -8.433, 0, 11.067, -6.923, 1, 11.089, -6.923, 11.111, -30, 11.133, -30, 2, 11.267, -30, 1, 11.278, -30, 11.289, 30, 11.3, 30, 2, 11.333, 30, 0, 11.467, -1.302, 0, 11.633, 13.815, 0, 11.8, -17.779, 0, 12, 25.661, 0, 12.2, -16.221, 0, 12.4, 17.938, 0, 12.667, -20.175, 0, 12.933, 23.955, 0, 13.2, -30, 0, 13.533, 30, 2, 13.6, 30, 0, 13.8, -18.263, 0, 14.067, 30, 2, 14.1, 30, 0, 14.4, -25.467, 0, 14.7, 22.435, 0, 15.067, -23.738, 0, 15.4, 22.395, 0, 15.767, -17.626, 0, 16.2, 12.692, 0, 16.633, -14.835, 0, 17, 15.278, 0, 17.4, -9.568, 0, 18.033, 4.565, 0, 18.467, -10.182, 0, 18.867, 14.805, 0, 19.233, -13.423, 0, 19.833, 7.434, 0, 20.2, -12.924, 0, 20.567, 6.707, 0, 21.267, -5.865, 0, 21.7, 7.415, 0, 22.267, -11.207, 0, 22.533, 27.136, 0, 22.733, -18.138, 0, 23, 21.722, 0, 23.333, -25.656, 0, 23.667, 21.129, 0, 24.067, -16.958, 0, 24.267, -11.659]}, {"Target": "Parameter", "Id": "Param55", "Segments": [0, -10.57, 1, 0.133, -10.57, 0.267, -8.175, 0.4, 0, 1, 0.478, 4.769, 0.555, 10.164, 0.633, 10.164, 0, 0.833, -22.585, 0, 1.067, 30, 2, 1.167, 30, 0, 1.433, -30, 2, 1.5, -30, 0, 1.8, 30, 2, 1.867, 30, 0, 2.233, -26.573, 0, 2.633, 19.922, 0, 3.067, -10.632, 0, 3.633, 15.814, 0, 3.967, -10.137, 0, 4.533, 10.598, 0, 4.7, 2.451, 0, 4.8, 9.839, 0, 4.933, 7.008, 0, 5.133, 30, 2, 5.2, 30, 1, 5.211, 30, 5.222, -30, 5.233, -30, 2, 5.267, -30, 0, 5.433, 25.502, 0, 5.8, -30, 0, 6.167, 26.254, 0, 6.533, -15.966, 0, 6.867, 7.262, 0, 7.167, -6.677, 0, 7.467, 10.354, 0, 7.8, -7.427, 0, 8.133, 1.399, 0, 8.4, -0.809, 0, 8.7, 3.115, 0, 9, -2.005, 0, 9.267, 2.121, 0, 9.567, -4.366, 0, 9.9, 2.514, 0, 10.367, -2.058, 0, 10.633, 12.892, 0, 10.9, -30, 0, 11.167, 30, 2, 11.5, 30, 0, 11.833, -27.476, 0, 12.067, 30, 2, 12.1, 30, 0, 12.3, -22.402, 0, 12.533, 25.979, 0, 12.733, -30, 2, 12.767, -30, 0, 13, 30, 2, 13.033, 30, 0, 13.267, -30, 2, 13.367, -30, 0, 13.433, -23.864, 0, 13.467, -30, 2, 13.5, -30, 0, 13.7, 21.584, 0, 13.867, -30, 2, 13.9, -30, 0, 14.133, 30, 2, 14.233, 30, 0, 14.467, -30, 2, 14.533, -30, 0, 14.767, 30, 2, 14.833, 30, 0, 15.1, -30, 2, 15.167, -30, 0, 15.433, 30, 2, 15.533, 30, 0, 15.833, -30, 2, 15.867, -30, 0, 16.267, 25.642, 0, 16.667, -26.466, 0, 17.067, 28.366, 0, 17.467, -20.849, 0, 17.867, 8.876, 0, 18.533, -17.119, 0, 18.933, 26.292, 0, 19.333, -25.953, 0, 19.733, 12.116, 0, 20.267, -22.706, 0, 20.633, 16.131, 0, 21, -3.246, 0, 21.133, -2.38, 0, 21.367, -8.96, 0, 21.767, 14.397, 0, 22.3, -16.236, 0, 22.567, 30, 2, 22.633, 30, 0, 22.833, -30, 2, 22.9, -30, 0, 23.067, 30, 0, 23.367, -30, 2, 23.467, -30, 0, 23.733, 30, 2, 23.8, 30, 0, 24.133, -30, 0, 24.267, -10.57]}, {"Target": "Parameter", "Id": "Param28", "Segments": [0, -7.12, 2, 0.333, -7.12, 0, 1.533, 7.36, 0, 2.733, -7.12, 0, 3.933, 7.36, 0, 5.133, -7.12, 0, 6.333, 7.36, 0, 7.533, -7.12, 0, 8.733, 7.36, 0, 9.933, -7.12, 0, 11.133, 7.36, 0, 12.367, -7.12, 0, 13.567, 7.36, 0, 14.767, -7.12, 0, 15.967, 7.36, 0, 17.167, -7.12, 0, 18.367, 7.36, 0, 19.567, -7.12, 0, 20.767, 7.36, 0, 21.967, -7.12, 0, 23.167, 7.36, 0, 24.267, -7.12]}, {"Target": "Parameter", "Id": "Param29", "Segments": [0, -1.278, 2, 0.333, -1.278, 0, 0.8, -4.74, 0, 2, 5.1, 0, 3.2, -4.74, 0, 4.4, 5.1, 1, 4.644, 5.1, 4.889, 2.706, 5.133, -1.278, 1, 5.289, -3.812, 5.444, -4.74, 5.6, -4.74, 0, 6.8, 5.1, 0, 8, -4.74, 0, 9.2, 5.1, 1, 9.444, 5.1, 9.689, 2.571, 9.933, -1.278, 1, 10.1, -3.902, 10.266, -4.74, 10.433, -4.74, 0, 11.633, 5.1, 0, 12.833, -4.74, 0, 14.033, 5.1, 1, 14.278, 5.1, 14.522, 2.706, 14.767, -1.278, 1, 14.922, -3.812, 15.078, -4.74, 15.233, -4.74, 0, 16.433, 5.1, 0, 17.633, -4.74, 0, 18.833, 5.1, 1, 19.078, 5.1, 19.322, 2.706, 19.567, -1.278, 1, 19.722, -3.812, 19.878, -4.74, 20.033, -4.74, 0, 21.233, 5.1, 0, 22.467, -4.74, 0, 23.633, 5.1, 0, 24.267, -1.278]}, {"Target": "Parameter", "Id": "Param30", "Segments": [0, -5.909, 2, 0.333, -5.909, 0, 1.367, 2.533, 0, 2.567, -6.287, 0, 3.767, 2.533, 0, 4.967, -6.287, 1, 5.022, -6.287, 5.078, -6.569, 5.133, -5.909, 1, 5.478, -1.813, 5.822, 2.533, 6.167, 2.533, 0, 7.367, -6.287, 0, 8.567, 2.533, 0, 9.8, -6.287, 1, 9.844, -6.287, 9.889, -6.492, 9.933, -5.909, 1, 10.289, -1.241, 10.644, 2.533, 11, 2.533, 0, 12.2, -6.287, 0, 13.4, 2.533, 0, 14.6, -6.287, 1, 14.656, -6.287, 14.711, -6.569, 14.767, -5.909, 1, 15.111, -1.813, 15.456, 2.533, 15.8, 2.533, 0, 17, -6.287, 0, 18.2, 2.533, 0, 19.4, -6.287, 1, 19.456, -6.287, 19.511, -6.569, 19.567, -5.909, 1, 19.911, -1.813, 20.256, 2.533, 20.6, 2.533, 0, 21.833, -6.287, 0, 23.033, 2.533, 0, 24.1, -6.287, 0, 24.267, -5.909]}, {"Target": "Parameter", "Id": "Param31", "Segments": [0, -0.551, 2, 0.333, -0.551, 0, 0.833, 3.018, 0, 2.033, -6.18, 0, 3.233, 3.018, 0, 4.433, -6.18, 1, 4.666, -6.18, 4.9, -4.115, 5.133, -0.551, 1, 5.3, 1.995, 5.466, 3.018, 5.633, 3.018, 0, 6.833, -6.18, 0, 8.033, 3.018, 0, 9.233, -6.18, 1, 9.466, -6.18, 9.7, -4.001, 9.933, -0.551, 1, 10.111, 2.078, 10.289, 3.018, 10.467, 3.018, 0, 11.667, -6.18, 0, 12.867, 3.018, 0, 14.067, -6.18, 1, 14.3, -6.18, 14.534, -4.115, 14.767, -0.551, 1, 14.934, 1.995, 15.1, 3.018, 15.267, 3.018, 0, 16.467, -6.18, 0, 17.667, 3.018, 0, 18.867, -6.18, 1, 19.1, -6.18, 19.334, -4.115, 19.567, -0.551, 1, 19.734, 1.995, 19.9, 3.018, 20.067, 3.018, 0, 21.267, -6.18, 0, 22.5, 3.018, 0, 23.667, -6.18, 0, 24.267, -0.551]}, {"Target": "Parameter", "Id": "Param32", "Segments": [0, -2.714, 2, 0.333, -2.714, 0, 1.433, 2.284, 0, 2.633, -2.796, 0, 3.833, 2.284, 0, 5.033, -2.796, 1, 5.066, -2.796, 5.1, -2.933, 5.133, -2.714, 1, 5.5, -0.309, 5.866, 2.284, 6.233, 2.284, 0, 7.433, -2.796, 0, 8.633, 2.284, 0, 9.833, -2.796, 1, 9.866, -2.796, 9.9, -2.93, 9.933, -2.714, 1, 10.311, -0.266, 10.689, 2.284, 11.067, 2.284, 0, 12.267, -2.796, 0, 13.467, 2.284, 0, 14.667, -2.796, 1, 14.7, -2.796, 14.734, -2.933, 14.767, -2.714, 1, 15.134, -0.309, 15.5, 2.284, 15.867, 2.284, 0, 17.067, -2.796, 0, 18.267, 2.284, 0, 19.467, -2.796, 1, 19.5, -2.796, 19.534, -2.933, 19.567, -2.714, 1, 19.934, -0.309, 20.3, 2.284, 20.667, 2.284, 0, 21.867, -2.796, 0, 23.1, 2.284, 0, 24.167, -2.796, 0, 24.267, -2.714]}, {"Target": "Parameter", "Id": "Param33", "Segments": [0, -2.392, 2, 0.333, -2.392, 0, 0.867, 3.279, 0, 2.067, -10.047, 0, 3.267, 3.279, 0, 4.467, -10.047, 1, 4.689, -10.047, 4.911, -7.319, 5.133, -2.392, 1, 5.311, 1.55, 5.489, 3.279, 5.667, 3.279, 0, 6.867, -10.047, 0, 8.067, 3.279, 0, 9.267, -10.047, 1, 9.489, -10.047, 9.711, -7.319, 9.933, -2.392, 1, 10.111, 1.55, 10.289, 3.279, 10.467, 3.279, 0, 11.7, -10.047, 0, 12.9, 3.279, 0, 14.1, -10.047, 1, 14.322, -10.047, 14.545, -7.319, 14.767, -2.392, 1, 14.945, 1.55, 15.122, 3.279, 15.3, 3.279, 0, 16.5, -10.047, 0, 17.7, 3.279, 0, 18.9, -10.047, 1, 19.122, -10.047, 19.345, -7.319, 19.567, -2.392, 1, 19.745, 1.55, 19.922, 3.279, 20.1, 3.279, 0, 21.3, -10.047, 0, 22.5, 3.279, 0, 23.7, -10.047, 0, 24.267, -2.392]}, {"Target": "Parameter", "Id": "Param34", "Segments": [0, -21.182, 2, 0.333, -21.182, 0, 1.467, 11.7, 0, 2.667, -21.42, 0, 3.867, 11.7, 0, 5.067, -21.42, 1, 5.089, -21.42, 5.111, -22.036, 5.133, -21.182, 1, 5.511, -6.656, 5.889, 11.7, 6.267, 11.7, 0, 7.467, -21.42, 0, 8.667, 11.7, 0, 9.867, -21.42, 1, 9.889, -21.42, 9.911, -22.03, 9.933, -21.182, 1, 10.322, -6.342, 10.711, 11.7, 11.1, 11.7, 0, 12.3, -21.42, 0, 13.5, 11.7, 0, 14.7, -21.42, 1, 14.722, -21.42, 14.745, -22.036, 14.767, -21.182, 1, 15.145, -6.656, 15.522, 11.7, 15.9, 11.7, 0, 17.1, -21.42, 0, 18.3, 11.7, 0, 19.5, -21.42, 1, 19.522, -21.42, 19.545, -22.036, 19.567, -21.182, 1, 19.945, -6.656, 20.322, 11.7, 20.7, 11.7, 0, 21.9, -21.42, 0, 23.133, 11.7, 0, 24.2, -21.42, 0, 24.267, -21.182]}, {"Target": "Parameter", "Id": "Param281", "Segments": [0, 0, 2, 0.333, 0, 0, 1.667, 1, 0, 3, 0, 0, 4.333, 1, 0, 5.667, 0, 0, 7, 1, 0, 8.333, 0, 0, 9.667, 1, 0, 11, 0, 0, 12.333, 1, 0, 13.667, 0, 0, 15, 1, 0, 16.333, 0, 0, 17.667, 1, 0, 19, 0, 0, 20.333, 1, 0, 21.667, 0, 0, 23, 1, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param197mao2", "Segments": [0, 0, 2, 0.333, 0, 0, 0.967, 2.894, 0, 2.4, -2.454, 1, 2.6, -2.454, 2.8, -1.729, 3, 0, 1, 3.211, 1.825, 3.422, 2.894, 3.633, 2.894, 0, 5.067, -2.454, 1, 5.267, -2.454, 5.467, -1.729, 5.667, 0, 1, 5.878, 1.825, 6.089, 2.894, 6.3, 2.894, 0, 7.733, -2.454, 1, 7.933, -2.454, 8.133, -1.729, 8.333, 0, 1, 8.544, 1.825, 8.756, 2.894, 8.967, 2.894, 0, 10.4, -2.454, 1, 10.6, -2.454, 10.8, -1.729, 11, 0, 1, 11.211, 1.825, 11.422, 2.894, 11.633, 2.894, 0, 13.067, -2.454, 1, 13.267, -2.454, 13.467, -1.729, 13.667, 0, 1, 13.878, 1.825, 14.089, 2.894, 14.3, 2.894, 0, 15.733, -2.454, 1, 15.933, -2.454, 16.133, -1.729, 16.333, 0, 1, 16.544, 1.825, 16.756, 2.894, 16.967, 2.894, 0, 18.4, -2.454, 1, 18.6, -2.454, 18.8, -1.729, 19, 0, 1, 19.211, 1.825, 19.422, 2.894, 19.633, 2.894, 0, 21.067, -2.454, 1, 21.267, -2.454, 21.467, -1.729, 21.667, 0, 1, 21.878, 1.825, 22.089, 2.894, 22.3, 2.894, 0, 23.7, -2.454, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "ParamCheek", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param268", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param269", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param193", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param192", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param282", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param183", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param8", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param23", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param24", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param25", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param26", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param27", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param200", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param286", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param287", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param288", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param197", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param198", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param208", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param279", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param212", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param270", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param271", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param275", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param225", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param227", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param285", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param276", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param143", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param147", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param145", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param141", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param247", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param115", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param116", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param260", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param261", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param133", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param139", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param257", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param137", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param138", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param135", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param176", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param136", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param177", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param251", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation63", "Segments": [0, -0.675, 0, 24.267, -0.675]}, {"Target": "Parameter", "Id": "Param84", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param85", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param83", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param86", "Segments": [0, 1, 0, 24.267, 1]}, {"Target": "Parameter", "Id": "Param91", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param2", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param87", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param88", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param94", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param95", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param92", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param93", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param96", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param97", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param89", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param90", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation56", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param98", "Segments": [0, -6, 0, 24.267, -6]}, {"Target": "Parameter", "Id": "Param99", "Segments": [0, -10.921, 0, 24.267, -10.921]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation67", "Segments": [0, -10.939, 0, 24.267, -10.939]}, {"Target": "Parameter", "Id": "Param218mao2", "Segments": [0, 1, 0, 24.267, 1]}, {"Target": "Parameter", "Id": "Param211mao2", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param216mao2", "Segments": [0, -8.88, 0, 24.267, -8.88]}, {"Target": "Parameter", "Id": "Param217mao2", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param202mao2", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param203mao2", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param204mao2", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param199mao2", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param200mao2", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param201mao2", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param208mao2", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param209mao2", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param210mao2", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param205mao2", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param206mao2", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param207mao2", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param212mao2", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param213mao2", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param214mao2", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param215mao2", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param198mao2", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param219mao2", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation143mao2", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation144mao2", "Segments": [0, -10.82, 0, 24.267, -10.82]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation145mao2", "Segments": [0, 8.46, 0, 24.267, 8.46]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation146mao2", "Segments": [0, 28.83, 0, 24.267, 28.83]}, {"Target": "Parameter", "Id": "Param264", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param100", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param105", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param108", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param109", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param107", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param284", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param101", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param106", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param104", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param103", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param102", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation71", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation72", "Segments": [0, 8.085, 0, 24.267, 8.085]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation73", "Segments": [0, 11.915, 0, 24.267, 11.915]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation74", "Segments": [0, 12.341, 0, 24.267, 12.341]}, {"Target": "Parameter", "Id": "Param148", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param146", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param110", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param111", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param248", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param112", "Segments": [0, -30, 0, 24.267, -30]}, {"Target": "Parameter", "Id": "Param249", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param206", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param114", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param113", "Segments": [0, 1, 0, 24.267, 1]}, {"Target": "Parameter", "Id": "Param253", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param254", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param158", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param149", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param150", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param151", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param255", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param256", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param153", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param152", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param155", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param154", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param181", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param180", "Segments": [0, 0.5, 0, 24.267, 0.5]}, {"Target": "Parameter", "Id": "Param156", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param157", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param159", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param160", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param119", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param265", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param118", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param120", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param121", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param161", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param259", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param123", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param124", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param130", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param131", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param178", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param179", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param125", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param126", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param127", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param128", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param171", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param173", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param172", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param122", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param170", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param129", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param132", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param250", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param258", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param162", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param169", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param174", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param175", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param163", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param164", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param165", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param166", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param167", "Segments": [0, 0, 0, 24.267, 0]}, {"Target": "Parameter", "Id": "Param168", "Segments": [0, 0, 0, 24.267, 0]}], "UserData": [{"Time": 0.0, "Value": ""}, {"Time": 0.333, "Value": ""}, {"Time": 23.767, "Value": ""}]}