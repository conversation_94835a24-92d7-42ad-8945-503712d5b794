{"Version": 3, "Meta": {"Duration": 14.0, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 355, "TotalSegmentCount": 4278, "TotalPointCount": 4842, "UserDataCount": 3, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 2, 1.733, 0, 2, 2.967, 0, 2, 3.267, 0, 1, 3.489, 0, 3.711, -6.883, 3.933, -17, 1, 4.144, -26.612, 4.356, -30, 4.567, -30, 0, 5.3, 30, 2, 5.833, 30, 2, 7, 30, 0, 7.3, 8, 0, 7.567, 26, 0, 7.933, 11.04, 1, 8.1, 11.04, 8.266, 11.14, 8.433, 12.547, 1, 8.544, 13.485, 8.656, 16.32, 8.767, 16.32, 0, 9, 4.182, 0, 9.2, 23.646, 0, 9.4, 5.791, 0, 9.633, 30, 0, 10, -5.791, 0, 10.167, -3.056, 2, 10.633, -3.056, 0, 11.133, 24.58, 0, 11.667, 20, 2, 12.133, 20, 2, 12.767, 20, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 2, 1.733, 0, 0, 1.767, -16, 0, 2.167, 25, 0, 2.633, 11.871, 0, 2.967, 17, 0, 3.1, 8, 0, 3.267, 25, 0, 3.933, 2, 2, 4.567, 2, 1, 4.745, 2, 4.922, -4.285, 5.1, -11, 1, 5.344, -20.233, 5.589, -23, 5.833, -23, 2, 7, -23, 0, 7.233, -11, 0, 7.433, -28, 1, 7.6, -28, 7.766, -26.565, 7.933, -22.74, 1, 8.011, -20.955, 8.089, -18.777, 8.167, -16.038, 1, 8.367, -8.995, 8.567, -4.405, 8.767, -4.405, 1, 8.889, -4.405, 9.011, -18.816, 9.133, -22.9, 1, 9.244, -26.612, 9.356, -26.381, 9.467, -26.381, 0, 9.767, -6.434, 0, 9.967, -30, 0, 10.367, -20.58, 2, 10.633, -20.58, 0, 10.933, -30, 0, 11.167, -2, 0, 11.667, -12, 2, 12.133, -12, 2, 12.767, -12, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 2, 1.733, 0, 2, 2.967, 0, 2, 3.267, 0, 0, 3.733, 6, 1, 3.833, 6, 3.933, 5.28, 4.033, 3.84, 1, 4.211, 1.28, 4.389, 0, 4.567, 0, 1, 4.745, 0, 4.922, 4.013, 5.1, 8, 1, 5.344, 13.483, 5.589, 15, 5.833, 15, 2, 7, 15, 0, 7.333, 12.04, 2, 7.933, 12.04, 2, 8.167, 12.04, 0, 8.767, 12, 0, 9.1, 13.48, 0, 9.667, 12.04, 0, 10, 12.36, 0, 10.567, 11.88, 2, 10.633, 11.88, 0, 10.933, 18.682, 0, 11.2, 17.175, 0, 11.667, 17.4, 2, 12.133, 17.4, 2, 12.767, 17.4, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param272", "Segments": [0, 0, 2, 1.733, 0, 2, 2.967, 0, 2, 3.267, 0, 2, 3.933, 0, 2, 4.567, 0, 2, 7, 0, 0, 7.733, -8.036, 2, 8.167, -8.036, 1, 8.334, -8.036, 8.5, -5.152, 8.667, -4, 1, 9, -1.696, 9.334, -1.298, 9.667, -1.298, 2, 10.633, -1.298, 0, 11.133, 0, 0, 11.667, -8, 2, 12.133, -8, 2, 12.767, -8, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 2, 1.733, 1, 0, 1.833, 0, 2, 2, 0, 0, 2.267, 1, 0, 2.533, 0.947, 0, 2.967, 1, 2, 3.267, 1, 0, 3.633, 0, 0, 3.933, 0.9, 2, 4.567, 0.9, 0, 4.767, 0, 0, 5, 0.9, 2, 7, 0.9, 0, 7.3, 0, 2, 7.733, 0, 2, 8.167, 0, 0, 8.467, 0.9, 2, 8.8, 0.9, 0, 8.9, 1, 0, 9, 0, 2, 9.633, 0, 2, 9.933, 0, 2, 10.633, 0, 2, 11.133, 0, 0, 11.333, 1, 2, 11.667, 1, 0, 12.133, 0.8, 0, 12.767, 0.9, 0, 12.9, 0, 0, 14, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 2, 1.733, 0, 2, 2.967, 0, 2, 3.267, 0, 2, 3.933, 0, 2, 4.567, 0, 2, 7, 0, 0, 7.3, 1, 2, 7.733, 1, 2, 8.167, 1, 2, 8.5, 1, 2, 8.8, 1, 2, 10.633, 1, 2, 11.333, 1, 2, 11.667, 1, 2, 12.133, 1, 2, 12.767, 1, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 2, 1.733, 1, 0, 1.833, 0, 2, 2, 0, 0, 2.267, 1, 0, 2.533, 0.929, 0, 2.967, 1, 2, 3.267, 1, 0, 3.633, 0, 0, 3.933, 0.9, 2, 4.567, 0.9, 0, 4.767, 0, 0, 5, 0.9, 2, 7, 0.9, 0, 7.3, 0, 2, 7.733, 0, 2, 8.167, 0, 0, 8.467, 0.9, 2, 8.8, 0.9, 0, 8.9, 1, 0, 9, 0, 2, 9.633, 0, 2, 9.933, 0, 2, 10.633, 0, 2, 11.133, 0, 0, 11.333, 1, 2, 11.667, 1, 0, 12.133, 0.8, 0, 12.767, 0.9, 0, 12.9, 0, 0, 14, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 2, 1.733, 0, 2, 2.967, 0, 2, 3.267, 0, 2, 3.933, 0, 2, 4.567, 0, 2, 7, 0, 0, 7.3, 1, 2, 7.733, 1, 2, 8.167, 1, 2, 8.8, 1, 2, 9.633, 1, 2, 10.633, 1, 2, 11.667, 1, 2, 12.133, 1, 2, 12.767, 1, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param76", "Segments": [0, 0, 2, 1.733, 0, 2, 2.967, 0, 2, 3.267, 0, 0, 3.933, -30, 2, 4.567, -30, 2, 7, -30, 2, 8.167, -30, 0, 8.5, 30, 0, 8.667, 18, 0, 8.867, 30, 0, 9.067, 18, 0, 9.267, 30, 0, 9.5, 16, 1, 9.544, 16, 9.589, 20.064, 9.633, 25.652, 1, 9.655, 28.446, 9.678, 29, 9.7, 29, 2, 10.633, 29, 0, 11.133, 0, 2, 11.667, 0, 2, 12.133, 0, 2, 12.767, 0, 2, 14, 0]}, {"Target": "Parameter", "Id": "Param273", "Segments": [0, 0, 2, 1.733, 0, 2, 2.967, 0, 2, 3.267, 0, 2, 3.933, 0, 2, 4.567, 0, 2, 7, 0, 2, 8.167, 0, 0, 8.5, 0.6, 2, 9.633, 0.6, 2, 10.633, 0.6, 2, 11.133, 0.6, 0, 11.667, 0, 2, 12.133, 0, 2, 12.767, 0, 2, 14, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 2, 1.733, 0, 0, 2.4, 0.1, 2, 2.967, 0.1, 2, 3.267, 0.1, 0, 3.933, -0.8, 2, 4.567, -0.8, 1, 4.745, -0.8, 4.922, 0.081, 5.1, 0.3, 1, 5.344, 0.602, 5.589, 0.6, 5.833, 0.6, 2, 7, 0.6, 0, 7.733, 0.1, 2, 8.167, 0.1, 0, 8.5, -0.1, 2, 9.633, -0.1, 2, 10.633, -0.1, 1, 10.8, -0.1, 10.966, -0.088, 11.133, 0, 1, 11.311, 0.094, 11.489, 0.2, 11.667, 0.2, 2, 12.133, 0.2, 2, 12.767, 0.2, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 2, 1.733, 0, 0, 2.4, 0.8, 2, 2.967, 0.8, 2, 3.267, 0.8, 0, 3.933, -0.2, 2, 4.567, -0.2, 0, 5.1, -0.8, 0, 5.833, -0.7, 0, 6.333, -1, 0, 6.9, -0.505, 0, 7, -0.7, 0, 7.733, -0.5, 2, 8.167, -0.5, 0, 8.5, 0, 2, 9.633, 0, 2, 10.633, 0, 1, 10.8, 0, 10.966, 0.018, 11.133, -0.1, 1, 11.311, -0.226, 11.489, -0.8, 11.667, -0.8, 2, 12.133, -0.8, 2, 12.767, -0.8, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 2, 1.733, 0, 0, 2.267, -0.182, 2, 2.967, -0.182, 2, 3.267, -0.182, 2, 3.933, -0.182, 2, 4.567, -0.182, 2, 7, -0.182, 2, 8.167, -0.182, 2, 9.633, -0.182, 0, 9.933, 0, 2, 10.633, 0, 2, 11.667, 0, 2, 12.133, 0, 2, 12.767, 0, 2, 14, 0]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 2, 1.733, 0, 2, 3.267, 0, 2, 3.933, 0, 2, 4.567, 0, 2, 7, 0, 2, 8.167, 0, 2, 9.633, 0, 0, 9.933, -0.5, 2, 10.633, -0.5, 0, 11.033, 0, 1, 11.244, 0, 11.456, -0.27, 11.667, -0.364, 1, 11.822, -0.434, 11.978, -0.462, 12.133, -0.478, 1, 12.344, -0.499, 12.556, -0.5, 12.767, -0.5, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 2, 1.733, 0, 0, 2.267, -0.252, 2, 2.967, -0.252, 2, 3.267, -0.252, 2, 3.933, -0.252, 2, 4.567, -0.252, 2, 7, -0.252, 2, 8.167, -0.252, 2, 9.633, -0.252, 0, 9.933, 0.1, 2, 10.633, 0.1, 2, 12.767, 0.1, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 2, 1.733, 0, 2, 2.967, 0, 2, 3.267, 0, 2, 3.933, 0, 2, 4.567, 0, 2, 7, 0, 2, 8.167, 0, 2, 9.633, 0, 0, 9.933, -0.5, 2, 10.633, -0.5, 0, 11.033, 0, 1, 11.244, 0, 11.456, -0.25, 11.667, -0.338, 1, 11.822, -0.403, 11.978, -0.414, 12.133, -0.444, 1, 12.344, -0.485, 12.556, -0.5, 12.767, -0.5, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 2, 1.733, 0, 2, 2.967, 0, 2, 3.267, 0, 2, 3.933, 0, 2, 4.567, 0, 2, 7, 0, 2, 8.167, 0, 2, 9.633, 0, 2, 10.633, 0, 0, 11.033, -0.2, 2, 11.667, -0.2, 2, 12.133, -0.2, 0, 12.767, 0, 2, 14, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 2, 1.733, 0, 2, 2.967, 0, 2, 3.267, 0, 2, 3.933, 0, 2, 4.567, 0, 2, 7, 0, 2, 8.167, 0, 2, 9.633, 0, 2, 10.633, 0, 0, 11.033, -0.2, 2, 11.667, -0.2, 2, 12.133, -0.2, 0, 12.767, 0, 2, 14, 0]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, -1, 2, 1.733, -1, 0, 2.267, 0, 2, 2.967, 0, 2, 3.267, 0, 0, 3.933, -1, 2, 4.567, -1, 2, 7, -1, 2, 8.167, -1, 2, 9.633, -1, 2, 9.933, -1, 0, 10.033, -0.858, 0, 10.133, -1, 0, 10.233, -0.858, 0, 10.333, -1, 0, 10.433, -0.858, 0, 10.533, -1, 0, 10.633, -0.858, 2, 11.667, -0.858, 2, 12.133, -0.858, 2, 12.767, -0.858, 0, 14, -1]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, -1, 2, 1.733, -1, 0, 2.267, 0, 2, 2.967, 0, 2, 3.267, 0, 0, 3.933, -1, 2, 4.567, -1, 2, 7, -1, 2, 8.167, -1, 2, 9.633, -1, 2, 9.933, -1, 0, 10.033, -0.858, 0, 10.133, -1, 0, 10.233, -0.858, 0, 10.333, -1, 0, 10.433, -0.858, 0, 10.533, -1, 0, 10.633, -0.858, 2, 11.667, -0.858, 2, 12.133, -0.858, 2, 12.767, -0.858, 0, 14, -1]}, {"Target": "Parameter", "Id": "Param", "Segments": [0, 0, 2, 1.733, 0, 2, 2.967, 0, 2, 3.267, 0, 2, 3.933, 0, 0, 4.367, 2, 0, 4.567, 0, 2, 6.333, 0, 0, 7, -12.505, 0, 7.2, 30, 0, 7.833, -30, 2, 8.067, -30, 2, 8.167, -30, 2, 8.667, -30, 1, 8.7, -30, 8.734, -27.083, 8.767, -27, 1, 9.056, -26.284, 9.344, -25.491, 9.633, -25.061, 1, 9.7, -24.962, 9.766, -25, 9.833, -25, 0, 10, -30, 2, 10.633, -30, 0, 11.133, 30, 2, 12.133, 30, 2, 12.767, 30, 0, 14, -30]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 0, 2, 1.733, 0, 2, 2.967, 0, 2, 3.267, 0, 0, 3.367, 0.3, 0, 3.6, -0.5, 0, 3.8, 0.4, 1, 3.889, 0.4, 3.978, 0.325, 4.067, 0.2, 1, 4.167, 0.059, 4.267, -0.014, 4.367, -0.2, 1, 4.411, -0.283, 4.456, -1.5, 4.5, -1.5, 1, 4.522, -1.5, 4.545, 0.104, 4.567, 0.2, 1, 4.611, 0.392, 4.656, 0.4, 4.7, 0.4, 0, 4.767, -1.4, 1, 4.822, -1.4, 4.878, -1.067, 4.933, -0.4, 1, 4.978, 0.133, 5.022, 0.4, 5.067, 0.4, 1, 5.111, 0.4, 5.156, -0.476, 5.2, -0.7, 1, 5.244, -0.924, 5.289, -0.9, 5.333, -0.9, 0, 5.467, -0.1, 0, 5.633, -0.2, 0, 6.333, 0, 1, 6.555, 0, 6.778, -0.227, 7, -0.417, 1, 7.067, -0.474, 7.133, -0.437, 7.2, -0.5, 1, 7.411, -0.699, 7.622, -1, 7.833, -1, 2, 8.067, -1, 2, 8.167, -1, 2, 8.767, -1, 2, 8.867, -1, 2, 9.633, -1, 2, 10, -1, 2, 10.633, -1, 2, 11.667, -1, 2, 12.133, -1, 2, 12.767, -1, 0, 14, 1]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 2, 1.733, 0, 2, 2.967, 0, 2, 3.267, 0, 0, 3.367, 0.215, 0, 3.467, 0.044, 0, 3.6, 0.258, 0, 3.7, 0.044, 1, 3.733, 0.044, 3.767, 0.065, 3.8, 0.172, 1, 3.811, 0.208, 3.822, 0.4, 3.833, 0.4, 0, 3.933, 0.28, 0, 4.067, 0.6, 0, 4.167, 0.1, 0, 4.233, 0.5, 1, 4.278, 0.5, 4.322, 0.35, 4.367, 0.167, 1, 4.4, 0.03, 4.434, 0, 4.467, 0, 1, 4.5, 0, 4.534, 0.173, 4.567, 0.3, 1, 4.611, 0.47, 4.656, 0.5, 4.7, 0.5, 0, 4.833, 0, 1, 4.866, 0, 4.9, 0.216, 4.933, 0.4, 1, 4.978, 0.646, 5.022, 0.7, 5.067, 0.7, 0, 5.2, 0.3, 0, 5.333, 0.6, 0, 5.467, 0.2, 0, 5.633, 0.5, 1, 5.866, 0.5, 6.1, 0.484, 6.333, 0.4, 1, 6.555, 0.32, 6.778, 0.233, 7, 0.233, 0, 7.2, 0.6, 0, 7.833, 0, 0, 8.067, 0.3, 2, 8.167, 0.3, 0, 8.333, 0, 0, 8.5, 0.6, 0, 8.667, 0.3, 0, 8.767, 0.6, 1, 8.8, 0.6, 8.834, 0.531, 8.867, 0.3, 1, 8.889, 0.146, 8.911, 0, 8.933, 0, 0, 9, 0.4, 0, 9.133, 0.2, 0, 9.233, 0.5, 0, 9.4, 0.2, 0, 9.533, 0.4, 0, 9.633, 0.2, 0, 9.7, 0.6, 0, 9.8, 0.2, 0, 10, 0.8, 1, 10.122, 0.8, 10.245, 0.661, 10.367, 0.3, 1, 10.422, 0.136, 10.478, 0, 10.533, 0, 2, 10.633, 0, 0, 11.133, 0.6, 0, 11.667, 0.3, 2, 12.133, 0.3, 2, 12.767, 0.3, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param252", "Segments": [0, 0, 2, 1.733, 0, 2, 2.967, 0, 2, 3.267, 0, 2, 3.933, 0, 2, 4.567, 0, 2, 7, 0, 0, 7.3, 1, 2, 8.167, 1, 2, 9.633, 1, 2, 10.633, 1, 2, 11.667, 1, 2, 12.133, 1, 2, 12.767, 1, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param196", "Segments": [0, 0, 2, 1.733, 0, 2, 2.967, 0, 2, 3.267, 0, 2, 3.933, 0, 2, 4.567, 0, 2, 7, 0, 2, 8.167, 0, 2, 10.633, 0, 0, 10.733, 0.9, 0, 10.867, -0.4, 0, 11, 0.6, 0, 11.133, -0.4, 0, 11.267, 0.6, 0, 11.4, -0.4, 0, 11.533, 0.6, 0, 11.667, -0.4, 0, 11.8, 0.6, 1, 11.822, 0.6, 11.845, 0.433, 11.867, 0.1, 1, 11.889, -0.233, 11.911, -0.4, 11.933, -0.4, 0, 12.067, 0.6, 1, 12.089, 0.6, 12.111, 0.433, 12.133, 0.1, 1, 12.155, -0.233, 12.178, -0.4, 12.2, -0.4, 0, 12.333, 0.6, 0, 12.467, -0.4, 0, 12.6, 0.6, 0, 12.733, -0.4, 1, 12.744, -0.4, 12.756, -0.399, 12.767, -0.244, 1, 12.8, 0.223, 12.834, 0.6, 12.867, 0.6, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param194", "Segments": [0, 0, 2, 1.733, 0, 2, 2.967, 0, 2, 3.267, 0, 2, 3.933, 0, 2, 4.567, 0, 2, 7, 0, 2, 8.167, 0, 2, 9.633, 0, 2, 10.633, 0, 0, 11.133, 1, 2, 11.667, 1, 2, 12.133, 1, 2, 12.767, 1, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param195", "Segments": [0, 0, 2, 1.733, 0, 2, 2.967, 0, 2, 3.267, 0, 2, 3.933, 0, 2, 4.567, 0, 2, 7, 0, 2, 8.167, 0, 2, 9.633, 0, 2, 10.633, 0, 2, 11.133, 0, 0, 11.233, 1.47, 0, 11.333, 0, 0, 11.433, 1.47, 0, 11.533, 0, 0, 11.633, 1.47, 0, 11.733, 0, 0, 11.833, 1.47, 0, 11.933, 0, 0, 12.033, 1.47, 0, 12.133, 0, 0, 12.267, 1.47, 0, 12.367, 0, 2, 12.767, 0, 2, 14, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 2, 0.2, 0, 0, 0.567, -16, 0, 1.067, 20, 2, 1.733, 20, 2, 2.967, 20, 0, 3.267, 0, 0, 3.933, 26, 1, 4.144, 26, 4.356, 26.198, 4.567, 21.8, 1, 4.745, 18.096, 4.922, -11, 5.1, -11, 1, 5.256, -11, 5.411, -4.528, 5.567, -3.561, 1, 6.045, -0.593, 6.522, 0, 7, 0, 0, 7.3, -25.469, 0, 7.633, -3.047, 0, 8, -18.146, 2, 8.167, -18.146, 0, 8.567, -3.561, 0, 9.567, -8.173, 1, 9.745, -8.173, 9.922, 2.332, 10.1, 8.45, 1, 10.211, 12.273, 10.322, 12, 10.433, 12, 1, 10.5, 12, 10.566, 10.58, 10.633, 9.835, 1, 10.8, 7.971, 10.966, 6.075, 11.133, 5.12, 1, 11.311, 4.101, 11.489, 4, 11.667, 4, 2, 12.133, 4, 2, 12.767, 4, 0, 13.067, 24, 0, 13.667, -10, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 2, 0.2, 0, 0, 0.4, -21, 0, 0.633, 1, 0, 0.867, -21, 1, 0.934, -21, 1, -18.362, 1.067, -17, 1, 1.289, -12.459, 1.511, -9.97, 1.733, -5, 1, 1.955, -0.03, 2.178, 5.02, 2.4, 5.02, 2, 2.967, 5.02, 2, 3.267, 5.02, 0, 3.933, -14, 0, 4.567, 0, 0, 5.1, -19, 0, 5.967, -9.763, 0, 7, -19, 0, 7.2, -2, 0, 7.533, -25, 0, 7.967, -21, 2, 8.167, -21, 1, 8.256, -21, 8.344, -14.655, 8.433, -11, 1, 8.566, -5.517, 8.7, -4.504, 8.833, -4.504, 0, 9.133, -16.44, 0, 9.633, -4, 2, 9.667, -4, 0, 10.1, -18.46, 0, 10.4, -5, 2, 10.633, -5, 0, 11, 6.497, 0, 11.667, -14, 0, 12.133, -9.763, 2, 12.767, -9.763, 0, 13.2, -13.54, 0, 13.7, 3.138, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 2, 0.2, 0, 1, 0.333, 0, 0.467, 8.925, 0.6, 11.28, 1, 0.756, 14.027, 0.911, 14, 1.067, 14, 2, 1.733, 14, 2, 2.967, 14, 2, 3.233, 14, 0, 3.267, 5.66, 0, 3.933, 6.18, 2, 4.567, 6.18, 0, 4.8, 3.72, 0, 5.267, 7.98, 1, 5.556, 7.98, 5.844, 7.26, 6.133, 6.9, 1, 6.422, 6.54, 6.711, 6.469, 7, 6.18, 1, 7.067, 6.113, 7.133, 5.011, 7.2, 4.26, 1, 7.311, 3.008, 7.422, 2.16, 7.533, 2.16, 2, 8.167, 2.16, 0, 8.733, 3.782, 0, 9.133, 1.139, 0, 9.733, 2.16, 0, 9.9, 0, 0, 10.1, 0.639, 2, 10.633, 0.639, 2, 11.667, 0.639, 2, 12.133, 0.639, 2, 12.767, 0.639, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param267", "Segments": [0, 0, 2, 0.2, 0, 0, 0.6, -10, 0, 1.067, 0, 2, 1.733, 0, 2, 2.967, 0, 2, 3.267, 0, 0, 3.933, -11.28, 1, 4.144, -11.28, 4.356, -10.957, 4.567, -9.36, 1, 4.8, -7.595, 5.034, -4.566, 5.267, -2.52, 1, 5.556, 0.013, 5.844, 2.629, 6.133, 4.5, 1, 6.422, 6.371, 6.711, 6.84, 7, 6.84, 1, 7.067, 6.84, 7.133, 6.133, 7.2, 4.5, 1, 7.311, 1.779, 7.422, 0.3, 7.533, 0.3, 2, 8.167, 0.3, 2, 9.733, 0.3, 0, 9.9, 1.493, 0, 10.1, 0.653, 2, 10.633, 0.653, 0, 10.867, -1.308, 0, 11.667, 3.113, 2, 12.133, 3.113, 2, 12.767, 3.113, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param3", "Segments": [0, 0, 2, 0.2, 0, 0, 0.5, 6, 0, 1.067, -30, 0, 1.467, -25.652, 2, 1.733, -25.652, 2, 2.967, -25.652, 2, 3.267, -25.652, 0, 3.933, -4.652, 1, 4.144, -4.652, 4.356, -5.441, 4.567, -8.946, 1, 4.745, -11.898, 4.922, -16.348, 5.1, -21.2, 1, 5.156, -22.716, 5.211, -29, 5.267, -29, 0, 6.133, -27.02, 2, 7, -27.02, 0, 7.367, -12.2, 0, 7.667, -19.4, 0, 7.967, -17.3, 2, 8.167, -17.3, 2, 9.633, -17.3, 2, 9.667, -17.3, 2, 10.633, -17.3, 0, 11.667, -21.32, 2, 12.133, -21.32, 2, 12.767, -21.32, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param4", "Segments": [0, 0, 2, 0.2, 0, 0, 0.333, -3.217, 1, 0.389, -3.217, 0.444, 1.465, 0.5, 5, 1, 0.544, 7.828, 0.589, 8.043, 0.633, 8.043, 0, 0.867, -6.871, 1, 0.934, -6.871, 1, -5.387, 1.067, -2, 1, 1.111, 0.258, 1.156, 1.625, 1.2, 1.625, 0, 1.5, -3.217, 2, 1.733, -3.217, 0, 1.767, -30, 0, 2.133, 4, 0, 2.433, -5, 2, 2.967, -5, 0, 3.267, 5.26, 0, 3.933, -5, 0, 4.567, -2.12, 1, 4.745, -2.12, 4.922, -5.618, 5.1, -8, 1, 5.256, -10.084, 5.411, -11.199, 5.567, -12.38, 1, 5.756, -13.814, 5.944, -14.24, 6.133, -14.24, 2, 7, -14.24, 0, 7.133, -12.38, 0, 7.433, -25.603, 1, 7.511, -25.603, 7.589, -20.338, 7.667, -17.828, 1, 7.767, -14.601, 7.867, -14.24, 7.967, -14.24, 2, 8.167, -14.24, 0, 8.833, -12.14, 0, 9.133, -14.24, 2, 9.633, -14.24, 2, 9.667, -14.24, 0, 10.1, -20.777, 0, 10.4, -17.828, 2, 10.633, -17.828, 0, 10.9, -12.38, 1, 11.067, -12.38, 11.233, -15.976, 11.4, -20.081, 1, 11.489, -22.27, 11.578, -22.507, 11.667, -22.507, 0, 12.133, -20.081, 2, 12.767, -20.081, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param266", "Segments": [0, 0, 2, 0.2, 0, 1, 0.3, 0, 0.4, 13.092, 0.5, 22.688, 1, 0.544, 26.953, 0.589, 26.418, 0.633, 26.418, 0, 1.067, -14.44, 0, 1.533, 2.443, 2, 1.733, 2.443, 2, 2.967, 2.443, 2, 3.267, 2.443, 0, 3.933, -20.897, 1, 4.144, -20.897, 4.356, -20.328, 4.567, -17.897, 1, 4.745, -15.85, 4.922, -13.74, 5.1, -13.74, 2, 7, -13.74, 0, 7.333, -22, 0, 7.567, -5.46, 0, 8.033, -16.012, 2, 8.167, -16.012, 2, 9.633, -16.012, 2, 9.667, -16.012, 2, 10.633, -16.012, 2, 11.667, -16.012, 2, 12.133, -16.012, 2, 12.767, -16.012, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param5", "Segments": [0, 0, 2, 1.733, 0, 2, 2.967, 0, 2, 3.267, 0, 0, 3.933, -18, 0, 4.567, -16, 1, 4.745, -16, 4.922, -17.878, 5.1, -19.9, 1, 5.444, -23.818, 5.789, -25.18, 6.133, -25.18, 2, 7, -25.18, 0, 7.167, -25.656, 0, 7.5, -9.64, 2, 8.167, -9.64, 1, 8.267, -9.64, 8.367, -12.539, 8.467, -14.769, 1, 8.589, -17.494, 8.711, -18, 8.833, -18, 1, 8.966, -18, 9.1, -8.436, 9.233, -6.118, 1, 9.4, -3.22, 9.566, -2.725, 9.733, -0.298, 1, 9.789, 0.51, 9.844, 10.296, 9.9, 10.296, 0, 10.3, 2.686, 2, 10.667, 2.686, 0, 11.133, 13.562, 0, 11.667, 0, 2, 12.133, 0, 2, 12.767, 0, 2, 14, 0]}, {"Target": "Parameter", "Id": "Param6", "Segments": [0, 0, 2, 1.733, 0, 2, 2.967, 0, 2, 3.267, 0, 0, 3.933, 9.18, 0, 4.567, 0, 1, 4.745, 0, 4.922, 6.036, 5.1, 7.98, 1, 5.444, 11.747, 5.789, 12.3, 6.133, 12.3, 2, 7, 12.3, 0, 7.167, 11.066, 0, 7.5, 26.16, 2, 8.167, 26.16, 1, 8.267, 26.16, 8.367, 23.653, 8.467, 21.151, 1, 8.589, 18.092, 8.711, 17.309, 8.833, 17.309, 0, 9.2, 26.16, 0, 9.733, 21.337, 0, 9.9, 30, 0, 10.267, 24.06, 2, 10.633, 24.06, 0, 11.133, 30, 0, 11.667, 29, 2, 12.133, 29, 2, 12.767, 29, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param201", "Segments": [0, 1, 2, 1.733, 1, 2, 2.967, 1, 2, 3.267, 1, 2, 3.933, 1, 2, 4.567, 1, 2, 7, 1, 2, 9.633, 1, 2, 10.633, 1, 2, 11.667, 1, 2, 12.133, 1, 2, 12.767, 1, 2, 13.567, 1, 0, 13.6, 0, 2, 14, 0]}, {"Target": "Parameter", "Id": "Param209", "Segments": [0, 1, 2, 1.733, 1, 2, 2.967, 1, 2, 3.267, 1, 2, 3.933, 1, 2, 4.567, 1, 2, 7, 1, 2, 9.633, 1, 2, 10.633, 1, 2, 11.667, 1, 2, 12.133, 1, 2, 12.767, 1, 2, 13.567, 1, 0, 13.6, 0, 2, 14, 0]}, {"Target": "Parameter", "Id": "Param214", "Segments": [0, 0, 2, 1.733, 0, 2, 2.967, 0, 2, 3.267, 0, 2, 3.933, 0, 2, 4.567, 0, 2, 7.2, 0, 2, 7.233, 0, 2, 9.633, 0, 2, 10.633, 0, 2, 10.8, 0, 0, 10.833, 1, 2, 11.133, 1, 2, 11.667, 1, 2, 12.133, 1, 2, 12.767, 1, 2, 13.567, 1, 0, 13.6, 0, 2, 14, 0]}, {"Target": "Parameter", "Id": "Param215", "Segments": [0, 1, 2, 1.733, 1, 2, 2.967, 1, 2, 3.267, 1, 2, 3.933, 1, 2, 4.567, 1, 2, 7.2, 1, 2, 7.233, 1, 2, 9.633, 1, 2, 10.633, 1, 2, 10.8, 1, 0, 11.133, 0, 2, 11.667, 0, 2, 12.133, 0, 2, 12.767, 0, 2, 14, 0]}, {"Target": "Parameter", "Id": "Param9", "Segments": [0, 0, 2, 1.733, 0, 2, 2.967, 0, 2, 3.267, 0, 2, 3.933, 0, 2, 4.567, 0, 0, 5.1, -4.8, 2, 7, -4.8, 0, 7.3, 0.84, 1, 7.411, 0.84, 7.522, 0.779, 7.633, 0.6, 1, 7.755, 0.404, 7.878, 0.031, 8, -0.059, 1, 8.233, -0.232, 8.467, -0.371, 8.7, -0.455, 1, 8.789, -0.487, 8.878, -0.479, 8.967, -0.479, 0, 9.633, -0.419, 1, 9.966, -0.419, 10.3, -0.419, 10.633, -0.479, 1, 10.8, -0.509, 10.966, -5.579, 11.133, -5.579, 1, 11.311, -5.579, 11.489, -1.694, 11.667, -0.719, 1, 11.822, 0.134, 11.978, 0.001, 12.133, 0.001, 2, 12.767, 0.001, 0, 13.233, -4, 0, 13.8, 0.414, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param10", "Segments": [0, 0, 2, 1.733, 0, 2, 2.967, 0, 2, 3.267, 0, 2, 3.933, 0, 2, 4.567, 0, 2, 5.3, 0, 2, 7, 0, 0, 7.467, 0.6, 2, 9.633, 0.6, 2, 10.633, 0.6, 2, 11.667, 0.6, 2, 12.133, 0.6, 2, 12.767, 0.6, 0, 13.467, 10.473, 1, 13.534, 10.473, 13.6, 9.416, 13.667, 6.806, 1, 13.778, 2.456, 13.889, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param11", "Segments": [0, 0, 2, 1.733, 0, 2, 2.967, 0, 2, 3.267, 0, 2, 3.933, 0, 2, 4.567, 0, 2, 7, 0, 2, 9.633, 0, 2, 10.633, 0, 2, 11.667, 0, 2, 12.133, 0, 2, 12.767, 0, 0, 13.8, 18.86, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param22", "Segments": [0, 1, 2, 1.733, 1, 2, 2.967, 1, 2, 3.267, 1, 2, 3.933, 1, 2, 4.567, 1, 2, 7, 1, 2, 9.633, 1, 2, 10.633, 1, 2, 11.667, 1, 2, 12.133, 1, 2, 12.767, 1, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param13", "Segments": [0, 0, 2, 1.733, 0, 2, 2.967, 0, 2, 3.267, 0, 2, 3.933, 0, 2, 4.567, 0, 2, 7, 0, 0, 7.333, -8.4, 2, 9.633, -8.4, 2, 10.633, -8.4, 0, 11.133, -13, 0, 11.667, -8.4, 2, 12.133, -8.4, 2, 12.767, -8.4, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param14", "Segments": [0, 0, 2, 1.733, 0, 2, 2.967, 0, 2, 3.267, 0, 2, 3.933, 0, 2, 4.567, 0, 2, 7, 0, 2, 9.633, 0, 2, 10.633, 0, 2, 11.667, 0, 2, 12.133, 0, 2, 12.767, 0, 0, 13.3, -23.227, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param210", "Segments": [0, 0, 2, 1.733, 0, 2, 2.967, 0, 2, 3.267, 0, 1, 3.489, 0, 3.711, 0.564, 3.933, 1.26, 1, 4.144, 1.921, 4.356, 2.1, 4.567, 2.1, 0, 5.333, -4.14, 0, 6.1, -3.24, 0, 7, -4.14, 1, 7.1, -4.14, 7.2, 3.456, 7.3, 4.741, 1, 7.411, 6.169, 7.522, 6.06, 7.633, 6.06, 0, 8.233, 5.1, 1, 8.389, 5.1, 8.544, 6.956, 8.7, 7.56, 1, 9.011, 8.768, 9.322, 8.94, 9.633, 8.94, 1, 9.966, 8.94, 10.3, 8.884, 10.633, 7.56, 1, 10.8, 6.898, 10.966, -4.5, 11.133, -4.5, 0, 11.667, -2.22, 0, 12.133, -2.58, 2, 12.767, -2.58, 0, 13.067, -4.14, 1, 13.234, -4.14, 13.4, 4.16, 13.567, 12.74, 1, 13.622, 15.6, 13.678, 15.376, 13.733, 15.376, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param211", "Segments": [0, 0, 2, 1.733, 0, 2, 2.967, 0, 2, 3.267, 0, 0, 3.933, -9.18, 0, 4.567, -2.62, 0, 5.667, -16, 0, 6.467, -10.357, 0, 7, -13.84, 1, 7.144, -13.84, 7.289, -1.028, 7.433, 4.52, 1, 7.533, 8.361, 7.633, 8, 7.733, 8, 1, 7.9, 8, 8.066, 6.728, 8.233, 4.52, 1, 8.389, 2.459, 8.544, 0.153, 8.7, -1.36, 1, 9.011, -4.386, 9.322, -5.44, 9.633, -5.44, 1, 9.966, -5.44, 10.3, -4.556, 10.633, -2, 1, 10.678, -1.659, 10.722, 13.295, 10.767, 13.295, 1, 10.889, 13.295, 11.011, -4.37, 11.133, -15.46, 1, 11.222, -23.525, 11.311, -23.473, 11.4, -23.473, 1, 11.489, -23.473, 11.578, -22.148, 11.667, -21.58, 1, 11.822, -20.586, 11.978, -20.44, 12.133, -20.44, 2, 12.767, -20.44, 1, 13.034, -20.44, 13.3, -16.234, 13.567, -7.4, 1, 13.711, -2.615, 13.856, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param228", "Segments": [0, 0, 2, 1.733, 0, 2, 2.967, 0, 2, 3.267, 0, 2, 3.933, 0, 2, 4.567, 0, 2, 7, 0, 0, 7.433, 5, 0, 8.233, -9, 0, 9.633, -5.4, 0, 10.633, -13.66, 0, 11.133, 30, 0, 11.667, -3.1, 2, 12.133, -3.1, 2, 12.767, -3.1, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param283", "Segments": [0, 0, 2, 9.633, 0, 2, 11.667, 0, 0, 12.733, 30, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param221", "Segments": [0, 0, 2, 1.733, 0, 2, 2.967, 0, 2, 3.267, 0, 2, 3.933, 0, 2, 4.567, 0, 2, 7.133, 0, 0, 7.167, 1, 2, 9.633, 1, 2, 10.633, 1, 2, 11.667, 1, 2, 12.133, 1, 2, 12.767, 1, 2, 13.133, 1, 0, 13.167, 0, 2, 14, 0]}, {"Target": "Parameter", "Id": "Param223", "Segments": [0, 0, 2, 1.733, 0, 2, 2.967, 0, 2, 3.267, 0, 2, 3.933, 0, 2, 4.567, 0, 2, 7.133, 0, 0, 7.167, 1, 2, 9.633, 1, 2, 10.633, 1, 2, 11.667, 1, 2, 12.133, 1, 2, 12.767, 1, 2, 13.133, 1, 0, 13.167, 0, 2, 14, 0]}, {"Target": "Parameter", "Id": "Param237", "Segments": [0, 0, 2, 1.733, 0, 2, 2.967, 0, 2, 3.267, 0, 2, 3.933, 0, 2, 4.567, 0, 2, 7.133, 0, 2, 7.167, 0, 2, 9.633, 0, 2, 10.633, 0, 2, 10.933, 0, 0, 10.967, 1, 2, 12.133, 1, 2, 12.767, 1, 2, 13.133, 1, 0, 13.167, 0, 2, 14, 0]}, {"Target": "Parameter", "Id": "Param238", "Segments": [0, 0, 2, 1.733, 0, 2, 2.967, 0, 2, 3.267, 0, 2, 3.933, 0, 2, 4.567, 0, 2, 7.133, 0, 0, 7.167, 1, 2, 9.633, 1, 2, 10.633, 1, 2, 10.933, 1, 0, 10.967, 0, 2, 12.133, 0, 2, 12.767, 0, 2, 14, 0]}, {"Target": "Parameter", "Id": "Param15", "Segments": [0, 0, 2, 1.733, 0, 2, 2.967, 0, 2, 3.267, 0, 2, 3.933, 0, 2, 4.567, 0, 0, 5.4, -5.58, 2, 7, -5.58, 1, 7.044, -5.58, 7.089, 4.313, 7.133, 4.703, 1, 7.189, 5.19, 7.244, 5.15, 7.3, 5.581, 1, 7.378, 6.185, 7.455, 6.961, 7.533, 6.961, 1, 7.644, 6.961, 7.756, 6.899, 7.867, 6.361, 1, 8.145, 5.017, 8.422, 4.021, 8.7, 4.021, 0, 9.633, 5.221, 0, 10.633, 4.021, 0, 11.133, 12.721, 0, 11.667, 3.841, 2, 12.133, 3.841, 2, 12.767, 3.841, 0, 12.867, 3.14, 0, 13, 8.927, 1, 13.067, 8.927, 13.133, 7.97, 13.2, 6.089, 1, 13.378, 1.073, 13.555, -1.418, 13.733, -1.418, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param16", "Segments": [0, 0, 0, 0.567, 2.46, 0, 1.233, -3.411, 0, 1.733, 0, 2, 2.967, 0, 2, 3.267, 0, 2, 3.933, 0, 0, 4.233, -0.96, 1, 4.344, -0.96, 4.456, -1.327, 4.567, 0, 1, 4.845, 3.318, 5.122, 9.12, 5.4, 9.12, 0, 7, 7.057, 1, 7.044, 7.057, 7.089, 10.919, 7.133, 13.28, 1, 7.211, 17.411, 7.289, 18.36, 7.367, 18.36, 2, 9.633, 18.36, 2, 10.633, 18.36, 0, 11.133, 17.88, 2, 11.667, 17.88, 0, 12.133, 18.12, 2, 12.767, 18.12, 0, 12.933, 18.699, 1, 13.022, 18.699, 13.111, 2.893, 13.2, -0.095, 1, 13.278, -2.709, 13.355, -2.421, 13.433, -2.421, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param17", "Segments": [0, 0, 0, 0.7, -5.456, 0, 1.4, 9.562, 0, 1.733, 0, 2, 2.967, 0, 2, 3.267, 0, 2, 3.933, 0, 0, 4.4, 4.62, 1, 4.456, 4.62, 4.511, 0.64, 4.567, 0, 1, 4.845, -3.202, 5.122, -4.976, 5.4, -8.4, 1, 5.733, -12.508, 6.067, -16, 6.4, -16, 0, 7, -4.8, 2, 9.633, -4.8, 2, 10.633, -4.8, 2, 11.667, -4.8, 2, 12.133, -4.8, 2, 12.767, -4.8, 0, 13.367, -11, 0, 13.7, 3.31, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param18", "Segments": [0, 0, 0, 0.8, -6.311, 0, 1.533, 7.428, 0, 2.967, 0, 2, 3.267, 0, 2, 3.933, 0, 1, 4.122, 0, 4.311, 3.167, 4.5, 7.011, 1, 4.8, 13.116, 5.1, 15.271, 5.4, 15.271, 0, 6.567, 0, 1, 6.734, 0, 6.9, 10.188, 7.067, 11.338, 1, 7.922, 17.238, 8.778, 19, 9.633, 19, 2, 10.633, 19, 2, 11.667, 19, 2, 12.133, 19, 2, 12.767, 19, 0, 13.333, -2.14, 0, 13.767, 6.642, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param19", "Segments": [0, 0, 2, 1.733, 0, 2, 2.967, 0, 2, 3.267, 0, 2, 3.933, 0, 2, 4.567, 0, 2, 7, 0, 1, 7.044, 0, 7.089, -17.612, 7.133, -34.255, 1, 7.189, -55.059, 7.244, -60, 7.3, -60, 2, 9.633, -60, 0, 10.633, -38, 0, 11.133, -55, 0, 11.667, -50.01, 2, 12.133, -50.01, 2, 12.767, -50.01, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param20", "Segments": [0, 0, 2, 1.733, 0, 2, 2.967, 0, 2, 3.267, 0, 2, 3.933, 0, 2, 4.567, 0, 0, 5.833, 8, 2, 7, 8, 2, 9.633, 8, 2, 10.633, 8, 2, 11.667, 8, 2, 12.133, 8, 2, 12.767, 8, 0, 13.1, -10.539, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param233", "Segments": [0, 0, 2, 1.733, 0, 2, 2.967, 0, 2, 3.267, 0, 2, 3.933, 0, 2, 4.567, 0, 2, 7, 0, 0, 7.133, 0.309, 0, 7.3, -4.595, 0, 7.533, -3.54, 1, 7.7, -3.54, 7.866, -4.244, 8.033, -4.605, 1, 8.1, -4.75, 8.166, -4.632, 8.233, -4.8, 1, 8.389, -5.192, 8.544, -6.66, 8.7, -6.66, 1, 9.011, -6.66, 9.322, -6.299, 9.633, -6.18, 1, 9.966, -6.053, 10.3, -6.059, 10.633, -5.94, 1, 10.8, -5.88, 10.966, 0.34, 11.133, 0.34, 0, 11.667, -2.84, 0, 12.133, -2.36, 2, 12.767, -2.36, 0, 12.833, -2.564, 1, 12.889, -2.564, 12.944, -1.128, 13, 2, 1, 13.044, 4.503, 13.089, 6.752, 13.133, 9.785, 1, 13.166, 12.06, 13.2, 16.025, 13.233, 18.769, 1, 13.255, 20.598, 13.278, 21.214, 13.3, 21.214, 0, 13.8, -0.19, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param234", "Segments": [0, 0, 2, 1.733, 0, 2, 2.967, 0, 2, 3.267, 0, 2, 3.933, 0, 2, 4.567, 0, 2, 7, 0, 0, 7.267, 14, 1, 7.334, 14, 7.4, 10.687, 7.467, -1.68, 1, 7.511, -9.925, 7.556, -19, 7.6, -19, 1, 7.811, -19, 8.022, -14.196, 8.233, -1.68, 1, 8.389, 7.542, 8.544, 14, 8.7, 14, 0, 9.2, 3.545, 1, 9.344, 3.545, 9.489, 12.251, 9.633, 12.405, 1, 9.966, 12.759, 10.3, 12.78, 10.633, 12.78, 0, 10.8, -7.742, 1, 10.911, -7.742, 11.022, 25.171, 11.133, 30, 1, 11.222, 33.863, 11.311, 33.486, 11.4, 36.54, 1, 11.489, 39.594, 11.578, 45, 11.667, 45, 0, 12.133, 43.2, 2, 12.767, 43.2, 1, 12.845, 43.2, 12.922, 17.709, 13, 15, 1, 13.333, 3.389, 13.667, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param232", "Segments": [0, 0, 2, 1.733, 0, 2, 2.967, 0, 2, 3.267, 0, 2, 3.933, 0, 2, 4.567, 0, 2, 7, 0, 2, 8.233, 0, 2, 8.7, 0, 2, 9.633, 0, 2, 10.633, 0, 2, 11.667, 0, 0, 12.767, -30, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param235", "Segments": [0, 0, 2, 1.733, 0, 2, 2.967, 0, 2, 3.267, 0, 2, 3.933, 0, 2, 4.567, 0, 2, 7, 0, 0, 7.167, -12.15, 0, 7.333, 12.369, 0, 7.567, 12, 0, 7.867, 19.82, 2, 8.233, 19.82, 1, 8.389, 19.82, 8.544, 6.821, 8.7, 6.039, 1, 9.011, 4.475, 9.322, 4.4, 9.633, 4.4, 1, 9.966, 4.4, 10.3, 9.788, 10.633, 19.82, 1, 10.8, 24.836, 10.966, 27, 11.133, 27, 0, 11.667, 13, 2, 12.133, 13, 2, 12.767, 13, 1, 12.845, 13, 12.922, 10.489, 13, -4, 1, 13.044, -12.279, 13.089, -30, 13.133, -30, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param274", "Segments": [0, 0, 2, 11.4, 0, 0, 11.667, -0.6, 2, 12.133, -0.6, 2, 12.767, -0.6, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param276", "Segments": [0, 0, 2, 11.4, 0, 0, 11.667, 0.8, 2, 12.133, 0.8, 2, 12.767, 0.8, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param142", "Segments": [0, 13.314, 0, 1, 15.78, 2, 1.733, 15.78, 0, 1.767, 18.96, 2, 2.733, 18.96, 2, 2.967, 18.96, 2, 3.233, 18.96, 0, 3.267, 0, 2, 3.933, 0, 2, 4.567, 0, 2, 5.9, 0, 2, 7, 0, 1, 7.367, 0, 7.733, 4.826, 8.1, 5.28, 1, 8.944, 6.04, 9.789, 6.8, 10.633, 7.56, 1, 11.133, 8.257, 11.633, 24, 12.133, 24, 2, 12.733, 24, 1, 12.744, 24, 12.756, 7.394, 12.767, 7.26, 1, 13.178, 2.311, 13.589, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param143", "Segments": [0, 0, 2, 1.733, 0, 2, 2.733, 0, 2, 2.967, 0, 2, 3.233, 0, 2, 3.267, 0, 2, 3.933, 0, 2, 4.567, 0, 0, 5.9, -3.4, 2, 7, -3.4, 1, 7.367, -3.4, 7.733, -2.143, 8.1, -2, 1, 8.944, -1.88, 9.789, -1.76, 10.633, -1.64, 2, 12.133, -1.64, 2, 12.733, -1.64, 0, 12.767, 0, 2, 14, 0]}, {"Target": "Parameter", "Id": "Param144", "Segments": [0, 1.873, 0, 1, 2.22, 2, 1.733, 2.22, 0, 1.767, -5.22, 0, 2.733, -4.56, 2, 2.967, -4.56, 2, 3.233, -4.56, 0, 3.267, 0, 2, 3.933, 0, 2, 4.567, 0, 0, 5.9, 0.78, 2, 7, 0.78, 1, 7.367, 0.78, 7.733, -0.827, 8.1, -0.9, 1, 8.944, -1.02, 9.789, -1.14, 10.633, -1.26, 1, 11.133, -1.368, 11.633, -2.52, 12.133, -2.52, 2, 12.733, -2.52, 0, 12.767, 0, 2, 14, 0]}, {"Target": "Parameter", "Id": "Param145", "Segments": [0, 0, 0, 0.067, 1, 2, 12.733, 1, 0, 12.767, 0, 2, 14, 0]}, {"Target": "Parameter", "Id": "Param134", "Segments": [0, 1, 0, 0.733, 0, 2, 1, 0, 2, 1.567, 0, 2, 1.733, 0, 2, 2.967, 0, 2, 3.267, 0, 2, 3.933, 0, 2, 4.567, 0, 2, 7, 0, 2, 9.633, 0, 2, 10.633, 0, 2, 11.667, 0, 2, 12.133, 0, 2, 12.733, 0, 2, 12.767, 0, 2, 14, 0]}, {"Target": "Parameter", "Id": "Param245", "Segments": [0, 0, 0, 0.5, -0.78, 2, 1.733, -0.78, 2, 2.967, -0.78, 2, 3.267, -0.78, 2, 3.933, -0.78, 2, 4.567, -0.78, 2, 7, -0.78, 2, 9.633, -0.78, 2, 10.633, -0.78, 2, 11.667, -0.78, 2, 12.133, -0.78, 2, 12.767, -0.78, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param140", "Segments": [0, 0, 0, 0.5, 1, 2, 1.733, 1, 2, 2.967, 1, 2, 3.267, 1, 2, 3.933, 1, 2, 4.567, 1, 2, 7, 1, 2, 9.633, 1, 2, 10.633, 1, 2, 11.667, 1, 2, 12.133, 1, 2, 12.767, 1, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param262", "Segments": [0, 1, 2, 1.733, 1, 2, 2.967, 1, 2, 3.267, 1, 2, 3.933, 1, 2, 4.567, 1, 2, 7, 1, 2, 9.633, 1, 2, 10.633, 1, 2, 11.667, 1, 2, 12.133, 1, 2, 12.767, 1, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param263", "Segments": [0, 0, 2, 1.367, 0, 0, 1.733, -1, 0, 2.233, 0, 2, 2.967, 0, 0, 3.267, -1, 0, 3.933, 0, 2, 4.567, 0, 2, 7, 0, 2, 9.633, 0, 2, 10.633, 0, 2, 11.667, 0, 2, 12.133, 0, 0, 12.733, -1, 1, 12.744, -1, 12.756, -1.011, 12.767, -0.995, 1, 13.178, -0.405, 13.589, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param277", "Segments": [0, 0, 0, 0.267, -11, 0, 0.8, 12, 0, 1.367, -15, 1, 1.9, -15, 2.434, -14.522, 2.967, -8, 1, 3.311, -3.788, 3.656, 11, 4, 11, 0, 5.267, -15, 1, 5.856, -15, 6.444, -6.668, 7.033, -2, 1, 7.344, 0.466, 7.656, 0, 7.967, 0, 0, 8.633, -11, 0, 9.033, 11, 0, 9.467, -8, 0, 9.8, 7, 0, 10.233, -16, 1, 10.411, -16, 10.589, 3.456, 10.767, 6, 1, 11.434, 15.539, 12.1, 18, 12.767, 18, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param278", "Segments": [0, 0, 0, 0.267, -0.4, 0, 0.8, 0.5, 0, 1.233, -0.6, 0, 1.767, 0.5, 0, 2.3, -0.51, 1, 2.522, -0.51, 2.745, -0.437, 2.967, -0.3, 1, 3.145, -0.19, 3.322, -0.13, 3.5, 0.021, 1, 3.6, 0.106, 3.7, 0.5, 3.8, 0.5, 0, 4.333, -0.6, 0, 4.8, 0.5, 0, 5.233, -0.548, 0, 5.667, 0.451, 0, 6.2, -0.548, 0, 6.7, 0.451, 0, 7.2, -0.548, 0, 7.733, 0.428, 0, 8.333, -0.572, 0, 9.133, 0.419, 0, 9.833, -0.51, 0, 10.8, 0.366, 0, 11.633, -0.451, 0, 12.3, 0.33, 0, 13.1, -0.451, 0, 13.7, 0.227, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param280", "Segments": [0, 0, 0, 0.267, -0.3, 0, 0.8, 0.4, 0, 1.367, -0.5, 1, 1.9, -0.5, 2.434, -0.383, 2.967, -0.2, 1, 3.2, -0.12, 3.434, -0.093, 3.667, 0, 1, 3.778, 0.044, 3.889, 0.4, 4, 0.4, 0, 5.267, -0.5, 1, 5.856, -0.5, 6.444, -0.123, 7.033, 0, 1, 8.944, 0.398, 10.856, 0.5, 12.767, 0.5, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param7", "Segments": [0, 0, 2, 1.733, 0, 2, 2.967, 0, 2, 3.267, 0, 0, 3.567, 20.461, 0, 3.933, -12.167, 0, 4.333, 12.072, 0, 4.733, -8.485, 0, 5.167, 7.523, 0, 5.667, 0, 2, 7, 0, 0, 7.333, -30, 2, 9.633, -30, 2, 10.633, -30, 0, 11.667, 0, 0, 12.133, -30, 2, 12.767, -30, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param66", "Segments": [0, 0, 2, 1.733, 0, 0, 2.033, -23, 0, 2.3, 16, 0, 2.567, -11, 0, 2.8, 4, 2, 2.967, 4, 2, 3.267, 4, 2, 3.933, 4, 0, 4.233, 12, 1, 4.344, 12, 4.456, -5.864, 4.567, -8, 1, 4.722, -10.99, 4.878, -11, 5.033, -11, 0, 5.333, 12, 1, 5.889, 12, 6.444, 9.905, 7, 4, 1, 7.144, 2.465, 7.289, 0, 7.433, 0, 0, 7.567, 14, 0, 7.767, -14, 0, 7.9, 8, 0, 8.1, 0, 2, 8.767, 0, 0, 9.033, -9, 0, 9.3, 8, 0, 9.467, -9, 0, 9.6, 11, 0, 9.933, -8, 0, 10.1, 0, 0, 10.233, -4, 0, 10.633, 0, 0, 11.1, -15, 0, 11.667, 11, 0, 12.133, -30, 2, 12.767, -30, 0, 13.167, 12, 0, 13.867, -6, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param67", "Segments": [0, 0, 2, 1.733, 0, 0, 1.933, 11, 0, 2.167, -18, 0, 2.4, 21, 0, 2.667, -12, 0, 2.967, 8, 2, 3.267, 8, 2, 3.933, 8, 0, 4.233, 13, 0, 4.567, -10, 0, 5.333, 14, 1, 5.922, 14, 6.511, 12.723, 7.1, 8, 1, 7.244, 6.842, 7.389, 0, 7.533, 0, 0, 7.667, 14, 0, 7.867, -16, 0, 8, 8, 0, 8.2, 0, 2, 8.867, 0, 0, 9.133, -11, 0, 9.4, 10, 0, 9.567, -11, 0, 9.7, 11, 0, 10.033, -9, 0, 10.2, 0, 0, 10.333, -4, 0, 10.733, 0, 0, 11.2, -15, 0, 11.767, 12, 0, 12.233, 0, 2, 12.867, 0, 0, 13.267, 14, 0, 13.867, -6, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param68", "Segments": [0, 0, 2, 1.733, 0, 0, 2.033, 23, 0, 2.3, -16, 0, 2.567, 11, 0, 2.8, -4, 2, 2.967, -4, 2, 3.267, -4, 2, 3.933, -4, 0, 4.233, 12, 0, 4.567, -11, 0, 5.333, 14, 0, 7, -4, 1, 7.144, -4, 7.289, -3.262, 7.433, 0, 1, 7.478, 1.004, 7.522, 15, 7.567, 15, 0, 7.767, -14, 0, 7.9, 9, 0, 8.1, 0, 2, 8.767, 0, 0, 9.033, -9, 0, 9.3, 10, 0, 9.467, -11, 0, 9.6, 9, 0, 9.933, -9, 0, 10.1, 0, 0, 10.233, -5, 0, 10.633, 0, 0, 11.1, -16, 1, 11.289, -16, 11.478, -5.7, 11.667, 11, 1, 11.822, 24.753, 11.978, 30, 12.133, 30, 2, 12.767, 30, 1, 12.9, 30, 13.034, 20.391, 13.167, 12, 1, 13.4, -2.684, 13.634, -7, 13.867, -7, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param69", "Segments": [0, 0, 2, 1.733, 0, 0, 1.933, -11, 0, 2.167, 18, 0, 2.4, -21, 0, 2.667, 12, 0, 2.967, -8, 2, 3.267, -8, 2, 3.933, -8, 0, 4.233, 14, 0, 4.567, -11, 0, 5.333, 18, 0, 7.1, -8, 1, 7.244, -8, 7.389, -6.409, 7.533, 0, 1, 7.578, 1.972, 7.622, 15, 7.667, 15, 0, 7.867, -14, 0, 8, 13, 0, 8.2, 0, 2, 8.867, 0, 0, 9.133, -10, 0, 9.4, 11, 0, 9.567, -11, 0, 9.7, 11, 0, 10.033, -9, 0, 10.2, 0, 0, 10.333, -4, 0, 10.733, 0, 0, 11.2, -14, 0, 11.767, 12, 0, 12.233, -1, 2, 12.867, -1, 0, 13.267, 17, 0, 13.867, -6, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param35", "Segments": [0, 0, 0, 0.367, 9.261, 0, 0.633, -19.27, 0, 0.9, 14.253, 0, 1.467, -4.735, 0, 1.833, 0.205, 0, 2.167, -0.881, 0, 2.533, 0.302, 0, 2.933, -0.61, 0, 3.633, 2.728, 0, 3.967, -4.509, 0, 4.867, 5.336, 0, 5.3, -4.281, 0, 5.733, 0.242, 0, 6, -0.2, 0, 6.4, 0.353, 0, 7.2, -8.715, 0, 7.467, 11.614, 0, 7.833, -6.882, 0, 8.2, 2.505, 0, 8.5, -4.183, 0, 9.067, 4.927, 0, 9.4, -5.955, 0, 9.9, 5.75, 0, 10.333, -8.448, 0, 10.667, 4.195, 0, 10.967, -4.082, 0, 11.333, 5.222, 0, 11.833, -3.326, 0, 12.267, 1.496, 0, 12.667, -0.514, 0, 13.1, 2.09, 0, 13.533, -3.816, 0, 13.9, 4.084, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param36", "Segments": [0, 0, 0, 0.2, -2.316, 0, 0.233, -2.01, 0, 0.333, -3.902, 0, 0.567, 17.086, 0, 0.833, -24.197, 0, 1.167, 15.775, 0, 1.6, -7.822, 0, 2, 3.888, 0, 2.433, -1.988, 0, 2.8, 1.188, 0, 3.2, -0.718, 0, 3.333, -0.4, 0, 3.5, -1.05, 0, 3.867, 4.126, 0, 4.2, -4.687, 0, 4.567, 1.776, 0, 4.833, -1.353, 0, 5.167, 4.264, 0, 5.533, -4.266, 0, 5.9, 2.287, 0, 6.3, -1.007, 0, 6.7, 0.512, 0, 7.033, -0.042, 0, 7.167, 5.51, 0, 7.4, -12.108, 0, 7.7, 13.994, 0, 8.067, -10.255, 0, 8.433, 7.771, 0, 8.767, -5.412, 0, 9.267, 6.927, 0, 9.667, -7.283, 0, 10.167, 7.149, 0, 10.533, -10.793, 0, 10.9, 9.142, 0, 11.2, -7.397, 0, 11.6, 5.854, 0, 12.033, -4.332, 0, 12.433, 2.828, 0, 12.933, -1.911, 0, 13.4, 3.26, 0, 13.767, -5.223, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param37", "Segments": [0, 0, 0, 0.233, 12.181, 0, 0.6, -28.81, 0, 0.867, 24.27, 0, 1.5, -7.944, 0, 1.8, 6.806, 0, 2.067, -9.607, 0, 2.367, 6, 0, 2.733, -3.728, 0, 3.1, 2.123, 0, 3.3, -7.792, 0, 3.6, 6.795, 0, 4.033, -0.842, 0, 4.633, 2.89, 0, 5.133, -3.002, 0, 5.433, 2.127, 0, 5.733, -2.328, 0, 6.1, 0.1, 0, 6.4, -0.479, 0, 6.667, -0.287, 0, 7.167, -4.78, 0, 7.4, 11.957, 0, 7.633, -9.542, 0, 7.867, 5.118, 0, 8.167, -3.981, 0, 8.267, -3.119, 0, 8.4, -3.502, 0, 9, 10.787, 0, 9.233, -14.137, 0, 9.433, 8.04, 0, 9.633, -16.289, 0, 9.933, 25.328, 0, 10.2, -18.782, 0, 10.533, 9.016, 0, 10.9, -10.118, 0, 11.333, 11.464, 0, 11.733, -4.458, 0, 12.1, 0.959, 0, 12.467, -0.305, 0, 13.1, 3.519, 0, 13.467, -2.022, 0, 13.833, 3.693, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param39", "Segments": [0, 0, 0, 0.167, -6.305, 0, 0.467, 15.784, 0, 0.767, -29.672, 0, 1.033, 24.171, 0, 1.3, -8.781, 0, 1.5, 1.071, 0, 1.667, -2.938, 0, 1.767, -1.252, 0, 1.8, -4.5, 0, 1.967, 10.152, 0, 2.233, -11.451, 0, 2.533, 8.748, 0, 2.833, -4.842, 0, 3.233, 7.164, 0, 3.467, -9.146, 0, 3.733, 6.888, 0, 4.067, -3.253, 0, 4.4, 1.077, 0, 4.633, -0.355, 0, 4.833, 1.623, 0, 5.1, -1.049, 0, 5.167, -0.967, 0, 5.367, -1.925, 0, 5.6, 3.209, 0, 5.9, -2.384, 0, 6.2, 1.322, 0, 6.5, -0.628, 0, 6.833, 0.316, 0, 7.033, -0.022, 0, 7.167, 2.371, 0, 7.333, -9.454, 0, 7.567, 14.649, 0, 7.8, -13.323, 0, 8.067, 9.913, 0, 8.3, -4.325, 0, 8.767, 0.481, 0, 8.967, -4.242, 0, 9.167, 14.335, 0, 9.367, -18.336, 0, 9.6, 16.948, 0, 9.8, -20.009, 0, 10.1, 28.515, 0, 10.367, -22.3, 0, 10.7, 15.351, 0, 11, -9.976, 0, 11.467, 9.578, 0, 11.8, -5.562, 0, 12.133, 2.722, 0, 12.5, -0.978, 0, 12.8, 0.372, 0, 13, -1.229, 0, 13.333, 2.141, 0, 13.633, -2.896, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param38", "Segments": [0, 0, 0, 0.233, 12.181, 0, 0.6, -28.81, 0, 0.867, 24.27, 0, 1.5, -7.944, 0, 1.8, 6.806, 0, 2.067, -9.607, 0, 2.367, 6, 0, 2.733, -3.728, 0, 3.1, 2.123, 0, 3.3, -7.792, 0, 3.6, 6.795, 0, 4.033, -0.842, 0, 4.633, 2.89, 0, 5.133, -3.002, 0, 5.433, 2.127, 0, 5.733, -2.328, 0, 6.1, 0.1, 0, 6.4, -0.479, 0, 6.667, -0.287, 0, 7.167, -4.78, 0, 7.4, 11.957, 0, 7.633, -9.542, 0, 7.867, 5.118, 0, 8.167, -3.981, 0, 8.267, -3.119, 0, 8.4, -3.502, 0, 9, 10.787, 0, 9.233, -14.137, 0, 9.433, 8.04, 0, 9.633, -16.289, 0, 9.933, 25.328, 0, 10.2, -18.782, 0, 10.533, 9.016, 0, 10.9, -10.118, 0, 11.333, 11.464, 0, 11.733, -4.458, 0, 12.1, 0.959, 0, 12.467, -0.305, 0, 13.1, 3.519, 0, 13.467, -2.022, 0, 13.833, 3.693, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param40", "Segments": [0, 0, 0, 0.167, -6.305, 0, 0.467, 15.784, 0, 0.767, -29.672, 0, 1.033, 24.171, 0, 1.3, -8.781, 0, 1.5, 1.071, 0, 1.667, -2.938, 0, 1.767, -1.252, 0, 1.8, -4.5, 0, 1.967, 10.152, 0, 2.233, -11.451, 0, 2.533, 8.748, 0, 2.833, -4.842, 0, 3.233, 7.164, 0, 3.467, -9.146, 0, 3.733, 6.888, 0, 4.067, -3.253, 0, 4.4, 1.077, 0, 4.633, -0.355, 0, 4.833, 1.623, 0, 5.1, -1.049, 0, 5.167, -0.967, 0, 5.367, -1.925, 0, 5.6, 3.209, 0, 5.9, -2.384, 0, 6.2, 1.322, 0, 6.5, -0.628, 0, 6.833, 0.316, 0, 7.033, -0.022, 0, 7.167, 2.371, 0, 7.333, -9.454, 0, 7.567, 14.649, 0, 7.8, -13.323, 0, 8.067, 9.913, 0, 8.3, -4.325, 0, 8.767, 0.481, 0, 8.967, -4.242, 0, 9.167, 14.335, 0, 9.367, -18.336, 0, 9.6, 16.948, 0, 9.8, -20.009, 0, 10.1, 28.515, 0, 10.367, -22.3, 0, 10.7, 15.351, 0, 11, -9.976, 0, 11.467, 9.578, 0, 11.8, -5.562, 0, 12.133, 2.722, 0, 12.5, -0.978, 0, 12.8, 0.372, 0, 13, -1.229, 0, 13.333, 2.141, 0, 13.633, -2.896, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param41", "Segments": [0, 0, 0, 0.233, 12.181, 0, 0.6, -28.81, 0, 0.867, 24.27, 0, 1.5, -7.944, 0, 1.8, 6.806, 0, 2.067, -9.607, 0, 2.367, 6, 0, 2.733, -3.728, 0, 3.1, 2.123, 0, 3.3, -7.792, 0, 3.6, 6.795, 0, 4.033, -0.842, 0, 4.633, 2.89, 0, 5.133, -3.002, 0, 5.433, 2.127, 0, 5.733, -2.328, 0, 6.1, 0.1, 0, 6.4, -0.479, 0, 6.667, -0.287, 0, 7.167, -4.78, 0, 7.4, 11.957, 0, 7.633, -9.542, 0, 7.867, 5.118, 0, 8.167, -3.981, 0, 8.267, -3.119, 0, 8.4, -3.502, 0, 9, 10.787, 0, 9.233, -14.137, 0, 9.433, 8.04, 0, 9.633, -16.289, 0, 9.933, 25.328, 0, 10.2, -18.782, 0, 10.533, 9.016, 0, 10.9, -10.118, 0, 11.333, 11.464, 0, 11.733, -4.458, 0, 12.1, 0.959, 0, 12.467, -0.305, 0, 13.1, 3.519, 0, 13.467, -2.022, 0, 13.833, 3.693, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param42", "Segments": [0, 0, 0, 0.167, -6.305, 0, 0.467, 15.784, 0, 0.767, -29.672, 0, 1.033, 24.171, 0, 1.3, -8.781, 0, 1.5, 1.071, 0, 1.667, -2.938, 0, 1.767, -1.252, 0, 1.8, -4.5, 0, 1.967, 10.152, 0, 2.233, -11.451, 0, 2.533, 8.748, 0, 2.833, -4.842, 0, 3.233, 7.164, 0, 3.467, -9.146, 0, 3.733, 6.888, 0, 4.067, -3.253, 0, 4.4, 1.077, 0, 4.633, -0.355, 0, 4.833, 1.623, 0, 5.1, -1.049, 0, 5.167, -0.967, 0, 5.367, -1.925, 0, 5.6, 3.209, 0, 5.9, -2.384, 0, 6.2, 1.322, 0, 6.5, -0.628, 0, 6.833, 0.316, 0, 7.033, -0.022, 0, 7.167, 2.371, 0, 7.333, -9.454, 0, 7.567, 14.649, 0, 7.8, -13.323, 0, 8.067, 9.913, 0, 8.3, -4.325, 0, 8.767, 0.481, 0, 8.967, -4.242, 0, 9.167, 14.335, 0, 9.367, -18.336, 0, 9.6, 16.948, 0, 9.8, -20.009, 0, 10.1, 28.515, 0, 10.367, -22.3, 0, 10.7, 15.351, 0, 11, -9.976, 0, 11.467, 9.578, 0, 11.8, -5.562, 0, 12.133, 2.722, 0, 12.5, -0.978, 0, 12.8, 0.372, 0, 13, -1.229, 0, 13.333, 2.141, 0, 13.633, -2.896, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param43", "Segments": [0, 0, 0, 0.233, 12.181, 0, 0.6, -28.81, 0, 0.867, 24.27, 0, 1.5, -7.944, 0, 1.8, 6.806, 0, 2.067, -9.607, 0, 2.367, 6, 0, 2.733, -3.728, 0, 3.1, 2.123, 0, 3.3, -7.792, 0, 3.6, 6.795, 0, 4.033, -0.842, 0, 4.633, 2.89, 0, 5.133, -3.002, 0, 5.433, 2.127, 0, 5.733, -2.328, 0, 6.1, 0.1, 0, 6.4, -0.479, 0, 6.667, -0.287, 0, 7.167, -4.78, 0, 7.4, 11.957, 0, 7.633, -9.542, 0, 7.867, 5.118, 0, 8.167, -3.981, 0, 8.267, -3.119, 0, 8.4, -3.502, 0, 9, 10.787, 0, 9.233, -14.137, 0, 9.433, 8.04, 0, 9.633, -16.289, 0, 9.933, 25.328, 0, 10.2, -18.782, 0, 10.533, 9.016, 0, 10.9, -10.118, 0, 11.333, 11.464, 0, 11.733, -4.458, 0, 12.1, 0.959, 0, 12.467, -0.305, 0, 13.1, 3.519, 0, 13.467, -2.022, 0, 13.833, 3.693, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param44", "Segments": [0, 0, 0, 0.167, -6.305, 0, 0.467, 15.784, 0, 0.767, -29.672, 0, 1.033, 24.171, 0, 1.3, -8.781, 0, 1.5, 1.071, 0, 1.667, -2.938, 0, 1.767, -1.252, 0, 1.8, -4.5, 0, 1.967, 10.152, 0, 2.233, -11.451, 0, 2.533, 8.748, 0, 2.833, -4.842, 0, 3.233, 7.164, 0, 3.467, -9.146, 0, 3.733, 6.888, 0, 4.067, -3.253, 0, 4.4, 1.077, 0, 4.633, -0.355, 0, 4.833, 1.623, 0, 5.1, -1.049, 0, 5.167, -0.967, 0, 5.367, -1.925, 0, 5.6, 3.209, 0, 5.9, -2.384, 0, 6.2, 1.322, 0, 6.5, -0.628, 0, 6.833, 0.316, 0, 7.033, -0.022, 0, 7.167, 2.371, 0, 7.333, -9.454, 0, 7.567, 14.649, 0, 7.8, -13.323, 0, 8.067, 9.913, 0, 8.3, -4.325, 0, 8.767, 0.481, 0, 8.967, -4.242, 0, 9.167, 14.335, 0, 9.367, -18.336, 0, 9.6, 16.948, 0, 9.8, -20.009, 0, 10.1, 28.515, 0, 10.367, -22.3, 0, 10.7, 15.351, 0, 11, -9.976, 0, 11.467, 9.578, 0, 11.8, -5.562, 0, 12.133, 2.722, 0, 12.5, -0.978, 0, 12.8, 0.372, 0, 13, -1.229, 0, 13.333, 2.141, 0, 13.633, -2.896, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param45", "Segments": [0, 0, 0, 0.233, 12.181, 0, 0.6, -28.81, 0, 0.867, 24.27, 0, 1.5, -7.944, 0, 1.8, 6.806, 0, 2.067, -9.607, 0, 2.367, 6, 0, 2.733, -3.728, 0, 3.1, 2.123, 0, 3.3, -7.792, 0, 3.6, 6.795, 0, 4.033, -0.842, 0, 4.633, 2.89, 0, 5.133, -3.002, 0, 5.433, 2.127, 0, 5.733, -2.328, 0, 6.1, 0.1, 0, 6.4, -0.479, 0, 6.667, -0.287, 0, 7.167, -4.78, 0, 7.4, 11.957, 0, 7.633, -9.542, 0, 7.867, 5.118, 0, 8.167, -3.981, 0, 8.267, -3.119, 0, 8.4, -3.502, 0, 9, 10.787, 0, 9.233, -14.137, 0, 9.433, 8.04, 0, 9.633, -16.289, 0, 9.933, 25.328, 0, 10.2, -18.782, 0, 10.533, 9.016, 0, 10.9, -10.118, 0, 11.333, 11.464, 0, 11.733, -4.458, 0, 12.1, 0.959, 0, 12.467, -0.305, 0, 13.1, 3.519, 0, 13.467, -2.022, 0, 13.833, 3.693, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param46", "Segments": [0, 0, 0, 0.167, -6.305, 0, 0.467, 15.784, 0, 0.767, -29.672, 0, 1.033, 24.171, 0, 1.3, -8.781, 0, 1.5, 1.071, 0, 1.667, -2.938, 0, 1.767, -1.252, 0, 1.8, -4.5, 0, 1.967, 10.152, 0, 2.233, -11.451, 0, 2.533, 8.748, 0, 2.833, -4.842, 0, 3.233, 7.164, 0, 3.467, -9.146, 0, 3.733, 6.888, 0, 4.067, -3.253, 0, 4.4, 1.077, 0, 4.633, -0.355, 0, 4.833, 1.623, 0, 5.1, -1.049, 0, 5.167, -0.967, 0, 5.367, -1.925, 0, 5.6, 3.209, 0, 5.9, -2.384, 0, 6.2, 1.322, 0, 6.5, -0.628, 0, 6.833, 0.316, 0, 7.033, -0.022, 0, 7.167, 2.371, 0, 7.333, -9.454, 0, 7.567, 14.649, 0, 7.8, -13.323, 0, 8.067, 9.913, 0, 8.3, -4.325, 0, 8.767, 0.481, 0, 8.967, -4.242, 0, 9.167, 14.335, 0, 9.367, -18.336, 0, 9.6, 16.948, 0, 9.8, -20.009, 0, 10.1, 28.515, 0, 10.367, -22.3, 0, 10.7, 15.351, 0, 11, -9.976, 0, 11.467, 9.578, 0, 11.8, -5.562, 0, 12.133, 2.722, 0, 12.5, -0.978, 0, 12.8, 0.372, 0, 13, -1.229, 0, 13.333, 2.141, 0, 13.633, -2.896, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param62", "Segments": [0, 0, 0, 0.233, 12.181, 0, 0.6, -28.81, 0, 0.867, 24.27, 0, 1.5, -7.944, 0, 1.8, 6.806, 0, 2.067, -9.607, 0, 2.367, 6, 0, 2.733, -3.728, 0, 3.1, 2.123, 0, 3.3, -7.792, 0, 3.6, 6.795, 0, 4.033, -0.842, 0, 4.633, 2.89, 0, 5.133, -3.002, 0, 5.433, 2.127, 0, 5.733, -2.328, 0, 6.1, 0.1, 0, 6.4, -0.479, 0, 6.667, -0.287, 0, 7.167, -4.78, 0, 7.4, 11.957, 0, 7.633, -9.542, 0, 7.867, 5.118, 0, 8.167, -3.981, 0, 8.267, -3.119, 0, 8.4, -3.502, 0, 9, 10.787, 0, 9.233, -14.137, 0, 9.433, 8.04, 0, 9.633, -16.289, 0, 9.933, 25.328, 0, 10.2, -18.782, 0, 10.533, 9.016, 0, 10.9, -10.118, 0, 11.333, 11.464, 0, 11.733, -4.458, 0, 12.1, 0.959, 0, 12.467, -0.305, 0, 13.1, 3.519, 0, 13.467, -2.022, 0, 13.833, 3.693, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param63", "Segments": [0, 0, 0, 0.167, -6.305, 0, 0.467, 15.784, 0, 0.767, -29.672, 0, 1.033, 24.171, 0, 1.3, -8.781, 0, 1.5, 1.071, 0, 1.667, -2.938, 0, 1.767, -1.252, 0, 1.8, -4.5, 0, 1.967, 10.152, 0, 2.233, -11.451, 0, 2.533, 8.748, 0, 2.833, -4.842, 0, 3.233, 7.164, 0, 3.467, -9.146, 0, 3.733, 6.888, 0, 4.067, -3.253, 0, 4.4, 1.077, 0, 4.633, -0.355, 0, 4.833, 1.623, 0, 5.1, -1.049, 0, 5.167, -0.967, 0, 5.367, -1.925, 0, 5.6, 3.209, 0, 5.9, -2.384, 0, 6.2, 1.322, 0, 6.5, -0.628, 0, 6.833, 0.316, 0, 7.033, -0.022, 0, 7.167, 2.371, 0, 7.333, -9.454, 0, 7.567, 14.649, 0, 7.8, -13.323, 0, 8.067, 9.913, 0, 8.3, -4.325, 0, 8.767, 0.481, 0, 8.967, -4.242, 0, 9.167, 14.335, 0, 9.367, -18.336, 0, 9.6, 16.948, 0, 9.8, -20.009, 0, 10.1, 28.515, 0, 10.367, -22.3, 0, 10.7, 15.351, 0, 11, -9.976, 0, 11.467, 9.578, 0, 11.8, -5.562, 0, 12.133, 2.722, 0, 12.5, -0.978, 0, 12.8, 0.372, 0, 13, -1.229, 0, 13.333, 2.141, 0, 13.633, -2.896, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param64", "Segments": [0, 0, 0, 0.233, 12.181, 0, 0.6, -28.81, 0, 0.867, 24.27, 0, 1.5, -7.944, 0, 1.8, 6.806, 0, 2.067, -9.607, 0, 2.367, 6, 0, 2.733, -3.728, 0, 3.1, 2.123, 0, 3.3, -7.792, 0, 3.6, 6.795, 0, 4.033, -0.842, 0, 4.633, 2.89, 0, 5.133, -3.002, 0, 5.433, 2.127, 0, 5.733, -2.328, 0, 6.1, 0.1, 0, 6.4, -0.479, 0, 6.667, -0.287, 0, 7.167, -4.78, 0, 7.4, 11.957, 0, 7.633, -9.542, 0, 7.867, 5.118, 0, 8.167, -3.981, 0, 8.267, -3.119, 0, 8.4, -3.502, 0, 9, 10.787, 0, 9.233, -14.137, 0, 9.433, 8.04, 0, 9.633, -16.289, 0, 9.933, 25.328, 0, 10.2, -18.782, 0, 10.533, 9.016, 0, 10.9, -10.118, 0, 11.333, 11.464, 0, 11.733, -4.458, 0, 12.1, 0.959, 0, 12.467, -0.305, 0, 13.1, 3.519, 0, 13.467, -2.022, 0, 13.833, 3.693, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param65", "Segments": [0, 0, 0, 0.167, -6.305, 0, 0.467, 15.784, 0, 0.767, -29.672, 0, 1.033, 24.171, 0, 1.3, -8.781, 0, 1.5, 1.071, 0, 1.667, -2.938, 0, 1.767, -1.252, 0, 1.8, -4.5, 0, 1.967, 10.152, 0, 2.233, -11.451, 0, 2.533, 8.748, 0, 2.833, -4.842, 0, 3.233, 7.164, 0, 3.467, -9.146, 0, 3.733, 6.888, 0, 4.067, -3.253, 0, 4.4, 1.077, 0, 4.633, -0.355, 0, 4.833, 1.623, 0, 5.1, -1.049, 0, 5.167, -0.967, 0, 5.367, -1.925, 0, 5.6, 3.209, 0, 5.9, -2.384, 0, 6.2, 1.322, 0, 6.5, -0.628, 0, 6.833, 0.316, 0, 7.033, -0.022, 0, 7.167, 2.371, 0, 7.333, -9.454, 0, 7.567, 14.649, 0, 7.8, -13.323, 0, 8.067, 9.913, 0, 8.3, -4.325, 0, 8.767, 0.481, 0, 8.967, -4.242, 0, 9.167, 14.335, 0, 9.367, -18.336, 0, 9.6, 16.948, 0, 9.8, -20.009, 0, 10.1, 28.515, 0, 10.367, -22.3, 0, 10.7, 15.351, 0, 11, -9.976, 0, 11.467, 9.578, 0, 11.8, -5.562, 0, 12.133, 2.722, 0, 12.5, -0.978, 0, 12.8, 0.372, 0, 13, -1.229, 0, 13.333, 2.141, 0, 13.633, -2.896, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param72", "Segments": [0, 0, 0, 0.233, 12.181, 0, 0.6, -28.81, 0, 0.867, 24.27, 0, 1.5, -7.944, 0, 1.8, 6.806, 0, 2.067, -9.607, 0, 2.367, 6, 0, 2.733, -3.728, 0, 3.1, 2.123, 0, 3.3, -7.792, 0, 3.6, 6.795, 0, 4.033, -0.842, 0, 4.633, 2.89, 0, 5.133, -3.002, 0, 5.433, 2.127, 0, 5.733, -2.328, 0, 6.1, 0.1, 0, 6.4, -0.479, 0, 6.667, -0.287, 0, 7.167, -4.78, 0, 7.4, 11.957, 0, 7.633, -9.542, 0, 7.867, 5.118, 0, 8.167, -3.981, 0, 8.267, -3.119, 0, 8.4, -3.502, 0, 9, 10.787, 0, 9.233, -14.137, 0, 9.433, 8.04, 0, 9.633, -16.289, 0, 9.933, 25.328, 0, 10.2, -18.782, 0, 10.533, 9.016, 0, 10.9, -10.118, 0, 11.333, 11.464, 0, 11.733, -4.458, 0, 12.1, 0.959, 0, 12.467, -0.305, 0, 13.1, 3.519, 0, 13.467, -2.022, 0, 13.833, 3.693, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param73", "Segments": [0, 0, 0, 0.167, -6.305, 0, 0.467, 15.784, 0, 0.767, -29.672, 0, 1.033, 24.171, 0, 1.3, -8.781, 0, 1.5, 1.071, 0, 1.667, -2.938, 0, 1.767, -1.252, 0, 1.8, -4.5, 0, 1.967, 10.152, 0, 2.233, -11.451, 0, 2.533, 8.748, 0, 2.833, -4.842, 0, 3.233, 7.164, 0, 3.467, -9.146, 0, 3.733, 6.888, 0, 4.067, -3.253, 0, 4.4, 1.077, 0, 4.633, -0.355, 0, 4.833, 1.623, 0, 5.1, -1.049, 0, 5.167, -0.967, 0, 5.367, -1.925, 0, 5.6, 3.209, 0, 5.9, -2.384, 0, 6.2, 1.322, 0, 6.5, -0.628, 0, 6.833, 0.316, 0, 7.033, -0.022, 0, 7.167, 2.371, 0, 7.333, -9.454, 0, 7.567, 14.649, 0, 7.8, -13.323, 0, 8.067, 9.913, 0, 8.3, -4.325, 0, 8.767, 0.481, 0, 8.967, -4.242, 0, 9.167, 14.335, 0, 9.367, -18.336, 0, 9.6, 16.948, 0, 9.8, -20.009, 0, 10.1, 28.515, 0, 10.367, -22.3, 0, 10.7, 15.351, 0, 11, -9.976, 0, 11.467, 9.578, 0, 11.8, -5.562, 0, 12.133, 2.722, 0, 12.5, -0.978, 0, 12.8, 0.372, 0, 13, -1.229, 0, 13.333, 2.141, 0, 13.633, -2.896, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param74", "Segments": [0, 0, 0, 0.233, 12.181, 0, 0.6, -28.81, 0, 0.867, 24.27, 0, 1.5, -7.944, 0, 1.8, 6.806, 0, 2.067, -9.607, 0, 2.367, 6, 0, 2.733, -3.728, 0, 3.1, 2.123, 0, 3.3, -7.792, 0, 3.6, 6.795, 0, 4.033, -0.842, 0, 4.633, 2.89, 0, 5.133, -3.002, 0, 5.433, 2.127, 0, 5.733, -2.328, 0, 6.1, 0.1, 0, 6.4, -0.479, 0, 6.667, -0.287, 0, 7.167, -4.78, 0, 7.4, 11.957, 0, 7.633, -9.542, 0, 7.867, 5.118, 0, 8.167, -3.981, 0, 8.267, -3.119, 0, 8.4, -3.502, 0, 9, 10.787, 0, 9.233, -14.137, 0, 9.433, 8.04, 0, 9.633, -16.289, 0, 9.933, 25.328, 0, 10.2, -18.782, 0, 10.533, 9.016, 0, 10.9, -10.118, 0, 11.333, 11.464, 0, 11.733, -4.458, 0, 12.1, 0.959, 0, 12.467, -0.305, 0, 13.1, 3.519, 0, 13.467, -2.022, 0, 13.833, 3.693, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param75", "Segments": [0, 0, 0, 0.167, -6.305, 0, 0.467, 15.784, 0, 0.767, -29.672, 0, 1.033, 24.171, 0, 1.3, -8.781, 0, 1.5, 1.071, 0, 1.667, -2.938, 0, 1.767, -1.252, 0, 1.8, -4.5, 0, 1.967, 10.152, 0, 2.233, -11.451, 0, 2.533, 8.748, 0, 2.833, -4.842, 0, 3.233, 7.164, 0, 3.467, -9.146, 0, 3.733, 6.888, 0, 4.067, -3.253, 0, 4.4, 1.077, 0, 4.633, -0.355, 0, 4.833, 1.623, 0, 5.1, -1.049, 0, 5.167, -0.967, 0, 5.367, -1.925, 0, 5.6, 3.209, 0, 5.9, -2.384, 0, 6.2, 1.322, 0, 6.5, -0.628, 0, 6.833, 0.316, 0, 7.033, -0.022, 0, 7.167, 2.371, 0, 7.333, -9.454, 0, 7.567, 14.649, 0, 7.8, -13.323, 0, 8.067, 9.913, 0, 8.3, -4.325, 0, 8.767, 0.481, 0, 8.967, -4.242, 0, 9.167, 14.335, 0, 9.367, -18.336, 0, 9.6, 16.948, 0, 9.8, -20.009, 0, 10.1, 28.515, 0, 10.367, -22.3, 0, 10.7, 15.351, 0, 11, -9.976, 0, 11.467, 9.578, 0, 11.8, -5.562, 0, 12.133, 2.722, 0, 12.5, -0.978, 0, 12.8, 0.372, 0, 13, -1.229, 0, 13.333, 2.141, 0, 13.633, -2.896, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param53", "Segments": [0, 0, 0, 0.233, 12.181, 0, 0.6, -28.81, 0, 0.867, 24.27, 0, 1.5, -7.944, 0, 1.8, 6.806, 0, 2.067, -9.607, 0, 2.367, 6, 0, 2.733, -3.728, 0, 3.1, 2.123, 0, 3.3, -7.792, 0, 3.6, 6.795, 0, 4.033, -0.842, 0, 4.633, 2.89, 0, 5.133, -3.002, 0, 5.433, 2.127, 0, 5.733, -2.328, 0, 6.1, 0.1, 0, 6.4, -0.479, 0, 6.667, -0.287, 0, 7.167, -4.78, 0, 7.4, 11.957, 0, 7.633, -9.542, 0, 7.867, 5.118, 0, 8.167, -3.981, 0, 8.267, -3.119, 0, 8.4, -3.502, 0, 9, 10.787, 0, 9.233, -14.137, 0, 9.433, 8.04, 0, 9.633, -16.289, 0, 9.933, 25.328, 0, 10.2, -18.782, 0, 10.533, 9.016, 0, 10.9, -10.118, 0, 11.333, 11.464, 0, 11.733, -4.458, 0, 12.1, 0.959, 0, 12.467, -0.305, 0, 13.1, 3.519, 0, 13.467, -2.022, 0, 13.833, 3.693, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param54", "Segments": [0, 0, 0, 0.167, -6.305, 0, 0.467, 15.784, 0, 0.767, -29.672, 0, 1.033, 24.171, 0, 1.3, -8.781, 0, 1.5, 1.071, 0, 1.667, -2.938, 0, 1.767, -1.252, 0, 1.8, -4.5, 0, 1.967, 10.152, 0, 2.233, -11.451, 0, 2.533, 8.748, 0, 2.833, -4.842, 0, 3.233, 7.164, 0, 3.467, -9.146, 0, 3.733, 6.888, 0, 4.067, -3.253, 0, 4.4, 1.077, 0, 4.633, -0.355, 0, 4.833, 1.623, 0, 5.1, -1.049, 0, 5.167, -0.967, 0, 5.367, -1.925, 0, 5.6, 3.209, 0, 5.9, -2.384, 0, 6.2, 1.322, 0, 6.5, -0.628, 0, 6.833, 0.316, 0, 7.033, -0.022, 0, 7.167, 2.371, 0, 7.333, -9.454, 0, 7.567, 14.649, 0, 7.8, -13.323, 0, 8.067, 9.913, 0, 8.3, -4.325, 0, 8.767, 0.481, 0, 8.967, -4.242, 0, 9.167, 14.335, 0, 9.367, -18.336, 0, 9.6, 16.948, 0, 9.8, -20.009, 0, 10.1, 28.515, 0, 10.367, -22.3, 0, 10.7, 15.351, 0, 11, -9.976, 0, 11.467, 9.578, 0, 11.8, -5.562, 0, 12.133, 2.722, 0, 12.5, -0.978, 0, 12.8, 0.372, 0, 13, -1.229, 0, 13.333, 2.141, 0, 13.633, -2.896, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param81", "Segments": [0, 0, 0, 0.233, 12.181, 0, 0.6, -28.81, 0, 0.867, 24.27, 0, 1.5, -7.944, 0, 1.8, 6.806, 0, 2.067, -9.607, 0, 2.367, 6, 0, 2.733, -3.728, 0, 3.1, 2.123, 0, 3.3, -7.792, 0, 3.6, 6.795, 0, 4.033, -0.842, 0, 4.633, 2.89, 0, 5.133, -3.002, 0, 5.433, 2.127, 0, 5.733, -2.328, 0, 6.1, 0.1, 0, 6.4, -0.479, 0, 6.667, -0.287, 0, 7.167, -4.78, 0, 7.4, 11.957, 0, 7.633, -9.542, 0, 7.867, 5.118, 0, 8.167, -3.981, 0, 8.267, -3.119, 0, 8.4, -3.502, 0, 9, 10.787, 0, 9.233, -14.137, 0, 9.433, 8.04, 0, 9.633, -16.289, 0, 9.933, 25.328, 0, 10.2, -18.782, 0, 10.533, 9.016, 0, 10.9, -10.118, 0, 11.333, 11.464, 0, 11.733, -4.458, 0, 12.1, 0.959, 0, 12.467, -0.305, 0, 13.1, 3.519, 0, 13.467, -2.022, 0, 13.833, 3.693, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param82", "Segments": [0, 0, 0, 0.167, -6.305, 0, 0.467, 15.784, 0, 0.767, -29.672, 0, 1.033, 24.171, 0, 1.3, -8.781, 0, 1.5, 1.071, 0, 1.667, -2.938, 0, 1.767, -1.252, 0, 1.8, -4.5, 0, 1.967, 10.152, 0, 2.233, -11.451, 0, 2.533, 8.748, 0, 2.833, -4.842, 0, 3.233, 7.164, 0, 3.467, -9.146, 0, 3.733, 6.888, 0, 4.067, -3.253, 0, 4.4, 1.077, 0, 4.633, -0.355, 0, 4.833, 1.623, 0, 5.1, -1.049, 0, 5.167, -0.967, 0, 5.367, -1.925, 0, 5.6, 3.209, 0, 5.9, -2.384, 0, 6.2, 1.322, 0, 6.5, -0.628, 0, 6.833, 0.316, 0, 7.033, -0.022, 0, 7.167, 2.371, 0, 7.333, -9.454, 0, 7.567, 14.649, 0, 7.8, -13.323, 0, 8.067, 9.913, 0, 8.3, -4.325, 0, 8.767, 0.481, 0, 8.967, -4.242, 0, 9.167, 14.335, 0, 9.367, -18.336, 0, 9.6, 16.948, 0, 9.8, -20.009, 0, 10.1, 28.515, 0, 10.367, -22.3, 0, 10.7, 15.351, 0, 11, -9.976, 0, 11.467, 9.578, 0, 11.8, -5.562, 0, 12.133, 2.722, 0, 12.5, -0.978, 0, 12.8, 0.372, 0, 13, -1.229, 0, 13.333, 2.141, 0, 13.633, -2.896, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param70", "Segments": [0, 0, 0, 0.233, 12.181, 0, 0.6, -28.81, 0, 0.867, 24.27, 0, 1.5, -7.944, 0, 1.8, 6.806, 0, 2.067, -9.607, 0, 2.367, 6, 0, 2.733, -3.728, 0, 3.1, 2.123, 0, 3.3, -7.792, 0, 3.6, 6.795, 0, 4.033, -0.842, 0, 4.633, 2.89, 0, 5.133, -3.002, 0, 5.433, 2.127, 0, 5.733, -2.328, 0, 6.1, 0.1, 0, 6.4, -0.479, 0, 6.667, -0.287, 0, 7.167, -4.78, 0, 7.4, 11.957, 0, 7.633, -9.542, 0, 7.867, 5.118, 0, 8.167, -3.981, 0, 8.267, -3.119, 0, 8.4, -3.502, 0, 9, 10.787, 0, 9.233, -14.137, 0, 9.433, 8.04, 0, 9.633, -16.289, 0, 9.933, 25.328, 0, 10.2, -18.782, 0, 10.533, 9.016, 0, 10.9, -10.118, 0, 11.333, 11.464, 0, 11.733, -4.458, 0, 12.1, 0.959, 0, 12.467, -0.305, 0, 13.1, 3.519, 0, 13.467, -2.022, 0, 13.833, 3.693, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param71", "Segments": [0, 0, 0, 0.167, -6.305, 0, 0.467, 15.784, 0, 0.767, -29.672, 0, 1.033, 24.171, 0, 1.3, -8.781, 0, 1.5, 1.071, 0, 1.667, -2.938, 0, 1.767, -1.252, 0, 1.8, -4.5, 0, 1.967, 10.152, 0, 2.233, -11.451, 0, 2.533, 8.748, 0, 2.833, -4.842, 0, 3.233, 7.164, 0, 3.467, -9.146, 0, 3.733, 6.888, 0, 4.067, -3.253, 0, 4.4, 1.077, 0, 4.633, -0.355, 0, 4.833, 1.623, 0, 5.1, -1.049, 0, 5.167, -0.967, 0, 5.367, -1.925, 0, 5.6, 3.209, 0, 5.9, -2.384, 0, 6.2, 1.322, 0, 6.5, -0.628, 0, 6.833, 0.316, 0, 7.033, -0.022, 0, 7.167, 2.371, 0, 7.333, -9.454, 0, 7.567, 14.649, 0, 7.8, -13.323, 0, 8.067, 9.913, 0, 8.3, -4.325, 0, 8.767, 0.481, 0, 8.967, -4.242, 0, 9.167, 14.335, 0, 9.367, -18.336, 0, 9.6, 16.948, 0, 9.8, -20.009, 0, 10.1, 28.515, 0, 10.367, -22.3, 0, 10.7, 15.351, 0, 11, -9.976, 0, 11.467, 9.578, 0, 11.8, -5.562, 0, 12.133, 2.722, 0, 12.5, -0.978, 0, 12.8, 0.372, 0, 13, -1.229, 0, 13.333, 2.141, 0, 13.633, -2.896, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param77", "Segments": [0, 0, 0, 0.233, 12.181, 0, 0.6, -28.81, 0, 0.867, 24.27, 0, 1.5, -7.944, 0, 1.8, 6.806, 0, 2.067, -9.607, 0, 2.367, 6, 0, 2.733, -3.728, 0, 3.1, 2.123, 0, 3.3, -7.792, 0, 3.6, 6.795, 0, 4.033, -0.842, 0, 4.633, 2.89, 0, 5.133, -3.002, 0, 5.433, 2.127, 0, 5.733, -2.328, 0, 6.1, 0.1, 0, 6.4, -0.479, 0, 6.667, -0.287, 0, 7.167, -4.78, 0, 7.4, 11.957, 0, 7.633, -9.542, 0, 7.867, 5.118, 0, 8.167, -3.981, 0, 8.267, -3.119, 0, 8.4, -3.502, 0, 9, 10.787, 0, 9.233, -14.137, 0, 9.433, 8.04, 0, 9.633, -16.289, 0, 9.933, 25.328, 0, 10.2, -18.782, 0, 10.533, 9.016, 0, 10.9, -10.118, 0, 11.333, 11.464, 0, 11.733, -4.458, 0, 12.1, 0.959, 0, 12.467, -0.305, 0, 13.1, 3.519, 0, 13.467, -2.022, 0, 13.833, 3.693, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param78", "Segments": [0, 0, 0, 0.167, -6.305, 0, 0.467, 15.784, 0, 0.767, -29.672, 0, 1.033, 24.171, 0, 1.3, -8.781, 0, 1.5, 1.071, 0, 1.667, -2.938, 0, 1.767, -1.252, 0, 1.8, -4.5, 0, 1.967, 10.152, 0, 2.233, -11.451, 0, 2.533, 8.748, 0, 2.833, -4.842, 0, 3.233, 7.164, 0, 3.467, -9.146, 0, 3.733, 6.888, 0, 4.067, -3.253, 0, 4.4, 1.077, 0, 4.633, -0.355, 0, 4.833, 1.623, 0, 5.1, -1.049, 0, 5.167, -0.967, 0, 5.367, -1.925, 0, 5.6, 3.209, 0, 5.9, -2.384, 0, 6.2, 1.322, 0, 6.5, -0.628, 0, 6.833, 0.316, 0, 7.033, -0.022, 0, 7.167, 2.371, 0, 7.333, -9.454, 0, 7.567, 14.649, 0, 7.8, -13.323, 0, 8.067, 9.913, 0, 8.3, -4.325, 0, 8.767, 0.481, 0, 8.967, -4.242, 0, 9.167, 14.335, 0, 9.367, -18.336, 0, 9.6, 16.948, 0, 9.8, -20.009, 0, 10.1, 28.515, 0, 10.367, -22.3, 0, 10.7, 15.351, 0, 11, -9.976, 0, 11.467, 9.578, 0, 11.8, -5.562, 0, 12.133, 2.722, 0, 12.5, -0.978, 0, 12.8, 0.372, 0, 13, -1.229, 0, 13.333, 2.141, 0, 13.633, -2.896, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param79", "Segments": [0, 0, 0, 0.233, 12.181, 0, 0.6, -28.81, 0, 0.867, 24.27, 0, 1.5, -7.944, 0, 1.8, 6.806, 0, 2.067, -9.607, 0, 2.367, 6, 0, 2.733, -3.728, 0, 3.1, 2.123, 0, 3.3, -7.792, 0, 3.6, 6.795, 0, 4.033, -0.842, 0, 4.633, 2.89, 0, 5.133, -3.002, 0, 5.433, 2.127, 0, 5.733, -2.328, 0, 6.1, 0.1, 0, 6.4, -0.479, 0, 6.667, -0.287, 0, 7.167, -4.78, 0, 7.4, 11.957, 0, 7.633, -9.542, 0, 7.867, 5.118, 0, 8.167, -3.981, 0, 8.267, -3.119, 0, 8.4, -3.502, 0, 9, 10.787, 0, 9.233, -14.137, 0, 9.433, 8.04, 0, 9.633, -16.289, 0, 9.933, 25.328, 0, 10.2, -18.782, 0, 10.533, 9.016, 0, 10.9, -10.118, 0, 11.333, 11.464, 0, 11.733, -4.458, 0, 12.1, 0.959, 0, 12.467, -0.305, 0, 13.1, 3.519, 0, 13.467, -2.022, 0, 13.833, 3.693, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param80", "Segments": [0, 0, 0, 0.167, -6.305, 0, 0.467, 15.784, 0, 0.767, -29.672, 0, 1.033, 24.171, 0, 1.3, -8.781, 0, 1.5, 1.071, 0, 1.667, -2.938, 0, 1.767, -1.252, 0, 1.8, -4.5, 0, 1.967, 10.152, 0, 2.233, -11.451, 0, 2.533, 8.748, 0, 2.833, -4.842, 0, 3.233, 7.164, 0, 3.467, -9.146, 0, 3.733, 6.888, 0, 4.067, -3.253, 0, 4.4, 1.077, 0, 4.633, -0.355, 0, 4.833, 1.623, 0, 5.1, -1.049, 0, 5.167, -0.967, 0, 5.367, -1.925, 0, 5.6, 3.209, 0, 5.9, -2.384, 0, 6.2, 1.322, 0, 6.5, -0.628, 0, 6.833, 0.316, 0, 7.033, -0.022, 0, 7.167, 2.371, 0, 7.333, -9.454, 0, 7.567, 14.649, 0, 7.8, -13.323, 0, 8.067, 9.913, 0, 8.3, -4.325, 0, 8.767, 0.481, 0, 8.967, -4.242, 0, 9.167, 14.335, 0, 9.367, -18.336, 0, 9.6, 16.948, 0, 9.8, -20.009, 0, 10.1, 28.515, 0, 10.367, -22.3, 0, 10.7, 15.351, 0, 11, -9.976, 0, 11.467, 9.578, 0, 11.8, -5.562, 0, 12.133, 2.722, 0, 12.5, -0.978, 0, 12.8, 0.372, 0, 13, -1.229, 0, 13.333, 2.141, 0, 13.633, -2.896, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param294", "Segments": [0, 0, 2, 13.867, 0, 0, 14, 1]}, {"Target": "Parameter", "Id": "Param47", "Segments": [0, 0, 2, 0.033, 0, 0, 0.2, 8.687, 0, 0.5, -12.84, 0, 0.967, 10.203, 0, 1.467, -6.194, 0, 1.767, 2.077, 0, 2.067, -0.972, 0, 2.367, -0.097, 0, 2.733, -0.695, 0, 2.767, -0.694, 0, 2.833, -0.711, 0, 2.867, -0.707, 0, 3.3, -1.937, 0, 4, 3.516, 0, 4.367, 1.44, 0, 4.467, 1.486, 0, 4.9, -12.291, 0, 5.4, 6.965, 0, 5.7, -2.996, 0, 6, 0.373, 0, 6.3, -0.862, 0, 6.667, -0.394, 0, 6.833, -0.46, 0, 7.233, 14.886, 0, 7.5, -22.34, 0, 7.8, 18.104, 0, 8.067, -9.305, 0, 8.367, 5.087, 0, 8.8, -9.281, 0, 9, 5.871, 0, 9.167, -11.895, 0, 9.4, 27.677, 0, 9.633, -30, 0, 9.933, 29.707, 0, 10.233, -12.627, 0, 10.667, -1.358, 0, 10.933, -8.417, 0, 11.267, 8.158, 0, 11.6, -2.485, 0, 11.967, 0.529, 0, 12.267, -0.282, 0, 12.6, 0.103, 0, 12.8, 0.042, 0, 13.133, 3.934, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param48", "Segments": [0, 0, 2, 0.033, 0, 0, 0.167, -4.727, 0, 0.4, 11.005, 0, 0.667, -11.455, 0, 1.067, 5.498, 0, 1.367, -0.172, 0, 1.4, -0.161, 0, 1.6, -4.816, 0, 1.9, 3.915, 0, 2.2, -2.143, 0, 2.5, 1.056, 0, 2.8, -0.436, 0, 3.167, 0.556, 0, 3.467, -1.586, 0, 3.767, 0.966, 0, 4, -0.606, 0, 4.2, 0.878, 0, 4.467, -0.572, 0, 4.767, 5.294, 0, 5.033, -6.77, 0, 5.533, 5.642, 0, 5.833, -4.568, 0, 6.133, 2.534, 0, 6.433, -1.225, 0, 6.733, 0.53, 0, 7.167, -7.445, 0, 7.4, 18.861, 0, 7.667, -27.551, 0, 7.967, 22.696, 0, 8.233, -15.865, 0, 8.533, 9.891, 0, 8.933, -10.75, 0, 9.167, 14.748, 0, 9.333, -17.736, 0, 9.5, 30, 2, 9.533, 30, 0, 9.733, -30, 2, 9.8, -30, 0, 10.067, 30, 2, 10.1, 30, 0, 10.367, -14.934, 0, 10.767, 6.373, 0, 11.133, -7.85, 0, 11.4, 8.887, 0, 11.7, -4.958, 0, 12.067, 2.274, 0, 12.367, -1.042, 0, 12.7, 0.44, 0, 12.967, -1.503, 0, 13.267, 1.471, 0, 13.567, -0.551, 0, 13.867, 0.695, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param56", "Segments": [0, 0, 2, 0.033, 0, 0, 0.233, 3.894, 0, 0.567, -5.887, 0, 1.1, 7.218, 0, 1.567, -4.61, 0, 2.167, 1.802, 0, 2.667, -1.288, 0, 3.067, -0.142, 0, 3.333, -0.645, 0, 4, 2.337, 0, 4.933, -6.632, 0, 5.467, 5.226, 0, 6.033, -2.241, 0, 6.567, 0.818, 0, 7.033, -0.635, 0, 7.267, 6.629, 0, 7.533, -8.915, 0, 7.867, 5.552, 0, 8.133, 0.149, 0, 8.3, 0.41, 0, 8.8, -4.121, 0, 9.033, 1.602, 0, 9.167, -3.852, 0, 9.4, 13.056, 0, 9.667, -11.685, 0, 9.967, 11.785, 0, 10.533, -7.555, 0, 11.267, 4.772, 0, 11.8, -1.959, 0, 12.367, 0.873, 0, 12.8, -0.345, 0, 13.267, 2.332, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param57", "Segments": [0, 0, 2, 0.033, 0, 0, 0.167, -2.343, 0, 0.4, 6.109, 0, 0.767, -6.058, 0, 1.333, 5.315, 0, 1.733, -3.331, 0, 2.4, 1.155, 0, 2.833, -0.889, 0, 3.233, 0.228, 0, 3.533, -1.048, 0, 4.233, 1.565, 0, 4.6, 0.281, 0, 4.767, 2.863, 0, 5.167, -5.334, 0, 5.633, 3.885, 0, 6.267, -1.182, 0, 6.733, 0.652, 0, 7.167, -4.301, 0, 7.433, 10.847, 0, 7.7, -12.659, 0, 8, 8.672, 0, 8.3, -2.757, 0, 8.567, 2.294, 0, 8.967, -5.418, 0, 9.167, 5.143, 0, 9.333, -9.405, 0, 9.567, 17.577, 0, 9.833, -18.751, 0, 10.133, 16.111, 0, 10.533, -3.256, 0, 10.567, -3.215, 0, 10.667, -3.519, 0, 10.9, -1.479, 0, 11.167, -3.675, 0, 11.467, 3.985, 0, 11.867, -1.253, 0, 12.6, 0.56, 0, 13, -1.047, 0, 13.4, 1.464, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param58", "Segments": [0, 0, 2, 0.067, 0, 0, 0.267, -2.65, 0, 0.533, 7.548, 0, 0.867, -9.001, 0, 1.267, 5.976, 0, 1.767, -5.307, 0, 2.133, 2.207, 0, 2.433, 0.412, 0, 2.533, 0.48, 0, 2.9, -1.033, 0, 3.3, 0.903, 0, 3.633, -1.437, 0, 3.967, 0.511, 0, 4.1, 0.227, 0, 4.333, 0.856, 0, 4.633, -0.581, 0, 4.9, 3.684, 0, 5.233, -6.032, 0, 5.667, 5.767, 0, 6.033, -3.044, 0, 6.367, 0.142, 0, 6.5, -0.123, 0, 6.8, 0.947, 0, 7.267, -5.154, 0, 7.533, 12.712, 0, 7.833, -16.265, 0, 8.133, 13.951, 0, 8.433, -8.305, 0, 8.733, 5.037, 0, 9.067, -6.587, 0, 9.267, 5.462, 0, 9.467, -12.541, 0, 9.667, 17.814, 0, 9.933, -21.738, 0, 10.267, 20.592, 0, 10.567, -10.255, 0, 10.933, 3.128, 0, 11.267, -4.186, 0, 11.567, 5.937, 0, 11.9, -3.776, 0, 12.267, 1.041, 0, 12.5, 0.068, 0, 12.733, 0.425, 0, 13.1, -1.573, 0, 13.467, 1.497, 0, 13.8, -0.128, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param59", "Segments": [0, 0, 2, 0.033, 0, 0, 0.233, 3.894, 0, 0.567, -5.887, 0, 1.1, 7.218, 0, 1.567, -4.61, 0, 2.167, 1.802, 0, 2.667, -1.288, 0, 3.067, -0.142, 0, 3.333, -0.645, 0, 4, 2.337, 0, 4.933, -6.632, 0, 5.467, 5.226, 0, 6.033, -2.241, 0, 6.567, 0.818, 0, 7.033, -0.635, 0, 7.267, 6.629, 0, 7.533, -8.915, 0, 7.867, 5.552, 0, 8.133, 0.149, 0, 8.3, 0.41, 0, 8.8, -4.121, 0, 9.033, 1.602, 0, 9.167, -3.852, 0, 9.4, 13.056, 0, 9.667, -11.685, 0, 9.967, 11.785, 0, 10.533, -7.555, 0, 11.267, 4.772, 0, 11.8, -1.959, 0, 12.367, 0.873, 0, 12.8, -0.345, 0, 13.267, 2.332, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param60", "Segments": [0, 0, 2, 0.033, 0, 0, 0.167, -2.343, 0, 0.4, 6.109, 0, 0.767, -6.058, 0, 1.333, 5.315, 0, 1.733, -3.331, 0, 2.4, 1.155, 0, 2.833, -0.889, 0, 3.233, 0.228, 0, 3.533, -1.048, 0, 4.233, 1.565, 0, 4.6, 0.281, 0, 4.767, 2.863, 0, 5.167, -5.334, 0, 5.633, 3.885, 0, 6.267, -1.182, 0, 6.733, 0.652, 0, 7.167, -4.301, 0, 7.433, 10.847, 0, 7.7, -12.659, 0, 8, 8.672, 0, 8.3, -2.757, 0, 8.567, 2.294, 0, 8.967, -5.418, 0, 9.167, 5.143, 0, 9.333, -9.405, 0, 9.567, 17.577, 0, 9.833, -18.751, 0, 10.133, 16.111, 0, 10.533, -3.256, 0, 10.567, -3.215, 0, 10.667, -3.519, 0, 10.9, -1.479, 0, 11.167, -3.675, 0, 11.467, 3.985, 0, 11.867, -1.253, 0, 12.6, 0.56, 0, 13, -1.047, 0, 13.4, 1.464, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param61", "Segments": [0, 0, 2, 0.067, 0, 0, 0.267, -2.65, 0, 0.533, 7.548, 0, 0.867, -9.001, 0, 1.267, 5.976, 0, 1.767, -5.307, 0, 2.133, 2.207, 0, 2.433, 0.412, 0, 2.533, 0.48, 0, 2.9, -1.033, 0, 3.3, 0.903, 0, 3.633, -1.437, 0, 3.967, 0.511, 0, 4.1, 0.227, 0, 4.333, 0.856, 0, 4.633, -0.581, 0, 4.9, 3.684, 0, 5.233, -6.032, 0, 5.667, 5.767, 0, 6.033, -3.044, 0, 6.367, 0.142, 0, 6.5, -0.123, 0, 6.8, 0.947, 0, 7.267, -5.154, 0, 7.533, 12.712, 0, 7.833, -16.265, 0, 8.133, 13.951, 0, 8.433, -8.305, 0, 8.733, 5.037, 0, 9.067, -6.587, 0, 9.267, 5.462, 0, 9.467, -12.541, 0, 9.667, 17.814, 0, 9.933, -21.738, 0, 10.267, 20.592, 0, 10.567, -10.255, 0, 10.933, 3.128, 0, 11.267, -4.186, 0, 11.567, 5.937, 0, 11.9, -3.776, 0, 12.267, 1.041, 0, 12.5, 0.068, 0, 12.733, 0.425, 0, 13.1, -1.573, 0, 13.467, 1.497, 0, 13.8, -0.128, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param49", "Segments": [0, 0, 2, 0.033, 0, 0, 0.2, 8.687, 0, 0.5, -12.84, 0, 0.967, 10.203, 0, 1.467, -6.194, 0, 1.767, 2.077, 0, 2.067, -0.972, 0, 2.367, -0.097, 0, 2.733, -0.695, 0, 2.767, -0.694, 0, 2.833, -0.711, 0, 2.867, -0.707, 0, 3.3, -1.937, 0, 4, 3.516, 0, 4.367, 1.44, 0, 4.467, 1.486, 0, 4.9, -12.291, 0, 5.4, 6.965, 0, 5.7, -2.996, 0, 6, 0.373, 0, 6.3, -0.862, 0, 6.667, -0.394, 0, 6.833, -0.46, 0, 7.233, 14.886, 0, 7.5, -22.34, 0, 7.8, 18.104, 0, 8.067, -9.305, 0, 8.367, 5.087, 0, 8.8, -9.281, 0, 9, 5.871, 0, 9.167, -11.895, 0, 9.4, 27.677, 0, 9.633, -30, 0, 9.933, 29.707, 0, 10.233, -12.627, 0, 10.667, -1.358, 0, 10.933, -8.417, 0, 11.267, 8.158, 0, 11.6, -2.485, 0, 11.967, 0.529, 0, 12.267, -0.282, 0, 12.6, 0.103, 0, 12.8, 0.042, 0, 13.133, 3.934, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param50", "Segments": [0, 0, 2, 0.033, 0, 0, 0.167, -4.727, 0, 0.4, 11.005, 0, 0.667, -11.455, 0, 1.067, 5.498, 0, 1.367, -0.172, 0, 1.4, -0.161, 0, 1.6, -4.816, 0, 1.9, 3.915, 0, 2.2, -2.143, 0, 2.5, 1.056, 0, 2.8, -0.436, 0, 3.167, 0.556, 0, 3.467, -1.586, 0, 3.767, 0.966, 0, 4, -0.606, 0, 4.2, 0.878, 0, 4.467, -0.572, 0, 4.767, 5.294, 0, 5.033, -6.77, 0, 5.533, 5.642, 0, 5.833, -4.568, 0, 6.133, 2.534, 0, 6.433, -1.225, 0, 6.733, 0.53, 0, 7.167, -7.445, 0, 7.4, 18.861, 0, 7.667, -27.551, 0, 7.967, 22.696, 0, 8.233, -15.865, 0, 8.533, 9.891, 0, 8.933, -10.75, 0, 9.167, 14.748, 0, 9.333, -17.736, 0, 9.5, 30, 2, 9.533, 30, 0, 9.733, -30, 2, 9.8, -30, 0, 10.067, 30, 2, 10.1, 30, 0, 10.367, -14.934, 0, 10.767, 6.373, 0, 11.133, -7.85, 0, 11.4, 8.887, 0, 11.7, -4.958, 0, 12.067, 2.274, 0, 12.367, -1.042, 0, 12.7, 0.44, 0, 12.967, -1.503, 0, 13.267, 1.471, 0, 13.567, -0.551, 0, 13.867, 0.695, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param51", "Segments": [0, 0, 2, 0.033, 0, 0, 0.2, 8.687, 0, 0.5, -12.84, 0, 0.967, 10.203, 0, 1.467, -6.194, 0, 1.767, 2.077, 0, 2.067, -0.972, 0, 2.367, -0.097, 0, 2.733, -0.695, 0, 2.767, -0.694, 0, 2.833, -0.711, 0, 2.867, -0.707, 0, 3.3, -1.937, 0, 4, 3.516, 0, 4.367, 1.44, 0, 4.467, 1.486, 0, 4.9, -12.291, 0, 5.4, 6.965, 0, 5.7, -2.996, 0, 6, 0.373, 0, 6.3, -0.862, 0, 6.667, -0.394, 0, 6.833, -0.46, 0, 7.233, 14.886, 0, 7.5, -22.34, 0, 7.8, 18.104, 0, 8.067, -9.305, 0, 8.367, 5.087, 0, 8.8, -9.281, 0, 9, 5.871, 0, 9.167, -11.895, 0, 9.4, 27.677, 0, 9.633, -30, 0, 9.933, 29.707, 0, 10.233, -12.627, 0, 10.667, -1.358, 0, 10.933, -8.417, 0, 11.267, 8.158, 0, 11.6, -2.485, 0, 11.967, 0.529, 0, 12.267, -0.282, 0, 12.6, 0.103, 0, 12.8, 0.042, 0, 13.133, 3.934, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param52", "Segments": [0, 0, 2, 0.033, 0, 0, 0.167, -4.727, 0, 0.4, 11.005, 0, 0.667, -11.455, 0, 1.067, 5.498, 0, 1.367, -0.172, 0, 1.4, -0.161, 0, 1.6, -4.816, 0, 1.9, 3.915, 0, 2.2, -2.143, 0, 2.5, 1.056, 0, 2.8, -0.436, 0, 3.167, 0.556, 0, 3.467, -1.586, 0, 3.767, 0.966, 0, 4, -0.606, 0, 4.2, 0.878, 0, 4.467, -0.572, 0, 4.767, 5.294, 0, 5.033, -6.77, 0, 5.533, 5.642, 0, 5.833, -4.568, 0, 6.133, 2.534, 0, 6.433, -1.225, 0, 6.733, 0.53, 0, 7.167, -7.445, 0, 7.4, 18.861, 0, 7.667, -27.551, 0, 7.967, 22.696, 0, 8.233, -15.865, 0, 8.533, 9.891, 0, 8.933, -10.75, 0, 9.167, 14.748, 0, 9.333, -17.736, 0, 9.5, 30, 2, 9.533, 30, 0, 9.733, -30, 2, 9.8, -30, 0, 10.067, 30, 2, 10.1, 30, 0, 10.367, -14.934, 0, 10.767, 6.373, 0, 11.133, -7.85, 0, 11.4, 8.887, 0, 11.7, -4.958, 0, 12.067, 2.274, 0, 12.367, -1.042, 0, 12.7, 0.44, 0, 12.967, -1.503, 0, 13.267, 1.471, 0, 13.567, -0.551, 0, 13.867, 0.695, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation63", "Segments": [0, 1.196, 2, 1.733, 1.196, 2, 2.967, 1.196, 2, 3.267, 1.196, 2, 3.933, 1.196, 2, 4.567, 1.196, 2, 7, 1.196, 2, 9.633, 1.196, 2, 10.633, 1.196, 2, 11.667, 1.196, 2, 12.133, 1.196, 2, 12.767, 1.196, 0, 14, -0.675]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation64", "Segments": [0, 0, 2, 0.033, 0, 0, 0.2, 2.896, 0, 0.5, -4.28, 0, 0.967, 3.401, 0, 1.467, -2.064, 0, 1.767, 0.692, 0, 2.067, -0.324, 0, 2.367, -0.032, 0, 2.733, -0.232, 0, 2.767, -0.231, 0, 2.833, -0.237, 0, 2.867, -0.235, 0, 3.3, -0.646, 0, 4, 1.172, 0, 4.367, 0.48, 0, 4.467, 0.495, 0, 4.9, -4.097, 0, 5.4, 2.322, 0, 5.7, -0.999, 0, 6, 0.124, 0, 6.3, -0.287, 0, 6.667, -0.131, 0, 6.833, -0.153, 0, 7.233, 4.962, 0, 7.5, -7.447, 0, 7.8, 6.035, 0, 8.067, -3.102, 0, 8.367, 1.696, 0, 8.8, -3.094, 0, 9, 1.957, 0, 9.167, -3.965, 0, 9.4, 9.226, 0, 9.633, -10.307, 0, 9.933, 9.902, 0, 10.233, -4.209, 0, 10.667, -0.453, 0, 10.933, -2.806, 0, 11.267, 2.719, 0, 11.6, -0.828, 0, 11.967, 0.176, 0, 12.267, -0.094, 0, 12.6, 0.034, 0, 12.8, 0.014, 1, 12.911, 0.014, 13.022, 0.821, 13.133, 1.311, 1, 13.422, 2.586, 13.711, 2.951, 14, 2.951]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation65", "Segments": [0, 0, 2, 0.033, 0, 0, 0.167, -2.363, 0, 0.4, 5.503, 0, 0.667, -5.728, 0, 1.067, 2.749, 0, 1.367, -0.086, 0, 1.4, -0.081, 0, 1.6, -2.408, 0, 1.9, 1.958, 0, 2.2, -1.072, 0, 2.5, 0.528, 0, 2.8, -0.218, 0, 3.167, 0.278, 0, 3.467, -0.793, 0, 3.767, 0.483, 0, 4, -0.303, 0, 4.2, 0.439, 0, 4.467, -0.286, 0, 4.767, 2.647, 0, 5.033, -3.385, 0, 5.533, 2.821, 0, 5.833, -2.284, 0, 6.133, 1.267, 0, 6.433, -0.613, 0, 6.733, 0.265, 0, 7.167, -3.722, 0, 7.4, 9.43, 0, 7.667, -13.775, 0, 7.967, 11.348, 0, 8.233, -7.932, 0, 8.533, 4.946, 0, 8.933, -5.375, 0, 9.167, 7.374, 0, 9.333, -8.868, 0, 9.533, 16.509, 0, 9.767, -19.258, 0, 10.067, 16.756, 0, 10.367, -7.467, 0, 10.767, 3.187, 0, 11.133, -3.925, 0, 11.4, 4.444, 0, 11.7, -2.479, 0, 12.067, 1.137, 0, 12.367, -0.521, 0, 12.7, 0.22, 0, 12.967, -0.751, 0, 13.267, 0.735, 0, 13.567, -0.276, 1, 13.667, -0.276, 13.767, -0.226, 13.867, 0.347, 1, 13.911, 0.602, 13.956, 4.849, 14, 4.849]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation66", "Segments": [0, 0, 2, 0.067, 0, 0, 0.233, -2.46, 0, 0.5, 7.686, 0, 0.767, -10.519, 0, 1.133, 7.279, 0, 1.667, -3.142, 0, 2, 3.843, 0, 2.3, -2.81, 0, 2.633, 1.736, 0, 2.933, -0.918, 0, 3.233, 0.7, 0, 3.567, -1.537, 0, 3.867, 1.193, 0, 4.133, -0.536, 0, 4.367, 0.557, 0, 4.6, -0.497, 0, 4.867, 3.606, 0, 5.167, -6.318, 0, 5.567, 4.982, 0, 5.933, -4.834, 0, 6.233, 3.451, 0, 6.567, -2.042, 0, 6.867, 1.097, 0, 7.233, -4.549, 0, 7.5, 12.239, 0, 7.767, -19.274, 0, 8.067, 19.309, 0, 8.367, -15.124, 0, 8.667, 11.126, 0, 9, -9.841, 0, 9.233, 10.12, 0, 9.467, -14.406, 0, 9.633, 14.719, 0, 9.867, -23.71, 0, 10.167, 24.524, 0, 10.5, -15.231, 0, 10.833, 9.172, 0, 11.2, -8.419, 0, 11.533, 8.903, 0, 11.833, -6.507, 0, 12.167, 3.822, 0, 12.467, -2.063, 0, 12.8, 1.029, 0, 13.1, -1.39, 0, 13.4, 1.536, 1, 13.5, 1.536, 13.6, 1.62, 13.7, -0.872, 1, 13.8, -3.364, 13.9, -11.659, 14, -11.659]}, {"Target": "Parameter", "Id": "Param55", "Segments": [0, 0, 2, 0.1, 0, 0, 0.333, -3.515, 0, 0.567, 12.582, 0, 0.867, -20.341, 0, 1.233, 17.515, 0, 1.6, -8.602, 0, 2.1, 7.634, 0, 2.433, -7.045, 0, 2.733, 5.246, 0, 3.033, -3.262, 0, 3.367, 2.266, 0, 3.667, -3.243, 0, 3.967, 2.969, 0, 4.267, -1.585, 0, 4.5, 1.069, 0, 4.7, -0.543, 0, 4.967, 5.693, 0, 5.267, -12.25, 0, 5.633, 11.712, 0, 6, -11.05, 0, 6.333, 9.118, 0, 6.667, -6.388, 0, 6.967, 3.967, 0, 7.333, -8.253, 0, 7.567, 20.967, 0, 7.833, -30, 2, 7.867, -30, 0, 8.133, 30, 2, 8.167, 30, 0, 8.467, -29.481, 0, 8.767, 23.682, 0, 9.1, -22.05, 0, 9.333, 11.843, 0, 9.533, -10.709, 0, 9.7, 21.55, 0, 9.933, -30, 2, 9.967, -30, 0, 10.233, 30, 2, 10.333, 30, 0, 10.6, -29.779, 0, 10.933, 21.676, 0, 11.267, -19.641, 0, 11.633, 19.509, 0, 11.933, -16.029, 0, 12.267, 11.222, 0, 12.6, -6.976, 0, 12.9, 4.052, 0, 13.2, -3.631, 0, 13.5, 3.804, 1, 13.6, 3.804, 13.7, 2.536, 13.8, -2.675, 1, 13.867, -6.149, 13.933, -10.57, 14, -10.57]}, {"Target": "Parameter", "Id": "Param28", "Segments": [0, 0, 0, 1, 7.36, 0, 2.067, -7.12, 0, 3.133, 7.36, 0, 4.2, -7.12, 0, 4.567, 0, 0, 4.9, -8, 0, 5.5, 8, 0, 6.133, -8, 0, 6.933, 8, 1, 6.955, 8, 6.978, 8.378, 7, 7.729, 1, 7.267, -0.062, 7.533, -8, 7.8, -8, 0, 8.767, 8, 2, 9.633, 8, 2, 10.633, 8, 2, 11.667, 8, 2, 12.133, 8, 2, 12.767, 8, 0, 14, -7.12]}, {"Target": "Parameter", "Id": "Param29", "Segments": [0, 0, 0, 0.333, -4.74, 0, 1.4, 5.1, 0, 2.467, -4.74, 0, 3.533, 5.1, 0, 4.2, -1.278, 0, 4.567, 0, 0, 5.3, -18, 0, 5.9, 18, 0, 6.533, -18, 1, 6.689, -18, 6.844, -9.45, 7, 4.441, 1, 7.111, 14.364, 7.222, 18, 7.333, 18, 0, 8.2, -18, 0, 9.167, 18, 2, 9.633, 18, 2, 10.633, 18, 2, 11.667, 18, 2, 12.133, 18, 2, 12.767, 18, 0, 14, -1.278]}, {"Target": "Parameter", "Id": "Param30", "Segments": [0, 0, 0, 0.867, 2.533, 0, 1.933, -6.287, 0, 3, 2.533, 0, 4.067, -6.287, 1, 4.111, -6.287, 4.156, -6.523, 4.2, -5.909, 1, 4.322, -4.22, 4.445, 0, 4.567, 0, 0, 5.267, -3.724, 0, 5.867, 4.776, 0, 6.5, -3.724, 1, 6.667, -3.724, 6.833, -1.394, 7, 2.087, 1, 7.1, 4.175, 7.2, 4.776, 7.3, 4.776, 0, 8.167, -3.724, 0, 9.133, 4.776, 2, 9.633, 4.776, 2, 10.633, 4.776, 2, 11.667, 4.776, 2, 12.133, 4.776, 2, 12.767, 4.776, 0, 14, -5.909]}, {"Target": "Parameter", "Id": "Param31", "Segments": [0, 0, 0, 0.367, 3.018, 0, 1.433, -6.18, 0, 2.5, 3.018, 0, 3.567, -6.18, 1, 3.778, -6.18, 3.989, -1.874, 4.2, -0.551, 1, 4.322, 0.215, 4.445, 0, 4.567, 0, 0, 5.433, -7.134, 0, 6.033, 8.512, 0, 6.667, -7.134, 1, 6.778, -7.134, 6.889, -5.554, 7, -1.242, 1, 7.156, 4.795, 7.311, 8.512, 7.467, 8.512, 0, 8.333, -7.134, 0, 9.3, 8.512, 2, 9.633, 8.512, 2, 10.633, 8.512, 2, 11.667, 8.512, 2, 12.133, 8.512, 2, 12.767, 8.512, 0, 14, -0.551]}, {"Target": "Parameter", "Id": "Param32", "Segments": [0, 0, 0, 0.9, 2.284, 0, 1.967, -2.796, 0, 3.033, 2.284, 0, 4.1, -2.796, 1, 4.133, -2.796, 4.167, -2.854, 4.2, -2.714, 1, 4.322, -2.2, 4.445, -1.238, 4.567, 0, 1, 4.622, 0.563, 4.678, 12.248, 4.733, 12.248, 0, 5.6, -10.545, 0, 6.2, 12.248, 0, 6.833, -10.545, 1, 6.889, -10.545, 6.944, -10.837, 7, -7.996, 1, 7.211, 2.797, 7.422, 12.248, 7.633, 12.248, 0, 8.5, -10.545, 0, 9.467, 12.248, 2, 9.633, 12.248, 2, 10.633, 12.248, 2, 11.667, 12.248, 2, 12.133, 12.248, 2, 12.767, 12.248, 0, 14, -2.714]}, {"Target": "Parameter", "Id": "Param33", "Segments": [0, 0, 0, 0.4, 3.279, 0, 1.467, -10.047, 0, 2.533, 3.279, 0, 3.6, -10.047, 1, 3.8, -10.047, 4, -4.768, 4.2, -2.392, 1, 4.322, -0.94, 4.445, -1.402, 4.567, 0, 1, 4.689, 1.402, 4.811, 15.984, 4.933, 15.984, 0, 5.8, -13.955, 0, 6.4, 15.984, 1, 6.6, 15.984, 6.8, 0.202, 7, -13.714, 1, 7.011, -14.487, 7.022, -13.955, 7.033, -13.955, 0, 7.833, 15.984, 0, 8.7, -13.955, 1, 9.011, -13.955, 9.322, 2.052, 9.633, 15.881, 1, 9.644, 16.375, 9.656, 15.984, 9.667, 15.984, 2, 10.633, 15.984, 2, 11.667, 15.984, 2, 12.133, 15.984, 2, 12.767, 15.984, 0, 14, -2.392]}, {"Target": "Parameter", "Id": "Param34", "Segments": [0, 0, 0, 0.933, 11.7, 0, 2, -21.42, 0, 3.067, 11.7, 0, 4.133, -21.42, 1, 4.155, -21.42, 4.178, -21.741, 4.2, -21.182, 1, 4.322, -18.106, 4.445, -7.282, 4.567, 0, 1, 4.767, 11.916, 4.967, 18.225, 5.167, 18.225, 0, 6.033, -16.001, 0, 6.633, 18.225, 1, 6.755, 18.225, 6.878, 10.201, 7, -2.915, 1, 7.089, -12.454, 7.178, -16.001, 7.267, -16.001, 0, 8.067, 18.225, 0, 8.933, -16.001, 1, 9.166, -16.001, 9.4, -3.698, 9.633, 11.848, 1, 9.722, 17.77, 9.811, 18.225, 9.9, 18.225, 2, 10.633, 18.225, 2, 11.667, 18.225, 2, 12.133, 18.225, 2, 12.767, 18.225, 0, 14, -21.182]}, {"Target": "Parameter", "Id": "Param84", "Segments": [0, 0, 2, 1.733, 0, 2, 2.967, 0, 2, 3.267, 0, 2, 3.933, 0, 2, 4.567, 0, 0, 6.033, 30, 0, 9.633, 0, 2, 10.633, 0, 2, 11.667, 0, 2, 12.133, 0, 2, 12.767, 0, 2, 14, 0]}, {"Target": "Parameter", "Id": "Param85", "Segments": [0, 0, 2, 1.733, 0, 2, 2.967, 0, 2, 3.267, 0, 2, 3.933, 0, 2, 4.567, 0, 0, 6.033, -30, 0, 9.633, 0, 2, 10.633, 0, 2, 11.667, 0, 2, 12.133, 0, 2, 12.767, 0, 2, 14, 0]}, {"Target": "Parameter", "Id": "Param83", "Segments": [0, 0, 2, 1.733, 0, 2, 2.967, 0, 2, 3.267, 0, 2, 3.933, 0, 2, 4.567, 0, 0, 6.033, 1, 0, 9.633, 0, 2, 10.633, 0, 2, 11.667, 0, 2, 12.133, 0, 2, 12.767, 0, 2, 14, 0]}, {"Target": "Parameter", "Id": "Param98", "Segments": [0, 0, 2, 1.733, 0, 2, 2.967, 0, 2, 3.267, 0, 2, 3.933, 0, 2, 4.567, 0, 2, 7, 0, 2, 9.633, 0, 2, 10.633, 0, 2, 11.667, 0, 2, 12.133, 0, 2, 12.767, 0, 0, 14, -6]}, {"Target": "Parameter", "Id": "Param99", "Segments": [0, 0, 2, 1.733, 0, 2, 2.967, 0, 2, 3.267, 0, 2, 3.933, 0, 2, 4.567, 0, 2, 7, 0, 2, 9.633, 0, 2, 10.633, 0, 2, 11.667, 0, 2, 12.133, 0, 2, 12.767, 0, 0, 14, -10.921]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation67", "Segments": [0, 0, 2, 1.733, 0, 2, 2.967, 0, 2, 3.267, 0, 2, 3.933, 0, 2, 4.567, 0, 2, 7, 0, 2, 9.633, 0, 2, 10.633, 0, 2, 11.667, 0, 2, 12.133, 0, 2, 12.767, 0, 0, 14, -10.939]}, {"Target": "Parameter", "Id": "Param216mao2", "Segments": [0, 0, 2, 1.733, 0, 2, 2.967, 0, 2, 3.267, 0, 2, 3.933, 0, 2, 4.567, 0, 2, 7, 0, 2, 9.633, 0, 2, 10.633, 0, 2, 11.667, 0, 2, 12.133, 0, 0, 12.767, -8.88, 2, 14, -8.88]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation144mao2", "Segments": [0, 0, 2, 1.733, 0, 2, 2.967, 0, 2, 3.267, 0, 2, 3.933, 0, 2, 4.567, 0, 2, 7, 0, 2, 9.633, 0, 2, 10.633, 0, 2, 11.667, 0, 2, 12.133, 0, 2, 12.767, 0, 0, 14, -10.82]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation145mao2", "Segments": [0, 0, 2, 1.733, 0, 2, 2.967, 0, 2, 3.267, 0, 2, 3.933, 0, 2, 4.567, 0, 2, 7, 0, 2, 9.633, 0, 2, 10.633, 0, 2, 11.667, 0, 2, 12.133, 0, 2, 12.767, 0, 0, 14, 8.46]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation146mao2", "Segments": [0, 0, 2, 1.733, 0, 2, 2.967, 0, 2, 3.267, 0, 2, 3.933, 0, 2, 4.567, 0, 2, 7, 0, 2, 9.633, 0, 2, 10.633, 0, 2, 11.667, 0, 2, 12.133, 0, 2, 12.767, 0, 0, 14, 28.83]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation72", "Segments": [0, 0, 2, 1.733, 0, 2, 2.967, 0, 2, 3.267, 0, 2, 3.933, 0, 2, 4.567, 0, 2, 7, 0, 2, 9.633, 0, 2, 10.633, 0, 2, 11.667, 0, 2, 12.133, 0, 2, 12.767, 0, 0, 14, 8.085]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation73", "Segments": [0, 0, 2, 1.733, 0, 2, 2.967, 0, 2, 3.267, 0, 2, 3.933, 0, 2, 4.567, 0, 2, 7, 0, 2, 9.633, 0, 2, 10.633, 0, 2, 11.667, 0, 2, 12.133, 0, 2, 12.767, 0, 0, 14, 11.915]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation74", "Segments": [0, 0, 2, 1.733, 0, 2, 2.967, 0, 2, 3.267, 0, 2, 3.933, 0, 2, 4.567, 0, 2, 7, 0, 2, 9.633, 0, 2, 10.633, 0, 2, 11.667, 0, 2, 12.133, 0, 2, 12.767, 0, 0, 14, 12.341]}, {"Target": "Parameter", "Id": "Param180", "Segments": [0, 0, 2, 1.733, 0, 2, 2.967, 0, 2, 3.267, 0, 2, 3.933, 0, 0, 4.567, 0.5, 2, 14, 0.5]}, {"Target": "Parameter", "Id": "Param293", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamCheek", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param268", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param269", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param193", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param192", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param282", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param183", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param184", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param289", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param185", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param290", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param291", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param186", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param187", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param188", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param292", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param189", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param190", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param191", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param8", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param23", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param24", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param25", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param26", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param27", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param200", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param286", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param287", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param288", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param202", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param205", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param203", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param204", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param21", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param213", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param216", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param217", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param218", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param219", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param220", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param12", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param197", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param198", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param199", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param208", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param279", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param212", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param270", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param271", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param230", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param231", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param222", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param207", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param236", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param239", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param240", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param241", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param242", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param243", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param244", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param275", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param224", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param225", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param227", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param226", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param285", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param147", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param246", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param141", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param182", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param247", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param229", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param117", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param115", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param116", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param260", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param261", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param133", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param139", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param257", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param137", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param138", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param135", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param176", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param136", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param177", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param251", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param86", "Segments": [0, 1, 0, 14, 1]}, {"Target": "Parameter", "Id": "Param91", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param2", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param87", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param88", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param94", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param95", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param92", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param93", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param96", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param97", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param89", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param90", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation56", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param218mao2", "Segments": [0, 1, 0, 14, 1]}, {"Target": "Parameter", "Id": "Param281", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param211mao2", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param197mao2", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param217mao2", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param202mao2", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param203mao2", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param204mao2", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param199mao2", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param200mao2", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param201mao2", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param208mao2", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param209mao2", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param210mao2", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param205mao2", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param206mao2", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param207mao2", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param212mao2", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param213mao2", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param214mao2", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param215mao2", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param198mao2", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param219mao2", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation143mao2", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param264", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param100", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param105", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param108", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param109", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param107", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param284", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param101", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param106", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param104", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param103", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param102", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation71", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param148", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param146", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param110", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param111", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param248", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param112", "Segments": [0, -30, 0, 14, -30]}, {"Target": "Parameter", "Id": "Param249", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param206", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param114", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param113", "Segments": [0, 1, 0, 14, 1]}, {"Target": "Parameter", "Id": "Param253", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param254", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param158", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param149", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param150", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param151", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param255", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param256", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param153", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param152", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param155", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param154", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param181", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param156", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param157", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param159", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param160", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param119", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param265", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param118", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param120", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param121", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param161", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param259", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param123", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param124", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param130", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param131", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param178", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param179", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param125", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param126", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param127", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param128", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param171", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param173", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param172", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param122", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param170", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param129", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param132", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param250", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param258", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param162", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param169", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param174", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param175", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param163", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param164", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param165", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param166", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param167", "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param168", "Segments": [0, 0, 0, 14, 0]}], "UserData": [{"Time": 0.0, "Value": ""}, {"Time": 0.267, "Value": ""}, {"Time": 13.5, "Value": ""}]}