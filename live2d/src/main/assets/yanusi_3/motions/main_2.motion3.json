{"Version": 3, "Meta": {"Duration": 18.1, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 355, "TotalSegmentCount": 4462, "TotalPointCount": 5188, "UserDataCount": 3, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 2, 0.333, 0, 0, 1.167, 8, 2, 2.033, 8, 2, 5.567, 8, 2, 5.9, 8, 0, 6.767, 20, 2, 7.033, 20, 2, 7.167, 20, 0, 7.667, 18, 2, 9.6, 18, 2, 9.967, 18, 1, 10.167, 18, 10.367, 9.601, 10.567, 7.784, 1, 10.778, 5.866, 10.989, 6.039, 11.2, 6.039, 2, 11.5, 6.039, 0, 12.133, 18, 2, 12.5, 18, 1, 12.778, 18, 13.055, 9.081, 13.333, 0, 1, 13.555, -7.265, 13.778, -10.104, 14, -17, 1, 14.211, -23.552, 14.422, -30, 14.633, -30, 0, 16.1, -25.98, 2, 16.567, -25.98, 0, 17.767, 0, 2, 18.1, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 2, 0.333, 0, 0, 0.733, 10, 0, 1.167, -11, 0, 1.567, -3.434, 1, 1.722, -3.434, 1.878, -4.954, 2.033, -11, 1, 2.111, -14.023, 2.189, -19.169, 2.267, -19.169, 0, 2.667, 1.877, 0, 2.9, -4, 0, 3.4, 2.3, 0, 3.8, -7.668, 0, 4.2, 0.313, 0, 4.567, -5.63, 0, 5, -2.268, 1, 5.289, -2.268, 5.578, -2.392, 5.867, -3.434, 1, 5.978, -3.835, 6.089, -11, 6.2, -11, 1, 6.378, -11, 6.555, 1.477, 6.733, 4.614, 1, 6.878, 7.163, 7.022, 7.333, 7.167, 8.999, 1, 7.256, 10.024, 7.344, 11, 7.433, 11, 1, 7.566, 11, 7.7, -9.179, 7.833, -14.067, 1, 8.033, -21.4, 8.233, -22, 8.433, -22, 0, 8.8, -15.116, 1, 8.922, -15.116, 9.045, -19.085, 9.167, -20.418, 1, 9.311, -21.994, 9.456, -22, 9.6, -22, 2, 9.967, -22, 0, 10.133, -28, 0, 10.733, 1, 2, 11.5, 1, 0, 11.9, -9.828, 1, 12.1, -9.828, 12.3, -5.39, 12.5, -5, 1, 12.778, -4.458, 13.055, -4.567, 13.333, -4, 1, 13.555, -3.546, 13.778, 2, 14, 2, 2, 14.633, 2, 0, 15.267, -5.98, 0, 16.1, -2.268, 2, 16.567, -2.268, 0, 17.033, -24, 1, 17.3, -24, 17.566, -16.221, 17.833, -4.549, 1, 17.922, -0.659, 18.011, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 2, 0.333, 0, 1, 0.533, 0, 0.733, 6.725, 0.933, 8.177, 1, 1.011, 8.741, 1.089, 8.259, 1.167, 8.784, 1, 1.456, 10.734, 1.744, 14, 2.033, 14, 0, 2.9, 6, 1, 3.067, 6, 3.233, 6.027, 3.4, 6.84, 1, 3.544, 7.545, 3.689, 9.08, 3.833, 9.08, 0, 4.567, 2.36, 1, 4.9, 2.36, 5.234, 2.414, 5.567, 3.491, 1, 5.822, 4.316, 6.078, 6.567, 6.333, 9.136, 1, 6.522, 11.035, 6.711, 15, 6.9, 15, 2, 7.033, 15, 2, 7.167, 15, 1, 7.334, 15, 7.5, 10.212, 7.667, 8.76, 1, 7.911, 6.63, 8.156, 6.515, 8.4, 6.515, 1, 8.589, 6.515, 8.778, 9.042, 8.967, 9.973, 1, 9.3, 11.617, 9.634, 11.823, 9.967, 11.823, 1, 10.145, 11.823, 10.322, 11.154, 10.5, 9.973, 1, 10.733, 8.423, 10.967, 7.72, 11.2, 7.72, 2, 11.5, 7.72, 0, 12.133, 13, 2, 12.5, 13, 0, 13.333, 0, 0, 13.8, 6, 1, 13.9, 6, 14, 4.716, 14.1, 3.84, 1, 14.278, 2.283, 14.455, 1.581, 14.633, 0, 1, 14.866, -2.075, 15.1, -3.8, 15.333, -3.8, 2, 16.567, -3.8, 1, 16.689, -3.8, 16.811, -3.808, 16.933, -3, 1, 17.244, -0.942, 17.556, 0.88, 17.867, 0.88, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 2, 0.333, 1, 0, 0.467, 0, 0, 0.733, 1, 2, 2.033, 1, 0, 2.2, 0, 1, 2.356, 0, 2.511, 0.834, 2.667, 0.9, 1, 2.911, 1.004, 3.156, 1, 3.4, 1, 1, 3.722, 1, 4.045, 0.98, 4.367, 0.9, 1, 4.434, 0.883, 4.5, 0, 4.567, 0, 2, 5.567, 0, 0, 6.033, 0.9, 2, 6.767, 0.9, 2, 7.033, 0.9, 2, 7.267, 0.9, 0, 7.4, 0.6, 1, 7.589, 0.6, 7.778, 0.668, 7.967, 0.799, 1, 8.067, 0.868, 8.167, 0.9, 8.267, 0.9, 2, 8.867, 0.9, 0, 9.3, 0.746, 0, 9.6, 0.799, 2, 9.967, 0.799, 0, 10.167, 0, 0, 10.3, 1, 2, 11.167, 1, 0, 11.233, 0, 2, 11.267, 0, 0, 11.367, 1, 2, 11.5, 1, 0, 11.7, 0.8, 0, 12.133, 0.9, 2, 12.567, 0.9, 0, 12.7, 0, 2, 12.733, 0, 0, 12.833, 0.9, 2, 13.333, 0.9, 2, 14, 0.9, 2, 14.633, 0.9, 2, 16.567, 0.9, 2, 16.733, 0.9, 0, 16.867, 0, 2, 16.9, 0, 1, 16.978, 0, 17.055, 0.867, 17.133, 0.9, 1, 17.344, 0.99, 17.556, 1, 17.767, 1, 2, 18.1, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 2, 0.333, 0, 2, 0.467, 0, 0, 0.733, 1, 2, 2.033, 1, 0, 2.2, 0, 0, 2.667, 1, 2, 5.567, 1, 2, 7.033, 1, 2, 7.267, 1, 0, 7.4, 0, 0, 7.967, 1, 2, 9.6, 1, 0, 9.667, 0.6, 0, 9.967, 1, 0, 10.167, 0, 0, 10.3, 1, 2, 11.167, 1, 0, 11.233, 0, 2, 11.267, 0, 0, 11.367, 1, 2, 11.5, 1, 0, 11.7, 0.8, 0, 12.133, 1, 2, 12.567, 1, 0, 12.7, 0, 2, 12.733, 0, 0, 12.833, 1, 2, 13.333, 1, 0, 14, 0, 2, 14.633, 0, 2, 16.567, 0, 0, 16.733, 1, 0, 16.867, 0, 2, 16.9, 0, 2, 17.767, 0, 2, 18.1, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 2, 0.333, 1, 0, 0.467, 0, 0, 0.733, 1, 2, 2.033, 1, 0, 2.2, 0, 1, 2.356, 0, 2.511, 0.834, 2.667, 0.9, 1, 2.911, 1.004, 3.156, 1, 3.4, 1, 1, 3.722, 1, 4.045, 0.98, 4.367, 0.9, 1, 4.434, 0.883, 4.5, 0, 4.567, 0, 2, 5.567, 0, 0, 6.033, 0.9, 2, 6.767, 0.9, 2, 7.033, 0.9, 2, 7.267, 0.9, 0, 7.4, 0.7, 1, 7.589, 0.7, 7.778, 0.72, 7.967, 0.8, 1, 8.067, 0.842, 8.167, 0.9, 8.267, 0.9, 2, 8.867, 0.9, 0, 9.3, 0.724, 0, 9.6, 0.813, 2, 9.967, 0.813, 0, 10.167, 0, 0, 10.3, 1, 2, 11.167, 1, 0, 11.233, 0, 2, 11.267, 0, 0, 11.367, 1, 2, 11.5, 1, 0, 11.7, 0.8, 0, 12.133, 0.9, 2, 12.567, 0.9, 0, 12.7, 0, 2, 12.733, 0, 0, 12.833, 0.9, 2, 13.333, 0.9, 2, 14, 0.9, 2, 14.633, 0.9, 2, 16.567, 0.9, 2, 16.733, 0.9, 0, 16.867, 0, 2, 16.9, 0, 1, 16.978, 0, 17.055, 0.867, 17.133, 0.9, 1, 17.344, 0.99, 17.556, 1, 17.767, 1, 2, 18.1, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 2, 0.333, 0, 2, 0.467, 0, 0, 0.733, 1, 2, 2.033, 1, 0, 2.2, 0, 0, 2.667, 1, 2, 5.567, 1, 2, 7.033, 1, 2, 7.267, 1, 0, 7.4, 0, 0, 7.967, 1, 2, 9.6, 1, 2, 9.967, 1, 0, 10.167, 0, 0, 10.3, 1, 2, 11.167, 1, 0, 11.233, 0, 2, 11.267, 0, 0, 11.367, 1, 2, 11.5, 1, 0, 11.7, 0.8, 0, 12.133, 1, 2, 12.567, 1, 0, 12.7, 0, 2, 12.733, 0, 0, 12.833, 1, 2, 13.333, 1, 0, 14, 0, 2, 14.633, 0, 2, 16.567, 0, 0, 16.733, 1, 0, 16.867, 0, 2, 16.9, 0, 2, 17.767, 0, 2, 18.1, 0]}, {"Target": "Parameter", "Id": "Param76", "Segments": [0, 0, 2, 0.333, 0, 0, 0.5, -30, 2, 2.033, -30, 2, 5.567, -30, 2, 7.033, -30, 2, 7.167, -30, 2, 7.967, -30, 2, 9.6, -30, 2, 9.967, -30, 2, 10.3, -30, 2, 10.667, -30, 2, 11.5, -30, 2, 12.133, -30, 2, 12.5, -30, 2, 13.333, -30, 2, 14, -30, 2, 14.633, -30, 2, 16.567, -30, 0, 17.767, 0, 2, 18.1, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 2, 0.333, 0, 2, 2.033, 0, 2, 5.567, 0, 2, 6, 0, 0, 6.133, 0.5, 2, 7.033, 0.5, 2, 7.267, 0.5, 1, 7.3, 0.5, 7.334, 0.184, 7.367, 0.152, 1, 7.567, -0.04, 7.767, -0.1, 7.967, -0.1, 2, 9.6, -0.1, 2, 9.967, -0.1, 1, 10.045, -0.1, 10.122, -0.088, 10.2, -0.022, 1, 10.233, 0.007, 10.267, 0.092, 10.3, 0.2, 1, 10.333, 0.308, 10.367, 0.5, 10.4, 0.5, 2, 11.5, 0.5, 0, 12.133, 0.3, 2, 12.5, 0.3, 1, 12.589, 0.3, 12.678, -0.277, 12.767, -0.3, 1, 12.956, -0.348, 13.144, -0.338, 13.333, -0.4, 1, 13.555, -0.473, 13.778, -0.8, 14, -0.8, 2, 14.633, -0.8, 2, 16.567, -0.8, 0, 17.767, 0, 2, 18.1, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 2, 0.333, 0, 2, 2.033, 0, 2, 5.567, 0, 2, 6, 0, 0, 6.133, 0.6, 2, 7.033, 0.6, 2, 7.267, 0.6, 1, 7.3, 0.6, 7.334, 0.063, 7.367, 0, 1, 7.567, -0.38, 7.767, -0.5, 7.967, -0.5, 2, 9.6, -0.5, 2, 9.967, -0.5, 1, 10.045, -0.5, 10.122, -0.494, 10.2, -0.431, 1, 10.233, -0.403, 10.267, 0.019, 10.3, 0.3, 1, 10.333, 0.581, 10.367, 0.8, 10.4, 0.8, 2, 11.5, 0.8, 0, 12.133, 0.1, 2, 12.5, 0.1, 1, 12.589, 0.1, 12.678, -0.058, 12.767, -0.2, 1, 12.956, -0.503, 13.144, -0.6, 13.333, -0.6, 0, 14, -0.2, 2, 14.633, -0.2, 2, 16.567, -0.2, 0, 17.767, 0, 2, 18.1, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 2, 0.333, 0, 0, 1.167, -0.3, 2, 2.033, -0.3, 2, 5.567, -0.3, 0, 6.767, -0.2, 2, 7.033, -0.2, 2, 7.167, -0.2, 2, 7.967, -0.2, 2, 9.6, -0.2, 2, 9.967, -0.2, 2, 11.5, -0.2, 2, 12.133, -0.2, 2, 12.5, -0.2, 0, 13.333, -0.182, 2, 14, -0.182, 2, 14.633, -0.182, 2, 16.567, -0.182, 0, 17.767, 0, 2, 18.1, 0]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 2, 0.333, 0, 0, 1.167, -0.222, 1, 1.456, -0.222, 1.744, -0.134, 2.033, 0, 1, 2.344, 0.145, 2.656, 0.2, 2.967, 0.2, 0, 3.867, 0, 0, 4.6, 0.112, 2, 5.567, 0.112, 0, 6.767, -0.2, 2, 7.033, -0.2, 2, 7.167, -0.2, 0, 7.967, -0.356, 2, 9.6, -0.356, 2, 9.967, -0.356, 0, 10.567, -0.2, 2, 11.5, -0.2, 2, 12.133, -0.2, 2, 12.5, -0.2, 0, 13.333, 0, 2, 14, 0, 2, 14.633, 0, 2, 16.567, 0, 2, 17.767, 0, 2, 18.1, 0]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 2, 0.333, 0, 0, 1.167, -0.3, 2, 2.033, -0.3, 2, 5.567, -0.3, 0, 6.767, -0.2, 2, 7.033, -0.2, 2, 7.167, -0.2, 2, 7.967, -0.2, 2, 9.6, -0.2, 2, 9.967, -0.2, 2, 11.5, -0.2, 2, 12.133, -0.2, 2, 12.5, -0.2, 0, 13.333, -0.252, 2, 14, -0.252, 2, 14.633, -0.252, 2, 16.567, -0.252, 0, 17.767, 0, 2, 18.1, 0]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 2, 0.333, 0, 0, 1.167, -0.128, 1, 1.456, -0.128, 1.744, -0.102, 2.033, 0, 1, 2.344, 0.11, 2.656, 0.2, 2.967, 0.2, 0, 3.867, 0, 0, 4.6, 0.06, 2, 5.567, 0.06, 0, 6.767, -0.2, 2, 7.033, -0.2, 2, 7.167, -0.2, 0, 7.967, -0.348, 2, 9.6, -0.348, 2, 9.967, -0.348, 0, 10.567, -0.2, 2, 11.5, -0.2, 2, 12.133, -0.2, 2, 12.5, -0.2, 0, 13.333, 0, 2, 14, 0, 2, 14.633, 0, 2, 16.567, 0, 2, 17.767, 0, 2, 18.1, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 2, 0.333, 0, 2, 2.033, 0, 0, 2.967, 0.206, 2, 5.567, 0.206, 2, 7.033, 0.206, 2, 7.167, 0.206, 2, 7.967, 0.206, 2, 9.6, 0.206, 2, 9.967, 0.206, 0, 10.567, 0, 2, 11.5, 0, 2, 12.133, 0, 2, 12.5, 0, 2, 13.333, 0, 2, 14, 0, 2, 14.633, 0, 2, 16.567, 0, 2, 17.767, 0, 2, 18.1, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 2, 0.333, 0, 2, 2.033, 0, 0, 2.967, 0.112, 2, 5.567, 0.112, 2, 7.033, 0.112, 2, 7.167, 0.112, 2, 7.967, 0.112, 2, 9.6, 0.112, 2, 9.967, 0.112, 0, 10.567, 0, 2, 11.5, 0, 2, 12.133, 0, 2, 12.5, 0, 2, 13.333, 0, 2, 14, 0, 2, 14.633, 0, 2, 16.567, 0, 2, 17.767, 0, 2, 18.1, 0]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, -1, 2, 0.333, -1, 0, 1.167, 0, 2, 2.033, 0, 2, 5.567, 0, 0, 6.767, -0.8, 2, 7.033, -0.8, 2, 7.167, -0.8, 2, 7.967, -0.8, 2, 9.6, -0.8, 2, 9.967, -0.8, 0, 10.567, 0, 2, 11.5, 0, 2, 12.133, 0, 2, 12.5, 0, 2, 13.333, 0, 0, 14, -1, 2, 14.633, -1, 2, 16.567, -1, 2, 17.767, -1, 2, 18.1, -1]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, -1, 2, 0.333, -1, 0, 1.167, 0, 2, 2.033, 0, 2, 5.567, 0, 0, 6.767, -0.9, 2, 7.033, -0.9, 2, 7.167, -0.9, 2, 7.967, -0.9, 2, 9.6, -0.9, 2, 9.967, -0.9, 0, 10.567, 0, 2, 11.5, 0, 2, 12.133, 0, 2, 12.5, 0, 2, 13.333, 0, 0, 14, -1, 2, 14.633, -1, 2, 16.567, -1, 2, 17.767, -1, 2, 18.1, -1]}, {"Target": "Parameter", "Id": "Param", "Segments": [0, -30, 2, 0.333, -30, 0, 0.433, 0, 2, 1.8, 0, 2, 2.033, 0, 2, 4.767, 0, 2, 5.567, 0, 2, 5.667, 0, 2, 6.767, 0, 2, 7.033, 0, 2, 7.167, 0, 2, 8, 0, 2, 9.6, 0, 2, 9.967, 0, 2, 11.5, 0, 2, 12.133, 0, 2, 12.5, 0, 2, 13.333, 0, 2, 14, 0, 2, 14.633, 0, 1, 15.278, 0, 15.922, -6.675, 16.567, -19, 1, 16.967, -26.65, 17.367, -30, 17.767, -30, 2, 18.1, -30]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 1, 2, 0.333, 1, 0, 0.433, -1, 1, 0.589, -1, 0.744, -0.942, 0.9, -0.7, 1, 0.944, -0.631, 0.989, -0.3, 1.033, -0.3, 0, 1.133, -1, 0, 1.3, -0.5, 0, 1.433, -0.9, 0, 1.5, -0.2, 0, 1.8, -1, 2, 2.033, -1, 0, 2.533, 0, 1, 2.633, 0, 2.733, -0.031, 2.833, -0.3, 1, 2.889, -0.45, 2.944, -0.9, 3, -0.9, 0, 3.167, -0.2, 0, 3.3, -0.9, 0, 3.733, -0.1, 0, 3.9, -0.9, 1, 3.944, -0.9, 3.989, -0.407, 4.033, -0.2, 1, 4.078, 0.007, 4.122, 0, 4.167, 0, 2, 4.767, 0, 0, 5.067, 0.6, 1, 5.234, 0.6, 5.4, 0.469, 5.567, 0.2, 1, 5.6, 0.146, 5.634, 0.161, 5.667, 0.03, 1, 5.756, -0.317, 5.844, -1, 5.933, -1, 0, 6.767, -0.9, 2, 7.033, -0.9, 2, 7.167, -0.9, 2, 8, -0.9, 0, 8.133, -0.3, 0, 8.4, -1, 0, 9.6, -0.9, 2, 9.967, -0.9, 0, 10.133, -0.3, 0, 10.433, -0.9, 1, 10.5, -0.9, 10.566, -0.182, 10.633, -0.1, 1, 10.722, 0.009, 10.811, 0, 10.9, 0, 1, 10.967, 0, 11.033, -0.752, 11.1, -0.8, 1, 11.233, -0.897, 11.367, -0.9, 11.5, -0.9, 0, 11.7, -0.7, 1, 11.844, -0.7, 11.989, -0.758, 12.133, -0.9, 1, 12.189, -0.955, 12.244, -1, 12.3, -1, 0, 12.5, -0.2, 0, 12.8, -0.9, 0, 13.333, -0.2, 0, 13.5, -0.9, 1, 13.622, -0.9, 13.745, -0.875, 13.867, -0.7, 1, 13.911, -0.636, 13.956, 0, 14, 0, 2, 14.533, 0, 0, 14.667, -1, 1, 14.734, -1, 14.8, -0.675, 14.867, -0.6, 1, 14.922, -0.538, 14.978, -0.54, 15.033, -0.5, 1, 15.111, -0.443, 15.189, -0.4, 15.267, -0.4, 2, 16.567, -0.4, 0, 17.767, 1, 2, 18.1, 1]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, 0.3, 0, 0.567, 0, 0, 0.7, 0.5, 0, 0.9, 0.2, 0, 1.033, 0.7, 0, 1.133, 0.4, 0, 1.3, 0.8, 0, 1.433, 0.3, 2, 1.5, 0.3, 0, 1.6, 0.6, 1, 1.667, 0.6, 1.733, 0.484, 1.8, 0.3, 1, 1.878, 0.086, 1.955, 0, 2.033, 0, 0, 2.233, 0.5, 0, 2.4, 0.3, 0, 2.533, 0.6, 0, 2.667, 0.3, 0, 2.833, 0.8, 1, 2.889, 0.8, 2.944, 0.6, 3, 0.5, 1, 3.056, 0.4, 3.111, 0.379, 3.167, 0.3, 1, 3.178, 0.284, 3.189, 0, 3.2, 0, 1, 3.233, 0, 3.267, 0.1, 3.3, 0.3, 1, 3.344, 0.567, 3.389, 0.7, 3.433, 0.7, 0, 3.533, 0.2, 0, 3.733, 0.7, 0, 3.9, 0.3, 0, 4.033, 0.8, 0, 4.167, 0.259, 0, 4.267, 0.7, 0, 4.367, 0.4, 0, 4.5, 0.6, 1, 4.589, 0.6, 4.678, 0.613, 4.767, 0.5, 1, 4.867, 0.373, 4.967, 0, 5.067, 0, 2, 5.567, 0, 1, 5.6, 0, 5.634, -0.011, 5.667, 0.01, 1, 5.756, 0.066, 5.844, 0.5, 5.933, 0.5, 0, 6.2, 0.2, 0, 6.5, 0.7, 1, 6.589, 0.7, 6.678, 0.69, 6.767, 0.5, 1, 6.856, 0.31, 6.944, 0, 7.033, 0, 0, 7.167, 0.5, 0, 7.333, 0.2, 0, 7.533, 0.7, 0, 7.7, 0.3, 0, 7.9, 0.8, 0, 8, 0.4, 0, 8.133, 0.6, 0, 8.267, 0, 0, 8.4, 1, 0, 8.6, 0.2, 0, 8.9, 0.8, 0, 9.2, 0, 2, 9.967, 0, 0, 10.133, 0.5, 0, 10.3, 0.3, 0, 10.433, 0.7, 0, 10.533, 0, 0, 10.633, 0.3, 0, 10.767, 0, 1, 10.811, 0, 10.856, 0.233, 10.9, 0.4, 1, 10.967, 0.65, 11.033, 0.7, 11.1, 0.7, 0, 11.267, 0.4, 0, 11.5, 0.7, 0, 11.7, 0.3, 0, 11.833, 0.5, 0, 12.133, 0, 2, 12.3, 0, 0, 12.5, 0.3, 0, 12.667, 0, 0, 12.8, 0.6, 1, 12.878, 0.6, 12.955, 0.342, 13.033, 0.2, 1, 13.133, 0.018, 13.233, 0, 13.333, 0, 0, 13.5, 0.5, 0, 13.667, 0.2, 0, 13.867, 0.6, 0, 14, 0.4, 0, 14.167, 0.6, 0, 14.4, 0.3, 0, 14.533, 0.7, 0, 14.667, 0.3, 0, 14.867, 0.7, 0, 15.033, 0, 0, 15.267, 0.5, 0, 15.7, 0, 2, 16.567, 0, 2, 17.767, 0, 2, 18.1, 0]}, {"Target": "Parameter", "Id": "Param269", "Segments": [0, 0, 2, 0.333, 0, 2, 2.033, 0, 2, 5.567, 0, 2, 7.033, 0, 2, 7.967, 0, 2, 9.967, 0, 2, 12.5, 0, 2, 13.333, 0, 2, 14, 0, 2, 14.633, 0, 2, 16.567, 0, 0, 16.733, 1, 3, 16.767, 0, 2, 17.767, 0, 2, 18.1, 0]}, {"Target": "Parameter", "Id": "Param282", "Segments": [0, 0, 2, 0.333, 0, 2, 10.4, 0, 3, 10.667, 0.5, 3, 10.933, 1, 3, 11.2, 0.5, 3, 11.5, 1, 3, 11.8, 0.5, 3, 12.067, 0, 2, 18.1, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 2, 0.333, 0, 0, 1.167, -19, 2, 2.033, -19, 0, 2.9, -12, 2, 5.567, -12, 0, 5.967, -17, 1, 6.167, -17, 6.367, 1.795, 6.567, 7.224, 1, 6.722, 11.446, 6.878, 10.938, 7.033, 10.938, 1, 7.344, 10.938, 7.656, 6.195, 7.967, 4.758, 1, 8.511, 2.244, 9.056, 1.998, 9.6, 1.998, 2, 9.967, 1.998, 1, 10.1, 1.998, 10.234, -6.609, 10.367, -7.421, 1, 10.645, -9.113, 10.922, -9.243, 11.2, -9.243, 0, 12.133, 19.445, 2, 12.5, 19.445, 0, 13.333, 0, 0, 14, 26, 0, 15.467, 15.239, 0, 16.233, 23.646, 2, 16.567, 23.646, 0, 17.767, -5.757, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 2, 0.333, 0, 0, 0.6, 6.08, 0, 1.167, -24.06, 0, 1.7, -8.499, 1, 1.811, -8.499, 1.922, -8.906, 2.033, -12.582, 1, 2.111, -15.155, 2.189, -21.914, 2.267, -21.914, 0, 2.9, 6, 0, 3.133, 2.143, 0, 3.4, 3.979, 0, 4.067, -10.408, 0, 4.367, -1.735, 0, 4.6, -6.326, 1, 4.922, -6.326, 5.245, -6.155, 5.567, 0, 1, 5.8, 4.457, 6.034, 26, 6.267, 26, 1, 6.434, 26, 6.6, 19.368, 6.767, 14, 1, 6.856, 11.137, 6.944, 9.996, 7.033, 8.455, 1, 7.344, 3.063, 7.656, -0.983, 7.967, -7, 1, 8.122, -10.009, 8.278, -14, 8.433, -14, 0, 8.933, -8.499, 0, 9.6, -13, 2, 9.967, -13, 0, 10.367, -21.914, 0, 11.2, -10.408, 0, 12.133, -18, 0, 12.433, -13.86, 2, 12.5, -13.86, 0, 13.333, 5.02, 0, 14, -14, 0, 14.633, 0, 2, 16.567, 0, 0, 16.833, -30, 0, 17.833, 4.435, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 2, 0.333, 0, 2, 2.033, 0, 1, 2.322, 0, 2.611, 0.646, 2.9, 1.8, 1, 3.211, 3.043, 3.522, 3.6, 3.833, 3.6, 0, 4.533, 1.8, 2, 5.567, 1.8, 2, 7.033, 1.8, 1, 7.344, 1.8, 7.656, 1.204, 7.967, 0, 1, 8.1, -0.516, 8.234, -0.78, 8.367, -0.78, 0, 9.433, -0.307, 2, 9.967, -0.307, 0, 11.2, 3.953, 2, 12.5, 3.953, 1, 12.778, 3.953, 13.055, 5, 13.333, 5.66, 1, 13.555, 6.188, 13.778, 6.18, 14, 6.18, 2, 14.633, 6.18, 2, 16.567, 6.18, 0, 17.767, 0, 2, 18.1, 0]}, {"Target": "Parameter", "Id": "Param267", "Segments": [0, 0, 2, 0.333, 0, 0, 1.167, 7.02, 1, 1.322, 7.02, 1.478, 5.658, 1.633, 4.724, 1, 1.766, 3.924, 1.9, 3.896, 2.033, 3.896, 0, 2.3, 5.583, 0, 2.9, -1.167, 0, 3.833, 2.853, 1, 4.066, 2.853, 4.3, 0.993, 4.533, 0.593, 1, 4.878, 0.002, 5.222, 0, 5.567, 0, 0, 6.767, 2, 2, 7.033, 2, 2, 7.967, 2, 2, 9.967, 2, 1, 10.089, 2, 10.211, 1.907, 10.333, 0.258, 1, 10.622, -3.638, 10.911, -6.892, 11.2, -6.892, 0, 12, 1.305, 1, 12.167, 1.305, 12.333, 0.729, 12.5, 0.347, 1, 12.511, 0.321, 12.522, 0.34, 12.533, 0.336, 1, 12.8, 0.225, 13.066, 0.202, 13.333, 0, 1, 13.555, -0.169, 13.778, -11.28, 14, -11.28, 0, 14.633, -9.36, 2, 16.567, -9.36, 0, 17.767, 0, 2, 18.1, 0]}, {"Target": "Parameter", "Id": "Param3", "Segments": [0, 0, 2, 0.333, 0, 0, 1.167, -7.32, 2, 2.033, -7.32, 0, 2.9, 4.56, 0, 3.833, -0.6, 0, 4.6, 1.68, 1, 4.922, 1.68, 5.245, 2.195, 5.567, 0, 1, 5.967, -2.725, 6.367, -23, 6.767, -23, 2, 7.033, -23, 1, 7.344, -23, 7.656, -18.501, 7.967, -14.6, 1, 8.511, -7.774, 9.056, -5.84, 9.6, -5.84, 2, 9.967, -5.84, 0, 11.2, -0.648, 0, 12.133, -12.408, 2, 12.5, -12.408, 0, 13.333, -25.652, 0, 14, -4.652, 1, 14.211, -4.652, 14.422, -7.143, 14.633, -8.946, 1, 14.889, -11.13, 15.144, -11.466, 15.4, -11.466, 2, 16.567, -11.466, 0, 17.767, 0, 2, 18.1, 0]}, {"Target": "Parameter", "Id": "Param4", "Segments": [0, 0, 2, 0.333, 0, 0, 0.6, 3, 0, 1.167, -4.5, 0, 1.633, -2.16, 0, 2.033, -4.5, 0, 2.433, -0.819, 0, 2.9, -4.029, 0, 3.4, -1.649, 0, 3.867, -3.841, 0, 4.2, -2.534, 0, 4.733, -3.881, 1, 5.011, -3.881, 5.289, -3.278, 5.567, -1.649, 1, 5.656, -1.128, 5.744, -0.535, 5.833, -0.535, 0, 6.5, -8.165, 0, 7.233, -4.029, 0, 8.133, -8.847, 0, 8.733, -6.987, 0, 9.3, -8.847, 0, 9.967, -7.591, 0, 10.267, -11.083, 1, 10.4, -11.083, 10.534, -7.276, 10.667, -6.031, 1, 10.867, -4.163, 11.067, -4.029, 11.267, -4.029, 0, 12.133, -11.083, 0, 12.433, -10.063, 2, 12.5, -10.063, 0, 13.333, 5.26, 0, 14, -5, 1, 14.211, -5, 14.422, -3.321, 14.633, -2.12, 1, 14.944, -0.351, 15.256, 0, 15.567, 0, 1, 15.9, 0, 16.234, -0.266, 16.567, -2.136, 1, 16.734, -3.071, 16.9, -6.031, 17.067, -6.031, 0, 17.767, 1.472, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param266", "Segments": [0, 0, 2, 0.333, 0, 0, 1.333, -9, 0, 2.033, 0, 0, 2.9, -12, 0, 3.833, -4.19, 0, 4.567, -12, 0, 5.567, -9, 0, 6.767, -15.3, 2, 7.033, -15.3, 2, 7.967, -15.3, 0, 9.633, -4.19, 2, 9.967, -4.19, 0, 10.4, -10.198, 0, 11.2, 3.036, 0, 12.133, -6.407, 2, 12.5, -6.407, 0, 13.333, 2.443, 0, 14, -20.897, 0, 14.8, -9, 0, 15.6, -20.019, 1, 15.856, -20.019, 16.111, -17.555, 16.367, -12.833, 1, 16.834, -4.21, 17.3, 0, 17.767, 0, 2, 18.1, 0]}, {"Target": "Parameter", "Id": "Param5", "Segments": [0, 0, 2, 0.333, 0, 1, 0.444, 0, 0.556, -17.694, 0.667, -18.039, 1, 0.834, -18.557, 1, -18.368, 1.167, -19, 1, 1.445, -20.053, 1.722, -22.84, 2, -22.84, 2, 5.567, -22.84, 0, 6.167, -25.72, 0, 6.767, -16, 2, 7.033, -16, 2, 7.967, -16, 2, 9.967, -16, 0, 10.267, -12.64, 0, 10.667, -16.303, 1, 10.845, -16.303, 11.022, -16.435, 11.2, -16, 1, 11.444, -15.402, 11.689, -10, 11.933, -10, 1, 12.122, -10, 12.311, -10.954, 12.5, -12.028, 1, 12.533, -12.218, 12.567, -12.16, 12.6, -12.16, 0, 13.333, 0, 0, 14, -18, 0, 14.633, -11.44, 0, 15.633, -22.84, 0, 16.567, -16.36, 0, 17.133, -28, 0, 17.767, 0, 2, 18.1, 0]}, {"Target": "Parameter", "Id": "Param6", "Segments": [0, 0, 2, 0.333, 0, 0, 0.667, 9.742, 0, 1.167, 8.491, 0, 2, 11.251, 2, 5.567, 11.251, 0, 6.167, 6.571, 0, 6.767, 18, 2, 7.033, 18, 2, 7.967, 18, 2, 9.967, 18, 0, 10.267, 25.56, 0, 10.667, 17.903, 1, 10.856, 17.903, 11.044, 17.86, 11.233, 18, 1, 11.466, 18.173, 11.7, 23.4, 11.933, 23.4, 1, 12.122, 23.4, 12.311, 22.419, 12.5, 21.766, 1, 12.533, 21.651, 12.567, 21.903, 12.6, 21.66, 1, 12.844, 19.876, 13.089, 0, 13.333, 0, 0, 14, 9.18, 0, 14.633, 0, 0, 15.633, 6.571, 0, 16.567, 0, 2, 17.767, 0, 2, 18.1, 0]}, {"Target": "Parameter", "Id": "Param24", "Segments": [0, 0, 2, 0.333, 0, 0, 1.167, -10, 2, 2.033, -10, 2, 5.567, -10, 2, 7.033, -10, 2, 7.967, -10, 2, 9.967, -10, 2, 11.2, -10, 2, 12.5, -10, 0, 13.333, 0, 2, 14, 0, 2, 14.633, 0, 2, 16.567, 0, 2, 17.767, 0, 2, 18.1, 0]}, {"Target": "Parameter", "Id": "Param26", "Segments": [0, 0, 2, 0.333, 0, 0, 1.167, -13, 2, 2.033, -13, 2, 5.567, -13, 2, 7.033, -13, 2, 7.967, -13, 2, 9.967, -13, 2, 11.2, -13, 2, 12.5, -13, 0, 13.333, 0, 2, 14, 0, 2, 14.633, 0, 2, 16.567, 0, 2, 17.767, 0, 2, 18.1, 0]}, {"Target": "Parameter", "Id": "Param201", "Segments": [0, 0, 2, 0.333, 0, 2, 0.533, 0, 0, 0.567, 1, 2, 2.033, 1, 2, 5.567, 1, 2, 7.033, 1, 2, 7.967, 1, 2, 9.967, 1, 2, 12.5, 1, 2, 13.333, 1, 2, 14, 1, 2, 14.633, 1, 2, 16.867, 1, 0, 16.9, 0, 2, 18.1, 0]}, {"Target": "Parameter", "Id": "Param209", "Segments": [0, 0, 2, 0.333, 0, 2, 0.533, 0, 0, 0.567, 1, 2, 2.033, 1, 2, 5.567, 1, 2, 7.033, 1, 2, 7.967, 1, 2, 9.967, 1, 2, 12.5, 1, 2, 13.333, 1, 2, 14, 1, 2, 14.633, 1, 2, 16.867, 1, 0, 16.9, 0, 2, 18.1, 0]}, {"Target": "Parameter", "Id": "Param213", "Segments": [0, 0, 2, 0.333, 0, 2, 0.533, 0, 2, 2.433, 0, 0, 2.467, 1, 2, 5.8, 1, 0, 5.833, 0, 2, 7.033, 0, 2, 7.967, 0, 2, 9.967, 0, 2, 12.5, 0, 2, 13.333, 0, 2, 14, 0, 2, 14.633, 0, 2, 16.867, 0, 2, 16.9, 0, 2, 18.1, 0]}, {"Target": "Parameter", "Id": "Param215", "Segments": [0, 0, 2, 0.333, 0, 2, 0.533, 0, 0, 0.567, 1, 2, 2.433, 1, 0, 2.467, 0, 2, 5.8, 0, 0, 5.833, 1, 2, 7.033, 1, 2, 7.967, 1, 2, 9.967, 1, 2, 12.5, 1, 2, 13.333, 1, 2, 14, 1, 2, 14.633, 1, 2, 16.867, 1, 0, 16.9, 0, 2, 18.1, 0]}, {"Target": "Parameter", "Id": "Param9", "Segments": [0, 0, 2, 0.333, 0, 0, 1.167, -3.84, 1, 1.456, -3.84, 1.744, -2.91, 2.033, -2.04, 1, 2.322, -1.17, 2.611, -1.016, 2.9, -1.016, 0, 3.8, -4.02, 0, 4.633, -2.52, 2, 5.567, -2.52, 0, 6.067, -4.034, 0, 6.767, -1.92, 2, 7.033, -1.92, 0, 7.967, -2.22, 2, 9.967, -2.22, 2, 12.5, -2.22, 0, 12.9, -5.677, 0, 13.333, 0, 2, 14, 0, 2, 14.633, 0, 2, 16.567, 0, 0, 17.633, 0.3, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param10", "Segments": [0, 0, 2, 0.333, 0, 1, 0.433, 0, 0.533, 4.981, 0.633, 15.297, 1, 0.644, 16.443, 0.656, 17.22, 0.667, 17.22, 2, 1.167, 17.22, 2, 2.033, 17.22, 2, 5.567, 17.22, 2, 7.033, 17.22, 2, 7.967, 17.22, 2, 9.967, 17.22, 2, 12.5, 17.22, 1, 12.622, 17.22, 12.745, 16.202, 12.867, 11.414, 1, 13.022, 5.32, 13.178, 0, 13.333, 0, 2, 14, 0, 2, 14.633, 0, 2, 16.567, 0, 0, 16.867, 8.986, 1, 16.878, 8.986, 16.889, 7.581, 16.9, 7.273, 1, 16.944, 6.039, 16.989, 4.654, 17.033, 4.067, 1, 17.278, 0.84, 17.522, -0.42, 17.767, -0.42, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param11", "Segments": [0, 0, 2, 0.333, 0, 0, 1.167, 8, 2, 2.033, 8, 2, 5.567, 8, 2, 7.033, 8, 2, 7.967, 8, 2, 9.967, 8, 2, 12.5, 8, 0, 13.333, 0, 2, 14, 0, 2, 14.633, 0, 2, 16.567, 0, 0, 17.033, 12.45, 1, 17.189, 12.45, 17.344, 11.445, 17.5, 9.28, 1, 17.622, 7.579, 17.745, 6.252, 17.867, 4.013, 1, 17.945, 2.588, 18.022, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param12", "Segments": [0, 0, 2, 0.333, 0, 0, 0.633, -30, 0, 1.167, -17, 2, 2.033, -17, 2, 5.567, -17, 2, 7.033, -17, 2, 7.967, -17, 2, 9.967, -17, 2, 12.5, -17, 0, 13.333, 0, 2, 14, 0, 2, 14.633, 0, 2, 16.567, 0, 0, 17.433, 19, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param197", "Segments": [0, 0, 2, 0.333, 0, 2, 2.033, 0, 2, 5.567, 0, 2, 7.033, 0, 2, 7.967, 0, 2, 9.967, 0, 2, 12.5, 0, 2, 13.333, 0, 2, 14, 0, 2, 14.633, 0, 2, 16.567, 0, 0, 17.367, -6, 0, 17.767, 0, 2, 18.1, 0]}, {"Target": "Parameter", "Id": "Param198", "Segments": [0, 0, 2, 0.333, 0, 2, 2.033, 0, 2, 5.567, 0, 2, 7.033, 0, 2, 7.967, 0, 2, 9.967, 0, 2, 12.5, 0, 2, 13.333, 0, 2, 14, 0, 2, 14.633, 0, 2, 16.567, 0, 0, 17.367, 24, 0, 17.767, 0, 2, 18.1, 0]}, {"Target": "Parameter", "Id": "Param22", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, 1, 2, 17.767, 1, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param13", "Segments": [0, 0, 2, 0.333, 0, 2, 2.033, 0, 0, 3, -4.14, 2, 5.567, -4.14, 0, 6.267, -7.963, 0, 6.767, -6.207, 2, 7.033, -6.207, 0, 7.967, -1.827, 2, 9.967, -1.827, 2, 12.5, -1.827, 0, 13.333, 0, 2, 14, 0, 2, 14.633, 0, 2, 16.567, 0, 2, 17.767, 0, 2, 18.1, 0]}, {"Target": "Parameter", "Id": "Param14", "Segments": [0, 0, 2, 0.333, 0, 1, 0.433, 0, 0.533, -9.609, 0.633, -13, 1, 0.811, -19.029, 0.989, -20, 1.167, -20, 2, 2.033, -20, 2, 5.567, -20, 2, 7.033, -20, 2, 7.967, -20, 2, 9.967, -20, 2, 12.5, -20, 0, 13.333, 0, 2, 14, 0, 2, 14.633, 0, 2, 16.567, 0, 0, 16.967, -14.34, 0, 17.767, 0, 2, 18.1, 0]}, {"Target": "Parameter", "Id": "Param210", "Segments": [0, 0, 2, 0.333, 0, 1, 0.344, 0, 0.356, 26, 0.367, 26, 1, 0.434, 26, 0.5, 12.848, 0.567, 7.523, 1, 0.667, -0.464, 0.767, -1.487, 0.867, -1.487, 0, 1.167, 0, 1, 1.3, 0, 1.434, -1.694, 1.567, -1.98, 1, 1.722, -2.313, 1.878, -2.072, 2.033, -2.58, 1, 2.322, -3.524, 2.611, -11.88, 2.9, -11.88, 0, 3.8, -10.257, 0, 4.633, -11.82, 2, 5.567, -11.82, 1, 5.745, -11.82, 5.922, -4.22, 6.1, -2.608, 1, 6.322, -0.593, 6.545, -0.66, 6.767, -0.66, 2, 7.033, -0.66, 1, 7.255, -0.66, 7.478, -0.28, 7.7, 0.024, 1, 7.878, 0.267, 8.055, 0.338, 8.233, 0.54, 1, 8.611, 0.969, 8.989, 1.242, 9.367, 1.242, 2, 9.967, 1.242, 0, 10.2, 2.221, 1, 10.4, 2.221, 10.6, 0.875, 10.8, 0.566, 1, 11.367, -0.308, 11.933, -0.48, 12.5, -0.48, 0, 12.933, 2.1, 0, 13.333, 0, 1, 13.555, 0, 13.778, 0.564, 14, 1.26, 1, 14.211, 1.921, 14.422, 2.1, 14.633, 2.1, 2, 16.567, 2.1, 1, 16.7, 2.1, 16.834, 9.386, 16.967, 23, 1, 16.989, 25.269, 17.011, 26, 17.033, 26, 0, 17.767, 0, 2, 18.1, 0]}, {"Target": "Parameter", "Id": "Param211", "Segments": [0, 0, 2, 0.333, 0, 0, 0.567, 14.381, 0, 1.167, -16.92, 0, 1.467, -11.724, 0, 2.033, -23.317, 0, 2.4, -8, 0, 2.9, -12.32, 2, 5.567, -12.32, 0, 6.033, -20, 0, 6.4, -8, 0, 6.767, -13.46, 2, 7.033, -13.46, 0, 7.667, -9.68, 1, 7.911, -9.68, 8.156, -11.563, 8.4, -13.46, 1, 8.933, -17.598, 9.467, -19.04, 10, -19.04, 0, 10.8, 18.992, 1, 11.367, 18.992, 11.933, 9.118, 12.5, -13.46, 1, 12.556, -15.674, 12.611, -20, 12.667, -20, 0, 13.167, 8.462, 1, 13.222, 8.462, 13.278, 1.754, 13.333, 0, 1, 13.555, -7.017, 13.778, -9.18, 14, -9.18, 0, 14.633, -2.62, 2, 16.567, -2.62, 0, 17.767, 0, 2, 18.1, 0]}, {"Target": "Parameter", "Id": "Param228", "Segments": [0, 0, 2, 0.333, 0, 2, 2.033, 0, 1, 2.166, 0, 2.3, 0.073, 2.433, -0.217, 1, 2.578, -0.533, 2.722, -10.217, 2.867, -10.217, 0, 3.367, 1.438, 0, 3.8, -7.81, 0, 4.2, 2.051, 0, 4.633, -7.069, 0, 5.6, -2.596, 0, 6.333, -9.615, 1, 6.478, -9.615, 6.622, -5.108, 6.767, -3.672, 1, 6.856, -2.788, 6.944, -3, 7.033, -3, 0, 7.967, -4.44, 2, 9.967, -4.44, 2, 12.5, -4.44, 0, 13.333, 0, 2, 14, 0, 2, 14.633, 0, 2, 16.567, 0, 2, 17.767, 0, 2, 18.1, 0]}, {"Target": "Parameter", "Id": "Param221", "Segments": [0, 0, 2, 0.333, 0, 2, 0.6, 0, 0, 0.633, 1, 2, 2.033, 1, 2, 5.567, 1, 2, 7.033, 1, 2, 7.967, 1, 2, 9.967, 1, 2, 12.733, 1, 0, 12.767, 0, 2, 14, 0, 2, 14.633, 0, 2, 16.567, 0, 2, 18.1, 0]}, {"Target": "Parameter", "Id": "Param223", "Segments": [0, 0, 2, 0.333, 0, 2, 0.6, 0, 0, 0.633, 1, 2, 2.033, 1, 2, 5.567, 1, 2, 7.033, 1, 2, 7.967, 1, 2, 9.967, 1, 2, 12.733, 1, 0, 12.767, 0, 2, 14, 0, 2, 14.633, 0, 2, 16.567, 0, 2, 18.1, 0]}, {"Target": "Parameter", "Id": "Param240", "Segments": [0, 0, 2, 0.333, 0, 2, 0.6, 0, 0, 0.633, 1, 2, 2.4, 1, 2, 5.867, 1, 0, 5.9, 0, 2, 7.033, 0, 2, 7.967, 0, 2, 9.967, 0, 2, 12.733, 0, 2, 12.767, 0, 2, 14, 0, 2, 14.633, 0, 2, 16.567, 0, 2, 18.1, 0]}, {"Target": "Parameter", "Id": "Param241", "Segments": [0, 0, 2, 0.333, 0, 2, 0.6, 0, 2, 2.033, 0, 2, 5.867, 0, 0, 5.9, 1, 2, 7.033, 1, 2, 7.967, 1, 2, 9.967, 1, 2, 12.733, 1, 0, 12.767, 0, 2, 14, 0, 2, 14.633, 0, 2, 16.567, 0, 2, 18.1, 0]}, {"Target": "Parameter", "Id": "Param15", "Segments": [0, 0, 2, 0.333, 0, 1, 0.455, 0, 0.578, 4.548, 0.7, 5.419, 1, 0.856, 6.527, 1.011, 6.48, 1.167, 6.48, 0, 2.033, 4.92, 1, 2.322, 4.92, 2.611, 5.248, 2.9, 6.36, 1, 3.167, 7.387, 3.433, 8.22, 3.7, 8.22, 0, 4.633, 5.28, 1, 4.955, 5.28, 5.278, 5.434, 5.6, 6.36, 1, 5.722, 6.711, 5.845, 8.835, 5.967, 10.77, 1, 6.045, 12.001, 6.122, 14.763, 6.2, 14.763, 0, 6.767, 8.82, 2, 7.033, 8.82, 0, 7.967, 9.24, 2, 9.967, 9.24, 1, 10.067, 9.24, 10.167, 9.267, 10.267, 9.2, 1, 10.434, 9.088, 10.6, 7.342, 10.767, 7.342, 1, 11.345, 7.342, 11.922, 7.847, 12.5, 9.24, 1, 12.567, 9.401, 12.633, 11.316, 12.7, 11.316, 0, 13.333, 0, 2, 14, 0, 2, 14.633, 0, 2, 16.567, 0, 0, 17.033, -4.342, 1, 17.333, -4.342, 17.633, -2.938, 17.933, -0.525, 1, 17.989, -0.078, 18.044, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param16", "Segments": [0, 0, 2, 0.333, 0, 0, 0.5, -3.445, 1, 0.533, -3.445, 0.567, 0.935, 0.6, 2.123, 1, 0.789, 8.854, 0.978, 11.28, 1.167, 11.28, 2, 2.033, 11.28, 2, 5.6, 11.28, 0, 6.767, 3.12, 2, 7.033, 3.12, 2, 7.967, 3.12, 2, 9.967, 3.12, 2, 12.5, 3.12, 0, 12.733, -18.477, 1, 12.744, -18.477, 12.756, -10.716, 12.767, -10.295, 1, 12.956, -3.132, 13.144, 0, 13.333, 0, 0, 13.733, -2.34, 0, 14.233, 1.2, 0, 14.633, 0, 2, 16.567, 0, 0, 16.933, 3.808, 1, 17.078, 3.808, 17.222, 3.019, 17.367, 1.163, 1, 17.467, -0.122, 17.567, -0.924, 17.667, -0.924, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param17", "Segments": [0, 0, 2, 0.333, 0, 2, 2.033, 0, 2, 5.6, 0, 2, 7.033, 0, 2, 7.967, 0, 2, 9.967, 0, 2, 12.5, 0, 0, 12.733, 22.919, 1, 12.755, 22.919, 12.778, 19.588, 12.8, 18, 1, 12.978, 5.298, 13.155, 0, 13.333, 0, 0, 13.867, 13.74, 0, 14.467, -4, 0, 15.033, 5.18, 0, 15.733, 1.52, 2, 16.567, 1.52, 0, 17.333, -2.729, 0, 17.9, 2.689, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param18", "Segments": [0, 0, 2, 0.333, 0, 2, 2.033, 0, 2, 5.6, 0, 2, 7.033, 0, 2, 7.967, 0, 2, 9.967, 0, 2, 12.5, 0, 0, 12.8, 30, 0, 13.333, 0, 0, 13.967, 15, 0, 14.6, -11, 0, 14.767, 0, 2, 16.567, 0, 0, 16.8, 11.55, 0, 17.267, -21.904, 0, 17.867, 5.01, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param19", "Segments": [0, 0, 2, 0.333, 0, 0, 1.167, -37, 2, 2.033, -37, 2, 5.6, -37, 2, 7.033, -37, 2, 7.967, -37, 2, 9.967, -37, 2, 12.5, -37, 0, 13.333, 0, 2, 14, 0, 2, 14.633, 0, 2, 16.567, 0, 2, 17.767, 0, 2, 18.1, 0]}, {"Target": "Parameter", "Id": "Param20", "Segments": [0, 0, 2, 0.333, 0, 2, 0.5, 0, 0, 0.633, -5.349, 0, 2.033, 0, 2, 5.6, 0, 2, 7.033, 0, 2, 7.967, 0, 2, 9.967, 0, 2, 12.5, 0, 2, 13.333, 0, 2, 14, 0, 2, 14.633, 0, 2, 16.567, 0, 2, 17.767, 0, 2, 18.1, 0]}, {"Target": "Parameter", "Id": "Param233", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, 18.24, 1, 0.478, 18.24, 0.555, 14.37, 0.633, 6.666, 1, 0.644, 5.565, 0.656, 3.551, 0.667, 3.399, 1, 0.734, 2.486, 0.8, 1.571, 0.867, 0.963, 1, 1, -0.253, 1.134, -0.66, 1.267, -0.66, 1, 1.356, -0.66, 1.444, -0.119, 1.533, 0, 1, 1.7, 0.222, 1.866, 0.099, 2.033, 0.48, 1, 2.322, 1.14, 2.611, 5.52, 2.9, 5.52, 0, 3.7, 4.26, 0, 4.633, 5.94, 2, 5.6, 5.94, 1, 5.822, 5.94, 6.045, -10.347, 6.267, -11.773, 1, 6.434, -12.842, 6.6, -12.54, 6.767, -12.54, 2, 7.033, -12.54, 0, 7.967, -13.08, 2, 9.967, -13.08, 0, 10.267, -13.44, 0, 10.767, -12.434, 1, 11.345, -12.434, 11.922, -12.568, 12.5, -13.08, 1, 12.611, -13.178, 12.722, -30, 12.833, -30, 0, 13.333, 0, 2, 14, 0, 2, 14.633, 0, 2, 16.567, 0, 2, 18.1, 0]}, {"Target": "Parameter", "Id": "Param234", "Segments": [0, 0, 2, 0.333, 0, 0, 1.033, 9.429, 0, 1.267, 8.175, 0, 2.033, 17.127, 0, 2.367, 5.972, 0, 3, 22, 0, 3.833, 9.402, 0, 4.7, 19.002, 0, 5.6, 9.402, 0, 5.933, 22, 0, 6.433, -6.462, 0, 6.767, 9.825, 2, 7.033, 9.825, 0, 7.967, 8.1, 2, 9.967, 8.1, 1, 10.056, 8.1, 10.144, 6.951, 10.233, 5.625, 1, 10.411, 2.973, 10.589, 1.997, 10.767, 1.997, 0, 12.5, 8.1, 0, 13.333, 0, 2, 14, 0, 2, 14.633, 0, 2, 16.567, 0, 2, 18.1, 0]}, {"Target": "Parameter", "Id": "Param235", "Segments": [0, 0, 2, 0.333, 0, 0, 0.6, -27.925, 1, 0.667, -27.925, 0.733, -17.58, 0.8, -16.77, 1, 0.956, -14.88, 1.111, -14.64, 1.267, -14.64, 2, 2.033, -14.64, 0, 2.433, -8.46, 0, 2.933, -14.64, 0, 3.333, -7.178, 0, 3.767, -15.595, 0, 4.3, -9.53, 0, 4.667, -14.64, 2, 5.6, -14.64, 0, 6.2, -29, 0, 6.767, -24, 2, 7.033, -24, 2, 7.967, -24, 2, 9.967, -24, 2, 12.5, -24, 0, 13.333, 0, 2, 14, 0, 2, 14.633, 0, 2, 16.567, 0, 2, 18.1, 0]}, {"Target": "Parameter", "Id": "Param274", "Segments": [0, 0, 2, 0.333, 0, 0, 0.533, -0.7, 2, 1.033, -0.7, 2, 2.033, -0.7, 2, 5.6, -0.7, 2, 7.033, -0.7, 2, 7.967, -0.7, 2, 9.967, -0.7, 2, 12.5, -0.7, 2, 16.567, -0.7, 0, 16.9, 0, 2, 18.1, 0]}, {"Target": "Parameter", "Id": "Param277", "Segments": [0, 0, 2, 0.333, 0, 0, 0.6, 12.46, 0, 1.133, -9.549, 0, 1.567, 12.46, 0, 1.967, -9.549, 0, 2.4, 12.46, 0, 2.8, -9.549, 0, 3.233, 12.46, 0, 3.633, -9.549, 0, 4.067, 12.46, 0, 4.467, -9.549, 0, 4.9, 12.46, 0, 5.3, -9.549, 0, 5.733, 12.46, 0, 6.133, -9.549, 0, 6.567, 12.46, 0, 6.967, -9.549, 0, 7.4, 12.46, 0, 7.8, -9.549, 0, 8.233, 12.46, 0, 8.633, -9.549, 0, 9.333, 12.46, 0, 9.867, -9.549, 0, 10.3, 12.46, 0, 10.7, -9.549, 0, 11.133, 12.46, 0, 11.533, -9.549, 0, 11.967, 12.46, 0, 12.367, -9.549, 0, 12.8, 12.46, 0, 13.2, -9.549, 0, 13.633, 12.46, 0, 14.033, -9.549, 0, 14.467, 12.46, 0, 14.867, -9.549, 0, 15.3, 12.46, 0, 15.7, -9.549, 0, 16.133, 12.46, 0, 16.533, -9.549, 0, 16.967, 12.46, 0, 17.367, -9.549, 0, 17.767, 7.671, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param278", "Segments": [0, 0, 2, 0.333, 0, 0, 0.667, 0.5, 0, 1.167, -0.7, 0, 2, 0.6, 0, 2.667, -0.895, 0, 3.4, 1, 0, 4.567, -0.99, 0, 5.567, 0.367, 0, 6.267, -0.5, 0, 6.933, 0.701, 0, 7.7, -0.7, 0, 8.367, 0.822, 0, 9.167, -0.647, 0, 9.933, 0.822, 0, 10.867, -0.895, 0, 11.533, 0.918, 0, 12.267, -0.822, 0, 13, 0.701, 0, 13.933, -0.99, 0, 14.633, 0.701, 0, 15.4, -0.647, 0, 16.2, 0.688, 0, 17.033, -0.647, 0, 17.833, 0.6, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param280", "Segments": [0, 0, 2, 0.333, 0, 0, 0.567, 0.376, 0, 0.833, -0.333, 0, 1.467, 0.467, 0, 2.3, -0.393, 0, 2.8, 0.376, 0, 3.567, -0.647, 0, 4.267, 0.533, 0, 4.9, -0.533, 0, 5.567, 0.367, 0, 6.267, -0.5, 0, 6.933, 0.701, 1, 7.222, 0.701, 7.511, 0.557, 7.8, 0, 1, 7.922, -0.236, 8.045, -0.647, 8.167, -0.647, 0, 8.933, 0.6, 0, 9.8, -0.5, 0, 10.433, 0.502, 0, 11.367, -0.647, 0, 12.533, 0.701, 0, 13.567, -0.99, 0, 14.8, 0.757, 0, 15.967, -0.647, 0, 17.033, 0.656, 0, 17.933, -0.281, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param7", "Segments": [0, 0, 2, 0.333, 0, 0, 1.567, -9, 0, 2.833, 0, 0, 4.1, -9, 0, 5.367, 0, 0, 6.7, -9, 0, 7.967, 0, 0, 9.233, -9, 0, 10.5, 0, 0, 11.767, -9, 1, 12.311, -9, 12.856, -6.862, 13.4, 0, 1, 13.5, 1.26, 13.6, 20.461, 13.7, 20.461, 0, 14.067, -12.167, 0, 14.467, 12.072, 0, 14.867, -8.485, 0, 15.3, 7.523, 0, 15.9, -4.986, 0, 16.4, 1.137, 0, 17.067, -4.111, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param66", "Segments": [0, 0, 2, 0.333, 0, 0, 1.567, -19, 0, 2.833, 0, 0, 4.1, -19, 0, 5.367, 0, 0, 6.7, -19, 0, 7.967, 0, 0, 9.233, -19, 0, 10.233, 0, 0, 10.533, -23, 0, 10.8, 16, 0, 11.067, -11, 0, 11.3, 4, 2, 11.467, 4, 0, 11.767, -19, 0, 13.1, 0, 0, 14.367, -19, 0, 15.633, 0, 0, 16.9, -19, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param67", "Segments": [0, 0, 2, 0.333, 0, 0, 0.9, 16, 0, 2.167, -16, 1, 2.389, -16, 2.611, -10.667, 2.833, 0, 1, 3.055, 10.667, 3.278, 16, 3.5, 16, 0, 4.767, -16, 1, 4.967, -16, 5.167, -10.073, 5.367, 0, 1, 5.589, 11.192, 5.811, 16, 6.033, 16, 0, 7.3, -16, 1, 7.522, -16, 7.745, -11.192, 7.967, 0, 1, 8.167, 10.073, 8.367, 16, 8.567, 16, 0, 9.9, -16, 1, 10.011, -16, 10.122, -11.195, 10.233, 0, 1, 10.3, 6.717, 10.366, 11, 10.433, 11, 0, 10.667, -18, 0, 10.9, 21, 0, 11.167, -12, 0, 11.467, 8, 0, 12.433, -16, 1, 12.655, -16, 12.878, -11.192, 13.1, 0, 1, 13.3, 10.073, 13.5, 16, 13.7, 16, 0, 15, -16, 1, 15.211, -16, 15.422, -10.385, 15.633, 0, 1, 15.855, 10.932, 16.078, 16, 16.3, 16, 0, 17.567, -16, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param68", "Segments": [0, 0, 2, 0.333, 0, 0, 1.567, 17, 0, 2.833, 0, 0, 4.1, 17, 0, 5.367, 0, 0, 6.7, 17, 0, 7.967, 0, 0, 9.233, 17, 0, 10.233, 0, 0, 10.533, 23, 0, 10.8, -16, 0, 11.067, 11, 0, 11.3, -4, 2, 11.467, -4, 0, 11.767, 17, 0, 13.1, 0, 0, 14.367, 17, 0, 15.633, 0, 0, 16.9, 17, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param69", "Segments": [0, 0, 2, 0.333, 0, 0, 0.9, -16, 0, 2.167, 16, 1, 2.389, 16, 2.611, 10.667, 2.833, 0, 1, 3.055, -10.667, 3.278, -16, 3.5, -16, 0, 4.767, 16, 1, 4.967, 16, 5.167, 10.073, 5.367, 0, 1, 5.589, -11.192, 5.811, -16, 6.033, -16, 0, 7.3, 16, 1, 7.522, 16, 7.745, 11.192, 7.967, 0, 1, 8.167, -10.073, 8.367, -16, 8.567, -16, 0, 9.9, 16, 1, 10.011, 16, 10.122, 11.195, 10.233, 0, 1, 10.3, -6.717, 10.366, -11, 10.433, -11, 0, 10.667, 18, 0, 10.9, -21, 0, 11.167, 12, 0, 11.467, -8, 0, 12.433, 16, 1, 12.655, 16, 12.878, 11.192, 13.1, 0, 1, 13.3, -10.073, 13.5, -16, 13.7, -16, 0, 15, 16, 1, 15.211, 16, 15.422, 10.385, 15.633, 0, 1, 15.855, -10.932, 16.078, -16, 16.3, -16, 0, 17.567, 16, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param35", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.567, -7.485, 0, 0.833, 16.408, 0, 1.267, -11.475, 0, 1.767, 7.307, 0, 2.1, 2.229, 0, 2.167, 2.348, 0, 2.6, -12.099, 0, 3.067, 11.711, 0, 3.467, -2.883, 0, 3.667, -0.932, 0, 4.033, -3.926, 0, 4.133, -3.722, 0, 4.167, -3.759, 0, 4.567, 10.823, 0, 5.1, -7.246, 0, 5.7, 2.322, 0, 6.6, -3.72, 0, 7.1, 3.916, 0, 7.433, 0.888, 0, 7.967, 4.516, 0, 8.5, -7.181, 0, 9.167, 5.768, 0, 10.033, -4.751, 0, 10.667, 2.37, 0, 11.7, -2.465, 0, 12.167, -1.7, 0, 12.333, -2.336, 0, 12.733, 2.758, 0, 13.1, 1.685, 0, 13.467, 2.229, 0, 14.2, -5.721, 0, 14.9, 2.913, 0, 15.233, 2.244, 0, 15.267, 2.247, 0, 15.3, 2.232, 0, 15.333, 2.241, 0, 16.3, -3.175, 0, 16.8, 11.336, 0, 17.1, -7.595, 0, 17.533, 3.107, 0, 17.9, 0.321, 0, 18, 0.589, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param36", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.533, 4.835, 0, 0.767, -14.348, 0, 1.067, 16.736, 0, 1.5, -13.473, 0, 1.933, 8.247, 0, 2.267, -2.487, 0, 2.5, 4.535, 0, 2.867, -10.414, 0, 3.267, 12.099, 0, 3.633, -7.22, 0, 4, 3.345, 0, 4.467, -7.208, 0, 4.833, 10.047, 0, 5.267, -6.675, 0, 5.8, 2.328, 0, 6.233, -1.062, 0, 6.533, 1.752, 0, 6.9, -3.186, 0, 7.3, 3.43, 0, 7.667, -2.508, 0, 8.267, 4.58, 0, 8.7, -4.492, 0, 9.333, 2.958, 0, 9.733, -0.594, 0, 10, 1.298, 0, 10.3, -3.088, 0, 10.733, 1.219, 0, 11.133, -0.312, 0, 11.5, 1.2, 0, 11.867, -0.976, 0, 12.3, 0.897, 0, 12.567, -2.487, 0, 12.933, 1.78, 0, 13.333, -0.95, 0, 13.767, 1.944, 0, 14.467, -2.032, 0, 15, 1.586, 0, 15.4, -0.67, 0, 15.833, 0.857, 0, 16.5, -0.865, 0, 16.6, -0.715, 0, 16.767, -7.877, 0, 17, 12.834, 0, 17.367, -10.108, 0, 17.733, 5.688, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param37", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.567, -16.053, 0, 0.9, 29.42, 0, 1.333, -23.007, 0, 2.233, 17.903, 0, 2.933, -12.254, 0, 3.567, 14.302, 0, 4.033, 3.716, 0, 4.067, 3.739, 0, 4.333, 0.673, 0, 4.5, 3.092, 0, 4.8, -9.994, 0, 5.8, 5.973, 0, 6.533, -12.002, 0, 7.167, 11.55, 0, 7.967, -16.009, 0, 8.533, 11.544, 0, 8.867, 5.365, 0, 8.9, 5.375, 0, 9.433, -12.449, 0, 10.167, 14.604, 0, 10.567, 0.656, 0, 10.7, 0.898, 0, 11.133, -18.258, 0, 11.733, 16.483, 0, 12.467, -14.399, 0, 13.233, 11.731, 0, 14.233, -16.642, 0, 14.8, 13.431, 0, 15.633, -10.736, 0, 16.433, 9.683, 0, 16.6, 7.399, 0, 16.767, 16.033, 0, 17.133, -16.521, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param39", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.533, 7.141, 0, 0.767, -19.515, 0, 1.067, 22.91, 0, 1.433, -17.531, 0, 1.767, 7.931, 0, 2.133, -7.069, 0, 2.4, 12.745, 0, 2.7, -6.896, 0, 2.967, 2.201, 0, 3.467, -4.482, 0, 3.733, 6.915, 0, 4.033, -3.584, 0, 4.3, 2.186, 0, 4.5, -1.823, 0, 4.7, 5.521, 0, 4.967, -5.583, 0, 5.267, 2.75, 0, 5.567, -2.53, 0, 5.933, 2.471, 0, 6.167, 0.567, 0, 6.4, 1.92, 0, 6.7, -5.201, 0, 7.033, 1.037, 0, 7.067, 0.982, 0, 7.3, 2.193, 0, 7.6, -0.463, 0, 7.867, 4.862, 0, 8.133, -8.232, 0, 8.633, 4.268, 0, 8.933, -2.499, 0, 9.233, 4.582, 0, 9.533, -5.142, 0, 9.833, 0.499, 0, 10.1, -2.727, 0, 10.333, 6.406, 0, 10.633, -4.132, 0, 11, 6.458, 0, 11.3, -9.432, 0, 11.867, 4.954, 0, 12.167, -1.493, 0, 12.367, 3.793, 0, 12.633, -5.38, 0, 12.867, 0.581, 0, 13.1, -2.471, 0, 13.333, 3.384, 0, 13.6, -0.877, 0, 13.933, 2.495, 0, 13.967, 2.422, 0, 14.067, 2.947, 0, 14.4, -6.55, 0, 14.933, 4.549, 0, 15.233, -1.209, 0, 15.5, 2.533, 0, 15.8, -3.599, 0, 16.067, 0.391, 0, 16.3, -1.757, 0, 16.6, 2.919, 0, 16.767, -5.489, 0, 16.967, 13.903, 0, 17.233, -10.583, 0, 17.533, 4.251, 0, 17.833, -4.374, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param38", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.567, -16.053, 0, 0.9, 29.42, 0, 1.333, -23.007, 0, 2.233, 17.903, 0, 2.933, -12.254, 0, 3.567, 14.302, 0, 4.033, 3.716, 0, 4.067, 3.739, 0, 4.333, 0.673, 0, 4.5, 3.092, 0, 4.8, -9.994, 0, 5.8, 5.973, 0, 6.533, -12.002, 0, 7.167, 11.55, 0, 7.967, -16.009, 0, 8.533, 11.544, 0, 8.867, 5.365, 0, 8.9, 5.375, 0, 9.433, -12.449, 0, 10.167, 14.604, 0, 10.567, 0.656, 0, 10.7, 0.898, 0, 11.133, -18.258, 0, 11.733, 16.483, 0, 12.467, -14.399, 0, 13.233, 11.731, 0, 14.233, -16.642, 0, 14.8, 13.431, 0, 15.633, -10.736, 0, 16.433, 9.683, 0, 16.6, 7.399, 0, 16.767, 16.033, 0, 17.133, -16.521, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param40", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.533, 7.141, 0, 0.767, -19.515, 0, 1.067, 22.91, 0, 1.433, -17.531, 0, 1.767, 7.931, 0, 2.133, -7.069, 0, 2.4, 12.745, 0, 2.7, -6.896, 0, 2.967, 2.201, 0, 3.467, -4.482, 0, 3.733, 6.915, 0, 4.033, -3.584, 0, 4.3, 2.186, 0, 4.5, -1.823, 0, 4.7, 5.521, 0, 4.967, -5.583, 0, 5.267, 2.75, 0, 5.567, -2.53, 0, 5.933, 2.471, 0, 6.167, 0.567, 0, 6.4, 1.92, 0, 6.7, -5.201, 0, 7.033, 1.037, 0, 7.067, 0.982, 0, 7.3, 2.193, 0, 7.6, -0.463, 0, 7.867, 4.862, 0, 8.133, -8.232, 0, 8.633, 4.268, 0, 8.933, -2.499, 0, 9.233, 4.582, 0, 9.533, -5.142, 0, 9.833, 0.499, 0, 10.1, -2.727, 0, 10.333, 6.406, 0, 10.633, -4.132, 0, 11, 6.458, 0, 11.3, -9.432, 0, 11.867, 4.954, 0, 12.167, -1.493, 0, 12.367, 3.793, 0, 12.633, -5.38, 0, 12.867, 0.581, 0, 13.1, -2.471, 0, 13.333, 3.384, 0, 13.6, -0.877, 0, 13.933, 2.495, 0, 13.967, 2.422, 0, 14.067, 2.947, 0, 14.4, -6.55, 0, 14.933, 4.549, 0, 15.233, -1.209, 0, 15.5, 2.533, 0, 15.8, -3.599, 0, 16.067, 0.391, 0, 16.3, -1.757, 0, 16.6, 2.919, 0, 16.767, -5.489, 0, 16.967, 13.903, 0, 17.233, -10.583, 0, 17.533, 4.251, 0, 17.833, -4.374, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param41", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.567, -16.053, 0, 0.9, 29.42, 0, 1.333, -23.007, 0, 2.233, 17.903, 0, 2.933, -12.254, 0, 3.567, 14.302, 0, 4.033, 3.716, 0, 4.067, 3.739, 0, 4.333, 0.673, 0, 4.5, 3.092, 0, 4.8, -9.994, 0, 5.8, 5.973, 0, 6.533, -12.002, 0, 7.167, 11.55, 0, 7.967, -16.009, 0, 8.533, 11.544, 0, 8.867, 5.365, 0, 8.9, 5.375, 0, 9.433, -12.449, 0, 10.167, 14.604, 0, 10.567, 0.656, 0, 10.7, 0.898, 0, 11.133, -18.258, 0, 11.733, 16.483, 0, 12.467, -14.399, 0, 13.233, 11.731, 0, 14.233, -16.642, 0, 14.8, 13.431, 0, 15.633, -10.736, 0, 16.433, 9.683, 0, 16.6, 7.399, 0, 16.767, 16.033, 0, 17.133, -16.521, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param42", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.533, 7.141, 0, 0.767, -19.515, 0, 1.067, 22.91, 0, 1.433, -17.531, 0, 1.767, 7.931, 0, 2.133, -7.069, 0, 2.4, 12.745, 0, 2.7, -6.896, 0, 2.967, 2.201, 0, 3.467, -4.482, 0, 3.733, 6.915, 0, 4.033, -3.584, 0, 4.3, 2.186, 0, 4.5, -1.823, 0, 4.7, 5.521, 0, 4.967, -5.583, 0, 5.267, 2.75, 0, 5.567, -2.53, 0, 5.933, 2.471, 0, 6.167, 0.567, 0, 6.4, 1.92, 0, 6.7, -5.201, 0, 7.033, 1.037, 0, 7.067, 0.982, 0, 7.3, 2.193, 0, 7.6, -0.463, 0, 7.867, 4.862, 0, 8.133, -8.232, 0, 8.633, 4.268, 0, 8.933, -2.499, 0, 9.233, 4.582, 0, 9.533, -5.142, 0, 9.833, 0.499, 0, 10.1, -2.727, 0, 10.333, 6.406, 0, 10.633, -4.132, 0, 11, 6.458, 0, 11.3, -9.432, 0, 11.867, 4.954, 0, 12.167, -1.493, 0, 12.367, 3.793, 0, 12.633, -5.38, 0, 12.867, 0.581, 0, 13.1, -2.471, 0, 13.333, 3.384, 0, 13.6, -0.877, 0, 13.933, 2.495, 0, 13.967, 2.422, 0, 14.067, 2.947, 0, 14.4, -6.55, 0, 14.933, 4.549, 0, 15.233, -1.209, 0, 15.5, 2.533, 0, 15.8, -3.599, 0, 16.067, 0.391, 0, 16.3, -1.757, 0, 16.6, 2.919, 0, 16.767, -5.489, 0, 16.967, 13.903, 0, 17.233, -10.583, 0, 17.533, 4.251, 0, 17.833, -4.374, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param43", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.567, -16.053, 0, 0.9, 29.42, 0, 1.333, -23.007, 0, 2.233, 17.903, 0, 2.933, -12.254, 0, 3.567, 14.302, 0, 4.033, 3.716, 0, 4.067, 3.739, 0, 4.333, 0.673, 0, 4.5, 3.092, 0, 4.8, -9.994, 0, 5.8, 5.973, 0, 6.533, -12.002, 0, 7.167, 11.55, 0, 7.967, -16.009, 0, 8.533, 11.544, 0, 8.867, 5.365, 0, 8.9, 5.375, 0, 9.433, -12.449, 0, 10.167, 14.604, 0, 10.567, 0.656, 0, 10.7, 0.898, 0, 11.133, -18.258, 0, 11.733, 16.483, 0, 12.467, -14.399, 0, 13.233, 11.731, 0, 14.233, -16.642, 0, 14.8, 13.431, 0, 15.633, -10.736, 0, 16.433, 9.683, 0, 16.6, 7.399, 0, 16.767, 16.033, 0, 17.133, -16.521, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param44", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.533, 7.141, 0, 0.767, -19.515, 0, 1.067, 22.91, 0, 1.433, -17.531, 0, 1.767, 7.931, 0, 2.133, -7.069, 0, 2.4, 12.745, 0, 2.7, -6.896, 0, 2.967, 2.201, 0, 3.467, -4.482, 0, 3.733, 6.915, 0, 4.033, -3.584, 0, 4.3, 2.186, 0, 4.5, -1.823, 0, 4.7, 5.521, 0, 4.967, -5.583, 0, 5.267, 2.75, 0, 5.567, -2.53, 0, 5.933, 2.471, 0, 6.167, 0.567, 0, 6.4, 1.92, 0, 6.7, -5.201, 0, 7.033, 1.037, 0, 7.067, 0.982, 0, 7.3, 2.193, 0, 7.6, -0.463, 0, 7.867, 4.862, 0, 8.133, -8.232, 0, 8.633, 4.268, 0, 8.933, -2.499, 0, 9.233, 4.582, 0, 9.533, -5.142, 0, 9.833, 0.499, 0, 10.1, -2.727, 0, 10.333, 6.406, 0, 10.633, -4.132, 0, 11, 6.458, 0, 11.3, -9.432, 0, 11.867, 4.954, 0, 12.167, -1.493, 0, 12.367, 3.793, 0, 12.633, -5.38, 0, 12.867, 0.581, 0, 13.1, -2.471, 0, 13.333, 3.384, 0, 13.6, -0.877, 0, 13.933, 2.495, 0, 13.967, 2.422, 0, 14.067, 2.947, 0, 14.4, -6.55, 0, 14.933, 4.549, 0, 15.233, -1.209, 0, 15.5, 2.533, 0, 15.8, -3.599, 0, 16.067, 0.391, 0, 16.3, -1.757, 0, 16.6, 2.919, 0, 16.767, -5.489, 0, 16.967, 13.903, 0, 17.233, -10.583, 0, 17.533, 4.251, 0, 17.833, -4.374, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param45", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.567, -16.053, 0, 0.9, 29.42, 0, 1.333, -23.007, 0, 2.233, 17.903, 0, 2.933, -12.254, 0, 3.567, 14.302, 0, 4.033, 3.716, 0, 4.067, 3.739, 0, 4.333, 0.673, 0, 4.5, 3.092, 0, 4.8, -9.994, 0, 5.8, 5.973, 0, 6.533, -12.002, 0, 7.167, 11.55, 0, 7.967, -16.009, 0, 8.533, 11.544, 0, 8.867, 5.365, 0, 8.9, 5.375, 0, 9.433, -12.449, 0, 10.167, 14.604, 0, 10.567, 0.656, 0, 10.7, 0.898, 0, 11.133, -18.258, 0, 11.733, 16.483, 0, 12.467, -14.399, 0, 13.233, 11.731, 0, 14.233, -16.642, 0, 14.8, 13.431, 0, 15.633, -10.736, 0, 16.433, 9.683, 0, 16.6, 7.399, 0, 16.767, 16.033, 0, 17.133, -16.521, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param46", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.533, 7.141, 0, 0.767, -19.515, 0, 1.067, 22.91, 0, 1.433, -17.531, 0, 1.767, 7.931, 0, 2.133, -7.069, 0, 2.4, 12.745, 0, 2.7, -6.896, 0, 2.967, 2.201, 0, 3.467, -4.482, 0, 3.733, 6.915, 0, 4.033, -3.584, 0, 4.3, 2.186, 0, 4.5, -1.823, 0, 4.7, 5.521, 0, 4.967, -5.583, 0, 5.267, 2.75, 0, 5.567, -2.53, 0, 5.933, 2.471, 0, 6.167, 0.567, 0, 6.4, 1.92, 0, 6.7, -5.201, 0, 7.033, 1.037, 0, 7.067, 0.982, 0, 7.3, 2.193, 0, 7.6, -0.463, 0, 7.867, 4.862, 0, 8.133, -8.232, 0, 8.633, 4.268, 0, 8.933, -2.499, 0, 9.233, 4.582, 0, 9.533, -5.142, 0, 9.833, 0.499, 0, 10.1, -2.727, 0, 10.333, 6.406, 0, 10.633, -4.132, 0, 11, 6.458, 0, 11.3, -9.432, 0, 11.867, 4.954, 0, 12.167, -1.493, 0, 12.367, 3.793, 0, 12.633, -5.38, 0, 12.867, 0.581, 0, 13.1, -2.471, 0, 13.333, 3.384, 0, 13.6, -0.877, 0, 13.933, 2.495, 0, 13.967, 2.422, 0, 14.067, 2.947, 0, 14.4, -6.55, 0, 14.933, 4.549, 0, 15.233, -1.209, 0, 15.5, 2.533, 0, 15.8, -3.599, 0, 16.067, 0.391, 0, 16.3, -1.757, 0, 16.6, 2.919, 0, 16.767, -5.489, 0, 16.967, 13.903, 0, 17.233, -10.583, 0, 17.533, 4.251, 0, 17.833, -4.374, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param62", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.567, -16.053, 0, 0.9, 29.42, 0, 1.333, -23.007, 0, 2.233, 17.903, 0, 2.933, -12.254, 0, 3.567, 14.302, 0, 4.033, 3.716, 0, 4.067, 3.739, 0, 4.333, 0.673, 0, 4.5, 3.092, 0, 4.8, -9.994, 0, 5.8, 5.973, 0, 6.533, -12.002, 0, 7.167, 11.55, 0, 7.967, -16.009, 0, 8.533, 11.544, 0, 8.867, 5.365, 0, 8.9, 5.375, 0, 9.433, -12.449, 0, 10.167, 14.604, 0, 10.567, 0.656, 0, 10.7, 0.898, 0, 11.133, -18.258, 0, 11.733, 16.483, 0, 12.467, -14.399, 0, 13.233, 11.731, 0, 14.233, -16.642, 0, 14.8, 13.431, 0, 15.633, -10.736, 0, 16.433, 9.683, 0, 16.6, 7.399, 0, 16.767, 16.033, 0, 17.133, -16.521, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param63", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.533, 7.141, 0, 0.767, -19.515, 0, 1.067, 22.91, 0, 1.433, -17.531, 0, 1.767, 7.931, 0, 2.133, -7.069, 0, 2.4, 12.745, 0, 2.7, -6.896, 0, 2.967, 2.201, 0, 3.467, -4.482, 0, 3.733, 6.915, 0, 4.033, -3.584, 0, 4.3, 2.186, 0, 4.5, -1.823, 0, 4.7, 5.521, 0, 4.967, -5.583, 0, 5.267, 2.75, 0, 5.567, -2.53, 0, 5.933, 2.471, 0, 6.167, 0.567, 0, 6.4, 1.92, 0, 6.7, -5.201, 0, 7.033, 1.037, 0, 7.067, 0.982, 0, 7.3, 2.193, 0, 7.6, -0.463, 0, 7.867, 4.862, 0, 8.133, -8.232, 0, 8.633, 4.268, 0, 8.933, -2.499, 0, 9.233, 4.582, 0, 9.533, -5.142, 0, 9.833, 0.499, 0, 10.1, -2.727, 0, 10.333, 6.406, 0, 10.633, -4.132, 0, 11, 6.458, 0, 11.3, -9.432, 0, 11.867, 4.954, 0, 12.167, -1.493, 0, 12.367, 3.793, 0, 12.633, -5.38, 0, 12.867, 0.581, 0, 13.1, -2.471, 0, 13.333, 3.384, 0, 13.6, -0.877, 0, 13.933, 2.495, 0, 13.967, 2.422, 0, 14.067, 2.947, 0, 14.4, -6.55, 0, 14.933, 4.549, 0, 15.233, -1.209, 0, 15.5, 2.533, 0, 15.8, -3.599, 0, 16.067, 0.391, 0, 16.3, -1.757, 0, 16.6, 2.919, 0, 16.767, -5.489, 0, 16.967, 13.903, 0, 17.233, -10.583, 0, 17.533, 4.251, 0, 17.833, -4.374, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param64", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.567, -16.053, 0, 0.9, 29.42, 0, 1.333, -23.007, 0, 2.233, 17.903, 0, 2.933, -12.254, 0, 3.567, 14.302, 0, 4.033, 3.716, 0, 4.067, 3.739, 0, 4.333, 0.673, 0, 4.5, 3.092, 0, 4.8, -9.994, 0, 5.8, 5.973, 0, 6.533, -12.002, 0, 7.167, 11.55, 0, 7.967, -16.009, 0, 8.533, 11.544, 0, 8.867, 5.365, 0, 8.9, 5.375, 0, 9.433, -12.449, 0, 10.167, 14.604, 0, 10.567, 0.656, 0, 10.7, 0.898, 0, 11.133, -18.258, 0, 11.733, 16.483, 0, 12.467, -14.399, 0, 13.233, 11.731, 0, 14.233, -16.642, 0, 14.8, 13.431, 0, 15.633, -10.736, 0, 16.433, 9.683, 0, 16.6, 7.399, 0, 16.767, 16.033, 0, 17.133, -16.521, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param65", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.533, 7.141, 0, 0.767, -19.515, 0, 1.067, 22.91, 0, 1.433, -17.531, 0, 1.767, 7.931, 0, 2.133, -7.069, 0, 2.4, 12.745, 0, 2.7, -6.896, 0, 2.967, 2.201, 0, 3.467, -4.482, 0, 3.733, 6.915, 0, 4.033, -3.584, 0, 4.3, 2.186, 0, 4.5, -1.823, 0, 4.7, 5.521, 0, 4.967, -5.583, 0, 5.267, 2.75, 0, 5.567, -2.53, 0, 5.933, 2.471, 0, 6.167, 0.567, 0, 6.4, 1.92, 0, 6.7, -5.201, 0, 7.033, 1.037, 0, 7.067, 0.982, 0, 7.3, 2.193, 0, 7.6, -0.463, 0, 7.867, 4.862, 0, 8.133, -8.232, 0, 8.633, 4.268, 0, 8.933, -2.499, 0, 9.233, 4.582, 0, 9.533, -5.142, 0, 9.833, 0.499, 0, 10.1, -2.727, 0, 10.333, 6.406, 0, 10.633, -4.132, 0, 11, 6.458, 0, 11.3, -9.432, 0, 11.867, 4.954, 0, 12.167, -1.493, 0, 12.367, 3.793, 0, 12.633, -5.38, 0, 12.867, 0.581, 0, 13.1, -2.471, 0, 13.333, 3.384, 0, 13.6, -0.877, 0, 13.933, 2.495, 0, 13.967, 2.422, 0, 14.067, 2.947, 0, 14.4, -6.55, 0, 14.933, 4.549, 0, 15.233, -1.209, 0, 15.5, 2.533, 0, 15.8, -3.599, 0, 16.067, 0.391, 0, 16.3, -1.757, 0, 16.6, 2.919, 0, 16.767, -5.489, 0, 16.967, 13.903, 0, 17.233, -10.583, 0, 17.533, 4.251, 0, 17.833, -4.374, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param72", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.567, -16.053, 0, 0.9, 29.42, 0, 1.333, -23.007, 0, 2.233, 17.903, 0, 2.933, -12.254, 0, 3.567, 14.302, 0, 4.033, 3.716, 0, 4.067, 3.739, 0, 4.333, 0.673, 0, 4.5, 3.092, 0, 4.8, -9.994, 0, 5.8, 5.973, 0, 6.533, -12.002, 0, 7.167, 11.55, 0, 7.967, -16.009, 0, 8.533, 11.544, 0, 8.867, 5.365, 0, 8.9, 5.375, 0, 9.433, -12.449, 0, 10.167, 14.604, 0, 10.567, 0.656, 0, 10.7, 0.898, 0, 11.133, -18.258, 0, 11.733, 16.483, 0, 12.467, -14.399, 0, 13.233, 11.731, 0, 14.233, -16.642, 0, 14.8, 13.431, 0, 15.633, -10.736, 0, 16.433, 9.683, 0, 16.6, 7.399, 0, 16.767, 16.033, 0, 17.133, -16.521, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param73", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.533, 7.141, 0, 0.767, -19.515, 0, 1.067, 22.91, 0, 1.433, -17.531, 0, 1.767, 7.931, 0, 2.133, -7.069, 0, 2.4, 12.745, 0, 2.7, -6.896, 0, 2.967, 2.201, 0, 3.467, -4.482, 0, 3.733, 6.915, 0, 4.033, -3.584, 0, 4.3, 2.186, 0, 4.5, -1.823, 0, 4.7, 5.521, 0, 4.967, -5.583, 0, 5.267, 2.75, 0, 5.567, -2.53, 0, 5.933, 2.471, 0, 6.167, 0.567, 0, 6.4, 1.92, 0, 6.7, -5.201, 0, 7.033, 1.037, 0, 7.067, 0.982, 0, 7.3, 2.193, 0, 7.6, -0.463, 0, 7.867, 4.862, 0, 8.133, -8.232, 0, 8.633, 4.268, 0, 8.933, -2.499, 0, 9.233, 4.582, 0, 9.533, -5.142, 0, 9.833, 0.499, 0, 10.1, -2.727, 0, 10.333, 6.406, 0, 10.633, -4.132, 0, 11, 6.458, 0, 11.3, -9.432, 0, 11.867, 4.954, 0, 12.167, -1.493, 0, 12.367, 3.793, 0, 12.633, -5.38, 0, 12.867, 0.581, 0, 13.1, -2.471, 0, 13.333, 3.384, 0, 13.6, -0.877, 0, 13.933, 2.495, 0, 13.967, 2.422, 0, 14.067, 2.947, 0, 14.4, -6.55, 0, 14.933, 4.549, 0, 15.233, -1.209, 0, 15.5, 2.533, 0, 15.8, -3.599, 0, 16.067, 0.391, 0, 16.3, -1.757, 0, 16.6, 2.919, 0, 16.767, -5.489, 0, 16.967, 13.903, 0, 17.233, -10.583, 0, 17.533, 4.251, 0, 17.833, -4.374, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param74", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.567, -16.053, 0, 0.9, 29.42, 0, 1.333, -23.007, 0, 2.233, 17.903, 0, 2.933, -12.254, 0, 3.567, 14.302, 0, 4.033, 3.716, 0, 4.067, 3.739, 0, 4.333, 0.673, 0, 4.5, 3.092, 0, 4.8, -9.994, 0, 5.8, 5.973, 0, 6.533, -12.002, 0, 7.167, 11.55, 0, 7.967, -16.009, 0, 8.533, 11.544, 0, 8.867, 5.365, 0, 8.9, 5.375, 0, 9.433, -12.449, 0, 10.167, 14.604, 0, 10.567, 0.656, 0, 10.7, 0.898, 0, 11.133, -18.258, 0, 11.733, 16.483, 0, 12.467, -14.399, 0, 13.233, 11.731, 0, 14.233, -16.642, 0, 14.8, 13.431, 0, 15.633, -10.736, 0, 16.433, 9.683, 0, 16.6, 7.399, 0, 16.767, 16.033, 0, 17.133, -16.521, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param75", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.533, 7.141, 0, 0.767, -19.515, 0, 1.067, 22.91, 0, 1.433, -17.531, 0, 1.767, 7.931, 0, 2.133, -7.069, 0, 2.4, 12.745, 0, 2.7, -6.896, 0, 2.967, 2.201, 0, 3.467, -4.482, 0, 3.733, 6.915, 0, 4.033, -3.584, 0, 4.3, 2.186, 0, 4.5, -1.823, 0, 4.7, 5.521, 0, 4.967, -5.583, 0, 5.267, 2.75, 0, 5.567, -2.53, 0, 5.933, 2.471, 0, 6.167, 0.567, 0, 6.4, 1.92, 0, 6.7, -5.201, 0, 7.033, 1.037, 0, 7.067, 0.982, 0, 7.3, 2.193, 0, 7.6, -0.463, 0, 7.867, 4.862, 0, 8.133, -8.232, 0, 8.633, 4.268, 0, 8.933, -2.499, 0, 9.233, 4.582, 0, 9.533, -5.142, 0, 9.833, 0.499, 0, 10.1, -2.727, 0, 10.333, 6.406, 0, 10.633, -4.132, 0, 11, 6.458, 0, 11.3, -9.432, 0, 11.867, 4.954, 0, 12.167, -1.493, 0, 12.367, 3.793, 0, 12.633, -5.38, 0, 12.867, 0.581, 0, 13.1, -2.471, 0, 13.333, 3.384, 0, 13.6, -0.877, 0, 13.933, 2.495, 0, 13.967, 2.422, 0, 14.067, 2.947, 0, 14.4, -6.55, 0, 14.933, 4.549, 0, 15.233, -1.209, 0, 15.5, 2.533, 0, 15.8, -3.599, 0, 16.067, 0.391, 0, 16.3, -1.757, 0, 16.6, 2.919, 0, 16.767, -5.489, 0, 16.967, 13.903, 0, 17.233, -10.583, 0, 17.533, 4.251, 0, 17.833, -4.374, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param53", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.567, -16.053, 0, 0.9, 29.42, 0, 1.333, -23.007, 0, 2.233, 17.903, 0, 2.933, -12.254, 0, 3.567, 14.302, 0, 4.033, 3.716, 0, 4.067, 3.739, 0, 4.333, 0.673, 0, 4.5, 3.092, 0, 4.8, -9.994, 0, 5.8, 5.973, 0, 6.533, -12.002, 0, 7.167, 11.55, 0, 7.967, -16.009, 0, 8.533, 11.544, 0, 8.867, 5.365, 0, 8.9, 5.375, 0, 9.433, -12.449, 0, 10.167, 14.604, 0, 10.567, 0.656, 0, 10.7, 0.898, 0, 11.133, -18.258, 0, 11.733, 16.483, 0, 12.467, -14.399, 0, 13.233, 11.731, 0, 14.233, -16.642, 0, 14.8, 13.431, 0, 15.633, -10.736, 0, 16.433, 9.683, 0, 16.6, 7.399, 0, 16.767, 16.033, 0, 17.133, -16.521, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param54", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.533, 7.141, 0, 0.767, -19.515, 0, 1.067, 22.91, 0, 1.433, -17.531, 0, 1.767, 7.931, 0, 2.133, -7.069, 0, 2.4, 12.745, 0, 2.7, -6.896, 0, 2.967, 2.201, 0, 3.467, -4.482, 0, 3.733, 6.915, 0, 4.033, -3.584, 0, 4.3, 2.186, 0, 4.5, -1.823, 0, 4.7, 5.521, 0, 4.967, -5.583, 0, 5.267, 2.75, 0, 5.567, -2.53, 0, 5.933, 2.471, 0, 6.167, 0.567, 0, 6.4, 1.92, 0, 6.7, -5.201, 0, 7.033, 1.037, 0, 7.067, 0.982, 0, 7.3, 2.193, 0, 7.6, -0.463, 0, 7.867, 4.862, 0, 8.133, -8.232, 0, 8.633, 4.268, 0, 8.933, -2.499, 0, 9.233, 4.582, 0, 9.533, -5.142, 0, 9.833, 0.499, 0, 10.1, -2.727, 0, 10.333, 6.406, 0, 10.633, -4.132, 0, 11, 6.458, 0, 11.3, -9.432, 0, 11.867, 4.954, 0, 12.167, -1.493, 0, 12.367, 3.793, 0, 12.633, -5.38, 0, 12.867, 0.581, 0, 13.1, -2.471, 0, 13.333, 3.384, 0, 13.6, -0.877, 0, 13.933, 2.495, 0, 13.967, 2.422, 0, 14.067, 2.947, 0, 14.4, -6.55, 0, 14.933, 4.549, 0, 15.233, -1.209, 0, 15.5, 2.533, 0, 15.8, -3.599, 0, 16.067, 0.391, 0, 16.3, -1.757, 0, 16.6, 2.919, 0, 16.767, -5.489, 0, 16.967, 13.903, 0, 17.233, -10.583, 0, 17.533, 4.251, 0, 17.833, -4.374, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param81", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.567, -16.053, 0, 0.9, 29.42, 0, 1.333, -23.007, 0, 2.233, 17.903, 0, 2.933, -12.254, 0, 3.567, 14.302, 0, 4.033, 3.716, 0, 4.067, 3.739, 0, 4.333, 0.673, 0, 4.5, 3.092, 0, 4.8, -9.994, 0, 5.8, 5.973, 0, 6.533, -12.002, 0, 7.167, 11.55, 0, 7.967, -16.009, 0, 8.533, 11.544, 0, 8.867, 5.365, 0, 8.9, 5.375, 0, 9.433, -12.449, 0, 10.167, 14.604, 0, 10.567, 0.656, 0, 10.7, 0.898, 0, 11.133, -18.258, 0, 11.733, 16.483, 0, 12.467, -14.399, 0, 13.233, 11.731, 0, 14.233, -16.642, 0, 14.8, 13.431, 0, 15.633, -10.736, 0, 16.433, 9.683, 0, 16.6, 7.399, 0, 16.767, 16.033, 0, 17.133, -16.521, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param82", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.533, 7.141, 0, 0.767, -19.515, 0, 1.067, 22.91, 0, 1.433, -17.531, 0, 1.767, 7.931, 0, 2.133, -7.069, 0, 2.4, 12.745, 0, 2.7, -6.896, 0, 2.967, 2.201, 0, 3.467, -4.482, 0, 3.733, 6.915, 0, 4.033, -3.584, 0, 4.3, 2.186, 0, 4.5, -1.823, 0, 4.7, 5.521, 0, 4.967, -5.583, 0, 5.267, 2.75, 0, 5.567, -2.53, 0, 5.933, 2.471, 0, 6.167, 0.567, 0, 6.4, 1.92, 0, 6.7, -5.201, 0, 7.033, 1.037, 0, 7.067, 0.982, 0, 7.3, 2.193, 0, 7.6, -0.463, 0, 7.867, 4.862, 0, 8.133, -8.232, 0, 8.633, 4.268, 0, 8.933, -2.499, 0, 9.233, 4.582, 0, 9.533, -5.142, 0, 9.833, 0.499, 0, 10.1, -2.727, 0, 10.333, 6.406, 0, 10.633, -4.132, 0, 11, 6.458, 0, 11.3, -9.432, 0, 11.867, 4.954, 0, 12.167, -1.493, 0, 12.367, 3.793, 0, 12.633, -5.38, 0, 12.867, 0.581, 0, 13.1, -2.471, 0, 13.333, 3.384, 0, 13.6, -0.877, 0, 13.933, 2.495, 0, 13.967, 2.422, 0, 14.067, 2.947, 0, 14.4, -6.55, 0, 14.933, 4.549, 0, 15.233, -1.209, 0, 15.5, 2.533, 0, 15.8, -3.599, 0, 16.067, 0.391, 0, 16.3, -1.757, 0, 16.6, 2.919, 0, 16.767, -5.489, 0, 16.967, 13.903, 0, 17.233, -10.583, 0, 17.533, 4.251, 0, 17.833, -4.374, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param70", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.567, -16.053, 0, 0.9, 29.42, 0, 1.333, -23.007, 0, 2.233, 17.903, 0, 2.933, -12.254, 0, 3.567, 14.302, 0, 4.033, 3.716, 0, 4.067, 3.739, 0, 4.333, 0.673, 0, 4.5, 3.092, 0, 4.8, -9.994, 0, 5.8, 5.973, 0, 6.533, -12.002, 0, 7.167, 11.55, 0, 7.967, -16.009, 0, 8.533, 11.544, 0, 8.867, 5.365, 0, 8.9, 5.375, 0, 9.433, -12.449, 0, 10.167, 14.604, 0, 10.567, 0.656, 0, 10.7, 0.898, 0, 11.133, -18.258, 0, 11.733, 16.483, 0, 12.467, -14.399, 0, 13.233, 11.731, 0, 14.233, -16.642, 0, 14.8, 13.431, 0, 15.633, -10.736, 0, 16.433, 9.683, 0, 16.6, 7.399, 0, 16.767, 16.033, 0, 17.133, -16.521, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param71", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.533, 7.141, 0, 0.767, -19.515, 0, 1.067, 22.91, 0, 1.433, -17.531, 0, 1.767, 7.931, 0, 2.133, -7.069, 0, 2.4, 12.745, 0, 2.7, -6.896, 0, 2.967, 2.201, 0, 3.467, -4.482, 0, 3.733, 6.915, 0, 4.033, -3.584, 0, 4.3, 2.186, 0, 4.5, -1.823, 0, 4.7, 5.521, 0, 4.967, -5.583, 0, 5.267, 2.75, 0, 5.567, -2.53, 0, 5.933, 2.471, 0, 6.167, 0.567, 0, 6.4, 1.92, 0, 6.7, -5.201, 0, 7.033, 1.037, 0, 7.067, 0.982, 0, 7.3, 2.193, 0, 7.6, -0.463, 0, 7.867, 4.862, 0, 8.133, -8.232, 0, 8.633, 4.268, 0, 8.933, -2.499, 0, 9.233, 4.582, 0, 9.533, -5.142, 0, 9.833, 0.499, 0, 10.1, -2.727, 0, 10.333, 6.406, 0, 10.633, -4.132, 0, 11, 6.458, 0, 11.3, -9.432, 0, 11.867, 4.954, 0, 12.167, -1.493, 0, 12.367, 3.793, 0, 12.633, -5.38, 0, 12.867, 0.581, 0, 13.1, -2.471, 0, 13.333, 3.384, 0, 13.6, -0.877, 0, 13.933, 2.495, 0, 13.967, 2.422, 0, 14.067, 2.947, 0, 14.4, -6.55, 0, 14.933, 4.549, 0, 15.233, -1.209, 0, 15.5, 2.533, 0, 15.8, -3.599, 0, 16.067, 0.391, 0, 16.3, -1.757, 0, 16.6, 2.919, 0, 16.767, -5.489, 0, 16.967, 13.903, 0, 17.233, -10.583, 0, 17.533, 4.251, 0, 17.833, -4.374, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param77", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.567, -16.053, 0, 0.9, 29.42, 0, 1.333, -23.007, 0, 2.233, 17.903, 0, 2.933, -12.254, 0, 3.567, 14.302, 0, 4.033, 3.716, 0, 4.067, 3.739, 0, 4.333, 0.673, 0, 4.5, 3.092, 0, 4.8, -9.994, 0, 5.8, 5.973, 0, 6.533, -12.002, 0, 7.167, 11.55, 0, 7.967, -16.009, 0, 8.533, 11.544, 0, 8.867, 5.365, 0, 8.9, 5.375, 0, 9.433, -12.449, 0, 10.167, 14.604, 0, 10.567, 0.656, 0, 10.7, 0.898, 0, 11.133, -18.258, 0, 11.733, 16.483, 0, 12.467, -14.399, 0, 13.233, 11.731, 0, 14.233, -16.642, 0, 14.8, 13.431, 0, 15.633, -10.736, 0, 16.433, 9.683, 0, 16.6, 7.399, 0, 16.767, 16.033, 0, 17.133, -16.521, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param78", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.533, 7.141, 0, 0.767, -19.515, 0, 1.067, 22.91, 0, 1.433, -17.531, 0, 1.767, 7.931, 0, 2.133, -7.069, 0, 2.4, 12.745, 0, 2.7, -6.896, 0, 2.967, 2.201, 0, 3.467, -4.482, 0, 3.733, 6.915, 0, 4.033, -3.584, 0, 4.3, 2.186, 0, 4.5, -1.823, 0, 4.7, 5.521, 0, 4.967, -5.583, 0, 5.267, 2.75, 0, 5.567, -2.53, 0, 5.933, 2.471, 0, 6.167, 0.567, 0, 6.4, 1.92, 0, 6.7, -5.201, 0, 7.033, 1.037, 0, 7.067, 0.982, 0, 7.3, 2.193, 0, 7.6, -0.463, 0, 7.867, 4.862, 0, 8.133, -8.232, 0, 8.633, 4.268, 0, 8.933, -2.499, 0, 9.233, 4.582, 0, 9.533, -5.142, 0, 9.833, 0.499, 0, 10.1, -2.727, 0, 10.333, 6.406, 0, 10.633, -4.132, 0, 11, 6.458, 0, 11.3, -9.432, 0, 11.867, 4.954, 0, 12.167, -1.493, 0, 12.367, 3.793, 0, 12.633, -5.38, 0, 12.867, 0.581, 0, 13.1, -2.471, 0, 13.333, 3.384, 0, 13.6, -0.877, 0, 13.933, 2.495, 0, 13.967, 2.422, 0, 14.067, 2.947, 0, 14.4, -6.55, 0, 14.933, 4.549, 0, 15.233, -1.209, 0, 15.5, 2.533, 0, 15.8, -3.599, 0, 16.067, 0.391, 0, 16.3, -1.757, 0, 16.6, 2.919, 0, 16.767, -5.489, 0, 16.967, 13.903, 0, 17.233, -10.583, 0, 17.533, 4.251, 0, 17.833, -4.374, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param79", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.567, -16.053, 0, 0.9, 29.42, 0, 1.333, -23.007, 0, 2.233, 17.903, 0, 2.933, -12.254, 0, 3.567, 14.302, 0, 4.033, 3.716, 0, 4.067, 3.739, 0, 4.333, 0.673, 0, 4.5, 3.092, 0, 4.8, -9.994, 0, 5.8, 5.973, 0, 6.533, -12.002, 0, 7.167, 11.55, 0, 7.967, -16.009, 0, 8.533, 11.544, 0, 8.867, 5.365, 0, 8.9, 5.375, 0, 9.433, -12.449, 0, 10.167, 14.604, 0, 10.567, 0.656, 0, 10.7, 0.898, 0, 11.133, -18.258, 0, 11.733, 16.483, 0, 12.467, -14.399, 0, 13.233, 11.731, 0, 14.233, -16.642, 0, 14.8, 13.431, 0, 15.633, -10.736, 0, 16.433, 9.683, 0, 16.6, 7.399, 0, 16.767, 16.033, 0, 17.133, -16.521, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param80", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.533, 7.141, 0, 0.767, -19.515, 0, 1.067, 22.91, 0, 1.433, -17.531, 0, 1.767, 7.931, 0, 2.133, -7.069, 0, 2.4, 12.745, 0, 2.7, -6.896, 0, 2.967, 2.201, 0, 3.467, -4.482, 0, 3.733, 6.915, 0, 4.033, -3.584, 0, 4.3, 2.186, 0, 4.5, -1.823, 0, 4.7, 5.521, 0, 4.967, -5.583, 0, 5.267, 2.75, 0, 5.567, -2.53, 0, 5.933, 2.471, 0, 6.167, 0.567, 0, 6.4, 1.92, 0, 6.7, -5.201, 0, 7.033, 1.037, 0, 7.067, 0.982, 0, 7.3, 2.193, 0, 7.6, -0.463, 0, 7.867, 4.862, 0, 8.133, -8.232, 0, 8.633, 4.268, 0, 8.933, -2.499, 0, 9.233, 4.582, 0, 9.533, -5.142, 0, 9.833, 0.499, 0, 10.1, -2.727, 0, 10.333, 6.406, 0, 10.633, -4.132, 0, 11, 6.458, 0, 11.3, -9.432, 0, 11.867, 4.954, 0, 12.167, -1.493, 0, 12.367, 3.793, 0, 12.633, -5.38, 0, 12.867, 0.581, 0, 13.1, -2.471, 0, 13.333, 3.384, 0, 13.6, -0.877, 0, 13.933, 2.495, 0, 13.967, 2.422, 0, 14.067, 2.947, 0, 14.4, -6.55, 0, 14.933, 4.549, 0, 15.233, -1.209, 0, 15.5, 2.533, 0, 15.8, -3.599, 0, 16.067, 0.391, 0, 16.3, -1.757, 0, 16.6, 2.919, 0, 16.767, -5.489, 0, 16.967, 13.903, 0, 17.233, -10.583, 0, 17.533, 4.251, 0, 17.833, -4.374, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param294", "Segments": [0, 1, 2, 0.333, 1, 0, 0.4, 0, 2, 18, 0, 0, 18.1, 1]}, {"Target": "Parameter", "Id": "Param47", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, 10.332, 0, 0.767, -13.248, 0, 1.233, 6.315, 0, 2.2, -9.138, 0, 2.633, 9.306, 0, 3.5, -8.528, 0, 4.133, 9.066, 0, 4.733, -8.009, 0, 5.433, 6.629, 0, 6.2, -11.17, 0, 6.7, 6.412, 0, 7, -0.028, 0, 7.467, 4.97, 0, 7.5, 4.913, 0, 7.6, 5.323, 0, 8.033, -9.559, 0, 8.8, 6.159, 0, 9.733, -8.343, 0, 10.233, 9.347, 0, 10.733, 1.384, 0, 10.767, 1.409, 0, 11.233, -4.352, 0, 11.533, -3.078, 0, 11.767, -5.891, 0, 12.467, 5.447, 0, 12.533, 5.391, 0, 12.767, 6.124, 0, 13.6, -3.721, 0, 14.633, 3.031, 0, 14.7, 2.925, 0, 14.933, 3.462, 0, 15.9, -4.516, 0, 17, 1.607, 0, 17.733, -2.912, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param48", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -6.29, 0, 0.633, 11.97, 0, 0.933, -11.412, 0, 1.267, 4.691, 0, 1.633, -1.827, 0, 1.967, 1.729, 0, 2, 1.635, 0, 2.1, 2.282, 0, 2.4, -6.012, 0, 2.767, 6.208, 0, 3.067, -2.645, 0, 3.367, 2.974, 0, 3.667, -3.929, 0, 3.967, 0.194, 0, 4.067, -0.399, 0, 4.333, 3.104, 0, 4.9, -2.845, 0, 5.167, 0.287, 0, 5.333, -0.762, 0, 5.633, 2.207, 0, 5.9, -0.016, 0, 6.1, 2.386, 0, 6.367, -4.697, 0, 6.833, 4.347, 0, 7.133, -3.523, 0, 7.5, 1.65, 0, 7.633, 0.255, 0, 7.9, 3.691, 0, 8.167, -4.848, 0, 8.467, 1.556, 0, 8.7, -1.718, 0, 8.967, 2.117, 0, 9.233, -0.593, 0, 9.6, 1.756, 0, 9.9, -3.827, 0, 10.4, 4.131, 0, 10.7, -2.051, 0, 11.033, 2.047, 0, 11.333, -1.78, 0, 11.7, 1.98, 0, 12, -2.918, 0, 12.333, 1.575, 0, 12.7, -0.818, 0, 13, 1.354, 0, 13.467, -0.596, 2, 13.5, -0.596, 0, 13.533, -0.599, 0, 13.8, -0.231, 0, 13.867, -0.263, 0, 13.9, -0.229, 0, 13.967, -0.373, 0, 14, -0.365, 0, 14.067, -0.457, 0, 14.267, 0.331, 0, 14.533, -0.702, 0, 14.767, 0.539, 0, 14.933, -0.134, 0, 15.233, 0.536, 0, 15.5, 0.226, 0, 15.733, 0.543, 0, 16.033, -0.948, 0, 16.333, 0.225, 0, 16.6, -0.769, 0, 16.833, 0.43, 0, 17.067, 0.09, 0, 17.367, 0.3, 0, 17.433, 0.294, 0, 17.467, 0.312, 0, 17.533, 0.296, 0, 17.633, 0.33, 0, 17.9, -1.151, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param56", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, 4.313, 0, 0.833, -5.954, 0, 1.4, 5.442, 0, 2.2, -5.455, 0, 2.733, 6.351, 0, 3.467, -6.157, 0, 4.167, 6.792, 0, 4.8, -6.799, 0, 5.467, 5.919, 0, 6.2, -7.298, 0, 6.767, 5.532, 0, 7.267, -0.229, 0, 7.667, 3.118, 0, 8.133, -6.132, 0, 8.8, 5.69, 0, 9.733, -4.678, 0, 10.333, 6.594, 0, 11.133, -2.887, 0, 11.533, -1.321, 0, 11.833, -2.611, 0, 12.467, 4.456, 0, 13.567, -2.797, 0, 14.633, 1.59, 0, 14.7, 1.55, 0, 14.933, 1.774, 0, 15.933, -2.881, 0, 16.867, 1.284, 0, 17.767, -1.788, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param57", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -3.218, 0, 0.667, 6.29, 0, 1, -5.768, 0, 1.6, 2.86, 0, 2.033, 0.43, 0, 2.1, 0.499, 0, 2.467, -5.029, 0, 2.9, 3.929, 0, 3.7, -3.984, 0, 4.433, 4.543, 0, 5, -4.164, 0, 5.667, 3.583, 0, 6.467, -5.093, 0, 6.933, 3.451, 0, 7.4, -0.633, 0, 7.9, 4.394, 0, 8.333, -3.95, 0, 9, 3.263, 0, 9.533, 0.498, 0, 9.6, 0.564, 0, 10, -3.632, 0, 10.5, 4.033, 0, 11.367, -1.283, 0, 11.7, 0.13, 0, 12.067, -2.8, 0, 12.967, 2.102, 0, 13.867, -1.386, 0, 14.367, 0.081, 0, 14.533, -0.051, 0, 14.833, 0.571, 0, 14.867, 0.554, 0, 15.3, 1.279, 0, 16.167, -1.593, 0, 17.267, 0.58, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param58", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.567, -2.77, 0, 0.8, 7.584, 0, 1.1, -8.584, 0, 1.5, 4.256, 0, 1.9, 0.031, 0, 2.233, 0.696, 0, 2.567, -5.197, 0, 2.933, 5.547, 0, 3.267, -0.906, 0, 3.467, 0.806, 0, 3.8, -3.771, 0, 4.5, 3.729, 0, 5.033, -3.927, 0, 5.733, 2.891, 0, 6.067, -0.002, 0, 6.2, 0.619, 0, 6.533, -4.454, 0, 7, 4.722, 0, 7.367, -2.97, 0, 8, 4.473, 0, 8.367, -4.954, 0, 8.7, 0.118, 0, 8.767, 0.11, 0, 9.1, 2.75, 0, 9.433, -0.913, 0, 9.733, 1.292, 0, 10.1, -3.6, 0, 10.567, 4.356, 0, 10.933, -1.714, 0, 11.233, 0.527, 0, 11.5, -0.859, 0, 11.8, 1.546, 0, 12.167, -2.882, 0, 12.567, 1.621, 0, 12.9, -0.019, 0, 13.2, 0.809, 0, 13.8, -0.759, 0, 14.4, 0.449, 0, 14.7, -0.418, 0, 14.933, 0.127, 0, 15.067, 0.072, 0, 15.4, 0.617, 0, 15.7, 0.317, 0, 15.833, 0.357, 0, 16.2, -1, 0, 16.533, 0.02, 0, 16.733, -0.288, 0, 17.033, 0.42, 0, 17.3, 0.191, 0, 17.667, 0.315, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param59", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, 4.313, 0, 0.833, -5.954, 0, 1.4, 5.442, 0, 2.2, -5.455, 0, 2.733, 6.351, 0, 3.467, -6.157, 0, 4.167, 6.792, 0, 4.8, -6.799, 0, 5.467, 5.919, 0, 6.2, -7.298, 0, 6.767, 5.532, 0, 7.267, -0.229, 0, 7.667, 3.118, 0, 8.133, -6.132, 0, 8.8, 5.69, 0, 9.733, -4.678, 0, 10.333, 6.594, 0, 11.133, -2.887, 0, 11.533, -1.321, 0, 11.833, -2.611, 0, 12.467, 4.456, 0, 13.567, -2.797, 0, 14.633, 1.59, 0, 14.7, 1.55, 0, 14.933, 1.774, 0, 15.933, -2.881, 0, 16.867, 1.284, 0, 17.767, -1.788, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param60", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -3.218, 0, 0.667, 6.29, 0, 1, -5.768, 0, 1.6, 2.86, 0, 2.033, 0.43, 0, 2.1, 0.499, 0, 2.467, -5.029, 0, 2.9, 3.929, 0, 3.7, -3.984, 0, 4.433, 4.543, 0, 5, -4.164, 0, 5.667, 3.583, 0, 6.467, -5.093, 0, 6.933, 3.451, 0, 7.4, -0.633, 0, 7.9, 4.394, 0, 8.333, -3.95, 0, 9, 3.263, 0, 9.533, 0.498, 0, 9.6, 0.564, 0, 10, -3.632, 0, 10.5, 4.033, 0, 11.367, -1.283, 0, 11.7, 0.13, 0, 12.067, -2.8, 0, 12.967, 2.102, 0, 13.867, -1.386, 0, 14.367, 0.081, 0, 14.533, -0.051, 0, 14.833, 0.571, 0, 14.867, 0.554, 0, 15.3, 1.279, 0, 16.167, -1.593, 0, 17.267, 0.58, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param61", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.567, -2.77, 0, 0.8, 7.584, 0, 1.1, -8.584, 0, 1.5, 4.256, 0, 1.9, 0.031, 0, 2.233, 0.696, 0, 2.567, -5.197, 0, 2.933, 5.547, 0, 3.267, -0.906, 0, 3.467, 0.806, 0, 3.8, -3.771, 0, 4.5, 3.729, 0, 5.033, -3.927, 0, 5.733, 2.891, 0, 6.067, -0.002, 0, 6.2, 0.619, 0, 6.533, -4.454, 0, 7, 4.722, 0, 7.367, -2.97, 0, 8, 4.473, 0, 8.367, -4.954, 0, 8.7, 0.118, 0, 8.767, 0.11, 0, 9.1, 2.75, 0, 9.433, -0.913, 0, 9.733, 1.292, 0, 10.1, -3.6, 0, 10.567, 4.356, 0, 10.933, -1.714, 0, 11.233, 0.527, 0, 11.5, -0.859, 0, 11.8, 1.546, 0, 12.167, -2.882, 0, 12.567, 1.621, 0, 12.9, -0.019, 0, 13.2, 0.809, 0, 13.8, -0.759, 0, 14.4, 0.449, 0, 14.7, -0.418, 0, 14.933, 0.127, 0, 15.067, 0.072, 0, 15.4, 0.617, 0, 15.7, 0.317, 0, 15.833, 0.357, 0, 16.2, -1, 0, 16.533, 0.02, 0, 16.733, -0.288, 0, 17.033, 0.42, 0, 17.3, 0.191, 0, 17.667, 0.315, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param49", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, 10.332, 0, 0.767, -13.248, 0, 1.233, 6.315, 0, 2.2, -9.138, 0, 2.633, 9.306, 0, 3.5, -8.528, 0, 4.133, 9.066, 0, 4.733, -8.009, 0, 5.433, 6.629, 0, 6.2, -11.17, 0, 6.7, 6.412, 0, 7, -0.028, 0, 7.467, 4.97, 0, 7.5, 4.913, 0, 7.6, 5.323, 0, 8.033, -9.559, 0, 8.8, 6.159, 0, 9.733, -8.343, 0, 10.233, 9.347, 0, 10.733, 1.384, 0, 10.767, 1.409, 0, 11.233, -4.352, 0, 11.533, -3.078, 0, 11.767, -5.891, 0, 12.467, 5.447, 0, 12.533, 5.391, 0, 12.767, 6.124, 0, 13.6, -3.721, 0, 14.633, 3.031, 0, 14.7, 2.925, 0, 14.933, 3.462, 0, 15.9, -4.516, 0, 17, 1.607, 0, 17.733, -2.912, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param50", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -6.29, 0, 0.633, 11.97, 0, 0.933, -11.412, 0, 1.267, 4.691, 0, 1.633, -1.827, 0, 1.967, 1.729, 0, 2, 1.635, 0, 2.1, 2.282, 0, 2.4, -6.012, 0, 2.767, 6.208, 0, 3.067, -2.645, 0, 3.367, 2.974, 0, 3.667, -3.929, 0, 3.967, 0.194, 0, 4.067, -0.399, 0, 4.333, 3.104, 0, 4.9, -2.845, 0, 5.167, 0.287, 0, 5.333, -0.762, 0, 5.633, 2.207, 0, 5.9, -0.016, 0, 6.1, 2.386, 0, 6.367, -4.697, 0, 6.833, 4.347, 0, 7.133, -3.523, 0, 7.5, 1.65, 0, 7.633, 0.255, 0, 7.9, 3.691, 0, 8.167, -4.848, 0, 8.467, 1.556, 0, 8.7, -1.718, 0, 8.967, 2.117, 0, 9.233, -0.593, 0, 9.6, 1.756, 0, 9.9, -3.827, 0, 10.4, 4.131, 0, 10.7, -2.051, 0, 11.033, 2.047, 0, 11.333, -1.78, 0, 11.7, 1.98, 0, 12, -2.918, 0, 12.333, 1.575, 0, 12.7, -0.818, 0, 13, 1.354, 0, 13.467, -0.596, 2, 13.5, -0.596, 0, 13.533, -0.599, 0, 13.8, -0.231, 0, 13.867, -0.263, 0, 13.9, -0.229, 0, 13.967, -0.373, 0, 14, -0.365, 0, 14.067, -0.457, 0, 14.267, 0.331, 0, 14.533, -0.702, 0, 14.767, 0.539, 0, 14.933, -0.134, 0, 15.233, 0.536, 0, 15.5, 0.226, 0, 15.733, 0.543, 0, 16.033, -0.948, 0, 16.333, 0.225, 0, 16.6, -0.769, 0, 16.833, 0.43, 0, 17.067, 0.09, 0, 17.367, 0.3, 0, 17.433, 0.294, 0, 17.467, 0.312, 0, 17.533, 0.296, 0, 17.633, 0.33, 0, 17.9, -1.151, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param51", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, 10.332, 0, 0.767, -13.248, 0, 1.233, 6.315, 0, 2.2, -9.138, 0, 2.633, 9.306, 0, 3.5, -8.528, 0, 4.133, 9.066, 0, 4.733, -8.009, 0, 5.433, 6.629, 0, 6.2, -11.17, 0, 6.7, 6.412, 0, 7, -0.028, 0, 7.467, 4.97, 0, 7.5, 4.913, 0, 7.6, 5.323, 0, 8.033, -9.559, 0, 8.8, 6.159, 0, 9.733, -8.343, 0, 10.233, 9.347, 0, 10.733, 1.384, 0, 10.767, 1.409, 0, 11.233, -4.352, 0, 11.533, -3.078, 0, 11.767, -5.891, 0, 12.467, 5.447, 0, 12.533, 5.391, 0, 12.767, 6.124, 0, 13.6, -3.721, 0, 14.633, 3.031, 0, 14.7, 2.925, 0, 14.933, 3.462, 0, 15.9, -4.516, 0, 17, 1.607, 0, 17.733, -2.912, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param52", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -6.29, 0, 0.633, 11.97, 0, 0.933, -11.412, 0, 1.267, 4.691, 0, 1.633, -1.827, 0, 1.967, 1.729, 0, 2, 1.635, 0, 2.1, 2.282, 0, 2.4, -6.012, 0, 2.767, 6.208, 0, 3.067, -2.645, 0, 3.367, 2.974, 0, 3.667, -3.929, 0, 3.967, 0.194, 0, 4.067, -0.399, 0, 4.333, 3.104, 0, 4.9, -2.845, 0, 5.167, 0.287, 0, 5.333, -0.762, 0, 5.633, 2.207, 0, 5.9, -0.016, 0, 6.1, 2.386, 0, 6.367, -4.697, 0, 6.833, 4.347, 0, 7.133, -3.523, 0, 7.5, 1.65, 0, 7.633, 0.255, 0, 7.9, 3.691, 0, 8.167, -4.848, 0, 8.467, 1.556, 0, 8.7, -1.718, 0, 8.967, 2.117, 0, 9.233, -0.593, 0, 9.6, 1.756, 0, 9.9, -3.827, 0, 10.4, 4.131, 0, 10.7, -2.051, 0, 11.033, 2.047, 0, 11.333, -1.78, 0, 11.7, 1.98, 0, 12, -2.918, 0, 12.333, 1.575, 0, 12.7, -0.818, 0, 13, 1.354, 0, 13.467, -0.596, 2, 13.5, -0.596, 0, 13.533, -0.599, 0, 13.8, -0.231, 0, 13.867, -0.263, 0, 13.9, -0.229, 0, 13.967, -0.373, 0, 14, -0.365, 0, 14.067, -0.457, 0, 14.267, 0.331, 0, 14.533, -0.702, 0, 14.767, 0.539, 0, 14.933, -0.134, 0, 15.233, 0.536, 0, 15.5, 0.226, 0, 15.733, 0.543, 0, 16.033, -0.948, 0, 16.333, 0.225, 0, 16.6, -0.769, 0, 16.833, 0.43, 0, 17.067, 0.09, 0, 17.367, 0.3, 0, 17.433, 0.294, 0, 17.467, 0.312, 0, 17.533, 0.296, 0, 17.633, 0.33, 0, 17.9, -1.151, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation64", "Segments": [0, 2.951, 2, 0.333, 2.951, 0, 0.367, 0, 0, 0.5, 3.444, 0, 0.767, -4.416, 0, 1.233, 2.105, 0, 2.2, -3.046, 0, 2.633, 3.102, 0, 3.5, -2.843, 0, 4.133, 3.022, 0, 4.733, -2.67, 0, 5.433, 2.21, 0, 6.2, -3.724, 0, 6.7, 2.137, 0, 7, -0.009, 0, 7.467, 1.657, 0, 7.5, 1.638, 0, 7.6, 1.774, 0, 8.033, -3.186, 0, 8.8, 2.053, 0, 9.733, -2.781, 0, 10.233, 3.116, 0, 10.733, 0.461, 0, 10.767, 0.47, 0, 11.233, -1.451, 0, 11.533, -1.026, 0, 11.767, -1.964, 0, 12.467, 1.815, 0, 12.533, 1.797, 0, 12.767, 2.041, 0, 13.6, -1.24, 0, 14.633, 1.01, 0, 14.7, 0.975, 0, 14.933, 1.154, 0, 15.9, -1.505, 0, 17, 0.536, 0, 17.733, -0.971, 0, 18.1, 2.951]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation65", "Segments": [0, 4.849, 2, 0.333, 4.849, 1, 0.344, 4.849, 0.356, 0.846, 0.367, 0, 1, 0.4, -2.538, 0.434, -3.145, 0.467, -3.145, 0, 0.633, 5.985, 0, 0.933, -5.706, 0, 1.267, 2.345, 0, 1.633, -0.914, 0, 1.967, 0.865, 0, 2, 0.818, 0, 2.1, 1.141, 0, 2.4, -3.006, 0, 2.767, 3.104, 0, 3.067, -1.322, 0, 3.367, 1.487, 0, 3.667, -1.964, 0, 3.967, 0.097, 0, 4.067, -0.2, 0, 4.333, 1.552, 0, 4.9, -1.423, 0, 5.167, 0.144, 0, 5.333, -0.381, 0, 5.633, 1.104, 0, 5.9, -0.008, 0, 6.1, 1.193, 0, 6.367, -2.349, 0, 6.833, 2.174, 0, 7.133, -1.762, 0, 7.5, 0.825, 0, 7.633, 0.127, 0, 7.9, 1.845, 0, 8.167, -2.424, 0, 8.467, 0.778, 0, 8.7, -0.859, 0, 8.967, 1.058, 0, 9.233, -0.297, 0, 9.6, 0.878, 0, 9.9, -1.913, 0, 10.4, 2.066, 0, 10.7, -1.025, 0, 11.033, 1.023, 0, 11.333, -0.89, 0, 11.7, 0.99, 0, 12, -1.459, 0, 12.333, 0.787, 0, 12.7, -0.409, 0, 13, 0.677, 0, 13.467, -0.298, 2, 13.5, -0.298, 0, 13.533, -0.3, 0, 13.8, -0.116, 0, 13.867, -0.132, 0, 13.9, -0.114, 0, 13.967, -0.187, 0, 14, -0.182, 0, 14.067, -0.229, 0, 14.267, 0.166, 0, 14.533, -0.351, 0, 14.767, 0.27, 0, 14.933, -0.067, 0, 15.233, 0.268, 0, 15.5, 0.113, 0, 15.733, 0.272, 0, 16.033, -0.474, 0, 16.333, 0.113, 0, 16.6, -0.384, 0, 16.833, 0.215, 0, 17.067, 0.045, 0, 17.367, 0.15, 0, 17.433, 0.147, 0, 17.467, 0.156, 0, 17.533, 0.148, 0, 17.633, 0.165, 0, 17.9, -0.576, 0, 18.1, 4.849]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation66", "Segments": [0, -11.659, 2, 0.333, -11.659, 0, 0.367, 0, 0, 0.533, -2.779, 0, 0.767, 7.921, 0, 1.033, -10.462, 0, 1.367, 6.818, 0, 1.7, -3.416, 0, 2.033, 2.486, 0, 2.5, -5.132, 0, 2.833, 6.078, 0, 3.167, -3.771, 0, 3.467, 3.389, 0, 3.767, -4.163, 0, 4.067, 1.283, 0, 4.233, 0.631, 0, 4.467, 2.123, 0, 4.967, -2.32, 0, 5.267, 0.783, 0, 5.467, -0.271, 0, 5.733, 1.719, 0, 6, -0.324, 0, 6.2, 1.319, 0, 6.5, -4.011, 0, 6.9, 3.902, 0, 7.233, -3.823, 0, 7.567, 2.429, 0, 7.8, 0.338, 0, 8, 2.124, 0, 8.267, -4.285, 0, 8.567, 2.343, 0, 8.833, -1.549, 0, 9.1, 1.945, 0, 9.367, -0.902, 0, 9.667, 1.947, 0, 10, -3.624, 0, 10.467, 3.531, 0, 10.8, -2.601, 0, 11.133, 2.43, 0, 11.467, -2.114, 0, 11.767, 2.078, 0, 12.1, -2.953, 0, 12.433, 2.058, 0, 12.767, -1.23, 0, 13.1, 1.498, 0, 13.567, -0.615, 0, 13.9, -0.035, 0, 14.133, -0.292, 0, 14.4, 0.261, 0, 14.633, -0.538, 0, 14.9, 0.458, 0, 15.133, -0.074, 0, 15.4, 0.421, 0, 15.633, 0.13, 0, 15.833, 0.325, 0, 16.133, -0.83, 0, 16.433, 0.379, 0, 16.7, -0.616, 0, 17, 0.504, 0, 17.267, -0.021, 0, 17.567, 0.278, 0, 18.1, -11.659]}, {"Target": "Parameter", "Id": "Param55", "Segments": [0, -10.57, 2, 0.333, -10.57, 0, 0.4, 0, 0, 0.6, -3.438, 0, 0.867, 12.485, 0, 1.133, -20.011, 0, 1.467, 16.676, 0, 1.8, -10.475, 0, 2.133, 7.759, 0, 2.567, -9.885, 0, 2.933, 12.874, 0, 3.267, -9.789, 0, 3.567, 8.686, 0, 3.867, -9.877, 0, 4.2, 5.144, 0, 4.433, 1.126, 0, 4.6, 2.454, 0, 5.033, -4.54, 0, 5.367, 2.466, 0, 5.6, -0.57, 0, 5.867, 2.964, 0, 6.133, -0.849, 0, 6.3, 1.481, 0, 6.6, -7.238, 0, 6.967, 8.541, 0, 7.333, -8.939, 0, 7.667, 6.828, 0, 7.933, -0.41, 0, 8.1, 2.135, 0, 8.4, -7.757, 0, 8.7, 6.034, 0, 8.967, -3.92, 0, 9.233, 4.217, 0, 9.5, -2.509, 0, 9.767, 3.814, 0, 10.1, -7.549, 0, 10.533, 7.265, 0, 10.9, -6.341, 0, 11.233, 6.119, 0, 11.567, -5.516, 0, 11.867, 5.242, 0, 12.2, -6.778, 0, 12.533, 5.598, 0, 12.867, -3.82, 0, 13.2, 3.876, 0, 13.567, -2.005, 0, 13.933, 0.379, 0, 14.233, -0.605, 0, 14.5, 0.533, 0, 14.767, -0.925, 0, 15.033, 0.988, 0, 15.267, -0.224, 0, 15.533, 0.775, 0, 15.8, 0.134, 0, 15.933, 0.324, 0, 16.267, -1.536, 0, 16.567, 1.05, 0, 16.833, -1.269, 0, 17.133, 1.264, 0, 17.4, -0.371, 0, 17.7, 0.659, 0, 18.1, -10.57]}, {"Target": "Parameter", "Id": "Param28", "Segments": [0, -7.12, 2, 0.333, -7.12, 0, 1.567, 7.36, 0, 2.833, -7.12, 0, 4.1, 7.36, 0, 5.367, -7.12, 0, 6.7, 7.36, 0, 7.967, -7.12, 0, 9.233, 7.36, 0, 10.5, -7.12, 0, 11.767, 7.36, 0, 13.1, -7.12, 0, 14.367, 7.36, 0, 15.633, -7.12, 0, 16.9, 7.36, 0, 18.1, -7.12]}, {"Target": "Parameter", "Id": "Param29", "Segments": [0, -1.278, 2, 0.333, -1.278, 0, 0.767, -4.74, 0, 2.033, 5.1, 1, 2.3, 5.1, 2.566, 2.616, 2.833, -1.278, 1, 3.011, -3.874, 3.189, -4.74, 3.367, -4.74, 0, 4.633, 5.1, 1, 4.878, 5.1, 5.122, 2.437, 5.367, -1.278, 1, 5.545, -3.979, 5.722, -4.74, 5.9, -4.74, 0, 7.167, 5.1, 1, 7.434, 5.1, 7.7, 2.739, 7.967, -1.278, 1, 8.134, -3.788, 8.3, -4.74, 8.467, -4.74, 0, 9.733, 5.1, 1, 9.989, 5.1, 10.244, 2.53, 10.5, -1.278, 1, 10.678, -3.927, 10.855, -4.74, 11.033, -4.74, 0, 12.3, 5.1, 1, 12.567, 5.1, 12.833, 2.86, 13.1, -1.278, 1, 13.256, -3.691, 13.411, -4.74, 13.567, -4.74, 0, 14.867, 5.1, 1, 15.122, 5.1, 15.378, 2.659, 15.633, -1.278, 1, 15.8, -3.845, 15.966, -4.74, 16.133, -4.74, 0, 17.433, 5.1, 0, 18.1, -1.278]}, {"Target": "Parameter", "Id": "Param30", "Segments": [0, -5.909, 2, 0.333, -5.909, 0, 1.4, 2.533, 0, 2.667, -6.287, 1, 2.722, -6.287, 2.778, -6.553, 2.833, -5.909, 1, 3.2, -1.656, 3.566, 2.533, 3.933, 2.533, 0, 5.233, -6.287, 1, 5.278, -6.287, 5.322, -6.461, 5.367, -5.909, 1, 5.756, -1.072, 6.144, 2.533, 6.533, 2.533, 0, 7.8, -6.287, 1, 7.856, -6.287, 7.911, -6.553, 7.967, -5.909, 1, 8.334, -1.656, 8.7, 2.533, 9.067, 2.533, 0, 10.333, -6.287, 1, 10.389, -6.287, 10.444, -6.544, 10.5, -5.909, 1, 10.878, -1.585, 11.255, 2.533, 11.633, 2.533, 0, 12.9, -6.287, 1, 12.967, -6.287, 13.033, -6.598, 13.1, -5.909, 1, 13.467, -2.118, 13.833, 2.533, 14.2, 2.533, 0, 15.467, -6.287, 1, 15.522, -6.287, 15.578, -6.553, 15.633, -5.909, 1, 16, -1.656, 16.366, 2.533, 16.733, 2.533, 0, 18.1, -5.909]}, {"Target": "Parameter", "Id": "Param31", "Segments": [0, -0.551, 2, 0.333, -0.551, 0, 0.8, 3.018, 0, 2.067, -6.18, 1, 2.322, -6.18, 2.578, -4.056, 2.833, -0.551, 1, 3.022, 2.039, 3.211, 3.018, 3.4, 3.018, 0, 4.667, -6.18, 1, 4.9, -6.18, 5.134, -3.888, 5.367, -0.551, 1, 5.556, 2.15, 5.744, 3.018, 5.933, 3.018, 0, 7.2, -6.18, 1, 7.456, -6.18, 7.711, -4.16, 7.967, -0.551, 1, 8.145, 1.959, 8.322, 3.018, 8.5, 3.018, 0, 9.767, -6.18, 1, 10.011, -6.18, 10.256, -3.976, 10.5, -0.551, 1, 10.689, 2.095, 10.878, 3.018, 11.067, 3.018, 0, 12.333, -6.18, 1, 12.589, -6.18, 12.844, -4.262, 13.1, -0.551, 1, 13.267, 1.869, 13.433, 3.018, 13.6, 3.018, 0, 14.9, -6.18, 1, 15.144, -6.18, 15.389, -4.084, 15.633, -0.551, 1, 15.811, 2.018, 15.989, 3.018, 16.167, 3.018, 0, 17.467, -6.18, 0, 18.1, -0.551]}, {"Target": "Parameter", "Id": "Param32", "Segments": [0, -2.714, 2, 0.333, -2.714, 0, 1.467, 2.284, 0, 2.733, -2.796, 1, 2.766, -2.796, 2.8, -2.927, 2.833, -2.714, 1, 3.222, -0.226, 3.611, 2.284, 4, 2.284, 0, 5.267, -2.796, 1, 5.3, -2.796, 5.334, -2.922, 5.367, -2.714, 1, 5.778, -0.152, 6.189, 2.284, 6.6, 2.284, 0, 7.867, -2.796, 1, 7.9, -2.796, 7.934, -2.927, 7.967, -2.714, 1, 8.356, -0.226, 8.744, 2.284, 9.133, 2.284, 0, 10.4, -2.796, 1, 10.433, -2.796, 10.467, -2.924, 10.5, -2.714, 1, 10.9, -0.188, 11.3, 2.284, 11.7, 2.284, 0, 12.967, -2.796, 1, 13.011, -2.796, 13.056, -2.949, 13.1, -2.714, 1, 13.489, -0.659, 13.878, 2.284, 14.267, 2.284, 0, 15.533, -2.796, 1, 15.566, -2.796, 15.6, -2.927, 15.633, -2.714, 1, 16.022, -0.226, 16.411, 2.284, 16.8, 2.284, 0, 18.1, -2.714]}, {"Target": "Parameter", "Id": "Param33", "Segments": [0, -2.392, 2, 0.333, -2.392, 0, 0.833, 3.279, 0, 2.1, -10.047, 1, 2.344, -10.047, 2.589, -7.265, 2.833, -2.392, 1, 3.033, 1.595, 3.233, 3.279, 3.433, 3.279, 0, 4.7, -10.047, 1, 4.922, -10.047, 5.145, -7.019, 5.367, -2.392, 1, 5.567, 1.773, 5.767, 3.279, 5.967, 3.279, 0, 7.233, -10.047, 1, 7.478, -10.047, 7.722, -7.535, 7.967, -2.392, 1, 8.145, 1.349, 8.322, 3.279, 8.5, 3.279, 0, 9.8, -10.047, 1, 10.033, -10.047, 10.267, -7.148, 10.5, -2.392, 1, 10.7, 1.685, 10.9, 3.279, 11.1, 3.279, 0, 12.367, -10.047, 1, 12.611, -10.047, 12.856, -7.535, 13.1, -2.392, 1, 13.278, 1.349, 13.455, 3.279, 13.633, 3.279, 0, 14.933, -10.047, 1, 15.166, -10.047, 15.4, -7.291, 15.633, -2.392, 1, 15.822, 1.574, 16.011, 3.279, 16.2, 3.279, 0, 17.5, -10.047, 0, 18.1, -2.392]}, {"Target": "Parameter", "Id": "Param34", "Segments": [0, -21.182, 2, 0.333, -21.182, 0, 1.5, 11.7, 0, 2.767, -21.42, 1, 2.789, -21.42, 2.811, -22.023, 2.833, -21.182, 1, 3.233, -6.042, 3.633, 11.7, 4.033, 11.7, 0, 5.3, -21.42, 1, 5.322, -21.42, 5.345, -22.016, 5.367, -21.182, 1, 5.778, -5.756, 6.189, 11.7, 6.6, 11.7, 0, 7.9, -21.42, 1, 7.922, -21.42, 7.945, -22.023, 7.967, -21.182, 1, 8.367, -6.042, 8.767, 11.7, 9.167, 11.7, 0, 10.433, -21.42, 1, 10.455, -21.42, 10.478, -22.016, 10.5, -21.182, 1, 10.911, -5.756, 11.322, 11.7, 11.733, 11.7, 0, 13, -21.42, 1, 13.033, -21.42, 13.067, -22.095, 13.1, -21.182, 1, 13.5, -10.22, 13.9, 11.7, 14.3, 11.7, 0, 15.567, -21.42, 1, 15.589, -21.42, 15.611, -22.023, 15.633, -21.182, 1, 16.033, -6.042, 16.433, 11.7, 16.833, 11.7, 0, 18.1, -21.182]}, {"Target": "Parameter", "Id": "Param98", "Segments": [0, -6, 0, 0.333, 0, 2, 2.033, 0, 2, 5.567, 0, 2, 7.033, 0, 2, 7.967, 0, 2, 9.967, 0, 2, 12.5, 0, 2, 13.333, 0, 2, 14, 0, 2, 14.633, 0, 2, 16.567, 0, 0, 18.1, -6]}, {"Target": "Parameter", "Id": "Param99", "Segments": [0, -10.921, 0, 0.333, 0, 2, 2.033, 0, 2, 5.567, 0, 2, 7.033, 0, 2, 7.967, 0, 2, 9.967, 0, 2, 12.5, 0, 2, 13.333, 0, 2, 14, 0, 2, 14.633, 0, 2, 16.567, 0, 0, 18.1, -10.921]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation67", "Segments": [0, -10.939, 0, 0.333, 0, 2, 2.033, 0, 2, 5.567, 0, 2, 7.033, 0, 2, 7.967, 0, 2, 9.967, 0, 2, 12.5, 0, 2, 13.333, 0, 2, 14, 0, 2, 14.633, 0, 2, 16.567, 0, 0, 18.1, -10.939]}, {"Target": "Parameter", "Id": "Param281", "Segments": [0, 0, 2, 0.333, 0, 0, 1.6, 1, 0, 2.867, 0, 0, 4.133, 1, 0, 5.4, 0, 0, 6.733, 1, 0, 8, 0, 0, 9.267, 1, 0, 10.533, 0, 0, 11.8, 1, 0, 13.133, 0, 0, 14.4, 1, 0, 15.667, 0, 0, 16.933, 1, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param197mao2", "Segments": [0, 0, 2, 0.333, 0, 0, 0.9, 2.894, 0, 2.267, -2.454, 1, 2.467, -2.454, 2.667, -1.729, 2.867, 0, 1, 3.078, 1.825, 3.289, 2.894, 3.5, 2.894, 0, 4.867, -2.454, 1, 5.045, -2.454, 5.222, -1.63, 5.4, 0, 1, 5.611, 1.936, 5.822, 2.894, 6.033, 2.894, 0, 7.4, -2.454, 1, 7.6, -2.454, 7.8, -1.811, 8, 0, 1, 8.189, 1.71, 8.378, 2.894, 8.567, 2.894, 0, 9.967, -2.454, 1, 10.156, -2.454, 10.344, -1.682, 10.533, 0, 1, 10.744, 1.88, 10.956, 2.894, 11.167, 2.894, 0, 12.533, -2.454, 1, 12.733, -2.454, 12.933, -1.811, 13.133, 0, 1, 13.322, 1.71, 13.511, 2.894, 13.7, 2.894, 0, 15.067, -2.454, 1, 15.267, -2.454, 15.467, -1.729, 15.667, 0, 1, 15.878, 1.825, 16.089, 2.894, 16.3, 2.894, 0, 17.6, -2.454, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation72", "Segments": [0, 8.085, 2, 0.333, 8.085, 2, 2.033, 8.085, 2, 5.567, 8.085, 2, 7.033, 8.085, 2, 7.967, 8.085, 2, 9.967, 8.085, 2, 12.5, 8.085, 0, 13.333, 0, 2, 14, 0, 2, 14.633, 0, 2, 16.567, 0, 0, 17.767, 8.085, 2, 18.1, 8.085]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation73", "Segments": [0, 11.915, 2, 0.333, 11.915, 2, 2.033, 11.915, 2, 5.567, 11.915, 2, 7.033, 11.915, 2, 7.967, 11.915, 2, 9.967, 11.915, 2, 12.5, 11.915, 0, 13.333, 0, 2, 14, 0, 2, 14.633, 0, 2, 16.567, 0, 0, 17.767, 11.915, 2, 18.1, 11.915]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation74", "Segments": [0, 12.341, 2, 0.333, 12.341, 2, 2.033, 12.341, 2, 5.567, 12.341, 2, 7.033, 12.341, 2, 7.967, 12.341, 2, 9.967, 12.341, 2, 12.5, 12.341, 0, 13.333, 0, 2, 14, 0, 2, 14.633, 0, 2, 16.567, 0, 0, 17.767, 12.341, 2, 18.1, 12.341]}, {"Target": "Parameter", "Id": "Param112", "Segments": [0, -30, 0, 0.333, 0, 2, 2.033, 0, 2, 5.567, 0, 2, 7.033, 0, 2, 7.967, 0, 2, 9.967, 0, 2, 12.5, 0, 0, 13.333, -30, 2, 14, -30, 2, 14.633, -30, 2, 16.567, -30, 0, 17.767, 0, 0, 18.1, -30]}, {"Target": "Parameter", "Id": "Param272", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param293", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param273", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "ParamCheek", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param268", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param252", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param193", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param192", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param196", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param183", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param194", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param195", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param184", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param289", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param185", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param290", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param291", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param186", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param187", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param188", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param292", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param189", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param190", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param191", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param8", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param23", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param25", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param27", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param200", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param286", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param287", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param288", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param202", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param205", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param203", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param204", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param21", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param214", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param216", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param217", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param218", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param219", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param220", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param199", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param208", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param279", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param212", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param270", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param271", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param283", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param230", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param231", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param222", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param207", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param236", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param237", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param238", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param239", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param242", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param243", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param244", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param275", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param224", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param225", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param227", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param226", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param232", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param285", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param276", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param142", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param143", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param144", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param147", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param145", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param134", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param246", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param141", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param182", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param247", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param229", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param245", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param117", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param140", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param262", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param263", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param115", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param116", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param260", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param261", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param133", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param139", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param257", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param137", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param138", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param135", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param176", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param136", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param177", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param251", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation63", "Segments": [0, -0.675, 0, 18.1, -0.675]}, {"Target": "Parameter", "Id": "Param84", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param85", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param83", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param86", "Segments": [0, 1, 0, 18.1, 1]}, {"Target": "Parameter", "Id": "Param91", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param2", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param87", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param88", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param94", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param95", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param92", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param93", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param96", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param97", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param89", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param90", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation56", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param218mao2", "Segments": [0, 1, 0, 18.1, 1]}, {"Target": "Parameter", "Id": "Param211mao2", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param216mao2", "Segments": [0, -8.88, 0, 18.1, -8.88]}, {"Target": "Parameter", "Id": "Param217mao2", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param202mao2", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param203mao2", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param204mao2", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param199mao2", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param200mao2", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param201mao2", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param208mao2", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param209mao2", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param210mao2", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param205mao2", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param206mao2", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param207mao2", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param212mao2", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param213mao2", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param214mao2", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param215mao2", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param198mao2", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param219mao2", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation143mao2", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation144mao2", "Segments": [0, -10.82, 0, 18.1, -10.82]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation145mao2", "Segments": [0, 8.46, 0, 18.1, 8.46]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation146mao2", "Segments": [0, 28.83, 0, 18.1, 28.83]}, {"Target": "Parameter", "Id": "Param264", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param100", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param105", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param108", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param109", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param107", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param284", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param101", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param106", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param104", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param103", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param102", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation71", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param148", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param146", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param110", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param111", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param248", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param249", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param206", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param114", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param113", "Segments": [0, 1, 0, 18.1, 1]}, {"Target": "Parameter", "Id": "Param253", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param254", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param158", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param149", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param150", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param151", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param255", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param256", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param153", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param152", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param155", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param154", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param181", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param180", "Segments": [0, 0.5, 0, 18.1, 0.5]}, {"Target": "Parameter", "Id": "Param156", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param157", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param159", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param160", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param119", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param265", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param118", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param120", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param121", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param161", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param259", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param123", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param124", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param130", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param131", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param178", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param179", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param125", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param126", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param127", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param128", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param171", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param173", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param172", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param122", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param170", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param129", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param132", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param250", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param258", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param162", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param169", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param174", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param175", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param163", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param164", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param165", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param166", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param167", "Segments": [0, 0, 0, 18.1, 0]}, {"Target": "Parameter", "Id": "Param168", "Segments": [0, 0, 0, 18.1, 0]}], "UserData": [{"Time": 0.0, "Value": ""}, {"Time": 0.367, "Value": ""}, {"Time": 17.6, "Value": ""}]}