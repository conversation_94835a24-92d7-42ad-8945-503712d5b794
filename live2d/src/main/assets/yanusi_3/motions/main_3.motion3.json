{"Version": 3, "Meta": {"Duration": 19.733, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 355, "TotalSegmentCount": 3049, "TotalPointCount": 3699, "UserDataCount": 3, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 2, 0.333, 0, 0, 1.2, 21, 2, 1.867, 21, 0, 2.4, 23.52, 1, 2.644, 23.52, 2.889, 22.693, 3.133, 18.38, 1, 3.355, 14.459, 3.578, 10, 3.8, 10, 2, 4.467, 10, 2, 5.233, 10, 0, 6, 18, 2, 7.433, 18, 0, 7.833, 8, 2, 8.667, 8, 1, 8.867, 8, 9.067, 2.864, 9.267, -4, 1, 9.367, -7.432, 9.467, -8.06, 9.567, -8.06, 2, 10.633, -8.06, 0, 11.5, 5.88, 1, 11.578, 5.88, 11.655, 4.638, 11.733, 2.879, 1, 11.866, -0.138, 12, -1.355, 12.133, -1.355, 1, 12.278, -1.355, 12.422, -1.534, 12.567, 1.693, 1, 12.756, 5.914, 12.944, 24.081, 13.133, 27.468, 1, 13.289, 30.257, 13.444, 30, 13.6, 30, 1, 13.811, 30, 14.022, 8.73, 14.233, 2.931, 1, 14.366, -0.731, 14.5, 0, 14.633, 0, 1, 14.911, 0, 15.189, 2.371, 15.467, 7, 1, 15.689, 10.703, 15.911, 13.889, 16.133, 18, 1, 16.444, 23.756, 16.756, 27.468, 17.067, 27.468, 0, 18.4, 22.41, 2, 18.467, 22.41, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 2, 0.333, 0, 0, 0.633, 11, 0, 1.2, -6, 0, 1.633, -0.775, 2, 1.867, -0.775, 0, 2.1, -6.895, 0, 2.333, -3.415, 0, 2.733, -14, 1, 2.844, -14, 2.956, -8.26, 3.067, -6, 1, 3.134, -4.644, 3.2, -4.893, 3.267, -4.893, 0, 3.567, -14, 0, 3.967, -9.316, 2, 4.467, -9.316, 0, 4.667, -17, 1, 4.756, -17, 4.844, -6.649, 4.933, -6, 1, 5.033, -5.27, 5.133, -5.427, 5.233, -4.74, 1, 5.311, -4.206, 5.389, 2, 5.467, 2, 0, 6, -16, 0, 6.467, -10, 0, 7, -24, 2, 7.433, -24, 0, 7.567, -27, 0, 7.933, 5.909, 0, 8.2, -17, 0, 8.667, -1, 0, 9.567, -7, 2, 10.633, -7, 0, 10.967, -6, 1, 11.145, -6, 11.322, -15.249, 11.5, -23, 1, 11.644, -29.298, 11.789, -30, 11.933, -30, 2, 12.467, -30, 1, 12.678, -30, 12.889, -20.177, 13.1, -17.102, 1, 13.267, -14.673, 13.433, -15, 13.6, -15, 0, 14.633, -27, 0, 15.467, -20, 0, 16.133, -28, 0, 16.467, -16, 1, 16.667, -16, 16.867, -21.453, 17.067, -23, 1, 17.511, -26.438, 17.956, -27, 18.4, -27, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 2, 0.333, 0, 1, 0.622, 0, 0.911, 7.994, 1.2, 12, 1, 1.344, 14.003, 1.489, 13.56, 1.633, 13.56, 2, 1.867, 13.56, 1, 2.034, 13.56, 2.2, 11.943, 2.367, 10.88, 1, 2.622, 9.249, 2.878, 8.764, 3.133, 7, 1, 3.355, 5.466, 3.578, 3, 3.8, 3, 2, 4.467, 3, 0, 4.933, 9.115, 2, 5.2, 9.115, 1, 5.289, 9.115, 5.378, 9.862, 5.467, 10.88, 1, 5.611, 12.533, 5.756, 13.939, 5.9, 15.463, 1, 6, 16.518, 6.1, 17.04, 6.2, 17.04, 2, 6.467, 17.04, 0, 6.633, 15.463, 0, 7, 20, 1, 7.067, 20, 7.133, 18.925, 7.2, 18.816, 1, 7.278, 18.688, 7.355, 18.782, 7.433, 18.595, 1, 7.566, 18.275, 7.7, 13, 7.833, 13, 0, 8.233, 15.28, 0, 8.6, 12.36, 2, 8.667, 12.36, 0, 9.567, 2.36, 2, 10.633, 2.36, 1, 10.922, 2.36, 11.211, 5.284, 11.5, 8, 1, 11.644, 9.358, 11.789, 9.32, 11.933, 9.32, 2, 12.467, 9.32, 1, 12.689, 9.32, 12.911, 10.381, 13.133, 11.076, 1, 13.289, 11.562, 13.444, 11.617, 13.6, 12, 1, 13.811, 12.519, 14.022, 13.678, 14.233, 13.898, 1, 14.366, 14.036, 14.5, 14, 14.633, 14, 0, 15.467, 12, 0, 16.133, 12.72, 0, 16.467, 7.92, 1, 16.667, 7.92, 16.867, 10.79, 17.067, 11, 1, 17.511, 11.467, 17.956, 11.605, 18.4, 12, 1, 18.489, 12.079, 18.578, 13.716, 18.667, 13.716, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 2, 0.333, 1, 1, 0.355, 1, 0.378, 0.897, 0.4, 0.5, 1, 0.411, 0.302, 0.422, 0, 0.433, 0, 2, 0.467, 0, 0, 0.6, 0.9, 2, 1.2, 0.9, 2, 1.767, 0.9, 0, 1.967, 0.7, 2, 2.467, 0.7, 0, 2.567, 0, 1, 2.611, 0, 2.656, 0.879, 2.7, 0.9, 1, 2.878, 0.983, 3.055, 1, 3.233, 1, 0, 3.533, 0.82, 0, 4.067, 0.879, 2, 4.467, 0.879, 0, 4.6, 0.7, 1, 4.644, 0.7, 4.689, 0.783, 4.733, 0.8, 1, 4.9, 0.863, 5.066, 0.879, 5.233, 0.879, 0, 5.3, 0, 2, 5.333, 0, 0, 5.433, 0.8, 2, 6.333, 0.8, 0, 6.4, 0, 0, 6.5, 0.8, 2, 7.467, 0.8, 0, 7.567, 0, 2, 7.6, 0, 0, 7.767, 0.9, 2, 8.667, 0.9, 0, 8.8, 0, 2, 8.833, 0, 0, 9.067, 0.9, 0, 9.567, 0.774, 0, 10.833, 0.833, 0, 10.9, 0, 2, 10.933, 0, 0, 11.067, 0.9, 2, 11.5, 0.9, 0, 11.567, 0, 2, 11.6, 0, 0, 11.7, 0.9, 0, 12.067, 0.808, 0, 12.267, 0.9, 0, 12.333, 0, 2, 12.367, 0, 0, 12.433, 0.8, 2, 12.967, 0.8, 2, 13.6, 0.8, 0, 14.233, 0.9, 0, 14.4, 0, 0, 14.667, 0.9, 2, 15.267, 0.9, 1, 15.389, 0.9, 15.511, 0.801, 15.633, 0.781, 1, 15.8, 0.754, 15.966, 0.752, 16.133, 0.729, 1, 16.189, 0.721, 16.244, 0, 16.3, 0, 0, 16.467, 0.9, 0, 16.733, 0.872, 0, 16.9, 0.898, 0, 17.067, 0.872, 0, 17.233, 0.898, 0, 17.4, 0.872, 0, 17.567, 0.898, 0, 17.733, 0.872, 0, 17.9, 0.898, 0, 18.067, 0.872, 1, 18.122, 0.872, 18.178, 0.896, 18.233, 0.898, 1, 18.3, 0.9, 18.366, 0.9, 18.433, 0.9, 2, 18.6, 0.9, 0, 18.767, 0, 2, 18.8, 0, 0, 19.733, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.467, 0, 0, 0.6, 1, 2, 1.2, 1, 2, 1.767, 1, 2, 2.467, 1, 0, 2.567, 0, 0, 2.7, 1, 2, 4.467, 1, 0, 4.733, 0.8, 0, 5.233, 1, 0, 5.3, 0, 2, 5.333, 0, 0, 5.433, 1, 0, 6, 0.5, 2, 6.333, 0.5, 0, 6.4, 0, 0, 6.5, 1, 2, 7.467, 1, 0, 7.567, 0, 2, 7.6, 0, 2, 7.767, 0, 2, 8.667, 0, 2, 10.833, 0, 2, 12.267, 0, 2, 16.133, 0, 0, 18.433, 1, 2, 18.6, 1, 0, 18.767, 0, 2, 18.8, 0, 2, 19, 0, 2, 19.733, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 2, 0.333, 1, 1, 0.355, 1, 0.378, 0.897, 0.4, 0.5, 1, 0.411, 0.302, 0.422, 0, 0.433, 0, 2, 0.467, 0, 0, 0.6, 0.9, 2, 1.2, 0.9, 2, 1.767, 0.9, 0, 1.967, 0.7, 2, 2.467, 0.7, 0, 2.567, 0, 1, 2.611, 0, 2.656, 0.879, 2.7, 0.9, 1, 2.878, 0.983, 3.055, 1, 3.233, 1, 0, 3.533, 0.857, 0, 4.067, 0.889, 2, 4.467, 0.889, 0, 4.6, 0.7, 1, 4.644, 0.7, 4.689, 0.781, 4.733, 0.8, 1, 4.9, 0.871, 5.066, 0.889, 5.233, 0.889, 0, 5.3, 0, 2, 5.333, 0, 0, 5.433, 0.8, 2, 6.333, 0.8, 0, 6.4, 0, 0, 6.5, 0.8, 2, 7.467, 0.8, 0, 7.567, 0, 2, 7.6, 0, 0, 7.767, 0.9, 2, 8.667, 0.9, 0, 8.8, 0, 2, 8.833, 0, 0, 9.067, 0.9, 0, 9.567, 0.827, 0, 10.833, 0.905, 0, 10.9, 0, 2, 10.933, 0, 0, 11.067, 0.9, 2, 11.5, 0.9, 0, 11.567, 0, 2, 11.6, 0, 0, 11.7, 0.9, 0, 12.067, 0.74, 0, 12.267, 0.9, 0, 12.333, 0, 2, 12.367, 0, 0, 12.433, 0.8, 2, 12.967, 0.8, 2, 13.6, 0.8, 0, 14.233, 0.9, 0, 14.4, 0, 0, 14.667, 0.9, 2, 15.267, 0.9, 1, 15.389, 0.9, 15.511, 0.79, 15.633, 0.771, 1, 15.8, 0.745, 15.966, 0.743, 16.133, 0.721, 1, 16.189, 0.714, 16.244, 0, 16.3, 0, 0, 16.467, 0.9, 0, 16.733, 0.86, 0, 16.9, 0.898, 0, 17.067, 0.86, 0, 17.233, 0.898, 0, 17.4, 0.86, 0, 17.567, 0.898, 0, 17.733, 0.86, 0, 17.9, 0.898, 0, 18.067, 0.86, 1, 18.122, 0.86, 18.178, 0.896, 18.233, 0.898, 1, 18.3, 0.9, 18.366, 0.9, 18.433, 0.9, 2, 18.6, 0.9, 0, 18.767, 0, 2, 18.8, 0, 0, 19.733, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.467, 0, 0, 0.6, 1, 2, 1.2, 1, 2, 1.767, 1, 2, 2.467, 1, 0, 2.567, 0, 0, 2.7, 1, 2, 4.467, 1, 0, 4.733, 0.8, 0, 5.233, 1, 0, 5.3, 0, 2, 5.333, 0, 0, 5.433, 1, 0, 6, 0.5, 2, 6.333, 0.5, 0, 6.4, 0, 0, 6.5, 0.9, 2, 7.467, 0.9, 0, 7.567, 0, 2, 7.6, 0, 2, 7.767, 0, 2, 8.667, 0, 2, 10.633, 0, 2, 11.933, 0, 2, 16.133, 0, 0, 18.433, 1, 2, 18.6, 1, 0, 18.767, 0, 2, 18.8, 0, 2, 19, 0, 2, 19.733, 0]}, {"Target": "Parameter", "Id": "Param76", "Segments": [0, 0, 2, 0.333, 0, 0, 1.2, -30, 2, 4.467, -30, 2, 5.233, -30, 2, 7.433, -30, 2, 8.667, -30, 2, 10.633, -30, 2, 11.933, -30, 2, 16.133, -30, 1, 16.211, -30, 16.289, -13.961, 16.367, 7, 1, 16.434, 24.967, 16.5, 30, 16.567, 30, 0, 16.733, 15.5, 0, 16.867, 28.954, 0, 17, 16.568, 0, 17.133, 30, 0, 17.267, 15.5, 0, 17.4, 29.115, 0, 17.533, 16.729, 0, 17.667, 29.115, 0, 17.8, 16.729, 0, 17.933, 29.115, 0, 18.067, 16.729, 0, 18.2, 29.115, 0, 18.333, 16.729, 2, 18.433, 16.729, 0, 18.8, 0, 2, 19.733, 0]}, {"Target": "Parameter", "Id": "Param273", "Segments": [0, 0, 2, 0.333, 0, 2, 1.2, 0, 2, 3.233, 0, 0, 3.533, 0.5, 2, 4.467, 0.5, 2, 5.233, 0.5, 0, 6, 0, 2, 7.433, 0, 2, 8.667, 0, 2, 9.067, 0, 0, 9.567, -0.5, 2, 10.633, -0.5, 0, 11.5, 0, 2, 11.933, 0, 2, 12.5, 0, 1, 12.656, 0, 12.811, 0.037, 12.967, 0.3, 1, 13.178, 0.658, 13.389, 1, 13.6, 1, 0, 14.233, 0.7, 2, 16.133, 0.7, 0, 16.3, 0, 2, 18.433, 0, 2, 18.8, 0, 2, 19.733, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 2, 0.333, 0, 0, 1.2, -0.4, 2, 1.767, -0.4, 1, 1.822, -0.4, 1.878, -0.233, 1.933, -0.1, 1, 2.089, 0.273, 2.244, 0.4, 2.4, 0.4, 0, 2.767, -0.4, 0, 3.633, -0.244, 2, 4.467, -0.244, 2, 5.233, -0.244, 1, 5.489, -0.244, 5.744, -0.214, 6, -0.1, 1, 6.056, -0.075, 6.111, 0.2, 6.167, 0.2, 2, 6.333, 0.2, 0, 6.5, 0.6, 2, 7.433, 0.6, 0, 7.833, -0.1, 2, 8.667, -0.1, 1, 8.789, -0.1, 8.911, -0.201, 9.033, -0.4, 1, 9.211, -0.689, 9.389, -0.832, 9.567, -0.832, 2, 10.633, -0.832, 1, 10.655, -0.832, 10.678, -0.46, 10.7, -0.4, 1, 10.744, -0.28, 10.789, -0.279, 10.833, -0.142, 1, 10.866, -0.04, 10.9, 0.6, 10.933, 0.6, 2, 11.2, 0.6, 2, 11.5, 0.6, 0, 11.633, -1, 2, 12.3, -1, 1, 12.356, -1, 12.411, 0.142, 12.467, 0.2, 1, 12.634, 0.374, 12.8, 0.4, 12.967, 0.4, 0, 14.267, 0.1, 0, 14.367, 0.2, 2, 15.3, 0.2, 1, 15.356, 0.2, 15.411, 0.432, 15.467, 0.46, 1, 15.689, 0.573, 15.911, 0.6, 16.133, 0.6, 0, 16.467, 0.1, 0, 16.933, 0.4, 2, 18.433, 0.4, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 2, 0.333, 0, 2, 1.2, 0, 2, 1.767, 0, 0, 1.933, 0.1, 2, 2.4, 0.1, 0, 2.767, 0, 2, 4.467, 0, 2, 5.233, 0, 0, 6, -0.6, 0, 6.167, -0.184, 2, 6.333, -0.184, 0, 6.5, -0.802, 2, 7.433, -0.802, 1, 7.566, -0.802, 7.7, -0.69, 7.833, -0.3, 1, 7.966, 0.09, 8.1, 0.4, 8.233, 0.4, 0, 8.6, 0, 2, 8.667, 0, 0, 9.033, -0.5, 2, 9.567, -0.5, 2, 10.633, -0.5, 2, 10.7, -0.5, 1, 10.744, -0.5, 10.789, -0.502, 10.833, -0.53, 1, 10.866, -0.551, 10.9, -0.6, 10.933, -0.6, 0, 11.2, -0.3, 0, 11.5, -0.6, 0, 11.633, -0.5, 2, 12.3, -0.5, 1, 12.356, -0.5, 12.411, -0.482, 12.467, -0.4, 1, 12.634, -0.154, 12.8, 0, 12.967, 0, 0, 14.267, -0.102, 0, 14.367, 0.3, 2, 15.3, 0.3, 1, 15.356, 0.3, 15.411, -0.164, 15.467, -0.22, 1, 15.689, -0.446, 15.911, -0.5, 16.133, -0.5, 0, 16.467, 0.2, 0, 16.933, -0.1, 2, 18.433, -0.1, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 2, 0.333, 0, 0, 0.6, -0.3, 2, 1.2, -0.3, 2, 3.333, -0.3, 0, 3.533, -0.2, 2, 4.467, -0.2, 2, 5.233, -0.2, 2, 7.433, -0.2, 2, 8.067, -0.2, 0, 8.667, 0, 2, 10.633, 0, 2, 11.933, 0, 0, 12.5, -0.152, 0, 16.133, 0, 2, 18.433, 0, 2, 19.733, 0]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 2, 0.333, 0, 0, 0.6, -0.278, 2, 1.2, -0.278, 0, 1.6, -0.168, 2, 1.767, -0.168, 0, 1.933, -0.272, 2, 2.767, -0.272, 0, 3.333, 0, 2, 4.467, 0, 0, 4.667, -0.4, 1, 4.745, -0.4, 4.822, -0.168, 4.9, -0.1, 1, 5.011, -0.003, 5.122, 0, 5.233, 0, 0, 6, -0.144, 2, 7.433, -0.144, 0, 8.067, -0.4, 2, 8.667, -0.4, 0, 8.867, -0.55, 0, 9.567, -0.346, 2, 10.633, -0.346, 2, 11.933, -0.346, 0, 12.5, -0.2, 0, 16.133, -0.346, 2, 18.433, -0.346, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 2, 0.333, 0, 0, 0.6, -0.2, 2, 1.2, -0.2, 2, 3.333, -0.2, 0, 3.533, -0.1, 2, 4.467, -0.1, 2, 5.233, -0.1, 2, 7.433, -0.1, 2, 8.067, -0.1, 0, 8.667, 0, 2, 10.633, 0, 2, 11.933, 0, 0, 12.5, -0.062, 0, 16.133, 0, 2, 18.433, 0, 2, 19.733, 0]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 2, 0.333, 0, 0, 0.6, -0.264, 2, 1.2, -0.264, 0, 1.6, -0.17, 2, 1.767, -0.17, 0, 1.933, -0.288, 2, 2.767, -0.288, 0, 3.333, 0, 2, 4.467, 0, 0, 4.667, -0.4, 1, 4.745, -0.4, 4.822, -0.168, 4.9, -0.1, 1, 5.011, -0.003, 5.122, 0, 5.233, 0, 0, 6, -0.13, 2, 7.433, -0.13, 1, 7.644, -0.13, 7.856, -0.34, 8.067, -0.4, 1, 8.267, -0.457, 8.467, -0.457, 8.667, -0.5, 1, 8.734, -0.514, 8.8, -0.642, 8.867, -0.642, 0, 9.567, -0.332, 2, 10.633, -0.332, 2, 11.933, -0.332, 0, 12.5, -0.1, 0, 16.133, -0.332, 2, 18.433, -0.332, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, -1, 2, 0.333, -1, 0, 0.6, 0, 2, 1.2, 0, 2, 3.333, 0, 0, 3.533, -1, 2, 4.467, -1, 0, 4.867, 0, 2, 5.233, 0, 0, 5.467, -0.5, 2, 7.433, -0.5, 0, 8.067, -1, 2, 8.667, -1, 2, 10.633, -1, 2, 11.933, -1, 2, 16.133, -1, 2, 18.433, -1, 2, 19.733, -1]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, -1, 2, 0.333, -1, 0, 0.6, 0, 2, 1.2, 0, 2, 3.333, 0, 0, 3.533, -1, 2, 4.467, -1, 0, 4.867, 0, 2, 5.233, 0, 0, 5.467, -0.5, 2, 7.433, -0.5, 0, 8.067, -1, 2, 8.667, -1, 2, 10.633, -1, 2, 11.933, -1, 2, 16.133, -1, 2, 18.433, -1, 2, 19.733, -1]}, {"Target": "Parameter", "Id": "Param", "Segments": [0, -30, 2, 0.333, -30, 1, 0.355, -30, 0.378, 0, 0.4, 0, 0, 1.2, -3, 0, 2.333, 2, 2, 3.333, 2, 0, 4, 0, 2, 4.467, 0, 2, 5.233, 0, 2, 7.433, 0, 2, 8.067, 0, 2, 8.1, 0, 2, 8.233, 0, 2, 8.667, 0, 2, 8.933, 0, 2, 9.333, 0, 0, 9.567, -30, 2, 10.633, -30, 1, 10.755, -30, 10.878, -15.434, 11, 0, 1, 11.111, 14.031, 11.222, 17, 11.333, 17, 0, 11.533, 0, 0, 11.933, 30, 1, 12.044, 30, 12.156, 26.892, 12.267, 20, 1, 12.478, 6.906, 12.689, 0, 12.9, 0, 2, 14.533, 0, 1, 15.066, 0, 15.6, 5.424, 16.133, 20, 1, 16.3, 24.555, 16.466, 30, 16.633, 30, 0, 16.867, 22, 0, 17.067, 30, 0, 17.233, 21, 0, 17.433, 30, 0, 17.633, 26, 0, 17.8, 30, 0, 17.967, 21, 0, 18.133, 30, 0, 18.3, 24, 0, 18.433, 30, 0, 19.733, -30]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 1, 2, 0.333, 1, 1, 0.355, 1, 0.378, -0.339, 0.4, -0.4, 1, 0.589, -0.917, 0.778, -1.1, 0.967, -1.1, 0, 1.2, -0.5, 0, 1.367, -1.3, 0, 1.733, -0.7, 0, 1.933, -1.5, 0, 2.133, -0.6, 0, 2.333, -1, 2, 2.5, -1, 2, 2.967, -1, 0, 3.133, -0.4, 0, 3.333, -1, 0, 4, -0.6, 2, 4.467, -0.6, 2, 5.233, -0.6, 0, 5.467, -0.4, 2, 5.6, -0.4, 1, 5.656, -0.401, 5.711, -1, 5.767, -1, 0, 5.933, -0.2, 1, 6.122, -0.2, 6.311, -0.204, 6.5, -0.227, 1, 6.556, -0.234, 6.611, -1.1, 6.667, -1.1, 1, 6.745, -1.1, 6.822, -0.441, 6.9, -0.4, 1, 7.078, -0.307, 7.255, -0.287, 7.433, -0.2, 1, 7.511, -0.162, 7.589, 0, 7.667, 0, 0, 7.967, -0.5, 0, 8.067, -0.3, 1, 8.122, -0.3, 8.178, -0.481, 8.233, -0.6, 1, 8.378, -0.908, 8.522, -1, 8.667, -1, 2, 8.933, -1, 0, 9.133, -0.2, 1, 9.2, -0.2, 9.266, -0.63, 9.333, -0.8, 1, 9.411, -0.999, 9.489, -1, 9.567, -1, 2, 10.633, -1, 2, 11.533, -1, 2, 11.933, -1, 0, 12.9, -0.6, 0, 13.233, -1, 2, 16.133, -1, 2, 18.433, -1, 0, 19.733, 1]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, 0.557, 0, 0.533, 0.3, 0, 0.667, 0.6, 0, 0.8, 0.2, 0, 0.967, 0.7, 0, 1.2, 0.3, 0, 1.4, 0.7, 0, 1.567, 0.1, 0, 1.733, 0.6, 0, 1.933, 0.3, 0, 2.133, 0.6, 0, 2.333, 0, 0, 2.5, 0.4, 0, 2.667, 0.2, 0, 2.833, 0.6, 1, 2.878, 0.6, 2.922, 0.379, 2.967, 0.3, 1, 3.022, 0.201, 3.078, 0.2, 3.133, 0.2, 0, 3.333, 0.6, 2, 3.5, 0.6, 0, 4, 0, 2, 4.467, 0, 2, 5.233, 0, 0, 5.467, 0.6, 0, 5.6, 0.4, 0, 5.767, 0.7, 0, 5.933, 0.4, 0, 6.133, 0.8, 0, 6.3, 0.4, 0, 6.4, 0.8, 0, 6.5, 0.2, 0, 6.7, 0.8, 1, 6.767, 0.8, 6.833, 0.624, 6.9, 0.4, 1, 6.989, 0.101, 7.078, 0, 7.167, 0, 2, 7.433, 0, 0, 7.667, 0.24, 0, 7.8, 0, 0, 7.967, 0.6, 0, 8.067, 0.3, 0, 8.233, 0.8, 0, 8.367, 0, 0, 8.567, 0.7, 0, 8.667, 0.3, 0, 8.8, 0.7, 0, 8.933, 0.3, 2, 9.133, 0.3, 0, 9.333, 0.5, 0, 9.567, 0, 2, 9.867, 0, 2, 10.633, 0, 2, 10.833, 0, 0, 11, 0.3, 0, 11.333, 0.2, 0, 11.533, 0.4, 0, 11.933, 0, 2, 12.267, 0, 2, 12.667, 0, 0, 12.9, 0.4, 0, 13.1, 0.2, 0, 13.233, 0.6, 0, 13.467, 0.2, 0, 13.667, 0.8, 0, 13.833, 0.4, 0, 14.067, 0.8, 0, 14.2, 0, 0, 14.333, 0.7, 0, 14.533, 0.2, 0, 14.733, 0.4, 0, 14.867, 0.1, 0, 15.067, 0.6, 1, 15.156, 0.6, 15.244, 0.533, 15.333, 0.4, 1, 15.511, 0.133, 15.689, 0, 15.867, 0, 2, 16.133, 0, 0, 16.633, 0.3, 2, 18.433, 0.3, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param268", "Segments": [0, 0, 2, 0.333, 0, 2, 1.2, 0, 2, 4.467, 0, 2, 5.233, 0, 2, 7.433, 0, 2, 8.667, 0, 2, 10.633, 0, 2, 11.933, 0, 2, 16.133, 0, 0, 16.633, 1, 0, 18.433, 0, 2, 19.733, 0]}, {"Target": "Parameter", "Id": "Param269", "Segments": [0, 0, 2, 0.333, 0, 2, 1.2, 0, 2, 4.467, 0, 2, 5.233, 0, 2, 7.433, 0, 0, 7.967, 0.2, 2, 8.667, 0.2, 2, 10.633, 0.2, 2, 11.933, 0.2, 2, 16.133, 0.2, 2, 18.433, 0.2, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param252", "Segments": [0, 0, 2, 0.333, 0, 2, 1.2, 0, 2, 4.467, 0, 2, 5.233, 0, 2, 7.433, 0, 2, 8.667, 0, 2, 10.633, 0, 0, 11.467, 1, 2, 16.133, 1, 2, 18.433, 1, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 2, 0.333, 0, 1, 0.622, 0, 0.911, -15.469, 1.2, -25.86, 1, 1.333, -30.656, 1.467, -30, 1.6, -30, 0, 2.333, -11.88, 1, 2.555, -11.88, 2.778, -14.564, 3, -19.15, 1, 3.256, -24.424, 3.511, -26.711, 3.767, -26.711, 2, 4.467, -26.711, 2, 5.233, -26.711, 1, 5.489, -26.711, 5.744, -18.997, 6, -7.89, 1, 6.144, -1.612, 6.289, 0, 6.433, 0, 0, 6.933, -3.4, 2, 7.433, -3.4, 0, 7.933, -30, 2, 8.667, -30, 0, 9.567, -18, 2, 10.633, -18, 0, 11.667, -22.44, 0, 12.2, -15, 0, 12.767, -23, 0, 13.233, -15.868, 0, 13.733, -22.44, 0, 14.133, -15.868, 0, 14.6, -22.44, 0, 14.967, -15.451, 0, 15.467, -22.44, 0, 15.833, -15.868, 2, 16.133, -15.868, 0, 17.067, 15, 2, 18.433, 15, 0, 19.233, -4, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 2, 0.333, 0, 0, 0.7, 12, 0, 1.2, -8, 0, 1.633, -3.5, 0, 1.967, -5.094, 0, 2.367, -1.823, 0, 2.8, -7.763, 0, 3.167, 1.201, 0, 3.7, -8, 0, 4.033, -5.094, 1, 4.144, -5.094, 4.256, -5.193, 4.367, -7.262, 1, 4.467, -9.123, 4.567, -13.186, 4.667, -13.186, 1, 4.767, -13.186, 4.867, -10.792, 4.967, -8, 1, 5.056, -5.518, 5.144, -4.652, 5.233, -1.823, 1, 5.311, 0.652, 5.389, 5.446, 5.467, 5.446, 0, 6, -23.64, 0, 6.4, -12, 0, 6.933, -20, 1, 7.1, -20, 7.266, -19.097, 7.433, -13.186, 1, 7.555, -8.851, 7.678, -1, 7.8, -1, 0, 8.167, -20, 0, 8.433, -12, 2, 8.633, -12, 1, 8.755, -12, 8.878, -5.026, 9, -3.928, 1, 9.189, -2.232, 9.378, -1.708, 9.567, -0.48, 1, 9.922, 1.832, 10.278, 3.687, 10.633, 6.72, 1, 10.766, 7.857, 10.9, 13.467, 11.033, 13.467, 0, 11.7, 0, 2, 11.933, 0, 0, 13.8, 6, 0, 15.8, 1.839, 0, 16.1, 8, 0, 16.7, -12, 0, 17.367, -5, 2, 18.433, -5, 0, 19.233, 8, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 2, 0.333, 0, 2, 1.2, 0, 2, 2.8, 0, 2, 4.467, 0, 2, 5.233, 0, 1, 5.489, 0, 5.744, 5.139, 6, 6, 1, 6.133, 6.449, 6.267, 6.272, 6.4, 6.272, 0, 6.933, 5.52, 0, 7.4, 6, 2, 7.433, 6, 0, 7.9, 2.738, 0, 8.1, 4.613, 0, 8.3, 3.878, 2, 8.667, 3.878, 2, 10.633, 3.878, 0, 11.7, 6.458, 2, 11.933, 6.458, 2, 16.133, 6.458, 2, 18.433, 6.458, 0, 19.267, -1.062, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param267", "Segments": [0, 0, 2, 0.333, 0, 0, 1.2, 9.9, 0, 1.633, 8.52, 2, 2.8, 8.52, 2, 4.4, 8.52, 2, 4.467, 8.52, 1, 4.6, 8.52, 4.734, 5.094, 4.867, 4.866, 1, 4.989, 4.657, 5.111, 4.858, 5.233, 4.549, 1, 5.489, 3.904, 5.744, -14, 6, -14, 2, 7.433, -14, 0, 8.1, 0, 1, 8.289, 0, 8.478, -0.379, 8.667, -1.56, 1, 8.9, -3.019, 9.134, -4.02, 9.367, -4.02, 2, 10.633, -4.02, 0, 11.933, 0.9, 0, 12.5, 0, 2, 16.133, 0, 2, 18.433, 0, 2, 19.733, 0]}, {"Target": "Parameter", "Id": "Param3", "Segments": [0, 0, 2, 0.333, 0, 0, 1.2, -13.02, 2, 2.8, -13.02, 0, 4.4, -25.32, 2, 4.467, -25.32, 2, 5.233, -25.32, 1, 5.489, -25.32, 5.744, -10.29, 6, -5, 1, 6.189, -1.09, 6.378, -1.58, 6.567, -1.58, 0, 7.167, -8.553, 2, 7.433, -8.553, 0, 7.8, -4.545, 0, 8.1, -11.142, 0, 8.3, -8.742, 2, 8.667, -8.742, 0, 9.567, -13.002, 2, 10.633, -13.002, 1, 10.978, -13.002, 11.322, -8.638, 11.667, -5, 1, 12.067, -0.775, 12.467, 0, 12.867, 0, 0, 14.6, -9.342, 1, 15.111, -9.342, 15.622, -5.531, 16.133, -1.604, 1, 16.166, -1.348, 16.2, -1.52, 16.233, -1.52, 2, 18.433, -1.52, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param4", "Segments": [0, 0, 2, 0.333, 0, 0, 1.2, -10.803, 0, 1.633, -8.424, 0, 1.967, -9.924, 0, 2.367, -7.824, 0, 2.8, -9.095, 0, 3.033, -7.824, 0, 3.633, -10.206, 0, 3.933, -8.504, 2, 4.4, -8.504, 2, 4.467, -8.504, 0, 5.233, -10.206, 0, 5.467, -6.177, 0, 6, -13.04, 0, 6.4, -11.3, 0, 6.933, -16.16, 1, 7.1, -16.16, 7.266, -16.102, 7.433, -13.34, 1, 7.544, -11.499, 7.656, 3.22, 7.767, 3.22, 0, 8.067, -5.266, 1, 8.178, -5.266, 8.289, -4.703, 8.4, -3.23, 1, 8.489, -2.052, 8.578, -1.25, 8.667, -1.25, 0, 9.567, -7.79, 2, 10.633, -7.79, 0, 11.267, -16.16, 1, 11.411, -16.16, 11.556, -14.961, 11.7, -13.04, 1, 11.822, -11.415, 11.945, -10.803, 12.067, -10.803, 0, 13.2, -16.106, 1, 13.733, -16.106, 14.267, -13.097, 14.8, -13.041, 1, 15.133, -13.006, 15.467, -13.018, 15.8, -13, 1, 15.911, -12.994, 16.022, -10.803, 16.133, -10.803, 0, 17.2, -19.698, 0, 18.367, -16.116, 1, 18.389, -16.116, 18.411, -16.052, 18.433, -16.16, 1, 18.533, -16.648, 18.633, -18.063, 18.733, -18.063, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param266", "Segments": [0, 0, 2, 0.333, 0, 0, 1.2, -7.26, 1, 1.344, -7.26, 1.489, -7.266, 1.633, -5.28, 1, 1.866, -2.072, 2.1, 1.86, 2.333, 1.86, 2, 2.8, 1.86, 0, 3.167, 8.04, 0, 3.933, -8.7, 0, 4.467, -4.86, 2, 5.233, -4.86, 1, 5.489, -4.86, 5.744, -2.809, 6, 0.72, 1, 6.189, 3.328, 6.378, 4.38, 6.567, 4.38, 0, 7.267, -7, 1, 7.322, -7, 7.378, -7.433, 7.433, -5.874, 1, 7.555, -2.444, 7.678, 6.85, 7.8, 6.85, 0, 8.1, -10.13, 0, 8.4, -7.61, 2, 8.667, -7.61, 0, 9.567, -12.35, 2, 10.633, -12.35, 1, 10.978, -12.35, 11.322, -12.295, 11.667, -10.49, 1, 12.067, -8.393, 12.467, -5.28, 12.867, -5.28, 0, 14.6, -10.13, 1, 15.111, -10.13, 15.622, -6.912, 16.133, -3.521, 1, 16.178, -3.226, 16.222, -3.399, 16.267, -3.399, 2, 18.433, -3.399, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param5", "Segments": [0, 0, 2, 0.333, 0, 0, 1.2, -19, 2, 1.633, -19, 0, 2.7, -17.39, 2, 2.8, -17.39, 0, 3.2, -13.25, 0, 3.733, -21.141, 1, 3.822, -21.141, 3.911, -18.686, 4, -18.402, 1, 4.156, -17.905, 4.311, -17.836, 4.467, -17.39, 1, 4.522, -17.231, 4.578, -13.976, 4.633, -13.976, 0, 4.867, -17.39, 2, 5.233, -17.39, 0, 6, -27, 2, 6.467, -27, 0, 6.867, -17, 0, 7.167, -20, 1, 7.256, -20, 7.344, -20.202, 7.433, -19.34, 1, 7.589, -17.832, 7.744, -14, 7.9, -14, 0, 8.167, -22, 1, 8.334, -22, 8.5, -21.612, 8.667, -19.34, 1, 8.789, -17.674, 8.911, -12.267, 9.033, -11, 1, 9.178, -9.503, 9.322, -8.816, 9.467, -7.88, 1, 9.678, -6.512, 9.889, -5.96, 10.1, -5.96, 2, 10.633, -5.96, 0, 11.233, -12.178, 0, 11.933, -9.733, 0, 12.5, -11.773, 0, 13.167, -5.96, 0, 13.767, -11.173, 0, 14.233, -5.96, 0, 14.7, -11, 0, 15.233, -4.393, 0, 15.8, -11, 1, 15.911, -11, 16.022, -7.281, 16.133, -4.873, 1, 16.466, 2.352, 16.8, 4.737, 17.133, 4.737, 1, 17.566, 4.737, 18, 2.46, 18.433, 1.24, 1, 18.866, 0.02, 19.3, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param6", "Segments": [0, 0, 2, 0.333, 0, 0, 1.2, 25.08, 2, 1.633, 25.08, 0, 2.7, 25.02, 2, 2.8, 25.02, 0, 3.2, 30, 0, 3.733, 23.597, 0, 4, 26.124, 0, 4.467, 25.02, 0, 4.633, 28.034, 1, 4.711, 28.034, 4.789, 25.581, 4.867, 25.44, 1, 4.989, 25.219, 5.111, 25.424, 5.233, 25.02, 1, 5.489, 24.175, 5.744, 4, 6, 4, 2, 6.467, 4, 0, 6.867, 14, 0, 7.167, 10, 1, 7.256, 10, 7.344, 12.801, 7.433, 15.58, 1, 7.589, 20.443, 7.744, 22, 7.9, 22, 0, 8.167, 15, 1, 8.334, 15, 8.5, 14.946, 8.667, 15.58, 1, 8.789, 16.045, 8.911, 23.08, 9.033, 23.08, 0, 9.467, 17.14, 0, 10.1, 19.84, 2, 10.633, 19.84, 0, 11.667, 25.36, 2, 11.933, 25.36, 1, 12.122, 25.36, 12.311, 25.491, 12.5, 23.02, 1, 12.722, 20.113, 12.945, 13.72, 13.167, 13.72, 0, 13.767, 24.94, 0, 14.233, 18.1, 0, 14.7, 24.34, 0, 15.233, 19.54, 0, 15.8, 23.74, 0, 16.133, 20.8, 2, 18.433, 20.8, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 2, 0.333, 0, 2, 1.2, 0, 2, 2.8, 0, 2, 4.467, 0, 2, 5.233, 0, 2, 7.433, 0, 2, 8.667, 0, 2, 9.567, 0, 0, 10.633, 0.5, 2, 11.933, 0.5, 2, 16.133, 0.5, 2, 18.433, 0.5, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param201", "Segments": [0, 0, 2, 0.333, 0, 2, 0.467, 0, 0, 0.5, 1, 2, 1.2, 1, 2, 4.467, 1, 2, 5.667, 1, 0, 5.7, 0, 2, 7.433, 0, 2, 8.667, 0, 2, 10.633, 0, 2, 11.933, 0, 2, 16.1, 0, 2, 16.7, 0, 2, 16.733, 0, 2, 18.433, 0, 2, 18.6, 0, 2, 18.633, 0, 2, 19.733, 0]}, {"Target": "Parameter", "Id": "Param202", "Segments": [0, 0, 2, 0.333, 0, 2, 0.467, 0, 0, 0.5, 1, 2, 1.2, 1, 2, 4.467, 1, 2, 5.667, 1, 2, 7.433, 1, 2, 8.667, 1, 2, 10.633, 1, 2, 11.933, 1, 2, 16.1, 1, 2, 16.7, 1, 2, 16.733, 1, 2, 18.433, 1, 2, 18.6, 1, 0, 18.633, 0, 2, 19.733, 0]}, {"Target": "Parameter", "Id": "Param204", "Segments": [0, 0, 2, 0.333, 0, 2, 0.467, 0, 2, 0.5, 0, 2, 1.2, 0, 2, 4.467, 0, 2, 5.667, 0, 0, 5.7, 1, 2, 7.433, 1, 2, 8.667, 1, 2, 10.633, 1, 2, 11.933, 1, 2, 16.1, 1, 2, 16.7, 1, 2, 16.733, 1, 2, 18.433, 1, 2, 18.6, 1, 0, 18.633, 0, 2, 19.733, 0]}, {"Target": "Parameter", "Id": "Param209", "Segments": [0, 0, 2, 0.333, 0, 2, 0.467, 0, 0, 0.5, 1, 2, 1.2, 1, 2, 4.467, 1, 2, 5.667, 1, 0, 5.7, 0, 2, 7.433, 0, 2, 8.667, 0, 2, 10.633, 0, 2, 11.933, 0, 2, 16.1, 0, 2, 16.7, 0, 2, 16.733, 0, 2, 18.433, 0, 2, 18.6, 0, 2, 18.633, 0, 2, 19.733, 0]}, {"Target": "Parameter", "Id": "Param214", "Segments": [0, 0, 2, 0.333, 0, 2, 0.467, 0, 0, 0.5, 1, 2, 1.2, 1, 2, 4.467, 1, 2, 5.667, 1, 0, 5.7, 0, 2, 7.433, 0, 2, 8.667, 0, 2, 10.633, 0, 2, 11.933, 0, 2, 16.1, 0, 2, 16.7, 0, 2, 16.733, 0, 2, 18.433, 0, 2, 18.6, 0, 2, 18.633, 0, 2, 19.733, 0]}, {"Target": "Parameter", "Id": "Param9", "Segments": [0, 0, 2, 0.333, 0, 0, 1.167, -1.44, 2, 1.2, -1.44, 2, 1.633, -1.44, 0, 2.167, -0.36, 0, 2.8, -1.08, 0, 3.233, -0.36, 1, 3.344, -0.36, 3.456, -1, 3.567, -1.96, 1, 3.634, -2.535, 3.7, -2.7, 3.767, -2.7, 2, 4.467, -2.7, 0, 4.933, -3.84, 2, 5.233, -3.84, 0, 5.567, -6.772, 1, 5.711, -6.772, 5.856, -2.747, 6, -2.16, 1, 6.067, -1.889, 6.133, -2.019, 6.2, -1.86, 1, 6.322, -1.568, 6.445, -1.214, 6.567, -1.214, 0, 7.1, -2.855, 2, 7.433, -2.855, 0, 7.8, -3.226, 0, 8.133, -0.54, 2, 8.667, -0.54, 2, 10.633, -0.54, 0, 11.933, -1.32, 2, 16.1, -1.32, 0, 17.367, -1.92, 2, 18.433, -1.92, 0, 19.333, 0.707, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param10", "Segments": [0, 0, 2, 0.333, 0, 0, 0.467, 5.22, 0, 1.2, 0, 2, 1.633, 0, 2, 4.467, 0, 0, 5.233, 14.884, 1, 5.389, 14.884, 5.544, 11.165, 5.7, 6.865, 1, 5.8, 4.101, 5.9, 0.493, 6, 0.42, 1, 6.067, 0.371, 6.133, 0.405, 6.2, 0.36, 1, 6.322, 0.277, 6.445, -0.041, 6.567, -0.041, 0, 7.1, 0.461, 2, 7.433, 0.461, 0, 7.8, 1.906, 0, 8.133, 0.54, 2, 8.667, 0.54, 2, 10.633, 0.54, 0, 11.933, 0.96, 2, 16.1, 0.96, 0, 17.367, 0.6, 2, 18.433, 0.6, 0, 18.7, 0.96, 0, 19.333, -0.765, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param11", "Segments": [0, 0, 2, 0.333, 0, 2, 1.2, 0, 2, 1.633, 0, 2, 4.467, 0, 2, 5.233, 0, 2, 7.433, 0, 2, 8.667, 0, 2, 10.633, 0, 2, 11.933, 0, 2, 16.1, 0, 2, 18.433, 0, 1, 18.5, 0, 18.566, 13.466, 18.633, 18.72, 1, 18.7, 23.974, 18.766, 24, 18.833, 24, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param12", "Segments": [0, 0, 2, 0.333, 0, 2, 1.2, 0, 2, 1.633, 0, 2, 4.467, 0, 2, 5.233, 0, 2, 7.433, 0, 2, 8.667, 0, 2, 10.633, 0, 2, 11.933, 0, 2, 16.1, 0, 2, 18.433, 0, 0, 18.833, -30, 0, 19.433, 9.515, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param199", "Segments": [0, 0, 2, 0.333, 0, 2, 1.2, 0, 2, 1.633, 0, 2, 4.467, 0, 2, 5.233, 0, 1, 5.389, 0, 5.544, 9.032, 5.7, 19, 1, 5.744, 21.848, 5.789, 21.577, 5.833, 21.577, 1, 5.911, 21.577, 5.989, 10.276, 6.067, 7.991, 1, 6.167, 5.053, 6.267, 4.999, 6.367, 4.999, 1, 6.434, 4.999, 6.5, 6.28, 6.567, 7.467, 1, 6.667, 9.248, 6.767, 10.354, 6.867, 12, 1, 6.945, 13.28, 7.022, 14.175, 7.1, 14.175, 1, 7.211, 14.175, 7.322, 14.355, 7.433, 13.38, 1, 7.555, 12.308, 7.678, 3.602, 7.8, 3.602, 0, 8.133, 13.526, 0, 8.667, 6, 2, 10.633, 6, 0, 11.933, 7, 0, 16.1, 0, 0, 17.367, 15.36, 2, 18.433, 15.36, 1, 18.566, 15.36, 18.7, 7.449, 18.833, 4.999, 1, 18.9, 3.774, 18.966, 3.872, 19.033, 3.261, 1, 19.266, 1.121, 19.5, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param208", "Segments": [0, 0, 2, 0.333, 0, 2, 1.2, 0, 2, 1.633, 0, 2, 4.467, 0, 2, 5.233, 0, 0, 6.467, 4.125, 0, 7.033, -14.962, 0, 7.467, 0, 2, 8.667, 0, 2, 10.633, 0, 2, 11.933, 0, 2, 16.1, 0, 2, 18.433, 0, 2, 19.733, 0]}, {"Target": "Parameter", "Id": "Param22", "Segments": [0, 0, 2, 0.333, 0, 0, 0.367, 1, 2, 0.467, 1, 2, 1.2, 1, 2, 1.633, 1, 2, 4.467, 1, 2, 5.233, 1, 2, 7.433, 1, 2, 8.667, 1, 2, 10.633, 1, 2, 11.933, 1, 2, 16.1, 1, 2, 18.433, 1, 2, 19.2, 1, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param13", "Segments": [0, 0, 2, 0.333, 0, 0, 0.467, -2.16, 0, 1.2, 0, 2, 1.633, 0, 2, 4.467, 0, 2, 5.233, 0, 2, 7.433, 0, 2, 8.667, 0, 2, 10.633, 0, 2, 11.933, 0, 0, 12.533, -1.724, 0, 13.033, 2.88, 0, 13.833, -0.426, 0, 14.367, 1.614, 0, 14.667, -1.214, 0, 15.133, 2.208, 0, 16.1, 0, 2, 18.433, 0, 2, 19.733, 0]}, {"Target": "Parameter", "Id": "Param14", "Segments": [0, 0, 2, 0.333, 0, 0, 0.467, -14.28, 0, 1.2, 0, 2, 1.633, 0, 2, 4.467, 0, 2, 5.233, 0, 0, 5.7, -14.28, 1, 5.767, -14.28, 5.833, -6.369, 5.9, -1.899, 1, 5.933, 0.336, 5.967, 0, 6, 0, 2, 7.433, 0, 2, 8.667, 0, 2, 10.633, 0, 2, 11.933, 0, 2, 16.1, 0, 2, 18.433, 0, 2, 19.733, 0]}, {"Target": "Parameter", "Id": "Param210", "Segments": [0, 0, 2, 0.333, 0, 0, 0.467, 20.711, 1, 0.478, 20.711, 0.489, 17.795, 0.5, 16.851, 1, 0.633, 5.524, 0.767, 0.629, 0.9, 0.629, 0, 1.2, 1.36, 2, 1.633, 1.36, 1, 1.811, 1.36, 1.989, 1.773, 2.167, 2.08, 1, 2.378, 2.445, 2.589, 2.5, 2.8, 2.5, 0, 3.233, 2.08, 0, 3.767, 4.36, 1, 4, 4.36, 4.234, 4.141, 4.467, 3.639, 1, 4.622, 3.303, 4.778, 3.099, 4.933, 3.099, 2, 5.233, 3.099, 0, 5.333, 2.769, 0, 6, 30, 2, 7.433, 30, 0, 7.933, 4.8, 2, 8.667, 4.8, 2, 10.633, 4.8, 2, 11.933, 4.8, 2, 16.1, 4.8, 2, 18.433, 4.8, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param211", "Segments": [0, 0, 2, 0.333, 0, 0, 0.5, 15.106, 0, 0.9, -7.068, 0, 1.2, -1.38, 2, 1.633, -1.38, 1, 1.811, -1.38, 1.989, -2.286, 2.167, -3.78, 1, 2.378, -5.554, 2.589, -6.3, 2.8, -6.3, 0, 3.233, -3.78, 0, 3.767, -12.24, 0, 4.467, -9.563, 2, 5.233, -9.563, 0, 5.4, -15.743, 0, 7.433, -9.563, 2, 8.667, -9.563, 2, 10.633, -9.563, 2, 11.933, -9.563, 2, 16.1, -9.563, 2, 18.433, -9.563, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param228", "Segments": [0, 0, 2, 0.333, 0, 1, 0.389, 0, 0.444, -8.316, 0.5, -8.745, 1, 0.733, -10.548, 0.967, -11, 1.2, -11, 2, 1.633, -11, 2, 2.167, -11, 0, 2.8, -13.1, 0, 3.233, -11, 0, 3.767, -20.3, 0, 4.467, -17.357, 2, 5.233, -17.357, 2, 7.433, -17.357, 2, 8.667, -17.357, 2, 10.633, -17.357, 2, 11.933, -17.357, 2, 16.1, -17.357, 2, 18.433, -17.357, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param221", "Segments": [0, 0, 2, 0.333, 0, 2, 0.667, 0, 0, 0.7, 1, 2, 1.2, 1, 2, 4.467, 1, 2, 5.233, 1, 2, 7.6, 1, 2, 8.667, 1, 2, 10.633, 1, 2, 11.933, 1, 2, 15.833, 1, 2, 18.433, 1, 2, 18.8, 1, 0, 18.833, 0, 2, 19.733, 0]}, {"Target": "Parameter", "Id": "Param222", "Segments": [0, 0, 2, 0.333, 0, 2, 0.667, 0, 2, 0.7, 0, 2, 1.2, 0, 2, 4.467, 0, 2, 5.233, 0, 2, 7.6, 0, 0, 7.633, 1, 2, 8.667, 1, 2, 10.633, 1, 2, 11.933, 1, 2, 16.067, 1, 0, 16.1, 0, 2, 18.433, 0, 2, 18.8, 0, 2, 18.833, 0, 2, 19.733, 0]}, {"Target": "Parameter", "Id": "Param223", "Segments": [0, 0, 2, 0.333, 0, 2, 0.667, 0, 0, 0.7, 1, 2, 1.2, 1, 2, 4.467, 1, 2, 5.233, 1, 2, 7.6, 1, 0, 7.633, 0, 2, 8.667, 0, 2, 10.633, 0, 2, 11.933, 0, 2, 16.067, 0, 0, 16.1, 1, 2, 18.433, 1, 2, 18.8, 1, 0, 18.833, 0, 2, 19.733, 0]}, {"Target": "Parameter", "Id": "Param238", "Segments": [0, 0, 2, 0.333, 0, 2, 0.667, 0, 2, 0.7, 0, 2, 1.2, 0, 2, 4.467, 0, 2, 5.233, 0, 0, 5.267, 1, 2, 7.6, 1, 2, 8.667, 1, 2, 10.633, 1, 2, 11.933, 1, 2, 16.067, 1, 2, 16.1, 1, 2, 18.433, 1, 2, 18.8, 1, 0, 18.833, 0, 2, 19.733, 0]}, {"Target": "Parameter", "Id": "Param241", "Segments": [0, 0, 2, 0.333, 0, 2, 0.667, 0, 0, 0.7, 1, 2, 1.2, 1, 2, 4.467, 1, 2, 5.233, 1, 0, 5.267, 0, 2, 7.6, 0, 2, 8.667, 0, 2, 10.633, 0, 2, 11.933, 0, 2, 16.067, 0, 2, 16.1, 0, 2, 18.433, 0, 2, 18.8, 0, 2, 18.833, 0, 2, 19.733, 0]}, {"Target": "Parameter", "Id": "Param15", "Segments": [0, 0, 2, 0.333, 0, 0, 0.8, 6.761, 0, 1.2, 5.46, 2, 1.633, 5.46, 0, 2.167, 4.98, 0, 2.8, 6.06, 0, 3.233, 4.98, 0, 3.767, 7.08, 0, 4.467, 6.415, 0, 4.933, 7.256, 2, 5.233, 7.256, 1, 5.966, 7.256, 6.7, 7.071, 7.433, 6.415, 1, 7.6, 6.266, 7.766, 5.516, 7.933, 5.516, 2, 8.667, 5.516, 0, 10.633, 6.176, 0, 11.933, 6.056, 1, 12.111, 6.056, 12.289, 6.041, 12.467, 6.1, 1, 12.667, 6.166, 12.867, 7.056, 13.067, 7.056, 0, 15.833, 6.761, 0, 16.1, 9.86, 1, 16.167, 9.86, 16.233, 8.573, 16.3, 7.976, 1, 16.567, 5.586, 16.833, 4.796, 17.1, 4.796, 2, 18.433, 4.796, 0, 18.8, 8.798, 0, 19.233, -0.64, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param16", "Segments": [0, 0, 2, 0.333, 0, 1, 0.478, 0, 0.622, 5.802, 0.767, 7.83, 1, 0.911, 9.858, 1.056, 9.78, 1.2, 9.78, 2, 1.633, 9.78, 2, 4.467, 9.78, 2, 5.233, 9.78, 2, 7.433, 9.78, 2, 8.667, 9.78, 1, 9.322, 9.78, 9.978, 9.246, 10.633, 8.94, 1, 11.066, 8.738, 11.5, 8.76, 11.933, 8.76, 2, 15.833, 8.76, 2, 18.433, 8.76, 1, 18.622, 8.76, 18.811, 1.725, 19, -0.184, 1, 19.122, -1.419, 19.245, -1.121, 19.367, -1.121, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param17", "Segments": [0, 0, 2, 0.333, 0, 0, 0.767, 18.54, 0, 1.2, 0, 2, 1.633, 0, 2, 4.467, 0, 2, 5.233, 0, 2, 7.433, 0, 2, 8.667, 0, 2, 10.633, 0, 2, 11.933, 0, 2, 15.833, 0, 2, 18.433, 0, 0, 18.933, -10, 0, 19.433, 4.5, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param18", "Segments": [0, 0, 2, 0.333, 0, 0, 0.767, 30, 0, 1.2, 0, 2, 1.633, 0, 2, 4.467, 0, 2, 5.233, 0, 2, 7.433, 0, 2, 8.667, 0, 2, 10.633, 0, 2, 11.933, 0, 2, 15.833, 0, 2, 18.433, 0, 0, 18.933, 14, 1, 19.111, 14, 19.289, 3.888, 19.467, 1, 1, 19.556, -0.444, 19.644, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param19", "Segments": [0, 0, 2, 0.333, 0, 0, 1.2, -31, 2, 1.633, -31, 2, 4.467, -31, 2, 5.233, -31, 2, 7.433, -31, 0, 7.933, 0, 2, 8.667, 0, 2, 10.633, 0, 2, 11.933, 0, 0, 13.033, -7.65, 0, 13.833, -0.194, 0, 14.367, -3.592, 0, 14.667, 2.558, 0, 15.133, -0.558, 0, 15.833, 0, 0, 16.3, -41.76, 2, 18.433, -41.76, 0, 19.4, 0, 2, 19.733, 0]}, {"Target": "Parameter", "Id": "Param20", "Segments": [0, 0, 2, 0.333, 0, 1, 0.444, 0, 0.556, -17.188, 0.667, -19, 1, 0.845, -21.9, 1.022, -22, 1.2, -22, 2, 1.633, -22, 2, 4.467, -22, 2, 5.233, -22, 2, 7.433, -22, 2, 8.667, -22, 0, 10.633, -18.52, 0, 11.933, -22, 2, 15.833, -22, 2, 18.433, -22, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param224", "Segments": [0, 0, 2, 0.333, 0, 0, 1.2, 16.14, 2, 4.467, 16.14, 2, 5.233, 16.14, 2, 7.433, 16.14, 0, 7.467, -15.66, 1, 7.522, -15.66, 7.578, -14.928, 7.633, -12.728, 1, 7.644, -12.288, 7.656, -8.319, 7.667, -7.45, 1, 7.756, -0.504, 7.844, 2.4, 7.933, 2.4, 2, 8.667, 2.4, 0, 10.633, 1.74, 2, 11.933, 1.74, 2, 15.833, 1.74, 0, 16.3, -23, 0, 17.1, -22.76, 2, 18.433, -22.76, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param226", "Segments": [0, 0, 2, 0.333, 0, 2, 1.2, 0, 2, 4.467, 0, 2, 5.233, 0, 2, 7.433, 0, 0, 7.633, -22, 0, 7.933, 8.38, 2, 8.667, 8.38, 0, 10.633, 10.36, 2, 11.933, 10.36, 2, 15.833, 10.36, 2, 18.433, 10.36, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param233", "Segments": [0, 0, 2, 0.333, 0, 0, 0.633, 7.666, 1, 0.655, 7.666, 0.678, 6.605, 0.7, 5.465, 1, 0.733, 3.754, 0.767, 1.534, 0.8, 0.963, 1, 0.933, -1.321, 1.067, -2.04, 1.2, -2.04, 2, 1.633, -2.04, 2, 2.167, -2.04, 0, 2.8, -2.52, 0, 3.233, -2.04, 0, 3.767, -5.52, 0, 4.467, -4.976, 0, 4.933, -5.336, 2, 5.233, -5.336, 0, 6, -2.036, 2, 7.433, -2.036, 0, 7.933, -22.436, 2, 8.667, -22.436, 2, 10.633, -22.436, 2, 11.933, -22.436, 2, 15.833, -22.436, 0, 16.3, -1.92, 0, 17.1, -2.52, 2, 18.433, -2.52, 1, 18.578, -2.52, 18.722, 1.288, 18.867, 8.814, 1, 18.9, 10.551, 18.934, 11.376, 18.967, 11.376, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param234", "Segments": [0, 0, 2, 0.333, 0, 0, 0.8, 14.475, 0, 1.2, 0, 2, 1.633, 0, 1, 1.811, 0, 1.989, 3.746, 2.167, 7.65, 1, 2.378, 12.286, 2.589, 13.5, 2.8, 13.5, 0, 3.233, 7.65, 0, 3.767, 31.875, 0, 4.467, 28.09, 2, 5.233, 28.09, 0, 6, 5.665, 2, 7.433, 5.665, 2, 8.667, 5.665, 2, 10.633, 5.665, 2, 11.933, 5.665, 2, 15.833, 5.665, 0, 16.067, -19.22, 0, 16.4, 13.5, 1, 16.567, 13.5, 16.733, 7.93, 16.9, 7.65, 1, 17.411, 6.79, 17.922, 6.507, 18.433, 5.665, 1, 18.578, 5.427, 18.722, -30, 18.867, -30, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param235", "Segments": [0, 0, 2, 0.333, 0, 0, 0.733, -30, 0, 1.167, -21, 2, 1.2, -21, 2, 1.633, -21, 0, 2.167, -18.12, 0, 2.8, -20.04, 0, 3.233, -18.12, 0, 3.767, -25.5, 0, 4.467, -24.347, 2, 5.233, -24.347, 2, 7.433, -24.347, 0, 7.933, -8.68, 2, 8.667, -8.68, 2, 10.633, -8.68, 2, 11.933, -8.68, 2, 15.833, -8.68, 2, 18.433, -8.68, 0, 18.867, -28, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param274", "Segments": [0, 0, 2, 0.333, 0, 2, 0.667, 0, 0, 0.7, -0.6, 2, 1.167, -0.6, 2, 1.2, -0.6, 2, 1.633, -0.6, 2, 3.767, -0.6, 2, 4.467, -0.6, 2, 5.233, -0.6, 2, 7.433, -0.6, 0, 7.933, 0, 2, 8.667, 0, 2, 10.633, 0, 2, 11.933, 0, 2, 15.833, 0, 2, 18.433, 0, 2, 19.733, 0]}, {"Target": "Parameter", "Id": "Param277", "Segments": [0, 0, 2, 0.333, 0, 0, 0.6, 12.46, 0, 1.133, -9.549, 0, 1.567, 12.46, 0, 1.967, -9.549, 0, 2.4, 12.46, 0, 2.8, -9.549, 0, 3.233, 12.46, 0, 3.633, -9.549, 0, 4.067, 12.46, 0, 4.467, -9.549, 0, 4.9, 12.46, 0, 5.3, -9.549, 0, 5.733, 12.46, 0, 6.133, -9.549, 0, 6.567, 12.46, 0, 6.967, -9.549, 0, 7.4, 12.46, 0, 7.8, -9.549, 0, 8.233, 12.46, 0, 8.633, -9.549, 0, 9.333, 12.46, 0, 9.867, -9.549, 0, 10.433, 12.46, 0, 10.967, -9.549, 0, 11.533, 12.46, 0, 12.233, -9.549, 0, 13, 12.46, 0, 13.667, -9.549, 0, 14.433, 12.46, 0, 15.133, -9.549, 0, 15.867, 12.46, 0, 16.6, -9.549, 0, 17.333, 12.46, 0, 18.033, -9.549, 0, 18.8, 12.46, 0, 19.5, -7.022, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param278", "Segments": [0, 0, 2, 0.333, 0, 0, 0.6, 0.238, 0, 1.133, -0.138, 0, 1.567, 0.238, 0, 1.967, -0.138, 0, 2.4, 0.238, 0, 2.8, -0.138, 0, 3.233, 0.238, 0, 3.633, -0.138, 0, 4.067, 0.238, 0, 4.467, -0.138, 0, 4.9, 0.238, 0, 5.3, -0.138, 0, 5.733, 0.238, 0, 6.133, -0.138, 0, 6.567, 0.238, 0, 6.967, -0.138, 0, 7.167, 0, 0, 7.5, -0.106, 0, 7.767, 0.415, 0, 8.167, -0.605, 0, 8.6, 0.627, 0, 9.167, -0.512, 0, 9.6, 0.502, 0, 10.067, -0.39, 0, 10.467, 0.415, 0, 10.967, -0.318, 0, 11.533, 0.415, 0, 12.233, -0.318, 0, 13, 0.415, 0, 13.667, -0.318, 0, 14.433, 0.415, 0, 15.133, -0.318, 0, 15.867, 0.415, 0, 16.6, -0.318, 0, 17.333, 0.415, 0, 18.033, -0.318, 0, 18.8, 0.415, 0, 19.5, -0.234, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param280", "Segments": [0, 0, 2, 0.333, 0, 0, 0.567, 0.376, 0, 0.833, -0.333, 0, 1.467, 0.467, 0, 2.3, -0.393, 0, 2.8, 0.376, 0, 3.567, -0.647, 0, 4.267, 0.533, 0, 4.9, -0.533, 0, 5.567, 0.367, 0, 6.267, -0.5, 0, 6.933, 0.701, 1, 7.222, 0.701, 7.511, 0.557, 7.8, 0, 1, 7.922, -0.236, 8.045, -0.647, 8.167, -0.647, 0, 8.933, 0.6, 0, 9.8, -0.5, 0, 10.433, 0.502, 0, 11.367, -0.647, 0, 12.533, 0.701, 0, 13.567, -0.99, 0, 14.8, 0.757, 0, 15.967, -0.647, 0, 17.033, 0.656, 1, 17.333, 0.656, 17.633, 0.078, 17.933, -0.281, 1, 18.133, -0.52, 18.333, -0.5, 18.533, -0.5, 0, 19.367, 0.4, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param7", "Segments": [0, 0, 2, 0.333, 0, 0, 1.333, -9, 0, 2.767, 0, 0, 4.2, -9, 0, 5.633, 0, 0, 7.067, -9, 0, 8.4, 0, 0, 9.833, -9, 0, 11.267, 0, 0, 12.7, -9, 0, 14.067, 0, 0, 15.467, -9, 0, 16.867, 0, 0, 18.3, -9, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param294", "Segments": [0, 1, 2, 0.333, 1, 0, 0.4, 0, 2, 19.633, 0, 0, 19.733, 1]}, {"Target": "Parameter", "Id": "Param47", "Segments": [0, 0, 2, 0.333, 0, 0, 0.5, 19.718, 0, 0.767, -23.451, 0, 1.267, 12.353, 0, 2.2, -17.819, 0, 2.667, 16.955, 0, 3.467, -13.057, 0, 4.167, 15.395, 0, 4.733, -14.089, 0, 5.4, 10.516, 0, 6.167, -9.5, 0, 6.9, 23.012, 0, 7.233, -26.915, 0, 7.6, 26.465, 0, 7.933, -30, 2, 7.967, -30, 0, 8.3, 24.877, 0, 8.967, -18.791, 0, 9.3, 23.495, 0, 9.9, -13.132, 0, 10.467, 15.294, 0, 11.067, -23.49, 0, 11.467, 20.757, 0, 12.333, -12.541, 0, 13.133, 14.278, 0, 13.7, -18.012, 0, 14.133, 20.327, 0, 14.867, -17.496, 0, 15.3, 15.056, 0, 16.1, -13.301, 0, 16.733, 11.613, 0, 17.433, -9.283, 0, 18.267, 8.352, 0, 19.033, -3.832, 0, 19.367, -0.397, 0, 19.533, -0.719, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param48", "Segments": [0, 0, 2, 0.333, 0, 0, 0.467, -10.862, 0, 0.633, 21.217, 0, 0.9, -18.669, 0, 1.3, 5.983, 0, 1.667, -2.665, 0, 2.1, 4.651, 0, 2.4, -10.729, 0, 2.767, 9.73, 0, 3.067, -3.802, 0, 3.333, 4.654, 0, 3.633, -5.874, 0, 3.9, 0.143, 0, 4.067, -1.988, 0, 4.333, 5.864, 0, 4.9, -4.759, 0, 5.167, 0.134, 0, 5.3, -1.151, 0, 5.567, 3.75, 0, 5.833, -0.583, 0, 6.033, 1.424, 0, 6.333, -2.996, 0, 6.633, -0.308, 0, 6.833, -6.246, 0, 7.1, 20.006, 0, 7.433, -27.278, 0, 7.767, 28.351, 0, 8.133, -30, 0, 8.433, 20.707, 0, 8.767, -7.094, 0, 8.967, 5.022, 0, 9.167, -16.504, 0, 9.467, 19.267, 0, 9.767, -6.65, 0, 9.967, -0.765, 0, 10.233, -3.547, 0, 10.6, 6.553, 0, 10.9, 0.865, 0, 11, 2.336, 0, 11.3, -14.789, 0, 11.6, 15.409, 0, 11.867, -6.466, 0, 12.167, 4.899, 0, 12.467, -4.778, 0, 12.733, 1.365, 0, 12.967, -4.576, 0, 13.3, 6.769, 0, 13.567, 0.519, 0, 13.633, 0.91, 0, 13.867, -10.491, 0, 14.267, 11.313, 0, 14.567, -4.117, 0, 14.8, 4.24, 0, 15.067, -9.09, 0, 15.433, 9.78, 0, 15.733, -3.793, 0, 16, 3.927, 0, 16.3, -5.551, 0, 16.567, -0.049, 0, 16.6, -0.056, 0, 16.9, 3.305, 0, 17.167, 0.121, 0, 17.3, 1.256, 0, 17.567, -2.95, 0, 17.867, 0.681, 0, 18.133, -2.071, 0, 18.433, 2.878, 0, 18.7, -1.359, 0, 18.967, 2.511, 0, 19.2, -2.975, 0, 19.5, 1.673, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param56", "Segments": [0, 0, 2, 0.333, 0, 0, 0.5, 8.365, 0, 0.833, -11.326, 0, 1.4, 10.436, 0, 2.2, -10.757, 0, 2.767, 12.125, 0, 3.467, -10.397, 0, 4.2, 11.101, 0, 4.8, -11.569, 0, 5.433, 9.697, 0, 6.133, -7.713, 0, 6.933, 13.482, 0, 7.333, -14.43, 0, 7.7, 10.052, 0, 8.033, -11.589, 0, 8.5, 10.029, 0, 9.033, -14.052, 0, 9.433, 11.272, 0, 10, -12.098, 0, 10.567, 12.79, 0, 11.133, -17.395, 0, 11.633, 13.122, 0, 12.3, -11.042, 0, 13.167, 9.283, 0, 13.733, -12.532, 0, 14.233, 13.819, 0, 14.9, -14.427, 0, 15.4, 12.086, 0, 16.1, -11.056, 0, 16.767, 9.808, 0, 17.433, -8.003, 0, 18.267, 5.627, 0, 19.067, -2.947, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param57", "Segments": [0, 0, 2, 0.333, 0, 0, 0.467, -5.745, 0, 0.667, 11.083, 0, 1, -10.13, 0, 1.6, 5.25, 0, 2.033, 1.25, 0, 2.1, 1.303, 0, 2.467, -9.175, 0, 2.9, 6.463, 0, 3.7, -6.19, 0, 4.433, 7.338, 0, 5, -6.647, 0, 5.633, 5.605, 0, 6.4, -4.385, 0, 6.7, -1.812, 0, 6.8, -3.157, 0, 7.133, 15.525, 0, 7.5, -17.98, 0, 7.867, 17.071, 0, 8.233, -17.52, 0, 8.567, 7.452, 0, 9.2, -15.936, 0, 9.6, 9.379, 0, 10.233, -9.015, 0, 10.767, 8.846, 0, 11.333, -16.274, 0, 11.7, 6.437, 0, 12.533, -5.859, 0, 12.833, -2.407, 0, 12.967, -2.982, 0, 13.4, 6.662, 0, 13.967, -10.875, 0, 14.433, 8.59, 0, 15.133, -10.478, 0, 15.567, 6.936, 0, 16.333, -6.815, 0, 16.967, 5.426, 0, 17.633, -4.352, 0, 18.5, 3.1, 0, 18.767, 1.522, 0, 18.933, 1.993, 0, 19.267, -1.792, 1, 19.378, -1.792, 19.489, -0.2, 19.6, -0.032, 1, 19.644, 0.035, 19.689, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param58", "Segments": [0, 0, 2, 0.333, 0, 0, 0.567, -6.102, 0, 0.767, 12.575, 0, 1.1, -13.84, 0, 1.5, 6.799, 0, 1.933, -0.15, 0, 2.2, 1.694, 0, 2.567, -8.999, 0, 2.933, 8.728, 0, 3.267, -0.852, 0, 3.467, 1.26, 0, 3.767, -5.776, 0, 4.5, 6.033, 0, 5.033, -6.044, 0, 5.7, 4.965, 0, 6.067, -0.864, 0, 6.167, -0.811, 0, 6.467, -3.116, 0, 6.733, -0.475, 0, 6.933, -3.195, 0, 7.2, 15.102, 0, 7.6, -20.998, 0, 7.967, 21.229, 0, 8.333, -21.281, 0, 8.633, 13.245, 0, 9.3, -16.505, 0, 9.633, 14.167, 0, 10.3, -7.678, 0, 10.8, 8.758, 0, 11.4, -14.914, 0, 11.733, 11.737, 0, 12.1, -0.411, 0, 12.267, 1.084, 0, 12.6, -5.135, 0, 12.9, 1.299, 0, 13.1, -2.373, 0, 13.467, 6.639, 0, 14.033, -11.104, 0, 14.433, 10.542, 0, 14.767, -0.732, 0, 14.867, -0.192, 0, 15.2, -9.85, 0, 15.6, 9.561, 0, 15.933, -1.109, 0, 16.1, 0.143, 0, 16.4, -5.799, 0, 17.033, 4.176, 0, 17.7, -3.577, 0, 18.067, 0.73, 0, 18.267, -0.602, 0, 18.567, 2.375, 0, 18.867, -0.663, 0, 19.067, 1.195, 0, 19.367, -2.391, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param59", "Segments": [0, 0, 2, 0.333, 0, 0, 0.5, 8.365, 0, 0.833, -11.326, 0, 1.4, 10.436, 0, 2.2, -10.757, 0, 2.767, 12.125, 0, 3.467, -10.397, 0, 4.2, 11.101, 0, 4.8, -11.569, 0, 5.433, 9.697, 0, 6.133, -7.713, 0, 6.933, 13.482, 0, 7.333, -14.43, 0, 7.7, 10.052, 0, 8.033, -11.589, 0, 8.5, 10.029, 0, 9.033, -14.052, 0, 9.433, 11.272, 0, 10, -12.098, 0, 10.567, 12.79, 0, 11.133, -17.395, 0, 11.633, 13.122, 0, 12.3, -11.042, 0, 13.167, 9.283, 0, 13.733, -12.532, 0, 14.233, 13.819, 0, 14.9, -14.427, 0, 15.4, 12.086, 0, 16.1, -11.056, 0, 16.767, 9.808, 0, 17.433, -8.003, 0, 18.267, 5.627, 0, 19.067, -2.947, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param60", "Segments": [0, 0, 2, 0.333, 0, 0, 0.467, -5.745, 0, 0.667, 11.083, 0, 1, -10.13, 0, 1.6, 5.25, 0, 2.033, 1.25, 0, 2.1, 1.303, 0, 2.467, -9.175, 0, 2.9, 6.463, 0, 3.7, -6.19, 0, 4.433, 7.338, 0, 5, -6.647, 0, 5.633, 5.605, 0, 6.4, -4.385, 0, 6.7, -1.812, 0, 6.8, -3.157, 0, 7.133, 15.525, 0, 7.5, -17.98, 0, 7.867, 17.071, 0, 8.233, -17.52, 0, 8.567, 7.452, 0, 9.2, -15.936, 0, 9.6, 9.379, 0, 10.233, -9.015, 0, 10.767, 8.846, 0, 11.333, -16.274, 0, 11.7, 6.437, 0, 12.533, -5.859, 0, 12.833, -2.407, 0, 12.967, -2.982, 0, 13.4, 6.662, 0, 13.967, -10.875, 0, 14.433, 8.59, 0, 15.133, -10.478, 0, 15.567, 6.936, 0, 16.333, -6.815, 0, 16.967, 5.426, 0, 17.633, -4.352, 0, 18.5, 3.1, 0, 18.767, 1.522, 0, 18.933, 1.993, 0, 19.267, -1.792, 1, 19.378, -1.792, 19.489, -0.2, 19.6, -0.032, 1, 19.644, 0.035, 19.689, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param61", "Segments": [0, 0, 2, 0.333, 0, 0, 0.567, -6.102, 0, 0.767, 12.575, 0, 1.1, -13.84, 0, 1.5, 6.799, 0, 1.933, -0.15, 0, 2.2, 1.694, 0, 2.567, -8.999, 0, 2.933, 8.728, 0, 3.267, -0.852, 0, 3.467, 1.26, 0, 3.767, -5.776, 0, 4.5, 6.033, 0, 5.033, -6.044, 0, 5.7, 4.965, 0, 6.067, -0.864, 0, 6.167, -0.811, 0, 6.467, -3.116, 0, 6.733, -0.475, 0, 6.933, -3.195, 0, 7.2, 15.102, 0, 7.6, -20.998, 0, 7.967, 21.229, 0, 8.333, -21.281, 0, 8.633, 13.245, 0, 9.3, -16.505, 0, 9.633, 14.167, 0, 10.3, -7.678, 0, 10.8, 8.758, 0, 11.4, -14.914, 0, 11.733, 11.737, 0, 12.1, -0.411, 0, 12.267, 1.084, 0, 12.6, -5.135, 0, 12.9, 1.299, 0, 13.1, -2.373, 0, 13.467, 6.639, 0, 14.033, -11.104, 0, 14.433, 10.542, 0, 14.767, -0.732, 0, 14.867, -0.192, 0, 15.2, -9.85, 0, 15.6, 9.561, 0, 15.933, -1.109, 0, 16.1, 0.143, 0, 16.4, -5.799, 0, 17.033, 4.176, 0, 17.7, -3.577, 0, 18.067, 0.73, 0, 18.267, -0.602, 0, 18.567, 2.375, 0, 18.867, -0.663, 0, 19.067, 1.195, 0, 19.367, -2.391, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param49", "Segments": [0, 0, 2, 0.333, 0, 0, 0.5, 19.718, 0, 0.767, -23.451, 0, 1.267, 12.353, 0, 2.2, -17.819, 0, 2.667, 16.955, 0, 3.467, -13.057, 0, 4.167, 15.395, 0, 4.733, -14.089, 0, 5.4, 10.516, 0, 6.167, -9.5, 0, 6.9, 23.012, 0, 7.233, -26.915, 0, 7.6, 26.465, 0, 7.933, -30, 2, 7.967, -30, 0, 8.3, 24.877, 0, 8.967, -18.791, 0, 9.3, 23.495, 0, 9.9, -13.132, 0, 10.467, 15.294, 0, 11.067, -23.49, 0, 11.467, 20.757, 0, 12.333, -12.541, 0, 13.133, 14.278, 0, 13.7, -18.012, 0, 14.133, 20.327, 0, 14.867, -17.496, 0, 15.3, 15.056, 0, 16.1, -13.301, 0, 16.733, 11.613, 0, 17.433, -9.283, 0, 18.267, 8.352, 0, 19.033, -3.832, 0, 19.367, -0.397, 0, 19.533, -0.719, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param50", "Segments": [0, 0, 2, 0.333, 0, 0, 0.467, -10.862, 0, 0.633, 21.217, 0, 0.9, -18.669, 0, 1.3, 5.983, 0, 1.667, -2.665, 0, 2.1, 4.651, 0, 2.4, -10.729, 0, 2.767, 9.73, 0, 3.067, -3.802, 0, 3.333, 4.654, 0, 3.633, -5.874, 0, 3.9, 0.143, 0, 4.067, -1.988, 0, 4.333, 5.864, 0, 4.9, -4.759, 0, 5.167, 0.134, 0, 5.3, -1.151, 0, 5.567, 3.75, 0, 5.833, -0.583, 0, 6.033, 1.424, 0, 6.333, -2.996, 0, 6.633, -0.308, 0, 6.833, -6.246, 0, 7.1, 20.006, 0, 7.433, -27.278, 0, 7.767, 28.351, 0, 8.133, -30, 0, 8.433, 20.707, 0, 8.767, -7.094, 0, 8.967, 5.022, 0, 9.167, -16.504, 0, 9.467, 19.267, 0, 9.767, -6.65, 0, 9.967, -0.765, 0, 10.233, -3.547, 0, 10.6, 6.553, 0, 10.9, 0.865, 0, 11, 2.336, 0, 11.3, -14.789, 0, 11.6, 15.409, 0, 11.867, -6.466, 0, 12.167, 4.899, 0, 12.467, -4.778, 0, 12.733, 1.365, 0, 12.967, -4.576, 0, 13.3, 6.769, 0, 13.567, 0.519, 0, 13.633, 0.91, 0, 13.867, -10.491, 0, 14.267, 11.313, 0, 14.567, -4.117, 0, 14.8, 4.24, 0, 15.067, -9.09, 0, 15.433, 9.78, 0, 15.733, -3.793, 0, 16, 3.927, 0, 16.3, -5.551, 0, 16.567, -0.049, 0, 16.6, -0.056, 0, 16.9, 3.305, 0, 17.167, 0.121, 0, 17.3, 1.256, 0, 17.567, -2.95, 0, 17.867, 0.681, 0, 18.133, -2.071, 0, 18.433, 2.878, 0, 18.7, -1.359, 0, 18.967, 2.511, 0, 19.2, -2.975, 0, 19.5, 1.673, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param51", "Segments": [0, 0, 2, 0.333, 0, 0, 0.5, 19.718, 0, 0.767, -23.451, 0, 1.267, 12.353, 0, 2.2, -17.819, 0, 2.667, 16.955, 0, 3.467, -13.057, 0, 4.167, 15.395, 0, 4.733, -14.089, 0, 5.4, 10.516, 0, 6.167, -9.5, 0, 6.9, 23.012, 0, 7.233, -26.915, 0, 7.6, 26.465, 0, 7.933, -30, 2, 7.967, -30, 0, 8.3, 24.877, 0, 8.967, -18.791, 0, 9.3, 23.495, 0, 9.9, -13.132, 0, 10.467, 15.294, 0, 11.067, -23.49, 0, 11.467, 20.757, 0, 12.333, -12.541, 0, 13.133, 14.278, 0, 13.7, -18.012, 0, 14.133, 20.327, 0, 14.867, -17.496, 0, 15.3, 15.056, 0, 16.1, -13.301, 0, 16.733, 11.613, 0, 17.433, -9.283, 0, 18.267, 8.352, 0, 19.033, -3.832, 0, 19.367, -0.397, 0, 19.533, -0.719, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param52", "Segments": [0, 0, 2, 0.333, 0, 0, 0.467, -10.862, 0, 0.633, 21.217, 0, 0.9, -18.669, 0, 1.3, 5.983, 0, 1.667, -2.665, 0, 2.1, 4.651, 0, 2.4, -10.729, 0, 2.767, 9.73, 0, 3.067, -3.802, 0, 3.333, 4.654, 0, 3.633, -5.874, 0, 3.9, 0.143, 0, 4.067, -1.988, 0, 4.333, 5.864, 0, 4.9, -4.759, 0, 5.167, 0.134, 0, 5.3, -1.151, 0, 5.567, 3.75, 0, 5.833, -0.583, 0, 6.033, 1.424, 0, 6.333, -2.996, 0, 6.633, -0.308, 0, 6.833, -6.246, 0, 7.1, 20.006, 0, 7.433, -27.278, 0, 7.767, 28.351, 0, 8.133, -30, 0, 8.433, 20.707, 0, 8.767, -7.094, 0, 8.967, 5.022, 0, 9.167, -16.504, 0, 9.467, 19.267, 0, 9.767, -6.65, 0, 9.967, -0.765, 0, 10.233, -3.547, 0, 10.6, 6.553, 0, 10.9, 0.865, 0, 11, 2.336, 0, 11.3, -14.789, 0, 11.6, 15.409, 0, 11.867, -6.466, 0, 12.167, 4.899, 0, 12.467, -4.778, 0, 12.733, 1.365, 0, 12.967, -4.576, 0, 13.3, 6.769, 0, 13.567, 0.519, 0, 13.633, 0.91, 0, 13.867, -10.491, 0, 14.267, 11.313, 0, 14.567, -4.117, 0, 14.8, 4.24, 0, 15.067, -9.09, 0, 15.433, 9.78, 0, 15.733, -3.793, 0, 16, 3.927, 0, 16.3, -5.551, 0, 16.567, -0.049, 0, 16.6, -0.056, 0, 16.9, 3.305, 0, 17.167, 0.121, 0, 17.3, 1.256, 0, 17.567, -2.95, 0, 17.867, 0.681, 0, 18.133, -2.071, 0, 18.433, 2.878, 0, 18.7, -1.359, 0, 18.967, 2.511, 0, 19.2, -2.975, 0, 19.5, 1.673, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation64", "Segments": [0, 2.951, 0, 0.333, 0, 0, 0.5, 6.573, 0, 0.767, -7.817, 0, 1.267, 4.118, 0, 2.2, -5.94, 0, 2.667, 5.652, 0, 3.467, -4.352, 0, 4.167, 5.132, 0, 4.733, -4.697, 0, 5.4, 3.505, 0, 6.167, -3.167, 0, 6.9, 7.671, 0, 7.233, -8.972, 0, 7.6, 8.822, 0, 7.967, -10.398, 0, 8.3, 8.292, 0, 8.967, -6.264, 0, 9.3, 7.832, 0, 9.9, -4.377, 0, 10.467, 5.098, 0, 11.067, -7.83, 0, 11.467, 6.919, 0, 12.333, -4.18, 0, 13.133, 4.76, 0, 13.7, -6.004, 0, 14.133, 6.776, 0, 14.867, -5.832, 0, 15.3, 5.019, 0, 16.1, -4.434, 0, 16.733, 3.871, 0, 17.433, -3.095, 0, 18.267, 2.784, 0, 19.033, -1.277, 0, 19.367, -0.132, 0, 19.533, -0.24, 0, 19.733, 2.951]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation65", "Segments": [0, 4.849, 1, 0.111, 4.849, 0.222, 3.876, 0.333, 0, 1, 0.378, -1.55, 0.422, -5.431, 0.467, -5.431, 0, 0.633, 10.608, 0, 0.9, -9.334, 0, 1.3, 2.992, 0, 1.667, -1.333, 0, 2.1, 2.326, 0, 2.4, -5.365, 0, 2.767, 4.865, 0, 3.067, -1.901, 0, 3.333, 2.327, 0, 3.633, -2.937, 0, 3.9, 0.072, 0, 4.067, -0.994, 0, 4.333, 2.932, 0, 4.9, -2.38, 0, 5.167, 0.067, 0, 5.3, -0.576, 0, 5.567, 1.875, 0, 5.833, -0.291, 0, 6.033, 0.712, 0, 6.333, -1.498, 0, 6.633, -0.154, 0, 6.833, -3.123, 0, 7.1, 10.003, 0, 7.433, -13.639, 0, 7.767, 14.176, 0, 8.133, -15.474, 0, 8.433, 10.353, 0, 8.767, -3.547, 0, 8.967, 2.511, 0, 9.167, -8.252, 0, 9.467, 9.633, 0, 9.767, -3.325, 0, 9.967, -0.382, 0, 10.233, -1.774, 0, 10.6, 3.277, 0, 10.9, 0.433, 0, 11, 1.168, 0, 11.3, -7.395, 0, 11.6, 7.705, 0, 11.867, -3.233, 0, 12.167, 2.449, 0, 12.467, -2.389, 0, 12.733, 0.682, 0, 12.967, -2.288, 0, 13.3, 3.384, 0, 13.567, 0.259, 0, 13.633, 0.455, 0, 13.867, -5.245, 0, 14.267, 5.657, 0, 14.567, -2.059, 0, 14.8, 2.12, 0, 15.067, -4.545, 0, 15.433, 4.89, 0, 15.733, -1.896, 0, 16, 1.964, 0, 16.3, -2.776, 0, 16.567, -0.024, 0, 16.6, -0.028, 0, 16.9, 1.653, 0, 17.167, 0.06, 0, 17.3, 0.628, 0, 17.567, -1.475, 0, 17.867, 0.341, 0, 18.133, -1.035, 0, 18.433, 1.439, 0, 18.7, -0.679, 0, 18.967, 1.255, 0, 19.2, -1.487, 1, 19.3, -1.487, 19.4, -1.176, 19.5, 0.837, 1, 19.578, 2.402, 19.655, 4.849, 19.733, 4.849]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation66", "Segments": [0, -11.659, 0, 0.333, 0, 0, 0.533, -6.033, 0, 0.733, 12.756, 0, 1.033, -16.56, 0, 1.367, 8.585, 0, 1.733, -4.179, 0, 2.1, 4.247, 0, 2.5, -9.083, 0, 2.867, 9.317, 0, 3.167, -5.076, 0, 3.467, 4.768, 0, 3.767, -5.949, 0, 4.033, 1.373, 0, 4.2, -0.32, 0, 4.467, 4.575, 0, 4.967, -3.814, 0, 5.267, 0.955, 0, 5.433, -0.055, 0, 5.7, 2.701, 0, 5.967, -1.097, 0, 6.167, 0.851, 0, 6.467, -2.591, 0, 6.733, 0.247, 0, 6.9, -3.584, 0, 7.167, 13.582, 0, 7.5, -22.039, 0, 7.833, 23.665, 0, 8.2, -25.758, 0, 8.567, 18.628, 0, 8.867, -8.102, 0, 9.067, 3.762, 0, 9.267, -9.577, 0, 9.567, 16.1, 0, 9.9, -7.902, 0, 10.167, 0.911, 0, 10.4, -2.562, 0, 10.7, 5.957, 0, 11, -0.251, 0, 11.067, -0.015, 0, 11.367, -10.209, 0, 11.7, 13.485, 0, 12, -7.819, 0, 12.3, 5.99, 0, 12.6, -5.577, 0, 12.867, 2.28, 0, 13.1, -3.347, 0, 13.4, 6.448, 0, 13.967, -7.477, 0, 14.367, 10.014, 0, 14.667, -5.237, 0, 14.9, 3.812, 0, 15.2, -7.894, 0, 15.533, 9.396, 0, 15.867, -5.096, 0, 16.133, 3.99, 0, 16.4, -5.556, 0, 16.933, 2.152, 0, 17.267, -0.29, 0, 17.433, 0.357, 0, 17.7, -2.29, 0, 17.967, 1.07, 0, 18.233, -1.756, 0, 18.533, 2.772, 0, 18.833, -1.645, 0, 19.067, 1.953, 1, 19.156, 1.953, 19.244, 0.951, 19.333, -2.6, 1, 19.466, -7.925, 19.6, -11.659, 19.733, -11.659]}, {"Target": "Parameter", "Id": "Param55", "Segments": [0, -10.57, 0, 0.333, 0, 2, 0.4, 0, 0, 0.6, -6.235, 0, 0.8, 19.189, 0, 1.1, -29.963, 0, 1.467, 20.137, 0, 1.833, -11.937, 0, 2.167, 10.723, 0, 2.533, -16.969, 0, 2.933, 18.776, 0, 3.267, -12.571, 0, 3.567, 11.57, 0, 3.867, -13.604, 0, 4.167, 5.439, 0, 4.367, 0.734, 0, 4.567, 6.75, 0, 5, -7.653, 0, 5.367, 3.216, 0, 5.567, 0.255, 0, 5.8, 4.266, 0, 6.1, -2.703, 0, 6.3, 1.366, 0, 6.567, -4.727, 0, 6.8, 0.519, 0, 7, -4.749, 0, 7.233, 19.605, 0, 7.533, -30, 2, 7.6, -30, 0, 7.867, 30, 2, 7.933, 30, 0, 8.233, -30, 2, 8.333, -30, 0, 8.6, 30, 2, 8.667, 30, 0, 8.967, -17.107, 0, 9.2, 3.663, 0, 9.367, -11.757, 0, 9.633, 27.133, 0, 10, -18.44, 0, 10.3, 5.117, 0, 10.533, -4.765, 0, 10.8, 11.653, 0, 11.133, -3.28, 0, 11.167, -3.229, 0, 11.4, -14.178, 0, 11.767, 24.737, 0, 12.1, -17.491, 0, 12.4, 14.921, 0, 12.7, -13.929, 0, 13, 6.511, 0, 13.233, -6.359, 0, 13.5, 12.59, 0, 14, -11.606, 0, 14.433, 18.708, 0, 14.767, -11.841, 0, 15.033, 9.084, 0, 15.3, -14.455, 0, 15.633, 19.398, 0, 15.967, -12.567, 0, 16.233, 10.119, 0, 16.533, -12.178, 0, 16.867, 7.109, 0, 17.333, -1.03, 0, 17.533, 0.336, 0, 17.8, -3.943, 0, 18.1, 2.728, 0, 18.367, -3.485, 0, 18.633, 5.741, 0, 18.933, -4.259, 0, 19.2, 3.867, 1, 19.289, 3.867, 19.378, -0.644, 19.467, -5.122, 1, 19.556, -9.601, 19.644, -10.57, 19.733, -10.57]}, {"Target": "Parameter", "Id": "Param28", "Segments": [0, -7.12, 2, 0.333, -7.12, 0, 1.2, 7.36, 0, 2.4, -7.12, 0, 3.6, 7.36, 0, 4.833, -7.12, 0, 6.133, 7.36, 0, 7.333, -7.12, 0, 8.533, 7.36, 0, 9.767, -7.12, 0, 11, 7.36, 0, 12.267, -7.12, 0, 13.467, 7.36, 0, 14.7, -7.12, 0, 15.933, 7.36, 0, 17.167, -7.12, 0, 18.4, 7.36, 0, 19.733, -7.12]}, {"Target": "Parameter", "Id": "Param29", "Segments": [0, -1.278, 2, 0.333, -1.278, 0, 0.4, -4.74, 0, 1.633, 5.1, 0, 2.933, -4.74, 0, 4.133, 5.1, 1, 4.366, 5.1, 4.6, 2.475, 4.833, -1.278, 1, 5, -3.958, 5.166, -4.74, 5.333, -4.74, 0, 6.533, 5.1, 0, 7.833, -4.74, 0, 9.067, 5.1, 1, 9.3, 5.1, 9.534, 2.475, 9.767, -1.278, 1, 9.934, -3.958, 10.1, -4.74, 10.267, -4.74, 0, 11.467, 5.1, 0, 12.733, -4.74, 0, 13.967, 5.1, 1, 14.211, 5.1, 14.456, 2.571, 14.7, -1.278, 1, 14.867, -3.902, 15.033, -4.74, 15.2, -4.74, 0, 16.4, 5.1, 0, 17.633, -4.74, 0, 18.967, 5.1, 0, 19.733, -1.278]}, {"Target": "Parameter", "Id": "Param30", "Segments": [0, -5.909, 2, 0.333, -5.909, 0, 1.033, 2.533, 0, 2.233, -6.287, 0, 3.433, 2.533, 0, 4.7, -6.287, 1, 4.744, -6.287, 4.789, -6.471, 4.833, -5.909, 1, 5.211, -1.124, 5.589, 2.533, 5.967, 2.533, 0, 7.167, -6.287, 0, 8.367, 2.533, 0, 9.6, -6.287, 1, 9.656, -6.287, 9.711, -6.553, 9.767, -5.909, 1, 10.134, -1.656, 10.5, 2.533, 10.867, 2.533, 0, 12.1, -6.287, 0, 13.3, 2.533, 0, 14.533, -6.287, 1, 14.589, -6.287, 14.644, -6.561, 14.7, -5.909, 1, 15.056, -1.732, 15.411, 2.533, 15.767, 2.533, 0, 17, -6.287, 0, 18.233, 2.533, 0, 19.6, -6.287, 0, 19.733, -5.909]}, {"Target": "Parameter", "Id": "Param31", "Segments": [0, -0.551, 2, 0.333, -0.551, 0, 0.433, 3.018, 0, 1.633, -6.18, 0, 2.967, 3.018, 0, 4.167, -6.18, 1, 4.389, -6.18, 4.611, -3.91, 4.833, -0.551, 1, 5.011, 2.136, 5.189, 3.018, 5.367, 3.018, 0, 6.567, -6.18, 0, 7.833, 3.018, 0, 9.1, -6.18, 1, 9.322, -6.18, 9.545, -3.91, 9.767, -0.551, 1, 9.945, 2.136, 10.122, 3.018, 10.3, 3.018, 0, 11.5, -6.18, 0, 12.767, 3.018, 0, 14, -6.18, 1, 14.233, -6.18, 14.467, -4.001, 14.7, -0.551, 1, 14.878, 2.078, 15.055, 3.018, 15.233, 3.018, 0, 16.433, -6.18, 0, 17.667, 3.018, 0, 19, -6.18, 0, 19.733, -0.551]}, {"Target": "Parameter", "Id": "Param32", "Segments": [0, -2.714, 2, 0.333, -2.714, 0, 1.1, 2.284, 0, 2.3, -2.796, 0, 3.5, 2.284, 0, 4.767, -2.796, 1, 4.789, -2.796, 4.811, -2.878, 4.833, -2.714, 1, 5.233, 0.24, 5.633, 2.284, 6.033, 2.284, 0, 7.233, -2.796, 0, 8.433, 2.284, 0, 9.667, -2.796, 1, 9.7, -2.796, 9.734, -2.927, 9.767, -2.714, 1, 10.156, -0.226, 10.544, 2.284, 10.933, 2.284, 0, 12.167, -2.796, 0, 13.367, 2.284, 0, 14.6, -2.796, 1, 14.633, -2.796, 14.667, -2.93, 14.7, -2.714, 1, 15.078, -0.266, 15.455, 2.284, 15.833, 2.284, 0, 17.067, -2.796, 0, 18.3, 2.284, 0, 19.733, -2.714]}, {"Target": "Parameter", "Id": "Param33", "Segments": [0, -2.392, 2, 0.333, -2.392, 0, 0.467, 3.279, 0, 1.667, -10.047, 0, 3, 3.279, 0, 4.2, -10.047, 1, 4.411, -10.047, 4.622, -7.035, 4.833, -2.392, 1, 5.022, 1.763, 5.211, 3.279, 5.4, 3.279, 0, 6.6, -10.047, 0, 7.867, 3.279, 0, 9.133, -10.047, 1, 9.344, -10.047, 9.556, -7.035, 9.767, -2.392, 1, 9.956, 1.763, 10.144, 3.279, 10.333, 3.279, 0, 11.533, -10.047, 0, 12.8, 3.279, 0, 14.033, -10.047, 1, 14.255, -10.047, 14.478, -7.169, 14.7, -2.392, 1, 14.889, 1.669, 15.078, 3.279, 15.267, 3.279, 0, 16.467, -10.047, 0, 17.7, 3.279, 0, 19.033, -10.047, 0, 19.733, -2.392]}, {"Target": "Parameter", "Id": "Param34", "Segments": [0, -21.182, 2, 0.333, -21.182, 0, 1.133, 11.7, 0, 2.333, -21.42, 0, 3.533, 11.7, 0, 4.8, -21.42, 1, 4.811, -21.42, 4.822, -21.726, 4.833, -21.182, 1, 5.244, -1.046, 5.656, 11.7, 6.067, 11.7, 0, 7.267, -21.42, 0, 8.467, 11.7, 0, 9.7, -21.42, 1, 9.722, -21.42, 9.745, -22.023, 9.767, -21.182, 1, 10.167, -6.042, 10.567, 11.7, 10.967, 11.7, 0, 12.2, -21.42, 0, 13.4, 11.7, 0, 14.633, -21.42, 1, 14.655, -21.42, 14.678, -22.03, 14.7, -21.182, 1, 15.089, -6.342, 15.478, 11.7, 15.867, 11.7, 0, 17.1, -21.42, 0, 18.333, 11.7, 0, 19.733, -21.182]}, {"Target": "Parameter", "Id": "Param281", "Segments": [0, 0, 2, 0.333, 0, 0, 1.333, 1, 0, 2.667, 0, 0, 4.2, 1, 0, 5.533, 0, 0, 7.067, 1, 0, 8.4, 0, 0, 9.733, 1, 0, 11.267, 0, 0, 12.6, 1, 0, 14.133, 0, 0, 15.467, 1, 0, 16.833, 0, 0, 18.333, 1, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param197mao2", "Segments": [0, 0, 2, 0.333, 0, 0, 0.633, 2.894, 0, 2.067, -2.454, 1, 2.267, -2.454, 2.467, -1.729, 2.667, 0, 1, 2.878, 1.825, 3.089, 2.894, 3.3, 2.894, 0, 4.933, -2.454, 1, 5.133, -2.454, 5.333, -1.729, 5.533, 0, 1, 5.744, 1.825, 5.956, 2.894, 6.167, 2.894, 0, 7.8, -2.454, 1, 8, -2.454, 8.2, -1.729, 8.4, 0, 1, 8.611, 1.825, 8.822, 2.894, 9.033, 2.894, 0, 10.667, -2.454, 1, 10.867, -2.454, 11.067, -1.729, 11.267, 0, 1, 11.478, 1.825, 11.689, 2.894, 11.9, 2.894, 0, 13.333, -2.454, 1, 13.6, -2.454, 13.866, -1.913, 14.133, 0, 1, 14.344, 1.514, 14.556, 2.894, 14.767, 2.894, 0, 16.2, -2.454, 1, 16.411, -2.454, 16.622, -1.613, 16.833, 0, 1, 17.089, 1.952, 17.344, 2.894, 17.6, 2.894, 0, 19.1, -2.454, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param112", "Segments": [0, -30, 0, 0.333, 0, 0, 1.2, -30, 2, 4.467, -30, 2, 5.233, -30, 2, 7.433, -30, 2, 8.667, -30, 2, 10.633, -30, 2, 11.933, -30, 2, 16.133, -30, 2, 18.433, -30, 2, 19.733, -30]}, {"Target": "Parameter", "Id": "Param272", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param293", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "ParamCheek", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param193", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param192", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param196", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param282", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param183", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param194", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param195", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param184", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param289", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param185", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param290", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param291", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param186", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param187", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param188", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param292", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param189", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param190", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param191", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param8", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param23", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param24", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param25", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param26", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param27", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param200", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param286", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param287", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param288", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param205", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param203", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param21", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param213", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param215", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param216", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param217", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param218", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param219", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param220", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param197", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param198", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param279", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param212", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param270", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param271", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param283", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param230", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param231", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param207", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param236", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param237", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param239", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param240", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param242", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param243", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param244", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param275", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param225", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param227", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param232", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param285", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param276", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param142", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param143", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param144", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param147", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param145", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param134", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param246", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param141", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param182", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param247", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param229", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param245", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param117", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param140", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param262", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param263", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param115", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param116", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param260", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param261", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param133", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param139", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param257", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param137", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param138", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param135", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param176", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param136", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param177", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param251", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param66", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param67", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param68", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param69", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param35", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param36", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param37", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param39", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param38", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param40", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param41", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param42", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param43", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param44", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param45", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param46", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param62", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param63", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param64", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param65", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param72", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param73", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param74", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param75", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param53", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param54", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param81", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param82", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param70", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param71", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param77", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param78", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param79", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param80", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation63", "Segments": [0, -0.675, 0, 19.733, -0.675]}, {"Target": "Parameter", "Id": "Param84", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param85", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param83", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param86", "Segments": [0, 1, 0, 19.733, 1]}, {"Target": "Parameter", "Id": "Param91", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param2", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param87", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param88", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param94", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param95", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param92", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param93", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param96", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param97", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param89", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param90", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation56", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param98", "Segments": [0, -6, 0, 19.733, -6]}, {"Target": "Parameter", "Id": "Param99", "Segments": [0, -10.921, 0, 19.733, -10.921]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation67", "Segments": [0, -10.939, 0, 19.733, -10.939]}, {"Target": "Parameter", "Id": "Param218mao2", "Segments": [0, 1, 0, 19.733, 1]}, {"Target": "Parameter", "Id": "Param211mao2", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param216mao2", "Segments": [0, -8.88, 0, 19.733, -8.88]}, {"Target": "Parameter", "Id": "Param217mao2", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param202mao2", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param203mao2", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param204mao2", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param199mao2", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param200mao2", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param201mao2", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param208mao2", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param209mao2", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param210mao2", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param205mao2", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param206mao2", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param207mao2", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param212mao2", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param213mao2", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param214mao2", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param215mao2", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param198mao2", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param219mao2", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation143mao2", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation144mao2", "Segments": [0, -10.82, 0, 19.733, -10.82]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation145mao2", "Segments": [0, 8.46, 0, 19.733, 8.46]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation146mao2", "Segments": [0, 28.83, 0, 19.733, 28.83]}, {"Target": "Parameter", "Id": "Param264", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param100", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param105", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param108", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param109", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param107", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param284", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param101", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param106", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param104", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param103", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param102", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation71", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation72", "Segments": [0, 8.085, 0, 19.733, 8.085]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation73", "Segments": [0, 11.915, 0, 19.733, 11.915]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation74", "Segments": [0, 12.341, 0, 19.733, 12.341]}, {"Target": "Parameter", "Id": "Param148", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param146", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param110", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param111", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param248", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param249", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param206", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param114", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param113", "Segments": [0, 1, 0, 19.733, 1]}, {"Target": "Parameter", "Id": "Param253", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param254", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param158", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param149", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param150", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param151", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param255", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param256", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param153", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param152", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param155", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param154", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param181", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param180", "Segments": [0, 0.5, 0, 19.733, 0.5]}, {"Target": "Parameter", "Id": "Param156", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param157", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param159", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param160", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param119", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param265", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param118", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param120", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param121", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param161", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param259", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param123", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param124", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param130", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param131", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param178", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param179", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param125", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param126", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param127", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param128", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param171", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param173", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param172", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param122", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param170", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param129", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param132", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param250", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param258", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param162", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param169", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param174", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param175", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param163", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param164", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param165", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param166", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param167", "Segments": [0, 0, 0, 19.733, 0]}, {"Target": "Parameter", "Id": "Param168", "Segments": [0, 0, 0, 19.733, 0]}], "UserData": [{"Time": 0.0, "Value": ""}, {"Time": 0.333, "Value": ""}, {"Time": 19.233, "Value": ""}]}