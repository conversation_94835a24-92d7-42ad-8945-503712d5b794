{"Version": 3, "Meta": {"Duration": 12.0, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 389, "TotalSegmentCount": 3856, "TotalPointCount": 4472, "UserDataCount": 3, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 2, 0.333, 0, 0, 1, -2.4, 1, 1.222, -2.4, 1.445, -2.318, 1.667, 0, 1, 1.822, 1.623, 1.978, 7.801, 2.133, 7.801, 0, 2.467, 7, 0, 2.933, 16.052, 1, 3.1, 16.052, 3.266, 16.401, 3.433, 15, 1, 3.666, 13.039, 3.9, 2.531, 4.133, 2.531, 0, 4.667, 3.32, 2, 5.4, 3.32, 2, 5.867, 3.32, 2, 7.4, 3.32, 2, 7.967, 3.32, 0, 9.233, -7, 1, 9.511, -7, 9.789, -6.626, 10.067, -3.2, 1, 10.222, -1.282, 10.378, 5.92, 10.533, 5.92, 1, 10.711, 5.92, 10.889, 4.877, 11.067, 3.458, 1, 11.378, 0.975, 11.689, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 2, 0.333, 0, 0, 0.667, -6.54, 0, 1.333, 6.54, 0, 1.667, 5.76, 0, 1.833, 8.639, 0, 2.2, -21, 1, 2.256, -21, 2.311, -18.741, 2.367, -17.97, 1, 2.467, -16.582, 2.567, -16.388, 2.667, -16.388, 0, 2.933, -19.008, 1, 2.978, -19.008, 3.022, -16.758, 3.067, -16.39, 1, 3.189, -15.379, 3.311, -15.227, 3.433, -14, 1, 3.522, -13.108, 3.611, -6.54, 3.7, -6.54, 1, 3.778, -6.54, 3.855, -24.414, 3.933, -26.258, 1, 4.078, -29.683, 4.222, -30, 4.367, -30, 1, 4.467, -30, 4.567, -30.562, 4.667, -27.911, 1, 4.911, -21.431, 5.156, -11, 5.4, -11, 0, 5.867, -27, 1, 6.078, -27, 6.289, -27.461, 6.5, -24.04, 1, 6.8, -19.178, 7.1, -7, 7.4, -7, 0, 7.967, -29, 0, 9.233, -23, 2, 10.067, -23, 0, 10.533, -27.82, 1, 10.711, -27.82, 10.889, -27.462, 11.067, -25.96, 1, 11.156, -25.209, 11.244, -24.654, 11.333, -22, 1, 11.489, -17.355, 11.644, 4.186, 11.8, 4.186, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 2, 0.333, 0, 0, 1, -0.96, 1, 1.222, -0.96, 1.445, -1.027, 1.667, 0, 1, 1.822, 0.719, 1.978, 11.848, 2.133, 11.848, 0, 2.467, 10.516, 0, 3.433, 11, 0, 4.133, 9.334, 0, 4.667, 10.516, 0, 5.4, 5.246, 0, 5.867, 6, 2, 7.4, 6, 0, 7.967, 5.246, 0, 9.233, 10, 2, 10.067, 10, 1, 10.222, 10, 10.378, 10.208, 10.533, 10.516, 1, 10.711, 10.868, 10.889, 11, 11.067, 11, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param272", "Segments": [0, 0, 2, 0.333, 0, 2, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 0, 3.433, 0.545, 0, 4.133, -14, 1, 4.311, -14, 4.489, -14.121, 4.667, -11, 1, 4.911, -6.709, 5.156, 0.545, 5.4, 0.545, 0, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 2, 0.333, 1, 2, 0.567, 1, 0, 0.6, 0, 0, 0.667, 1, 2, 1.667, 1, 0, 1.8, 0, 1, 1.822, 0, 1.845, 0.759, 1.867, 0.8, 1, 1.956, 0.964, 2.044, 1, 2.133, 1, 2, 3, 1, 0, 3.067, 0.002, 1, 3.1, 0.002, 3.134, 0.884, 3.167, 0.9, 1, 3.334, 0.98, 3.5, 1, 3.667, 1, 0, 3.867, 0, 2, 4.133, 0, 1, 4.311, 0, 4.489, -0.021, 4.667, 0.046, 1, 4.911, 0.138, 5.156, 1, 5.4, 1, 0, 5.867, 0.8, 0, 7.4, 1, 0, 7.967, 0.659, 0, 9.233, 0.7, 0, 10.067, 0.659, 0, 10.233, 0.8, 0, 10.333, 0, 1, 10.366, 0, 10.4, 0.584, 10.433, 0.7, 1, 10.466, 0.816, 10.5, 0.8, 10.533, 0.8, 2, 11.067, 0.8, 1, 11.222, 0.8, 11.378, 0.789, 11.533, 0.727, 1, 11.578, 0.709, 11.622, 0, 11.667, 0, 0, 11.767, 1, 2, 12, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 2, 0.333, 0, 2, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.667, 0, 0, 3.867, 1, 0, 4.133, 0.5, 2, 4.667, 0.5, 2, 5.4, 0.5, 2, 5.867, 0.5, 2, 7.4, 0.5, 0, 7.967, 0.4, 2, 9.233, 0.4, 2, 10.067, 0.4, 2, 10.233, 0.4, 0, 10.333, 0, 0, 10.533, 0.4, 2, 11.067, 0.4, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 2, 0.333, 1, 2, 0.567, 1, 0, 0.6, 0, 0, 0.667, 1, 2, 1.667, 1, 0, 1.8, 0, 1, 1.822, 0, 1.845, 0.759, 1.867, 0.8, 1, 1.956, 0.964, 2.044, 1, 2.133, 1, 2, 3, 1, 0, 3.067, 0.002, 1, 3.1, 0.002, 3.134, 0.884, 3.167, 0.9, 1, 3.334, 0.98, 3.5, 1, 3.667, 1, 0, 3.867, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 0, 7.967, 0.932, 1, 8.389, 0.932, 8.811, 0.921, 9.233, 0.9, 1, 9.511, 0.886, 9.789, 0.88, 10.067, 0.88, 0, 10.233, 0.9, 0, 10.333, 0, 0, 10.433, 0.9, 0, 10.533, 0.88, 2, 11.067, 0.88, 0, 11.533, 0.917, 0, 11.667, 0, 0, 11.767, 1, 2, 12, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 2, 0.333, 0, 2, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.667, 0, 0, 3.867, 1, 0, 4.133, 0.5, 2, 4.667, 0.5, 1, 4.911, 0.5, 5.156, 0.596, 5.4, 0.8, 1, 5.556, 0.93, 5.711, 1, 5.867, 1, 0, 7.4, 0.3, 0, 7.967, 0.4, 2, 9.233, 0.4, 2, 10.067, 0.4, 2, 10.233, 0.4, 0, 10.333, 0, 0, 10.533, 0.4, 2, 11.067, 0.4, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param273", "Segments": [0, 0, 2, 0.333, 0, 2, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 0, 4.133, 1, 2, 4.667, 1, 2, 5.4, 1, 2, 5.867, 1, 2, 7.4, 1, 0, 7.967, 0.9, 2, 9.233, 0.9, 2, 10.067, 0.9, 0, 10.333, 0.2, 0, 10.533, 0.9, 2, 11.067, 0.9, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 2, 0.333, 0, 2, 1.667, 0, 2, 1.933, 0, 0, 2, -0.3, 0, 2.067, 0.3, 1, 2.089, 0.3, 2.111, 0.116, 2.133, 0.1, 1, 2.244, 0.022, 2.356, 0, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 7.4, 0, 0, 7.467, -0.072, 0, 7.533, 0.07, 1, 7.555, 0.07, 7.578, 0.047, 7.6, 0, 1, 7.622, -0.047, 7.645, -0.072, 7.667, -0.072, 0, 7.733, 0.07, 1, 7.755, 0.07, 7.778, 0.047, 7.8, 0, 1, 7.822, -0.047, 7.845, -0.072, 7.867, -0.072, 0, 7.933, 0.07, 1, 7.955, 0.07, 7.978, 0.047, 8, 0, 1, 8.022, -0.047, 8.045, -0.072, 8.067, -0.072, 0, 8.133, 0.07, 1, 8.155, 0.07, 8.178, 0.047, 8.2, 0, 1, 8.222, -0.047, 8.245, -0.072, 8.267, -0.072, 0, 8.333, 0.07, 1, 8.355, 0.07, 8.378, 0.047, 8.4, 0, 1, 8.422, -0.047, 8.445, -0.072, 8.467, -0.072, 0, 8.533, 0.07, 1, 8.555, 0.07, 8.578, 0.047, 8.6, 0, 1, 8.622, -0.047, 8.645, -0.072, 8.667, -0.072, 0, 8.733, 0.07, 1, 8.755, 0.07, 8.778, 0.047, 8.8, 0, 1, 8.822, -0.047, 8.845, -0.072, 8.867, -0.072, 0, 8.933, 0.07, 1, 8.955, 0.07, 8.978, 0.047, 9, 0, 1, 9.022, -0.047, 9.045, -0.072, 9.067, -0.072, 0, 9.133, 0.07, 1, 9.155, 0.07, 9.178, 0.032, 9.2, 0, 1, 9.489, -0.41, 9.778, -0.6, 10.067, -0.6, 0, 10.533, 0, 0, 11.067, -0.1, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 2, 0.333, 0, 2, 1.667, 0, 0, 2.133, -0.9, 0, 2.467, -0.6, 2, 2.933, -0.6, 2, 3.433, -0.6, 2, 4.133, -0.6, 2, 4.667, -0.6, 0, 5.4, 0.3, 2, 5.867, 0.3, 2, 7.4, 0.3, 0, 7.967, -0.6, 0, 9.233, -0.4, 0, 10.067, -0.6, 0, 10.533, -0.5, 2, 11.067, -0.5, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 2, 0.333, 0, 2, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 0, 4.133, -0.5, 2, 4.667, -0.5, 0, 5.4, -0.3, 2, 5.867, -0.3, 2, 7.4, -0.3, 0, 7.967, -0.5, 2, 9.233, -0.5, 0, 10.067, -0.6, 0, 10.533, -0.2, 2, 11.067, -0.2, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 2, 0.333, 0, 2, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 0, 4.133, -0.5, 2, 4.667, -0.5, 0, 5.4, -0.3, 2, 5.867, -0.3, 2, 7.4, -0.3, 0, 7.967, -0.5, 2, 9.233, -0.5, 0, 10.067, -0.6, 0, 10.533, -0.2, 2, 11.067, -0.2, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param", "Segments": [0, -30, 2, 0.333, -30, 2, 1.667, -30, 0, 2.133, 26, 2, 2.467, 26, 0, 2.667, 30, 2, 2.933, 30, 0, 3.433, 0, 2, 4.133, 0, 0, 4.6, 30, 0, 5.033, -19, 1, 5.155, -19, 5.278, 17.562, 5.4, 24, 1, 5.522, 30.438, 5.645, 30, 5.767, 30, 2, 5.867, 30, 0, 6.067, 17, 1, 6.156, 17, 6.244, 18.855, 6.333, 22, 1, 6.489, 27.503, 6.644, 30, 6.8, 30, 0, 7.4, 0, 0, 7.733, 12, 0, 7.967, 0, 0, 8.133, 24, 0, 8.367, -3, 0, 8.567, 9, 0, 8.733, 0, 0, 9.033, 23, 2, 9.233, 23, 0, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 0, 12, -30]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 1, 2, 0.333, 1, 2, 1.667, 1, 1, 1.722, 1, 1.778, -0.489, 1.833, -0.6, 1, 1.933, -0.799, 2.033, -0.8, 2.133, -0.8, 2, 2.467, -0.8, 2, 2.667, -0.8, 2, 2.933, -0.8, 2, 3.433, -0.8, 0, 4.133, -1.5, 2, 4.3, -1.5, 0, 4.6, -0.2, 0, 4.867, -1.4, 0, 5.133, 0, 0, 5.4, -1.5, 1, 5.467, -1.5, 5.533, -1.139, 5.6, -0.6, 1, 5.656, -0.151, 5.711, 0, 5.767, 0, 0, 5.867, -0.3, 0, 5.967, -0.2, 0, 6.067, -1.2, 0, 6.2, -0.5, 1, 6.244, -0.5, 6.289, -0.516, 6.333, -0.6, 1, 6.389, -0.705, 6.444, -0.8, 6.5, -0.8, 0, 6.8, -0.3, 0, 7.167, -0.8, 0, 7.333, 0.1, 0, 7.5, -0.6, 1, 7.556, -0.6, 7.611, -0.48, 7.667, -0.4, 1, 7.778, -0.24, 7.889, -0.2, 8, -0.2, 0, 8.233, -0.8, 1, 8.289, -0.8, 8.344, -0.79, 8.4, -0.6, 1, 8.456, -0.41, 8.511, -0.1, 8.567, -0.1, 2, 8.733, -0.1, 0, 8.867, -0.6, 0, 9.033, -0.1, 2, 9.233, -0.1, 0, 10.067, -1.5, 2, 10.533, -1.5, 2, 11.067, -1.5, 0, 12, 1]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 2, 0.333, 0, 2, 1.667, 0, 0, 1.833, 0.3, 2, 2.133, 0.3, 0, 2.3, 0.5, 0, 2.467, 0.323, 0, 2.667, 0.4, 0, 2.933, 0.3, 0, 3.433, 0.4, 0, 3.733, 0, 0, 3.9, 0.4, 0, 4.133, 0, 2, 4.2, 0, 0, 4.333, 0.5, 0, 4.467, 0.28, 0, 4.6, 0.4, 0, 4.7, 0, 0, 4.867, 0.4, 0, 4.9, 0.2, 0, 5.033, 0.5, 0, 5.233, 0.2, 0, 5.4, 0.6, 0, 5.6, 0.3, 0, 5.767, 0.7, 0, 5.867, 0.4, 0, 5.967, 0.7, 0, 6.067, 0.4, 0, 6.2, 0.6, 1, 6.244, 0.6, 6.289, 0.345, 6.333, 0.2, 1, 6.389, 0.018, 6.444, 0, 6.5, 0, 1, 6.544, 0, 6.589, 0.486, 6.633, 0.6, 1, 6.678, 0.714, 6.722, 0.7, 6.767, 0.7, 0, 6.833, 0.3, 0, 6.933, 0.6, 0, 7.067, 0.2, 0, 7.167, 0.4, 0, 7.333, 0.2, 1, 7.389, 0.2, 7.444, 0.21, 7.5, 0.3, 1, 7.533, 0.354, 7.567, 0.5, 7.6, 0.5, 1, 7.622, 0.5, 7.645, 0.335, 7.667, 0.3, 1, 7.722, 0.214, 7.778, 0.2, 7.833, 0.2, 0, 8, 0.5, 0, 8.233, 0.3, 0, 8.4, 0.4, 0, 8.467, 0.2, 0, 8.567, 0.4, 1, 8.622, 0.4, 8.678, 0.218, 8.733, 0.2, 1, 9.178, 0.052, 9.622, 0, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param268", "Segments": [0, 0, 2, 0.333, 0, 2, 1.667, 0, 2, 2.133, 0, 0, 2.467, 1, 2, 10.067, 1, 0, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param252", "Segments": [0, 0, 2, 0.333, 0, 2, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 0, 4.133, 1, 2, 4.667, 1, 2, 5.4, 1, 2, 5.867, 1, 2, 7.4, 1, 2, 7.967, 1, 2, 9.233, 1, 2, 10.067, 1, 2, 10.533, 1, 2, 11.067, 1, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 2, 0.333, 0, 0, 0.667, -3.969, 0, 1.333, 3.788, 1, 1.444, 3.788, 1.556, 3.859, 1.667, 0, 1, 1.822, -5.402, 1.978, -14.339, 2.133, -14.339, 1, 2.244, -14.339, 2.356, -14.76, 2.467, -9, 1, 2.622, -0.936, 2.778, 16.834, 2.933, 16.834, 1, 3.1, 16.834, 3.266, 17.604, 3.433, 13, 1, 3.666, 6.555, 3.9, -19.941, 4.133, -19.941, 1, 4.311, -19.941, 4.489, -19.727, 4.667, -17, 1, 4.911, -13.251, 5.156, -9, 5.4, -9, 0, 5.867, -19.188, 0, 7.4, -17, 2, 7.967, -17, 1, 8.389, -17, 8.811, -17.284, 9.233, -19.941, 1, 9.511, -21.689, 9.789, -25.778, 10.067, -25.778, 0, 10.533, -19.178, 0, 11.067, -21.051, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 2, 0.333, 0, 0, 1, -6.9, 1, 1.222, -6.9, 1.445, -6.593, 1.667, 0, 1, 1.822, 4.615, 1.978, 29.192, 2.133, 29.192, 1, 2.244, 29.192, 2.356, 29.124, 2.467, 24, 1, 2.622, 16.826, 2.778, 6.225, 2.933, 6.225, 0, 3.433, 12, 0, 4.133, -30, 1, 4.311, -30, 4.489, -29.246, 4.667, -26, 1, 4.911, -21.536, 5.156, -18, 5.4, -18, 0, 5.867, -28, 0, 7.4, -14, 0, 7.967, -30, 0, 9.233, -10.156, 0, 10.067, -12, 0, 10.533, -7.08, 0, 11.067, -10.156, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 2, 0.333, 0, 0, 0.667, 0.6, 0, 1.333, -0.6, 0, 1.667, 0, 0, 2.133, -8.616, 1, 2.244, -8.616, 2.356, -8.221, 2.467, -8, 1, 2.622, -7.691, 2.778, -7.652, 2.933, -7.652, 0, 3.433, -8, 0, 4.133, -5.305, 0, 4.667, -6, 2, 5.4, -6, 2, 5.867, -6, 2, 7.4, -6, 2, 7.967, -6, 2, 9.233, -6, 0, 10.067, -6.784, 0, 10.533, -6.495, 0, 11.067, -6.784, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param267", "Segments": [0, 0, 2, 0.333, 0, 1, 0.778, 0, 1.222, 6.207, 1.667, 10.557, 1, 1.822, 12.079, 1.978, 11.669, 2.133, 11.669, 0, 2.467, 11, 2, 2.933, 11, 2, 3.433, 11, 2, 4.133, 11, 2, 4.667, 11, 2, 5.4, 11, 2, 5.867, 11, 2, 7.4, 11, 2, 7.967, 11, 2, 9.233, 11, 0, 10.067, 11.669, 2, 10.533, 11.669, 0, 11.067, 12.236, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param3", "Segments": [0, 0, 2, 0.333, 0, 2, 1.667, 0, 0, 2.133, 6.064, 1, 2.244, 6.064, 2.356, 4.051, 2.467, 0, 1, 2.622, -5.671, 2.778, -8.519, 2.933, -8.519, 0, 3.433, -5.905, 0, 4.133, -11.303, 0, 4.667, -9, 2, 5.4, -9, 2, 5.867, -9, 2, 7.4, -9, 2, 7.967, -9, 2, 9.233, -9, 0, 10.067, -9.382, 0, 10.533, -8.362, 0, 11.067, -9, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param4", "Segments": [0, 0, 2, 0.333, 0, 0, 0.667, -2.08, 0, 1.333, 2.025, 0, 1.667, 0, 0, 2.133, 10.038, 1, 2.244, 10.038, 2.356, 10.417, 2.467, 8.4, 1, 2.622, 5.576, 2.778, -18.198, 2.933, -20.463, 1, 3.1, -22.889, 3.266, -23.03, 3.433, -24.944, 1, 3.666, -27.623, 3.9, -30, 4.133, -30, 2, 4.667, -30, 0, 5.4, -18, 0, 5.867, -23, 0, 7.4, -18, 0, 7.967, -28, 0, 9.233, -21.94, 0, 10.067, -23.805, 1, 10.211, -23.805, 10.356, -21.642, 10.5, -20.463, 1, 10.689, -18.921, 10.878, -19.149, 11.067, -17.04, 1, 11.289, -14.559, 11.511, 2.333, 11.733, 2.333, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param266", "Segments": [0, 0, 2, 0.333, 0, 1, 0.778, 0, 1.222, 2.194, 1.667, 10.557, 1, 1.822, 13.484, 1.978, 21.395, 2.133, 21.395, 1, 2.244, 21.395, 2.356, 22.083, 2.467, 16.274, 1, 2.634, 7.559, 2.8, -14.392, 2.967, -14.392, 0, 3.433, -5.7, 0, 4.133, -13.52, 1, 4.311, -13.52, 4.489, -11.69, 4.667, -11, 1, 4.911, -10.051, 5.156, -10.001, 5.4, -10.001, 0, 5.867, -12.717, 0, 7.4, 6, 0, 7.967, -21, 0, 9.233, -19.32, 0, 10.067, -21, 0, 10.533, -19, 0, 11.067, -21, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param5", "Segments": [0, 0, 2, 0.333, 0, 2, 1.667, 0, 0, 2.133, 15.369, 0, 2.467, 14, 2, 2.933, 14, 0, 3.433, 15.369, 0, 4.133, -21.469, 0, 4.667, -20, 2, 5.4, -20, 0, 5.867, -14, 0, 7.4, -20, 0, 7.967, -11, 1, 8.389, -11, 8.811, -15.004, 9.233, -18.8, 1, 9.511, -21.297, 9.789, -21.469, 10.067, -21.469, 0, 10.533, -11, 0, 11.067, -14.934, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param6", "Segments": [0, 0, 2, 0.333, 0, 0, 0.667, -4.68, 0, 1.333, 4.68, 0, 1.667, 0, 0, 2.133, 15.525, 0, 2.467, 14, 2, 2.933, 14, 0, 3.433, 14.657, 0, 4.133, 12, 2, 4.667, 12, 0, 5.4, 10.674, 0, 5.867, 23, 1, 6.378, 23, 6.889, 20.922, 7.4, 12, 1, 7.589, 8.703, 7.778, -2.298, 7.967, -2.298, 0, 9.233, -1, 0, 10.067, -1.685, 0, 10.533, 10, 1, 10.711, 10, 10.889, 8.755, 11.067, 6.331, 1, 11.378, 2.089, 11.689, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 2, 0.333, 0, 0, 1, 0.546, 0, 1.667, 0, 0, 2.9, 0.546, 0, 4.133, 0.23, 0, 4.667, 0.3, 2, 5.4, 0.3, 2, 5.867, 0.3, 2, 7.4, 0.3, 2, 7.967, 0.3, 2, 9.233, 0.3, 0, 10.067, 0.323, 2, 10.533, 0.323, 0, 11.067, 0.336, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param286", "Segments": [0, 0, 2, 0.333, 0, 0, 1.2, 1, 2, 1.667, 1, 2, 2.133, 1, 2, 2.467, 1, 2, 2.933, 1, 2, 3.433, 1, 2, 4.133, 1, 2, 4.667, 1, 2, 5.4, 1, 2, 5.867, 1, 0, 7.4, 0, 0, 7.933, 1, 0, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param287", "Segments": [0, 0, 2, 0.333, 0, 2, 1.2, 0, 0, 1.667, 1, 2, 2.133, 1, 2, 2.467, 1, 2, 2.933, 1, 2, 3.433, 1, 2, 4.133, 1, 2, 4.667, 1, 2, 5.4, 1, 2, 5.867, 1, 2, 7.367, 1, 0, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param288", "Segments": [0, 0, 2, 0.333, 0, 2, 1.2, 0, 0, 1.667, 1, 2, 2.133, 1, 2, 2.467, 1, 2, 2.933, 1, 2, 3.433, 1, 2, 4.133, 1, 2, 4.667, 1, 2, 5.4, 1, 2, 5.867, 1, 2, 7.4, 1, 2, 7.967, 1, 0, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param201", "Segments": [0, 0, 2, 0.333, 0, 2, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.633, 0, 2, 2.667, 1, 2, 2.933, 1, 2, 3.433, 1, 2, 3.867, 1, 2, 3.9, 1, 2, 4.133, 1, 2, 4.667, 1, 2, 5.4, 1, 2, 5.867, 1, 2, 7.4, 1, 2, 7.967, 1, 2, 9.233, 1, 2, 10.067, 1, 2, 10.533, 1, 2, 11.067, 1, 2, 11.367, 1, 2, 11.4, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param202", "Segments": [0, 0, 2, 0.333, 0, 2, 1.667, 0, 2, 1.967, 0, 2, 2, 1, 2, 2.133, 0.934, 2, 2.467, 0.983, 2, 2.633, 1, 2, 2.933, 1, 2, 3.433, 1, 2, 4.133, 1, 2, 4.667, 1, 2, 5.4, 1, 2, 5.867, 1, 2, 7.4, 1, 2, 7.967, 1, 2, 9.233, 1, 2, 10.067, 1, 2, 10.533, 1, 2, 11.067, 1, 2, 11.367, 1, 2, 11.4, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param205", "Segments": [0, 0, 2, 0.333, 0, 2, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param203", "Segments": [0, 0, 2, 0.333, 0, 2, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param204", "Segments": [0, 0, 2, 0.333, 0, 2, 1.667, 0, 2, 1.967, 0, 2, 2, 1, 2, 2.133, 0.934, 2, 2.467, 0.983, 2, 2.933, 1, 2, 3.433, 1, 2, 4.133, 1, 2, 4.667, 1, 2, 5.4, 1, 2, 5.867, 1, 2, 7.4, 1, 2, 7.967, 1, 2, 9.233, 1, 2, 10.067, 1, 2, 10.533, 1, 2, 11.067, 1, 2, 11.367, 1, 2, 11.4, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param209", "Segments": [0, 0, 2, 0.333, 0, 2, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.633, 0, 2, 2.667, 1, 2, 2.933, 1, 2, 3.433, 1, 2, 3.867, 1, 2, 3.9, 1, 2, 4.133, 1, 2, 4.667, 1, 2, 5.4, 1, 2, 5.867, 1, 2, 7.4, 1, 2, 7.967, 1, 2, 9.233, 1, 2, 10.067, 1, 2, 10.533, 1, 2, 11.067, 1, 2, 11.367, 1, 2, 11.4, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param21", "Segments": [0, 0, 2, 0.333, 0, 2, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.667, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param213", "Segments": [0, 0, 2, 0.333, 0, 2, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.667, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param214", "Segments": [0, 0, 2, 0.333, 0, 2, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.667, 1, 2, 2.933, 1, 2, 3.433, 1, 2, 3.867, 1, 2, 3.9, 1, 2, 4.133, 1, 2, 4.667, 1, 2, 5.4, 1, 2, 5.867, 1, 2, 7.4, 1, 2, 7.667, 1, 2, 7.7, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param215", "Segments": [0, 0, 2, 0.333, 0, 2, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.667, 0, 2, 7.7, 1, 2, 7.967, 1, 2, 9.233, 1, 2, 10.067, 1, 2, 10.533, 1, 2, 11.067, 1, 2, 11.367, 1, 2, 11.4, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param216", "Segments": [0, 0, 2, 0.333, 0, 2, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param217", "Segments": [0, 0, 2, 0.333, 0, 2, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param218", "Segments": [0, 0, 2, 0.333, 0, 2, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param219", "Segments": [0, 0, 2, 0.333, 0, 2, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param220", "Segments": [0, 0, 2, 0.333, 0, 2, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param9", "Segments": [0, 0, 2, 0.333, 0, 0, 1, -1.02, 0, 1.667, 0, 0, 2.133, -6.3, 0, 2.467, -6, 0, 3.433, -6.3, 0, 4.133, -6, 0, 5.4, -6.748, 0, 5.867, -3.78, 1, 6.378, -3.78, 6.889, -4.221, 7.4, -6.3, 1, 7.589, -7.068, 7.778, -9.6, 7.967, -9.6, 0, 9.233, -7.26, 0, 10.067, -8.289, 1, 10.222, -8.289, 10.378, -7.132, 10.533, -6.189, 1, 10.711, -5.111, 10.889, -4.929, 11.067, -4.929, 0, 11.4, -6.987, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param10", "Segments": [0, 0, 2, 0.333, 0, 1, 0.555, 0, 0.778, 0.352, 1, 0.6, 1, 1.222, 0.848, 1.445, 0.842, 1.667, 1.14, 1, 1.822, 1.348, 1.978, 6.672, 2.133, 6.672, 0, 2.467, 6, 0, 2.933, 16.531, 0, 3.433, 16, 2, 4.133, 16, 2, 4.667, 16, 2, 5.4, 16, 0, 5.867, 16.48, 1, 6.378, 16.48, 6.889, 16.417, 7.4, 16, 1, 7.589, 15.846, 7.778, 14.68, 7.967, 14.68, 2, 9.233, 14.68, 2, 10.067, 14.68, 2, 10.533, 14.68, 0, 11.067, 20.62, 1, 11.178, 20.62, 11.289, 16.078, 11.4, 11.355, 1, 11.6, 2.853, 11.8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param11", "Segments": [0, 0, 2, 0.333, 0, 0, 0.667, 1.32, 1, 0.778, 1.32, 0.889, 0.483, 1, -0.42, 1, 1.111, -1.323, 1.222, -1.53, 1.333, -1.53, 0, 1.9, 0.455, 0, 2.367, 0, 0, 2.7, 0.455, 0, 3.167, -5, 2, 3.667, -5, 2, 4.367, -5, 2, 4.9, -5, 2, 5.633, -5, 2, 6.1, -5, 2, 7.4, -5, 2, 7.967, -5, 2, 9.233, -5, 2, 10.067, -5, 2, 10.533, -5, 2, 11.067, -5, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param12", "Segments": [0, 0, 2, 1.7, 0, 0, 1.967, -30, 2, 11.367, -30, 0, 11.7, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param199", "Segments": [0, 0, 2, 0.333, 0, 2, 0.567, 0, 2, 0.833, 0, 2, 1.167, 0, 2, 1.4, 0, 2, 1.967, 0, 0, 2.167, 3.769, 0, 2.433, 0, 2, 2.933, 0, 0, 3.067, 4.417, 0, 3.533, 0, 2, 3.967, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param22", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, 1, 2, 0.833, 1, 2, 1.167, 1, 2, 1.4, 1, 2, 1.967, 1, 2, 2.433, 1, 2, 3.967, 1, 2, 6.867, 1, 2, 7.967, 1, 2, 9.233, 1, 2, 10.067, 1, 2, 10.533, 1, 2, 11.067, 1, 2, 11.967, 1, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param13", "Segments": [0, 0, 2, 0.333, 0, 0, 0.567, -5, 2, 0.833, -5, 2, 1.167, -5, 2, 1.4, -5, 2, 1.967, -5, 2, 2.433, -5, 2, 3.967, -5, 2, 7.967, -5, 0, 9.233, -6.74, 2, 10.067, -6.74, 2, 10.533, -6.74, 2, 11.067, -6.74, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param14", "Segments": [0, 0, 2, 0.333, 0, 2, 1, 0, 2, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 0, 2.933, -28, 2, 3.433, -28, 0, 3.867, -25.36, 0, 4.133, -28, 2, 4.667, -28, 2, 5.4, -28, 2, 5.867, -28, 2, 7.4, -28, 2, 7.967, -28, 2, 9.233, -28, 2, 10.067, -28, 2, 10.533, -28, 2, 11.067, -28, 1, 11.178, -28, 11.289, -28.34, 11.4, -27.481, 1, 11.6, -25.935, 11.8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param210", "Segments": [0, 0, 2, 0.333, 0, 1, 0.778, 0, 1.222, 1.215, 1.667, 2.38, 1, 1.822, 2.788, 1.978, 2.944, 2.133, 3.075, 1, 2.244, 3.169, 2.356, 3.18, 2.467, 3.25, 1, 2.489, 3.264, 2.511, 15.506, 2.533, 15.506, 1, 2.578, 15.506, 2.622, 14.785, 2.667, 13.277, 1, 2.756, 10.26, 2.844, 4.32, 2.933, 4.159, 1, 3.1, 3.855, 3.266, 3.735, 3.433, 3.551, 1, 3.589, 3.379, 3.744, 3.216, 3.9, 3.08, 1, 3.978, 3.012, 4.055, 3, 4.133, 3, 0, 4.667, 3.25, 0, 5.4, 1.26, 0, 5.867, 5.469, 1, 6.378, 5.469, 6.889, 5.318, 7.4, 4.74, 1, 7.589, 4.526, 7.778, 3.752, 7.967, 3.42, 1, 8.1, 3.186, 8.234, 3.159, 8.367, 3.159, 0, 8.633, 3.214, 1, 8.755, 3.214, 8.878, 3.152, 9, 2.537, 1, 9.078, 2.145, 9.155, 1.14, 9.233, 1.14, 0, 10.067, 1.26, 1, 10.222, 1.26, 10.378, -1.612, 10.533, -1.8, 1, 10.711, -2.015, 10.889, -1.98, 11.067, -1.98, 1, 11.178, -1.98, 11.289, 5.268, 11.4, 7.881, 1, 11.456, 9.187, 11.511, 8.853, 11.567, 8.853, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param211", "Segments": [0, 0, 2, 0.333, 0, 2, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 1, 3.589, 0, 3.744, 1.926, 3.9, 5, 1, 3.978, 6.537, 4.055, 7, 4.133, 7, 2, 4.667, 7, 2, 5.4, 7, 2, 5.867, 7, 2, 7.4, 7, 2, 7.967, 7, 1, 8.1, 7, 8.234, 6.948, 8.367, 5.709, 1, 8.456, 4.883, 8.544, 1.574, 8.633, 1.574, 0, 9, 6.029, 0, 9.233, 5.98, 0, 10.067, 7, 1, 10.222, 7, 10.378, -1.034, 10.533, -4.52, 1, 10.711, -8.504, 10.889, -8.72, 11.067, -8.72, 0, 11.367, 14.282, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param228", "Segments": [0, 0, 2, 0.333, 0, 2, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 0, 3.9, -15.74, 0, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 0, 5.867, -8.7, 0, 7.4, 0, 0, 7.967, -30, 0, 8.367, -25.8, 1, 8.456, -25.8, 8.544, -29.375, 8.633, -29.39, 1, 8.755, -29.411, 8.878, -29.407, 9, -29.427, 1, 9.078, -29.44, 9.155, -30, 9.233, -30, 2, 10.067, -30, 2, 10.533, -30, 2, 11.067, -30, 1, 11.178, -30, 11.289, -28.724, 11.4, -21.888, 1, 11.6, -9.583, 11.8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param283", "Segments": [0, 0, 2, 0.333, 0, 2, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 0, 3.167, 10, 0, 3.433, 0, 0, 3.7, 22, 0, 4.033, -9, 1, 4.066, -9, 4.1, -6, 4.133, 0, 1, 4.222, 16, 4.311, 24, 4.4, 24, 0, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 0, 6.433, 12, 0, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param221", "Segments": [0, 0, 2, 0.333, 0, 2, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.633, 0, 2, 2.667, 1, 2, 2.933, 1, 2, 3.433, 1, 2, 4.133, 1, 2, 4.667, 1, 2, 5.4, 1, 2, 5.867, 1, 2, 7.4, 1, 2, 7.967, 1, 2, 9.233, 1, 2, 10.067, 1, 2, 10.533, 1, 2, 11.067, 1, 2, 11.367, 1, 2, 11.4, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param230", "Segments": [0, 0, 2, 0.333, 0, 2, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.567, 0, 2, 11.1, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param231", "Segments": [0, 0, 2, 0.333, 0, 2, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param222", "Segments": [0, 0, 2, 0.333, 0, 2, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param223", "Segments": [0, 0, 2, 0.333, 0, 2, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.633, 0, 2, 2.667, 1, 2, 2.933, 1, 2, 3.433, 1, 2, 4.133, 1, 2, 4.667, 1, 2, 5.4, 1, 2, 5.867, 1, 2, 7.4, 1, 2, 7.967, 1, 2, 9.233, 1, 2, 10.067, 1, 2, 10.533, 1, 2, 11.067, 1, 2, 11.367, 1, 2, 11.4, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param207", "Segments": [0, 0, 2, 0.333, 0, 2, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.633, 0, 2, 2.667, 1, 2, 2.933, 1, 2, 3.433, 1, 2, 3.767, 1, 2, 3.8, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param236", "Segments": [0, 0, 2, 0.333, 0, 2, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 3.767, 0, 2, 3.8, 1, 2, 4.133, 1, 2, 4.667, 1, 2, 5.4, 1, 2, 5.867, 1, 2, 7.4, 1, 2, 7.667, 1, 2, 7.7, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param237", "Segments": [0, 0, 2, 0.333, 0, 2, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param238", "Segments": [0, 0, 2, 0.333, 0, 2, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param239", "Segments": [0, 0, 2, 0.333, 0, 2, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param240", "Segments": [0, 0, 2, 0.333, 0, 2, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param241", "Segments": [0, 0, 2, 0.333, 0, 2, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.667, 0, 2, 7.7, 1, 2, 7.967, 1, 2, 9.233, 1, 2, 10.067, 1, 2, 10.533, 1, 2, 11.067, 1, 2, 11.367, 1, 2, 11.4, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param242", "Segments": [0, 0, 2, 0.333, 0, 2, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param243", "Segments": [0, 0, 2, 0.333, 0, 2, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param244", "Segments": [0, 0, 2, 0.333, 0, 2, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param15", "Segments": [0, 0, 2, 0.333, 0, 0, 1, -0.48, 1, 1.222, -0.48, 1.445, -0.517, 1.667, 0, 1, 1.822, 0.362, 1.978, 5.063, 2.133, 5.063, 0, 2.467, 4, 0, 2.933, 12.082, 1, 3.1, 12.082, 3.266, 12.12, 3.433, 11, 1, 3.666, 9.432, 3.9, 7.077, 4.133, 7.077, 0, 4.667, 8, 0, 5.4, 7.364, 1, 5.556, 7.364, 5.711, 9.497, 5.867, 9.56, 1, 6.378, 9.767, 6.889, 9.822, 7.4, 10.04, 1, 7.589, 10.12, 7.778, 15.08, 7.967, 15.08, 0, 9.233, 13.4, 0, 10.067, 13.952, 0, 10.533, 10.892, 2, 11.067, 10.892, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param16", "Segments": [0, 0, 2, 0.333, 0, 0, 0.533, -0.581, 0, 1.2, 0.939, 0, 1.667, 0, 0, 2.133, 8.584, 0, 2.467, 8, 0, 2.667, 10.906, 0, 2.933, 5.755, 0, 3.433, 7, 2, 4.133, 7, 2, 4.667, 7, 2, 5.4, 7, 2, 5.867, 7, 2, 7.6, 7, 2, 8.167, 7, 2, 9.433, 7, 0, 10.067, 7.778, 2, 10.533, 7.778, 2, 11.067, 7.778, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param17", "Segments": [0, 0, 2, 0.333, 0, 0, 0.733, 3.84, 0, 1.4, -3.84, 1, 1.556, -3.84, 1.711, -3.805, 1.867, 0, 1, 2.022, 3.805, 2.178, 12.918, 2.333, 12.918, 1, 2.444, 12.918, 2.556, 12.776, 2.667, 12.522, 1, 2.822, 12.165, 2.978, 12, 3.133, 12, 2, 3.633, 12, 2, 4.333, 12, 2, 4.867, 12, 2, 5.6, 12, 2, 6.067, 12, 2, 7.767, 12, 2, 8.333, 12, 2, 9.233, 12, 0, 10.067, 12.918, 2, 10.533, 12.918, 2, 11.067, 12.918, 0, 11.667, -4.859, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param18", "Segments": [0, 0, 2, 0.333, 0, 0, 1, 9.46, 0, 1.867, 0, 0, 2.033, 12.532, 0, 2.233, 2.292, 0, 2.333, 5, 2, 2.667, 5, 2, 3.133, 5, 2, 3.633, 5, 2, 4.333, 5, 2, 4.867, 5, 2, 5.6, 5, 2, 6.067, 5, 2, 7.933, 5, 2, 8.5, 5, 2, 9.233, 5, 2, 10.067, 5, 2, 10.533, 5, 2, 11.067, 5, 0, 11.467, -24, 0, 11.867, 5.04, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param19", "Segments": [0, 0, 2, 0.333, 0, 2, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 0, 4.133, -44.48, 0, 4.667, -42, 2, 5.4, -42, 2, 5.867, -42, 2, 7.4, -42, 0, 7.967, -12.39, 1, 8.389, -12.39, 8.811, -18.346, 9.233, -23.28, 1, 9.511, -26.526, 9.789, -26.589, 10.067, -26.589, 2, 10.533, -26.589, 2, 11.067, -26.589, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param224", "Segments": [0, 0, 2, 0.333, 0, 2, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 0, 7.6, -0.78, 0, 8.167, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param225", "Segments": [0, 0, 2, 0.333, 0, 2, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 0, 4.133, 1.02, 0, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.6, 0, 2, 8.167, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param233", "Segments": [0, 0, 2, 0.333, 0, 2, 1.667, 0, 2, 2.133, 0, 0, 2.467, -1.044, 0, 2.667, 4.241, 0, 2.933, 0.967, 0, 3.433, 3, 0, 4.133, -4.301, 0, 4.667, -3, 1, 4.911, -3, 5.156, -3.003, 5.4, -3.22, 1, 5.556, -3.358, 5.711, -4.871, 5.867, -4.92, 1, 6.445, -5.103, 7.022, -5.14, 7.6, -5.14, 1, 7.789, -5.14, 7.978, -2.207, 8.167, -1.48, 1, 8.234, -1.224, 8.3, -1.271, 8.367, -1.22, 1, 8.456, -1.152, 8.544, -1.142, 8.633, -1.142, 0, 9, -1.232, 0, 9.233, -0.7, 0, 10.067, -0.738, 1, 10.222, -0.738, 10.378, -0.724, 10.533, -0.678, 1, 10.711, -0.626, 10.889, -0.591, 11.067, -0.498, 1, 11.167, -0.446, 11.267, 5.796, 11.367, 8.655, 1, 11.422, 10.244, 11.478, 10.801, 11.533, 10.801, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param234", "Segments": [0, 0, 2, 0.333, 0, 2, 1.933, 0, 2, 2.4, 0, 2, 2.733, 0, 2, 3.2, 0, 2, 3.7, 0, 1, 3.933, 0, 4.167, -2.434, 4.4, -12.825, 1, 4.578, -20.742, 4.755, -30, 4.933, -30, 1, 5.178, -30, 5.422, -21.863, 5.667, -18.975, 1, 5.822, -17.137, 5.978, -16.57, 6.133, -16.05, 1, 6.622, -14.414, 7.111, -13.683, 7.6, -11.775, 1, 7.789, -11.038, 7.978, 0.9, 8.167, 0.9, 0, 8.367, -5.2, 0, 8.633, 1.547, 0, 9, -0.304, 0, 9.233, 1.575, 1, 9.511, 1.575, 9.789, 1.468, 10.067, 0, 1, 10.222, -0.822, 10.378, -4.575, 10.533, -4.575, 2, 11.067, -4.575, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param285", "Segments": [0, 0, 2, 0.333, 0, 2, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 0, 4.367, -30, 0, 4.667, -9.9, 0, 4.933, -21, 0, 5.867, 0, 0, 6.867, -30, 0, 7.6, 0, 2, 8.167, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 11.467, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param235", "Segments": [0, 0, 2, 0.333, 0, 2, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 1, 2.534, 0, 2.6, -13.84, 2.667, -17.243, 1, 2.756, -21.78, 2.844, -22, 2.933, -22, 2, 3.433, -22, 0, 4.133, 4.627, 0, 4.667, 3, 2, 5.4, 3, 2, 5.867, 3, 2, 7.6, 3, 0, 8.167, -30, 2, 8.367, -30, 1, 8.456, -30, 8.544, -29.598, 8.633, -29.16, 1, 8.755, -28.558, 8.878, -28.38, 9, -28.38, 0, 9.233, -30, 2, 10.067, -30, 2, 10.533, -30, 1, 10.711, -30, 10.889, -30.753, 11.067, -26.04, 1, 11.378, -17.792, 11.689, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param277", "Segments": [0, 0, 2, 0.333, 0, 0, 0.6, 12.46, 0, 1.133, -9.549, 0, 1.567, 12.46, 0, 1.967, -9.549, 0, 2.4, 12.46, 0, 2.8, -9.549, 0, 3.233, 12.46, 0, 3.633, -9.549, 0, 4.067, 12.46, 0, 4.467, -9.549, 0, 4.9, 12.46, 0, 5.3, -9.549, 0, 5.733, 12.46, 0, 6.133, -9.549, 0, 6.567, 12.46, 0, 6.967, -9.549, 0, 7.4, 12.46, 0, 7.8, -9.549, 0, 8.233, 12.46, 0, 8.767, -9.549, 0, 9.333, 12.46, 0, 9.867, -9.549, 0, 10.3, 12.46, 0, 10.7, -9.549, 0, 11.133, 12.46, 0, 11.533, -9.549, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param278", "Segments": [0, 0, 2, 0.333, 0, 0, 0.6, 0.415, 0, 1.133, -0.318, 0, 1.567, 0.415, 0, 1.967, -0.318, 0, 2.4, 0.415, 0, 2.8, -0.318, 0, 3.233, 0.415, 0, 3.633, -0.318, 0, 4.067, 0.415, 0, 4.6, -0.318, 0, 5.267, 0.415, 0, 5.8, -0.318, 0, 6.467, 0.415, 0, 7, -0.318, 0, 7.667, 0.415, 0, 8.2, -0.318, 0, 8.867, 0.415, 0, 9.4, -0.318, 0, 10.067, 0.415, 0, 10.667, -0.318, 0, 11.2, 0, 0, 11.7, -0.318, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param7", "Segments": [0, 0, 2, 0.333, 0, 0, 1, -9, 1, 1.222, -9, 1.445, -7.752, 1.667, 0, 1, 1.878, 7.365, 2.089, 16.712, 2.3, 16.712, 0, 3.033, -10.062, 0, 3.767, 7.342, 0, 4.467, -6.38, 0, 5.067, 4.323, 0, 5.6, -3.055, 0, 6.133, 2.531, 0, 6.833, -1.383, 0, 7.267, 1.186, 0, 7.9, -6.38, 0, 8.733, 4.323, 0, 9.4, -2.421, 0, 10.033, 2.519, 0, 10.867, -7.854, 0, 11.567, 2.667, 0, 11.867, -1.383, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param66", "Segments": [0, 0, 2, 0.333, 0, 0, 1, -19, 0, 1.667, 0, 0, 1.9, -12, 0, 2.3, 13, 0, 2.733, -10, 0, 3.2, 11, 0, 3.9, -11, 0, 4.467, 8, 0, 5.133, -14, 0, 5.733, 8, 0, 6.367, -5, 0, 6.967, 4, 0, 7.633, -2, 0, 8.4, 3, 0, 9.233, -3, 0, 10.833, 6, 0, 11.667, -8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param67", "Segments": [0, 0, 2, 0.333, 0, 0, 0.667, 16, 0, 1.333, -16, 0, 1.667, 0, 0, 1.9, -12, 0, 2.3, 13, 0, 2.733, -10, 0, 3.2, 11, 0, 3.9, -11, 0, 4.467, 8, 0, 5.133, -14, 0, 5.733, 9, 0, 6.367, -5, 0, 6.967, 4, 0, 7.633, -2, 0, 8.4, 4, 0, 9.233, -4, 0, 10.833, 6, 0, 11.667, -10, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param68", "Segments": [0, 0, 2, 0.333, 0, 0, 1, 17, 1, 1.222, 17, 1.445, 12.897, 1.667, 0, 1, 1.745, -4.514, 1.822, -12, 1.9, -12, 0, 2.3, 13, 0, 2.733, -10, 0, 3.2, 11, 0, 3.9, -11, 0, 4.467, 8, 0, 5.133, -15, 0, 5.733, 8, 0, 6.367, -5, 0, 6.967, 3, 0, 7.633, -3, 0, 8.4, 4, 0, 9.233, -3, 0, 10.833, 7, 0, 11.667, -8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param69", "Segments": [0, 0, 2, 0.333, 0, 0, 0.667, -16, 0, 1.333, 16, 1, 1.444, 16, 1.556, 10.966, 1.667, 0, 1, 1.745, -7.676, 1.822, -12, 1.9, -12, 0, 2.3, 13, 0, 2.733, -10, 0, 3.2, 11, 0, 3.9, -11, 0, 4.467, 9, 0, 5.133, -16, 0, 5.733, 8, 0, 6.367, -4, 0, 6.967, 4, 0, 7.633, -3, 0, 8.4, 4, 0, 9.233, -4, 0, 10.833, 8, 0, 11.667, -9, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param36", "Segments": [0, 0, 2, 0.333, 0, 0, 1, -14.88, 0, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param37", "Segments": [0, 0, 2, 0.333, 0, 0, 1, -13, 0, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param39", "Segments": [0, 0, 2, 0.333, 0, 0, 0.667, 21, 0, 1.333, -21, 0, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param38", "Segments": [0, 0, 2, 0.333, 0, 0, 1, -11, 0, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param40", "Segments": [0, 0, 2, 0.333, 0, 0, 0.667, 15.08, 0, 1.333, -15.08, 0, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param41", "Segments": [0, 0, 2, 0.333, 0, 0, 1, 14, 0, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param42", "Segments": [0, 0, 2, 0.333, 0, 0, 0.667, -13, 0, 1.333, 13, 0, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param43", "Segments": [0, 0, 2, 0.333, 0, 0, 1, -21, 0, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param44", "Segments": [0, 0, 2, 0.333, 0, 0, 0.667, 14, 0, 1.333, -14, 0, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param45", "Segments": [0, 0, 2, 0.333, 0, 0, 1, -10, 0, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param46", "Segments": [0, 0, 2, 0.333, 0, 0, 0.667, 13, 0, 1.333, -13, 0, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param62", "Segments": [0, 0, 2, 0.333, 0, 0, 1, 18.58, 0, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param63", "Segments": [0, 0, 2, 0.333, 0, 0, 0.667, -14.7, 0, 1.333, 14.7, 0, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param64", "Segments": [0, 0, 2, 0.333, 0, 0, 1, 18.796, 0, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param65", "Segments": [0, 0, 2, 0.333, 0, 0, 0.667, -9, 0, 1.333, 9, 0, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param72", "Segments": [0, 0, 2, 0.333, 0, 0, 0.667, 18.78, 0, 1.333, -18.78, 0, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param73", "Segments": [0, 0, 2, 0.333, 0, 0, 1, 7.08, 0, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param74", "Segments": [0, 0, 2, 0.333, 0, 0, 0.667, 14, 0, 1.333, -14, 0, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param75", "Segments": [0, 0, 2, 0.333, 0, 0, 1, 14, 0, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param53", "Segments": [0, 0, 2, 0.333, 0, 0, 1, -20.44, 0, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param54", "Segments": [0, 0, 2, 0.333, 0, 0, 0.667, 17.063, 0, 1.333, -18.549, 0, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param81", "Segments": [0, 0, 2, 0.333, 0, 0, 1, 8, 0, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param82", "Segments": [0, 0, 2, 0.333, 0, 0, 0.667, 20, 0, 1.333, -20, 0, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param70", "Segments": [0, 0, 2, 0.333, 0, 0, 1, 11, 0, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param71", "Segments": [0, 0, 2, 0.333, 0, 0, 0.667, -18, 0, 1.333, 18, 0, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param77", "Segments": [0, 0, 2, 0.333, 0, 0, 1, -10, 0, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param78", "Segments": [0, 0, 2, 0.333, 0, 0, 0.667, 15.931, 0, 1.333, -16.089, 0, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param79", "Segments": [0, 0, 2, 0.333, 0, 0, 1, -17, 0, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param80", "Segments": [0, 0, 2, 0.333, 0, 0, 0.667, 10, 0, 1.333, -10, 0, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param294", "Segments": [0, 1, 2, 0.333, 1, 0, 0.4, 0, 2, 11.9, 0, 0, 12, 1]}, {"Target": "Parameter", "Id": "Param47", "Segments": [0, 0, 2, 0.333, 0, 0, 1, 15, 0, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param48", "Segments": [0, 0, 2, 0.333, 0, 0, 0.667, -22, 0, 1.333, 22, 0, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param56", "Segments": [0, 0, 2, 0.333, 0, 0, 1, 9, 0, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param57", "Segments": [0, 0, 2, 0.333, 0, 0, 0.667, -9, 0, 1.333, 9, 0, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param58", "Segments": [0, 0, 2, 0.333, 0, 0, 1, 11, 0, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param59", "Segments": [0, 0, 2, 0.333, 0, 0, 1, -30, 0, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param60", "Segments": [0, 0, 2, 0.333, 0, 0, 0.667, 11, 0, 1.333, -11, 0, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param61", "Segments": [0, 0, 2, 0.333, 0, 0, 1, -9, 0, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param49", "Segments": [0, 0, 2, 0.333, 0, 0, 1, -11, 0, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param50", "Segments": [0, 0, 2, 0.333, 0, 0, 0.667, -12, 0, 1.333, 12, 0, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param51", "Segments": [0, 0, 2, 0.333, 0, 0, 1, -8, 0, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param52", "Segments": [0, 0, 2, 0.333, 0, 0, 0.667, -14, 0, 1.333, 14, 0, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation63", "Segments": [0, -0.675, 2, 0.333, -0.675, 0, 0.533, -1.187, 0, 1.2, 1.187, 2, 1.667, 1.187, 2, 2.133, 1.187, 2, 2.467, 1.187, 2, 2.933, 1.187, 2, 3.433, 1.187, 2, 4.133, 1.187, 2, 4.667, 1.187, 2, 5.4, 1.187, 2, 5.867, 1.187, 2, 7.4, 1.187, 2, 7.967, 1.187, 2, 9.233, 1.187, 2, 10.067, 1.187, 2, 10.533, 1.187, 2, 11.067, 1.187, 0, 12, -0.675]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation64", "Segments": [0, 2.951, 2, 0.333, 2.951, 0, 0.867, -3.89, 0, 1.533, 3.747, 2, 1.667, 3.747, 2, 2.133, 3.747, 2, 2.467, 3.747, 2, 2.933, 3.747, 2, 3.433, 3.747, 2, 4.133, 3.747, 2, 4.667, 3.747, 2, 5.4, 3.747, 2, 5.867, 3.747, 2, 7.4, 3.747, 2, 7.967, 3.747, 2, 9.233, 3.747, 2, 10.067, 3.747, 2, 10.533, 3.747, 2, 11.067, 3.747, 0, 12, 2.951]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation65", "Segments": [0, 4.849, 2, 0.333, 4.849, 0, 0.567, 9.374, 0, 1.233, -8.836, 2, 1.667, -8.836, 2, 2.133, -8.836, 2, 2.467, -8.836, 2, 2.933, -8.836, 2, 3.433, -8.836, 2, 4.133, -8.836, 2, 4.667, -8.836, 2, 5.4, -8.836, 2, 5.867, -8.836, 2, 7.4, -8.836, 2, 7.967, -8.836, 2, 9.233, -8.836, 2, 10.067, -8.836, 2, 10.533, -8.836, 2, 11.067, -8.836, 0, 12, 4.849]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation66", "Segments": [0, -11.659, 2, 0.333, -11.659, 0, 0.867, 13.837, 0, 1.533, -14.624, 2, 1.667, -14.624, 2, 2.133, -14.624, 2, 2.467, -14.624, 2, 2.933, -14.624, 2, 3.433, -14.624, 2, 4.133, -14.624, 2, 4.667, -14.624, 2, 5.4, -14.624, 2, 5.867, -14.624, 2, 7.4, -14.624, 2, 7.967, -14.624, 2, 9.233, -14.624, 2, 10.067, -14.624, 2, 10.533, -14.624, 2, 11.067, -14.624, 0, 12, -11.659]}, {"Target": "Parameter", "Id": "Param55", "Segments": [0, -10.57, 2, 0.333, -10.57, 0, 0.567, -20.383, 0, 1.233, 19.111, 2, 1.667, 19.111, 2, 2.133, 19.111, 2, 2.467, 19.111, 2, 2.933, 19.111, 2, 3.433, 19.111, 2, 4.133, 19.111, 2, 4.667, 19.111, 2, 5.4, 19.111, 2, 5.867, 19.111, 2, 7.4, 19.111, 2, 7.967, 19.111, 2, 9.233, 19.111, 2, 10.067, 19.111, 2, 10.533, 19.111, 2, 11.067, 19.111, 0, 12, -10.57]}, {"Target": "Parameter", "Id": "Param28", "Segments": [0, -7.12, 2, 0.333, -7.12, 0, 1, 7.36, 0, 1.667, -6.322, 0, 2.333, 3.205, 0, 3, -6.322, 0, 3.667, 3.205, 0, 4.333, -6.322, 0, 5, 3.205, 0, 5.667, -6.322, 0, 6.333, 3.205, 0, 7, -6.322, 0, 7.667, 3.205, 0, 8.333, -6.322, 0, 9, 3.205, 0, 9.667, -6.322, 0, 10.667, 3.205, 0, 12, -7.12]}, {"Target": "Parameter", "Id": "Param29", "Segments": [0, -1.278, 2, 0.333, -1.278, 0, 0.6, -4.74, 0, 1.267, 5.1, 1, 1.4, 5.1, 1.534, 0.858, 1.667, -2.478, 1, 1.756, -4.702, 1.844, -4.756, 1.933, -4.756, 0, 2.6, 1.718, 1, 2.733, 1.718, 2.867, 0.083, 3, -2.478, 1, 3.089, -4.185, 3.178, -4.756, 3.267, -4.756, 0, 3.933, 1.718, 1, 4.066, 1.718, 4.2, 0.083, 4.333, -2.478, 1, 4.422, -4.185, 4.511, -4.756, 4.6, -4.756, 0, 5.267, 1.718, 1, 5.4, 1.718, 5.534, 0.083, 5.667, -2.478, 1, 5.756, -4.185, 5.844, -4.756, 5.933, -4.756, 0, 6.6, 1.718, 1, 6.733, 1.718, 6.867, 0.083, 7, -2.478, 1, 7.089, -4.185, 7.178, -4.756, 7.267, -4.756, 0, 7.933, 1.718, 1, 8.066, 1.718, 8.2, 0.083, 8.333, -2.478, 1, 8.422, -4.185, 8.511, -4.756, 8.6, -4.756, 0, 9.267, 1.718, 1, 9.4, 1.718, 9.534, -0.762, 9.667, -2.478, 1, 9.822, -4.48, 9.978, -4.756, 10.133, -4.756, 0, 11.067, 5.1, 0, 12, -1.278]}, {"Target": "Parameter", "Id": "Param30", "Segments": [0, -5.909, 2, 0.333, -5.909, 0, 0.933, 2.533, 0, 1.6, -5.774, 1, 1.622, -5.774, 1.645, -5.881, 1.667, -5.524, 1, 1.867, -2.317, 2.067, 0.029, 2.267, 0.029, 0, 2.933, -5.774, 1, 2.955, -5.774, 2.978, -5.881, 3, -5.524, 1, 3.2, -2.317, 3.4, 0.029, 3.6, 0.029, 0, 4.267, -5.774, 1, 4.289, -5.774, 4.311, -5.881, 4.333, -5.524, 1, 4.533, -2.317, 4.733, 0.029, 4.933, 0.029, 0, 5.6, -5.774, 1, 5.622, -5.774, 5.645, -5.881, 5.667, -5.524, 1, 5.867, -2.317, 6.067, 0.029, 6.267, 0.029, 0, 6.933, -5.774, 1, 6.955, -5.774, 6.978, -5.881, 7, -5.524, 1, 7.2, -2.317, 7.4, 0.029, 7.6, 0.029, 0, 8.267, -5.774, 1, 8.289, -5.774, 8.311, -5.881, 8.333, -5.524, 1, 8.533, -2.317, 8.733, 0.029, 8.933, 0.029, 0, 9.6, -5.774, 1, 9.622, -5.774, 9.645, -5.779, 9.667, -5.524, 1, 9.978, -1.964, 10.289, 0.029, 10.6, 0.029, 0, 11.6, -6.287, 0, 12, -5.909]}, {"Target": "Parameter", "Id": "Param31", "Segments": [0, -0.551, 2, 0.333, -0.551, 0, 0.633, 3.018, 0, 1.3, -6.18, 1, 1.422, -6.18, 1.545, -4.305, 1.667, -2, 1, 1.767, -0.114, 1.867, 0.349, 1.967, 0.349, 0, 2.633, -5.703, 1, 2.755, -5.703, 2.878, -4.181, 3, -2, 1, 3.1, -0.215, 3.2, 0.349, 3.3, 0.349, 0, 3.967, -5.703, 1, 4.089, -5.703, 4.211, -4.181, 4.333, -2, 1, 4.433, -0.215, 4.533, 0.349, 4.633, 0.349, 0, 5.3, -5.703, 1, 5.422, -5.703, 5.545, -4.181, 5.667, -2, 1, 5.767, -0.215, 5.867, 0.349, 5.967, 0.349, 0, 6.633, -5.703, 1, 6.755, -5.703, 6.878, -4.181, 7, -2, 1, 7.1, -0.215, 7.2, 0.349, 7.3, 0.349, 0, 7.967, -5.703, 1, 8.089, -5.703, 8.211, -4.181, 8.333, -2, 1, 8.433, -0.215, 8.533, 0.349, 8.633, 0.349, 0, 9.3, -5.703, 1, 9.422, -5.703, 9.545, -3.474, 9.667, -2, 1, 9.834, 0.011, 10, 0.349, 10.167, 0.349, 0, 11.1, -6.18, 0, 12, -0.551]}, {"Target": "Parameter", "Id": "Param32", "Segments": [0, -2.714, 2, 0.333, -2.714, 0, 0.967, 2.284, 0, 1.633, -3.477, 1, 1.644, -3.477, 1.656, -3.526, 1.667, -3.423, 1, 1.878, -1.453, 2.089, -0.134, 2.3, -0.134, 0, 2.967, -3.477, 1, 2.978, -3.477, 2.989, -3.526, 3, -3.423, 1, 3.211, -1.453, 3.422, -0.134, 3.633, -0.134, 0, 4.3, -3.477, 1, 4.311, -3.477, 4.322, -3.526, 4.333, -3.423, 1, 4.544, -1.453, 4.756, -0.134, 4.967, -0.134, 0, 5.633, -3.477, 1, 5.644, -3.477, 5.656, -3.526, 5.667, -3.423, 1, 5.878, -1.453, 6.089, -0.134, 6.3, -0.134, 0, 6.967, -3.477, 1, 6.978, -3.477, 6.989, -3.526, 7, -3.423, 1, 7.211, -1.453, 7.422, -0.134, 7.633, -0.134, 0, 8.3, -3.477, 1, 8.311, -3.477, 8.322, -3.526, 8.333, -3.423, 1, 8.544, -1.453, 8.756, -0.134, 8.967, -0.134, 0, 9.633, -3.477, 1, 9.644, -3.477, 9.656, -3.495, 9.667, -3.423, 1, 9.989, -1.309, 10.311, -0.134, 10.633, -0.134, 0, 11.633, -2.796, 0, 12, -2.714]}, {"Target": "Parameter", "Id": "Param33", "Segments": [0, -2.392, 2, 0.333, -2.392, 0, 0.633, 3.279, 0, 1.3, -10.047, 1, 1.422, -10.047, 1.545, -6.912, 1.667, -3.211, 1, 1.767, -0.183, 1.867, 0.52, 1.967, 0.52, 0, 2.633, -8.247, 1, 2.755, -8.247, 2.878, -6.417, 3, -3.211, 1, 3.1, -0.588, 3.2, 0.52, 3.3, 0.52, 0, 3.967, -8.247, 1, 4.089, -8.247, 4.211, -6.417, 4.333, -3.211, 1, 4.433, -0.588, 4.533, 0.52, 4.633, 0.52, 0, 5.3, -8.247, 1, 5.422, -8.247, 5.545, -6.417, 5.667, -3.211, 1, 5.767, -0.588, 5.867, 0.52, 5.967, 0.52, 0, 6.633, -8.247, 1, 6.755, -8.247, 6.878, -6.417, 7, -3.211, 1, 7.1, -0.588, 7.2, 0.52, 7.3, 0.52, 0, 7.967, -8.247, 1, 8.089, -8.247, 8.211, -6.417, 8.333, -3.211, 1, 8.433, -0.588, 8.533, 0.52, 8.633, 0.52, 0, 9.3, -8.247, 1, 9.422, -8.247, 9.545, -5.455, 9.667, -3.211, 1, 9.834, -0.15, 10, 0.52, 10.167, 0.52, 0, 11.1, -10.047, 0, 12, -2.392]}, {"Target": "Parameter", "Id": "Param34", "Segments": [0, -21.182, 2, 0.333, -21.182, 0, 0.967, 11.7, 0, 1.633, -15.729, 1, 1.644, -15.729, 1.656, -16.109, 1.667, -15.572, 1, 1.878, -5.385, 2.089, 6.06, 2.3, 6.06, 0, 2.967, -15.729, 1, 2.978, -15.729, 2.989, -16.109, 3, -15.572, 1, 3.211, -5.385, 3.422, 6.06, 3.633, 6.06, 0, 4.3, -15.729, 1, 4.311, -15.729, 4.322, -16.109, 4.333, -15.572, 1, 4.544, -5.385, 4.756, 6.06, 4.967, 6.06, 0, 5.633, -15.729, 1, 5.644, -15.729, 5.656, -16.109, 5.667, -15.572, 1, 5.878, -5.385, 6.089, 6.06, 6.3, 6.06, 0, 6.967, -15.729, 1, 6.978, -15.729, 6.989, -16.109, 7, -15.572, 1, 7.211, -5.385, 7.422, 6.06, 7.633, 6.06, 0, 8.3, -15.729, 1, 8.311, -15.729, 8.322, -16.109, 8.333, -15.572, 1, 8.544, -5.385, 8.756, 6.06, 8.967, 6.06, 0, 9.633, -15.729, 1, 9.644, -15.729, 9.656, -16.004, 9.667, -15.572, 1, 9.989, -3.071, 10.311, 6.06, 10.633, 6.06, 0, 11.633, -21.42, 0, 12, -21.182]}, {"Target": "Parameter", "Id": "Param84", "Segments": [0, 0, 2, 0.333, 0, 0, 1, -6.9, 0, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param85", "Segments": [0, 0, 2, 0.333, 0, 0, 0.667, 4.74, 0, 1.333, -4.74, 0, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param83", "Segments": [0, 0, 2, 0.333, 0, 0, 1, 3.96, 0, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param86", "Segments": [0, 1, 2, 0.333, 1, 2, 1.3, 1, 0, 1.333, 0, 0, 1.4, 1, 2, 1.667, 1, 2, 2.133, 1, 2, 2.467, 1, 2, 2.933, 1, 2, 3.433, 1, 2, 4.133, 1, 2, 4.667, 1, 2, 5.4, 1, 2, 5.867, 1, 2, 7.4, 1, 2, 7.967, 1, 2, 9.233, 1, 2, 10.067, 1, 2, 10.533, 1, 2, 11.067, 1, 2, 12, 1]}, {"Target": "Parameter", "Id": "Param95", "Segments": [0, 0, 2, 0.333, 0, 0, 1, 10, 0, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param92", "Segments": [0, 0, 2, 0.333, 0, 0, 0.667, -12, 0, 1.333, 12, 0, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param93", "Segments": [0, 0, 2, 0.333, 0, 0, 1, -12, 0, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param96", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, -16, 0, 0.5, 0, 0, 0.6, -16, 0, 0.7, 0, 2, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param97", "Segments": [0, 0, 2, 0.333, 0, 0, 0.367, 14, 0, 0.467, -14, 1, 0.478, -14, 0.489, -5.584, 0.5, 0, 1, 0.522, 11.168, 0.545, 14, 0.567, 14, 0, 0.667, -14, 0, 0.7, 0, 2, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param89", "Segments": [0, 0, 2, 0.333, 0, 0, 1, 18.72, 0, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param90", "Segments": [0, 0, 2, 0.333, 0, 0, 0.667, -19, 0, 1.333, 19, 0, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation56", "Segments": [0, 0, 2, 0.333, 0, 0, 0.667, 9, 0, 1, -9, 0, 1.333, 9, 0, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param98", "Segments": [0, -6, 2, 0.333, -6, 0, 0.5, 0, 0, 0.833, -12, 0, 1.167, 12, 0, 1.5, -12, 2, 1.667, -12, 2, 2.133, -12, 2, 2.467, -12, 2, 2.933, -12, 2, 3.433, -12, 2, 4.133, -12, 2, 4.667, -12, 2, 5.4, -12, 2, 5.867, -12, 2, 7.4, -12, 2, 7.967, -12, 2, 9.233, -12, 2, 10.067, -12, 2, 10.533, -12, 2, 11.067, -12, 0, 12, -6]}, {"Target": "Parameter", "Id": "Param99", "Segments": [0, -10.921, 2, 0.333, -10.921, 0, 0.667, 0, 0, 1, -11, 0, 1.333, 11, 0, 1.667, -11, 2, 2.133, -11, 2, 2.467, -11, 2, 2.933, -11, 2, 3.433, -11, 2, 4.133, -11, 2, 4.667, -11, 2, 5.4, -11, 2, 5.867, -11, 2, 7.4, -11, 2, 7.967, -11, 2, 9.233, -11, 2, 10.067, -11, 2, 10.533, -11, 2, 11.067, -11, 0, 12, -10.921]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation67", "Segments": [0, -10.939, 2, 0.333, -10.939, 0, 0.467, -25, 0, 0.8, 0, 0, 1.133, -25, 0, 1.467, 25, 2, 1.667, 25, 2, 2.133, 25, 2, 2.467, 25, 2, 2.933, 25, 2, 3.433, 25, 2, 4.133, 25, 2, 4.667, 25, 2, 5.4, 25, 2, 5.867, 25, 2, 7.4, 25, 2, 7.967, 25, 2, 9.233, 25, 2, 10.067, 25, 2, 10.533, 25, 2, 11.067, 25, 0, 12, -10.939]}, {"Target": "Parameter", "Id": "Param281", "Segments": [0, 0, 2, 0.333, 0, 0, 1, 1, 0, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param197mao2", "Segments": [0, 0, 2, 0.333, 0, 0, 0.667, 2.894, 0, 1.367, -2.454, 0, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param212mao2", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, -16, 0, 0.5, 12.568, 0, 0.6, -16, 0, 0.7, 0, 2, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param214mao2", "Segments": [0, 0, 2, 0.333, 0, 2, 0.933, 0, 0, 1.033, 16, 0, 1.1, 0, 0, 1.2, 16, 0, 1.3, 0, 2, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param215mao2", "Segments": [0, 0, 2, 0.333, 0, 2, 0.933, 0, 0, 0.967, -14, 0, 1.067, 15, 0, 1.167, -8, 0, 1.233, 9, 0, 1.367, 0, 2, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param198mao2", "Segments": [0, 0, 2, 0.333, 0, 0, 0.967, 30, 0, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation143mao2", "Segments": [0, 0, 2, 0.333, 0, 0, 0.633, 17, 0, 1, 0, 0, 1.367, 17, 2, 1.667, 17, 2, 2.133, 17, 2, 2.467, 17, 2, 2.933, 17, 2, 3.433, 17, 2, 4.133, 17, 2, 4.667, 17, 2, 5.4, 17, 2, 5.867, 17, 2, 7.4, 17, 2, 7.967, 17, 2, 9.233, 17, 2, 10.067, 17, 2, 10.533, 17, 2, 11.067, 17, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation144mao2", "Segments": [0, -10.82, 2, 0.333, -10.82, 0, 0.433, -19.438, 0, 0.733, 17.12, 0, 1.1, -19.438, 0, 1.467, 17.12, 0, 1.667, -10.82, 2, 2.133, -10.82, 2, 2.467, -10.82, 2, 2.933, -10.82, 2, 3.433, -10.82, 2, 4.133, -10.82, 2, 4.667, -10.82, 2, 5.4, -10.82, 2, 5.867, -10.82, 2, 7.4, -10.82, 2, 7.967, -10.82, 2, 9.233, -10.82, 2, 10.067, -10.82, 2, 10.533, -10.82, 2, 11.067, -10.82, 2, 12, -10.82]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation145mao2", "Segments": [0, 8.46, 2, 0.333, 8.46, 0, 0.567, -17.322, 0, 0.867, 16.411, 0, 1.233, -17.322, 0, 1.567, 16.411, 0, 1.667, 8.46, 2, 2.133, 8.46, 2, 2.467, 8.46, 2, 2.933, 8.46, 2, 3.433, 8.46, 2, 4.133, 8.46, 2, 4.667, 8.46, 2, 5.4, 8.46, 2, 5.867, 8.46, 2, 7.4, 8.46, 2, 7.967, 8.46, 2, 9.233, 8.46, 2, 10.067, 8.46, 2, 10.533, 8.46, 2, 11.067, 8.46, 2, 12, 8.46]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation146mao2", "Segments": [0, 28.83, 2, 0.333, 28.83, 0, 0.367, 29.224, 0, 0.667, -30, 0, 0.967, 29.224, 0, 1.333, -30, 0, 1.667, 28.83, 2, 2.133, 28.83, 2, 2.467, 28.83, 2, 2.933, 28.83, 2, 3.433, 28.83, 2, 4.133, 28.83, 2, 4.667, 28.83, 2, 5.4, 28.83, 2, 5.867, 28.83, 2, 7.4, 28.83, 2, 7.967, 28.83, 2, 9.233, 28.83, 2, 10.067, 28.83, 2, 10.533, 28.83, 2, 11.067, 28.83, 2, 12, 28.83]}, {"Target": "Parameter", "Id": "Param100", "Segments": [0, 0, 2, 0.333, 0, 0, 1, -2.04, 0, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param105", "Segments": [0, 0, 2, 0.333, 0, 0, 0.667, 16, 0, 1.333, -16, 0, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param108", "Segments": [0, 0, 2, 0.333, 0, 0, 1, 4, 0, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param109", "Segments": [0, 0, 2, 0.333, 0, 0, 0.667, -4, 0, 1.333, 4.46, 0, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param101", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, -21, 0, 0.467, 0, 1, 0.478, 0, 0.489, -21, 0.5, -21, 0, 0.567, 0, 2, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param106", "Segments": [0, 0, 2, 0.333, 0, 0, 1, 15.405, 0, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param104", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, -16, 0, 0.467, 0, 0, 0.5, -16, 0, 0.567, 0, 2, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param103", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, 27, 0, 0.467, 0, 1, 0.478, 0, 0.489, 27, 0.5, 27, 0, 0.567, 0, 2, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param102", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, 15, 0, 0.467, 0, 0, 0.5, 15, 0, 0.567, 0, 2, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation71", "Segments": [0, 0, 2, 0.333, 0, 0, 0.667, 18, 0, 1, -18, 0, 1.333, 18, 0, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation72", "Segments": [0, 8.085, 2, 0.333, 8.085, 0, 0.5, 0, 0, 0.833, 19, 0, 1.167, -19, 0, 1.5, 19, 2, 1.667, 19, 2, 2.133, 19, 2, 2.467, 19, 2, 2.933, 19, 2, 3.433, 19, 2, 4.133, 19, 2, 4.667, 19, 2, 5.4, 19, 2, 5.867, 19, 2, 7.4, 19, 2, 7.967, 19, 2, 9.233, 19, 2, 10.067, 19, 2, 10.533, 19, 2, 11.067, 19, 0, 12, 8.085]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation73", "Segments": [0, 11.915, 2, 0.333, 11.915, 0, 0.5, 0, 0, 0.833, 28, 0, 1.167, -28, 0, 1.5, 28, 2, 1.667, 28, 2, 2.133, 28, 2, 2.467, 28, 2, 2.933, 28, 2, 3.433, 28, 2, 4.133, 28, 2, 4.667, 28, 2, 5.4, 28, 2, 5.867, 28, 2, 7.4, 28, 2, 7.967, 28, 2, 9.233, 28, 2, 10.067, 28, 2, 10.533, 28, 2, 11.067, 28, 0, 12, 11.915]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation74", "Segments": [0, 12.341, 2, 0.333, 12.341, 0, 0.5, 0, 0, 0.833, 29, 0, 1.167, -29, 0, 1.5, 29, 2, 1.667, 29, 2, 2.133, 29, 2, 2.467, 29, 2, 2.933, 29, 2, 3.433, 29, 2, 4.133, 29, 2, 4.667, 29, 2, 5.4, 29, 2, 5.867, 29, 2, 7.4, 29, 2, 7.967, 29, 2, 9.233, 29, 2, 10.067, 29, 2, 10.533, 29, 2, 11.067, 29, 0, 12, 12.341]}, {"Target": "Parameter", "Id": "Param111", "Segments": [0, 0, 2, 0.333, 0, 0, 1, 6.54, 0, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param114", "Segments": [0, 0, 2, 0.333, 0, 0, 0.667, 19, 0, 1.333, -19, 0, 1.667, 0, 2, 2.133, 0, 2, 2.467, 0, 2, 2.933, 0, 2, 3.433, 0, 2, 4.133, 0, 2, 4.667, 0, 2, 5.4, 0, 2, 5.867, 0, 2, 7.4, 0, 2, 7.967, 0, 2, 9.233, 0, 2, 10.067, 0, 2, 10.533, 0, 2, 11.067, 0, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param113", "Segments": [0, 1, 2, 0.333, 1, 2, 0.967, 1, 0, 1, 0, 0, 1.067, 1, 2, 1.667, 1, 2, 2.133, 1, 2, 2.467, 1, 2, 2.933, 1, 2, 3.433, 1, 2, 4.133, 1, 2, 4.667, 1, 2, 5.4, 1, 2, 5.867, 1, 2, 7.4, 1, 2, 7.967, 1, 2, 9.233, 1, 2, 10.067, 1, 2, 10.533, 1, 2, 11.067, 1, 2, 12, 1]}, {"Target": "Parameter", "Id": "Param76", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param293", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, -1, 0, 12, -1]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, -1, 0, 12, -1]}, {"Target": "Parameter", "Id": "ParamCheek", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param269", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param193", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param192", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param196", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param282", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param183", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param194", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param195", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param184", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param289", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param185", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param290", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param291", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param186", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param187", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param188", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param292", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param189", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param190", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param191", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param8", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param23", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param24", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param25", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param26", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param27", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param200", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param197", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param198", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param208", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param279", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param212", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param270", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param271", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param20", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param275", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param227", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param226", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param232", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param274", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param276", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param142", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param143", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param144", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param147", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param145", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param134", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param246", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param141", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param182", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param247", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param229", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param245", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param117", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param140", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param262", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param263", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param115", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param116", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param260", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param261", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param133", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param139", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param257", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param137", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param138", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param135", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param176", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param136", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param177", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param251", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param280", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param35", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param295", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param296", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param297", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param299", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param298", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param300", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param301", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param302", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param303", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param304", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param305", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param306", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param307", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param308", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param309", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param310", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param311", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param312", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param313", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param314", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param315", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param316", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param317", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param318", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param319", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param321", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param320", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param322", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param324", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param323", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param325", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param326", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param327", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param328", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param91", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param2", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param87", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param88", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param94", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param218mao2", "Segments": [0, 1, 0, 12, 1]}, {"Target": "Parameter", "Id": "Param211mao2", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param216mao2", "Segments": [0, -8.88, 0, 12, -8.88]}, {"Target": "Parameter", "Id": "Param217mao2", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param202mao2", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param203mao2", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param204mao2", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param199mao2", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param200mao2", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param201mao2", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param208mao2", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param209mao2", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param210mao2", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param205mao2", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param206mao2", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param207mao2", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param213mao2", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param219mao2", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param264", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param107", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param284", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param148", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param146", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param110", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param248", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param112", "Segments": [0, -30, 0, 12, -30]}, {"Target": "Parameter", "Id": "Param249", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param206", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param253", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param254", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param158", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param149", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param150", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param151", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param255", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param256", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param153", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param152", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param155", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param154", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param181", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param180", "Segments": [0, 0.5, 0, 12, 0.5]}, {"Target": "Parameter", "Id": "Param156", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param157", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param159", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param160", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param119", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param265", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param118", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param120", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param121", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param161", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param259", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param123", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param124", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param130", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param131", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param178", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param179", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param125", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param126", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param127", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param128", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param171", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param173", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param172", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param122", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param170", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param129", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param132", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param250", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param258", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param162", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param169", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param174", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param175", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param163", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param164", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param165", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param166", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param167", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param168", "Segments": [0, 0, 0, 12, 0]}], "UserData": [{"Time": 0.0, "Value": ""}, {"Time": 1.333, "Value": ""}, {"Time": 11.5, "Value": ""}]}