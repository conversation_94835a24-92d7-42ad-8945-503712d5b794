{"Version": 3, "Meta": {"Duration": 12.033, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 355, "TotalSegmentCount": 4484, "TotalPointCount": 4998, "UserDataCount": 3, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 2, 0.333, 0, 1, 0.366, 0, 0.4, 0.145, 0.433, -2.069, 1, 0.522, -7.974, 0.611, -14, 0.7, -14, 0, 1.2, 11, 0, 1.667, -1, 0, 2.267, 9, 0, 2.9, 1.292, 0, 3.6, 4.197, 0, 3.8, 2.809, 0, 4.1, 4.988, 2, 5.1, 4.988, 2, 5.333, 4.988, 0, 5.633, 2.076, 1, 5.722, 2.076, 5.811, 2.031, 5.9, 2.681, 1, 5.989, 3.331, 6.078, 5.349, 6.167, 5.349, 0, 6.4, -0.089, 0, 6.6, 5.349, 0, 6.9, -0.089, 2, 7.067, -0.089, 0, 7.2, 1.231, 0, 7.933, -0.087, 2, 9.867, -0.087, 2, 10.7, -0.087, 0, 11.067, -1.269, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 2, 0.333, 0, 1, 0.366, 0, 0.4, -7.22, 0.433, -8.392, 1, 0.533, -11.909, 0.633, -12.733, 0.733, -12.733, 0, 1.267, 1.405, 0, 1.667, -5.676, 0, 2.333, 0, 0, 3.1, -3.961, 0, 3.5, -1.174, 1, 3.589, -1.174, 3.678, -1.253, 3.767, -3.274, 1, 3.834, -4.79, 3.9, -10.796, 3.967, -10.796, 0, 4.2, 5, 0, 4.8, -3.18, 1, 4.9, -3.18, 5, 10.874, 5.1, 12, 1, 5.178, 12.875, 5.255, 12.66, 5.333, 12.66, 0, 5.633, -12.184, 0, 5.9, 8.304, 0, 6.4, 5.362, 0, 6.567, 8.629, 0, 6.9, 4.105, 0, 7.2, 6.483, 0, 7.933, -7.778, 2, 9.867, -7.778, 2, 10.7, -7.778, 0, 11.067, -11.16, 0, 11.6, 5.555, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, -0.23, 1, 0.544, -0.23, 0.656, -0.363, 0.767, 0, 1, 0.945, 0.581, 1.122, 7.143, 1.3, 7.143, 0, 1.833, 0, 0, 2.267, 6.17, 0, 2.9, 2.68, 0, 3.6, 9.48, 0, 3.8, 7.143, 0, 4.1, 7.901, 2, 5.1, 7.901, 2, 5.333, 7.901, 0, 5.633, 5.764, 0, 5.9, 6.678, 0, 6.9, 6.17, 2, 7.067, 6.17, 0, 7.2, 7.13, 0, 7.933, 5.193, 0, 8.433, 6.998, 0, 9.4, 4.23, 0, 10, 7.489, 0, 10.7, 2.901, 0, 11.067, 6.325, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 2, 0.333, 1, 0, 0.433, 0, 2, 0.9, 0, 2, 2.167, 0, 2, 2.633, 0, 2, 3.5, 0, 2, 3.767, 0, 0, 3.9, 1, 2, 4, 1, 2, 5, 1, 2, 5.233, 1, 0, 5.533, 0, 0, 5.8, 1, 2, 6.8, 1, 2, 6.967, 1, 2, 7.2, 1, 0, 7.267, 0, 0, 7.367, 0.6, 2, 7.833, 0.6, 2, 8.8, 0.6, 0, 8.867, 0, 0, 8.967, 0.6, 2, 9.767, 0.6, 2, 10.7, 0.6, 2, 10.967, 0.6, 0, 11.067, 0, 0, 11.2, 1, 2, 12.033, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 2, 0.333, 0, 1, 0.366, 0, 0.4, 0.161, 0.433, 0.235, 1, 0.589, 0.58, 0.744, 0.7, 0.9, 0.7, 2, 2.167, 0.7, 2, 2.633, 0.7, 2, 3.5, 0.7, 0, 3.767, 0, 2, 3.9, 0, 2, 4, 0, 2, 5, 0, 2, 5.233, 0, 2, 5.8, 0, 2, 6.8, 0, 2, 6.967, 0, 2, 7.2, 0, 2, 7.367, 0, 2, 7.833, 0, 2, 8.8, 0, 2, 8.967, 0, 2, 9.767, 0, 2, 10.7, 0, 2, 10.967, 0, 2, 12.033, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 2, 0.333, 1, 0, 0.433, 0, 2, 0.9, 0, 2, 2.167, 0, 2, 2.633, 0, 2, 3.5, 0, 2, 3.767, 0, 0, 3.9, 1, 2, 4, 1, 2, 5, 1, 2, 5.233, 1, 0, 5.533, 0, 0, 5.8, 1, 2, 6.8, 1, 2, 6.967, 1, 2, 7.2, 1, 0, 7.267, 0, 0, 7.367, 0.6, 2, 7.833, 0.6, 2, 8.8, 0.6, 0, 8.867, 0, 0, 8.967, 0.6, 2, 9.767, 0.6, 2, 10.7, 0.6, 2, 10.967, 0.6, 0, 11.067, 0, 0, 11.2, 1, 2, 12.033, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 2, 0.333, 0, 1, 0.366, 0, 0.4, 0.161, 0.433, 0.235, 1, 0.589, 0.58, 0.744, 0.7, 0.9, 0.7, 2, 2.167, 0.7, 2, 2.633, 0.7, 2, 3.5, 0.7, 0, 3.767, 0, 2, 3.9, 0, 2, 4, 0, 2, 5, 0, 2, 5.233, 0, 2, 5.8, 0, 2, 6.8, 0, 2, 6.967, 0, 2, 7.2, 0, 2, 7.367, 0, 2, 7.833, 0, 2, 9.767, 0, 2, 10.7, 0, 2, 12.033, 0]}, {"Target": "Parameter", "Id": "Param76", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.9, 0, 2, 2.167, 0, 2, 2.633, 0, 2, 3.5, 0, 2, 3.7, 0, 2, 4, 0, 2, 4.2, 0, 0, 5, -7.457, 2, 5.233, -7.457, 0, 5.8, -29.779, 0, 6.8, -2.748, 2, 6.967, -2.748, 2, 7.267, -2.748, 0, 7.3, -2.282, 2, 7.833, -2.282, 2, 9.767, -2.282, 2, 10.7, -2.282, 0, 11.2, 0, 2, 12.033, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.9, 0, 2, 2.167, 0, 2, 2.633, 0, 2, 3.5, 0, 2, 3.7, 0, 2, 4, 0, 2, 5, 0, 2, 5.233, 0, 2, 5.8, 0, 2, 6.8, 0, 2, 6.967, 0, 2, 7.267, 0, 0, 7.3, -0.7, 2, 7.833, -0.7, 2, 9.767, -0.7, 2, 10.7, -0.7, 0, 11.2, 0, 2, 12.033, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 2, 0.333, 0, 1, 0.366, 0, 0.4, -0.016, 0.433, -0.023, 1, 0.589, -0.056, 0.744, -0.068, 0.9, -0.068, 2, 2.167, -0.068, 2, 2.633, -0.068, 2, 3.5, -0.068, 2, 3.7, -0.068, 0, 4, -0.056, 2, 5, -0.056, 2, 5.233, -0.056, 0, 5.8, 0.064, 2, 6.8, 0.064, 2, 6.967, 0.064, 0, 7.833, 0.063, 2, 9.767, 0.063, 2, 10.7, 0.063, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.9, 0, 2, 2.167, 0, 2, 2.633, 0, 2, 3.5, 0, 2, 3.7, 0, 0, 3.767, -0.5, 0, 4.133, 0.1, 0, 5, -0.09, 2, 5.233, -0.09, 0, 5.8, -0.344, 2, 6.8, -0.344, 2, 6.967, -0.344, 0, 7.833, -0.338, 2, 9.767, -0.338, 2, 10.7, -0.338, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 2, 0.333, 0, 1, 0.366, 0, 0.4, -0.034, 0.433, -0.049, 1, 0.589, -0.121, 0.744, -0.146, 0.9, -0.146, 2, 2.167, -0.146, 2, 2.633, -0.146, 2, 3.5, -0.146, 2, 3.7, -0.146, 1, 3.844, -0.144, 3.989, -0.12, 4.133, -0.12, 2, 5, -0.12, 2, 5.233, -0.12, 0, 5.8, 0.082, 2, 6.8, 0.082, 2, 6.967, 0.082, 0, 7.833, 0.08, 2, 9.767, 0.08, 2, 10.7, 0.08, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.9, 0, 2, 2.167, 0, 2, 2.633, 0, 2, 3.5, 0, 2, 3.7, 0, 0, 3.767, -0.5, 0, 4.133, 0.2, 0, 5, -0.101, 2, 5.233, -0.101, 0, 5.8, -0.386, 2, 6.8, -0.386, 2, 6.967, -0.386, 0, 7.833, -0.379, 2, 9.767, -0.379, 2, 10.7, -0.379, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.9, 0, 2, 2.167, 0, 2, 2.633, 0, 2, 3.5, 0, 2, 3.7, 0, 0, 3.767, -0.8, 0, 4, 0, 2, 5, 0, 2, 5.233, 0, 2, 5.8, 0, 2, 6.8, 0, 2, 6.967, 0, 2, 7.833, 0, 2, 9.767, 0, 2, 10.7, 0, 2, 12.033, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.9, 0, 2, 2.167, 0, 2, 2.633, 0, 2, 3.5, 0, 2, 3.7, 0, 0, 3.767, -1, 0, 4, 0, 2, 5, 0, 2, 5.233, 0, 2, 5.8, 0, 2, 6.8, 0, 2, 6.967, 0, 2, 7.833, 0, 2, 9.767, 0, 2, 10.7, 0, 2, 12.033, 0]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, -1, 2, 0.333, -1, 1, 0.366, -1, 0.4, -0.701, 0.433, -0.564, 1, 0.589, 0.077, 0.744, 0.3, 0.9, 0.3, 2, 2.167, 0.3, 2, 2.633, 0.3, 2, 3.5, 0.3, 1, 3.567, 0.3, 3.633, 0.3, 3.7, 0.298, 1, 3.722, 0.297, 3.745, -0.8, 3.767, -0.8, 0, 4, -0.045, 2, 5, -0.045, 2, 5.233, -0.045, 0, 5.8, -1, 2, 6.8, -1, 2, 6.967, -1, 2, 7.833, -1, 2, 9.767, -1, 2, 10.7, -1, 2, 12.033, -1]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, -1, 2, 0.333, -1, 1, 0.366, -1, 0.4, -0.701, 0.433, -0.564, 1, 0.589, 0.077, 0.744, 0.3, 0.9, 0.3, 2, 2.167, 0.3, 2, 2.633, 0.3, 2, 3.5, 0.3, 1, 3.567, 0.3, 3.633, 0.3, 3.7, 0.298, 1, 3.722, 0.297, 3.745, -0.8, 3.767, -0.8, 0, 4, -0.045, 2, 5, -0.045, 2, 5.233, -0.045, 0, 5.8, -1, 2, 6.8, -1, 2, 6.967, -1, 2, 7.833, -1, 2, 9.767, -1, 2, 10.7, -1, 2, 12.033, -1]}, {"Target": "Parameter", "Id": "Param", "Segments": [0, -30, 0, 0.333, 0, 2, 0.433, 0, 2, 0.9, 0, 2, 2.167, 0, 2, 2.633, 0, 2, 3.5, 0, 2, 3.7, 0, 2, 4, 0, 2, 5, 0, 2, 5.233, 0, 0, 6.8, 30, 2, 6.967, 30, 0, 7.833, 29.421, 2, 9.767, 29.421, 2, 10.7, 29.421, 0, 12.033, -30]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 1, 0, 0.333, 0, 2, 0.433, 0, 2, 0.9, 0, 2, 2.167, 0, 2, 2.633, 0, 2, 3.5, 0, 2, 3.7, 0, 2, 4, 0, 0, 4.2, -1, 0, 5, 0, 2, 5.233, 0, 0, 6.8, -0.543, 2, 6.967, -0.543, 0, 7.833, -0.532, 2, 9.767, -0.532, 2, 10.7, -0.532, 0, 12.033, 1]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 2, 0.333, 0, 0, 0.6, 0.8, 0, 0.867, 0, 0, 1.267, 0.6, 0, 1.4, 0, 0, 1.733, 0.82, 0, 2.333, 0, 2, 3.8, 0, 0, 3.867, 0.106, 0, 3.933, 0.102, 0, 4, 0.977, 0, 4.133, 0.141, 0, 4.2, 0.161, 0, 4.267, 0, 2, 4.733, 0, 0, 4.867, 0.706, 0, 4.933, 0, 2, 5.267, 0, 0, 5.4, 0.388, 0, 5.533, 0.31, 0, 5.667, 0.98, 0, 5.733, 0.431, 0, 5.8, 0.859, 0, 5.867, 0.172, 0, 5.933, 0.875, 0, 6.067, 0.286, 0, 6.267, 0.761, 0, 6.4, 0.122, 0, 6.467, 0.769, 0, 6.8, 0, 0, 7.067, 0.816, 0, 7.267, 0, 2, 7.533, 0, 0, 7.6, 0.859, 0, 7.667, 0.165, 0, 7.867, 0.996, 0, 8, 0.004, 0, 8.067, 0.016, 0, 8.133, 0.008, 0, 8.267, 0.886, 0, 8.4, 0, 0, 8.533, 0.698, 0, 8.6, 0.353, 0, 8.733, 0.902, 0, 8.867, 0, 0, 8.933, 0.726, 0, 9, 0.6, 0, 9.067, 0.718, 0, 9.2, 0.294, 0, 9.267, 0.792, 0, 9.333, 0.275, 0, 9.4, 0.431, 0, 9.467, 0, 0, 9.6, 0.549, 0, 9.867, 0, 2, 10.533, 0, 2, 10.7, 0, 2, 12.033, 0]}, {"Target": "Parameter", "Id": "Param252", "Segments": [0, 0, 2, 0.333, 0, 1, 0.366, 0, 0.4, 0.23, 0.433, 0.336, 1, 0.589, 0.829, 0.744, 1, 0.9, 1, 2, 2.167, 1, 2, 2.633, 1, 2, 3.5, 1, 1, 3.567, 1, 3.633, 1.001, 3.7, 0.998, 1, 3.8, 0.994, 3.9, 0.735, 4, 0.735, 2, 5, 0.735, 2, 5.233, 0.735, 0, 5.8, 1, 2, 6.8, 1, 2, 6.967, 1, 0, 7.833, 0.981, 2, 9.767, 0.981, 2, 10.7, 0.981, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, -1.29, 0, 0.9, 16, 0, 1.267, -10, 0, 1.7, 5.651, 0, 2.133, -8.703, 0, 2.567, 0, 0, 2.967, -8, 1, 3.189, -8, 3.411, -3.498, 3.633, 3.215, 1, 3.7, 5.229, 3.766, 5.498, 3.833, 5.498, 1, 3.866, 5.498, 3.9, 5.763, 3.933, -1, 1, 4, -14.526, 4.066, -30, 4.133, -30, 0, 5, -18.596, 2, 5.233, -18.596, 0, 5.4, -24.526, 0, 5.933, -15.147, 0, 6.367, -22.164, 0, 6.6, -15.102, 0, 6.9, -18.596, 0, 7.2, -3.872, 0, 7.7, -22.015, 0, 8.1, -11.678, 0, 9.1, -24.526, 0, 9.9, -11.678, 0, 10.7, -17.72, 1, 10.822, -17.72, 10.945, -5.648, 11.067, -4.263, 1, 11.389, -0.61, 11.711, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 2, 0.333, 0, 1, 0.366, 0, 0.4, -2.601, 0.433, -4.242, 1, 0.533, -9.166, 0.633, -10.797, 0.733, -10.797, 1, 0.789, -10.797, 0.844, -10.084, 0.9, -7.384, 1, 0.989, -3.064, 1.078, 0, 1.167, 0, 0, 1.4, -6.042, 1, 1.689, -6.042, 1.978, -1.681, 2.267, 3.54, 1, 2.467, 7.154, 2.667, 7.86, 2.867, 7.86, 1, 3.045, 7.86, 3.222, 7.851, 3.4, 5.477, 1, 3.533, 3.697, 3.667, -4.626, 3.8, -4.626, 0, 4.1, 23, 0, 4.667, 3.247, 0, 5, 15.485, 0, 5.233, 3.006, 0, 5.5, 12.508, 0, 5.7, 0.643, 0, 6.033, 11.817, 0, 6.467, 0.313, 1, 6.611, 0.313, 6.756, 4.572, 6.9, 7.86, 1, 7, 10.136, 7.1, 10.199, 7.2, 10.199, 0, 7.5, -26.565, 0, 8.133, -21.502, 1, 8.233, -21.502, 8.333, -21.243, 8.433, -22.138, 1, 8.711, -24.622, 8.989, -28.485, 9.267, -28.485, 0, 9.9, -15.254, 1, 10.167, -15.254, 10.433, -18.802, 10.7, -25.539, 1, 10.822, -28.626, 10.945, -30, 11.067, -30, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, -0.927, 0, 0.9, -0.272, 0, 1.3, -1.292, 0, 1.8, 0, 0, 2.333, -0.927, 0, 2.7, -0.35, 1, 2.967, -0.35, 3.233, -0.422, 3.5, -0.927, 1, 3.6, -1.116, 3.7, -2.732, 3.8, -2.732, 0, 4, -1.218, 0, 4.5, -2.238, 0, 5, -1.218, 2, 5.233, -1.218, 0, 5.4, -1.299, 0, 5.933, 0.917, 0, 6.367, 0.326, 0, 6.6, 1.019, 0, 6.8, 0.825, 2, 6.967, 0.825, 0, 7.1, 1.198, 0, 7.833, -7.707, 0, 8.867, -7.222, 0, 9.767, -7.702, 2, 10.7, -7.702, 1, 10.822, -7.702, 10.945, -8.005, 11.067, -7.063, 1, 11.389, -4.579, 11.711, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param267", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, -0.294, 0, 0.9, 10.977, 0, 1.333, 8.539, 0, 1.867, 9.709, 0, 2.4, 8.539, 0, 2.8, 9.666, 0, 3.467, 8.539, 0, 4, 12.632, 0, 4.5, 10.601, 0, 5, 11.435, 2, 5.233, 11.435, 0, 5.8, 2.247, 2, 6.8, 2.247, 2, 6.967, 2.247, 1, 7.011, 2.247, 7.056, 2.015, 7.1, 3.744, 1, 7.344, 13.255, 7.589, 20.504, 7.833, 20.504, 1, 8.178, 20.504, 8.522, 19.085, 8.867, 18.68, 1, 9.167, 18.326, 9.467, 18.379, 9.767, 18.379, 2, 10.7, 18.379, 0, 11.067, 19.116, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param3", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, -3.295, 0, 0.9, 3.951, 0, 1.3, -3.969, 0, 1.8, 5.88, 0, 2.633, -1.689, 0, 3.5, 3.951, 0, 3.7, -0.449, 0, 4, 3.399, 0, 5, 2.493, 2, 5.233, 2.493, 0, 5.533, -3.061, 0, 5.8, 6.289, 1, 6.133, 6.289, 6.467, 6.19, 6.8, 5.85, 1, 6.856, 5.793, 6.911, 1.71, 6.967, 1.71, 0, 7.4, 8.41, 0, 7.933, 5.44, 0, 8.333, 8.227, 0, 8.867, 5.697, 0, 9.2, 7.938, 1, 9.389, 7.938, 9.578, 7.062, 9.767, 4.21, 1, 10.067, -0.322, 10.367, -3.295, 10.667, -3.295, 0, 11.067, 1.808, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param4", "Segments": [0, 0, 2, 0.333, 0, 1, 0.366, 0, 0.4, -6.119, 0.433, -8.004, 1, 0.589, -16.799, 0.744, -19.782, 0.9, -19.782, 0, 1.3, -16.964, 0, 2.167, -20.202, 0, 2.767, -16.964, 1, 2.945, -16.964, 3.122, -17.242, 3.3, -18.135, 1, 3.367, -18.471, 3.433, -18.887, 3.5, -19.782, 1, 3.567, -20.677, 3.633, -24.254, 3.7, -24.254, 0, 4, -6.718, 0, 4.567, -9.833, 1, 4.711, -9.833, 4.856, -7.469, 5, -4.933, 1, 5.078, -3.567, 5.155, -3.467, 5.233, -3.467, 0, 5.533, -16.267, 0, 5.967, -11.545, 0, 6.267, -15.174, 0, 6.8, -12.787, 2, 6.967, -12.787, 0, 7.833, -18.877, 1, 8.478, -18.877, 9.122, -18.825, 9.767, -18.064, 1, 10.2, -17.553, 10.634, -15.887, 11.067, -14.222, 1, 11.211, -13.667, 11.356, 1.441, 11.5, 1.441, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param266", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, -2.909, 0, 0.9, 3.532, 0, 1.3, 1.852, 0, 2.167, 3.532, 2, 2.633, 3.532, 2, 3.5, 3.532, 0, 3.7, -1.091, 1, 3.8, -1.091, 3.9, -1.021, 4, 0.354, 1, 4.067, 1.271, 4.133, 5.154, 4.2, 5.154, 0, 5, 0.354, 2, 5.233, 0.354, 0, 5.8, 0, 2, 6.8, 0, 2, 6.967, 0, 0, 7.4, 5.986, 0, 7.833, -12.309, 0, 8.333, -7.217, 0, 8.867, -8.387, 1, 8.978, -8.387, 9.089, -6.435, 9.2, -4.449, 1, 9.389, -1.072, 9.578, 0, 9.767, 0, 0, 10.7, -5.88, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param5", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, 4.706, 0, 0.667, -20.892, 0, 1.067, -10.171, 0, 1.5, -18.083, 0, 1.867, -13.07, 0, 2.267, -17, 0, 2.633, -13.089, 1, 2.922, -13.089, 3.211, -13.656, 3.5, -15.441, 1, 3.567, -15.853, 3.633, -17.035, 3.7, -17.035, 0, 4, -7.918, 0, 4.3, -15.251, 0, 5, -14.098, 0, 5.233, -15.953, 0, 5.8, 10.746, 0, 6.8, 10.007, 0, 6.967, 13.252, 0, 7.4, -28.102, 2, 8.333, -28.102, 2, 10.233, -28.102, 1, 10.5, -28.102, 10.766, -26.37, 11.033, -18.453, 1, 11.366, -8.557, 11.7, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param6", "Segments": [0, 0, 2, 0.333, 0, 1, 0.366, 0, 0.4, -0.042, 0.433, 1.994, 1, 0.511, 6.746, 0.589, 11.58, 0.667, 11.58, 0, 1.067, -0.696, 0, 1.5, 8.603, 0, 1.867, -0.696, 0, 2.267, 4.99, 0, 2.633, 0.171, 0, 3.5, 5.94, 0, 3.7, 4.306, 0, 4, 12.999, 0, 4.3, 1.322, 0, 5, 2.26, 0, 5.233, 0.405, 0, 5.8, 10.746, 0, 6.8, 10.007, 0, 6.967, 13.252, 0, 7.4, -6.733, 1, 7.6, -6.733, 7.8, -6.587, 8, -4.25, 1, 8.4, 0.426, 8.8, 4.638, 9.2, 4.638, 0, 9.767, 0.249, 2, 10.7, 0.249, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.9, 0, 2, 2.167, 0, 2, 2.633, 0, 2, 3.5, 0, 2, 3.7, 0, 2, 4, 0, 2, 5, 0, 2, 5.233, 0, 2, 5.533, 0, 0, 5.8, 0.7, 0, 6.8, 0.667, 2, 6.967, 0.667, 0, 7.833, 0.566, 2, 9.767, 0.566, 2, 10.7, 0.566, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param8", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.9, 0, 2, 2.167, 0, 2, 2.633, 0, 2, 3.5, 0, 2, 3.7, 0, 0, 4, -0.073, 2, 5, -0.073, 2, 5.233, -0.073, 0, 5.8, -0.282, 2, 6.8, -0.282, 2, 6.967, -0.282, 0, 7.833, -0.277, 2, 9.767, -0.277, 2, 10.7, -0.277, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param23", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.9, 0, 2, 2.167, 0, 2, 2.633, 0, 2, 3.5, 0, 2, 3.7, 0, 0, 4, -0.268, 2, 5, -0.268, 2, 5.233, -0.268, 0, 5.8, -1.027, 2, 6.8, -1.027, 2, 6.967, -1.027, 0, 7.833, -1.007, 2, 9.767, -1.007, 2, 10.7, -1.007, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param25", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.9, 0, 2, 2.167, 0, 2, 2.633, 0, 2, 3.5, 0, 2, 3.7, 0, 0, 4, -0.151, 2, 5, -0.151, 2, 5.233, -0.151, 0, 5.8, -0.579, 2, 6.8, -0.579, 2, 6.967, -0.579, 0, 7.833, -0.567, 2, 9.767, -0.567, 2, 10.7, -0.567, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param27", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.9, 0, 2, 2.167, 0, 2, 2.633, 0, 2, 3.5, 0, 2, 3.7, 0, 0, 4, -0.565, 2, 5, -0.565, 2, 5.233, -0.565, 0, 5.8, -2.167, 2, 6.8, -2.167, 2, 6.967, -2.167, 0, 7.833, -2.125, 2, 9.767, -2.125, 2, 10.7, -2.125, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param201", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.9, 0, 2, 2.167, 0, 2, 2.633, 0, 2, 3.667, 0, 2, 3.7, 0, 2, 4, 0, 2, 5, 0, 2, 5.233, 0, 2, 5.5, 0, 0, 5.533, 1, 2, 6.8, 1, 2, 6.967, 1, 2, 7.2, 1, 0, 7.233, 0, 2, 7.833, 0, 2, 9.767, 0, 2, 10.7, 0, 2, 11.1, 0, 2, 11.133, 0, 2, 12.033, 0]}, {"Target": "Parameter", "Id": "Param202", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, 1, 2, 0.9, 1, 2, 2.167, 1, 2, 2.633, 1, 2, 3.667, 1, 0, 3.7, 0.998, 2, 4, 0.998, 2, 5, 0.998, 2, 5.233, 0.998, 2, 5.5, 0.998, 0, 5.533, 0, 2, 6.8, 0, 2, 6.967, 0, 2, 7.2, 0, 0, 7.233, 1, 2, 7.833, 1, 2, 9.767, 1, 2, 10.7, 1, 2, 11.1, 1, 0, 11.133, 0, 2, 12.033, 0]}, {"Target": "Parameter", "Id": "Param203", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.9, 0, 2, 2.167, 0, 2, 2.633, 0, 2, 3.667, 0, 2, 3.7, 0, 2, 4, 0, 2, 5, 0, 2, 5.233, 0, 2, 5.5, 0, 2, 5.533, 0, 2, 6.8, 0, 2, 6.967, 0, 2, 7.2, 0, 0, 7.233, 1, 2, 7.833, 1, 2, 9.767, 1, 2, 10.7, 1, 2, 11.1, 1, 0, 11.133, 0, 2, 12.033, 0]}, {"Target": "Parameter", "Id": "Param204", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, 1, 2, 0.9, 1, 2, 2.167, 1, 2, 2.633, 1, 2, 3.667, 1, 0, 3.7, 0.998, 2, 4, 0.998, 2, 5, 0.998, 2, 5.233, 0.998, 2, 5.5, 0.998, 0, 5.533, 0, 2, 6.8, 0, 2, 6.967, 0, 2, 7.2, 0, 2, 7.233, 0, 2, 7.833, 0, 2, 9.767, 0, 2, 10.7, 0, 2, 11.1, 0, 2, 11.133, 0, 2, 12.033, 0]}, {"Target": "Parameter", "Id": "Param209", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.9, 0, 2, 2.167, 0, 2, 2.633, 0, 2, 3.667, 0, 2, 3.7, 0, 2, 4, 0, 2, 5, 0, 2, 5.233, 0, 2, 5.5, 0, 0, 5.533, 1, 2, 6.8, 1, 2, 6.967, 1, 2, 7.2, 1, 0, 7.233, 0, 2, 7.833, 0, 2, 9.767, 0, 2, 10.7, 0, 2, 11.1, 0, 2, 11.133, 0, 2, 12.033, 0]}, {"Target": "Parameter", "Id": "Param217", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.9, 0, 2, 2.167, 0, 2, 2.633, 0, 2, 3.667, 0, 2, 3.7, 0, 2, 4, 0, 2, 5, 0, 2, 5.233, 0, 2, 5.5, 0, 0, 5.533, 1, 2, 6.8, 1, 2, 6.967, 1, 2, 7.2, 1, 0, 7.233, 0, 2, 7.833, 0, 2, 9.767, 0, 2, 10.7, 0, 2, 11.1, 0, 2, 11.133, 0, 2, 12.033, 0]}, {"Target": "Parameter", "Id": "Param9", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, -4.778, 0, 0.9, 0.78, 0, 1.133, -0.363, 0, 1.667, 0.424, 0, 2.167, -0.363, 0, 2.633, 0.78, 2, 3.5, 0.78, 1, 3.567, 0.78, 3.633, -1.407, 3.7, -1.86, 1, 3.8, -2.539, 3.9, -2.551, 4, -2.551, 0, 4.667, -1.111, 2, 4.767, -1.111, 2, 5, -1.111, 2, 5.233, -1.111, 0, 5.367, -5.742, 0, 5.9, -3.019, 0, 6.133, -4.688, 0, 6.333, -3.859, 0, 6.533, -4.965, 1, 6.644, -4.965, 6.756, -4.579, 6.867, -3.776, 1, 6.9, -3.535, 6.934, -3.288, 6.967, -3.019, 1, 7.011, -2.66, 7.056, -2.452, 7.1, -2.452, 0, 7.467, -4.896, 0, 7.767, -2.498, 0, 8.267, -3.184, 1, 8.689, -3.184, 9.111, -3.137, 9.533, -2.615, 1, 9.922, -2.134, 10.311, -1.397, 10.7, -1.397, 0, 11.067, -3.035, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param10", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, 2.88, 0, 0.9, 0.18, 0, 1.133, 0.75, 0, 1.667, 0.196, 0, 2.167, 0.728, 0, 2.633, 0.18, 2, 3.5, 0.18, 1, 3.567, 0.18, 3.633, 1.122, 3.7, 1.5, 1, 3.8, 2.067, 3.9, 2.119, 4, 2.119, 0, 4.667, 0.919, 2, 4.767, 0.919, 2, 5, 0.919, 2, 5.233, 0.919, 1, 5.322, 0.919, 5.411, 1.125, 5.5, 6.775, 1, 5.6, 13.131, 5.7, 24.419, 5.8, 24.419, 0, 5.9, -6, 0, 6.8, 23.099, 2, 6.967, 23.099, 0, 7.1, 25, 1, 7.144, 25, 7.189, 19.467, 7.233, 16.488, 1, 7.311, 11.274, 7.389, 4.411, 7.467, 3.885, 1, 7.589, 3.058, 7.711, 2.343, 7.833, 2.251, 1, 8.478, 1.765, 9.122, 1.621, 9.767, 1.621, 2, 10.7, 1.621, 0, 11.433, 2.989, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param11", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.9, 0, 2, 2.167, 0, 2, 2.633, 0, 2, 3.5, 0, 2, 3.7, 0, 0, 4, 4.655, 2, 4.667, 4.655, 2, 4.767, 4.655, 2, 5, 4.655, 2, 5.233, 4.655, 0, 5.8, 4.232, 0, 6.8, 17.852, 2, 6.967, 17.852, 0, 7.233, 30, 1, 7.311, 30, 7.389, 25.097, 7.467, 22.445, 1, 7.589, 18.276, 7.711, 17.507, 7.833, 17.507, 2, 9.767, 17.507, 2, 10.7, 17.507, 0, 11.7, -4.12, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param12", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.9, 0, 2, 2.167, 0, 2, 2.633, 0, 2, 3.5, 0, 2, 3.7, 0, 0, 4, -7.436, 2, 4.667, -7.436, 2, 4.767, -7.436, 2, 5, -7.436, 2, 5.233, -7.436, 0, 5.8, -28.517, 2, 6.8, -28.517, 2, 6.967, -28.517, 1, 7.134, -28.517, 7.3, -28.125, 7.467, -28.024, 1, 7.589, -27.949, 7.711, -27.966, 7.833, -27.966, 2, 9.767, -27.966, 2, 10.7, -27.966, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param199", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.9, 0, 2, 2.167, 0, 2, 2.633, 0, 2, 3.5, 0, 2, 3.7, 0, 2, 4, 0, 2, 4.667, 0, 2, 4.767, 0, 2, 5, 0, 2, 5.233, 0, 2, 5.8, 0, 2, 6.8, 0, 2, 6.967, 0, 0, 7.467, 11.029, 0, 7.633, -6.39, 0, 8.2, 0, 2, 9.767, 0, 2, 10.7, 0, 2, 12.033, 0]}, {"Target": "Parameter", "Id": "Param22", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, 1, 2, 0.9, 1, 2, 2.167, 1, 2, 2.633, 1, 2, 3.5, 1, 2, 3.7, 1, 2, 4, 1, 2, 4.667, 1, 2, 4.767, 1, 2, 5, 1, 2, 5.233, 1, 2, 5.8, 1, 2, 6.8, 1, 2, 6.967, 1, 2, 7.467, 1, 2, 7.833, 1, 2, 9.767, 1, 2, 10.7, 1, 2, 12, 1, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param13", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.9, 0, 2, 2.167, 0, 2, 2.633, 0, 2, 3.5, 0, 2, 3.7, 0, 0, 4, 2.618, 2, 4.667, 2.618, 2, 4.767, 2.618, 2, 5, 2.618, 2, 5.233, 2.618, 0, 5.8, 10.038, 2, 6.8, 10.038, 2, 6.967, 10.038, 1, 7.134, 10.038, 7.3, 10.204, 7.467, 10.907, 1, 7.589, 11.423, 7.711, 12, 7.833, 12, 2, 9.767, 12, 2, 10.7, 12, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param14", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.9, 0, 2, 2.167, 0, 2, 2.633, 0, 2, 3.5, 0, 2, 3.7, 0, 0, 4, -4.224, 2, 4.667, -4.224, 2, 4.767, -4.224, 2, 5, -4.224, 2, 5.233, -4.224, 0, 5.8, -16.198, 2, 6.8, -16.198, 2, 6.967, -16.198, 1, 7.134, -16.198, 7.3, -14.774, 7.467, -9.022, 1, 7.589, -4.804, 7.711, 0, 7.833, 0, 2, 9.767, 0, 2, 10.7, 0, 2, 12.033, 0]}, {"Target": "Parameter", "Id": "Param210", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.9, 0, 2, 2.167, 0, 2, 2.633, 0, 2, 3.5, 0, 2, 3.7, 0, 0, 4, -0.004, 2, 4.667, -0.004, 2, 4.767, -0.004, 2, 5, -0.004, 2, 5.233, -0.004, 0, 5.5, 19.894, 0, 5.867, -11.359, 0, 6.067, -6.117, 0, 6.3, -12.117, 0, 6.5, -6.117, 0, 6.8, -12.117, 0, 6.967, -11.757, 0, 7.1, -13.126, 0, 7.467, 30, 2, 7.833, 30, 2, 9.767, 30, 2, 10.7, 30, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param211", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.9, 0, 2, 2.167, 0, 2, 2.633, 0, 2, 3.5, 0, 2, 3.7, 0, 0, 4, 1.222, 2, 4.667, 1.222, 2, 4.767, 1.222, 2, 5, 1.222, 2, 5.233, 1.222, 0, 5.8, 24.066, 1, 5.856, 24.066, 5.911, 14.186, 5.967, 5.749, 1, 6, 0.687, 6.034, 0.606, 6.067, 0.606, 0, 6.2, 6.735, 0, 6.4, 0.606, 0, 6.633, 6.735, 0, 6.8, 4.313, 0, 6.967, 6.353, 0, 7.1, 2.642, 0, 7.467, 3.625, 2, 7.833, 3.625, 2, 9.767, 3.625, 2, 10.7, 3.625, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param212", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.9, 0, 2, 2.167, 0, 2, 2.633, 0, 2, 3.5, 0, 2, 3.7, 0, 2, 4, 0, 2, 4.667, 0, 2, 4.767, 0, 2, 5, 0, 2, 5.233, 0, 0, 5.5, -10.14, 0, 5.8, 0, 2, 6.8, 0, 2, 6.967, 0, 2, 7.833, 0, 2, 9.767, 0, 2, 10.7, 0, 2, 12.033, 0]}, {"Target": "Parameter", "Id": "Param221", "Segments": [0, 0, 2, 0.333, 0, 2, 0.6, 0, 0, 0.633, 1, 2, 0.9, 1, 2, 2.167, 1, 2, 2.633, 1, 2, 3.533, 1, 0, 3.567, 0.998, 0, 4, 0.999, 2, 5, 0.999, 2, 5.233, 0.999, 0, 5.4, 1, 2, 5.433, 1, 2, 6.8, 1, 2, 6.967, 1, 2, 7.3, 1, 0, 7.333, 0.981, 2, 7.833, 0.981, 2, 9.767, 0.981, 2, 10.7, 0.981, 2, 11.533, 0.981, 0, 11.567, 0, 2, 12.033, 0]}, {"Target": "Parameter", "Id": "Param222", "Segments": [0, 0, 2, 0.333, 0, 2, 0.6, 0, 2, 0.633, 0, 2, 0.9, 0, 2, 2.167, 0, 2, 2.633, 0, 2, 3.533, 0, 2, 3.567, 0, 2, 4, 0, 2, 5, 0, 2, 5.233, 0, 2, 5.4, 0, 2, 5.433, 0, 2, 6.8, 0, 2, 6.967, 0, 2, 7.3, 0, 0, 7.333, 1, 2, 7.833, 1, 2, 9.767, 1, 2, 10.7, 1, 2, 11.533, 1, 0, 11.567, 0, 2, 12.033, 0]}, {"Target": "Parameter", "Id": "Param223", "Segments": [0, 0, 2, 0.333, 0, 2, 0.6, 0, 0, 0.633, 1, 2, 0.9, 1, 2, 2.167, 1, 2, 2.633, 1, 2, 3.533, 1, 0, 3.567, 0.998, 2, 4, 0.998, 2, 5, 0.998, 2, 5.233, 0.998, 2, 5.4, 0.998, 0, 5.433, 1, 2, 6.8, 1, 2, 6.967, 1, 2, 7.3, 1, 0, 7.333, 0, 2, 7.833, 0, 2, 9.767, 0, 2, 10.7, 0, 2, 11.533, 0, 2, 11.567, 0, 2, 12.033, 0]}, {"Target": "Parameter", "Id": "Param236", "Segments": [0, 0, 2, 0.333, 0, 2, 0.6, 0, 0, 0.633, 1, 2, 0.9, 1, 2, 1.5, 1, 2, 2.167, 1, 2, 2.633, 1, 2, 3.533, 1, 0, 3.567, 0, 2, 4, 0, 2, 5, 0, 2, 5.233, 0, 2, 5.4, 0, 2, 5.433, 0, 2, 6.8, 0, 2, 6.967, 0, 2, 7.3, 0, 2, 7.333, 0, 2, 7.833, 0, 2, 9.767, 0, 2, 10.7, 0, 2, 11.533, 0, 2, 11.567, 0, 2, 12.033, 0]}, {"Target": "Parameter", "Id": "Param238", "Segments": [0, 0, 2, 0.333, 0, 2, 0.6, 0, 2, 0.633, 0, 2, 0.9, 0, 2, 2.167, 0, 2, 2.633, 0, 2, 3.533, 0, 0, 3.567, 1, 2, 4, 1, 2, 5, 1, 2, 5.233, 1, 2, 5.4, 1, 0, 5.433, 0, 2, 6.8, 0, 2, 6.967, 0, 2, 7.3, 0, 2, 7.333, 0, 2, 7.833, 0, 2, 9.767, 0, 2, 10.7, 0, 2, 11.533, 0, 2, 11.567, 0, 2, 12.033, 0]}, {"Target": "Parameter", "Id": "Param243", "Segments": [0, 0, 2, 0.333, 0, 2, 0.6, 0, 2, 0.633, 0, 2, 0.9, 0, 2, 2.167, 0, 2, 2.633, 0, 2, 3.533, 0, 2, 3.567, 0, 2, 4, 0, 2, 5, 0, 2, 5.233, 0, 2, 5.4, 0, 0, 5.433, 1, 2, 6.8, 1, 2, 6.967, 1, 2, 7.3, 1, 0, 7.333, 0.981, 2, 7.833, 0.981, 2, 9.767, 0.981, 2, 10.7, 0.981, 2, 11.533, 0.981, 0, 11.567, 0, 2, 12.033, 0]}, {"Target": "Parameter", "Id": "Param15", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, 12.215, 1, 0.589, 12.215, 0.744, 11.095, 0.9, 10.26, 1, 1.1, 9.187, 1.3, 8.419, 1.5, 7.74, 1, 1.6, 7.401, 1.7, 7.372, 1.8, 7.372, 0, 2.167, 9.06, 0, 2.633, 8.76, 0, 3.167, 9.741, 0, 3.5, 9.12, 0, 3.7, 11.741, 0, 4, 10.049, 2, 5, 10.049, 2, 5.233, 10.049, 0, 5.8, 6.925, 2, 6.8, 6.925, 2, 6.967, 6.925, 1, 7.011, 6.925, 7.056, 6.342, 7.1, 6.195, 1, 7.244, 5.717, 7.389, 5.604, 7.533, 5.604, 0, 7.833, 6.791, 1, 8.111, 6.791, 8.389, 6.698, 8.667, 6.491, 1, 8.822, 6.375, 8.978, 6.307, 9.133, 6.307, 1, 9.344, 6.307, 9.556, 6.238, 9.767, 6.611, 1, 10.078, 7.16, 10.389, 8.531, 10.7, 8.531, 1, 10.822, 8.531, 10.945, 8.411, 11.067, 8.353, 1, 11.245, 8.269, 11.422, 8.278, 11.6, 8.167, 1, 11.744, 8.077, 11.889, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param16", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, -0.268, 0, 0.767, 18.921, 0, 0.9, 0, 2, 2.167, 0, 2, 2.633, 0, 2, 3.5, 0, 2, 3.7, 0, 0, 4, 4.625, 2, 5, 4.625, 2, 5.233, 4.625, 0, 5.8, 17.738, 2, 6.8, 17.738, 2, 6.967, 17.738, 0, 7.833, 16.675, 2, 9.767, 16.675, 0, 10.7, -16, 0, 11.067, 30, 1, 11.245, 30, 11.422, 4.715, 11.6, 1.86, 1, 11.744, -0.459, 11.889, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param17", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, 17.7, 1, 0.544, 17.7, 0.656, 17.221, 0.767, 14.4, 1, 0.811, 13.272, 0.856, 0, 0.9, 0, 2, 2.167, 0, 2, 2.633, 0, 2, 3.5, 0, 2, 3.7, 0, 0, 4, 5.741, 2, 5, 5.741, 2, 5.233, 5.741, 0, 5.8, 22.015, 2, 6.8, 22.015, 2, 6.967, 22.015, 0, 7.833, 21.59, 2, 9.767, 21.59, 2, 10.7, 21.59, 1, 11, 21.59, 11.3, 21.562, 11.6, 20.762, 1, 11.744, 20.377, 11.889, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param19", "Segments": [0, 0, 2, 0.333, 0, 1, 0.366, 0, 0.4, -52.637, 0.433, -52.637, 2, 0.9, -52.637, 2, 2.167, -52.637, 0, 2.633, -55, 2, 3.5, -55, 1, 3.567, -55, 3.633, -55.059, 3.7, -54.899, 1, 3.8, -54.659, 3.9, -48.785, 4, -48.785, 2, 5, -48.785, 2, 5.233, -48.785, 0, 5.8, -27.034, 2, 6.8, -27.034, 2, 6.967, -27.034, 0, 7.833, -26.512, 2, 9.767, -26.512, 2, 10.7, -26.512, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param20", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 0, 0.767, 16, 0, 0.9, 0, 2, 2.167, 0, 2, 2.633, 0, 2, 3.5, 0, 2, 3.7, 0, 2, 4, 0, 2, 5, 0, 2, 5.233, 0, 2, 5.8, 0, 2, 6.8, 0, 2, 6.967, 0, 2, 7.833, 0, 2, 9.767, 0, 2, 10.7, 0, 2, 12.033, 0]}, {"Target": "Parameter", "Id": "Param224", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.9, 0, 2, 2.167, 0, 2, 2.633, 0, 2, 3.5, 0, 2, 3.7, 0, 2, 4, 0, 2, 5, 0, 2, 5.233, 0, 2, 5.8, 0, 2, 6.8, 0, 2, 6.967, 0, 0, 7.133, -30, 2, 7.267, -30, 1, 7.278, -30, 7.289, -27.316, 7.3, -25.908, 1, 7.322, -23.092, 7.345, -21.052, 7.367, -19, 1, 7.422, -13.869, 7.478, -11.618, 7.533, -11.618, 0, 7.833, -14, 1, 7.944, -14, 8.056, -14.004, 8.167, -13.938, 1, 8.334, -13.839, 8.5, -13.688, 8.667, -13.688, 0, 9.133, -14.621, 0, 9.767, -14, 1, 10.078, -14, 10.389, -14.043, 10.7, -14.36, 1, 10.822, -14.484, 10.945, -15.358, 11.067, -15.358, 0, 11.6, 30, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param225", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.9, 0, 2, 2.167, 0, 2, 2.633, 0, 2, 3.5, 0, 2, 3.7, 0, 2, 4, 0, 2, 5, 0, 2, 5.233, 0, 2, 5.8, 0, 2, 6.8, 0, 2, 6.967, 0, 2, 7.833, 0, 1, 8.089, 0, 8.344, -0.031, 8.6, 0.963, 1, 8.767, 1.611, 8.933, 8.329, 9.1, 8.329, 0, 9.767, 0, 2, 10.7, 0, 2, 12.033, 0]}, {"Target": "Parameter", "Id": "Param233", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, 15.294, 1, 0.544, 15.294, 0.656, 2.38, 0.767, 0.72, 1, 0.845, -0.442, 0.922, -0.48, 1, -1.02, 1, 1.067, -1.483, 1.133, -1.663, 1.2, -1.663, 0, 1.5, -0.72, 0, 1.8, -1.319, 0, 2.167, 0.36, 0, 2.633, -0.84, 1, 2.811, -0.84, 2.989, -0.71, 3.167, -0.509, 1, 3.278, -0.384, 3.389, -0.321, 3.5, -0.18, 1, 3.567, -0.096, 3.633, 0.046, 3.7, 0.3, 1, 3.756, 0.511, 3.811, 1.401, 3.867, 1.401, 2, 4, 1.401, 0, 5, -0.519, 2, 5.233, -0.519, 1, 5.311, -0.519, 5.389, 0.792, 5.467, 2.34, 1, 5.578, 4.551, 5.689, 5.281, 5.8, 5.281, 0, 6.033, 2.041, 0, 6.267, 5.281, 0, 6.467, 2.041, 0, 6.667, 5.281, 0, 6.8, 4.775, 2, 6.967, 4.775, 0, 7.1, 5.205, 0, 7.533, -16.14, 0, 7.833, -11.225, 2, 9.767, -11.225, 2, 10.7, -11.225, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param234", "Segments": [0, 0, 2, 0.333, 0, 1, 0.366, 0, 0.4, 1.77, 0.433, 5, 1, 0.544, 15.766, 0.656, 21, 0.767, 21, 0, 1.333, -1.178, 0, 1.667, 3.622, 0, 2, -2.528, 0, 2.433, 3.322, 0, 2.933, -2.528, 0, 3.433, 1.643, 1, 3.455, 1.643, 3.478, -0.193, 3.5, -5.283, 1, 3.567, -20.55, 3.633, -29, 3.7, -29, 1, 3.8, -29, 3.9, -12.62, 4, -11.543, 1, 4.333, -7.952, 4.667, -7.193, 5, -7.193, 2, 5.233, -7.193, 0, 5.8, 4.106, 0, 6.033, 0.281, 0, 6.367, 6.469, 0, 6.567, 0.281, 0, 6.8, 6.004, 0, 6.967, 4.504, 0, 7.1, 7.72, 0, 7.833, 5.888, 2, 9.767, 5.888, 2, 10.7, 5.888, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param285", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 0, 0.767, 29, 0, 1.2, -15.18, 0, 1.5, 7.381, 0, 2.167, -12.266, 1, 2.322, -12.266, 2.478, 10.104, 2.633, 11.832, 1, 2.922, 15.04, 3.211, 15.221, 3.5, 15.221, 0, 3.7, -30, 0, 4, 0, 2, 5, 0, 2, 5.233, 0, 2, 6.8, 0, 2, 6.967, 0, 2, 7.833, 0, 2, 9.767, 0, 2, 10.7, 0, 2, 12.033, 0]}, {"Target": "Parameter", "Id": "Param235", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 0, 0.9, 30, 2, 2.167, 30, 2, 2.633, 30, 2, 3.5, 30, 1, 3.567, 30, 3.633, 30.035, 3.7, 29.945, 1, 3.8, 29.81, 3.9, 19.663, 4, 19.663, 2, 5, 19.663, 2, 5.233, 19.663, 0, 5.8, -9.125, 2, 6.8, -9.125, 2, 6.967, -9.125, 0, 7.833, -8.949, 2, 9.767, -8.949, 2, 10.7, -8.949, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param277", "Segments": [0, 0, 2, 0.333, 0, 0, 0.6, 12.46, 0, 1.133, -9.549, 0, 1.567, 12.46, 0, 1.967, -9.549, 0, 2.4, 12.46, 0, 2.8, -9.549, 0, 3.233, 12.46, 0, 3.633, -9.549, 0, 4.067, 12.46, 0, 4.467, -9.549, 0, 4.9, 12.46, 0, 5.3, -9.549, 0, 5.733, 12.46, 0, 6.133, -9.549, 0, 6.567, 12.46, 0, 6.967, -9.549, 0, 7.4, 12.46, 0, 7.8, -9.549, 0, 8.233, 12.46, 0, 8.633, -9.549, 0, 9.067, 0, 2, 9.4, 0, 0, 9.667, 12.46, 0, 10.2, -9.549, 0, 10.633, 12.46, 0, 11.033, -9.549, 0, 11.467, 12.46, 0, 11.867, -9.549, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param278", "Segments": [0, 0, 2, 0.333, 0, 0, 0.6, 0.25, 0, 1.267, -0.17, 0, 1.767, 0.25, 0, 2.267, -0.17, 0, 2.767, 0.25, 0, 3.267, -0.17, 0, 3.767, 0.25, 0, 4.267, -0.17, 0, 4.767, 0.25, 0, 5.267, -0.17, 0, 5.767, 0.25, 0, 6.267, -0.17, 0, 6.767, 0.25, 0, 7.267, -0.17, 0, 7.767, 0.25, 0, 8.267, -0.17, 0, 8.767, 0.25, 0, 9.267, -0.17, 0, 9.767, 0.25, 0, 10.267, -0.17, 1, 10.434, -0.17, 10.6, -0.113, 10.767, 0, 1, 11.011, 0.166, 11.256, 0.25, 11.5, 0.25, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param280", "Segments": [0, 0, 2, 0.333, 0, 0, 0.467, -0.261, 0, 0.767, 0.265, 0, 1.067, -0.261, 0, 1.3, 0.265, 0, 1.7, -0.261, 1, 1.789, -0.261, 1.878, 0.104, 1.967, 0.161, 1, 2.122, 0.262, 2.278, 0.265, 2.433, 0.265, 0, 2.8, -0.261, 0, 3.367, 0.265, 0, 3.733, -0.261, 0, 4.033, 0.265, 0, 4.233, -0.261, 0, 4.7, 0.265, 0, 5, -0.261, 0, 5.233, 0.265, 0, 5.8, -0.261, 0, 6.067, 0.265, 0, 6.433, -0.261, 0, 6.7, 0.265, 0, 6.967, -0.261, 0, 7.267, 0.265, 0, 7.833, -0.261, 0, 8.3, 0.265, 0, 8.933, -0.261, 0, 9.4, 0.265, 0, 9.833, -0.261, 0, 10.3, 0.161, 0, 10.7, -0.261, 0, 11.067, 0.265, 0, 11.433, -0.261, 0, 11.7, 0.265, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param7", "Segments": [0, 0, 2, 0.333, 0, 0, 1.8, -9, 0, 3.267, 0, 0, 4.733, -9, 0, 6.2, 0, 0, 7.667, -9, 0, 9.133, 0, 0, 10.6, -9, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param66", "Segments": [0, 0, 2, 0.333, 0, 0, 1.8, -19, 0, 3.267, 0, 0, 4.733, -19, 0, 6.2, 0, 0, 7.667, -19, 0, 9.133, 0, 0, 10.6, -19, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param67", "Segments": [0, 0, 2, 0.333, 0, 0, 1.067, 16, 0, 2.533, -16, 1, 2.778, -16, 3.022, -10.667, 3.267, 0, 1, 3.511, 10.667, 3.756, 16, 4, 16, 0, 5.467, -16, 1, 5.711, -16, 5.956, -10.667, 6.2, 0, 1, 6.444, 10.667, 6.689, 16, 6.933, 16, 0, 8.4, -16, 1, 8.644, -16, 8.889, -10.667, 9.133, 0, 1, 9.378, 10.667, 9.622, 16, 9.867, 16, 0, 11.333, -16, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param68", "Segments": [0, 0, 2, 0.333, 0, 0, 1.8, 17, 0, 3.267, 0, 0, 4.733, 17, 0, 6.2, 0, 0, 7.667, 17, 0, 9.133, 0, 0, 10.6, 17, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param69", "Segments": [0, 0, 2, 0.333, 0, 0, 1.067, -16, 0, 2.533, 16, 1, 2.778, 16, 3.022, 10.667, 3.267, 0, 1, 3.511, -10.667, 3.756, -16, 4, -16, 0, 5.467, 16, 1, 5.711, 16, 5.956, 10.667, 6.2, 0, 1, 6.444, -10.667, 6.689, -16, 6.933, -16, 0, 8.4, 16, 1, 8.644, 16, 8.889, 10.667, 9.133, 0, 1, 9.378, -10.667, 9.622, -16, 9.867, -16, 0, 11.333, 16, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param35", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, 10.489, 0, 0.7, -10.218, 0, 0.967, 7.848, 0, 1.267, -11.642, 0, 1.533, 11.825, 0, 1.867, -10.559, 0, 2.167, 4.567, 0, 2.467, -0.901, 0, 2.7, 4.965, 0, 3, -5.302, 0, 3.6, 5.295, 0, 3.933, -5.556, 0, 4.267, 7.806, 0, 4.533, -4.887, 0, 4.867, 6.58, 0, 5.133, -11.154, 0, 5.5, 13.968, 0, 5.833, -16.112, 0, 6.2, 11.048, 0, 6.6, -11.766, 0, 6.9, 13.167, 0, 7.167, -12.77, 0, 7.467, 10.058, 0, 8.033, -4.123, 0, 8.433, 3.561, 0, 9.167, -3.52, 0, 9.6, 4.995, 0, 10, -4.666, 0, 10.5, 4.121, 0, 10.9, -5.359, 0, 11.233, 4.88, 0, 11.6, -8.697, 0, 11.867, 10.284, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param36", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -6.747, 0, 0.633, 11.46, 0, 0.9, -13.212, 0, 1.167, 14.344, 0, 1.433, -16.626, 0, 1.7, 14.828, 0, 2.033, -13.24, 0, 2.3, 8.483, 0, 2.6, -6.308, 0, 2.867, 6.775, 0, 3.167, -5.455, 0, 3.433, 1.588, 0, 3.567, -0.169, 0, 3.8, 4.022, 0, 4.033, -3.321, 0, 4.067, -3.288, 0, 4.2, -6.836, 0, 4.433, 9.63, 0, 4.7, -7.703, 0, 5.033, 10.352, 0, 5.3, -14.507, 0, 5.7, 14.985, 0, 6, -16.931, 0, 6.333, 11.865, 0, 6.767, -14.177, 0, 7.033, 18.185, 0, 7.333, -18.187, 0, 7.633, 12.398, 0, 7.933, -5.108, 0, 8.233, -0.143, 0, 8.367, -0.445, 0, 8.6, 1.874, 0, 8.9, -0.748, 0, 9.1, 0.968, 0, 9.4, -2.494, 0, 9.767, 3.713, 0, 10.133, -3.773, 0, 10.667, 2.411, 0, 11.067, -4.613, 0, 11.4, 5.912, 0, 11.767, -11.019, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param37", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, 14.852, 0, 0.767, -15.068, 0, 1, 7.817, 0, 1.233, -16.248, 0, 1.5, 20.694, 0, 1.867, -19.984, 0, 2.2, 7.779, 0, 2.467, 0.29, 0, 2.7, 9.868, 0, 3, -11.665, 0, 3.567, 9.43, 0, 3.933, -17.472, 0, 4.233, 23.369, 0, 4.5, -15.495, 0, 4.867, 15.512, 0, 5.2, -21.704, 0, 5.467, 15.551, 0, 6, -14.293, 0, 6.3, 20.04, 0, 6.6, -25.087, 0, 6.9, 24.152, 0, 7.2, -21.575, 0, 7.467, 14.769, 0, 8.033, -6.789, 0, 8.467, 6.944, 0, 9.167, -6.976, 0, 9.6, 9.817, 0, 10, -9.156, 0, 10.5, 8.154, 0, 10.9, -11.574, 0, 11.267, 14.011, 0, 11.633, -18.595, 0, 11.9, 16.123, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param39", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -8.588, 0, 0.667, 15.804, 0, 0.9, -18.259, 0, 1.167, 18.49, 0, 1.4, -22.213, 0, 1.7, 22.244, 0, 2.033, -21.683, 0, 2.333, 12.6, 0, 2.633, -9.688, 0, 2.867, 12.2, 0, 3.167, -10.888, 0, 3.433, 3.694, 0, 3.567, 0.696, 0, 3.833, 8.05, 0, 4.133, -21.13, 0, 4.367, 29.102, 0, 4.667, -19.684, 0, 5.067, 19.142, 0, 5.333, -27.926, 0, 5.633, 18.471, 0, 5.9, -4.785, 0, 5.967, -3.807, 0, 6.167, -13.066, 0, 6.467, 22.736, 0, 6.767, -30, 0, 7.033, 30, 2, 7.067, 30, 0, 7.333, -28.448, 0, 7.633, 17.741, 0, 7.933, -6.87, 0, 8.2, -0.658, 0, 8.367, -1.518, 0, 8.6, 3.828, 0, 8.9, -1.441, 0, 9.1, 1.741, 0, 9.4, -4.829, 0, 9.767, 7.224, 0, 10.133, -7.276, 0, 10.667, 4.84, 0, 11.067, -9.966, 0, 11.433, 13.728, 0, 11.767, -22.902, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param38", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, 14.852, 0, 0.767, -15.068, 0, 1, 7.817, 0, 1.233, -16.248, 0, 1.5, 20.694, 0, 1.867, -19.984, 0, 2.2, 7.779, 0, 2.467, 0.29, 0, 2.7, 9.868, 0, 3, -11.665, 0, 3.567, 9.43, 0, 3.933, -17.472, 0, 4.233, 23.369, 0, 4.5, -15.495, 0, 4.867, 15.512, 0, 5.2, -21.704, 0, 5.467, 15.551, 0, 6, -14.293, 0, 6.3, 20.04, 0, 6.6, -25.087, 0, 6.9, 24.152, 0, 7.2, -21.575, 0, 7.467, 14.769, 0, 8.033, -6.789, 0, 8.467, 6.944, 0, 9.167, -6.976, 0, 9.6, 9.817, 0, 10, -9.156, 0, 10.5, 8.154, 0, 10.9, -11.574, 0, 11.267, 14.011, 0, 11.633, -18.595, 0, 11.9, 16.123, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param40", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -8.588, 0, 0.667, 15.804, 0, 0.9, -18.259, 0, 1.167, 18.49, 0, 1.4, -22.213, 0, 1.7, 22.244, 0, 2.033, -21.683, 0, 2.333, 12.6, 0, 2.633, -9.688, 0, 2.867, 12.2, 0, 3.167, -10.888, 0, 3.433, 3.694, 0, 3.567, 0.696, 0, 3.833, 8.05, 0, 4.133, -21.13, 0, 4.367, 29.102, 0, 4.667, -19.684, 0, 5.067, 19.142, 0, 5.333, -27.926, 0, 5.633, 18.471, 0, 5.9, -4.785, 0, 5.967, -3.807, 0, 6.167, -13.066, 0, 6.467, 22.736, 0, 6.767, -30, 0, 7.033, 30, 2, 7.067, 30, 0, 7.333, -28.448, 0, 7.633, 17.741, 0, 7.933, -6.87, 0, 8.2, -0.658, 0, 8.367, -1.518, 0, 8.6, 3.828, 0, 8.9, -1.441, 0, 9.1, 1.741, 0, 9.4, -4.829, 0, 9.767, 7.224, 0, 10.133, -7.276, 0, 10.667, 4.84, 0, 11.067, -9.966, 0, 11.433, 13.728, 0, 11.767, -22.902, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param41", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, 14.852, 0, 0.767, -15.068, 0, 1, 7.817, 0, 1.233, -16.248, 0, 1.5, 20.694, 0, 1.867, -19.984, 0, 2.2, 7.779, 0, 2.467, 0.29, 0, 2.7, 9.868, 0, 3, -11.665, 0, 3.567, 9.43, 0, 3.933, -17.472, 0, 4.233, 23.369, 0, 4.5, -15.495, 0, 4.867, 15.512, 0, 5.2, -21.704, 0, 5.467, 15.551, 0, 6, -14.293, 0, 6.3, 20.04, 0, 6.6, -25.087, 0, 6.9, 24.152, 0, 7.2, -21.575, 0, 7.467, 14.769, 0, 8.033, -6.789, 0, 8.467, 6.944, 0, 9.167, -6.976, 0, 9.6, 9.817, 0, 10, -9.156, 0, 10.5, 8.154, 0, 10.9, -11.574, 0, 11.267, 14.011, 0, 11.633, -18.595, 0, 11.9, 16.123, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param42", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -8.588, 0, 0.667, 15.804, 0, 0.9, -18.259, 0, 1.167, 18.49, 0, 1.4, -22.213, 0, 1.7, 22.244, 0, 2.033, -21.683, 0, 2.333, 12.6, 0, 2.633, -9.688, 0, 2.867, 12.2, 0, 3.167, -10.888, 0, 3.433, 3.694, 0, 3.567, 0.696, 0, 3.833, 8.05, 0, 4.133, -21.13, 0, 4.367, 29.102, 0, 4.667, -19.684, 0, 5.067, 19.142, 0, 5.333, -27.926, 0, 5.633, 18.471, 0, 5.9, -4.785, 0, 5.967, -3.807, 0, 6.167, -13.066, 0, 6.467, 22.736, 0, 6.767, -30, 0, 7.033, 30, 2, 7.067, 30, 0, 7.333, -28.448, 0, 7.633, 17.741, 0, 7.933, -6.87, 0, 8.2, -0.658, 0, 8.367, -1.518, 0, 8.6, 3.828, 0, 8.9, -1.441, 0, 9.1, 1.741, 0, 9.4, -4.829, 0, 9.767, 7.224, 0, 10.133, -7.276, 0, 10.667, 4.84, 0, 11.067, -9.966, 0, 11.433, 13.728, 0, 11.767, -22.902, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param43", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, 14.852, 0, 0.767, -15.068, 0, 1, 7.817, 0, 1.233, -16.248, 0, 1.5, 20.694, 0, 1.867, -19.984, 0, 2.2, 7.779, 0, 2.467, 0.29, 0, 2.7, 9.868, 0, 3, -11.665, 0, 3.567, 9.43, 0, 3.933, -17.472, 0, 4.233, 23.369, 0, 4.5, -15.495, 0, 4.867, 15.512, 0, 5.2, -21.704, 0, 5.467, 15.551, 0, 6, -14.293, 0, 6.3, 20.04, 0, 6.6, -25.087, 0, 6.9, 24.152, 0, 7.2, -21.575, 0, 7.467, 14.769, 0, 8.033, -6.789, 0, 8.467, 6.944, 0, 9.167, -6.976, 0, 9.6, 9.817, 0, 10, -9.156, 0, 10.5, 8.154, 0, 10.9, -11.574, 0, 11.267, 14.011, 0, 11.633, -18.595, 0, 11.9, 16.123, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param44", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -8.588, 0, 0.667, 15.804, 0, 0.9, -18.259, 0, 1.167, 18.49, 0, 1.4, -22.213, 0, 1.7, 22.244, 0, 2.033, -21.683, 0, 2.333, 12.6, 0, 2.633, -9.688, 0, 2.867, 12.2, 0, 3.167, -10.888, 0, 3.433, 3.694, 0, 3.567, 0.696, 0, 3.833, 8.05, 0, 4.133, -21.13, 0, 4.367, 29.102, 0, 4.667, -19.684, 0, 5.067, 19.142, 0, 5.333, -27.926, 0, 5.633, 18.471, 0, 5.9, -4.785, 0, 5.967, -3.807, 0, 6.167, -13.066, 0, 6.467, 22.736, 0, 6.767, -30, 0, 7.033, 30, 2, 7.067, 30, 0, 7.333, -28.448, 0, 7.633, 17.741, 0, 7.933, -6.87, 0, 8.2, -0.658, 0, 8.367, -1.518, 0, 8.6, 3.828, 0, 8.9, -1.441, 0, 9.1, 1.741, 0, 9.4, -4.829, 0, 9.767, 7.224, 0, 10.133, -7.276, 0, 10.667, 4.84, 0, 11.067, -9.966, 0, 11.433, 13.728, 0, 11.767, -22.902, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param45", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, 14.852, 0, 0.767, -15.068, 0, 1, 7.817, 0, 1.233, -16.248, 0, 1.5, 20.694, 0, 1.867, -19.984, 0, 2.2, 7.779, 0, 2.467, 0.29, 0, 2.7, 9.868, 0, 3, -11.665, 0, 3.567, 9.43, 0, 3.933, -17.472, 0, 4.233, 23.369, 0, 4.5, -15.495, 0, 4.867, 15.512, 0, 5.2, -21.704, 0, 5.467, 15.551, 0, 6, -14.293, 0, 6.3, 20.04, 0, 6.6, -25.087, 0, 6.9, 24.152, 0, 7.2, -21.575, 0, 7.467, 14.769, 0, 8.033, -6.789, 0, 8.467, 6.944, 0, 9.167, -6.976, 0, 9.6, 9.817, 0, 10, -9.156, 0, 10.5, 8.154, 0, 10.9, -11.574, 0, 11.267, 14.011, 0, 11.633, -18.595, 0, 11.9, 16.123, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param46", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -8.588, 0, 0.667, 15.804, 0, 0.9, -18.259, 0, 1.167, 18.49, 0, 1.4, -22.213, 0, 1.7, 22.244, 0, 2.033, -21.683, 0, 2.333, 12.6, 0, 2.633, -9.688, 0, 2.867, 12.2, 0, 3.167, -10.888, 0, 3.433, 3.694, 0, 3.567, 0.696, 0, 3.833, 8.05, 0, 4.133, -21.13, 0, 4.367, 29.102, 0, 4.667, -19.684, 0, 5.067, 19.142, 0, 5.333, -27.926, 0, 5.633, 18.471, 0, 5.9, -4.785, 0, 5.967, -3.807, 0, 6.167, -13.066, 0, 6.467, 22.736, 0, 6.767, -30, 0, 7.033, 30, 2, 7.067, 30, 0, 7.333, -28.448, 0, 7.633, 17.741, 0, 7.933, -6.87, 0, 8.2, -0.658, 0, 8.367, -1.518, 0, 8.6, 3.828, 0, 8.9, -1.441, 0, 9.1, 1.741, 0, 9.4, -4.829, 0, 9.767, 7.224, 0, 10.133, -7.276, 0, 10.667, 4.84, 0, 11.067, -9.966, 0, 11.433, 13.728, 0, 11.767, -22.902, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param62", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, 14.852, 0, 0.767, -15.068, 0, 1, 7.817, 0, 1.233, -16.248, 0, 1.5, 20.694, 0, 1.867, -19.984, 0, 2.2, 7.779, 0, 2.467, 0.29, 0, 2.7, 9.868, 0, 3, -11.665, 0, 3.567, 9.43, 0, 3.933, -17.472, 0, 4.233, 23.369, 0, 4.5, -15.495, 0, 4.867, 15.512, 0, 5.2, -21.704, 0, 5.467, 15.551, 0, 6, -14.293, 0, 6.3, 20.04, 0, 6.6, -25.087, 0, 6.9, 24.152, 0, 7.2, -21.575, 0, 7.467, 14.769, 0, 8.033, -6.789, 0, 8.467, 6.944, 0, 9.167, -6.976, 0, 9.6, 9.817, 0, 10, -9.156, 0, 10.5, 8.154, 0, 10.9, -11.574, 0, 11.267, 14.011, 0, 11.633, -18.595, 0, 11.9, 16.123, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param63", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -8.588, 0, 0.667, 15.804, 0, 0.9, -18.259, 0, 1.167, 18.49, 0, 1.4, -22.213, 0, 1.7, 22.244, 0, 2.033, -21.683, 0, 2.333, 12.6, 0, 2.633, -9.688, 0, 2.867, 12.2, 0, 3.167, -10.888, 0, 3.433, 3.694, 0, 3.567, 0.696, 0, 3.833, 8.05, 0, 4.133, -21.13, 0, 4.367, 29.102, 0, 4.667, -19.684, 0, 5.067, 19.142, 0, 5.333, -27.926, 0, 5.633, 18.471, 0, 5.9, -4.785, 0, 5.967, -3.807, 0, 6.167, -13.066, 0, 6.467, 22.736, 0, 6.767, -30, 0, 7.033, 30, 2, 7.067, 30, 0, 7.333, -28.448, 0, 7.633, 17.741, 0, 7.933, -6.87, 0, 8.2, -0.658, 0, 8.367, -1.518, 0, 8.6, 3.828, 0, 8.9, -1.441, 0, 9.1, 1.741, 0, 9.4, -4.829, 0, 9.767, 7.224, 0, 10.133, -7.276, 0, 10.667, 4.84, 0, 11.067, -9.966, 0, 11.433, 13.728, 0, 11.767, -22.902, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param64", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, 14.852, 0, 0.767, -15.068, 0, 1, 7.817, 0, 1.233, -16.248, 0, 1.5, 20.694, 0, 1.867, -19.984, 0, 2.2, 7.779, 0, 2.467, 0.29, 0, 2.7, 9.868, 0, 3, -11.665, 0, 3.567, 9.43, 0, 3.933, -17.472, 0, 4.233, 23.369, 0, 4.5, -15.495, 0, 4.867, 15.512, 0, 5.2, -21.704, 0, 5.467, 15.551, 0, 6, -14.293, 0, 6.3, 20.04, 0, 6.6, -25.087, 0, 6.9, 24.152, 0, 7.2, -21.575, 0, 7.467, 14.769, 0, 8.033, -6.789, 0, 8.467, 6.944, 0, 9.167, -6.976, 0, 9.6, 9.817, 0, 10, -9.156, 0, 10.5, 8.154, 0, 10.9, -11.574, 0, 11.267, 14.011, 0, 11.633, -18.595, 0, 11.9, 16.123, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param65", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -8.588, 0, 0.667, 15.804, 0, 0.9, -18.259, 0, 1.167, 18.49, 0, 1.4, -22.213, 0, 1.7, 22.244, 0, 2.033, -21.683, 0, 2.333, 12.6, 0, 2.633, -9.688, 0, 2.867, 12.2, 0, 3.167, -10.888, 0, 3.433, 3.694, 0, 3.567, 0.696, 0, 3.833, 8.05, 0, 4.133, -21.13, 0, 4.367, 29.102, 0, 4.667, -19.684, 0, 5.067, 19.142, 0, 5.333, -27.926, 0, 5.633, 18.471, 0, 5.9, -4.785, 0, 5.967, -3.807, 0, 6.167, -13.066, 0, 6.467, 22.736, 0, 6.767, -30, 0, 7.033, 30, 2, 7.067, 30, 0, 7.333, -28.448, 0, 7.633, 17.741, 0, 7.933, -6.87, 0, 8.2, -0.658, 0, 8.367, -1.518, 0, 8.6, 3.828, 0, 8.9, -1.441, 0, 9.1, 1.741, 0, 9.4, -4.829, 0, 9.767, 7.224, 0, 10.133, -7.276, 0, 10.667, 4.84, 0, 11.067, -9.966, 0, 11.433, 13.728, 0, 11.767, -22.902, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param72", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, 14.852, 0, 0.767, -15.068, 0, 1, 7.817, 0, 1.233, -16.248, 0, 1.5, 20.694, 0, 1.867, -19.984, 0, 2.2, 7.779, 0, 2.467, 0.29, 0, 2.7, 9.868, 0, 3, -11.665, 0, 3.567, 9.43, 0, 3.933, -17.472, 0, 4.233, 23.369, 0, 4.5, -15.495, 0, 4.867, 15.512, 0, 5.2, -21.704, 0, 5.467, 15.551, 0, 6, -14.293, 0, 6.3, 20.04, 0, 6.6, -25.087, 0, 6.9, 24.152, 0, 7.2, -21.575, 0, 7.467, 14.769, 0, 8.033, -6.789, 0, 8.467, 6.944, 0, 9.167, -6.976, 0, 9.6, 9.817, 0, 10, -9.156, 0, 10.5, 8.154, 0, 10.9, -11.574, 0, 11.267, 14.011, 0, 11.633, -18.595, 0, 11.9, 16.123, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param73", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -8.588, 0, 0.667, 15.804, 0, 0.9, -18.259, 0, 1.167, 18.49, 0, 1.4, -22.213, 0, 1.7, 22.244, 0, 2.033, -21.683, 0, 2.333, 12.6, 0, 2.633, -9.688, 0, 2.867, 12.2, 0, 3.167, -10.888, 0, 3.433, 3.694, 0, 3.567, 0.696, 0, 3.833, 8.05, 0, 4.133, -21.13, 0, 4.367, 29.102, 0, 4.667, -19.684, 0, 5.067, 19.142, 0, 5.333, -27.926, 0, 5.633, 18.471, 0, 5.9, -4.785, 0, 5.967, -3.807, 0, 6.167, -13.066, 0, 6.467, 22.736, 0, 6.767, -30, 0, 7.033, 30, 2, 7.067, 30, 0, 7.333, -28.448, 0, 7.633, 17.741, 0, 7.933, -6.87, 0, 8.2, -0.658, 0, 8.367, -1.518, 0, 8.6, 3.828, 0, 8.9, -1.441, 0, 9.1, 1.741, 0, 9.4, -4.829, 0, 9.767, 7.224, 0, 10.133, -7.276, 0, 10.667, 4.84, 0, 11.067, -9.966, 0, 11.433, 13.728, 0, 11.767, -22.902, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param74", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, 14.852, 0, 0.767, -15.068, 0, 1, 7.817, 0, 1.233, -16.248, 0, 1.5, 20.694, 0, 1.867, -19.984, 0, 2.2, 7.779, 0, 2.467, 0.29, 0, 2.7, 9.868, 0, 3, -11.665, 0, 3.567, 9.43, 0, 3.933, -17.472, 0, 4.233, 23.369, 0, 4.5, -15.495, 0, 4.867, 15.512, 0, 5.2, -21.704, 0, 5.467, 15.551, 0, 6, -14.293, 0, 6.3, 20.04, 0, 6.6, -25.087, 0, 6.9, 24.152, 0, 7.2, -21.575, 0, 7.467, 14.769, 0, 8.033, -6.789, 0, 8.467, 6.944, 0, 9.167, -6.976, 0, 9.6, 9.817, 0, 10, -9.156, 0, 10.5, 8.154, 0, 10.9, -11.574, 0, 11.267, 14.011, 0, 11.633, -18.595, 0, 11.9, 16.123, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param75", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -8.588, 0, 0.667, 15.804, 0, 0.9, -18.259, 0, 1.167, 18.49, 0, 1.4, -22.213, 0, 1.7, 22.244, 0, 2.033, -21.683, 0, 2.333, 12.6, 0, 2.633, -9.688, 0, 2.867, 12.2, 0, 3.167, -10.888, 0, 3.433, 3.694, 0, 3.567, 0.696, 0, 3.833, 8.05, 0, 4.133, -21.13, 0, 4.367, 29.102, 0, 4.667, -19.684, 0, 5.067, 19.142, 0, 5.333, -27.926, 0, 5.633, 18.471, 0, 5.9, -4.785, 0, 5.967, -3.807, 0, 6.167, -13.066, 0, 6.467, 22.736, 0, 6.767, -30, 0, 7.033, 30, 2, 7.067, 30, 0, 7.333, -28.448, 0, 7.633, 17.741, 0, 7.933, -6.87, 0, 8.2, -0.658, 0, 8.367, -1.518, 0, 8.6, 3.828, 0, 8.9, -1.441, 0, 9.1, 1.741, 0, 9.4, -4.829, 0, 9.767, 7.224, 0, 10.133, -7.276, 0, 10.667, 4.84, 0, 11.067, -9.966, 0, 11.433, 13.728, 0, 11.767, -22.902, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param53", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, 14.852, 0, 0.767, -15.068, 0, 1, 7.817, 0, 1.233, -16.248, 0, 1.5, 20.694, 0, 1.867, -19.984, 0, 2.2, 7.779, 0, 2.467, 0.29, 0, 2.7, 9.868, 0, 3, -11.665, 0, 3.567, 9.43, 0, 3.933, -17.472, 0, 4.233, 23.369, 0, 4.5, -15.495, 0, 4.867, 15.512, 0, 5.2, -21.704, 0, 5.467, 15.551, 0, 6, -14.293, 0, 6.3, 20.04, 0, 6.6, -25.087, 0, 6.9, 24.152, 0, 7.2, -21.575, 0, 7.467, 14.769, 0, 8.033, -6.789, 0, 8.467, 6.944, 0, 9.167, -6.976, 0, 9.6, 9.817, 0, 10, -9.156, 0, 10.5, 8.154, 0, 10.9, -11.574, 0, 11.267, 14.011, 0, 11.633, -18.595, 0, 11.9, 16.123, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param54", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -8.588, 0, 0.667, 15.804, 0, 0.9, -18.259, 0, 1.167, 18.49, 0, 1.4, -22.213, 0, 1.7, 22.244, 0, 2.033, -21.683, 0, 2.333, 12.6, 0, 2.633, -9.688, 0, 2.867, 12.2, 0, 3.167, -10.888, 0, 3.433, 3.694, 0, 3.567, 0.696, 0, 3.833, 8.05, 0, 4.133, -21.13, 0, 4.367, 29.102, 0, 4.667, -19.684, 0, 5.067, 19.142, 0, 5.333, -27.926, 0, 5.633, 18.471, 0, 5.9, -4.785, 0, 5.967, -3.807, 0, 6.167, -13.066, 0, 6.467, 22.736, 0, 6.767, -30, 0, 7.033, 30, 2, 7.067, 30, 0, 7.333, -28.448, 0, 7.633, 17.741, 0, 7.933, -6.87, 0, 8.2, -0.658, 0, 8.367, -1.518, 0, 8.6, 3.828, 0, 8.9, -1.441, 0, 9.1, 1.741, 0, 9.4, -4.829, 0, 9.767, 7.224, 0, 10.133, -7.276, 0, 10.667, 4.84, 0, 11.067, -9.966, 0, 11.433, 13.728, 0, 11.767, -22.902, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param81", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, 14.852, 0, 0.767, -15.068, 0, 1, 7.817, 0, 1.233, -16.248, 0, 1.5, 20.694, 0, 1.867, -19.984, 0, 2.2, 7.779, 0, 2.467, 0.29, 0, 2.7, 9.868, 0, 3, -11.665, 0, 3.567, 9.43, 0, 3.933, -17.472, 0, 4.233, 23.369, 0, 4.5, -15.495, 0, 4.867, 15.512, 0, 5.2, -21.704, 0, 5.467, 15.551, 0, 6, -14.293, 0, 6.3, 20.04, 0, 6.6, -25.087, 0, 6.9, 24.152, 0, 7.2, -21.575, 0, 7.467, 14.769, 0, 8.033, -6.789, 0, 8.467, 6.944, 0, 9.167, -6.976, 0, 9.6, 9.817, 0, 10, -9.156, 0, 10.5, 8.154, 0, 10.9, -11.574, 0, 11.267, 14.011, 0, 11.633, -18.595, 0, 11.9, 16.123, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param82", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -8.588, 0, 0.667, 15.804, 0, 0.9, -18.259, 0, 1.167, 18.49, 0, 1.4, -22.213, 0, 1.7, 22.244, 0, 2.033, -21.683, 0, 2.333, 12.6, 0, 2.633, -9.688, 0, 2.867, 12.2, 0, 3.167, -10.888, 0, 3.433, 3.694, 0, 3.567, 0.696, 0, 3.833, 8.05, 0, 4.133, -21.13, 0, 4.367, 29.102, 0, 4.667, -19.684, 0, 5.067, 19.142, 0, 5.333, -27.926, 0, 5.633, 18.471, 0, 5.9, -4.785, 0, 5.967, -3.807, 0, 6.167, -13.066, 0, 6.467, 22.736, 0, 6.767, -30, 0, 7.033, 30, 2, 7.067, 30, 0, 7.333, -28.448, 0, 7.633, 17.741, 0, 7.933, -6.87, 0, 8.2, -0.658, 0, 8.367, -1.518, 0, 8.6, 3.828, 0, 8.9, -1.441, 0, 9.1, 1.741, 0, 9.4, -4.829, 0, 9.767, 7.224, 0, 10.133, -7.276, 0, 10.667, 4.84, 0, 11.067, -9.966, 0, 11.433, 13.728, 0, 11.767, -22.902, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param70", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, 14.852, 0, 0.767, -15.068, 0, 1, 7.817, 0, 1.233, -16.248, 0, 1.5, 20.694, 0, 1.867, -19.984, 0, 2.2, 7.779, 0, 2.467, 0.29, 0, 2.7, 9.868, 0, 3, -11.665, 0, 3.567, 9.43, 0, 3.933, -17.472, 0, 4.233, 23.369, 0, 4.5, -15.495, 0, 4.867, 15.512, 0, 5.2, -21.704, 0, 5.467, 15.551, 0, 6, -14.293, 0, 6.3, 20.04, 0, 6.6, -25.087, 0, 6.9, 24.152, 0, 7.2, -21.575, 0, 7.467, 14.769, 0, 8.033, -6.789, 0, 8.467, 6.944, 0, 9.167, -6.976, 0, 9.6, 9.817, 0, 10, -9.156, 0, 10.5, 8.154, 0, 10.9, -11.574, 0, 11.267, 14.011, 0, 11.633, -18.595, 0, 11.9, 16.123, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param71", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -8.588, 0, 0.667, 15.804, 0, 0.9, -18.259, 0, 1.167, 18.49, 0, 1.4, -22.213, 0, 1.7, 22.244, 0, 2.033, -21.683, 0, 2.333, 12.6, 0, 2.633, -9.688, 0, 2.867, 12.2, 0, 3.167, -10.888, 0, 3.433, 3.694, 0, 3.567, 0.696, 0, 3.833, 8.05, 0, 4.133, -21.13, 0, 4.367, 29.102, 0, 4.667, -19.684, 0, 5.067, 19.142, 0, 5.333, -27.926, 0, 5.633, 18.471, 0, 5.9, -4.785, 0, 5.967, -3.807, 0, 6.167, -13.066, 0, 6.467, 22.736, 0, 6.767, -30, 0, 7.033, 30, 2, 7.067, 30, 0, 7.333, -28.448, 0, 7.633, 17.741, 0, 7.933, -6.87, 0, 8.2, -0.658, 0, 8.367, -1.518, 0, 8.6, 3.828, 0, 8.9, -1.441, 0, 9.1, 1.741, 0, 9.4, -4.829, 0, 9.767, 7.224, 0, 10.133, -7.276, 0, 10.667, 4.84, 0, 11.067, -9.966, 0, 11.433, 13.728, 0, 11.767, -22.902, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param77", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, 14.852, 0, 0.767, -15.068, 0, 1, 7.817, 0, 1.233, -16.248, 0, 1.5, 20.694, 0, 1.867, -19.984, 0, 2.2, 7.779, 0, 2.467, 0.29, 0, 2.7, 9.868, 0, 3, -11.665, 0, 3.567, 9.43, 0, 3.933, -17.472, 0, 4.233, 23.369, 0, 4.5, -15.495, 0, 4.867, 15.512, 0, 5.2, -21.704, 0, 5.467, 15.551, 0, 6, -14.293, 0, 6.3, 20.04, 0, 6.6, -25.087, 0, 6.9, 24.152, 0, 7.2, -21.575, 0, 7.467, 14.769, 0, 8.033, -6.789, 0, 8.467, 6.944, 0, 9.167, -6.976, 0, 9.6, 9.817, 0, 10, -9.156, 0, 10.5, 8.154, 0, 10.9, -11.574, 0, 11.267, 14.011, 0, 11.633, -18.595, 0, 11.9, 16.123, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param78", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -8.588, 0, 0.667, 15.804, 0, 0.9, -18.259, 0, 1.167, 18.49, 0, 1.4, -22.213, 0, 1.7, 22.244, 0, 2.033, -21.683, 0, 2.333, 12.6, 0, 2.633, -9.688, 0, 2.867, 12.2, 0, 3.167, -10.888, 0, 3.433, 3.694, 0, 3.567, 0.696, 0, 3.833, 8.05, 0, 4.133, -21.13, 0, 4.367, 29.102, 0, 4.667, -19.684, 0, 5.067, 19.142, 0, 5.333, -27.926, 0, 5.633, 18.471, 0, 5.9, -4.785, 0, 5.967, -3.807, 0, 6.167, -13.066, 0, 6.467, 22.736, 0, 6.767, -30, 0, 7.033, 30, 2, 7.067, 30, 0, 7.333, -28.448, 0, 7.633, 17.741, 0, 7.933, -6.87, 0, 8.2, -0.658, 0, 8.367, -1.518, 0, 8.6, 3.828, 0, 8.9, -1.441, 0, 9.1, 1.741, 0, 9.4, -4.829, 0, 9.767, 7.224, 0, 10.133, -7.276, 0, 10.667, 4.84, 0, 11.067, -9.966, 0, 11.433, 13.728, 0, 11.767, -22.902, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param79", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, 14.852, 0, 0.767, -15.068, 0, 1, 7.817, 0, 1.233, -16.248, 0, 1.5, 20.694, 0, 1.867, -19.984, 0, 2.2, 7.779, 0, 2.467, 0.29, 0, 2.7, 9.868, 0, 3, -11.665, 0, 3.567, 9.43, 0, 3.933, -17.472, 0, 4.233, 23.369, 0, 4.5, -15.495, 0, 4.867, 15.512, 0, 5.2, -21.704, 0, 5.467, 15.551, 0, 6, -14.293, 0, 6.3, 20.04, 0, 6.6, -25.087, 0, 6.9, 24.152, 0, 7.2, -21.575, 0, 7.467, 14.769, 0, 8.033, -6.789, 0, 8.467, 6.944, 0, 9.167, -6.976, 0, 9.6, 9.817, 0, 10, -9.156, 0, 10.5, 8.154, 0, 10.9, -11.574, 0, 11.267, 14.011, 0, 11.633, -18.595, 0, 11.9, 16.123, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param80", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -8.588, 0, 0.667, 15.804, 0, 0.9, -18.259, 0, 1.167, 18.49, 0, 1.4, -22.213, 0, 1.7, 22.244, 0, 2.033, -21.683, 0, 2.333, 12.6, 0, 2.633, -9.688, 0, 2.867, 12.2, 0, 3.167, -10.888, 0, 3.433, 3.694, 0, 3.567, 0.696, 0, 3.833, 8.05, 0, 4.133, -21.13, 0, 4.367, 29.102, 0, 4.667, -19.684, 0, 5.067, 19.142, 0, 5.333, -27.926, 0, 5.633, 18.471, 0, 5.9, -4.785, 0, 5.967, -3.807, 0, 6.167, -13.066, 0, 6.467, 22.736, 0, 6.767, -30, 0, 7.033, 30, 2, 7.067, 30, 0, 7.333, -28.448, 0, 7.633, 17.741, 0, 7.933, -6.87, 0, 8.2, -0.658, 0, 8.367, -1.518, 0, 8.6, 3.828, 0, 8.9, -1.441, 0, 9.1, 1.741, 0, 9.4, -4.829, 0, 9.767, 7.224, 0, 10.133, -7.276, 0, 10.667, 4.84, 0, 11.067, -9.966, 0, 11.433, 13.728, 0, 11.767, -22.902, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param294", "Segments": [0, 1, 2, 0.333, 1, 0, 0.4, 0, 2, 11.933, 0, 0, 12.033, 1]}, {"Target": "Parameter", "Id": "Param47", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, 17.099, 0, 0.767, -20.75, 0, 1, 13.618, 0, 1.233, -19.149, 0, 1.5, 19.427, 0, 1.9, -23.833, 0, 2.167, 9.466, 0, 2.433, 0.135, 0, 2.667, 15.683, 0, 2.967, -16.095, 0, 3.533, 9.427, 0, 3.933, -20.507, 0, 4.2, 24.171, 0, 4.467, -16.614, 0, 4.9, 20.396, 0, 5.2, -25.753, 0, 5.467, 16.697, 0, 6, -19.362, 0, 6.3, 24.515, 0, 6.6, -28.204, 0, 6.9, 23.312, 0, 7.2, -19.292, 0, 7.5, 15.005, 0, 8.033, -11.05, 0, 8.5, 14.286, 0, 8.933, -13.555, 0, 9.4, 12.995, 0, 9.833, -16.512, 0, 10.133, 13.04, 0, 10.733, -10.031, 0, 11.033, 11.611, 0, 11.633, -14.768, 0, 11.9, 9.466, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param48", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, -9.327, 0, 0.667, 20.314, 0, 0.9, -25.675, 0, 1.167, 24.484, 0, 1.4, -25.469, 0, 1.7, 22.203, 0, 2.033, -27.131, 0, 2.333, 14.708, 0, 2.6, -12.565, 0, 2.867, 16.997, 0, 3.133, -15.261, 0, 3.433, 5.2, 0, 3.6, 2.445, 0, 3.867, 6.908, 0, 4.1, -24.229, 0, 4.333, 29.759, 0, 4.667, -20.202, 0, 5.067, 24.148, 0, 5.333, -30, 2, 5.367, -30, 0, 5.633, 19.932, 0, 5.867, -3.771, 0, 5.967, -1.673, 0, 6.167, -18.369, 0, 6.467, 27.946, 0, 6.733, -30, 2, 6.8, -30, 0, 7.067, 30, 0, 7.333, -26.078, 0, 7.633, 16.875, 0, 7.933, -5.187, 0, 8.1, -2.621, 0, 8.3, -4.392, 0, 8.667, 8.901, 0, 9.067, -8.411, 0, 9.567, 7.721, 0, 10, -17.431, 0, 10.3, 14.605, 0, 10.567, -6.592, 0, 10.767, 1.548, 0, 10.933, -8.479, 0, 11.2, 10.201, 0, 11.467, -4.083, 0, 11.6, 2.629, 0, 11.8, -5.187, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param56", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.533, 5.953, 0, 0.833, -6.347, 0, 1.1, 0.299, 0, 1.267, -3.595, 0, 1.6, 7.984, 0, 1.967, -10.385, 0, 2.633, 10.062, 0, 3.133, -11.09, 0, 3.633, 10.131, 0, 4.033, -9.877, 0, 4.3, 2.864, 0, 4.667, -2.768, 0, 5, 7.989, 0, 5.267, -7.073, 0, 5.7, 5.433, 0, 6.067, -9.786, 0, 6.433, 9.573, 0, 6.7, -6.393, 0, 7, 3.675, 0, 7.267, -3.64, 0, 7.667, 5.224, 0, 8.167, -8.675, 0, 8.633, 10.632, 0, 9.1, -10.77, 0, 9.567, 10.434, 0, 9.967, -8.347, 0, 10.433, 5.397, 0, 10.833, -6.766, 0, 11.267, 5.879, 0, 11.7, -4.91, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param57", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, -4.011, 0, 0.7, 9.115, 0, 1, -8.961, 0, 1.233, 4.963, 0, 1.467, -7.584, 0, 1.8, 11.747, 0, 2.167, -11.685, 0, 2.5, 1.53, 0, 2.6, 1.171, 0, 2.867, 10.334, 0, 3.3, -9.432, 0, 3.867, 11.154, 0, 4.167, -14.845, 0, 4.467, 8.555, 0, 4.833, -8.078, 0, 5.133, 13.668, 0, 5.467, -10.977, 0, 5.9, 7.943, 0, 6.233, -14.353, 0, 6.567, 16.848, 0, 6.867, -13.379, 0, 7.167, 9.654, 0, 7.467, -7.815, 0, 7.867, 6.772, 0, 8.367, -9.4, 0, 8.833, 10.97, 0, 9.3, -10.806, 0, 9.767, 11.566, 0, 10.133, -10.116, 0, 10.633, 5.526, 0, 11, -8.387, 0, 11.433, 5.98, 0, 11.867, -6.385, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param58", "Segments": [0, 0, 2, 0.333, 0, 2, 0.4, 0, 0, 0.6, -4.299, 0, 0.833, 11.143, 0, 1.1, -12.716, 0, 1.367, 10.119, 0, 1.6, -10.632, 0, 1.9, 15.361, 0, 2.233, -16.274, 0, 2.567, 6.158, 0, 2.8, -0.023, 0, 3, 8.298, 0, 3.367, -11.413, 0, 3.9, 11.209, 0, 4.267, -18.75, 0, 4.6, 16.097, 0, 4.9, -13.899, 0, 5.233, 17.903, 0, 5.567, -17.845, 0, 5.933, 13.57, 0, 6.333, -17.677, 0, 6.667, 21.607, 0, 6.967, -20.556, 0, 7.3, 17.298, 0, 7.567, -14.308, 0, 7.933, 11.412, 0, 8.433, -9.704, 0, 8.9, 11.972, 0, 9.367, -12.083, 0, 9.833, 12.905, 0, 10.2, -14.628, 0, 10.6, 9.446, 0, 11.1, -10.143, 0, 11.467, 8.782, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param59", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.533, 5.953, 0, 0.833, -6.347, 0, 1.1, 0.299, 0, 1.267, -3.595, 0, 1.6, 7.984, 0, 1.967, -10.385, 0, 2.633, 10.062, 0, 3.133, -11.09, 0, 3.633, 10.131, 0, 4.033, -9.877, 0, 4.3, 2.864, 0, 4.667, -2.768, 0, 5, 7.989, 0, 5.267, -7.073, 0, 5.7, 5.433, 0, 6.067, -9.786, 0, 6.433, 9.573, 0, 6.7, -6.393, 0, 7, 3.675, 0, 7.267, -3.64, 0, 7.667, 5.224, 0, 8.167, -8.675, 0, 8.633, 10.632, 0, 9.1, -10.77, 0, 9.567, 10.434, 0, 9.967, -8.347, 0, 10.433, 5.397, 0, 10.833, -6.766, 0, 11.267, 5.879, 0, 11.7, -8.681, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param60", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, -4.011, 0, 0.7, 9.115, 0, 1, -8.961, 0, 1.233, 4.963, 0, 1.467, -7.584, 0, 1.8, 11.747, 0, 2.167, -11.685, 0, 2.5, 1.53, 0, 2.6, 1.171, 0, 2.867, 10.334, 0, 3.3, -9.432, 0, 3.867, 11.154, 0, 4.167, -14.845, 0, 4.467, 8.555, 0, 4.833, -8.078, 0, 5.133, 13.668, 0, 5.467, -10.977, 0, 5.9, 7.943, 0, 6.233, -14.353, 0, 6.567, 16.848, 0, 6.867, -13.379, 0, 7.167, 9.654, 0, 7.467, -7.815, 0, 7.867, 6.772, 0, 8.367, -9.4, 0, 8.833, 10.97, 0, 9.3, -10.806, 0, 9.767, 11.566, 0, 10.133, -10.116, 0, 10.633, 5.526, 0, 11, -8.387, 0, 11.433, 5.98, 0, 11.5, 5.871, 0, 11.533, 5.938, 0, 11.867, -11.056, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param61", "Segments": [0, 0, 2, 0.333, 0, 2, 0.4, 0, 0, 0.6, -4.299, 0, 0.833, 11.143, 0, 1.1, -12.716, 0, 1.367, 10.119, 0, 1.6, -10.632, 0, 1.9, 15.361, 0, 2.233, -16.274, 0, 2.567, 6.158, 0, 2.8, -0.023, 0, 3, 8.298, 0, 3.367, -11.413, 0, 3.9, 11.209, 0, 4.267, -18.75, 0, 4.6, 16.097, 0, 4.9, -13.899, 0, 5.233, 17.903, 0, 5.567, -17.845, 0, 5.933, 13.57, 0, 6.333, -17.677, 0, 6.667, 21.607, 0, 6.967, -20.556, 0, 7.3, 17.298, 0, 7.567, -14.308, 0, 7.933, 11.412, 0, 8.433, -9.704, 0, 8.9, 11.972, 0, 9.367, -12.083, 0, 9.833, 12.905, 0, 10.2, -14.628, 0, 10.6, 9.446, 0, 11.1, -10.143, 0, 11.467, 8.782, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param49", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, 17.099, 0, 0.767, -20.75, 0, 1, 13.618, 0, 1.233, -19.149, 0, 1.5, 19.427, 0, 1.9, -23.833, 0, 2.167, 9.466, 0, 2.433, 0.135, 0, 2.667, 15.683, 0, 2.967, -16.095, 0, 3.533, 9.427, 0, 3.933, -20.507, 0, 4.2, 24.171, 0, 4.467, -16.614, 0, 4.9, 20.396, 0, 5.2, -25.753, 0, 5.467, 16.697, 0, 6, -19.362, 0, 6.3, 24.515, 0, 6.6, -28.204, 0, 6.9, 23.312, 0, 7.2, -19.292, 0, 7.5, 15.005, 0, 8.033, -11.05, 0, 8.5, 14.286, 0, 8.933, -13.555, 0, 9.4, 12.995, 0, 9.833, -16.512, 0, 10.133, 13.04, 0, 10.733, -10.031, 0, 11.033, 11.611, 0, 11.633, -14.768, 0, 11.9, 16.485, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param50", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, -9.327, 0, 0.667, 20.314, 0, 0.9, -25.675, 0, 1.167, 24.484, 0, 1.4, -25.469, 0, 1.7, 22.203, 0, 2.033, -27.131, 0, 2.333, 14.708, 0, 2.6, -12.565, 0, 2.867, 16.997, 0, 3.133, -15.261, 0, 3.433, 5.2, 0, 3.6, 2.445, 0, 3.867, 6.908, 0, 4.1, -24.229, 0, 4.333, 29.759, 0, 4.667, -20.202, 0, 5.067, 24.148, 0, 5.333, -30, 2, 5.367, -30, 0, 5.633, 19.932, 0, 5.867, -3.771, 0, 5.967, -1.673, 0, 6.167, -18.369, 0, 6.467, 27.946, 0, 6.733, -30, 2, 6.8, -30, 0, 7.067, 30, 0, 7.333, -26.078, 0, 7.633, 16.875, 0, 7.933, -5.187, 0, 8.1, -2.621, 0, 8.3, -4.392, 0, 8.667, 8.901, 0, 9.067, -8.411, 0, 9.567, 7.721, 0, 10, -17.431, 0, 10.3, 14.605, 0, 10.567, -6.592, 0, 10.767, 1.548, 0, 10.933, -8.479, 0, 11.2, 10.201, 0, 11.467, -4.083, 0, 11.6, 2.629, 0, 11.8, -14.531, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param51", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, 17.099, 0, 0.767, -20.75, 0, 1, 13.618, 0, 1.233, -19.149, 0, 1.5, 19.427, 0, 1.9, -23.833, 0, 2.167, 9.466, 0, 2.433, 0.135, 0, 2.667, 15.683, 0, 2.967, -16.095, 0, 3.533, 9.427, 0, 3.933, -20.507, 0, 4.2, 24.171, 0, 4.467, -16.614, 0, 4.9, 20.396, 0, 5.2, -25.753, 0, 5.467, 16.697, 0, 6, -19.362, 0, 6.3, 24.515, 0, 6.6, -28.204, 0, 6.9, 23.312, 0, 7.2, -19.292, 0, 7.5, 15.005, 0, 8.033, -11.05, 0, 8.5, 14.286, 0, 8.933, -13.555, 0, 9.4, 12.995, 0, 9.833, -16.512, 0, 10.133, 13.04, 0, 10.733, -10.031, 0, 11.033, 11.611, 0, 11.633, -14.768, 0, 11.9, 16.485, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param52", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, -9.327, 0, 0.667, 20.314, 0, 0.9, -25.675, 0, 1.167, 24.484, 0, 1.4, -25.469, 0, 1.7, 22.203, 0, 2.033, -27.131, 0, 2.333, 14.708, 0, 2.6, -12.565, 0, 2.867, 16.997, 0, 3.133, -15.261, 0, 3.433, 5.2, 0, 3.6, 2.445, 0, 3.867, 6.908, 0, 4.1, -24.229, 0, 4.333, 29.759, 0, 4.667, -20.202, 0, 5.067, 24.148, 0, 5.333, -30, 2, 5.367, -30, 0, 5.633, 19.932, 0, 5.867, -3.771, 0, 5.967, -1.673, 0, 6.167, -18.369, 0, 6.467, 27.946, 0, 6.733, -30, 2, 6.8, -30, 0, 7.067, 30, 0, 7.333, -26.078, 0, 7.633, 16.875, 0, 7.933, -5.187, 0, 8.1, -2.621, 0, 8.3, -4.392, 0, 8.667, 8.901, 0, 9.067, -8.411, 0, 9.567, 7.721, 0, 10, -17.431, 0, 10.3, 14.605, 0, 10.567, -6.592, 0, 10.767, 1.548, 0, 10.933, -8.479, 0, 11.2, 10.201, 0, 11.467, -4.083, 0, 11.6, 2.629, 0, 11.8, -14.531, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation63", "Segments": [0, -0.675, 2, 0.333, -0.675, 1, 0.344, -0.675, 0.356, -0.763, 0.367, 0, 1, 0.411, 3.051, 0.456, 5.7, 0.5, 5.7, 0, 0.767, -6.917, 0, 1, 4.539, 0, 1.233, -6.383, 0, 1.5, 6.476, 0, 1.9, -7.944, 0, 2.167, 3.155, 0, 2.433, 0.045, 0, 2.667, 5.228, 0, 2.967, -5.365, 0, 3.533, 3.142, 0, 3.933, -6.836, 0, 4.2, 8.057, 0, 4.467, -5.538, 0, 4.9, 6.799, 0, 5.2, -8.584, 0, 5.467, 5.566, 0, 6, -6.454, 0, 6.3, 8.172, 0, 6.6, -9.401, 0, 6.9, 7.771, 0, 7.2, -6.431, 0, 7.5, 5.002, 0, 8.033, -3.683, 0, 8.5, 4.762, 0, 8.933, -4.519, 0, 9.4, 4.332, 0, 9.833, -5.504, 0, 10.133, 4.347, 0, 10.733, -3.344, 0, 11.033, 3.87, 0, 11.633, -4.923, 0, 11.9, 5.495, 0, 12.033, -0.675]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation64", "Segments": [0, 2.951, 2, 0.333, 2.951, 1, 0.344, 2.951, 0.356, 0.874, 0.367, 0, 1, 0.411, -3.496, 0.456, -4.663, 0.5, -4.663, 0, 0.667, 10.157, 0, 0.9, -12.837, 0, 1.167, 12.242, 0, 1.4, -12.735, 0, 1.7, 11.102, 0, 2.033, -13.566, 0, 2.333, 7.354, 0, 2.6, -6.282, 0, 2.867, 8.498, 0, 3.133, -7.63, 0, 3.433, 2.6, 0, 3.6, 1.222, 0, 3.867, 3.454, 0, 4.1, -12.114, 0, 4.333, 14.879, 0, 4.667, -10.101, 0, 5.067, 12.074, 0, 5.333, -16.356, 0, 5.633, 9.966, 0, 5.867, -1.886, 0, 5.967, -0.837, 0, 6.167, -9.184, 0, 6.467, 13.973, 0, 6.767, -18.415, 0, 7.067, 15.048, 0, 7.333, -13.039, 0, 7.633, 8.438, 0, 7.933, -2.594, 0, 8.1, -1.31, 0, 8.3, -2.196, 0, 8.667, 4.451, 0, 9.067, -4.206, 0, 9.567, 3.861, 0, 10, -8.715, 0, 10.3, 7.303, 0, 10.567, -3.296, 0, 10.767, 0.774, 0, 10.933, -4.24, 0, 11.2, 5.101, 0, 11.467, -2.041, 0, 11.6, 1.315, 0, 11.8, -7.266, 0, 12.033, 2.951]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation65", "Segments": [0, 4.849, 2, 0.333, 4.849, 1, 0.344, 4.849, 0.356, 0.663, 0.367, 0, 1, 0.434, -3.978, 0.5, -5.369, 0.567, -5.369, 0, 0.767, 12.638, 0, 1, -17.858, 0, 1.267, 18.801, 0, 1.533, -17.835, 0, 1.8, 18.38, 0, 2.133, -20.6, 0, 2.433, 14.6, 0, 2.7, -11.993, 0, 2.967, 13.337, 0, 3.267, -14.116, 0, 3.567, 7.374, 0, 3.8, 1.48, 0, 3.933, 2.751, 0, 4.167, -15.046, 0, 4.467, 21.055, 0, 4.767, -18.514, 0, 5.133, 19.328, 0, 5.433, -23.183, 0, 5.767, 18.362, 0, 6.2, -13.049, 0, 6.533, 21.849, 0, 6.833, -25.393, 0, 7.167, 22.308, 0, 7.467, -20.376, 0, 7.767, 16.449, 0, 8.1, -7.663, 0, 8.767, 7.115, 0, 9.167, -7.994, 0, 9.633, 6.636, 0, 10.067, -12.198, 0, 10.4, 13.638, 0, 10.7, -7.19, 0, 10.9, -0.326, 0, 11.067, -3.358, 0, 11.333, 8.12, 0, 11.6, -3.423, 0, 11.7, -1.581, 0, 11.9, -7.657, 0, 12.033, 4.849]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation66", "Segments": [0, -11.659, 2, 0.333, -11.659, 0, 0.4, 0, 0, 0.633, -5.074, 0, 0.867, 16.4, 0, 1.1, -25.561, 0, 1.367, 27.801, 0, 1.633, -26.142, 0, 1.867, 25.269, 0, 2.2, -30, 0, 2.533, 23.379, 0, 2.833, -19.917, 0, 3.067, 20.463, 0, 3.367, -22.564, 0, 3.7, 15.615, 0, 4.233, -17.894, 0, 4.533, 30, 0, 4.833, -27.296, 0, 5.167, 28.335, 0, 5.5, -30, 2, 5.533, -30, 0, 5.833, 27.762, 0, 6.233, -21.171, 0, 6.6, 30, 0, 6.9, -30, 2, 6.967, -30, 0, 7.233, 30, 2, 7.267, 30, 0, 7.533, -29.698, 0, 7.867, 26.24, 0, 8.2, -16.317, 0, 8.833, 9.027, 0, 9.233, -13.295, 0, 9.667, 11.243, 0, 10.133, -16.918, 0, 10.5, 21.635, 0, 10.833, -14.313, 0, 11.067, 0.467, 0, 11.167, 0.041, 0, 11.433, 10.493, 0, 11.733, -7.976, 0, 11.833, -6.912, 1, 11.866, -6.912, 11.9, -6.877, 11.933, -7.727, 1, 11.966, -8.577, 12, -11.659, 12.033, -11.659]}, {"Target": "Parameter", "Id": "Param55", "Segments": [0, -10.57, 2, 0.333, -10.57, 0, 0.433, 0, 0, 0.7, -5.252, 0, 0.933, 20.206, 0, 1.167, -30, 2, 1.2, -30, 0, 1.467, 30, 2, 1.5, 30, 0, 1.7, -30, 2, 1.767, -30, 0, 1.933, 30, 2, 1.967, 30, 0, 2.233, -30, 2, 2.333, -30, 0, 2.6, 30, 2, 2.667, 30, 0, 2.933, -27.453, 0, 3.167, 28.874, 0, 3.433, -30, 2, 3.467, -30, 0, 3.8, 26.06, 0, 4.267, -20.3, 0, 4.567, 30, 2, 4.667, 30, 0, 4.9, -30, 2, 4.967, -30, 0, 5.2, 30, 2, 5.3, 30, 0, 5.533, -30, 2, 5.667, -30, 0, 5.9, 30, 2, 5.967, 30, 0, 6.267, -30, 2, 6.3, -30, 0, 6.633, 30, 2, 6.7, 30, 0, 6.967, -30, 2, 7.1, -30, 0, 7.3, 30, 2, 7.4, 30, 0, 7.6, -30, 2, 7.667, -30, 0, 7.933, 30, 2, 8, 30, 0, 8.3, -27.119, 0, 8.7, 9.181, 0, 8.767, 9.049, 0, 8.867, 9.829, 0, 9.3, -19.154, 0, 9.733, 17.74, 0, 10.2, -22.066, 0, 10.567, 30, 0, 10.933, -24.111, 0, 11.5, 11.764, 0, 11.833, -13.706, 0, 12.033, -10.57]}, {"Target": "Parameter", "Id": "Param28", "Segments": [0, -7.12, 2, 0.333, -7.12, 2, 0.433, -7.12, 2, 0.9, -7.12, 2, 2.167, -7.12, 2, 2.633, -7.12, 2, 3.5, -7.12, 2, 3.7, -7.12, 0, 4, -3.494, 2, 5, -3.494, 2, 5.233, -3.494, 0, 6.8, -6.622, 2, 6.967, -6.622, 0, 7.833, -6.631, 2, 9.767, -6.631, 2, 10.7, -6.631, 0, 12.033, -7.12]}, {"Target": "Parameter", "Id": "Param29", "Segments": [0, -1.278, 2, 0.333, -1.278, 2, 0.433, -1.278, 2, 0.9, -1.278, 2, 2.167, -1.278, 2, 2.633, -1.278, 2, 3.5, -1.278, 2, 3.7, -1.278, 0, 4, -0.253, 2, 5, -0.253, 2, 5.233, -0.253, 0, 6.8, -2.883, 2, 6.967, -2.883, 0, 7.833, -2.852, 2, 9.767, -2.852, 2, 10.7, -2.852, 0, 12.033, -1.278]}, {"Target": "Parameter", "Id": "Param30", "Segments": [0, -5.909, 2, 0.333, -5.909, 2, 0.433, -5.909, 2, 0.9, -5.909, 2, 2.167, -5.909, 2, 2.633, -5.909, 2, 3.5, -5.909, 2, 3.7, -5.909, 0, 4, -5.948, 2, 5, -5.948, 2, 5.233, -5.948, 0, 6.8, -4.909, 2, 6.967, -4.909, 0, 7.833, -4.929, 2, 9.767, -4.929, 2, 10.7, -4.929, 0, 12.033, -5.909]}, {"Target": "Parameter", "Id": "Param31", "Segments": [0, -0.551, 2, 0.333, -0.551, 2, 0.433, -0.551, 2, 0.9, -0.551, 2, 2.167, -0.551, 2, 2.633, -0.551, 2, 3.5, -0.551, 2, 3.7, -0.551, 0, 4, -1.431, 2, 5, -1.431, 2, 5.233, -1.431, 0, 6.8, 0.966, 2, 6.967, 0.966, 0, 7.833, 0.936, 2, 9.767, 0.936, 2, 10.7, 0.936, 0, 12.033, -0.551]}, {"Target": "Parameter", "Id": "Param32", "Segments": [0, -2.714, 2, 0.333, -2.714, 2, 0.433, -2.714, 2, 0.9, -2.714, 2, 2.167, -2.714, 2, 2.633, -2.714, 2, 3.5, -2.714, 2, 3.7, -2.714, 0, 4, -2.722, 2, 5, -2.722, 2, 5.233, -2.722, 0, 6.8, -2.295, 2, 6.967, -2.295, 0, 7.833, -2.303, 2, 9.767, -2.303, 2, 10.7, -2.303, 0, 12.033, -2.714]}, {"Target": "Parameter", "Id": "Param33", "Segments": [0, -2.392, 2, 0.333, -2.392, 2, 0.433, -2.392, 2, 0.9, -2.392, 2, 2.167, -2.392, 2, 2.633, -2.392, 2, 3.5, -2.392, 2, 3.7, -2.392, 0, 4, -3.554, 2, 5, -3.554, 2, 5.233, -3.554, 0, 6.8, -0.179, 2, 6.967, -0.179, 0, 7.833, -0.222, 2, 9.767, -0.222, 2, 10.7, -0.222, 0, 12.033, -2.392]}, {"Target": "Parameter", "Id": "Param34", "Segments": [0, -21.182, 2, 0.333, -21.182, 2, 0.433, -21.182, 2, 0.9, -21.182, 2, 2.167, -21.182, 2, 2.633, -21.182, 2, 3.5, -21.182, 2, 3.7, -21.182, 0, 4, -21.205, 2, 5, -21.205, 2, 5.233, -21.205, 0, 6.8, -18.971, 2, 6.967, -18.971, 0, 7.833, -19.014, 2, 9.767, -19.014, 2, 10.7, -19.014, 0, 12.033, -21.182]}, {"Target": "Parameter", "Id": "Param84", "Segments": [0, 0, 2, 0.333, 0, 0, 1.8, -6.9, 0, 3.267, 0, 0, 4.733, -6.9, 0, 6.2, 0, 0, 7.667, -6.9, 0, 9.133, 0, 0, 10.6, -6.9, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param85", "Segments": [0, 0, 2, 0.333, 0, 0, 1.067, 4.74, 0, 2.533, -4.74, 1, 2.778, -4.74, 3.022, -3.16, 3.267, 0, 1, 3.511, 3.16, 3.756, 4.74, 4, 4.74, 0, 5.467, -4.74, 1, 5.711, -4.74, 5.956, -3.16, 6.2, 0, 1, 6.444, 3.16, 6.689, 4.74, 6.933, 4.74, 0, 8.4, -4.74, 1, 8.644, -4.74, 8.889, -3.16, 9.133, 0, 1, 9.378, 3.16, 9.622, 4.74, 9.867, 4.74, 0, 11.333, -4.74, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param83", "Segments": [0, 0, 2, 0.333, 0, 0, 1.8, 3.96, 0, 3.267, 0, 0, 4.733, 3.96, 0, 6.2, 0, 0, 7.667, 3.96, 0, 9.133, 0, 0, 10.6, 3.96, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param86", "Segments": [0, 1, 2, 0.333, 1, 2, 2.4, 1, 0, 2.533, 0, 0, 2.667, 1, 2, 3.267, 1, 2, 5.333, 1, 0, 5.467, 0, 0, 5.6, 1, 2, 6.2, 1, 2, 8.267, 1, 0, 8.4, 0, 0, 8.533, 1, 2, 9.133, 1, 2, 11.2, 1, 0, 11.333, 0, 0, 11.467, 1, 2, 12.033, 1]}, {"Target": "Parameter", "Id": "Param95", "Segments": [0, 0, 2, 0.333, 0, 0, 1.8, 10, 0, 3.267, 0, 0, 4.733, 10, 0, 6.2, 0, 0, 7.667, 10, 0, 9.133, 0, 0, 10.6, 10, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param92", "Segments": [0, 0, 2, 0.333, 0, 0, 1.067, -12, 0, 2.533, 12, 1, 2.778, 12, 3.022, 8, 3.267, 0, 1, 3.511, -8, 3.756, -12, 4, -12, 0, 5.467, 12, 1, 5.711, 12, 5.956, 8, 6.2, 0, 1, 6.444, -8, 6.689, -12, 6.933, -12, 0, 8.4, 12, 1, 8.644, 12, 8.889, 8, 9.133, 0, 1, 9.378, -8, 9.622, -12, 9.867, -12, 0, 11.333, 12, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param93", "Segments": [0, 0, 2, 0.333, 0, 0, 1.8, -12, 0, 3.267, 0, 0, 4.733, -12, 0, 6.2, 0, 0, 7.667, -12, 0, 9.133, 0, 0, 10.6, -12, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param96", "Segments": [0, 0, 2, 0.333, 0, 0, 0.5, -16, 0, 0.7, 0, 0, 0.9, -16, 0, 1.133, 0, 2, 3.267, 0, 0, 3.433, -16, 0, 3.633, 0, 0, 3.833, -16, 0, 4.067, 0, 2, 6.2, 0, 0, 6.367, -16, 0, 6.567, 0, 0, 6.767, -16, 0, 7, 0, 2, 9.133, 0, 0, 9.3, -16, 0, 9.5, 0, 0, 9.7, -16, 0, 9.933, 0, 2, 12.033, 0]}, {"Target": "Parameter", "Id": "Param97", "Segments": [0, 0, 2, 0.333, 0, 0, 0.367, 14, 0, 0.6, -14, 1, 0.633, -14, 0.667, -7.829, 0.7, 0, 1, 0.744, 10.439, 0.789, 14, 0.833, 14, 0, 1, -14, 0, 1.133, 0, 2, 3.267, 0, 0, 3.3, 14, 0, 3.533, -14, 1, 3.566, -14, 3.6, -7.829, 3.633, 0, 1, 3.678, 10.439, 3.722, 14, 3.767, 14, 0, 3.933, -14, 0, 4.067, 0, 2, 6.2, 0, 0, 6.233, 14, 0, 6.467, -14, 1, 6.5, -14, 6.534, -7.829, 6.567, 0, 1, 6.611, 10.439, 6.656, 14, 6.7, 14, 0, 6.867, -14, 0, 7, 0, 2, 9.133, 0, 0, 9.167, 14, 0, 9.4, -14, 1, 9.433, -14, 9.467, -7.829, 9.5, 0, 1, 9.544, 10.439, 9.589, 14, 9.633, 14, 0, 9.8, -14, 0, 9.933, 0, 2, 12.033, 0]}, {"Target": "Parameter", "Id": "Param89", "Segments": [0, 0, 2, 0.333, 0, 0, 1.8, 18.72, 0, 3.267, 0, 0, 4.733, 18.72, 0, 6.2, 0, 0, 7.667, 18.72, 0, 9.133, 0, 0, 10.6, 18.72, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param90", "Segments": [0, 0, 2, 0.333, 0, 0, 1.067, -19, 0, 2.533, 19, 1, 2.778, 19, 3.022, 12.667, 3.267, 0, 1, 3.511, -12.667, 3.756, -19, 4, -19, 0, 5.467, 19, 1, 5.711, 19, 5.956, 12.667, 6.2, 0, 1, 6.444, -12.667, 6.689, -19, 6.933, -19, 0, 8.4, 19, 1, 8.644, 19, 8.889, 12.667, 9.133, 0, 1, 9.378, -12.667, 9.622, -19, 9.867, -19, 0, 11.333, 19, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation56", "Segments": [0, 0, 2, 0.333, 0, 0, 1.067, 9, 0, 1.8, -9, 0, 2.533, 9, 0, 3.267, 0, 0, 4, 9, 0, 4.733, -9, 0, 5.467, 9, 0, 6.2, 0, 0, 6.933, 9, 0, 7.667, -9, 0, 8.4, 9, 0, 9.133, 0, 0, 9.867, 9, 0, 10.6, -9, 0, 11.333, 9, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param98", "Segments": [0, -6, 2, 0.333, -6, 0, 0.7, 0, 0, 1.433, -12, 0, 2.167, 12, 0, 2.9, -12, 0, 3.633, 0, 0, 4.367, -12, 0, 5.1, 12, 0, 5.833, -12, 1, 5.955, -12, 6.078, -10, 6.2, -6, 1, 6.322, -2, 6.445, 0, 6.567, 0, 0, 7.3, -12, 0, 8.033, 12, 0, 8.767, -12, 0, 9.5, 0, 0, 10.233, -12, 0, 10.967, 12, 0, 11.7, -12, 0, 12.033, -6]}, {"Target": "Parameter", "Id": "Param99", "Segments": [0, -10.921, 2, 0.333, -10.921, 0, 1, 0, 0, 1.733, -11, 0, 2.467, 11, 0, 3.2, -11, 0, 3.933, 0, 0, 4.667, -11, 0, 5.4, 11, 0, 6.133, -11, 1, 6.155, -11, 6.178, -11.239, 6.2, -10.921, 1, 6.422, -7.743, 6.645, 0, 6.867, 0, 0, 7.6, -11, 0, 8.333, 11, 0, 9.067, -11, 0, 9.8, 0, 0, 10.533, -11, 0, 11.267, 11, 0, 11.967, -11, 0, 12.033, -10.921]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation67", "Segments": [0, -10.939, 2, 0.333, -10.939, 0, 0.567, -25, 0, 1.3, 0, 0, 2.033, -25, 0, 2.767, 25, 0, 3.5, -25, 0, 4.233, 0, 0, 4.967, -25, 0, 5.7, 25, 1, 5.867, 25, 6.033, 11.618, 6.2, -10.939, 1, 6.278, -21.466, 6.355, -25, 6.433, -25, 0, 7.167, 0, 0, 7.9, -25, 0, 8.633, 25, 0, 9.367, -25, 0, 10.1, 0, 0, 10.833, -25, 0, 11.567, 25, 0, 12.033, -10.939]}, {"Target": "Parameter", "Id": "Param281", "Segments": [0, 0, 2, 0.333, 0, 0, 1.8, 1, 0, 3.267, 0, 0, 4.733, 1, 0, 6.2, 0, 0, 7.667, 1, 0, 9.133, 0, 0, 10.6, 1, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param197mao2", "Segments": [0, 0, 2, 0.333, 0, 0, 0.967, 2.894, 0, 2.6, -2.454, 1, 2.822, -2.454, 3.045, -1.77, 3.267, 0, 1, 3.489, 1.77, 3.711, 2.894, 3.933, 2.894, 0, 5.533, -2.454, 1, 5.755, -2.454, 5.978, -1.77, 6.2, 0, 1, 6.422, 1.77, 6.645, 2.894, 6.867, 2.894, 0, 8.467, -2.454, 1, 8.689, -2.454, 8.911, -1.77, 9.133, 0, 1, 9.355, 1.77, 9.578, 2.894, 9.8, 2.894, 0, 11.4, -2.454, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param216mao2", "Segments": [0, -8.88, 0, 0.333, -8.9, 2, 6.2, -8.9, 0, 12.033, -8.88]}, {"Target": "Parameter", "Id": "Param212mao2", "Segments": [0, 0, 2, 0.333, 0, 0, 0.5, -16, 0, 0.7, 12.568, 0, 0.9, -16, 0, 1.133, 0, 2, 3.267, 0, 2, 6.2, 0, 0, 6.367, -16, 0, 6.567, 12.568, 0, 6.767, -16, 0, 7, 0, 2, 9.133, 0, 2, 12.033, 0]}, {"Target": "Parameter", "Id": "Param214mao2", "Segments": [0, 0, 2, 0.333, 0, 2, 1.633, 0, 0, 1.833, 16, 0, 2, 0, 0, 2.233, 16, 0, 2.433, 0, 2, 3.267, 0, 2, 6.2, 0, 2, 7.5, 0, 0, 7.7, 16, 0, 7.867, 0, 0, 8.1, 16, 0, 8.3, 0, 2, 9.133, 0, 2, 12.033, 0]}, {"Target": "Parameter", "Id": "Param215mao2", "Segments": [0, 0, 2, 0.333, 0, 2, 1.633, 0, 0, 1.7, -14, 0, 1.933, 15, 0, 2.1, -8, 0, 2.3, 9, 0, 2.6, 0, 2, 3.267, 0, 2, 6.2, 0, 2, 7.5, 0, 0, 7.567, -14, 0, 7.8, 15, 0, 7.967, -8, 0, 8.167, 9, 0, 8.467, 0, 2, 9.133, 0, 2, 12.033, 0]}, {"Target": "Parameter", "Id": "Param198mao2", "Segments": [0, 0, 2, 0.333, 0, 0, 1.7, 30, 0, 3.267, 0, 0, 4.567, 30, 0, 6.2, 0, 0, 7.567, 30, 0, 9.133, 0, 0, 10.433, 30, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation143mao2", "Segments": [0, 0, 2, 0.333, 0, 0, 0.967, 17, 0, 1.8, 0, 0, 2.567, 17, 2, 3.267, 17, 2, 3.9, 17, 0, 4.733, 0, 0, 5.5, 17, 0, 6.2, 0, 0, 6.833, 17, 0, 7.667, 0, 0, 8.433, 17, 2, 9.133, 17, 2, 9.767, 17, 0, 10.6, 0, 0, 11.367, 17, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation144mao2", "Segments": [0, -10.82, 2, 0.333, -10.82, 0, 0.533, -19.438, 0, 1.2, 17.12, 0, 2, -19.438, 0, 2.767, 17.12, 1, 2.934, 17.12, 3.1, 6.316, 3.267, -10.82, 1, 3.334, -17.675, 3.4, -19.438, 3.467, -19.438, 0, 4.133, 17.12, 0, 4.933, -19.438, 0, 5.7, 17.12, 1, 5.867, 17.12, 6.033, 6.316, 6.2, -10.82, 1, 6.267, -17.675, 6.333, -19.438, 6.4, -19.438, 0, 7.067, 17.12, 0, 7.867, -19.438, 0, 8.633, 17.12, 1, 8.8, 17.12, 8.966, 6.316, 9.133, -10.82, 1, 9.2, -17.675, 9.266, -19.438, 9.333, -19.438, 0, 10, 17.12, 0, 10.8, -19.438, 0, 11.567, 17.12, 0, 12.033, -10.82]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation145mao2", "Segments": [0, 8.46, 2, 0.333, 8.46, 0, 0.8, -17.322, 0, 1.467, 16.411, 0, 2.267, -17.322, 0, 3.033, 16.411, 1, 3.111, 16.411, 3.189, 15.523, 3.267, 8.46, 1, 3.422, -5.666, 3.578, -17.322, 3.733, -17.322, 0, 4.4, 16.411, 0, 5.2, -17.322, 0, 5.967, 16.411, 1, 6.045, 16.411, 6.122, 15.523, 6.2, 8.46, 1, 6.356, -5.666, 6.511, -17.322, 6.667, -17.322, 0, 7.333, 16.411, 0, 8.133, -17.322, 0, 8.9, 16.411, 1, 8.978, 16.411, 9.055, 15.523, 9.133, 8.46, 1, 9.289, -5.666, 9.444, -17.322, 9.6, -17.322, 0, 10.267, 16.411, 0, 11.067, -17.322, 0, 11.833, 16.411, 0, 12.033, 8.46]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation146mao2", "Segments": [0, 28.83, 2, 0.333, 28.83, 0, 0.367, 29.224, 0, 1.067, -30, 0, 1.7, 29.224, 0, 2.533, -30, 1, 2.778, -30, 3.022, -0.272, 3.267, 28.83, 1, 3.278, 30.153, 3.289, 29.224, 3.3, 29.224, 0, 4, -30, 0, 4.633, 29.224, 0, 5.467, -30, 1, 5.711, -30, 5.956, -0.272, 6.2, 28.83, 1, 6.211, 30.153, 6.222, 29.224, 6.233, 29.224, 0, 6.933, -30, 0, 7.567, 29.224, 0, 8.4, -30, 1, 8.644, -30, 8.889, -0.272, 9.133, 28.83, 1, 9.144, 30.153, 9.156, 29.224, 9.167, 29.224, 0, 9.867, -30, 0, 10.5, 29.224, 0, 11.333, -30, 0, 12.033, 28.83]}, {"Target": "Parameter", "Id": "Param100", "Segments": [0, 0, 2, 0.333, 0, 0, 1.8, -2.04, 0, 3.267, 0, 0, 4.733, -2.04, 0, 6.2, 0, 0, 7.667, -2.04, 0, 9.133, 0, 0, 10.6, -2.04, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param105", "Segments": [0, 0, 2, 0.333, 0, 0, 1.067, 16, 0, 2.533, -16, 1, 2.778, -16, 3.022, -6.329, 3.267, 0, 1, 3.756, 12.659, 4.244, 16, 4.733, 16, 0, 6.2, 0, 0, 6.933, 16, 0, 8.4, -16, 1, 8.644, -16, 8.889, -6.329, 9.133, 0, 1, 9.622, 12.659, 10.111, 16, 10.6, 16, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param108", "Segments": [0, 0, 2, 0.333, 0, 0, 1.8, 4, 0, 3.267, 0, 0, 4.733, 4, 0, 6.2, 0, 0, 7.667, 4, 0, 9.133, 0, 0, 10.6, 4, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param109", "Segments": [0, 0, 2, 0.333, 0, 0, 1.067, -4, 0, 2.533, 4.46, 1, 2.778, 4.46, 3.022, 2.811, 3.267, 0, 1, 3.511, -2.811, 3.756, -4, 4, -4, 0, 5.467, 4.46, 1, 5.711, 4.46, 5.956, 2.811, 6.2, 0, 1, 6.444, -2.811, 6.689, -4, 6.933, -4, 0, 8.4, 4.46, 1, 8.644, 4.46, 8.889, 2.811, 9.133, 0, 1, 9.378, -2.811, 9.622, -4, 9.867, -4, 0, 11.333, 4.46, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param101", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, -21, 0, 0.567, 0, 0, 0.7, -21, 0, 0.833, 0, 2, 3.267, 0, 0, 3.367, -21, 0, 3.5, 0, 0, 3.633, -21, 0, 3.767, 0, 0, 4.733, -21, 0, 6.2, 0, 0, 6.3, -21, 0, 6.433, 0, 0, 6.567, -21, 0, 6.7, 0, 2, 9.133, 0, 0, 9.233, -21, 0, 9.367, 0, 0, 9.5, -21, 0, 9.633, 0, 0, 10.6, -21, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param106", "Segments": [0, 0, 2, 0.333, 0, 0, 1.8, 15.405, 0, 3.267, 0, 0, 4.733, 15.405, 0, 6.2, 0, 0, 7.667, 15.405, 0, 9.133, 0, 0, 10.6, 15.405, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param104", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, -16, 0, 0.567, 0, 0, 0.7, -16, 0, 0.833, 0, 2, 3.267, 0, 0, 3.367, -16, 0, 3.5, 0, 0, 3.633, -16, 0, 3.767, 0, 0, 4.733, -16, 0, 6.2, 0, 0, 6.3, -16, 0, 6.433, 0, 0, 6.567, -16, 0, 6.7, 0, 2, 9.133, 0, 0, 9.233, -16, 0, 9.367, 0, 0, 9.5, -16, 0, 9.633, 0, 0, 10.6, -16, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param103", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, 27, 0, 0.567, 0, 0, 0.7, 27, 0, 0.833, 0, 2, 3.267, 0, 0, 3.367, 27, 0, 3.5, 0, 0, 3.633, 27, 0, 3.767, 0, 0, 4.733, 27, 0, 6.2, 0, 0, 6.3, 27, 0, 6.433, 0, 0, 6.567, 27, 0, 6.7, 0, 2, 9.133, 0, 0, 9.233, 27, 0, 9.367, 0, 0, 9.5, 27, 0, 9.633, 0, 0, 10.6, 27, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param102", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, 15, 0, 0.567, 0, 0, 0.7, 15, 0, 0.833, 0, 2, 3.267, 0, 0, 3.367, 15, 0, 3.5, 0, 0, 3.633, 15, 0, 3.767, 0, 0, 4.733, 15, 0, 6.2, 0, 0, 6.3, 15, 0, 6.433, 0, 0, 6.567, 15, 0, 6.7, 0, 2, 9.133, 0, 0, 9.233, 15, 0, 9.367, 0, 0, 9.5, 15, 0, 9.633, 0, 0, 10.6, 15, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation71", "Segments": [0, 0, 2, 0.333, 0, 0, 1.067, 18, 0, 1.8, -18, 0, 2.533, 18, 0, 3.267, 0, 0, 4, 18, 0, 4.733, -18, 0, 5.467, 18, 0, 6.2, 0, 0, 6.933, 18, 0, 7.667, -18, 0, 8.4, 18, 0, 9.133, 0, 0, 9.867, 18, 0, 10.6, -18, 0, 11.333, 18, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation72", "Segments": [0, 8.085, 2, 0.333, 8.085, 0, 0.633, 0, 0, 1.367, 19, 0, 2.1, -19, 0, 2.833, 19, 0, 3.567, 0, 0, 4.3, 19, 0, 5.033, -19, 0, 5.767, 19, 1, 5.911, 19, 6.056, 15.561, 6.2, 8.085, 1, 6.3, 2.91, 6.4, 0, 6.5, 0, 0, 7.233, 19, 0, 7.967, -19, 0, 8.7, 19, 0, 9.433, 0, 0, 10.167, 19, 0, 10.9, -19, 0, 11.633, 19, 0, 12.033, 8.085]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation73", "Segments": [0, 11.915, 2, 0.333, 11.915, 0, 0.633, 0, 0, 1.367, 28, 0, 2.1, -28, 0, 2.833, 28, 0, 3.567, 0, 0, 4.3, 28, 0, 5.033, -28, 0, 5.767, 28, 1, 5.911, 28, 6.056, 22.931, 6.2, 11.915, 1, 6.3, 4.288, 6.4, 0, 6.5, 0, 0, 7.233, 28, 0, 7.967, -28, 0, 8.7, 28, 0, 9.433, 0, 0, 10.167, 28, 0, 10.9, -28, 0, 11.633, 28, 0, 12.033, 11.915]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation74", "Segments": [0, 12.341, 2, 0.333, 12.341, 0, 0.633, 0, 0, 1.367, 29, 0, 2.1, -29, 0, 2.833, 29, 0, 3.567, 0, 0, 4.3, 29, 0, 5.033, -29, 0, 5.767, 29, 1, 5.911, 29, 6.056, 23.75, 6.2, 12.341, 1, 6.3, 4.442, 6.4, 0, 6.5, 0, 0, 7.233, 29, 0, 7.967, -29, 0, 8.7, 29, 0, 9.433, 0, 0, 10.167, 29, 0, 10.9, -29, 0, 11.633, 29, 0, 12.033, 12.341]}, {"Target": "Parameter", "Id": "Param112", "Segments": [0, -30, 0, 0.333, 0, 2, 0.433, 0, 2, 0.9, 0, 2, 2.167, 0, 2, 2.633, 0, 2, 3.5, 0, 2, 3.7, 0, 2, 4, 0, 2, 5, 0, 2, 5.233, 0, 2, 5.8, 0, 2, 6.8, 0, 2, 6.967, 0, 2, 7.833, 0, 2, 9.767, 0, 2, 10.7, 0, 0, 12.033, -30]}, {"Target": "Parameter", "Id": "Param272", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param293", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param273", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "ParamCheek", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param268", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param269", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param193", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param192", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param196", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param282", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param183", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param194", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param195", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param184", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param289", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param185", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param290", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param291", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param186", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param187", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param188", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param292", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param189", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param190", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param191", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param24", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param26", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param200", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param286", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param287", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param288", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param205", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param21", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param213", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param214", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param215", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param216", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param218", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param219", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param220", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param197", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param198", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param208", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param279", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param228", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param270", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param271", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param283", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param230", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param231", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param207", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param237", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param239", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param240", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param241", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param242", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param244", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param18", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param275", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param227", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param226", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param232", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param274", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param276", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param142", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param143", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param144", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param147", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param145", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param134", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param246", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param141", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param182", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param247", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param229", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param245", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param117", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param140", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param262", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param263", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param115", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param116", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param260", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param261", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param133", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param139", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param257", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param137", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param138", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param135", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param176", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param136", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param177", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param251", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param91", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param2", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param87", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param88", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param94", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param218mao2", "Segments": [0, 1, 0, 12.033, 1]}, {"Target": "Parameter", "Id": "Param211mao2", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param217mao2", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param202mao2", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param203mao2", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param204mao2", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param199mao2", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param200mao2", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param201mao2", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param208mao2", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param209mao2", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param210mao2", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param205mao2", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param206mao2", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param207mao2", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param213mao2", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param219mao2", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param264", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param107", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param284", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param148", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param146", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param110", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param111", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param248", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param249", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param206", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param114", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param113", "Segments": [0, 1, 0, 12.033, 1]}, {"Target": "Parameter", "Id": "Param253", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param254", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param158", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param149", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param150", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param151", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param255", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param256", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param153", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param152", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param155", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param154", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param181", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param180", "Segments": [0, 0.5, 0, 12.033, 0.5]}, {"Target": "Parameter", "Id": "Param156", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param157", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param159", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param160", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param119", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param265", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param118", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param120", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param121", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param161", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param259", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param123", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param124", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param130", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param131", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param178", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param179", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param125", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param126", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param127", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param128", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param171", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param173", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param172", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param122", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param170", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param129", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param132", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param250", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param258", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param162", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param169", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param174", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param175", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param163", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param164", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param165", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param166", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param167", "Segments": [0, 0, 0, 12.033, 0]}, {"Target": "Parameter", "Id": "Param168", "Segments": [0, 0, 0, 12.033, 0]}], "UserData": [{"Time": 0.0, "Value": ""}, {"Time": 0.333, "Value": ""}, {"Time": 11.533, "Value": ""}]}