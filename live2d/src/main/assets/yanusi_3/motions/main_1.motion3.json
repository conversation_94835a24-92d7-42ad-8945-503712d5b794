{"Version": 3, "Meta": {"Duration": 14.567, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 355, "TotalSegmentCount": 4175, "TotalPointCount": 4669, "UserDataCount": 3, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 2, 0.333, 0, 0, 0.7, 8, 2, 0.833, 8, 0, 1, -7.211, 0, 1.233, 11.212, 0, 1.5, -5.087, 0, 1.733, 6.371, 0, 1.967, -2.766, 0, 2.2, 4.05, 2, 3, 4.05, 1, 3.089, 4.05, 3.178, 4.493, 3.267, 4.659, 1, 3.467, 5.032, 3.667, 5.08, 3.867, 5.546, 1, 4.011, 5.883, 4.156, 8.126, 4.3, 8.126, 1, 4.444, 8.126, 4.589, 3.779, 4.733, 1.056, 1, 4.855, -1.247, 4.978, -1.31, 5.1, -1.31, 2, 5.633, -1.31, 0, 6.3, -0.081, 0, 6.533, -21.124, 0, 6.8, 11.212, 0, 7.1, -14.198, 1, 7.2, -14.198, 7.3, -0.766, 7.4, 0, 1, 7.822, 3.233, 8.245, 4.05, 8.667, 4.05, 2, 9.4, 4.05, 1, 9.656, 4.05, 9.911, -3.784, 10.167, -8.751, 1, 10.3, -11.343, 10.434, -11, 10.567, -11, 2, 13, -11, 0, 14.233, 0, 2, 14.567, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 2, 0.333, 0, 0, 0.5, -8, 0, 0.7, 2.612, 1, 0.744, 2.612, 0.789, -0.129, 0.833, -1.242, 1, 1.211, -10.706, 1.589, -14.635, 1.967, -14.635, 0, 2.3, 2, 0, 2.667, -6.764, 0, 2.9, -4.549, 2, 3, -4.549, 1, 3.078, -4.549, 3.155, -8.229, 3.233, -9.61, 1, 3.444, -13.359, 3.656, -15.958, 3.867, -19, 1, 4.011, -21.082, 4.156, -22, 4.3, -22, 0, 4.633, -14.635, 1, 4.789, -14.635, 4.944, -18.75, 5.1, -20.265, 1, 5.278, -21.996, 5.455, -22, 5.633, -22, 2, 6.3, -22, 1, 6.467, -22, 6.633, -21.307, 6.8, -21, 1, 6.989, -20.653, 7.178, -20.736, 7.367, -20.294, 1, 7.556, -19.852, 7.744, -10.797, 7.933, -8, 1, 8.189, -4.216, 8.444, -3.37, 8.7, -3.37, 1, 8.756, -3.37, 8.811, -7.01, 8.867, -8, 1, 8.989, -10.177, 9.111, -12.164, 9.233, -13.13, 1, 9.4, -14.447, 9.566, -14.635, 9.733, -14.635, 0, 9.967, -10, 0, 10.567, -28, 0, 11.167, -24, 0, 12.133, -30, 1, 12.422, -30, 12.711, -29.576, 13, -28, 1, 13.167, -27.091, 13.333, -26.234, 13.5, -24, 1, 13.767, -20.425, 14.033, -11.518, 14.3, -4.549, 1, 14.389, -2.226, 14.478, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 2, 0.333, 0, 0, 0.667, 11.065, 1, 0.722, 11.065, 0.778, 11.11, 0.833, 10.872, 1, 1.078, 9.829, 1.322, 8.933, 1.567, 8.933, 2, 1.967, 8.933, 2, 2.2, 8.933, 0, 2.633, 15, 1, 2.755, 15, 2.878, 15.019, 3, 13.917, 1, 3.1, 13.016, 3.2, 9.674, 3.3, 9.674, 2, 3.833, 9.674, 1, 3.989, 9.674, 4.144, 9.647, 4.3, 8.933, 1, 4.444, 8.27, 4.589, 5.632, 4.733, 5.092, 1, 5.033, 3.969, 5.333, 3.772, 5.633, 3.772, 2, 6.3, 3.772, 0, 6.8, 7.56, 2, 7.9, 7.56, 2, 8.667, 7.56, 2, 9.733, 7.56, 0, 10.567, -1, 0, 11.133, 0.88, 0, 12.133, -1.12, 0, 13.033, -1, 0, 13.4, -3, 0, 14.333, 0.88, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 2, 0.333, 1, 0, 0.5, 0, 0, 0.6, 1, 2, 0.967, 1, 0, 1.1, 0, 2, 1.567, 0, 2, 1.9, 0, 0, 2.267, 0.8, 0, 2.733, 0.7, 0, 3.033, 0.8, 1, 3.044, 0.8, 3.056, 0.796, 3.067, 0.6, 1, 3.078, 0.404, 3.089, 0, 3.1, 0, 2, 3.133, 0, 1, 3.144, 0, 3.156, -0.014, 3.167, 0.104, 1, 3.2, 0.456, 3.234, 0.8, 3.267, 0.8, 2, 3.333, 0.8, 0, 3.467, 0.69, 2, 3.767, 0.69, 0, 3.867, 0.8, 2, 4.2, 0.8, 0, 4.3, 0.89, 1, 4.389, 0.89, 4.478, 0.871, 4.567, 0.723, 1, 4.6, 0.668, 4.634, 0, 4.667, 0, 2, 4.7, 0, 0, 4.833, 0.685, 2, 5.633, 0.685, 2, 6.3, 0.685, 0, 6.5, 0, 2, 7.367, 0, 0, 7.8, 0.8, 2, 7.9, 0.8, 0, 8.3, 0.9, 1, 8.422, 0.9, 8.545, 0.899, 8.667, 0.893, 1, 8.7, 0.891, 8.734, 0.701, 8.767, 0.701, 1, 8.922, 0.701, 9.078, 0.712, 9.233, 0.743, 1, 9.4, 0.777, 9.566, 0.8, 9.733, 0.8, 1, 9.789, 0.8, 9.844, 0.799, 9.9, 0.787, 1, 9.922, 0.783, 9.945, 0, 9.967, 0, 2, 10, 0, 1, 10.044, 0, 10.089, 0.774, 10.133, 0.8, 1, 10.278, 0.886, 10.422, 0.9, 10.567, 0.9, 2, 13, 0.9, 2, 13.2, 0.9, 0, 13.333, 0, 2, 13.367, 0, 1, 13.445, 0, 13.522, 0.867, 13.6, 0.9, 1, 13.811, 0.99, 14.022, 1, 14.233, 1, 2, 14.567, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 2, 0.333, 0, 2, 0.5, 0, 0, 0.967, 0.116, 0, 1.1, 0, 2, 1.567, 0, 2, 1.9, 0, 2, 3.033, 0, 1, 3.133, 0, 3.233, 0.061, 3.333, 0.296, 1, 3.511, 0.714, 3.689, 1, 3.867, 1, 2, 4.2, 1, 2, 4.3, 1, 2, 4.567, 1, 0, 4.667, 0, 2, 4.7, 0, 0, 4.833, 1, 2, 5.633, 1, 2, 6.3, 1, 0, 6.5, 0, 2, 7.367, 0, 0, 7.8, 0.9, 2, 7.9, 0.9, 2, 8.667, 0.9, 0, 8.767, 0, 2, 9.233, 0, 0, 9.733, 0.9, 2, 9.9, 0.9, 0, 9.967, 0, 2, 10, 0, 0, 10.133, 1, 2, 10.567, 1, 2, 13, 1, 2, 13.2, 1, 0, 13.333, 0, 2, 13.367, 0, 2, 14.233, 0, 2, 14.567, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 2, 0.333, 1, 0, 0.5, 0, 0, 0.6, 1, 2, 0.967, 1, 0, 1.1, 0, 2, 1.567, 0, 2, 1.9, 0, 0, 2.267, 0.8, 0, 2.733, 0.7, 0, 3.033, 0.8, 1, 3.044, 0.8, 3.056, 0.796, 3.067, 0.6, 1, 3.078, 0.404, 3.089, 0, 3.1, 0, 2, 3.133, 0, 1, 3.144, 0, 3.156, -0.012, 3.167, 0.107, 1, 3.2, 0.463, 3.234, 0.8, 3.267, 0.8, 2, 3.333, 0.8, 0, 3.467, 0.691, 2, 3.767, 0.691, 0, 3.867, 0.8, 2, 4.2, 0.8, 0, 4.3, 0.866, 1, 4.389, 0.866, 4.478, 0.849, 4.567, 0.71, 1, 4.6, 0.658, 4.634, 0, 4.667, 0, 2, 4.7, 0, 0, 4.833, 0.666, 2, 5.633, 0.666, 2, 6.3, 0.666, 0, 6.5, 0, 2, 7.367, 0, 0, 7.8, 0.8, 2, 7.9, 0.8, 0, 8.3, 0.9, 1, 8.422, 0.9, 8.545, 0.899, 8.667, 0.893, 1, 8.7, 0.891, 8.734, 0.752, 8.767, 0.75, 1, 8.922, 0.743, 9.078, 0.741, 9.233, 0.741, 0, 9.733, 0.8, 1, 9.789, 0.8, 9.844, 0.799, 9.9, 0.787, 1, 9.922, 0.783, 9.945, 0, 9.967, 0, 2, 10, 0, 1, 10.044, 0, 10.089, 0.774, 10.133, 0.8, 1, 10.278, 0.886, 10.422, 0.9, 10.567, 0.9, 2, 13, 0.9, 2, 13.2, 0.9, 0, 13.333, 0, 2, 13.367, 0, 1, 13.445, 0, 13.522, 0.867, 13.6, 0.9, 1, 13.811, 0.99, 14.022, 1, 14.233, 1, 2, 14.567, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 2, 0.333, 0, 2, 0.5, 0, 0, 0.967, 1, 0, 1.1, 0, 2, 1.567, 0, 2, 1.9, 0, 2, 3, 0, 1, 3.111, 0, 3.222, 0.077, 3.333, 0.33, 1, 3.511, 0.735, 3.689, 1, 3.867, 1, 2, 4.2, 1, 2, 4.3, 1, 2, 4.567, 1, 0, 4.667, 0, 2, 4.7, 0, 0, 4.833, 1, 2, 5.633, 1, 2, 6.3, 1, 0, 6.5, 0, 2, 7.367, 0, 0, 7.8, 0.8, 2, 7.9, 0.8, 2, 8.667, 0.8, 0, 8.767, 0, 2, 9.233, 0, 0, 9.733, 0.8, 2, 9.9, 0.8, 0, 9.967, 0, 2, 10, 0, 0, 10.133, 1, 2, 10.567, 1, 2, 13, 1, 2, 13.2, 1, 0, 13.333, 0, 2, 13.367, 0, 2, 14.233, 0, 2, 14.567, 0]}, {"Target": "Parameter", "Id": "Param76", "Segments": [0, 0, 2, 0.333, 0, 0, 0.833, -30, 2, 1.567, -30, 2, 3, -30, 2, 4.3, -30, 2, 5.633, -30, 2, 6.3, -30, 2, 7.9, -30, 2, 8.667, -30, 2, 9.733, -30, 2, 10.567, -30, 2, 13, -30, 0, 14.233, 0, 2, 14.567, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 2, 0.333, 0, 2, 0.833, 0, 2, 1.567, 0, 2, 1.967, 0, 2, 3, 0, 0, 3.333, 0.032, 0, 3.467, -0.2, 2, 3.767, -0.2, 0, 3.867, 0, 2, 4.2, 0, 0, 4.3, -0.296, 2, 4.467, -0.296, 0, 4.6, -0.7, 2, 5.633, -0.7, 2, 5.7, -0.7, 2, 6.167, -0.7, 1, 6.211, -0.7, 6.256, -0.54, 6.3, -0.5, 1, 6.622, -0.208, 6.945, -0.1, 7.267, -0.1, 2, 7.567, -0.1, 0, 7.9, -0.2, 2, 8.3, -0.2, 2, 8.667, -0.2, 2, 8.767, -0.2, 0, 9.233, -0.1, 1, 9.4, -0.1, 9.566, -0.121, 9.733, -0.2, 1, 9.766, -0.216, 9.8, -0.5, 9.833, -0.5, 2, 10.567, -0.5, 2, 13, -0.5, 0, 14.233, 0, 2, 14.567, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 2, 0.333, 0, 2, 0.833, 0, 2, 1.567, 0, 2, 1.967, 0, 0, 2.2, -0.2, 0, 2.6, 0.2, 2, 2.9, 0.2, 1, 2.933, 0.2, 2.967, 0.032, 3, 0, 1, 3.111, -0.108, 3.222, -0.153, 3.333, -0.264, 1, 3.378, -0.308, 3.422, -0.636, 3.467, -0.656, 1, 3.6, -0.717, 3.734, -0.766, 3.867, -0.8, 1, 3.978, -0.828, 4.089, -0.849, 4.2, -0.863, 1, 4.233, -0.867, 4.267, -0.866, 4.3, -0.866, 2, 4.467, -0.866, 0, 4.6, -0.4, 2, 5.633, -0.4, 0, 5.7, -0.3, 2, 6.167, -0.3, 0, 6.3, -0.5, 0, 7.267, 0.2, 0, 7.567, -0.6, 1, 7.678, -0.6, 7.789, -0.599, 7.9, -0.5, 1, 8.033, -0.381, 8.167, -0.2, 8.3, -0.2, 1, 8.422, -0.2, 8.545, -0.208, 8.667, -0.249, 1, 8.7, -0.26, 8.734, -0.7, 8.767, -0.7, 0, 9.233, -0.5, 2, 9.733, -0.5, 0, 9.833, -0.4, 2, 10.567, -0.4, 2, 13, -0.4, 0, 14.233, 0, 2, 14.567, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 2, 0.333, 0, 2, 0.467, 0, 2, 0.633, 0, 2, 0.967, 0, 0, 1.1, -0.4, 2, 1.567, -0.4, 2, 3, -0.4, 0, 3.333, -0.2, 2, 3.867, -0.2, 2, 4.3, -0.2, 2, 4.833, -0.2, 2, 5.633, -0.2, 2, 6.3, -0.2, 0, 6.667, -0.15, 2, 7.467, -0.15, 2, 7.9, -0.15, 2, 8.667, -0.15, 2, 9.733, -0.15, 2, 10.567, -0.15, 2, 13, -0.15, 0, 14.233, 0, 2, 14.567, 0]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 2, 0.333, 0, 0, 0.467, -0.5, 0, 0.633, -0.1, 2, 0.967, -0.1, 0, 1.1, -0.284, 2, 1.567, -0.284, 2, 3, -0.284, 0, 3.333, 0, 2, 3.867, 0, 0, 4.3, -0.198, 0, 4.833, 0, 2, 5.633, 0, 2, 6.3, 0, 0, 6.667, -0.12, 2, 7.467, -0.12, 0, 7.9, 0.1, 2, 8.667, 0.1, 1, 8.711, 0.1, 8.756, 0.021, 8.8, 0, 1, 9.111, -0.146, 9.422, -0.2, 9.733, -0.2, 0, 10.567, 0.1, 2, 13, 0.1, 0, 14.233, 0, 2, 14.567, 0]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 2, 0.333, 0, 2, 0.467, 0, 2, 0.633, 0, 2, 0.967, 0, 0, 1.1, -0.4, 2, 1.567, -0.4, 2, 3, -0.4, 0, 3.333, -0.2, 2, 3.867, -0.2, 2, 4.3, -0.2, 2, 4.833, -0.2, 2, 5.633, -0.2, 2, 6.3, -0.2, 2, 7.467, -0.2, 2, 7.9, -0.2, 2, 8.667, -0.2, 2, 10.567, -0.2, 2, 13, -0.2, 0, 14.233, 0, 2, 14.567, 0]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 2, 0.333, 0, 0, 0.467, -0.5, 0, 0.633, -0.2, 2, 0.967, -0.2, 0, 1.1, -0.232, 2, 1.567, -0.232, 2, 3, -0.232, 0, 3.333, 0, 2, 3.867, 0, 0, 4.3, -0.212, 0, 4.833, 0, 2, 5.633, 0, 2, 6.3, 0, 0, 6.667, -0.1, 2, 7.467, -0.1, 0, 7.9, 0.1, 2, 8.667, 0.1, 1, 8.711, 0.1, 8.756, -0.089, 8.8, -0.1, 1, 9.111, -0.175, 9.422, -0.2, 9.733, -0.2, 0, 10.567, 0.1, 2, 13, 0.1, 0, 14.233, 0, 2, 14.567, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 2, 0.333, 0, 0, 0.467, -0.7, 0, 0.633, 0, 2, 0.967, 0, 2, 1.1, 0, 2, 1.567, 0, 2, 3, 0, 2, 4.3, 0, 2, 4.833, 0, 2, 5.633, 0, 2, 6.3, 0, 2, 7.467, 0, 0, 7.9, 0.122, 2, 8.667, 0.122, 0, 9.033, 0.326, 2, 9.733, 0.326, 0, 10.567, 0.122, 2, 13, 0.122, 0, 14.233, 0, 2, 14.567, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 2, 0.333, 0, 0, 0.467, -0.9, 0, 0.633, 0, 2, 0.967, 0, 2, 1.1, 0, 2, 1.567, 0, 2, 3, 0, 2, 4.3, 0, 2, 5.633, 0, 2, 6.3, 0, 2, 7.467, 0, 0, 7.9, 0.162, 2, 8.667, 0.162, 0, 9.033, 0.332, 2, 9.733, 0.332, 0, 10.567, 0.162, 2, 13, 0.162, 0, 14.233, 0, 2, 14.567, 0]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, -1, 2, 0.333, -1, 0, 0.467, -0.8, 0, 0.633, -1, 2, 0.967, -1, 0, 1.1, 0, 2, 1.567, 0, 2, 3, 0, 0, 3.333, -1, 2, 4.3, -1, 2, 5.633, -1, 2, 6.3, -1, 0, 6.667, 0, 2, 7.467, 0, 2, 7.9, 0, 2, 8.667, 0, 2, 9.733, 0, 2, 10.567, 0, 2, 13, 0, 0, 14.233, -1, 2, 14.567, -1]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, -1, 2, 0.333, -1, 0, 0.467, -0.7, 0, 0.633, -1, 2, 0.967, -1, 0, 1.1, 0, 2, 1.567, 0, 2, 3, 0, 0, 3.333, -1, 2, 4.3, -1, 2, 5.633, -1, 2, 6.3, -1, 0, 6.667, 0, 2, 7.467, 0, 2, 7.9, 0, 2, 8.667, 0, 2, 9.733, 0, 2, 10.567, 0, 2, 13, 0, 0, 14.233, -1, 2, 14.567, -1]}, {"Target": "Parameter", "Id": "Param", "Segments": [0, -30, 2, 0.333, -30, 0, 0.567, -4, 0, 0.833, -19, 0, 1.567, -4, 2, 2.4, -4, 0, 2.733, 0, 0, 3, -24, 1, 3.133, -24, 3.267, -1.984, 3.4, -1, 1, 3.556, 0.148, 3.711, 0, 3.867, 0, 2, 4.3, 0, 2, 4.967, 0, 0, 5.2, -29, 2, 5.633, -29, 2, 6.3, -29, 1, 6.467, -29, 6.633, -9.982, 6.8, -8, 1, 7.111, -4.299, 7.422, -1.376, 7.733, -0.09, 1, 7.789, 0.14, 7.844, 0, 7.9, 0, 2, 8.667, 0, 2, 9.733, 0, 2, 10.567, 0, 2, 13, 0, 0, 14.233, -30, 2, 14.567, -30]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 1, 2, 0.333, 1, 0, 0.5, -0.6, 0, 0.567, 0, 1, 0.656, 0, 0.744, -0.619, 0.833, -0.7, 1, 1.033, -0.882, 1.233, -0.9, 1.433, -0.9, 0, 1.567, -0.7, 2, 2.4, -0.7, 0, 2.733, -1, 2, 3, -1, 1, 3.211, -1, 3.422, -0.934, 3.633, -0.836, 1, 3.666, -0.82, 3.7, -0.829, 3.733, -0.807, 1, 3.778, -0.779, 3.822, -0.2, 3.867, -0.2, 0, 4.033, -0.8, 0, 4.3, -0.6, 0, 4.433, -1, 0, 4.767, -0.8, 2, 4.967, -0.8, 2, 6.3, -0.8, 0, 6.8, -1, 2, 7.467, -1, 1, 7.556, -1, 7.644, -0.953, 7.733, -0.732, 1, 7.789, -0.594, 7.844, -0.4, 7.9, -0.4, 0, 8.267, -1, 0, 8.533, -0.6, 1, 8.566, -0.6, 8.6, -0.639, 8.633, -0.7, 1, 8.744, -0.905, 8.856, -1, 8.967, -1, 0, 9.733, -0.6, 1, 9.844, -0.6, 9.956, -0.608, 10.067, -0.7, 1, 10.122, -0.746, 10.178, -1, 10.233, -1, 0, 10.567, -0.3, 1, 10.656, -0.3, 10.744, -0.648, 10.833, -0.7, 1, 11.233, -0.936, 11.633, -1, 12.033, -1, 2, 12.467, -1, 1, 12.645, -1, 12.822, -0.47, 13, -0.1, 1, 13.411, 0.755, 13.822, 1, 14.233, 1, 2, 14.567, 1]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 2, 0.333, 0, 2, 0.5, 0, 0, 0.567, 0.6, 0, 0.833, 0, 0, 1.1, 0.5, 0, 1.267, 0.3, 0, 1.433, 0.6, 0, 1.567, 0.3, 0, 1.733, 0.6, 0, 1.933, 0.3, 0, 2.067, 0.6, 0, 2.2, 0.1, 0, 2.367, 0.6, 0, 2.567, 0.3, 0, 2.733, 0.7, 0, 3, 0, 2, 3.267, 0, 0, 3.4, 0.3, 0, 3.533, 0.2, 0, 3.633, 0.5, 0, 3.733, 0.4, 0, 3.867, 0.5, 0, 4.033, 0.3, 0, 4.167, 0.7, 0, 4.3, 0.2, 0, 4.433, 0.6, 0, 4.567, 0.3, 0, 4.767, 0.8, 1, 4.834, 0.8, 4.9, 0.521, 4.967, 0.3, 1, 5.045, 0.042, 5.122, 0, 5.2, 0, 2, 5.633, 0, 2, 6.3, 0, 2, 6.8, 0, 2, 7.467, 0, 2, 7.733, 0, 0, 7.9, 0.3, 0, 8.067, 0.2, 0, 8.267, 0.6, 0, 8.533, 0, 0, 8.633, 0.7, 0, 8.8, 0.3, 0, 8.967, 0.6, 0, 9.067, 0.2, 0, 9.267, 0.7, 0, 9.433, 0, 0, 9.567, 0.7, 0, 9.7, 0.3, 0, 9.867, 0.7, 0, 10.067, 0.3, 0, 10.233, 0.7, 0, 10.367, 0.2, 0, 10.567, 0.6, 0, 10.733, 0, 0, 10.833, 0.6, 0, 11, 0.2, 0, 11.2, 0.6, 0, 11.433, 0.2, 0, 11.633, 0.6, 0, 11.833, 0, 0, 12.033, 0.8, 0, 12.267, 0.2, 0, 12.467, 0.7, 0, 13, 0, 2, 14.233, 0, 2, 14.567, 0]}, {"Target": "Parameter", "Id": "Param269", "Segments": [0, 0, 2, 0.333, 0, 0, 0.7, 0.6, 2, 3, 0.6, 2, 5.633, 0.6, 2, 6.3, 0.6, 2, 7.9, 0.6, 2, 8.667, 0.6, 2, 9.733, 0.6, 2, 10.567, 0.6, 2, 13, 0.6, 0, 13.2, 1, 3, 13.233, 0, 2, 14.233, 0, 2, 14.567, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 2, 0.333, 0, 0, 0.833, -26, 0, 1.033, -19, 0, 1.3, -26, 0, 1.533, -19, 0, 1.733, -26.06, 0, 2.033, -20.322, 0, 2.533, -26.119, 1, 2.689, -26.119, 2.844, -24.179, 3, -21, 1, 3.167, -17.593, 3.333, -16.193, 3.5, -16.193, 0, 4.867, -28.525, 2, 5.633, -28.525, 2, 6.3, -28.525, 0, 6.6, -22, 0, 6.833, -27, 0, 7.033, -22.768, 0, 7.3, -26, 1, 7.5, -26, 7.7, -14.893, 7.9, -11, 1, 8.156, -6.026, 8.411, -5.757, 8.667, -5.757, 1, 8.922, -5.757, 9.178, -13.592, 9.433, -16.225, 1, 9.533, -17.256, 9.633, -16.543, 9.733, -17.468, 1, 10, -19.935, 10.266, -24.6, 10.533, -24.6, 0, 11.067, -21, 0, 11.733, -25.515, 0, 12.4, -22, 0, 13.033, -24.6, 1, 13.433, -24.6, 13.833, -18.569, 14.233, -5.757, 1, 14.344, -2.198, 14.456, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 2, 0.333, 0, 0, 0.5, -10, 0, 0.633, 1, 1, 0.7, 1, 0.766, -2.694, 0.833, -4, 1, 0.922, -5.741, 1.011, -7.629, 1.1, -7.72, 1, 1.378, -8.003, 1.655, -8.048, 1.933, -8.048, 0, 2.2, -1.908, 0, 2.633, -9.057, 1, 2.755, -9.057, 2.878, -8.916, 3, -7.72, 1, 3.067, -7.068, 3.133, -4.816, 3.2, -4.816, 0, 3.867, -14, 1, 3.967, -14, 4.067, -14.12, 4.167, -12.376, 1, 4.289, -10.244, 4.411, -5.198, 4.533, -5.198, 0, 5.633, -14, 1, 5.855, -14, 6.078, -11.87, 6.3, -10.602, 1, 6.644, -8.637, 6.989, -8.151, 7.333, -6, 1, 7.522, -4.82, 7.711, 4.655, 7.9, 9, 1, 8.033, 12.067, 8.167, 12.9, 8.3, 12.9, 1, 8.422, 12.9, 8.545, 12.972, 8.667, 12.824, 1, 9.022, 12.395, 9.378, 11.125, 9.733, 9, 1, 10.011, 7.34, 10.289, -23, 10.567, -23, 2, 13, -23, 0, 13.3, -30, 0, 14.3, 4.435, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 2, 0.333, 0, 2, 0.833, 0, 2, 1.567, 0, 2, 3, 0, 0, 3.867, 1.035, 2, 5.633, 1.035, 2, 6.3, 1.035, 2, 7.9, 1.035, 2, 8.667, 1.035, 2, 9.733, 1.035, 0, 10.567, -8.685, 2, 13, -8.685, 0, 14.233, 0, 2, 14.567, 0]}, {"Target": "Parameter", "Id": "Param267", "Segments": [0, 0, 2, 0.333, 0, 2, 0.833, 0, 0, 1.067, -0.777, 0, 1.333, 0.809, 0, 1.567, 0, 2, 3, 0, 0, 3.867, -0.4, 0, 5.367, 1.88, 2, 6.3, 1.88, 2, 7.267, 1.88, 0, 8.3, 3.32, 1, 8.422, 3.32, 8.545, 3.316, 8.667, 3.085, 1, 9.022, 2.414, 9.378, 1.88, 9.733, 1.88, 1, 10.011, 1.88, 10.289, 7.759, 10.567, 8.9, 0, 11.8, 9.052, 0, 13, 8.9, 2, 13.067, 8.9, 0, 14.233, 0, 2, 14.567, 0]}, {"Target": "Parameter", "Id": "Param3", "Segments": [0, 0, 2, 0.333, 0, 0, 0.833, 8.124, 0, 1.033, 7.228, 0, 1.3, 8.167, 0, 1.533, 7.228, 0, 1.733, 8.172, 0, 2.033, 7.332, 0, 2.533, 7.913, 2, 3, 7.913, 0, 3.867, 11.453, 1, 4.056, 11.453, 4.244, 10.623, 4.433, 8.8, 1, 4.744, 5.797, 5.056, 4.209, 5.367, 4.209, 0, 6.3, 5.918, 0, 6.6, 4.538, 0, 6.833, 5.798, 0, 7.033, 4.466, 0, 7.3, 5.846, 2, 7.9, 5.846, 2, 8.667, 5.846, 2, 9.733, 5.846, 0, 10.567, -4.1, 0, 11.833, -3.817, 0, 13, -4.1, 0, 14.233, 0, 2, 14.567, 0]}, {"Target": "Parameter", "Id": "Param4", "Segments": [0, 0, 2, 0.333, 0, 0, 0.467, -1.52, 1, 0.534, -1.52, 0.6, 0.623, 0.667, 1.472, 1, 0.722, 2.18, 0.778, 2.119, 0.833, 2.119, 0, 1.8, 0, 0, 2.267, 0.886, 0, 2.633, 0.177, 0, 2.933, 0.99, 2, 3, 0.99, 0, 3.2, 1.633, 1, 3.422, 1.633, 3.645, 0.751, 3.867, -0.588, 1, 4.056, -1.726, 4.244, -2.122, 4.433, -2.122, 2, 5.367, -2.122, 0, 6.3, -0.937, 1, 6.644, -0.937, 6.989, -1.271, 7.333, -2.588, 1, 7.422, -2.928, 7.511, -4.854, 7.6, -4.854, 0, 8.3, -2.098, 1, 8.422, -2.098, 8.545, -2.052, 8.667, -2.144, 1, 9.022, -2.413, 9.378, -3.256, 9.733, -4.678, 1, 10.011, -5.789, 10.289, -24, 10.567, -24, 2, 13, -24, 0, 13.233, -25.7, 0, 14.233, 1.472, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param266", "Segments": [0, 0, 2, 0.333, 0, 2, 0.833, 0, 0, 1.067, -3.72, 0, 1.3, 0, 0, 1.567, -3.303, 0, 1.733, -0.236, 0, 2.033, -4.247, 0, 2.467, -0.865, 2, 3, -0.865, 0, 3.867, 5.075, 0, 5.367, -6.341, 0, 6.3, -4.237, 0, 6.6, -7.201, 0, 6.833, -4.247, 0, 7.033, -7.201, 0, 7.3, -3.85, 2, 7.9, -3.85, 2, 8.667, -3.85, 2, 9.733, -3.85, 0, 10.567, -10.63, 2, 13, -10.63, 0, 14.233, 0, 2, 14.567, 0]}, {"Target": "Parameter", "Id": "Param5", "Segments": [0, 0, 2, 0.333, 0, 0, 0.5, 9, 0, 0.833, -5, 2, 1.567, -5, 2, 3, -5, 0, 3.333, -3.789, 0, 4, -12.43, 2, 5.633, -12.43, 2, 6.3, -12.43, 0, 6.667, -7.989, 1, 6.911, -7.989, 7.156, -7.988, 7.4, -8, 1, 7.567, -8.008, 7.733, -11.446, 7.9, -11.446, 2, 8.667, -11.446, 2, 9.733, -11.446, 1, 9.778, -11.446, 9.822, -11.699, 9.867, -13.64, 1, 10.1, -23.828, 10.334, -30, 10.567, -30, 2, 13, -30, 1, 13.2, -30, 13.4, -30.39, 13.6, -28, 1, 13.811, -25.477, 14.022, 0, 14.233, 0, 2, 14.567, 0]}, {"Target": "Parameter", "Id": "Param6", "Segments": [0, 0, 2, 0.333, 0, 0, 0.5, 22.719, 0, 0.833, 12.719, 2, 1.567, 12.719, 2, 3, 12.719, 0, 3.367, 17.041, 1, 3.534, 17.041, 3.7, 11.125, 3.867, 10.37, 1, 4.411, 7.902, 4.956, 7.399, 5.5, 7.399, 2, 5.633, 7.399, 2, 6.3, 7.399, 0, 6.667, 13.099, 1, 6.911, 13.1, 7.156, 13.1, 7.4, 13.1, 0, 7.9, 8.493, 2, 8.667, 8.493, 2, 9.733, 8.493, 0, 9.867, 7.64, 0, 10.567, 23, 2, 13, 23, 0, 14.233, 0, 2, 14.567, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 2, 0.333, 0, 2, 0.833, 0, 2, 1.567, 0, 2, 3, 0, 2, 4.167, 0, 0, 4.733, 0.4, 0, 5.633, 0, 2, 6.3, 0, 2, 7.4, 0, 0, 8.3, 0.5, 1, 8.422, 0.5, 8.545, 0.499, 8.667, 0.419, 1, 9.022, 0.185, 9.378, 0, 9.733, 0, 2, 10.567, 0, 0, 11.733, 0.5, 0, 13, 0, 2, 14.233, 0, 2, 14.567, 0]}, {"Target": "Parameter", "Id": "Param201", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 0, 0.467, 1, 2, 1.567, 1, 2, 3, 1, 2, 5.633, 1, 2, 6.3, 1, 2, 7.9, 1, 2, 8.667, 1, 2, 9.733, 1, 2, 10.033, 1, 0, 10.067, 0, 2, 10.567, 0, 2, 13.967, 0, 2, 14, 0, 2, 14.567, 0]}, {"Target": "Parameter", "Id": "Param202", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.467, 0, 2, 1.567, 0, 2, 3, 0, 2, 5.633, 0, 2, 6.3, 0, 2, 7.9, 0, 2, 8.667, 0, 2, 9.733, 0, 2, 10.033, 0, 0, 10.067, 1, 2, 10.567, 1, 2, 13.967, 1, 0, 14, 0, 2, 14.567, 0]}, {"Target": "Parameter", "Id": "Param205", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.467, 0, 2, 1.567, 0, 2, 3, 0, 2, 5.633, 0, 2, 6.3, 0, 2, 7.9, 0, 2, 8.667, 0, 2, 9.733, 0, 2, 10.033, 0, 0, 10.067, 1, 2, 10.567, 1, 2, 13.967, 1, 0, 14, 0, 2, 14.567, 0]}, {"Target": "Parameter", "Id": "Param209", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 0, 0.467, 1, 2, 1.567, 1, 2, 3, 1, 2, 5.633, 1, 2, 6.3, 1, 2, 7.9, 1, 2, 8.667, 1, 2, 9.733, 1, 2, 10.033, 1, 0, 10.067, 0, 2, 10.567, 0, 2, 13.967, 0, 2, 14, 0, 2, 14.567, 0]}, {"Target": "Parameter", "Id": "Param214", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.467, 0, 2, 1.567, 0, 2, 3.433, 0, 0, 3.467, 1, 2, 5.633, 1, 2, 6.3, 1, 2, 7.9, 1, 2, 8.667, 1, 2, 9.733, 1, 2, 10.033, 1, 0, 10.067, 0, 2, 10.567, 0, 2, 13.967, 0, 2, 14, 0, 2, 14.567, 0]}, {"Target": "Parameter", "Id": "Param217", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 0, 0.467, 1, 2, 1.567, 1, 2, 3.433, 1, 0, 3.467, 0, 2, 5.633, 0, 2, 6.3, 0, 2, 7.9, 0, 2, 8.667, 0, 2, 9.733, 0, 2, 10.567, 0, 2, 13.967, 0, 2, 14, 0, 2, 14.567, 0]}, {"Target": "Parameter", "Id": "Param9", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, -5.019, 0, 0.567, -4.5, 2, 0.833, -4.5, 0, 1.067, -5.008, 0, 1.3, -4.435, 0, 1.567, -4.849, 0, 1.833, -4.435, 0, 2.1, -4.732, 0, 2.5, -3.882, 0, 2.767, -4.037, 2, 3, -4.037, 2, 4.133, -4.037, 0, 4.433, -3.348, 0, 5.033, -4.577, 1, 5.189, -4.577, 5.344, -4.422, 5.5, -4.262, 1, 5.767, -3.989, 6.033, -3.848, 6.3, -3.542, 1, 6.4, -3.427, 6.5, -2.882, 6.6, -2.882, 0, 6.867, -3.314, 0, 7.1, -2.879, 1, 7.178, -2.879, 7.255, -3.315, 7.333, -3.376, 1, 7.522, -3.524, 7.711, -3.542, 7.9, -3.542, 2, 8.667, -3.542, 2, 9.733, -3.542, 0, 10.033, -7.068, 0, 10.333, -5.028, 0, 10.8, -7.068, 0, 11.267, -6.588, 0, 11.767, -7.052, 0, 12.3, -6.452, 0, 12.9, -7.068, 0, 14.1, 0.3, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param10", "Segments": [0, 0, 2, 0.333, 0, 1, 0.355, 0, 0.378, 1.918, 0.4, 6.253, 1, 0.411, 8.42, 0.422, 9.789, 0.433, 9.789, 0, 0.567, 8.04, 0, 0.833, 11.58, 0, 1.567, 8.04, 2, 3, 8.04, 2, 4.133, 8.04, 2, 5.033, 8.04, 2, 5.5, 8.04, 2, 6.3, 8.04, 2, 7.9, 8.04, 2, 8.667, 8.04, 2, 9.733, 8.04, 0, 10, 9.118, 1, 10.022, 9.118, 10.045, 8.26, 10.067, 7.795, 1, 10.2, 5.006, 10.334, 1.545, 10.467, 0, 1, 10.578, -1.287, 10.689, -1.316, 10.8, -1.316, 0, 11.267, -0.296, 0, 11.767, -1.871, 0, 12.3, -1.391, 1, 12.533, -1.391, 12.767, -1.487, 13, -1.871, 1, 13.133, -2.09, 13.267, -2.378, 13.4, -2.378, 1, 13.678, -2.378, 13.955, -1.466, 14.233, -0.42, 1, 14.344, -0.001, 14.456, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param11", "Segments": [0, 0, 2, 0.333, 0, 2, 0.567, 0, 2, 1.567, 0, 2, 3, 0, 2, 4.133, 0, 2, 5.033, 0, 2, 5.5, 0, 2, 6.3, 0, 2, 7.9, 0, 2, 8.667, 0, 2, 9.733, 0, 2, 10.567, 0, 2, 10.8, 0, 2, 13, 0, 0, 13.967, -3.659, 1, 13.978, -3.659, 13.989, 3.886, 14, 4.06, 1, 14.111, 5.801, 14.222, 6.48, 14.333, 6.48, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param12", "Segments": [0, 0, 2, 0.333, 0, 2, 0.567, 0, 2, 1.567, 0, 2, 3, 0, 2, 4.133, 0, 2, 5.033, 0, 2, 5.5, 0, 2, 6.3, 0, 2, 7.9, 0, 2, 8.667, 0, 2, 9.733, 0, 2, 10.567, 0, 2, 10.8, 0, 0, 11.267, 8, 2, 13, 8, 0, 13.9, 19, 0, 14, -6.52, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param197", "Segments": [0, 0, 2, 0.333, 0, 2, 0.567, 0, 2, 1.567, 0, 2, 3, 0, 2, 4.133, 0, 2, 5.033, 0, 2, 5.5, 0, 2, 6.3, 0, 2, 7.9, 0, 2, 8.667, 0, 2, 9.733, 0, 0, 10.067, 26.28, 1, 10.167, 26.28, 10.267, 11.316, 10.367, 5.771, 1, 10.434, 2.075, 10.5, 0.818, 10.567, -0.666, 1, 10.645, -2.396, 10.722, -2.745, 10.8, -2.745, 0, 11.267, 6.315, 0, 11.767, 3.309, 0, 12.3, 13.095, 2, 13, 13.095, 0, 13.833, -6, 0, 14.233, 0, 2, 14.567, 0]}, {"Target": "Parameter", "Id": "Param198", "Segments": [0, 0, 2, 0.333, 0, 2, 0.567, 0, 2, 1.567, 0, 2, 3, 0, 2, 4.133, 0, 2, 5.033, 0, 2, 5.5, 0, 2, 6.3, 0, 2, 7.9, 0, 2, 8.667, 0, 2, 9.733, 0, 2, 10.133, 0, 0, 10.367, 30, 0, 10.6, 0, 0, 11.267, 2.186, 0, 11.767, -12.919, 1, 12.178, -12.919, 12.589, -10.927, 13, 1, 1, 13.278, 9.059, 13.555, 24, 13.833, 24, 0, 14.233, 0, 2, 14.567, 0]}, {"Target": "Parameter", "Id": "Param22", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, 1, 2, 13.967, 1, 2, 14.3, 1, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param13", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, -3.984, 0, 0.567, -0.06, 2, 1.567, -0.06, 2, 3, -0.06, 2, 4.133, -0.06, 2, 5.033, -0.06, 2, 5.5, -0.06, 2, 6.3, -0.06, 2, 7.9, -0.06, 2, 8.667, -0.06, 2, 9.733, -0.06, 2, 10.567, -0.06, 2, 10.8, -0.06, 2, 13, -0.06, 0, 14.233, 0, 2, 14.567, 0]}, {"Target": "Parameter", "Id": "Param14", "Segments": [0, 0, 2, 0.333, 0, 1, 0.355, 0, 0.378, -9.375, 0.4, -14.964, 1, 0.411, -17.758, 0.422, -17.28, 0.433, -17.28, 0, 0.567, 0, 2, 1.567, 0, 2, 3, 0, 2, 4.133, 0, 2, 5.033, 0, 2, 5.5, 0, 2, 6.3, 0, 2, 7.9, 0, 2, 8.667, 0, 2, 9.733, 0, 0, 10.067, -26, 0, 10.4, 0, 0, 10.8, -6.3, 0, 11.767, 0, 2, 13, 0, 2, 14.233, 0, 2, 14.567, 0]}, {"Target": "Parameter", "Id": "Param279", "Segments": [0, 0, 2, 0.333, 0, 2, 10.3, 0, 0, 10.333, 1, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param210", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, 4.997, 1, 0.444, 4.997, 0.456, 3.749, 0.467, 1.982, 1, 0.478, 0.215, 0.489, -2.34, 0.5, -2.757, 1, 0.522, -3.592, 0.545, -4.356, 0.567, -4.68, 1, 0.656, -5.976, 0.744, -6.42, 0.833, -6.42, 0, 1.033, -4.522, 0, 1.3, -6.761, 0, 1.567, -5.64, 0, 1.833, -6.24, 0, 2.133, -5.22, 0, 2.5, -7.567, 0, 2.8, -6.761, 2, 3, -6.761, 0, 3.867, 3.079, 2, 4.133, 3.079, 0, 4.433, 3.016, 0, 5.033, 4.399, 1, 5.189, 4.399, 5.344, 3.82, 5.5, 3.629, 1, 5.767, 3.301, 6.033, 3.269, 6.3, 3.269, 0, 6.6, 3.449, 1, 6.689, 3.449, 6.778, 3.317, 6.867, 3.309, 1, 7.211, 3.276, 7.556, 3.269, 7.9, 3.269, 2, 8.667, 3.269, 2, 9.733, 3.269, 1, 9.844, 3.269, 9.956, 6.828, 10.067, 17.574, 1, 10.134, 24.021, 10.2, 30, 10.267, 30, 2, 10.567, 30, 2, 13, 30, 0, 14.233, 0, 2, 14.567, 0]}, {"Target": "Parameter", "Id": "Param211", "Segments": [0, 0, 2, 0.333, 0, 0, 0.567, 15, 0, 0.9, 0.9, 0, 1.133, 5.076, 0, 1.367, 0.9, 0, 1.667, 5.076, 0, 1.9, 1.92, 0, 2.2, 5.076, 2, 2.533, 5.076, 2, 2.867, 5.076, 2, 3, 5.076, 0, 3.867, -10.944, 2, 4.133, -10.944, 0, 5.033, -14.124, 0, 5.5, -12.27, 2, 6.3, -12.27, 0, 6.6, -13.71, 1, 6.689, -13.71, 6.778, -13.13, 6.867, -12.353, 1, 6.945, -11.673, 7.022, -11.474, 7.1, -11.474, 0, 7.333, -12.358, 0, 7.9, -12.27, 2, 8.667, -12.27, 2, 9.733, -12.27, 2, 10.567, -12.27, 2, 13, -12.27, 0, 14.233, 0, 2, 14.567, 0]}, {"Target": "Parameter", "Id": "Param228", "Segments": [0, 0, 2, 0.333, 0, 2, 0.567, 0, 0, 0.833, -7.08, 2, 1.567, -7.08, 2, 1.867, -7.08, 0, 2.167, -10.02, 0, 2.5, -7.8, 0, 2.867, -9.42, 2, 3, -9.42, 0, 3.867, -10, 2, 4.133, -10, 0, 4.433, -8.296, 0, 5.033, -13.36, 0, 5.5, -11.401, 2, 6.3, -11.401, 2, 7.9, -11.401, 2, 8.667, -11.401, 2, 9.733, -11.401, 2, 10.567, -11.401, 2, 13, -11.401, 0, 14.233, 0, 2, 14.567, 0]}, {"Target": "Parameter", "Id": "Param221", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 0, 0.467, 1, 2, 1.567, 1, 2, 3, 1, 2, 5.633, 1, 2, 6.3, 1, 2, 7.9, 1, 2, 8.667, 1, 2, 9.733, 1, 2, 10.567, 1, 2, 13, 1, 2, 13.733, 1, 0, 13.767, 0, 2, 14.567, 0]}, {"Target": "Parameter", "Id": "Param223", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 0, 0.467, 1, 2, 1.567, 1, 2, 3, 1, 2, 5.633, 1, 2, 6.3, 1, 2, 7.9, 1, 2, 8.667, 1, 2, 9.733, 1, 2, 10.567, 1, 2, 13, 1, 2, 13.733, 1, 0, 13.767, 0, 2, 14.567, 0]}, {"Target": "Parameter", "Id": "Param238", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 2, 0.467, 0, 2, 1.567, 0, 2, 3.433, 0, 0, 3.467, 1, 2, 5.633, 1, 2, 6.3, 1, 2, 7.9, 1, 2, 8.667, 1, 2, 9.733, 1, 2, 10.567, 1, 2, 13, 1, 2, 13.733, 1, 0, 13.767, 0, 2, 14.567, 0]}, {"Target": "Parameter", "Id": "Param243", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 0, 0.467, 1, 2, 1.567, 1, 2, 3.433, 1, 0, 3.467, 0, 2, 5.633, 0, 2, 6.3, 0, 2, 7.9, 0, 2, 8.667, 0, 2, 9.733, 0, 2, 10.567, 0, 2, 13, 0, 2, 13.733, 0, 2, 13.767, 0, 2, 14.567, 0]}, {"Target": "Parameter", "Id": "Param15", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, 4.8, 2, 0.567, 4.8, 2, 1.567, 4.8, 2, 3, 4.8, 2, 4.133, 4.8, 0, 4.433, 4.167, 0, 5.033, 6.06, 1, 5.189, 6.06, 5.344, 5.614, 5.5, 5.325, 1, 5.767, 4.83, 6.033, 4.725, 6.3, 4.725, 0, 6.6, 5.505, 0, 6.867, 4.94, 0, 7.067, 5.505, 1, 7.156, 5.505, 7.244, 5.031, 7.333, 4.94, 1, 7.522, 4.746, 7.711, 4.725, 7.9, 4.725, 2, 8.667, 4.725, 2, 9.733, 4.725, 2, 10.567, 4.725, 2, 13, 4.725, 0, 13.733, 6.547, 1, 13.744, 6.547, 13.756, 5.135, 13.767, 4.936, 1, 13.978, 1.161, 14.189, -0.525, 14.4, -0.525, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param16", "Segments": [0, 0, 2, 0.333, 0, 0, 0.433, 7.68, 2, 0.567, 7.68, 2, 1.567, 7.68, 2, 3, 7.68, 2, 4.133, 7.68, 2, 5.033, 7.68, 2, 5.5, 7.68, 2, 6.3, 7.68, 2, 7.9, 7.68, 2, 8.667, 7.68, 2, 9.733, 7.68, 2, 10.567, 7.68, 2, 13, 7.68, 1, 13.256, 7.68, 13.511, 6.539, 13.767, 3.8, 1, 13.789, 3.562, 13.811, 1.511, 13.833, 1.163, 1, 13.933, -0.401, 14.033, -0.924, 14.133, -0.924, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param17", "Segments": [0, 0, 2, 0.333, 0, 0, 0.4, 5.52, 0, 0.567, 0, 2, 1.567, 0, 2, 3, 0, 2, 4.133, 0, 2, 5.033, 0, 2, 5.5, 0, 2, 6.3, 0, 2, 7.9, 0, 2, 8.667, 0, 2, 9.733, 0, 2, 10.567, 0, 2, 13, 0, 0, 13.8, -2.729, 0, 14.367, 2.689, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param18", "Segments": [0, 0, 2, 0.333, 0, 1, 0.355, 0, 0.378, 30, 0.4, 30, 0, 0.567, 0, 2, 1.567, 0, 2, 3, 0, 2, 4.133, 0, 2, 5.033, 0, 2, 5.5, 0, 2, 6.3, 0, 2, 7.9, 0, 2, 8.667, 0, 2, 9.733, 0, 2, 10.567, 0, 2, 13, 0, 0, 13.967, -10.68, 0, 14.3, 0.784, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param19", "Segments": [0, 0, 2, 0.333, 0, 1, 0.411, 0, 0.489, -4.985, 0.567, -18.63, 1, 0.656, -34.224, 0.744, -44.27, 0.833, -44.27, 2, 1.567, -44.27, 2, 3, -44.27, 2, 4.133, -44.27, 2, 5.033, -44.27, 2, 5.5, -44.27, 2, 6.3, -44.27, 2, 7.9, -44.27, 2, 8.667, -44.27, 2, 9.733, -44.27, 2, 10.567, -44.27, 2, 13, -44.27, 0, 14.233, 0, 2, 14.567, 0]}, {"Target": "Parameter", "Id": "Param20", "Segments": [0, 0, 2, 0.333, 0, 2, 0.433, 0, 0, 0.567, -30, 2, 1.567, -30, 2, 3, -30, 2, 4.133, -30, 2, 5.033, -30, 2, 5.5, -30, 2, 6.3, -30, 2, 7.9, -30, 2, 8.667, -30, 2, 9.733, -30, 2, 10.567, -30, 2, 13, -30, 1, 13.267, -30, 13.533, -29.066, 13.8, -21.805, 1, 13.944, -17.872, 14.089, 0, 14.233, 0, 2, 14.567, 0]}, {"Target": "Parameter", "Id": "Param233", "Segments": [0, 0, 2, 0.333, 0, 0, 0.467, 2.26, 0, 0.567, 0.903, 0, 0.833, 1.74, 0, 1.033, 0.865, 0, 1.3, 1.256, 0, 1.567, -0.06, 0, 1.833, 0.516, 0, 2.233, -0.54, 0, 2.5, 0.78, 0, 2.833, 0.24, 2, 3, 0.24, 0, 3.867, -1.92, 2, 4.1, -1.92, 0, 4.433, -1.62, 0, 5.033, -3.06, 0, 5.5, -2.49, 0, 6.3, -2.85, 2, 7.333, -2.85, 0, 7.9, -2.55, 2, 8.667, -2.55, 2, 9.733, -2.55, 0, 10.567, -4.53, 2, 13, -4.53, 1, 13.178, -4.53, 13.355, -4.148, 13.533, -2.181, 1, 13.6, -1.443, 13.666, 3.301, 13.733, 6.428, 1, 13.744, 6.949, 13.756, 8.473, 13.767, 8.473, 0, 14.233, 0, 2, 14.567, 0]}, {"Target": "Parameter", "Id": "Param234", "Segments": [0, 0, 2, 0.333, 0, 1, 0.411, 0, 0.489, -0.17, 0.567, -2, 1, 0.611, -3.045, 0.656, -9.425, 0.7, -9.425, 0, 0.9, -2, 0, 1.1, -6.2, 0, 1.333, -1.476, 0, 1.567, -5.133, 0, 1.833, -1.604, 0, 3, -2, 2, 3.433, -2, 0, 3.467, 8, 0, 4.1, -2, 2, 4.433, -2, 0, 5.033, 8.35, 0, 5.5, 3.175, 1, 5.767, 3.175, 6.033, 4.025, 6.3, 7.075, 1, 6.4, 8.219, 6.5, 10.375, 6.6, 10.375, 0, 6.867, 7.316, 0, 7.067, 10.375, 0, 7.333, 9.416, 0, 7.9, 10.675, 2, 8.667, 10.675, 2, 9.733, 10.675, 0, 10.567, 18.55, 2, 13, 18.55, 0, 14.233, 0, 2, 14.567, 0]}, {"Target": "Parameter", "Id": "Param235", "Segments": [0, 0, 2, 0.333, 0, 0, 0.833, -14.974, 2, 1.567, -14.974, 2, 1.833, -14.974, 0, 2.233, -16.911, 0, 2.533, -15.194, 0, 2.833, -17.974, 2, 3, -17.974, 2, 4.1, -17.974, 0, 4.433, -16.834, 0, 5.033, -25.954, 0, 5.5, -21.964, 2, 6.3, -21.964, 2, 7.9, -21.964, 2, 8.667, -21.964, 2, 9.733, -21.964, 2, 10.567, -21.964, 2, 13, -21.964, 0, 13.733, -23.518, 0, 14.233, 0, 2, 14.567, 0]}, {"Target": "Parameter", "Id": "Param274", "Segments": [0, 0, 2, 0.333, 0, 2, 0.833, 0, 2, 1.567, 0, 2, 3, 0, 0, 3.033, -0.6, 2, 4.1, -0.6, 2, 4.433, -0.6, 2, 5.033, -0.6, 2, 5.5, -0.6, 2, 6.3, -0.6, 2, 7.9, -0.6, 2, 8.667, -0.6, 2, 9.733, -0.6, 2, 10.567, -0.6, 2, 13, -0.6, 0, 14.233, 0, 2, 14.567, 0]}, {"Target": "Parameter", "Id": "Param277", "Segments": [0, 0, 2, 0.333, 0, 0, 0.7, 16.952, 0, 1, -19.513, 0, 1.233, 15.363, 0, 1.467, -12.677, 0, 1.733, 15.514, 0, 1.967, -12.942, 0, 2.233, 13.739, 0, 2.533, -9.597, 0, 2.9, 30, 0, 3.3, -27.961, 0, 3.7, 30, 0, 4.2, -27.961, 0, 4.7, 30, 0, 5.233, -27.961, 0, 5.8, 26.436, 0, 6.533, -4.245, 0, 6.8, 13.739, 0, 7.267, -7.032, 0, 7.7, 15.403, 0, 8.133, -30, 0, 8.567, 30, 0, 9.133, -30, 0, 9.767, 30, 0, 10.4, -30, 0, 10.867, 30, 0, 11.767, -30, 0, 12.467, 30, 0, 13.333, -30, 0, 14.067, 20.429, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param278", "Segments": [0, 0, 2, 0.333, 0, 2, 2.3, 0, 0, 2.767, -0.801, 0, 3.333, 0.802, 0, 3.8, 0.001, 2, 4.567, 0.001, 2, 5.1, 0.001, 2, 5.567, 0.001, 2, 6.3, 0.001, 0, 6.6, 0.542, 0, 7.1, -0.554, 0, 7.867, 0.581, 0, 8.533, -0.554, 0, 9.167, 0.59, 0, 9.867, -0.631, 0, 10.333, 0.547, 0, 11.233, -0.463, 0, 12.033, 0.572, 0, 12.833, -0.506, 0, 13.433, 0.534, 0, 14.067, -0.382, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param280", "Segments": [0, 0, 2, 0.333, 0, 2, 2.1, 0, 2, 2.9, 0, 0, 3.1, -0.424, 0, 3.5, 0.619, 0, 3.967, -0.71, 0, 4.333, 0.482, 0, 4.8, -0.541, 0, 5.4, 0.417, 0, 6.1, -0.295, 0, 6.467, 0.002, 0, 7.633, -0.002, 0, 8.033, 0.715, 0, 8.667, -0.64, 0, 9.167, 0.658, 0, 9.667, -0.541, 0, 10.133, 0.577, 0, 10.633, -0.424, 0, 11.267, 0.417, 0, 12.133, -0.374, 0, 12.933, 0.482, 0, 13.5, -0.374, 0, 14.067, 0.577, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param7", "Segments": [0, 0, 2, 0.333, 0, 0, 1.733, -9, 0, 3.133, 0, 0, 4.533, -9, 0, 5.933, 0, 0, 7.333, -9, 0, 8.733, 0, 0, 10.133, -9, 0, 11.567, 0, 0, 13, -9, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param66", "Segments": [0, 0, 2, 0.333, 0, 0, 1.733, -19, 0, 3.133, 0, 0, 4.533, -19, 0, 5.933, 0, 0, 7.333, -19, 0, 8.733, 0, 0, 10.133, -19, 0, 11.567, 0, 0, 13, -19, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param67", "Segments": [0, 0, 2, 0.333, 0, 0, 1, 16, 0, 2.4, -16, 1, 2.644, -16, 2.889, -11.145, 3.133, 0, 1, 3.355, 10.132, 3.578, 16, 3.8, 16, 0, 5.233, -16, 1, 5.466, -16, 5.7, -10.412, 5.933, 0, 1, 6.178, 10.908, 6.422, 16, 6.667, 16, 0, 8.067, -16, 1, 8.289, -16, 8.511, -10.132, 8.733, 0, 1, 8.978, 11.145, 9.222, 16, 9.467, 16, 0, 10.867, -16, 1, 11.1, -16, 11.334, -10.667, 11.567, 0, 1, 11.8, 10.667, 12.034, 16, 12.267, 16, 0, 13.8, -16, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param68", "Segments": [0, 0, 2, 0.333, 0, 0, 1.733, 17, 0, 3.133, 0, 0, 4.533, 17, 0, 5.933, 0, 0, 7.333, 17, 0, 8.733, 0, 0, 10.133, 17, 0, 11.567, 0, 0, 13, 17, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param69", "Segments": [0, 0, 2, 0.333, 0, 0, 1, -16, 0, 2.4, 16, 1, 2.644, 16, 2.889, 11.145, 3.133, 0, 1, 3.355, -10.132, 3.578, -16, 3.8, -16, 0, 5.233, 16, 1, 5.466, 16, 5.7, 10.412, 5.933, 0, 1, 6.178, -10.908, 6.422, -16, 6.667, -16, 0, 8.067, 16, 1, 8.289, 16, 8.511, 10.132, 8.733, 0, 1, 8.978, -11.145, 9.222, -16, 9.467, -16, 0, 10.867, 16, 1, 11.1, 16, 11.334, 10.667, 11.567, 0, 1, 11.8, -10.667, 12.034, -16, 12.267, -16, 0, 13.8, 16, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param35", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, 5.28, 0, 0.667, -6.17, 0, 1, 3.205, 0, 1.367, -1.482, 0, 1.767, 0.508, 0, 2.167, -2.712, 0, 2.5, 3.44, 0, 2.867, -2.032, 0, 3.1, 7.793, 0, 3.4, -15.103, 0, 3.767, 21.872, 0, 4.233, -26.185, 0, 4.6, 22.422, 0, 5.033, -15.655, 0, 5.567, 8.809, 0, 6.333, -5.279, 0, 6.667, 3.265, 0, 7.1, -1.321, 0, 7.4, -0.065, 0, 7.9, -9.818, 0, 8.333, 14.869, 0, 8.933, -15.57, 0, 9.4, 18.164, 0, 9.9, -17.102, 0, 10.367, 18.898, 0, 10.833, -13.669, 0, 11.4, 5.807, 0, 12.467, -4.787, 0, 13.233, 10.268, 0, 13.733, -14.271, 0, 14.3, 10.451, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param36", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, -3.87, 0, 0.667, 8.09, 0, 0.9, -5.782, 0, 1.233, 4.264, 0, 1.6, -2.583, 0, 2.1, 2.329, 0, 2.4, -4.149, 0, 2.733, 4.495, 0, 3.067, -8.683, 0, 3.333, 13.033, 0, 3.633, -20.925, 0, 4.033, 26.428, 0, 4.4, -30, 2, 4.467, -30, 0, 4.833, 29.384, 0, 5.267, -20.171, 0, 5.7, 11.144, 0, 6.133, -3.777, 0, 6.367, -0.101, 0, 6.567, -3.594, 0, 6.9, 4.081, 0, 7.3, -2.502, 0, 7.633, 1.941, 0, 7.667, 1.894, 0, 7.833, 4.337, 0, 8.167, -12.49, 0, 8.567, 13.62, 0, 9.167, -13.181, 0, 9.633, 17.103, 0, 10.133, -19.801, 0, 10.567, 19.81, 0, 11, -15.059, 0, 11.467, 6.399, 0, 11.9, -2.259, 0, 12.3, 2.301, 0, 12.733, -2.7, 0, 13, -0.762, 0, 13.167, -2.491, 0, 13.5, 9.501, 0, 13.967, -10.59, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param37", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, 6.487, 0, 0.667, -13.095, 0, 1, 14.231, 0, 1.233, -15.526, 0, 1.467, 13.012, 0, 1.733, -10.729, 0, 1.967, 8.162, 0, 2.2, -10.726, 0, 2.533, 21.526, 0, 3, -27.198, 0, 3.5, 23.548, 0, 4.233, -22.593, 0, 4.667, 19.398, 0, 5.167, -3.668, 0, 5.467, 1.377, 0, 5.8, -0.927, 0, 6.2, -0.11, 0, 6.267, -0.111, 0, 6.333, -0.081, 0, 6.6, -11.782, 0, 6.967, 29.849, 0, 7.3, -29.634, 0, 7.6, -0.118, 0, 7.767, -2.702, 0, 8.133, 15.659, 0, 8.767, -15.489, 0, 9.4, 15.309, 0, 10.1, -20.162, 0, 10.467, 20.602, 0, 10.767, -0.356, 0, 11, 4.219, 0, 11.5, -9.825, 0, 12.3, 10.195, 0, 13.067, -14.842, 0, 13.633, 11.147, 0, 14.233, -10.095, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param39", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -4.103, 0, 0.633, 12.114, 0, 0.833, -14.543, 0, 1.133, 20.904, 0, 1.4, -22.363, 0, 1.633, 20.106, 0, 1.9, -17.113, 0, 2.133, 15.194, 0, 2.4, -15.327, 0, 2.733, 19.337, 0, 3.1, -13.998, 0, 3.633, 9.583, 0, 3.9, -1.909, 0, 4.133, 4.356, 0, 4.433, -12.225, 0, 4.8, 15.06, 0, 5.1, -7.304, 0, 5.533, 2.222, 0, 5.9, -1.263, 0, 6.2, 0.555, 0, 6.333, 0.208, 0, 6.533, 4.128, 0, 6.8, -15.351, 0, 7.133, 29.366, 0, 7.433, -26.113, 0, 7.733, 13.519, 0, 8, -10.314, 0, 8.3, 9.654, 0, 8.6, -1.165, 0, 8.7, -0.799, 0, 8.9, -4.786, 0, 9.167, -1.26, 0, 9.233, -1.371, 0, 9.533, 4.771, 0, 9.8, 0.039, 0, 10.033, 4.611, 0, 10.333, -14.566, 0, 10.633, 18.288, 0, 10.9, -11.038, 0, 11.2, 7.075, 0, 11.6, -4.169, 0, 11.9, 0.632, 0, 12.167, -1.898, 0, 12.433, 3.17, 0, 12.733, -0.051, 0, 13, 3.241, 0, 13.267, -7.239, 0, 13.8, 4.453, 0, 14.1, -0.057, 0, 14.133, -0.002, 0, 14.4, -4.918, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param38", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, 6.487, 0, 0.667, -13.095, 0, 1, 14.231, 0, 1.233, -15.526, 0, 1.467, 13.012, 0, 1.733, -10.729, 0, 1.967, 8.162, 0, 2.2, -10.726, 0, 2.533, 21.526, 0, 3, -27.198, 0, 3.5, 23.548, 0, 4.233, -22.593, 0, 4.667, 19.398, 0, 5.167, -3.668, 0, 5.467, 1.377, 0, 5.8, -0.927, 0, 6.2, -0.11, 0, 6.267, -0.111, 0, 6.333, -0.081, 0, 6.6, -11.782, 0, 6.967, 29.849, 0, 7.3, -29.634, 0, 7.6, -0.118, 0, 7.767, -2.702, 0, 8.133, 15.659, 0, 8.767, -15.489, 0, 9.4, 15.309, 0, 10.1, -20.162, 0, 10.467, 20.602, 0, 10.767, -0.356, 0, 11, 4.219, 0, 11.5, -9.825, 0, 12.3, 10.195, 0, 13.067, -14.842, 0, 13.633, 11.147, 0, 14.233, -10.095, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param40", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -4.103, 0, 0.633, 12.114, 0, 0.833, -14.543, 0, 1.133, 20.904, 0, 1.4, -22.363, 0, 1.633, 20.106, 0, 1.9, -17.113, 0, 2.133, 15.194, 0, 2.4, -15.327, 0, 2.733, 19.337, 0, 3.1, -13.998, 0, 3.633, 9.583, 0, 3.9, -1.909, 0, 4.133, 4.356, 0, 4.433, -12.225, 0, 4.8, 15.06, 0, 5.1, -7.304, 0, 5.533, 2.222, 0, 5.9, -1.263, 0, 6.2, 0.555, 0, 6.333, 0.208, 0, 6.533, 4.128, 0, 6.8, -15.351, 0, 7.133, 29.366, 0, 7.433, -26.113, 0, 7.733, 13.519, 0, 8, -10.314, 0, 8.3, 9.654, 0, 8.6, -1.165, 0, 8.7, -0.799, 0, 8.9, -4.786, 0, 9.167, -1.26, 0, 9.233, -1.371, 0, 9.533, 4.771, 0, 9.8, 0.039, 0, 10.033, 4.611, 0, 10.333, -14.566, 0, 10.633, 18.288, 0, 10.9, -11.038, 0, 11.2, 7.075, 0, 11.6, -4.169, 0, 11.9, 0.632, 0, 12.167, -1.898, 0, 12.433, 3.17, 0, 12.733, -0.051, 0, 13, 3.241, 0, 13.267, -7.239, 0, 13.8, 4.453, 0, 14.1, -0.057, 0, 14.133, -0.002, 0, 14.4, -4.918, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param41", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, 6.487, 0, 0.667, -13.095, 0, 1, 14.231, 0, 1.233, -15.526, 0, 1.467, 13.012, 0, 1.733, -10.729, 0, 1.967, 8.162, 0, 2.2, -10.726, 0, 2.533, 21.526, 0, 3, -27.198, 0, 3.5, 23.548, 0, 4.233, -22.593, 0, 4.667, 19.398, 0, 5.167, -3.668, 0, 5.467, 1.377, 0, 5.8, -0.927, 0, 6.2, -0.11, 0, 6.267, -0.111, 0, 6.333, -0.081, 0, 6.6, -11.782, 0, 6.967, 29.849, 0, 7.3, -29.634, 0, 7.6, -0.118, 0, 7.767, -2.702, 0, 8.133, 15.659, 0, 8.767, -15.489, 0, 9.4, 15.309, 0, 10.1, -20.162, 0, 10.467, 20.602, 0, 10.767, -0.356, 0, 11, 4.219, 0, 11.5, -9.825, 0, 12.3, 10.195, 0, 13.067, -14.842, 0, 13.633, 11.147, 0, 14.233, -10.095, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param42", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -4.103, 0, 0.633, 12.114, 0, 0.833, -14.543, 0, 1.133, 20.904, 0, 1.4, -22.363, 0, 1.633, 20.106, 0, 1.9, -17.113, 0, 2.133, 15.194, 0, 2.4, -15.327, 0, 2.733, 19.337, 0, 3.1, -13.998, 0, 3.633, 9.583, 0, 3.9, -1.909, 0, 4.133, 4.356, 0, 4.433, -12.225, 0, 4.8, 15.06, 0, 5.1, -7.304, 0, 5.533, 2.222, 0, 5.9, -1.263, 0, 6.2, 0.555, 0, 6.333, 0.208, 0, 6.533, 4.128, 0, 6.8, -15.351, 0, 7.133, 29.366, 0, 7.433, -26.113, 0, 7.733, 13.519, 0, 8, -10.314, 0, 8.3, 9.654, 0, 8.6, -1.165, 0, 8.7, -0.799, 0, 8.9, -4.786, 0, 9.167, -1.26, 0, 9.233, -1.371, 0, 9.533, 4.771, 0, 9.8, 0.039, 0, 10.033, 4.611, 0, 10.333, -14.566, 0, 10.633, 18.288, 0, 10.9, -11.038, 0, 11.2, 7.075, 0, 11.6, -4.169, 0, 11.9, 0.632, 0, 12.167, -1.898, 0, 12.433, 3.17, 0, 12.733, -0.051, 0, 13, 3.241, 0, 13.267, -7.239, 0, 13.8, 4.453, 0, 14.1, -0.057, 0, 14.133, -0.002, 0, 14.4, -4.918, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param43", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, 6.487, 0, 0.667, -13.095, 0, 1, 14.231, 0, 1.233, -15.526, 0, 1.467, 13.012, 0, 1.733, -10.729, 0, 1.967, 8.162, 0, 2.2, -10.726, 0, 2.533, 21.526, 0, 3, -27.198, 0, 3.5, 23.548, 0, 4.233, -22.593, 0, 4.667, 19.398, 0, 5.167, -3.668, 0, 5.467, 1.377, 0, 5.8, -0.927, 0, 6.2, -0.11, 0, 6.267, -0.111, 0, 6.333, -0.081, 0, 6.6, -11.782, 0, 6.967, 29.849, 0, 7.3, -29.634, 0, 7.6, -0.118, 0, 7.767, -2.702, 0, 8.133, 15.659, 0, 8.767, -15.489, 0, 9.4, 15.309, 0, 10.1, -20.162, 0, 10.467, 20.602, 0, 10.767, -0.356, 0, 11, 4.219, 0, 11.5, -9.825, 0, 12.3, 10.195, 0, 13.067, -14.842, 0, 13.633, 11.147, 0, 14.233, -10.095, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param44", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -4.103, 0, 0.633, 12.114, 0, 0.833, -14.543, 0, 1.133, 20.904, 0, 1.4, -22.363, 0, 1.633, 20.106, 0, 1.9, -17.113, 0, 2.133, 15.194, 0, 2.4, -15.327, 0, 2.733, 19.337, 0, 3.1, -13.998, 0, 3.633, 9.583, 0, 3.9, -1.909, 0, 4.133, 4.356, 0, 4.433, -12.225, 0, 4.8, 15.06, 0, 5.1, -7.304, 0, 5.533, 2.222, 0, 5.9, -1.263, 0, 6.2, 0.555, 0, 6.333, 0.208, 0, 6.533, 4.128, 0, 6.8, -15.351, 0, 7.133, 29.366, 0, 7.433, -26.113, 0, 7.733, 13.519, 0, 8, -10.314, 0, 8.3, 9.654, 0, 8.6, -1.165, 0, 8.7, -0.799, 0, 8.9, -4.786, 0, 9.167, -1.26, 0, 9.233, -1.371, 0, 9.533, 4.771, 0, 9.8, 0.039, 0, 10.033, 4.611, 0, 10.333, -14.566, 0, 10.633, 18.288, 0, 10.9, -11.038, 0, 11.2, 7.075, 0, 11.6, -4.169, 0, 11.9, 0.632, 0, 12.167, -1.898, 0, 12.433, 3.17, 0, 12.733, -0.051, 0, 13, 3.241, 0, 13.267, -7.239, 0, 13.8, 4.453, 0, 14.1, -0.057, 0, 14.133, -0.002, 0, 14.4, -4.918, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param45", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, 6.487, 0, 0.667, -13.095, 0, 1, 14.231, 0, 1.233, -15.526, 0, 1.467, 13.012, 0, 1.733, -10.729, 0, 1.967, 8.162, 0, 2.2, -10.726, 0, 2.533, 21.526, 0, 3, -27.198, 0, 3.5, 23.548, 0, 4.233, -22.593, 0, 4.667, 19.398, 0, 5.167, -3.668, 0, 5.467, 1.377, 0, 5.8, -0.927, 0, 6.2, -0.11, 0, 6.267, -0.111, 0, 6.333, -0.081, 0, 6.6, -11.782, 0, 6.967, 29.849, 0, 7.3, -29.634, 0, 7.6, -0.118, 0, 7.767, -2.702, 0, 8.133, 15.659, 0, 8.767, -15.489, 0, 9.4, 15.309, 0, 10.1, -20.162, 0, 10.467, 20.602, 0, 10.767, -0.356, 0, 11, 4.219, 0, 11.5, -9.825, 0, 12.3, 10.195, 0, 13.067, -14.842, 0, 13.633, 11.147, 0, 14.233, -10.095, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param46", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -4.103, 0, 0.633, 12.114, 0, 0.833, -14.543, 0, 1.133, 20.904, 0, 1.4, -22.363, 0, 1.633, 20.106, 0, 1.9, -17.113, 0, 2.133, 15.194, 0, 2.4, -15.327, 0, 2.733, 19.337, 0, 3.1, -13.998, 0, 3.633, 9.583, 0, 3.9, -1.909, 0, 4.133, 4.356, 0, 4.433, -12.225, 0, 4.8, 15.06, 0, 5.1, -7.304, 0, 5.533, 2.222, 0, 5.9, -1.263, 0, 6.2, 0.555, 0, 6.333, 0.208, 0, 6.533, 4.128, 0, 6.8, -15.351, 0, 7.133, 29.366, 0, 7.433, -26.113, 0, 7.733, 13.519, 0, 8, -10.314, 0, 8.3, 9.654, 0, 8.6, -1.165, 0, 8.7, -0.799, 0, 8.9, -4.786, 0, 9.167, -1.26, 0, 9.233, -1.371, 0, 9.533, 4.771, 0, 9.8, 0.039, 0, 10.033, 4.611, 0, 10.333, -14.566, 0, 10.633, 18.288, 0, 10.9, -11.038, 0, 11.2, 7.075, 0, 11.6, -4.169, 0, 11.9, 0.632, 0, 12.167, -1.898, 0, 12.433, 3.17, 0, 12.733, -0.051, 0, 13, 3.241, 0, 13.267, -7.239, 0, 13.8, 4.453, 0, 14.1, -0.057, 0, 14.133, -0.002, 0, 14.4, -4.918, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param62", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, 6.487, 0, 0.667, -13.095, 0, 1, 14.231, 0, 1.233, -15.526, 0, 1.467, 13.012, 0, 1.733, -10.729, 0, 1.967, 8.162, 0, 2.2, -10.726, 0, 2.533, 21.526, 0, 3, -27.198, 0, 3.5, 23.548, 0, 4.233, -22.593, 0, 4.667, 19.398, 0, 5.167, -3.668, 0, 5.467, 1.377, 0, 5.8, -0.927, 0, 6.2, -0.11, 0, 6.267, -0.111, 0, 6.333, -0.081, 0, 6.6, -11.782, 0, 6.967, 29.849, 0, 7.3, -29.634, 0, 7.6, -0.118, 0, 7.767, -2.702, 0, 8.133, 15.659, 0, 8.767, -15.489, 0, 9.4, 15.309, 0, 10.1, -20.162, 0, 10.467, 20.602, 0, 10.767, -0.356, 0, 11, 4.219, 0, 11.5, -9.825, 0, 12.3, 10.195, 0, 13.067, -14.842, 0, 13.633, 11.147, 0, 14.233, -10.095, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param63", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -4.103, 0, 0.633, 12.114, 0, 0.833, -14.543, 0, 1.133, 20.904, 0, 1.4, -22.363, 0, 1.633, 20.106, 0, 1.9, -17.113, 0, 2.133, 15.194, 0, 2.4, -15.327, 0, 2.733, 19.337, 0, 3.1, -13.998, 0, 3.633, 9.583, 0, 3.9, -1.909, 0, 4.133, 4.356, 0, 4.433, -12.225, 0, 4.8, 15.06, 0, 5.1, -7.304, 0, 5.533, 2.222, 0, 5.9, -1.263, 0, 6.2, 0.555, 0, 6.333, 0.208, 0, 6.533, 4.128, 0, 6.8, -15.351, 0, 7.133, 29.366, 0, 7.433, -26.113, 0, 7.733, 13.519, 0, 8, -10.314, 0, 8.3, 9.654, 0, 8.6, -1.165, 0, 8.7, -0.799, 0, 8.9, -4.786, 0, 9.167, -1.26, 0, 9.233, -1.371, 0, 9.533, 4.771, 0, 9.8, 0.039, 0, 10.033, 4.611, 0, 10.333, -14.566, 0, 10.633, 18.288, 0, 10.9, -11.038, 0, 11.2, 7.075, 0, 11.6, -4.169, 0, 11.9, 0.632, 0, 12.167, -1.898, 0, 12.433, 3.17, 0, 12.733, -0.051, 0, 13, 3.241, 0, 13.267, -7.239, 0, 13.8, 4.453, 0, 14.1, -0.057, 0, 14.133, -0.002, 0, 14.4, -4.918, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param64", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, 6.487, 0, 0.667, -13.095, 0, 1, 14.231, 0, 1.233, -15.526, 0, 1.467, 13.012, 0, 1.733, -10.729, 0, 1.967, 8.162, 0, 2.2, -10.726, 0, 2.533, 21.526, 0, 3, -27.198, 0, 3.5, 23.548, 0, 4.233, -22.593, 0, 4.667, 19.398, 0, 5.167, -3.668, 0, 5.467, 1.377, 0, 5.8, -0.927, 0, 6.2, -0.11, 0, 6.267, -0.111, 0, 6.333, -0.081, 0, 6.6, -11.782, 0, 6.967, 29.849, 0, 7.3, -29.634, 0, 7.6, -0.118, 0, 7.767, -2.702, 0, 8.133, 15.659, 0, 8.767, -15.489, 0, 9.4, 15.309, 0, 10.1, -20.162, 0, 10.467, 20.602, 0, 10.767, -0.356, 0, 11, 4.219, 0, 11.5, -9.825, 0, 12.3, 10.195, 0, 13.067, -14.842, 0, 13.633, 11.147, 0, 14.233, -10.095, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param65", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -4.103, 0, 0.633, 12.114, 0, 0.833, -14.543, 0, 1.133, 20.904, 0, 1.4, -22.363, 0, 1.633, 20.106, 0, 1.9, -17.113, 0, 2.133, 15.194, 0, 2.4, -15.327, 0, 2.733, 19.337, 0, 3.1, -13.998, 0, 3.633, 9.583, 0, 3.9, -1.909, 0, 4.133, 4.356, 0, 4.433, -12.225, 0, 4.8, 15.06, 0, 5.1, -7.304, 0, 5.533, 2.222, 0, 5.9, -1.263, 0, 6.2, 0.555, 0, 6.333, 0.208, 0, 6.533, 4.128, 0, 6.8, -15.351, 0, 7.133, 29.366, 0, 7.433, -26.113, 0, 7.733, 13.519, 0, 8, -10.314, 0, 8.3, 9.654, 0, 8.6, -1.165, 0, 8.7, -0.799, 0, 8.9, -4.786, 0, 9.167, -1.26, 0, 9.233, -1.371, 0, 9.533, 4.771, 0, 9.8, 0.039, 0, 10.033, 4.611, 0, 10.333, -14.566, 0, 10.633, 18.288, 0, 10.9, -11.038, 0, 11.2, 7.075, 0, 11.6, -4.169, 0, 11.9, 0.632, 0, 12.167, -1.898, 0, 12.433, 3.17, 0, 12.733, -0.051, 0, 13, 3.241, 0, 13.267, -7.239, 0, 13.8, 4.453, 0, 14.1, -0.057, 0, 14.133, -0.002, 0, 14.4, -4.918, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param72", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, 6.487, 0, 0.667, -13.095, 0, 1, 14.231, 0, 1.233, -15.526, 0, 1.467, 13.012, 0, 1.733, -10.729, 0, 1.967, 8.162, 0, 2.2, -10.726, 0, 2.533, 21.526, 0, 3, -27.198, 0, 3.5, 23.548, 0, 4.233, -22.593, 0, 4.667, 19.398, 0, 5.167, -3.668, 0, 5.467, 1.377, 0, 5.8, -0.927, 0, 6.2, -0.11, 0, 6.267, -0.111, 0, 6.333, -0.081, 0, 6.6, -11.782, 0, 6.967, 29.849, 0, 7.3, -29.634, 0, 7.6, -0.118, 0, 7.767, -2.702, 0, 8.133, 15.659, 0, 8.767, -15.489, 0, 9.4, 15.309, 0, 10.1, -20.162, 0, 10.467, 20.602, 0, 10.767, -0.356, 0, 11, 4.219, 0, 11.5, -9.825, 0, 12.3, 10.195, 0, 13.067, -14.842, 0, 13.633, 11.147, 0, 14.233, -10.095, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param73", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -4.103, 0, 0.633, 12.114, 0, 0.833, -14.543, 0, 1.133, 20.904, 0, 1.4, -22.363, 0, 1.633, 20.106, 0, 1.9, -17.113, 0, 2.133, 15.194, 0, 2.4, -15.327, 0, 2.733, 19.337, 0, 3.1, -13.998, 0, 3.633, 9.583, 0, 3.9, -1.909, 0, 4.133, 4.356, 0, 4.433, -12.225, 0, 4.8, 15.06, 0, 5.1, -7.304, 0, 5.533, 2.222, 0, 5.9, -1.263, 0, 6.2, 0.555, 0, 6.333, 0.208, 0, 6.533, 4.128, 0, 6.8, -15.351, 0, 7.133, 29.366, 0, 7.433, -26.113, 0, 7.733, 13.519, 0, 8, -10.314, 0, 8.3, 9.654, 0, 8.6, -1.165, 0, 8.7, -0.799, 0, 8.9, -4.786, 0, 9.167, -1.26, 0, 9.233, -1.371, 0, 9.533, 4.771, 0, 9.8, 0.039, 0, 10.033, 4.611, 0, 10.333, -14.566, 0, 10.633, 18.288, 0, 10.9, -11.038, 0, 11.2, 7.075, 0, 11.6, -4.169, 0, 11.9, 0.632, 0, 12.167, -1.898, 0, 12.433, 3.17, 0, 12.733, -0.051, 0, 13, 3.241, 0, 13.267, -7.239, 0, 13.8, 4.453, 0, 14.1, -0.057, 0, 14.133, -0.002, 0, 14.4, -4.918, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param74", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, 6.487, 0, 0.667, -13.095, 0, 1, 14.231, 0, 1.233, -15.526, 0, 1.467, 13.012, 0, 1.733, -10.729, 0, 1.967, 8.162, 0, 2.2, -10.726, 0, 2.533, 21.526, 0, 3, -27.198, 0, 3.5, 23.548, 0, 4.233, -22.593, 0, 4.667, 19.398, 0, 5.167, -3.668, 0, 5.467, 1.377, 0, 5.8, -0.927, 0, 6.2, -0.11, 0, 6.267, -0.111, 0, 6.333, -0.081, 0, 6.6, -11.782, 0, 6.967, 29.849, 0, 7.3, -29.634, 0, 7.6, -0.118, 0, 7.767, -2.702, 0, 8.133, 15.659, 0, 8.767, -15.489, 0, 9.4, 15.309, 0, 10.1, -20.162, 0, 10.467, 20.602, 0, 10.767, -0.356, 0, 11, 4.219, 0, 11.5, -9.825, 0, 12.3, 10.195, 0, 13.067, -14.842, 0, 13.633, 11.147, 0, 14.233, -10.095, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param75", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -4.103, 0, 0.633, 12.114, 0, 0.833, -14.543, 0, 1.133, 20.904, 0, 1.4, -22.363, 0, 1.633, 20.106, 0, 1.9, -17.113, 0, 2.133, 15.194, 0, 2.4, -15.327, 0, 2.733, 19.337, 0, 3.1, -13.998, 0, 3.633, 9.583, 0, 3.9, -1.909, 0, 4.133, 4.356, 0, 4.433, -12.225, 0, 4.8, 15.06, 0, 5.1, -7.304, 0, 5.533, 2.222, 0, 5.9, -1.263, 0, 6.2, 0.555, 0, 6.333, 0.208, 0, 6.533, 4.128, 0, 6.8, -15.351, 0, 7.133, 29.366, 0, 7.433, -26.113, 0, 7.733, 13.519, 0, 8, -10.314, 0, 8.3, 9.654, 0, 8.6, -1.165, 0, 8.7, -0.799, 0, 8.9, -4.786, 0, 9.167, -1.26, 0, 9.233, -1.371, 0, 9.533, 4.771, 0, 9.8, 0.039, 0, 10.033, 4.611, 0, 10.333, -14.566, 0, 10.633, 18.288, 0, 10.9, -11.038, 0, 11.2, 7.075, 0, 11.6, -4.169, 0, 11.9, 0.632, 0, 12.167, -1.898, 0, 12.433, 3.17, 0, 12.733, -0.051, 0, 13, 3.241, 0, 13.267, -7.239, 0, 13.8, 4.453, 0, 14.1, -0.057, 0, 14.133, -0.002, 0, 14.4, -4.918, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param53", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, 6.487, 0, 0.667, -13.095, 0, 1, 14.231, 0, 1.233, -15.526, 0, 1.467, 13.012, 0, 1.733, -10.729, 0, 1.967, 8.162, 0, 2.2, -10.726, 0, 2.533, 21.526, 0, 3, -27.198, 0, 3.5, 23.548, 0, 4.233, -22.593, 0, 4.667, 19.398, 0, 5.167, -3.668, 0, 5.467, 1.377, 0, 5.8, -0.927, 0, 6.2, -0.11, 0, 6.267, -0.111, 0, 6.333, -0.081, 0, 6.6, -11.782, 0, 6.967, 29.849, 0, 7.3, -29.634, 0, 7.6, -0.118, 0, 7.767, -2.702, 0, 8.133, 15.659, 0, 8.767, -15.489, 0, 9.4, 15.309, 0, 10.1, -20.162, 0, 10.467, 20.602, 0, 10.767, -0.356, 0, 11, 4.219, 0, 11.5, -9.825, 0, 12.3, 10.195, 0, 13.067, -14.842, 0, 13.633, 11.147, 0, 14.233, -10.095, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param54", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -4.103, 0, 0.633, 12.114, 0, 0.833, -14.543, 0, 1.133, 20.904, 0, 1.4, -22.363, 0, 1.633, 20.106, 0, 1.9, -17.113, 0, 2.133, 15.194, 0, 2.4, -15.327, 0, 2.733, 19.337, 0, 3.1, -13.998, 0, 3.633, 9.583, 0, 3.9, -1.909, 0, 4.133, 4.356, 0, 4.433, -12.225, 0, 4.8, 15.06, 0, 5.1, -7.304, 0, 5.533, 2.222, 0, 5.9, -1.263, 0, 6.2, 0.555, 0, 6.333, 0.208, 0, 6.533, 4.128, 0, 6.8, -15.351, 0, 7.133, 29.366, 0, 7.433, -26.113, 0, 7.733, 13.519, 0, 8, -10.314, 0, 8.3, 9.654, 0, 8.6, -1.165, 0, 8.7, -0.799, 0, 8.9, -4.786, 0, 9.167, -1.26, 0, 9.233, -1.371, 0, 9.533, 4.771, 0, 9.8, 0.039, 0, 10.033, 4.611, 0, 10.333, -14.566, 0, 10.633, 18.288, 0, 10.9, -11.038, 0, 11.2, 7.075, 0, 11.6, -4.169, 0, 11.9, 0.632, 0, 12.167, -1.898, 0, 12.433, 3.17, 0, 12.733, -0.051, 0, 13, 3.241, 0, 13.267, -7.239, 0, 13.8, 4.453, 0, 14.1, -0.057, 0, 14.133, -0.002, 0, 14.4, -4.918, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param81", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, 6.487, 0, 0.667, -13.095, 0, 1, 14.231, 0, 1.233, -15.526, 0, 1.467, 13.012, 0, 1.733, -10.729, 0, 1.967, 8.162, 0, 2.2, -10.726, 0, 2.533, 21.526, 0, 3, -27.198, 0, 3.5, 23.548, 0, 4.233, -22.593, 0, 4.667, 19.398, 0, 5.167, -3.668, 0, 5.467, 1.377, 0, 5.8, -0.927, 0, 6.2, -0.11, 0, 6.267, -0.111, 0, 6.333, -0.081, 0, 6.6, -11.782, 0, 6.967, 29.849, 0, 7.3, -29.634, 0, 7.6, -0.118, 0, 7.767, -2.702, 0, 8.133, 15.659, 0, 8.767, -15.489, 0, 9.4, 15.309, 0, 10.1, -20.162, 0, 10.467, 20.602, 0, 10.767, -0.356, 0, 11, 4.219, 0, 11.5, -9.825, 0, 12.3, 10.195, 0, 13.067, -14.842, 0, 13.633, 11.147, 0, 14.233, -10.095, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param82", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -4.103, 0, 0.633, 12.114, 0, 0.833, -14.543, 0, 1.133, 20.904, 0, 1.4, -22.363, 0, 1.633, 20.106, 0, 1.9, -17.113, 0, 2.133, 15.194, 0, 2.4, -15.327, 0, 2.733, 19.337, 0, 3.1, -13.998, 0, 3.633, 9.583, 0, 3.9, -1.909, 0, 4.133, 4.356, 0, 4.433, -12.225, 0, 4.8, 15.06, 0, 5.1, -7.304, 0, 5.533, 2.222, 0, 5.9, -1.263, 0, 6.2, 0.555, 0, 6.333, 0.208, 0, 6.533, 4.128, 0, 6.8, -15.351, 0, 7.133, 29.366, 0, 7.433, -26.113, 0, 7.733, 13.519, 0, 8, -10.314, 0, 8.3, 9.654, 0, 8.6, -1.165, 0, 8.7, -0.799, 0, 8.9, -4.786, 0, 9.167, -1.26, 0, 9.233, -1.371, 0, 9.533, 4.771, 0, 9.8, 0.039, 0, 10.033, 4.611, 0, 10.333, -14.566, 0, 10.633, 18.288, 0, 10.9, -11.038, 0, 11.2, 7.075, 0, 11.6, -4.169, 0, 11.9, 0.632, 0, 12.167, -1.898, 0, 12.433, 3.17, 0, 12.733, -0.051, 0, 13, 3.241, 0, 13.267, -7.239, 0, 13.8, 4.453, 0, 14.1, -0.057, 0, 14.133, -0.002, 0, 14.4, -4.918, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param70", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, 6.487, 0, 0.667, -13.095, 0, 1, 14.231, 0, 1.233, -15.526, 0, 1.467, 13.012, 0, 1.733, -10.729, 0, 1.967, 8.162, 0, 2.2, -10.726, 0, 2.533, 21.526, 0, 3, -27.198, 0, 3.5, 23.548, 0, 4.233, -22.593, 0, 4.667, 19.398, 0, 5.167, -3.668, 0, 5.467, 1.377, 0, 5.8, -0.927, 0, 6.2, -0.11, 0, 6.267, -0.111, 0, 6.333, -0.081, 0, 6.6, -11.782, 0, 6.967, 29.849, 0, 7.3, -29.634, 0, 7.6, -0.118, 0, 7.767, -2.702, 0, 8.133, 15.659, 0, 8.767, -15.489, 0, 9.4, 15.309, 0, 10.1, -20.162, 0, 10.467, 20.602, 0, 10.767, -0.356, 0, 11, 4.219, 0, 11.5, -9.825, 0, 12.3, 10.195, 0, 13.067, -14.842, 0, 13.633, 11.147, 0, 14.233, -10.095, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param71", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -4.103, 0, 0.633, 12.114, 0, 0.833, -14.543, 0, 1.133, 20.904, 0, 1.4, -22.363, 0, 1.633, 20.106, 0, 1.9, -17.113, 0, 2.133, 15.194, 0, 2.4, -15.327, 0, 2.733, 19.337, 0, 3.1, -13.998, 0, 3.633, 9.583, 0, 3.9, -1.909, 0, 4.133, 4.356, 0, 4.433, -12.225, 0, 4.8, 15.06, 0, 5.1, -7.304, 0, 5.533, 2.222, 0, 5.9, -1.263, 0, 6.2, 0.555, 0, 6.333, 0.208, 0, 6.533, 4.128, 0, 6.8, -15.351, 0, 7.133, 29.366, 0, 7.433, -26.113, 0, 7.733, 13.519, 0, 8, -10.314, 0, 8.3, 9.654, 0, 8.6, -1.165, 0, 8.7, -0.799, 0, 8.9, -4.786, 0, 9.167, -1.26, 0, 9.233, -1.371, 0, 9.533, 4.771, 0, 9.8, 0.039, 0, 10.033, 4.611, 0, 10.333, -14.566, 0, 10.633, 18.288, 0, 10.9, -11.038, 0, 11.2, 7.075, 0, 11.6, -4.169, 0, 11.9, 0.632, 0, 12.167, -1.898, 0, 12.433, 3.17, 0, 12.733, -0.051, 0, 13, 3.241, 0, 13.267, -7.239, 0, 13.8, 4.453, 0, 14.1, -0.057, 0, 14.133, -0.002, 0, 14.4, -4.918, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param77", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, 6.487, 0, 0.667, -13.095, 0, 1, 14.231, 0, 1.233, -15.526, 0, 1.467, 13.012, 0, 1.733, -10.729, 0, 1.967, 8.162, 0, 2.2, -10.726, 0, 2.533, 21.526, 0, 3, -27.198, 0, 3.5, 23.548, 0, 4.233, -22.593, 0, 4.667, 19.398, 0, 5.167, -3.668, 0, 5.467, 1.377, 0, 5.8, -0.927, 0, 6.2, -0.11, 0, 6.267, -0.111, 0, 6.333, -0.081, 0, 6.6, -11.782, 0, 6.967, 29.849, 0, 7.3, -29.634, 0, 7.6, -0.118, 0, 7.767, -2.702, 0, 8.133, 15.659, 0, 8.767, -15.489, 0, 9.4, 15.309, 0, 10.1, -20.162, 0, 10.467, 20.602, 0, 10.767, -0.356, 0, 11, 4.219, 0, 11.5, -9.825, 0, 12.3, 10.195, 0, 13.067, -14.842, 0, 13.633, 11.147, 0, 14.233, -10.095, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param78", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -4.103, 0, 0.633, 12.114, 0, 0.833, -14.543, 0, 1.133, 20.904, 0, 1.4, -22.363, 0, 1.633, 20.106, 0, 1.9, -17.113, 0, 2.133, 15.194, 0, 2.4, -15.327, 0, 2.733, 19.337, 0, 3.1, -13.998, 0, 3.633, 9.583, 0, 3.9, -1.909, 0, 4.133, 4.356, 0, 4.433, -12.225, 0, 4.8, 15.06, 0, 5.1, -7.304, 0, 5.533, 2.222, 0, 5.9, -1.263, 0, 6.2, 0.555, 0, 6.333, 0.208, 0, 6.533, 4.128, 0, 6.8, -15.351, 0, 7.133, 29.366, 0, 7.433, -26.113, 0, 7.733, 13.519, 0, 8, -10.314, 0, 8.3, 9.654, 0, 8.6, -1.165, 0, 8.7, -0.799, 0, 8.9, -4.786, 0, 9.167, -1.26, 0, 9.233, -1.371, 0, 9.533, 4.771, 0, 9.8, 0.039, 0, 10.033, 4.611, 0, 10.333, -14.566, 0, 10.633, 18.288, 0, 10.9, -11.038, 0, 11.2, 7.075, 0, 11.6, -4.169, 0, 11.9, 0.632, 0, 12.167, -1.898, 0, 12.433, 3.17, 0, 12.733, -0.051, 0, 13, 3.241, 0, 13.267, -7.239, 0, 13.8, 4.453, 0, 14.1, -0.057, 0, 14.133, -0.002, 0, 14.4, -4.918, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param79", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, 6.487, 0, 0.667, -13.095, 0, 1, 14.231, 0, 1.233, -15.526, 0, 1.467, 13.012, 0, 1.733, -10.729, 0, 1.967, 8.162, 0, 2.2, -10.726, 0, 2.533, 21.526, 0, 3, -27.198, 0, 3.5, 23.548, 0, 4.233, -22.593, 0, 4.667, 19.398, 0, 5.167, -3.668, 0, 5.467, 1.377, 0, 5.8, -0.927, 0, 6.2, -0.11, 0, 6.267, -0.111, 0, 6.333, -0.081, 0, 6.6, -11.782, 0, 6.967, 29.849, 0, 7.3, -29.634, 0, 7.6, -0.118, 0, 7.767, -2.702, 0, 8.133, 15.659, 0, 8.767, -15.489, 0, 9.4, 15.309, 0, 10.1, -20.162, 0, 10.467, 20.602, 0, 10.767, -0.356, 0, 11, 4.219, 0, 11.5, -9.825, 0, 12.3, 10.195, 0, 13.067, -14.842, 0, 13.633, 11.147, 0, 14.233, -10.095, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param80", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.467, -4.103, 0, 0.633, 12.114, 0, 0.833, -14.543, 0, 1.133, 20.904, 0, 1.4, -22.363, 0, 1.633, 20.106, 0, 1.9, -17.113, 0, 2.133, 15.194, 0, 2.4, -15.327, 0, 2.733, 19.337, 0, 3.1, -13.998, 0, 3.633, 9.583, 0, 3.9, -1.909, 0, 4.133, 4.356, 0, 4.433, -12.225, 0, 4.8, 15.06, 0, 5.1, -7.304, 0, 5.533, 2.222, 0, 5.9, -1.263, 0, 6.2, 0.555, 0, 6.333, 0.208, 0, 6.533, 4.128, 0, 6.8, -15.351, 0, 7.133, 29.366, 0, 7.433, -26.113, 0, 7.733, 13.519, 0, 8, -10.314, 0, 8.3, 9.654, 0, 8.6, -1.165, 0, 8.7, -0.799, 0, 8.9, -4.786, 0, 9.167, -1.26, 0, 9.233, -1.371, 0, 9.533, 4.771, 0, 9.8, 0.039, 0, 10.033, 4.611, 0, 10.333, -14.566, 0, 10.633, 18.288, 0, 10.9, -11.038, 0, 11.2, 7.075, 0, 11.6, -4.169, 0, 11.9, 0.632, 0, 12.167, -1.898, 0, 12.433, 3.17, 0, 12.733, -0.051, 0, 13, 3.241, 0, 13.267, -7.239, 0, 13.8, 4.453, 0, 14.1, -0.057, 0, 14.133, -0.002, 0, 14.4, -4.918, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param294", "Segments": [0, 1, 2, 0.333, 1, 0, 0.4, 0, 2, 14.467, 0, 0, 14.567, 1]}, {"Target": "Parameter", "Id": "Param47", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.567, -13.71, 0, 0.9, 30, 2, 0.967, 30, 0, 1.133, -30, 2, 1.233, -30, 0, 1.433, 30, 2, 1.467, 30, 0, 1.667, -30, 2, 1.733, -30, 0, 1.933, 30, 2, 1.967, 30, 0, 2.2, -30, 0, 2.467, 25.383, 0, 2.767, -28.596, 0, 3.067, 30, 2, 3.133, 30, 0, 3.433, -30, 2, 3.533, -30, 0, 3.867, 29.767, 0, 4.367, -23.359, 0, 4.867, 22.706, 0, 5.4, -19.741, 0, 5.933, 12.846, 0, 6.333, 1.2, 0, 6.5, 17.232, 0, 6.7, -30, 2, 6.8, -30, 0, 7, 30, 2, 7.067, 30, 0, 7.333, -23.889, 0, 7.933, 19.793, 0, 8.333, -29.434, 0, 8.733, 26.919, 0, 9.3, -17.602, 0, 9.967, 17.81, 0, 10.6, -23.756, 0, 11, 20.805, 0, 11.333, 2.325, 0, 11.5, 4.15, 0, 12, -13.867, 0, 12.633, 11.274, 0, 13.567, -11.786, 0, 14.267, 9.989, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param48", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, 6.016, 0, 0.8, -16.439, 0, 1.033, 30, 2, 1.133, 30, 0, 1.3, -30, 2, 1.467, -30, 0, 1.6, 20.338, 0, 1.8, -30, 2, 1.9, -30, 0, 2.067, 30, 2, 2.133, 30, 0, 2.3, -30, 2, 2.367, -30, 0, 2.6, 30, 2, 2.633, 30, 0, 2.9, -30, 2, 2.967, -30, 0, 3.267, 30, 2, 3.3, 30, 0, 3.667, -27.693, 0, 4.033, 21.298, 0, 4.5, -10.844, 0, 5, 10.037, 0, 5.567, -7.837, 0, 6.067, 7.261, 0, 6.433, -10.636, 0, 6.633, 24.897, 0, 6.833, -30, 2, 6.967, -30, 0, 7.167, 30, 2, 7.233, 30, 0, 7.5, -24.349, 0, 7.767, 5.721, 0, 7.9, 0.532, 0, 8.167, 11.941, 0, 8.5, -20.451, 0, 8.867, 17.675, 0, 9.167, -2.633, 0, 9.267, -1.973, 0, 9.467, -4.621, 0, 9.767, -0.848, 0, 9.867, -1.112, 0, 10.133, 5.549, 0, 10.433, 1.383, 0, 10.533, 2.905, 0, 10.833, -13.751, 0, 11.133, 16.259, 0, 11.433, -8.703, 0, 11.733, 6.053, 0, 12.167, -5.797, 0, 12.467, -0.074, 0, 12.533, -0.396, 0, 12.767, 3.36, 0, 13.033, -1.1, 0, 13.333, 2.511, 0, 13.7, -3.845, 0, 14, -0.157, 0, 14.167, -1.24, 0, 14.433, 4.928, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param56", "Segments": [0, 0, 2, 0.333, 0, 0, 0.6, -6.594, 0, 0.967, 17.678, 0, 1.233, -15.486, 0, 1.467, 12.959, 0, 1.733, -13.108, 0, 1.967, 12.694, 0, 2.233, -10.869, 0, 2.5, 8.783, 0, 2.833, -11.057, 0, 3.2, 15.393, 0, 3.6, -16.06, 0, 4.033, 13.261, 0, 4.5, -14.043, 0, 5, 15.035, 0, 5.533, -15.121, 0, 6.067, 10.769, 0, 6.367, 3.16, 0, 6.467, 4.919, 0, 6.767, -21.361, 0, 7.1, 11.878, 0, 7.5, -6.908, 0, 8, 12.81, 0, 8.433, -16.536, 0, 8.9, 14.15, 0, 9.467, -14.41, 0, 10.033, 15.38, 0, 10.667, -17.942, 0, 11.133, 13.063, 0, 11.967, -8.48, 0, 12.667, 8.568, 0, 13.567, -7.516, 0, 14.267, 7.64, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param57", "Segments": [0, 0, 2, 0.333, 0, 0, 0.533, 3.355, 0, 0.8, -11.03, 0, 1.1, 29.796, 0, 1.4, -24.189, 0, 1.633, 17.639, 0, 1.867, -21.23, 0, 2.133, 19.874, 0, 2.367, -17.512, 0, 2.667, 15.947, 0, 3, -18.508, 0, 3.4, 22.647, 0, 3.8, -22.709, 0, 4.267, 14.833, 0, 4.733, -13.948, 0, 5.233, 12.17, 0, 5.767, -10.497, 0, 6.233, 5.881, 0, 6.467, -1.697, 0, 6.633, 11.554, 0, 6.933, -30, 2, 6.967, -30, 0, 7.267, 18.71, 0, 7.6, -7.811, 0, 7.767, -6.359, 0, 7.8, -6.591, 0, 8.233, 15.101, 0, 8.633, -19.794, 0, 9.133, 11.233, 0, 9.667, -9.768, 0, 10.267, 9.279, 0, 10.9, -15.027, 0, 11.233, 7.407, 0, 12.233, -5.003, 0, 12.867, 4.462, 0, 13.8, -4.431, 0, 14.5, 5.578, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param58", "Segments": [0, 0, 2, 0.333, 0, 0, 0.633, 4.169, 0, 0.9, -12.459, 0, 1.233, 28.417, 0, 1.533, -24.407, 0, 1.8, 26.04, 0, 2.033, -25.489, 0, 2.267, 23.571, 0, 2.533, -21.014, 0, 2.8, 19.96, 0, 3.1, -21.766, 0, 3.467, 25.308, 0, 3.867, -23.401, 0, 4.333, 17.261, 0, 4.8, -14.886, 0, 5.3, 12.028, 0, 5.767, -10.072, 0, 6.233, 8.259, 0, 6.567, -6.416, 0, 6.733, 11.478, 0, 6.8, 10.643, 0, 6.867, 11.937, 0, 7.067, -25.43, 0, 7.367, 23.073, 0, 7.7, -14.343, 0, 8.3, 15.665, 0, 8.733, -20.36, 0, 9.067, 12.705, 0, 9.7, -8.927, 0, 10.3, 7.735, 0, 10.933, -13.765, 0, 11.3, 12.442, 0, 11.633, -3.124, 0, 11.9, 1.645, 0, 12.3, -3.932, 0, 12.933, 3.875, 0, 13.267, -0.246, 0, 13.5, 1.13, 0, 13.867, -3.426, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param59", "Segments": [0, 0, 2, 0.333, 0, 0, 0.6, -6.594, 0, 0.967, 17.678, 0, 1.233, -15.486, 0, 1.467, 12.959, 0, 1.733, -13.108, 0, 1.967, 12.694, 0, 2.233, -10.869, 0, 2.5, 8.783, 0, 2.833, -11.057, 0, 3.2, 15.393, 0, 3.6, -16.06, 0, 4.033, 13.261, 0, 4.5, -14.043, 0, 5, 15.035, 0, 5.533, -15.121, 0, 6.067, 10.769, 0, 6.367, 3.16, 0, 6.467, 4.919, 0, 6.767, -21.361, 0, 7.1, 11.878, 0, 7.5, -6.908, 0, 8, 12.81, 0, 8.433, -16.536, 0, 8.9, 14.15, 0, 9.467, -14.41, 0, 10.033, 15.38, 0, 10.667, -17.942, 0, 11.133, 13.063, 0, 11.967, -8.48, 0, 12.667, 8.568, 0, 13.567, -7.516, 0, 14.267, 7.64, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param60", "Segments": [0, 0, 2, 0.333, 0, 0, 0.533, 3.355, 0, 0.8, -11.03, 0, 1.1, 29.796, 0, 1.4, -24.189, 0, 1.633, 17.639, 0, 1.867, -21.23, 0, 2.133, 19.874, 0, 2.367, -17.512, 0, 2.667, 15.947, 0, 3, -18.508, 0, 3.4, 22.647, 0, 3.8, -22.709, 0, 4.267, 14.833, 0, 4.733, -13.948, 0, 5.233, 12.17, 0, 5.767, -10.497, 0, 6.233, 5.881, 0, 6.467, -1.697, 0, 6.633, 11.554, 0, 6.933, -30, 2, 6.967, -30, 0, 7.267, 18.71, 0, 7.6, -7.811, 0, 7.767, -6.359, 0, 7.8, -6.591, 0, 8.233, 15.101, 0, 8.633, -19.794, 0, 9.133, 11.233, 0, 9.667, -9.768, 0, 10.267, 9.279, 0, 10.9, -15.027, 0, 11.233, 7.407, 0, 12.233, -5.003, 0, 12.867, 4.462, 0, 13.8, -4.431, 0, 14.5, 5.578, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param61", "Segments": [0, 0, 2, 0.333, 0, 0, 0.633, 4.169, 0, 0.9, -12.459, 0, 1.233, 28.417, 0, 1.533, -24.407, 0, 1.8, 26.04, 0, 2.033, -25.489, 0, 2.267, 23.571, 0, 2.533, -21.014, 0, 2.8, 19.96, 0, 3.1, -21.766, 0, 3.467, 25.308, 0, 3.867, -23.401, 0, 4.333, 17.261, 0, 4.8, -14.886, 0, 5.3, 12.028, 0, 5.767, -10.072, 0, 6.233, 8.259, 0, 6.567, -6.416, 0, 6.733, 11.478, 0, 6.8, 10.643, 0, 6.867, 11.937, 0, 7.067, -25.43, 0, 7.367, 23.073, 0, 7.7, -14.343, 0, 8.3, 15.665, 0, 8.733, -20.36, 0, 9.067, 12.705, 0, 9.7, -8.927, 0, 10.3, 7.735, 0, 10.933, -13.765, 0, 11.3, 12.442, 0, 11.633, -3.124, 0, 11.9, 1.645, 0, 12.3, -3.932, 0, 12.933, 3.875, 0, 13.267, -0.246, 0, 13.5, 1.13, 0, 13.867, -3.426, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param49", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.567, -13.71, 0, 0.9, 30, 2, 0.967, 30, 0, 1.133, -30, 2, 1.233, -30, 0, 1.433, 30, 2, 1.467, 30, 0, 1.667, -30, 2, 1.733, -30, 0, 1.933, 30, 2, 1.967, 30, 0, 2.2, -30, 0, 2.467, 25.383, 0, 2.767, -28.596, 0, 3.067, 30, 2, 3.133, 30, 0, 3.433, -30, 2, 3.533, -30, 0, 3.867, 29.767, 0, 4.367, -23.359, 0, 4.867, 22.706, 0, 5.4, -19.741, 0, 5.933, 12.846, 0, 6.333, 1.2, 0, 6.5, 17.232, 0, 6.7, -30, 2, 6.8, -30, 0, 7, 30, 2, 7.067, 30, 0, 7.333, -23.889, 0, 7.933, 19.793, 0, 8.333, -29.434, 0, 8.733, 26.919, 0, 9.3, -17.602, 0, 9.967, 17.81, 0, 10.6, -23.756, 0, 11, 20.805, 0, 11.333, 2.325, 0, 11.5, 4.15, 0, 12, -13.867, 0, 12.633, 11.274, 0, 13.567, -11.786, 0, 14.267, 9.989, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param50", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, 6.016, 0, 0.8, -16.439, 0, 1.033, 30, 2, 1.133, 30, 0, 1.3, -30, 2, 1.467, -30, 0, 1.6, 20.338, 0, 1.8, -30, 2, 1.9, -30, 0, 2.067, 30, 2, 2.133, 30, 0, 2.3, -30, 2, 2.367, -30, 0, 2.6, 30, 2, 2.633, 30, 0, 2.9, -30, 2, 2.967, -30, 0, 3.267, 30, 2, 3.3, 30, 0, 3.667, -27.693, 0, 4.033, 21.298, 0, 4.5, -10.844, 0, 5, 10.037, 0, 5.567, -7.837, 0, 6.067, 7.261, 0, 6.433, -10.636, 0, 6.633, 24.897, 0, 6.833, -30, 2, 6.967, -30, 0, 7.167, 30, 2, 7.233, 30, 0, 7.5, -24.349, 0, 7.767, 5.721, 0, 7.9, 0.532, 0, 8.167, 11.941, 0, 8.5, -20.451, 0, 8.867, 17.675, 0, 9.167, -2.633, 0, 9.267, -1.973, 0, 9.467, -4.621, 0, 9.767, -0.848, 0, 9.867, -1.112, 0, 10.133, 5.549, 0, 10.433, 1.383, 0, 10.533, 2.905, 0, 10.833, -13.751, 0, 11.133, 16.259, 0, 11.433, -8.703, 0, 11.733, 6.053, 0, 12.167, -5.797, 0, 12.467, -0.074, 0, 12.533, -0.396, 0, 12.767, 3.36, 0, 13.033, -1.1, 0, 13.333, 2.511, 0, 13.7, -3.845, 0, 14, -0.157, 0, 14.167, -1.24, 0, 14.433, 4.928, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param51", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.567, -13.71, 0, 0.9, 30, 2, 0.967, 30, 0, 1.133, -30, 2, 1.233, -30, 0, 1.433, 30, 2, 1.467, 30, 0, 1.667, -30, 2, 1.733, -30, 0, 1.933, 30, 2, 1.967, 30, 0, 2.2, -30, 0, 2.467, 25.383, 0, 2.767, -28.596, 0, 3.067, 30, 2, 3.133, 30, 0, 3.433, -30, 2, 3.533, -30, 0, 3.867, 29.767, 0, 4.367, -23.359, 0, 4.867, 22.706, 0, 5.4, -19.741, 0, 5.933, 12.846, 0, 6.333, 1.2, 0, 6.5, 17.232, 0, 6.7, -30, 2, 6.8, -30, 0, 7, 30, 2, 7.067, 30, 0, 7.333, -23.889, 0, 7.933, 19.793, 0, 8.333, -29.434, 0, 8.733, 26.919, 0, 9.3, -17.602, 0, 9.967, 17.81, 0, 10.6, -23.756, 0, 11, 20.805, 0, 11.333, 2.325, 0, 11.5, 4.15, 0, 12, -13.867, 0, 12.633, 11.274, 0, 13.567, -11.786, 0, 14.267, 9.989, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param52", "Segments": [0, 0, 2, 0.333, 0, 2, 0.367, 0, 0, 0.5, 6.016, 0, 0.8, -16.439, 0, 1.033, 30, 2, 1.133, 30, 0, 1.3, -30, 2, 1.467, -30, 0, 1.6, 20.338, 0, 1.8, -30, 2, 1.9, -30, 0, 2.067, 30, 2, 2.133, 30, 0, 2.3, -30, 2, 2.367, -30, 0, 2.6, 30, 2, 2.633, 30, 0, 2.9, -30, 2, 2.967, -30, 0, 3.267, 30, 2, 3.3, 30, 0, 3.667, -27.693, 0, 4.033, 21.298, 0, 4.5, -10.844, 0, 5, 10.037, 0, 5.567, -7.837, 0, 6.067, 7.261, 0, 6.433, -10.636, 0, 6.633, 24.897, 0, 6.833, -30, 2, 6.967, -30, 0, 7.167, 30, 2, 7.233, 30, 0, 7.5, -24.349, 0, 7.767, 5.721, 0, 7.9, 0.532, 0, 8.167, 11.941, 0, 8.5, -20.451, 0, 8.867, 17.675, 0, 9.167, -2.633, 0, 9.267, -1.973, 0, 9.467, -4.621, 0, 9.767, -0.848, 0, 9.867, -1.112, 0, 10.133, 5.549, 0, 10.433, 1.383, 0, 10.533, 2.905, 0, 10.833, -13.751, 0, 11.133, 16.259, 0, 11.433, -8.703, 0, 11.733, 6.053, 0, 12.167, -5.797, 0, 12.467, -0.074, 0, 12.533, -0.396, 0, 12.767, 3.36, 0, 13.033, -1.1, 0, 13.333, 2.511, 0, 13.7, -3.845, 0, 14, -0.157, 0, 14.167, -1.24, 0, 14.433, 4.928, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation64", "Segments": [0, 2.951, 2, 0.333, 2.951, 1, 0.344, 2.951, 0.356, 0.559, 0.367, 0, 1, 0.434, -3.356, 0.5, -4.57, 0.567, -4.57, 0, 0.933, 10.844, 0, 1.2, -14.094, 0, 1.467, 11.456, 0, 1.7, -11.142, 0, 1.933, 11.25, 0, 2.2, -10.036, 0, 2.467, 8.461, 0, 2.767, -9.532, 0, 3.1, 11.006, 0, 3.5, -11.151, 0, 3.867, 9.922, 0, 4.367, -7.786, 0, 4.867, 7.569, 0, 5.4, -6.58, 0, 5.933, 4.282, 0, 6.333, 0.4, 0, 6.5, 5.744, 0, 6.733, -12.521, 0, 7.033, 11.025, 0, 7.333, -7.963, 0, 7.933, 6.598, 0, 8.333, -9.811, 0, 8.733, 8.973, 0, 9.3, -5.867, 0, 9.967, 5.936, 0, 10.6, -7.918, 0, 11, 6.935, 0, 11.333, 0.775, 0, 11.5, 1.383, 0, 12, -4.622, 0, 12.633, 3.758, 0, 13.567, -3.929, 0, 14.267, 3.33, 0, 14.567, 2.951]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation65", "Segments": [0, 4.849, 2, 0.333, 4.849, 0, 0.367, 0, 0, 0.5, 3.008, 0, 0.8, -8.219, 0, 1.067, 23.989, 0, 1.333, -30, 2, 1.4, -30, 0, 1.6, 10.169, 0, 1.833, -23.101, 0, 2.067, 20.335, 0, 2.333, -18.477, 0, 2.633, 16.81, 0, 2.933, -16.727, 0, 3.3, 15.551, 0, 3.667, -13.846, 0, 4.033, 10.649, 0, 4.5, -5.422, 0, 5, 5.018, 0, 5.567, -3.918, 0, 6.067, 3.631, 0, 6.433, -5.318, 0, 6.633, 12.449, 0, 6.9, -27.521, 0, 7.2, 18.346, 0, 7.5, -12.174, 0, 7.767, 2.861, 0, 7.9, 0.266, 0, 8.167, 5.971, 0, 8.5, -10.225, 0, 8.867, 8.838, 0, 9.167, -1.316, 0, 9.267, -0.986, 0, 9.467, -2.311, 0, 9.767, -0.424, 0, 9.867, -0.556, 0, 10.133, 2.774, 0, 10.433, 0.692, 0, 10.533, 1.453, 0, 10.833, -6.875, 0, 11.133, 8.129, 0, 11.433, -4.351, 0, 11.733, 3.026, 0, 12.167, -2.898, 0, 12.467, -0.037, 0, 12.533, -0.198, 0, 12.767, 1.68, 0, 13.033, -0.55, 0, 13.333, 1.255, 0, 13.7, -1.922, 0, 14, -0.079, 0, 14.167, -0.62, 1, 14.256, -0.62, 14.344, 0.144, 14.433, 2.464, 1, 14.478, 3.624, 14.522, 4.849, 14.567, 4.849]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation66", "Segments": [0, -11.659, 2, 0.333, -11.659, 1, 0.344, -11.659, 0.356, -0.404, 0.367, 0, 1, 0.445, 2.83, 0.522, 3.835, 0.6, 3.835, 0, 0.833, -11.403, 0, 1.133, 30, 2, 1.167, 30, 0, 1.233, 23.221, 0, 1.267, 24.428, 0, 1.533, -7.571, 0, 1.7, 14.746, 0, 1.9, -21.063, 0, 2.167, 22.707, 0, 2.433, -22.994, 0, 2.7, 23.159, 0, 3, -25.323, 0, 3.367, 26.482, 0, 3.733, -24.776, 0, 4.1, 19.457, 0, 4.567, -10.055, 0, 5.1, 8.135, 0, 5.633, -5.964, 0, 6.133, 5.619, 0, 6.5, -8.21, 0, 6.7, 14.155, 0, 6.967, -30, 2, 7.033, -30, 0, 7.3, 23.107, 0, 7.6, -19.941, 0, 7.933, 6.401, 0, 8.067, 4.045, 0, 8.233, 6.52, 0, 8.567, -16.822, 0, 8.967, 15.948, 0, 9.3, -4.913, 0, 10.233, 4.428, 0, 10.5, 0.394, 0, 10.6, 0.761, 0, 10.9, -10.124, 0, 11.233, 13.972, 0, 11.567, -9.721, 0, 11.867, 7.677, 0, 12.233, -6.372, 0, 12.567, 1.537, 0, 12.7, 1.202, 0, 12.867, 1.96, 0, 13.167, -1.237, 0, 13.467, 2.374, 0, 13.8, -3.694, 0, 14.1, 0.794, 1, 14.156, 0.794, 14.211, 1.137, 14.267, -0.266, 1, 14.367, -2.791, 14.467, -11.659, 14.567, -11.659]}, {"Target": "Parameter", "Id": "Param55", "Segments": [0, -10.57, 2, 0.333, -10.57, 1, 0.366, -10.57, 0.4, -2.033, 0.433, 0, 1, 0.511, 4.744, 0.589, 5.664, 0.667, 5.664, 0, 0.9, -17.789, 0, 1.067, -5.53, 0, 1.1, -5.974, 0, 1.167, 30, 2, 1.433, 30, 0, 1.6, -13.448, 0, 1.833, 27.672, 0, 2.033, -27.383, 0, 2.233, 30, 2, 2.3, 30, 0, 2.5, -30, 2, 2.567, -30, 0, 2.767, 30, 2, 2.833, 30, 0, 3.033, -30, 2, 3.133, -30, 0, 3.4, 30, 2, 3.467, 30, 0, 3.767, -30, 2, 3.867, -30, 0, 4.167, 30, 2, 4.233, 30, 0, 4.6, -21.335, 0, 5.133, 14.269, 0, 5.667, -10, 0, 6.2, 9.942, 0, 6.567, -16.293, 0, 6.767, 23.376, 0, 6.867, 16.957, 0, 6.9, 18.865, 0, 7.033, -30, 2, 7.167, -30, 0, 7.367, 30, 2, 7.4, 30, 0, 7.667, -30, 2, 7.733, -30, 0, 8.067, 17.757, 0, 8.633, -27.808, 0, 9.033, 29.524, 0, 9.4, -13.066, 0, 9.767, 0.165, 0, 9.933, -0.497, 0, 10.333, 7.708, 0, 10.633, -0.938, 0, 10.7, -0.847, 0, 10.967, -14.934, 0, 11.333, 25.145, 0, 11.667, -21.14, 0, 11.967, 18.32, 0, 12.3, -15.853, 0, 12.667, 6.399, 0, 13.267, -2.139, 0, 13.533, 4.7, 0, 13.9, -7.893, 0, 14.2, 3.333, 1, 14.278, 3.333, 14.355, 3.02, 14.433, 0.113, 1, 14.478, -1.548, 14.522, -10.57, 14.567, -10.57]}, {"Target": "Parameter", "Id": "Param28", "Segments": [0, -7.12, 2, 0.333, -7.12, 0, 1.667, 7.36, 0, 3, -7.12, 0, 4.333, 7.36, 0, 5.667, -7.12, 0, 7, 7.36, 0, 8.333, -7.12, 0, 9.667, 7.36, 0, 11, -7.12, 0, 12.333, 7.36, 0, 13.667, -7.12, 2, 14.567, -7.12]}, {"Target": "Parameter", "Id": "Param29", "Segments": [0, -1.278, 2, 0.333, -1.278, 0, 0.867, -4.74, 0, 2.2, 5.1, 0, 3.533, -4.74, 0, 4.867, 5.1, 0, 6.2, -4.74, 0, 7.533, 5.1, 0, 8.867, -4.74, 0, 10.2, 5.1, 0, 11.533, -4.74, 0, 12.867, 5.1, 0, 14.2, -4.74, 0, 14.567, -1.278]}, {"Target": "Parameter", "Id": "Param30", "Segments": [0, -5.909, 2, 0.333, -5.909, 0, 1.5, 2.533, 0, 2.833, -6.287, 0, 4.167, 2.533, 0, 5.5, -6.287, 0, 6.833, 2.533, 0, 8.167, -6.287, 0, 9.5, 2.533, 0, 10.833, -6.287, 0, 12.167, 2.533, 0, 13.5, -6.287, 0, 14.567, -5.909]}, {"Target": "Parameter", "Id": "Param31", "Segments": [0, -0.551, 2, 0.333, -0.551, 0, 0.9, 3.018, 0, 2.233, -6.18, 0, 3.567, 3.018, 0, 4.9, -6.18, 0, 6.233, 3.018, 0, 7.567, -6.18, 0, 8.9, 3.018, 0, 10.233, -6.18, 0, 11.567, 3.018, 0, 12.9, -6.18, 0, 14.233, 3.018, 0, 14.567, -0.551]}, {"Target": "Parameter", "Id": "Param32", "Segments": [0, -2.714, 2, 0.333, -2.714, 0, 1.567, 2.284, 0, 2.9, -2.796, 0, 4.233, 2.284, 0, 5.567, -2.796, 0, 6.9, 2.284, 0, 8.233, -2.796, 0, 9.567, 2.284, 0, 10.9, -2.796, 0, 12.233, 2.284, 0, 13.567, -2.796, 0, 14.567, -2.714]}, {"Target": "Parameter", "Id": "Param33", "Segments": [0, -2.392, 2, 0.333, -2.392, 0, 0.933, 3.279, 0, 2.267, -10.047, 0, 3.6, 3.279, 0, 4.933, -10.047, 0, 6.267, 3.279, 0, 7.6, -10.047, 0, 8.933, 3.279, 0, 10.267, -10.047, 0, 11.6, 3.279, 0, 12.933, -10.047, 0, 14.267, 3.279, 0, 14.567, -2.392]}, {"Target": "Parameter", "Id": "Param34", "Segments": [0, -21.182, 2, 0.333, -21.182, 0, 1.6, 11.7, 0, 2.933, -21.42, 0, 4.267, 11.7, 0, 5.6, -21.42, 0, 6.933, 11.7, 0, 8.267, -21.42, 0, 9.6, 11.7, 0, 10.933, -21.42, 0, 12.267, 11.7, 0, 13.6, -21.42, 0, 14.567, -21.182]}, {"Target": "Parameter", "Id": "Param98", "Segments": [0, -6, 0, 0.333, 0, 0, 14.567, -6]}, {"Target": "Parameter", "Id": "Param99", "Segments": [0, -10.921, 0, 0.333, 0, 0, 14.567, -10.921]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation67", "Segments": [0, -10.939, 0, 0.333, 0, 0, 14.567, -10.939]}, {"Target": "Parameter", "Id": "Param281", "Segments": [0, 0, 2, 0.333, 0, 0, 1.667, 1, 0, 3, 0, 0, 4.333, 1, 0, 5.667, 0, 0, 7, 1, 0, 8.333, 0, 0, 9.667, 1, 0, 11, 0, 0, 12.333, 1, 0, 13.667, 0, 2, 14.567, 0]}, {"Target": "Parameter", "Id": "Param197mao2", "Segments": [0, 0, 2, 0.333, 0, 0, 0.967, 2.894, 0, 2.4, -2.454, 1, 2.6, -2.454, 2.8, -1.729, 3, 0, 1, 3.211, 1.825, 3.422, 2.894, 3.633, 2.894, 0, 5.067, -2.454, 1, 5.267, -2.454, 5.467, -1.729, 5.667, 0, 1, 5.878, 1.825, 6.089, 2.894, 6.3, 2.894, 0, 7.733, -2.454, 1, 7.933, -2.454, 8.133, -1.729, 8.333, 0, 1, 8.544, 1.825, 8.756, 2.894, 8.967, 2.894, 0, 10, -11, 0, 10.633, 15.456, 2, 13.1, 15.456, 0, 13.767, -6.383, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param212mao2", "Segments": [0, 0, 2, 0.333, 0, 0, 0.5, -16, 0, 0.667, 12.568, 0, 0.867, -16, 0, 1.067, 0, 2, 3, 0, 2, 5.667, 0, 0, 5.833, -16, 0, 6, 12.568, 0, 6.2, -16, 0, 6.4, 0, 2, 8.333, 0, 2, 11, 0, 0, 11.167, -16, 0, 11.333, 12.568, 0, 11.533, -16, 0, 11.733, 0, 2, 13.667, 0, 2, 14.567, 0]}, {"Target": "Parameter", "Id": "Param214mao2", "Segments": [0, 0, 2, 0.333, 0, 2, 1.533, 0, 0, 1.7, 16, 0, 1.867, 0, 0, 2.067, 16, 0, 2.267, 0, 2, 3, 0, 2, 5.667, 0, 2, 6.867, 0, 0, 7.033, 16, 0, 7.2, 0, 0, 7.4, 16, 0, 7.6, 0, 2, 8.333, 0, 2, 11, 0, 2, 12.2, 0, 0, 12.367, 16, 0, 12.533, 0, 0, 12.733, 16, 0, 12.933, 0, 2, 13.667, 0, 2, 14.567, 0]}, {"Target": "Parameter", "Id": "Param215mao2", "Segments": [0, 0, 2, 0.333, 0, 2, 1.533, 0, 0, 1.6, -14, 0, 1.8, 15, 0, 1.967, -8, 0, 2.133, 9, 0, 2.4, 0, 2, 3, 0, 2, 5.667, 0, 2, 6.867, 0, 0, 6.933, -14, 0, 7.133, 15, 0, 7.3, -8, 0, 7.467, 9, 0, 7.733, 0, 2, 8.333, 0, 2, 11, 0, 2, 12.2, 0, 0, 12.267, -14, 0, 12.467, 15, 0, 12.633, -8, 0, 12.8, 9, 0, 13.067, 0, 2, 13.667, 0, 2, 14.567, 0]}, {"Target": "Parameter", "Id": "Param198mao2", "Segments": [0, 0, 2, 0.333, 0, 0, 1.6, 30, 0, 3, 0, 0, 4.2, 30, 0, 5.667, 0, 0, 6.933, 30, 0, 8.333, 0, 0, 9.533, 30, 0, 11, 0, 0, 12.267, 30, 0, 13.667, 0, 2, 14.567, 0]}, {"Target": "Parameter", "Id": "Param112", "Segments": [0, -30, 0, 0.333, 0, 2, 0.833, 0, 2, 1.567, 0, 2, 3, 0, 2, 5.633, 0, 2, 6.3, 0, 2, 7.9, 0, 2, 8.667, 0, 2, 9.733, 0, 2, 10.567, 0, 2, 13, 0, 2, 14.233, 0, 0, 14.567, -30]}, {"Target": "Parameter", "Id": "Param272", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param293", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param273", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "ParamCheek", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param268", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param252", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param193", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param192", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param196", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param282", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param183", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param194", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param195", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param184", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param289", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param185", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param290", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param291", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param186", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param187", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param188", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param292", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param189", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param190", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param191", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param8", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param23", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param24", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param25", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param26", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param27", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param200", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param286", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param287", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param288", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param203", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param204", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param21", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param213", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param215", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param216", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param218", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param219", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param220", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param199", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param208", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param212", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param270", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param271", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param283", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param230", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param231", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param222", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param207", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param236", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param237", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param239", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param240", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param241", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param242", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param244", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param275", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param224", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param225", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param227", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param226", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param232", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param285", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param276", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param142", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param143", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param144", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param147", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param145", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param134", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param246", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param141", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param182", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param247", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param229", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param245", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param117", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param140", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param262", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param263", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param115", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param116", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param260", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param261", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param133", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param139", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param257", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param137", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param138", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param135", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param176", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param136", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param177", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param251", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation63", "Segments": [0, -0.675, 0, 14.567, -0.675]}, {"Target": "Parameter", "Id": "Param84", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param85", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param83", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param86", "Segments": [0, 1, 0, 14.567, 1]}, {"Target": "Parameter", "Id": "Param91", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param2", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param87", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param88", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param94", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param95", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param92", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param93", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param96", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param97", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param89", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param90", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation56", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param218mao2", "Segments": [0, 1, 0, 14.567, 1]}, {"Target": "Parameter", "Id": "Param211mao2", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param216mao2", "Segments": [0, -8.88, 0, 14.567, -8.88]}, {"Target": "Parameter", "Id": "Param217mao2", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param202mao2", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param203mao2", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param204mao2", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param199mao2", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param200mao2", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param201mao2", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param208mao2", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param209mao2", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param210mao2", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param205mao2", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param206mao2", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param207mao2", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param213mao2", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param219mao2", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation143mao2", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation144mao2", "Segments": [0, -10.82, 0, 14.567, -10.82]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation145mao2", "Segments": [0, 8.46, 0, 14.567, 8.46]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation146mao2", "Segments": [0, 28.83, 0, 14.567, 28.83]}, {"Target": "Parameter", "Id": "Param264", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param100", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param105", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param108", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param109", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param107", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param284", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param101", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param106", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param104", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param103", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param102", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation71", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation72", "Segments": [0, 8.085, 0, 14.567, 8.085]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation73", "Segments": [0, 11.915, 0, 14.567, 11.915]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation74", "Segments": [0, 12.341, 0, 14.567, 12.341]}, {"Target": "Parameter", "Id": "Param148", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param146", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param110", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param111", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param248", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param249", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param206", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param114", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param113", "Segments": [0, 1, 0, 14.567, 1]}, {"Target": "Parameter", "Id": "Param253", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param254", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param158", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param149", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param150", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param151", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param255", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param256", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param153", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param152", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param155", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param154", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param181", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param180", "Segments": [0, 0.5, 0, 14.567, 0.5]}, {"Target": "Parameter", "Id": "Param156", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param157", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param159", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param160", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param119", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param265", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param118", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param120", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param121", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param161", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param259", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param123", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param124", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param130", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param131", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param178", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param179", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param125", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param126", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param127", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param128", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param171", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param173", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param172", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param122", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param170", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param129", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param132", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param250", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param258", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param162", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param169", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param174", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param175", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param163", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param164", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param165", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param166", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param167", "Segments": [0, 0, 0, 14.567, 0]}, {"Target": "Parameter", "Id": "Param168", "Segments": [0, 0, 0, 14.567, 0]}], "UserData": [{"Time": 0.0, "Value": ""}, {"Time": 0.5, "Value": ""}, {"Time": 14.067, "Value": ""}]}