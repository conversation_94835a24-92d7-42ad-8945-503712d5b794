{"Version": 3, "Meta": {"PhysicsSettingCount": 4, "TotalInputCount": 10, "TotalOutputCount": 52, "VertexCount": 27, "EffectiveForces": {"Gravity": {"X": 0, "Y": -1}, "Wind": {"X": 0, "Y": 0}}, "PhysicsDictionary": [{"Id": "PhysicsSetting1", "Name": "Dummy1"}, {"Id": "PhysicsSetting2", "Name": "Dummy2"}, {"Id": "PhysicsSetting3", "Name": "Dummy3"}, {"Id": "PhysicsSetting4", "Name": "Dummy4"}]}, "PhysicsSettings": [{"Id": "PhysicsSetting1", "Input": [{"Source": {"Target": "Parameter", "Id": "Param277"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation64"}, "VertexIndex": 1, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation65"}, "VertexIndex": 2, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation66"}, "VertexIndex": 3, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param55"}, "VertexIndex": 4, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param47"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param48"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param56"}, "VertexIndex": 1, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param57"}, "VertexIndex": 2, "Scale": 25, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param58"}, "VertexIndex": 3, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param59"}, "VertexIndex": 1, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param60"}, "VertexIndex": 2, "Scale": 25, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param61"}, "VertexIndex": 3, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param49"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param50"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param51"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param52"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 5}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 25}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 35}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}], "Normalization": {"Position": {"Minimum": -20, "Default": 0, "Maximum": 20}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting2", "Input": [{"Source": {"Target": "Parameter", "Id": "Param277"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param56"}, "VertexIndex": 1, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param57"}, "VertexIndex": 2, "Scale": 25, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param58"}, "VertexIndex": 3, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param59"}, "VertexIndex": 1, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param60"}, "VertexIndex": 2, "Scale": 25, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param61"}, "VertexIndex": 3, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.95, "Delay": 0.8, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 25}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 35}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}], "Normalization": {"Position": {"Minimum": -20, "Default": 0, "Maximum": 20}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting3", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 20, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param278"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 10, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleY"}, "Weight": 20, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param62"}, "VertexIndex": 1, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param63"}, "VertexIndex": 2, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param64"}, "VertexIndex": 1, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param65"}, "VertexIndex": 2, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param37"}, "VertexIndex": 1, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param39"}, "VertexIndex": 2, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param38"}, "VertexIndex": 1, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param40"}, "VertexIndex": 2, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param41"}, "VertexIndex": 1, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param42"}, "VertexIndex": 2, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param43"}, "VertexIndex": 1, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param44"}, "VertexIndex": 2, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param45"}, "VertexIndex": 1, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param46"}, "VertexIndex": 2, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param72"}, "VertexIndex": 1, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param73"}, "VertexIndex": 2, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param74"}, "VertexIndex": 1, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param75"}, "VertexIndex": 2, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param53"}, "VertexIndex": 1, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param54"}, "VertexIndex": 2, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param81"}, "VertexIndex": 1, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param82"}, "VertexIndex": 2, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param70"}, "VertexIndex": 1, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param71"}, "VertexIndex": 2, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param77"}, "VertexIndex": 1, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param78"}, "VertexIndex": 2, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param79"}, "VertexIndex": 1, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param80"}, "VertexIndex": 2, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 5}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 25}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 35}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}], "Normalization": {"Position": {"Minimum": -20, "Default": 0, "Maximum": 20}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting4", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamBodyAngleY"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Param280"}, "Weight": 50, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param35"}, "VertexIndex": 1, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param36"}, "VertexIndex": 2, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 8.6}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 0.99, "Radius": 8.6}, {"Position": {"X": 0, "Y": 17.6}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 9}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}]}