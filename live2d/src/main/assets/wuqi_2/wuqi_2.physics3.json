{"Version": 3, "Meta": {"PhysicsSettingCount": 11, "TotalInputCount": 22, "TotalOutputCount": 37, "VertexCount": 46, "EffectiveForces": {"Gravity": {"X": 0, "Y": -1}, "Wind": {"X": 0, "Y": 0}}, "PhysicsDictionary": [{"Id": "PhysicsSetting1", "Name": "Dummy1"}, {"Id": "PhysicsSetting2", "Name": "Dummy2"}, {"Id": "PhysicsSetting3", "Name": "Dummy3"}, {"Id": "PhysicsSetting4", "Name": "Dummy4"}, {"Id": "PhysicsSetting5", "Name": "Dummy5"}, {"Id": "PhysicsSetting6", "Name": "Dummy6"}, {"Id": "PhysicsSetting7", "Name": "Dummy7"}, {"Id": "PhysicsSetting8", "Name": "Dummy8"}, {"Id": "PhysicsSetting9", "Name": "Dummy9"}, {"Id": "PhysicsSetting10", "Name": "Dummy10"}, {"Id": "PhysicsSetting11", "Name": "Dummy11"}]}, "PhysicsSettings": [{"Id": "PhysicsSetting1", "Input": [{"Source": {"Target": "Parameter", "Id": "wuli"}, "Weight": 20, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 80, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "zuocefati1"}, "VertexIndex": 1, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "zuocefati2"}, "VertexIndex": 2, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "zuocefati3"}, "VertexIndex": 3, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "zuocefati4"}, "VertexIndex": 4, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "zuocefati5"}, "VertexIndex": 5, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.98, "Delay": 0.8, "Acceleration": 1.5, "Radius": 15}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 25}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 35}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting2", "Input": [{"Source": {"Target": "Parameter", "Id": "wuli"}, "Weight": 20, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 80, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "fashipiaodai1"}, "VertexIndex": 1, "Scale": 25, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "fashipiaodai2"}, "VertexIndex": 2, "Scale": 25, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "fashipiaodai3"}, "VertexIndex": 3, "Scale": 25, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "fashipiaodai4"}, "VertexIndex": 4, "Scale": 25, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "fashipiaodai5"}, "VertexIndex": 5, "Scale": 25, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "fashipiaodai6"}, "VertexIndex": 6, "Scale": 25, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.98, "Delay": 0.8, "Acceleration": 1.5, "Radius": 15}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 25}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 35}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting3", "Input": [{"Source": {"Target": "Parameter", "Id": "wuli"}, "Weight": 30, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 70, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "daimao1"}, "VertexIndex": 1, "Scale": 25, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "daimao2"}, "VertexIndex": 2, "Scale": 25, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "daimao3"}, "VertexIndex": 3, "Scale": 25, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "daimao4"}, "VertexIndex": 4, "Scale": 25, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.98, "Delay": 0.8, "Acceleration": 1.5, "Radius": 15}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 25}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting4", "Input": [{"Source": {"Target": "Parameter", "Id": "wuli"}, "Weight": 25, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 75, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param"}, "VertexIndex": 1, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param2"}, "VertexIndex": 2, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param3"}, "VertexIndex": 3, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param4"}, "VertexIndex": 4, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param5"}, "VertexIndex": 5, "Scale": 20, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.98, "Delay": 0.8, "Acceleration": 1.5, "Radius": 15}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 25}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 35}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting5", "Input": [{"Source": {"Target": "Parameter", "Id": "wuli"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ZUOHOUFABG1"}, "VertexIndex": 1, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ZUOHOUFABG2"}, "VertexIndex": 2, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ZUOHOUFABG3"}, "VertexIndex": 3, "Scale": 15, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.98, "Delay": 0.8, "Acceleration": 1.5, "Radius": 15}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 25}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting6", "Input": [{"Source": {"Target": "Parameter", "Id": "Para"}, "Weight": 10, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 80, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "xiongshangxai"}, "VertexIndex": 1, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 5}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 2, "Radius": 5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting7", "Input": [{"Source": {"Target": "Parameter", "Id": "wuli"}, "Weight": 10, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "hounaofa1yaobai"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "hounaifa1bianxing"}, "VertexIndex": 2, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "hounaofa2yaobai"}, "VertexIndex": 1, "Scale": 4.001, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 5}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1.5, "Radius": 5}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1.5, "Radius": 5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting8", "Input": [{"Source": {"Target": "Parameter", "Id": "wuli"}, "Weight": 10, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 5}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1.5, "Radius": 5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting9", "Input": [{"Source": {"Target": "Parameter", "Id": "wuli"}, "Weight": 10, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 70, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "VertexIndex": 1, "Scale": 0.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "VertexIndex": 2, "Scale": 0.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 5}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting10", "Input": [{"Source": {"Target": "Parameter", "Id": "wuli"}, "Weight": 20, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 70, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamHairFront"}, "VertexIndex": 1, "Scale": 2, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 5}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting11", "Input": [{"Source": {"Target": "Parameter", "Id": "wuli"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleX"}, "Weight": 60, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param6"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param7"}, "VertexIndex": 2, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param8"}, "VertexIndex": 3, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param9"}, "VertexIndex": 4, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 1, "Delay": 1, "Acceleration": 1, "Radius": 0}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.98, "Delay": 0.8, "Acceleration": 1.5, "Radius": 15}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 25}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}]}