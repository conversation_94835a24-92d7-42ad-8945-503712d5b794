{"Version": 3, "Meta": {"Duration": 6.833, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 200, "TotalSegmentCount": 1269, "TotalPointCount": 1445, "UserDataCount": 2, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "wuli", "Segments": [0, 0, 0, 0.333, -1, 0, 0.5, 1, 0, 4.833, -1, 0, 5, 1, 0, 5.667, -1, 0, 5.967, 1, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Para", "Segments": [0, 0, 0, 0.333, -1, 0, 0.433, 1, 0, 0.8, -1, 2, 4.367, -1, 0, 5.067, 1, 0, 5.633, -1, 0, 6, 1, 0, 6.267, -1, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "zuocefati1", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0.066, 0.033, -0.073, 1, 0.078, -0.629, 0.122, -1.729, 0.167, -1.729, 0, 0.367, 4.347, 0, 0.5, -4.296, 0, 0.733, 2.751, 0, 1.133, -2.419, 0, 1.567, 1.149, 0, 1.967, -0.414, 0, 2.367, 1.001, 0, 2.8, -0.322, 0, 3.2, 0.387, 0, 3.667, -0.135, 0, 4, 0.051, 0, 4.533, -0.224, 0, 4.867, 0.01, 0, 5, -8.99, 0, 5.367, 9.355, 0, 5.867, -11.575, 0, 6.233, 10.678, 0, 6.667, -5.966, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "zuocefati2", "Segments": [0, 0, 0, 0.033, -0.254, 0, 0.133, 0.525, 0, 0.3, -4.184, 0, 0.5, 7.709, 0, 0.7, -5.791, 0, 0.967, 5.481, 0, 1.3, -4.951, 0, 1.7, 1.598, 0, 2.067, -1.332, 0, 2.533, 1.711, 0, 2.933, -0.238, 0, 3.333, 1.084, 0, 3.8, 0.235, 0, 4.133, 0.503, 0, 4.733, -0.178, 0, 4.967, 3.727, 0, 5.167, -15.008, 0, 5.5, 15.47, 0, 6.033, -18.368, 0, 6.4, 15.579, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "zuocefati3", "Segments": [0, 0, 1, 0.078, 0.073, 0.155, 1.13, 0.233, 1.13, 0, 0.433, -4.753, 0, 0.6, 6.787, 0, 0.833, -6.787, 0, 1.1, 6.936, 0, 1.433, -5.893, 0, 1.767, 3.665, 0, 2.133, -2.004, 0, 2.6, 1.187, 0, 3, -0.939, 0, 3.4, 0.615, 0, 3.8, -0.328, 0, 4.233, 0.222, 0, 4.8, -0.129, 0, 5.067, 7.897, 0, 5.3, -13.639, 0, 5.633, 14.451, 0, 6.133, -12.555, 0, 6.5, 13.586, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "zuocefati4", "Segments": [0, 0, 1, 0.089, 0.194, 0.178, 0.876, 0.267, 0.876, 0, 0.5, -3.627, 0, 0.733, 6.14, 0, 0.967, -7.546, 0, 1.233, 9.256, 0, 1.533, -9.407, 0, 1.867, 7.045, 0, 2.2, -4.455, 0, 2.6, 2.287, 0, 3.033, -1.422, 0, 3.433, 1.05, 0, 3.833, -0.551, 0, 4.267, 0.371, 0, 4.7, -0.171, 0, 5.133, 6.052, 0, 5.367, -14.974, 0, 5.7, 18.764, 0, 6.167, -13.598, 0, 6.567, 16.594, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "zuocefati5", "Segments": [0, 0, 0, 0.367, 0.767, 0, 0.6, -3.709, 0, 0.833, 6.592, 0, 1.1, -8.665, 0, 1.333, 11.543, 0, 1.633, -13.286, 0, 1.967, 11.482, 0, 2.3, -8.369, 0, 2.667, 5.039, 0, 3.067, -2.569, 0, 3.5, 1.689, 0, 3.9, -1.054, 0, 4.3, 0.637, 0, 4.733, -0.313, 0, 5.2, 5.606, 0, 5.433, -15.806, 0, 5.767, 22.046, 0, 6.2, -15.864, 0, 6.633, 18.836, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "fashipiaodai1", "Segments": [0, 0, 1, 0.011, 0, 0.022, -0.034, 0.033, -0.055, 1, 0.044, -0.076, 0.056, -0.077, 0.067, -0.077, 0, 0.333, 4.991, 0, 0.533, -14.2, 0, 0.867, 7.51, 0, 1.3, -4.676, 0, 1.733, 2.789, 0, 2.133, -1.178, 0, 2.533, 1.139, 0, 2.967, -0.417, 0, 3.367, 0.418, 0, 3.833, -0.198, 0, 4.167, 0.032, 0, 4.6, -0.245, 0, 4.867, -0.091, 0, 5, -15.414, 0, 5.367, 15.148, 0, 5.867, -19.142, 0, 6.233, 16.495, 0, 6.7, -9.133, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "fashipiaodai2", "Segments": [0, 0, 0, 0.033, -0.19, 0, 0.067, -0.175, 0, 0.267, -1.637, 0, 0.467, 12.553, 0, 0.667, -21.429, 0, 1.033, 13.901, 0, 1.433, -8.3, 0, 1.833, 4.307, 0, 2.267, -2.094, 0, 2.667, 2.359, 0, 3.067, -0.192, 0, 3.5, 1.257, 0, 3.933, 0.218, 0, 4.3, 0.512, 0, 4.8, -0.198, 0, 4.967, 5.522, 0, 5.133, -25.482, 0, 5.533, 23.238, 0, 6.033, -29.937, 0, 6.4, 22.87, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "fashipiaodai3", "Segments": [0, 0, 1, 0.033, 0.003, 0.067, 0.003, 0.1, 0.003, 0, 0.367, -3.764, 0, 0.567, 18.788, 0, 0.8, -20.665, 0, 1.133, 16.721, 0, 1.5, -10.51, 0, 1.9, 5.685, 0, 2.3, -3.17, 0, 2.7, 1.993, 0, 3.1, -1.165, 0, 3.533, 0.662, 0, 3.967, -0.343, 0, 4.4, 0.254, 0, 4.867, -0.193, 0, 5.067, 14.881, 0, 5.267, -20.809, 0, 5.633, 20.137, 0, 6.1, -19.978, 0, 6.5, 19.077, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "fashipiaodai4", "Segments": [0, 0, 1, 0.033, 0, 0.067, 0.006, 0.1, -0.003, 1, 0.211, -0.034, 0.322, -3.091, 0.433, -3.091, 0, 0.667, 15.471, 0, 0.9, -24.395, 0, 1.233, 23.093, 0, 1.6, -16.789, 0, 1.967, 10.528, 0, 2.333, -6.059, 0, 2.767, 3.524, 0, 3.167, -2.113, 0, 3.567, 1.204, 0, 4, -0.605, 0, 4.433, 0.395, 0, 4.867, -0.27, 0, 5.133, 10.302, 0, 5.333, -21.539, 0, 5.7, 24.904, 0, 6.167, -19.968, 0, 6.567, 22.666, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "fashipiaodai5", "Segments": [0, 0, 1, 0.056, -0.009, 0.111, -0.001, 0.167, -0.011, 1, 0.267, -0.029, 0.367, -2.564, 0.467, -2.564, 0, 0.733, 14.37, 0, 0.967, -26.72, 0, 1.3, 28.048, 0, 1.7, -22.729, 0, 2.067, 16.449, 0, 2.433, -10.942, 0, 2.8, 6.605, 0, 3.2, -3.885, 0, 3.6, 2.228, 0, 4.033, -1.128, 0, 4.467, 0.629, 0, 4.9, -0.382, 0, 5.2, 9.209, 0, 5.4, -22.572, 0, 5.733, 29.789, 0, 6.2, -21.414, 0, 6.633, 24.993, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "fashipiaodai6", "Segments": [0, 0, 1, 0.022, -0.026, 0.045, -0.084, 0.067, -0.084, 0, 0.233, -0.032, 0, 0.567, -3.02, 0, 0.8, 14.186, 0, 1.033, -29.048, 0, 1.367, 30, 2, 1.4, 30, 0, 1.767, -28.381, 0, 2.133, 22.309, 0, 2.533, -16.699, 0, 2.9, 11.49, 0, 3.267, -7.236, 0, 3.667, 4.265, 0, 4.067, -2.233, 0, 4.467, 1.115, 0, 4.9, -0.537, 0, 5.267, 8.725, 0, 5.467, -22.214, 0, 5.767, 30, 2, 5.8, 30, 0, 6.233, -23.63, 0, 6.667, 28.127, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "daimao1", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0.08, 0.033, -0.091, 1, 0.078, -0.778, 0.122, -2.162, 0.167, -2.162, 0, 0.367, 5.434, 0, 0.5, -5.37, 0, 0.733, 3.438, 0, 1.133, -3.024, 0, 1.567, 1.437, 0, 1.967, -0.517, 0, 2.367, 1.251, 0, 2.8, -0.403, 0, 3.2, 0.483, 0, 3.667, -0.169, 0, 4, 0.063, 0, 4.533, -0.28, 0, 4.867, 0.012, 0, 5, -11.237, 0, 5.367, 11.694, 0, 5.867, -14.468, 0, 6.233, 13.347, 0, 6.667, -7.457, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "daimao2", "Segments": [0, 0, 0, 0.033, -0.317, 0, 0.133, 0.656, 0, 0.3, -5.231, 0, 0.5, 9.636, 0, 0.7, -7.239, 0, 0.967, 6.852, 0, 1.3, -6.189, 0, 1.7, 1.998, 0, 2.067, -1.666, 0, 2.533, 2.139, 0, 2.933, -0.297, 0, 3.333, 1.355, 0, 3.8, 0.294, 0, 4.133, 0.629, 0, 4.733, -0.223, 0, 4.967, 4.658, 0, 5.167, -18.76, 0, 5.5, 19.338, 0, 6.033, -22.96, 0, 6.4, 19.474, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "daimao3", "Segments": [0, 0, 1, 0.078, 0.091, 0.155, 1.413, 0.233, 1.413, 0, 0.433, -5.941, 0, 0.6, 8.484, 0, 0.833, -8.483, 0, 1.1, 8.67, 0, 1.433, -7.366, 0, 1.767, 4.582, 0, 2.133, -2.505, 0, 2.6, 1.484, 0, 3, -1.173, 0, 3.4, 0.769, 0, 3.8, -0.41, 0, 4.233, 0.277, 0, 4.8, -0.161, 0, 5.067, 9.871, 0, 5.3, -17.049, 0, 5.633, 18.064, 0, 6.133, -15.693, 0, 6.5, 16.983, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "daimao4", "Segments": [0, 0, 1, 0.089, 0.243, 0.178, 1.095, 0.267, 1.095, 0, 0.5, -4.533, 0, 0.733, 7.675, 0, 0.967, -9.433, 0, 1.233, 11.57, 0, 1.533, -11.759, 0, 1.867, 8.807, 0, 2.2, -5.568, 0, 2.6, 2.859, 0, 3.033, -1.777, 0, 3.433, 1.312, 0, 3.833, -0.689, 0, 4.267, 0.464, 0, 4.7, -0.214, 0, 5.133, 7.564, 0, 5.367, -18.717, 0, 5.7, 23.454, 0, 6.167, -16.997, 0, 6.567, 20.742, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Param", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0.094, 0.033, -0.087, 1, 0.078, -0.812, 0.122, -2.833, 0.167, -2.833, 0, 0.367, 4.633, 0, 0.5, -0.602, 0, 0.667, 2.625, 0, 1.033, -3.636, 0, 1.467, 1.599, 0, 1.867, -0.882, 0, 2.3, 1.382, 0, 2.733, -0.518, 0, 3.133, 0.532, 0, 3.567, -0.195, 0, 3.967, 0.105, 0, 4.5, -0.246, 0, 4.867, 0.049, 0, 5, -7.235, 0, 5.333, 7.742, 0, 5.867, -9.483, 0, 6.233, 9.013, 0, 6.667, -5.106, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Param2", "Segments": [0, 0, 0, 0.033, -0.302, 0, 0.133, 0.935, 0, 0.333, -6.177, 0, 0.5, 6.578, 0, 0.667, 0.405, 0, 0.867, 4.104, 0, 1.2, -5.937, 0, 1.6, 2.271, 0, 1.967, -2.39, 0, 2.467, 2.018, 0, 2.867, -0.584, 0, 3.267, 1.307, 0, 3.7, 0.14, 0, 4.067, 0.607, 0, 4.7, -0.192, 0, 4.967, 3.208, 0, 5.167, -12.309, 0, 5.5, 13.297, 0, 6.033, -15.32, 0, 6.4, 13.606, 0, 6.8, -7.5, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Param3", "Segments": [0, 0, 1, 0.078, 0.039, 0.155, 2.088, 0.233, 2.088, 0, 0.433, -6.545, 0, 0.667, 4.598, 0, 0.867, -0.084, 0, 1.033, 2.346, 0, 1.333, -4.876, 0, 1.667, 4.125, 0, 2.033, -2.736, 0, 2.5, 1.443, 0, 2.933, -1.179, 0, 3.333, 0.851, 0, 3.733, -0.49, 0, 4.167, 0.303, 0, 4.6, -0.127, 0, 5.067, 6.125, 0, 5.3, -11.823, 0, 5.633, 12.984, 0, 6.133, -10.808, 0, 6.5, 12.252, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Param4", "Segments": [0, 0, 1, 0.1, 0.288, 0.2, 1.693, 0.3, 1.693, 0, 0.533, -5.845, 0, 0.8, 5.97, 0, 1.033, -0.056, 0, 1.167, 0.522, 0, 1.433, -5.532, 0, 1.767, 6.44, 0, 2.133, -5.068, 0, 2.5, 2.865, 0, 3, -1.65, 0, 3.4, 1.364, 0, 3.8, -0.897, 0, 4.2, 0.55, 0, 4.6, -0.238, 0, 5.133, 4.921, 0, 5.4, -13.004, 0, 5.7, 17.096, 0, 6.167, -12.365, 0, 6.567, 15.291, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Param5", "Segments": [0, 0, 1, 0.122, 0.92, 0.245, 1.538, 0.367, 1.538, 0, 0.633, -6.155, 0, 0.933, 8.046, 0, 1.233, -2.152, 0, 1.3, -2.088, 0, 1.533, -5.293, 0, 1.867, 8.82, 0, 2.2, -8.22, 0, 2.567, 5.539, 0, 3, -2.824, 0, 3.467, 1.975, 0, 3.867, -1.513, 0, 4.233, 1.019, 0, 4.633, -0.494, 0, 5.233, 4.635, 0, 5.467, -13.999, 0, 5.767, 20.488, 0, 6.2, -15.444, 0, 6.633, 17.803, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ZUOHOUFABG1", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0.073, 0.033, -0.065, 1, 0.078, -0.619, 0.122, -2.125, 0.167, -2.125, 0, 0.367, 3.475, 0, 0.5, -0.452, 0, 0.667, 1.969, 0, 1.033, -2.727, 0, 1.467, 1.199, 0, 1.867, -0.662, 0, 2.3, 1.036, 0, 2.733, -0.389, 0, 3.133, 0.399, 0, 3.567, -0.146, 0, 3.967, 0.079, 0, 4.5, -0.184, 0, 4.867, 0.037, 0, 5, -5.426, 0, 5.333, 5.806, 0, 5.867, -7.112, 0, 6.233, 6.76, 0, 6.667, -3.829, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ZUOHOUFABG2", "Segments": [0, 0, 0, 0.033, -0.226, 0, 0.133, 0.701, 0, 0.333, -4.632, 0, 0.5, 4.933, 0, 0.667, 0.304, 0, 0.867, 3.078, 0, 1.2, -4.453, 0, 1.6, 1.704, 0, 1.967, -1.793, 0, 2.467, 1.513, 0, 2.867, -0.438, 0, 3.267, 0.98, 0, 3.7, 0.105, 0, 4.067, 0.455, 0, 4.7, -0.144, 0, 4.967, 2.406, 0, 5.167, -9.232, 0, 5.5, 9.973, 0, 6.033, -11.49, 0, 6.4, 10.205, 0, 6.8, -5.625, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ZUOHOUFABG3", "Segments": [0, 0, 1, 0.078, 0.029, 0.155, 1.566, 0.233, 1.566, 0, 0.433, -4.909, 0, 0.667, 3.448, 0, 0.867, -0.063, 0, 1.033, 1.759, 0, 1.333, -3.657, 0, 1.667, 3.094, 0, 2.033, -2.052, 0, 2.5, 1.082, 0, 2.933, -0.884, 0, 3.333, 0.638, 0, 3.733, -0.368, 0, 4.167, 0.227, 0, 4.6, -0.095, 0, 5.067, 4.594, 0, 5.3, -8.867, 0, 5.633, 9.738, 0, 6.133, -8.106, 0, 6.5, 9.189, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "hounaofa1yaobai", "Segments": [0, 0, 0, 0.167, -0.327, 0, 0.367, 0.426, 0, 0.533, 0.03, 0, 0.6, 0.062, 0, 0.833, -0.163, 0, 1.1, 0.007, 0, 1.333, -0.055, 0, 1.633, -0.025, 2, 1.667, -0.025, 0, 1.7, -0.024, 2, 1.733, -0.024, 0, 2.3, 0.027, 0, 2.533, 0.006, 0, 2.833, 0.016, 0, 3, 0.014, 2, 3.033, 0.014, 2, 3.1, 0.014, 0, 3.233, 0.015, 2, 3.3, 0.015, 2, 3.333, 0.015, 0, 4.567, -0.005, 0, 4.8, 0.001, 0, 5, -0.182, 0, 5.2, 0.147, 0, 5.467, -0.026, 0, 5.633, -0.001, 0, 5.867, -0.111, 0, 6.1, 0.1, 0, 6.367, -0.022, 0, 6.6, 0.014, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "hounaifa1bianxing", "Segments": [0, 0, 2, 0.033, 0, 1, 0.066, 0.001, 0.1, 0.327, 0.133, 0.327, 0, 0.3, -0.788, 0, 0.5, 0.83, 0, 0.7, -0.245, 0, 0.833, 0.031, 0, 0.967, -0.136, 0, 1.2, 0.133, 0, 1.467, -0.077, 0, 1.7, 0.033, 0, 1.967, -0.021, 0, 2.2, 0.001, 0, 2.267, 0, 0, 2.433, 0.022, 0, 2.667, -0.02, 0, 2.9, 0.011, 0, 3.167, -0.005, 0, 3.433, 0.003, 0, 3.6, 0, 2, 3.767, 0, 2, 3.8, 0, 0, 3.867, 0.001, 2, 3.9, 0.001, 2, 4.033, 0.001, 2, 4.067, 0.001, 2, 4.133, 0.001, 2, 4.167, 0.001, 2, 4.333, 0.001, 2, 4.367, 0.001, 2, 4.533, 0.001, 0, 4.7, -0.007, 0, 4.967, 0.212, 0, 5.1, -0.377, 0, 5.333, 0.316, 0, 5.567, -0.174, 0, 5.8, 0.159, 0, 6.033, -0.247, 0, 6.233, 0.225, 0, 6.467, -0.133, 0, 6.733, 0.068, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "hounaofa2yaobai", "Segments": [0, 0, 0, 0.133, -1, 2, 0.2, -1, 0, 0.3, 1, 2, 0.433, 1, 0, 0.533, 0.122, 0, 0.6, 0.247, 0, 0.833, -0.65, 0, 1.1, 0.027, 0, 1.333, -0.219, 0, 1.633, -0.1, 0, 1.667, -0.101, 0, 1.7, -0.096, 0, 1.733, -0.097, 0, 2.3, 0.108, 0, 2.533, 0.023, 0, 2.833, 0.064, 0, 3, 0.057, 2, 3.033, 0.057, 0, 3.1, 0.056, 0, 3.233, 0.059, 0, 3.3, 0.058, 0, 3.333, 0.059, 0, 4.567, -0.021, 0, 4.8, 0.006, 0, 5, -0.73, 0, 5.2, 0.59, 0, 5.467, -0.106, 0, 5.633, -0.004, 0, 5.867, -0.446, 0, 6.1, 0.401, 0, 6.367, -0.089, 0, 6.6, 0.056, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 0.167, -0.327, 0, 0.367, 0.426, 0, 0.533, 0.03, 0, 0.6, 0.062, 0, 0.833, -0.163, 0, 1.1, 0.007, 0, 1.333, -0.055, 0, 1.633, -0.025, 2, 1.667, -0.025, 0, 1.7, -0.024, 2, 1.733, -0.024, 0, 2.3, 0.027, 0, 2.533, 0.006, 0, 2.833, 0.016, 0, 3, 0.014, 2, 3.033, 0.014, 2, 3.1, 0.014, 0, 3.233, 0.015, 2, 3.3, 0.015, 2, 3.333, 0.015, 0, 4.567, -0.005, 0, 4.8, 0.001, 0, 5, -0.182, 0, 5.2, 0.147, 0, 5.467, -0.026, 0, 5.633, -0.001, 0, 5.867, -0.111, 0, 6.1, 0.1, 0, 6.367, -0.022, 0, 6.6, 0.014, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 0.167, -0.199, 0, 0.4, 0.245, 0, 0.867, -0.133, 0, 1.167, 0.009, 0, 1.467, -0.042, 0, 2.333, 0.02, 0, 2.6, 0.005, 0, 2.933, 0.013, 0, 3.2, 0.01, 2, 3.233, 0.01, 2, 3.3, 0.01, 2, 3.333, 0.01, 2, 3.467, 0.01, 2, 3.5, 0.01, 0, 4.567, -0.004, 0, 4.867, 0.001, 0, 5, -0.112, 0, 5.233, 0.087, 0, 5.6, -0.022, 0, 5.7, -0.019, 0, 5.867, -0.068, 0, 6.133, 0.06, 0, 6.467, -0.013, 0, 6.733, 0.006, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.033, 0, 1, 0.066, 0.001, 0.1, 0.107, 0.133, 0.107, 0, 0.333, -0.247, 0, 0.533, 0.184, 0, 1, -0.125, 0, 1.3, 0.082, 0, 1.6, -0.045, 0, 1.9, 0.018, 0, 2.2, -0.012, 0, 2.5, 0.014, 0, 2.767, -0.01, 0, 3.067, 0.005, 0, 3.367, -0.002, 0, 3.7, 0.001, 0, 3.933, 0, 2, 4.033, 0, 2, 4.067, 0, 0, 4.3, 0.001, 0, 4.733, -0.003, 0, 4.967, 0.071, 0, 5.167, -0.113, 0, 5.433, 0.094, 0, 5.7, -0.049, 0, 5.867, 0.018, 0, 6.067, -0.059, 0, 6.3, 0.063, 0, 6.6, -0.039, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamHairFront", "Segments": [0, 0, 0, 0.167, -0.797, 0, 0.4, 0.981, 0, 0.867, -0.533, 0, 1.167, 0.037, 0, 1.467, -0.167, 0, 2.333, 0.081, 0, 2.6, 0.018, 0, 2.933, 0.05, 0, 3.2, 0.041, 0, 3.233, 0.042, 0, 3.3, 0.041, 0, 3.333, 0.042, 0, 3.467, 0.041, 2, 3.5, 0.041, 0, 4.567, -0.015, 0, 4.867, 0.004, 0, 5, -0.447, 0, 5.233, 0.346, 0, 5.6, -0.089, 0, 5.7, -0.076, 0, 5.867, -0.271, 0, 6.133, 0.241, 0, 6.467, -0.053, 0, 6.733, 0.025, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 0.167, 0.301, 0, 0.367, -0.455, 0, 0.6, 0.168, 0, 1, -0.217, 0, 1.333, 0.224, 0, 1.633, -0.084, 0, 1.967, 0.077, 0, 2.3, -0.045, 0, 2.567, 0.012, 0, 2.867, -0.026, 0, 3.167, -0.006, 0, 3.5, -0.018, 0, 3.867, -0.009, 2, 3.9, -0.009, 2, 3.933, -0.009, 0, 4.567, 0.002, 0, 4.767, -0.004, 0, 5, 0.174, 0, 5.167, -0.186, 0, 5.467, 0.129, 0, 5.7, -0.079, 0, 5.9, 0.054, 0, 6.067, -0.071, 0, 6.333, 0.087, 0, 6.633, -0.08, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Param6", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0.005, 0.033, -0.004, 1, 0.078, -0.043, 0.122, -0.142, 0.167, -0.142, 0, 0.367, 0.232, 0, 0.5, -0.03, 0, 0.667, 0.131, 0, 1.033, -0.182, 0, 1.467, 0.08, 0, 1.867, -0.044, 0, 2.3, 0.069, 0, 2.733, -0.026, 0, 3.133, 0.027, 0, 3.567, -0.01, 0, 3.967, 0.005, 0, 4.5, -0.012, 0, 4.867, 0.002, 0, 5, -0.362, 0, 5.333, 0.387, 0, 5.867, -0.474, 0, 6.233, 0.451, 0, 6.667, -0.255, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Param7", "Segments": [0, 0, 0, 0.033, -0.015, 0, 0.133, 0.047, 0, 0.333, -0.309, 0, 0.5, 0.329, 0, 0.667, 0.02, 0, 0.867, 0.205, 0, 1.2, -0.297, 0, 1.6, 0.114, 0, 1.967, -0.12, 0, 2.467, 0.101, 0, 2.867, -0.029, 0, 3.267, 0.065, 0, 3.7, 0.007, 0, 4.067, 0.03, 0, 4.7, -0.01, 0, 4.967, 0.16, 0, 5.167, -0.615, 0, 5.5, 0.665, 0, 6.033, -0.766, 0, 6.4, 0.68, 0, 6.8, -0.375, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Param8", "Segments": [0, 0, 1, 0.078, 0.002, 0.155, 0.104, 0.233, 0.104, 0, 0.433, -0.327, 0, 0.667, 0.23, 0, 0.867, -0.004, 0, 1.033, 0.117, 0, 1.333, -0.244, 0, 1.667, 0.206, 0, 2.033, -0.137, 0, 2.5, 0.072, 0, 2.933, -0.059, 0, 3.333, 0.043, 0, 3.733, -0.025, 0, 4.167, 0.015, 0, 4.6, -0.006, 0, 5.067, 0.306, 0, 5.3, -0.591, 0, 5.633, 0.649, 0, 6.133, -0.54, 0, 6.5, 0.613, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Param9", "Segments": [0, 0, 1, 0.1, 0.014, 0.2, 0.085, 0.3, 0.085, 0, 0.533, -0.292, 0, 0.8, 0.299, 0, 1.033, -0.003, 0, 1.167, 0.026, 0, 1.433, -0.277, 0, 1.767, 0.322, 0, 2.133, -0.253, 0, 2.5, 0.143, 0, 3, -0.083, 0, 3.4, 0.068, 0, 3.8, -0.045, 0, 4.2, 0.027, 0, 4.6, -0.012, 0, 5.133, 0.246, 0, 5.4, -0.65, 0, 5.7, 0.855, 0, 6.167, -0.618, 0, 6.567, 0.765, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 1, 1, 2, 4.5, 1, 2, 5.933, 1, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.5, 0, 2, 0.9, 0, 0, 0.933, -1, 0, 2, -0.498, 0, 2.833, -0.71, 0, 4.5, -0.688, 1, 4.667, -0.688, 4.833, -0.691, 5, -0.737, 1, 5.078, -0.758, 5.155, -1, 5.233, -1, 2, 5.933, -1, 0, 6.067, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.9, 0, 0, 0.933, -1, 1, 1.189, -1, 1.444, -0.016, 1.7, 0.328, 1, 1.856, 0.538, 2.011, 0.604, 2.167, 0.642, 1, 2.389, 0.696, 2.611, 0.748, 2.833, 0.753, 1, 3.389, 0.766, 3.944, 0.767, 4.5, 0.767, 1, 4.667, 0.767, 4.833, 0.805, 5, 0.686, 1, 5.267, 0.495, 5.533, -0.5, 5.8, -0.5, 2, 5.933, -0.5, 0, 6.067, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.5, 0, 0, 2, -0.296, 1, 2.133, -0.296, 2.267, -0.195, 2.4, -0.109, 1, 2.544, -0.016, 2.689, 0, 2.833, 0, 2, 4.5, 0, 1, 4.611, 0, 4.722, 0.24, 4.833, 0.428, 1, 4.889, 0.522, 4.944, 0.507, 5, 0.595, 1, 5.044, 0.666, 5.089, 1, 5.133, 1, 2, 5.667, 1, 0, 5.933, -1, 0, 6.067, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 1, 1, 0.167, 1, 0.333, 0.355, 0.5, 0, 1, 0.878, -0.804, 1.255, -1, 1.633, -1, 1, 2.589, -1, 3.544, -0.727, 4.5, 0, 1, 5.278, 0.592, 6.055, 1, 6.833, 1]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 1.033, 1, 2, 5.667, 1, 0, 5.833, -1, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "xingfengtmd", "Segments": [0, 0, 0, 5.6, 1, 0, 5.733, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 1, 0.133, 0, 0.267, 0.378, 0.4, 0.5, 1, 0.544, 0.632, 0.689, 0.637, 0.833, 0.75, 1, 0.922, 0.82, 1.011, 1, 1.1, 1, 2, 1.667, 1, 2, 2.167, 1, 0, 2.833, 0.75, 2, 4.5, 0.75, 0, 5, 1, 2, 5.167, 1, 2, 5.667, 1, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 1, 0.133, 0, 0.267, 0.242, 0.4, 0.442, 1, 0.433, 0.492, 0.467, 0.484, 0.5, 0.508, 1, 0.611, 0.587, 0.722, 0.639, 0.833, 0.75, 1, 0.922, 0.839, 1.011, 1, 1.1, 1, 2, 1.667, 1, 2, 2.167, 1, 0, 2.833, 0.75, 2, 4.5, 0.75, 0, 5, 1, 2, 5.167, 1, 2, 5.667, 1, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 1, 0.367, 0.152, 0.733, 0.174, 1.1, 0.174, 1, 1.456, 0.174, 1.811, 0.131, 2.167, -0.004, 1, 2.389, -0.089, 2.611, -0.168, 2.833, -0.168, 2, 4.5, -0.168, 0, 5.167, 0.107, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON>biji<PERSON><PERSON>", "Segments": [0, 0, 0, 0.4, 0.083, 1, 0.633, 0.083, 0.867, 0.075, 1.1, -0.062, 1, 1.289, -0.173, 1.478, -0.4, 1.667, -0.4, 1, 1.834, -0.4, 2, -0.134, 2.167, -0.004, 1, 2.389, 0.169, 2.611, 0.184, 2.833, 0.184, 2, 4.5, 0.184, 0, 5.167, -0.158, 0, 5.667, 0.064, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 1, 0.133, 0, 0.267, 0.009, 0.4, -0.086, 1, 0.511, -0.166, 0.622, -0.68, 0.733, -0.68, 0, 1.1, -0.396, 0, 1.5, -0.4, 0, 2.167, 0, 2, 4.5, 0, 1, 4.722, 0, 4.945, -0.371, 5.167, -0.636, 1, 5.434, -0.954, 5.7, -1, 5.967, -1, 1, 6.045, -1, 6.122, -0.422, 6.2, -0.321, 1, 6.411, -0.046, 6.622, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.4, 0, 1, 0.511, 0, 0.622, -0.278, 0.733, -0.385, 1, 0.855, -0.502, 0.978, -0.5, 1.1, -0.5, 1, 1.233, -0.5, 1.367, -0.515, 1.5, -0.3, 1, 1.656, -0.049, 1.811, 0.5, 1.967, 0.5, 0, 2.5, -0.1, 2, 2.867, -0.1, 2, 4.5, -0.1, 1, 4.611, -0.1, 4.722, -0.095, 4.833, -0.143, 1, 5.111, -0.262, 5.389, -0.387, 5.667, -0.387, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "xiongtuceng", "Segments": [0, 0, 2, 1.467, 0, 2, 1.5, 1, 2, 5.133, 1, 2, 5.167, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 1, 2, 2.167, 1, 0, 2.5, 1.5, 2, 4.5, 1.5, 0, 5, 1, 2, 6.833, 1]}, {"Target": "Parameter", "Id": "xiongshangxai", "Segments": [0, 0, 0, 0.133, -0.29, 0, 0.333, 0.687, 0, 0.433, -1, 2, 0.467, -1, 0, 0.567, 1, 2, 0.667, 1, 0, 0.867, -0.928, 0, 1.1, 0.212, 0, 1.333, -0.148, 0, 1.567, -0.025, 0, 1.733, -0.049, 0, 2.3, 0.039, 0, 2.533, 0.006, 0, 2.767, 0.02, 0, 2.9, 0.017, 0, 2.933, 0.018, 2, 2.967, 0.018, 0, 3.167, 0.019, 2, 3.2, 0.019, 2, 3.233, 0.019, 0, 3.3, 0.018, 0, 3.333, 0.019, 0, 3.467, 0.017, 2, 3.5, 0.017, 0, 3.567, 0.016, 2, 3.6, 0.016, 0, 4.6, -0.263, 0, 5.267, 0.338, 0, 5.833, -0.651, 0, 6.133, 1, 2, 6.167, 1, 0, 6.367, -0.94, 0, 6.6, 0.158, 0, 6.8, -0.031, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 0.133, -0.145, 0, 0.333, 0.344, 0, 0.433, -0.742, 0, 0.6, 0.71, 0, 0.867, -0.464, 0, 1.1, 0.106, 0, 1.333, -0.074, 0, 1.567, -0.013, 0, 1.733, -0.024, 0, 2.3, 0.019, 0, 2.533, 0.003, 0, 2.767, 0.01, 0, 2.9, 0.009, 2, 2.933, 0.009, 2, 2.967, 0.009, 2, 3.167, 0.009, 2, 3.2, 0.009, 0, 3.233, 0.01, 0, 3.3, 0.009, 2, 3.333, 0.009, 0, 3.467, 0.008, 2, 3.5, 0.008, 2, 3.567, 0.008, 2, 3.6, 0.008, 0, 4.6, -0.131, 0, 5.267, 0.169, 0, 5.833, -0.326, 0, 6.167, 0.562, 0, 6.367, -0.47, 0, 6.6, 0.079, 0, 6.8, -0.015, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "op", "Segments": [0, 0, 1, 0.389, 0, 0.778, 0.014, 1.167, 0.2, 1, 1.356, 0.29, 1.544, 0.92, 1.733, 0.92, 2, 2.5, 0.92, 2, 4.633, 0.92, 1, 4.778, 0.92, 4.922, 0.918, 5.067, 0.84, 1, 5.145, 0.799, 5.222, 0.421, 5.3, 0.421, 0, 5.533, 0.6, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 2, 0.167, 1, 0, 0.333, 0, 0, 0.667, 1, 2, 2.533, 1, 0, 2.7, 0, 0, 3.033, 1, 2, 4.5, 1, 0, 4.9, 0, 0, 6.833, 1]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 2, 0.167, 1, 0, 0.333, 0, 0, 0.667, 1, 2, 2.533, 1, 0, 2.7, 0, 0, 3.033, 1, 2, 4.5, 1, 0, 4.9, 0, 0, 6.833, 1]}, {"Target": "Parameter", "Id": "<PERSON><PERSON>kongda<PERSON><PERSON>", "Segments": [0, 0, 2, 0.167, 0, 0, 0.333, 1, 0, 0.667, -1, 0, 0.833, 0.5, 0, 0.967, -0.2, 0, 1.1, 0, 2, 2.533, 0, 0, 2.7, 1, 0, 3.033, -1, 0, 3.2, 0.5, 0, 3.333, -0.2, 0, 3.467, 0, 2, 4.5, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "gaoguangdaxia<PERSON>", "Segments": [0, 0, 2, 0.333, 0, 0, 0.567, 1, 0, 0.733, 0, 0, 0.967, 0.9, 0, 1.167, 0, 0, 1.3, 0.4, 0, 1.433, 0, 2, 2.7, 0, 0, 2.933, 1, 0, 3.1, 0, 0, 3.333, 0.9, 0, 3.533, 0, 0, 3.667, 0.4, 0, 3.8, 0, 2, 4.5, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, -1, 0, 4.5, 0, 0, 6.833, -1]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, -1, 0, 4.5, 0, 0, 6.833, -1]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 0, 2, 0.267, 0, 0, 0.533, 1, 0, 0.833, 0, 0, 1.433, 1, 2, 4.5, 1, 0, 4.833, 0, 0, 5.667, 1, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 2.5, 0, 0, 4.033, 0.498, 1, 4.533, 0.498, 5.033, 0.48, 5.533, 0.327, 1, 5.966, 0.195, 6.4, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 2, 2, 0, 0, 3.5, 90, 2, 4.5, 90, 1, 4.633, 90, 4.767, 24.339, 4.9, 21, 1, 5.544, 4.86, 6.189, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 0, 0.2, 90, 0, 0.667, -90, 2, 2, -90, 1, 2.833, -90, 3.667, -64.155, 4.5, 0, 1, 4.6, 7.699, 4.7, 33, 4.8, 33, 0, 5.067, -51, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, -30, 0, 2, -6, 0, 3.5, -30, 2, 4.5, -30, 0, 5.367, 1, 0, 6.833, -30]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 1, 0.067, 0.719, 0.133, 9.146, 0.2, 9.146, 0, 0.733, -20, 0, 1.267, 20, 0, 2.233, -20, 0, 4.5, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 2.233, 0.499, 0, 4.5, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "shangshenyaobai", "Segments": [0, 0, 1, 0.389, 2.845, 0.778, 17, 1.167, 17, 0, 3, -10, 1, 3.5, -10, 4, -9.248, 4.5, 0, 1, 4.889, 7.193, 5.278, 24, 5.667, 24, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.222, -1.426, 0.445, -27, 0.667, -27, 0, 2, 30, 0, 4.5, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 0.167, -0.289, 0, 0.367, 0.416, 0, 0.5, -0.152, 0, 0.667, 0.175, 0, 0.867, -0.177, 0, 1.133, 0.012, 0, 1.367, -0.053, 0, 1.7, -0.02, 0, 1.733, -0.021, 0, 2.3, 0.029, 0, 2.533, 0.008, 0, 2.8, 0.018, 0, 3, 0.016, 2, 3.033, 0.016, 2, 3.1, 0.016, 0, 3.233, 0.017, 2, 3.3, 0.017, 2, 3.333, 0.017, 0, 3.567, 0.014, 2, 3.6, 0.014, 0, 4.567, -0.005, 0, 4.8, 0.001, 0, 5, -0.358, 0, 5.2, 0.282, 0, 5.467, -0.048, 0, 5.633, -0.003, 0, 5.867, -0.221, 0, 6.1, 0.198, 0, 6.367, -0.043, 0, 6.6, 0.028, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 4.5, 0, 0, 5.433, -0.3, 0, 6.833, 0]}, {"Target": "Model", "Id": "Opacity", "Segments": [0, 1, 0, 6.833, 1]}, {"Target": "Parameter", "Id": "ParamHairSide", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamHairBack", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "shi<PERSON>ez<PERSON>biaozuoyou", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Param11", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "tiqiehuan", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "drzbxz", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "drybxz", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "tisousuo", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "dryoutui", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "renzuoyou", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "rens<PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "rendaxiao", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "jingshen", "Segments": [0, 0.5, 0, 6.833, 0.5]}, {"Target": "Parameter", "Id": "ZZZ", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "jingjingtmd", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "jingjingdaixioa", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "<PERSON>ng<PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "jingjingshangxai", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Param10", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "baiguang<PERSON><PERSON>u", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "lizi1daxoao", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "lizi2daxiao", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "liziyi<PERSON>", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "liziqumian", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "lizizong<PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "jing<PERSON>u", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "andu", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "lanmu", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "heidi", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "bai", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON>ia<PERSON><PERSON>uo<PERSON>", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "zu<PERSON><PERSON><PERSON>ng", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "xingfengyaobai", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "fizitmd", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 1, 0, 6.833, 1]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "youxiabifa", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "leimu", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "leidonghua", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "youleidonghua", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "leimudonghua", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "jingyaa", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamCheek", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "wu", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "zao1", "Segments": [0, 0.1, 0, 6.833, 0.1]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "<PERSON>banzhenmian", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "dibantmd", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "beijingtmd", "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "PartOpacity", "Id": "Part", "Segments": [0, 1, 0, 6.833, 1]}, {"Target": "PartOpacity", "Id": "Part24", "Segments": [0, 1, 0, 6.833, 1]}, {"Target": "PartOpacity", "Id": "Part38", "Segments": [0, 1, 0, 6.833, 1]}, {"Target": "PartOpacity", "Id": "Part3", "Segments": [0, 1, 0, 6.833, 1]}, {"Target": "PartOpacity", "Id": "toushipiaodai1_Skinning", "Segments": [0, 1, 0, 6.833, 1]}, {"Target": "PartOpacity", "Id": "toushipiaodai2_Skinning", "Segments": [0, 1, 0, 6.833, 1]}, {"Target": "PartOpacity", "Id": "Part2", "Segments": [0, 1, 0, 6.833, 1]}, {"Target": "PartOpacity", "Id": "Part18", "Segments": [0, 1, 0, 6.833, 1]}, {"Target": "PartOpacity", "Id": "toudaimao_Skinning", "Segments": [0, 1, 0, 6.833, 1]}, {"Target": "PartOpacity", "Id": "Part4", "Segments": [0, 1, 0, 6.833, 1]}, {"Target": "PartOpacity", "Id": "liuhaisi2_Skinning", "Segments": [0, 1, 0, 6.833, 1]}, {"Target": "PartOpacity", "Id": "Part17", "Segments": [0, 1, 0, 6.833, 1]}, {"Target": "PartOpacity", "Id": "Part22", "Segments": [0, 1, 0, 6.833, 1]}, {"Target": "PartOpacity", "Id": "Part21", "Segments": [0, 1, 0, 6.833, 1]}, {"Target": "PartOpacity", "Id": "cefatiao_Skinning2", "Segments": [0, 1, 0, 6.833, 1]}, {"Target": "PartOpacity", "Id": "Part5", "Segments": [0, 1, 0, 6.833, 1]}, {"Target": "PartOpacity", "Id": "Part6", "Segments": [0, 1, 0, 6.833, 1]}, {"Target": "PartOpacity", "Id": "Part7", "Segments": [0, 1, 0, 6.833, 1]}, {"Target": "PartOpacity", "Id": "Part45", "Segments": [0, 1, 0, 6.833, 1]}, {"Target": "PartOpacity", "Id": "Part26", "Segments": [0, 1, 0, 6.833, 1]}, {"Target": "PartOpacity", "Id": "Part8", "Segments": [0, 1, 0, 6.833, 1]}, {"Target": "PartOpacity", "Id": "Part56", "Segments": [0, 1, 0, 6.833, 1]}, {"Target": "PartOpacity", "Id": "Part27", "Segments": [0, 1, 0, 6.833, 1]}, {"Target": "PartOpacity", "Id": "Part30", "Segments": [0, 1, 0, 6.833, 1]}, {"Target": "PartOpacity", "Id": "Part37", "Segments": [0, 1, 0, 6.833, 1]}, {"Target": "PartOpacity", "Id": "Part31", "Segments": [0, 1, 0, 6.833, 1]}, {"Target": "PartOpacity", "Id": "Part9", "Segments": [0, 1, 0, 6.833, 1]}, {"Target": "PartOpacity", "Id": "Part39", "Segments": [0, 1, 0, 6.833, 1]}, {"Target": "PartOpacity", "Id": "houfatiao_Skinning", "Segments": [0, 1, 0, 6.833, 1]}, {"Target": "PartOpacity", "Id": "Part40", "Segments": [0, 1, 0, 6.833, 1]}, {"Target": "PartOpacity", "Id": "Part16", "Segments": [0, 1, 0, 6.833, 1]}, {"Target": "PartOpacity", "Id": "Part11", "Segments": [0, 1, 0, 6.833, 1]}, {"Target": "PartOpacity", "Id": "Part50", "Segments": [0, 1, 0, 6.833, 1]}, {"Target": "PartOpacity", "Id": "Part49", "Segments": [0, 1, 0, 6.833, 1]}, {"Target": "PartOpacity", "Id": "Part48", "Segments": [0, 1, 0, 6.833, 1]}, {"Target": "PartOpacity", "Id": "Part36", "Segments": [0, 1, 0, 6.833, 1]}, {"Target": "PartOpacity", "Id": "Part44", "Segments": [0, 1, 0, 6.833, 1]}, {"Target": "PartOpacity", "Id": "Part43", "Segments": [0, 1, 0, 6.833, 1]}, {"Target": "PartOpacity", "Id": "Part42", "Segments": [0, 1, 0, 6.833, 1]}, {"Target": "PartOpacity", "Id": "Part41", "Segments": [0, 1, 0, 6.833, 1]}, {"Target": "PartOpacity", "Id": "Part47", "Segments": [0, 1, 0, 6.833, 1]}, {"Target": "PartOpacity", "Id": "Part28", "Segments": [0, 1, 0, 6.833, 1]}, {"Target": "PartOpacity", "Id": "Part12", "Segments": [0, 1, 0, 6.833, 1]}, {"Target": "PartOpacity", "Id": "Part20", "Segments": [0, 1, 0, 6.833, 1]}, {"Target": "PartOpacity", "Id": "Part19", "Segments": [0, 1, 0, 6.833, 1]}, {"Target": "PartOpacity", "Id": "Part51", "Segments": [0, 1, 0, 6.833, 1]}, {"Target": "PartOpacity", "Id": "Part53", "Segments": [0, 1, 0, 6.833, 1]}, {"Target": "PartOpacity", "Id": "Part52", "Segments": [0, 1, 0, 6.833, 1]}, {"Target": "PartOpacity", "Id": "Part10", "Segments": [0, 1, 0, 6.833, 1]}, {"Target": "PartOpacity", "Id": "Part15", "Segments": [0, 1, 0, 6.833, 1]}, {"Target": "PartOpacity", "Id": "Part25", "Segments": [0, 1, 0, 6.833, 1]}, {"Target": "PartOpacity", "Id": "Part23", "Segments": [0, 1, 0, 6.833, 1]}, {"Target": "PartOpacity", "Id": "Part13", "Segments": [0, 1, 0, 6.833, 1]}, {"Target": "PartOpacity", "Id": "Part14", "Segments": [0, 1, 0, 6.833, 1]}, {"Target": "PartOpacity", "Id": "Part29", "Segments": [0, 0, 0, 6.833, 0]}], "UserData": [{"Time": 0.0, "Value": ""}, {"Time": 6.333, "Value": ""}]}