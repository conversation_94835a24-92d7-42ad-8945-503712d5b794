{"Version": 3, "Meta": {"Duration": 11.333, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 204, "TotalSegmentCount": 1730, "TotalPointCount": 1948, "UserDataCount": 2, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "wuli", "Segments": [0, 0, 0, 0.333, -1, 0, 0.5, 1, 0, 3.667, -0.949, 0, 4.133, 0.125, 0, 4.733, -1, 0, 5.333, 0.125, 0, 5.867, -1, 0, 6.2, 1, 1, 6.978, 1, 7.755, -0.233, 8.533, -0.949, 1, 8.666, -1.072, 8.8, -1, 8.933, -1, 0, 9.3, 1, 0, 9.733, -1, 0, 10.267, 1, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Para", "Segments": [0, 0, 0, 0.333, -1, 0, 0.433, 1, 0, 3.667, -0.991, 2, 8.533, -0.991, 0, 8.667, -1, 0, 9.067, 1, 0, 9.933, -1, 0, 10.333, 1, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "zuocefati1", "Segments": [0, -0.117, 0, 0.033, -0.116, 0, 0.2, -5.277, 0, 0.433, 7.026, 0, 0.5, 6.701, 0, 0.567, 6.963, 0, 0.933, -8.595, 0, 1.367, 4.062, 0, 1.8, -2.512, 0, 2.233, 2.398, 0, 2.7, -0.703, 0, 3.067, 0.697, 0, 3.567, -0.742, 0, 3.733, -0.636, 0, 3.9, -0.789, 0, 4.367, 1.665, 0, 4.867, -1.631, 0, 5.6, 2.38, 0, 6.133, -5.252, 0, 6.5, 4.005, 0, 6.933, -2.44, 0, 7.333, 1.407, 0, 7.767, -0.812, 0, 8.2, 0.587, 0, 8.767, -0.314, 0, 8.967, -0.041, 0, 9.2, -2.031, 0, 9.567, 4.277, 0, 10, -5.01, 0, 10.433, 4.233, 0, 10.833, -2.308, 0, 11.267, 1.254, 1, 11.289, 1.254, 11.311, 1.197, 11.333, 1.102]}, {"Target": "Parameter", "Id": "zuocefati2", "Segments": [0, -0.403, 0, 0.033, -0.404, 0, 0.133, 1.71, 0, 0.333, -10.439, 0, 0.7, 12.634, 0, 1.1, -12.526, 0, 1.5, 5.814, 0, 1.933, -5.278, 0, 2.4, 3.198, 0, 2.8, -0.731, 0, 3.2, 1.883, 0, 3.7, -0.554, 0, 3.833, -0.418, 0, 4.133, -1.61, 0, 4.533, 2.775, 0, 5.033, -2.277, 0, 5.833, 4.384, 0, 6.3, -8.854, 0, 6.633, 6.816, 0, 7.033, -4.412, 0, 7.467, 2.201, 0, 7.9, -1.478, 0, 8.333, 0.879, 0, 8.967, -0.328, 0, 9.1, 0.487, 0, 9.4, -4.736, 0, 9.767, 7.988, 0, 10.133, -8.479, 0, 10.567, 6.547, 0, 10.967, -3.69, 1, 11.089, -3.69, 11.211, 1.382, 11.333, 2.227]}, {"Target": "Parameter", "Id": "zuocefati3", "Segments": [0, 0.853, 1, 0.078, 2.029, 0.155, 3.99, 0.233, 3.99, 0, 0.467, -10.764, 0, 0.8, 11.482, 0, 1.2, -11, 0, 1.6, 7.91, 0, 2, -5.271, 0, 2.4, 2.876, 0, 2.867, -1.689, 0, 3.3, 1.25, 0, 3.7, -0.619, 0, 4, 0.488, 0, 4.3, -1.506, 0, 4.667, 2.289, 0, 5.067, -1.617, 0, 5.467, 0.358, 0, 5.667, -0.5, 0, 6.033, 3.121, 0, 6.4, -7.616, 0, 6.767, 7.906, 0, 7.133, -5.478, 0, 7.5, 3.118, 0, 7.933, -1.657, 0, 8.367, 0.901, 0, 8.733, -0.398, 0, 9.233, 1.598, 0, 9.533, -5.075, 0, 9.867, 8.026, 0, 10.233, -8.06, 0, 10.667, 6.458, 0, 11.033, -4.581, 1, 11.133, -4.581, 11.233, -0.323, 11.333, 1.807]}, {"Target": "Parameter", "Id": "zuocefati4", "Segments": [0, 0.262, 1, 0.1, 1.079, 0.2, 3.326, 0.3, 3.326, 0, 0.533, -10.659, 0, 0.867, 15.337, 0, 1.267, -14.549, 0, 1.667, 11.735, 0, 2.067, -8.645, 0, 2.467, 5.146, 0, 2.9, -2.745, 0, 3.333, 1.919, 0, 3.733, -1.125, 0, 4.067, 0.829, 0, 4.4, -2.012, 0, 4.733, 3.276, 0, 5.133, -2.777, 0, 5.5, 0.926, 0, 5.767, -0.692, 0, 6.1, 3.873, 0, 6.5, -9.296, 0, 6.833, 11.292, 0, 7.2, -9.139, 0, 7.567, 5.911, 0, 7.967, -3.3, 0, 8.367, 1.623, 0, 8.8, -0.776, 0, 9.333, 1.626, 0, 9.633, -6.037, 0, 9.967, 10.616, 0, 10.333, -11.703, 0, 10.733, 9.728, 0, 11.133, -7.343, 1, 11.2, -7.343, 11.266, -3.612, 11.333, -0.221]}, {"Target": "Parameter", "Id": "zuocefati5", "Segments": [0, -0.079, 0, 0.367, 3.063, 0, 0.633, -11.295, 0, 0.933, 18.762, 0, 1.333, -18.147, 0, 1.733, 15.516, 0, 2.133, -12.699, 0, 2.533, 8.555, 0, 2.933, -4.948, 0, 3.367, 2.962, 0, 3.8, -1.878, 0, 4.167, 1.456, 0, 4.5, -2.742, 0, 4.833, 4.655, 0, 5.2, -4.547, 0, 5.567, 2.215, 0, 5.867, -1.354, 0, 6.2, 4.914, 0, 6.567, -11.177, 0, 6.933, 14.842, 0, 7.3, -13.451, 0, 7.667, 9.927, 0, 8.033, -6.313, 0, 8.433, 3.376, 0, 8.833, -1.555, 0, 9.4, 1.622, 0, 9.7, -6.945, 0, 10.033, 13.34, 0, 10.4, -15.632, 0, 10.8, 13.658, 0, 11.2, -11.05, 1, 11.244, -11.05, 11.289, -8.892, 11.333, -6.016]}, {"Target": "Parameter", "Id": "fashipiaodai1", "Segments": [0, -0.146, 0, 0.033, -0.145, 0, 0.2, -6.597, 0, 0.433, 8.782, 0, 0.5, 8.376, 0, 0.567, 8.704, 0, 0.933, -10.744, 0, 1.367, 5.078, 0, 1.8, -3.14, 0, 2.233, 2.998, 0, 2.7, -0.878, 0, 3.067, 0.871, 0, 3.567, -0.927, 0, 3.733, -0.795, 0, 3.9, -0.987, 0, 4.367, 2.081, 0, 4.867, -2.038, 0, 5.6, 2.975, 0, 6.133, -6.565, 0, 6.5, 5.006, 0, 6.933, -3.05, 0, 7.333, 1.758, 0, 7.767, -1.014, 0, 8.2, 0.734, 0, 8.767, -0.393, 0, 8.967, -0.052, 0, 9.2, -2.539, 0, 9.567, 5.346, 0, 10, -6.263, 0, 10.433, 5.292, 0, 10.833, -2.885, 0, 11.267, 1.567, 1, 11.289, 1.567, 11.311, 1.496, 11.333, 1.378]}, {"Target": "Parameter", "Id": "fashipiaodai2", "Segments": [0, -0.503, 1, 0.011, -0.504, 0.022, -0.505, 0.033, -0.505, 0, 0.133, 2.137, 0, 0.333, -13.049, 0, 0.7, 15.792, 0, 1.1, -15.657, 0, 1.5, 7.267, 0, 1.933, -6.598, 0, 2.4, 3.998, 0, 2.8, -0.913, 0, 3.2, 2.353, 0, 3.7, -0.693, 0, 3.833, -0.523, 0, 4.133, -2.012, 0, 4.533, 3.469, 0, 5.033, -2.846, 0, 5.833, 5.48, 0, 6.3, -11.068, 0, 6.633, 8.52, 0, 7.033, -5.516, 0, 7.467, 2.751, 0, 7.9, -1.848, 0, 8.333, 1.099, 0, 8.967, -0.41, 0, 9.1, 0.609, 0, 9.4, -5.92, 0, 9.767, 9.985, 0, 10.133, -10.599, 0, 10.567, 8.184, 0, 10.967, -4.613, 1, 11.089, -4.613, 11.211, 1.727, 11.333, 2.783]}, {"Target": "Parameter", "Id": "fashipiaodai3", "Segments": [0, 1.066, 1, 0.078, 2.536, 0.155, 4.987, 0.233, 4.987, 0, 0.467, -13.455, 0, 0.8, 14.353, 0, 1.2, -13.751, 0, 1.6, 9.888, 0, 2, -6.589, 0, 2.4, 3.596, 0, 2.867, -2.111, 0, 3.3, 1.563, 0, 3.7, -0.774, 0, 4, 0.61, 0, 4.3, -1.882, 0, 4.667, 2.861, 0, 5.067, -2.021, 0, 5.467, 0.447, 0, 5.667, -0.626, 0, 6.033, 3.902, 0, 6.4, -9.52, 0, 6.767, 9.883, 0, 7.133, -6.848, 0, 7.5, 3.898, 0, 7.933, -2.071, 0, 8.367, 1.126, 0, 8.733, -0.498, 0, 9.233, 1.998, 0, 9.533, -6.344, 0, 9.867, 10.033, 0, 10.233, -10.075, 0, 10.667, 8.073, 0, 11.033, -5.726, 1, 11.133, -5.726, 11.233, -0.403, 11.333, 2.258]}, {"Target": "Parameter", "Id": "fashipiaodai4", "Segments": [0, 0.327, 1, 0.1, 1.349, 0.2, 4.158, 0.3, 4.158, 0, 0.533, -13.324, 0, 0.867, 19.172, 0, 1.267, -18.186, 0, 1.667, 14.669, 0, 2.067, -10.807, 0, 2.467, 6.432, 0, 2.9, -3.431, 0, 3.333, 2.398, 0, 3.733, -1.407, 0, 4.067, 1.036, 0, 4.4, -2.515, 0, 4.733, 4.095, 0, 5.133, -3.471, 0, 5.5, 1.157, 0, 5.767, -0.866, 0, 6.1, 4.841, 0, 6.5, -11.62, 0, 6.833, 14.115, 0, 7.2, -11.424, 0, 7.567, 7.389, 0, 7.967, -4.125, 0, 8.367, 2.029, 0, 8.8, -0.97, 0, 9.333, 2.033, 0, 9.633, -7.546, 0, 9.967, 13.27, 0, 10.333, -14.629, 0, 10.733, 12.16, 0, 11.133, -9.178, 1, 11.2, -9.178, 11.266, -4.515, 11.333, -0.276]}, {"Target": "Parameter", "Id": "fashipiaodai5", "Segments": [0, -0.099, 0, 0.367, 3.829, 0, 0.633, -14.119, 0, 0.933, 23.453, 0, 1.333, -22.684, 0, 1.733, 19.395, 0, 2.133, -15.874, 0, 2.533, 10.693, 0, 2.933, -6.185, 0, 3.367, 3.702, 0, 3.8, -2.348, 0, 4.167, 1.82, 0, 4.5, -3.427, 0, 4.833, 5.819, 0, 5.2, -5.684, 0, 5.567, 2.769, 0, 5.867, -1.692, 0, 6.2, 6.142, 0, 6.567, -13.972, 0, 6.933, 18.553, 0, 7.3, -16.813, 0, 7.667, 12.408, 0, 8.033, -7.892, 0, 8.433, 4.22, 0, 8.833, -1.943, 0, 9.4, 2.027, 0, 9.7, -8.681, 0, 10.033, 16.675, 0, 10.4, -19.54, 0, 10.8, 17.072, 0, 11.2, -13.812, 1, 11.244, -13.812, 11.289, -11.115, 11.333, -7.52]}, {"Target": "Parameter", "Id": "fashipiaodai6", "Segments": [0, -0.172, 1, 0.022, -0.203, 0.045, -0.222, 0.067, -0.222, 0, 0.467, 4.083, 0, 0.7, -14.846, 0, 1, 26.641, 0, 1.4, -26.714, 0, 1.8, 23.737, 0, 2.2, -20.71, 0, 2.6, 15.617, 0, 3, -10.419, 0, 3.4, 6.264, 0, 3.833, -3.719, 0, 4.233, 2.956, 0, 4.567, -4.794, 0, 4.933, 8.011, 0, 5.3, -8.73, 0, 5.633, 5.369, 0, 5.967, -3.407, 0, 6.267, 7.97, 0, 6.633, -16.346, 0, 7, 22.64, 0, 7.367, -22.022, 0, 7.767, 17.94, 0, 8.133, -13.066, 0, 8.5, 8.116, 0, 8.867, -4.268, 0, 9.267, 2.283, 0, 9.8, -9.677, 0, 10.1, 19.702, 0, 10.467, -23.991, 0, 10.867, 21.891, 0, 11.267, -18.742, 1, 11.289, -18.742, 11.311, -17.791, 11.333, -16.204]}, {"Target": "Parameter", "Id": "daimao1", "Segments": [0, -0.128, 0, 0.033, -0.127, 0, 0.2, -5.016, 0, 0.4, 7.005, 0, 0.5, 3.875, 0, 0.6, 5.19, 0, 0.967, -7.531, 0, 1.4, 3.509, 0, 1.8, -2.121, 0, 2.267, 2.345, 0, 2.7, -0.618, 0, 3.1, 0.639, 0, 3.6, -0.815, 0, 3.7, -0.774, 0, 3.933, -1.578, 0, 4.367, 3.207, 0, 4.867, -3.144, 0, 5.567, 3.422, 0, 6.1, -8.073, 0, 6.467, 6.816, 0, 6.9, -4.04, 0, 7.333, 2.404, 0, 7.767, -1.338, 0, 8.167, 0.944, 0, 8.733, -0.57, 0, 8.967, -0.032, 0, 9.2, -3.82, 0, 9.567, 7.952, 0, 10, -9.205, 0, 10.433, 7.736, 0, 10.867, -4.154, 0, 11.267, 2.246, 1, 11.289, 2.246, 11.311, 2.144, 11.333, 1.974]}, {"Target": "Parameter", "Id": "daimao2", "Segments": [0, -0.441, 1, 0.011, -0.442, 0.022, -0.443, 0.033, -0.443, 0, 0.133, 1.663, 0, 0.333, -10.397, 0, 0.533, 7.699, 0, 0.6, 7.551, 0, 0.767, 9.295, 0, 1.133, -11.313, 0, 1.533, 5.149, 0, 1.933, -4.768, 0, 2.4, 3.207, 0, 2.833, -0.432, 0, 3.233, 1.956, 0, 3.733, -0.547, 0, 3.833, -0.358, 0, 4.167, -3.329, 0, 4.533, 5.364, 0, 5.033, -4.396, 0, 5.8, 5.741, 0, 6.267, -13.989, 0, 6.633, 11.39, 0, 7.033, -7.072, 0, 7.467, 3.912, 0, 7.867, -2.264, 0, 8.3, 1.579, 0, 8.933, -0.584, 0, 9.1, 0.972, 0, 9.4, -8.785, 0, 9.733, 14.353, 0, 10.133, -14.824, 0, 10.567, 11.504, 0, 11, -6.464, 1, 11.111, -6.464, 11.222, 0.944, 11.333, 3.413]}, {"Target": "Parameter", "Id": "daimao3", "Segments": [0, 0.811, 1, 0.078, 1.934, 0.155, 3.806, 0.233, 3.806, 0, 0.467, -10.899, 0, 0.8, 8.154, 0, 1.233, -9.587, 0, 1.6, 7.545, 0, 2, -5.046, 0, 2.4, 2.586, 0, 2.9, -1.543, 0, 3.3, 1.211, 0, 3.7, -0.56, 0, 4, 0.928, 0, 4.3, -3.187, 0, 4.667, 4.542, 0, 5.067, -3.15, 0, 5.467, 0.867, 0, 5.667, -0.086, 0, 6.033, 3.896, 0, 6.4, -11.741, 0, 6.733, 12.319, 0, 7.1, -8.591, 0, 7.5, 5.07, 0, 7.9, -2.769, 0, 8.333, 1.541, 0, 8.733, -0.69, 0, 9.233, 2.993, 0, 9.533, -9.114, 0, 9.867, 13.985, 0, 10.233, -13.322, 0, 10.667, 10.729, 0, 11.067, -7.738, 1, 11.156, -7.738, 11.244, -2.035, 11.333, 1.767]}, {"Target": "Parameter", "Id": "daimao4", "Segments": [0, 0.24, 1, 0.1, 1.015, 0.2, 3.145, 0.3, 3.145, 0, 0.567, -10.347, 0, 0.867, 12.989, 0, 1.3, -12.763, 0, 1.7, 11.529, 0, 2.067, -8.656, 0, 2.467, 4.958, 0, 2.9, -2.429, 0, 3.367, 1.811, 0, 3.767, -1.052, 0, 4.067, 1.269, 0, 4.4, -4.031, 0, 4.733, 6.458, 0, 5.133, -5.339, 0, 5.533, 2.011, 0, 5.8, -0.407, 0, 6.1, 4.712, 0, 6.467, -13.613, 0, 6.833, 17.115, 0, 7.2, -13.781, 0, 7.567, 9.107, 0, 7.967, -5.33, 0, 8.367, 2.8, 0, 8.767, -1.382, 0, 9.333, 3.029, 0, 9.6, -10.528, 0, 9.933, 17.872, 0, 10.3, -18.078, 0, 10.733, 15.029, 0, 11.133, -11.843, 1, 11.2, -11.843, 11.266, -6.743, 11.333, -1.643]}, {"Target": "Parameter", "Id": "Param", "Segments": [0, -0.11, 1, 0.011, -0.109, 0.022, -0.109, 0.033, -0.109, 0, 0.2, -4.646, 0, 0.4, 6.091, 0, 0.5, 4.919, 0, 0.567, 5.421, 0, 0.967, -7.305, 0, 1.4, 3.429, 0, 1.8, -2.12, 0, 2.267, 2.132, 0, 2.7, -0.602, 0, 3.067, 0.603, 0, 3.6, -0.695, 0, 3.7, -0.634, 0, 3.933, -1.017, 0, 4.367, 2.115, 0, 4.867, -2.074, 0, 5.6, 2.541, 0, 6.133, -5.854, 0, 6.5, 4.729, 0, 6.9, -2.839, 0, 7.333, 1.67, 0, 7.767, -0.944, 0, 8.2, 0.669, 0, 8.733, -0.385, 0, 8.967, -0.033, 0, 9.2, -2.543, 0, 9.567, 5.325, 0, 10, -6.216, 0, 10.433, 5.235, 0, 10.833, -2.835, 0, 11.267, 1.537, 1, 11.289, 1.537, 11.311, 1.467, 11.333, 1.351]}, {"Target": "Parameter", "Id": "Param2", "Segments": [0, -0.378, 1, 0.011, -0.379, 0.022, -0.379, 0.033, -0.379, 0, 0.133, 1.523, 0, 0.333, -9.38, 0, 0.733, 10.057, 0, 1.133, -10.78, 0, 1.533, 4.977, 0, 1.933, -4.573, 0, 2.4, 2.891, 0, 2.8, -0.53, 0, 3.233, 1.725, 0, 3.7, -0.496, 0, 3.833, -0.35, 0, 4.133, -2.133, 0, 4.533, 3.537, 0, 5.033, -2.902, 0, 5.833, 4.455, 0, 6.3, -9.999, 0, 6.633, 7.952, 0, 7.033, -5.047, 0, 7.467, 2.676, 0, 7.867, -1.643, 0, 8.3, 1.068, 0, 8.933, -0.399, 0, 9.1, 0.633, 0, 9.4, -5.895, 0, 9.767, 9.809, 0, 10.133, -10.276, 0, 10.567, 7.945, 0, 10.967, -4.474, 1, 11.089, -4.474, 11.211, 0.787, 11.333, 2.406]}, {"Target": "Parameter", "Id": "Param3", "Segments": [0, 0.75, 1, 0.078, 1.789, 0.155, 3.52, 0.233, 3.52, 0, 0.467, -9.814, 0, 0.8, 9.139, 0, 1.2, -9.39, 0, 1.6, 7.084, 0, 2, -4.687, 0, 2.4, 2.486, 0, 2.867, -1.462, 0, 3.3, 1.113, 0, 3.7, -0.536, 0, 4, 0.619, 0, 4.3, -2.054, 0, 4.667, 2.973, 0, 5.067, -2.078, 0, 5.467, 0.517, 0, 5.667, -0.268, 0, 6.033, 3.121, 0, 6.4, -8.541, 0, 6.767, 8.86, 0, 7.133, -6.185, 0, 7.5, 3.6, 0, 7.933, -1.939, 0, 8.333, 1.066, 0, 8.733, -0.476, 0, 9.233, 2.002, 0, 9.533, -6.229, 0, 9.867, 9.764, 0, 10.233, -9.513, 0, 10.667, 7.617, 0, 11.067, -5.428, 1, 11.156, -5.428, 11.244, -0.691, 11.333, 1.892]}, {"Target": "Parameter", "Id": "Param4", "Segments": [0, 0.225, 1, 0.1, 0.945, 0.2, 2.924, 0.3, 2.924, 0, 0.567, -9.524, 0, 0.867, 13.016, 0, 1.3, -12.681, 0, 1.667, 10.614, 0, 2.067, -7.874, 0, 2.467, 4.579, 0, 2.9, -2.345, 0, 3.333, 1.671, 0, 3.767, -0.982, 0, 4.067, 0.928, 0, 4.4, -2.642, 0, 4.733, 4.249, 0, 5.133, -3.554, 0, 5.533, 1.23, 0, 5.8, -0.471, 0, 6.1, 3.814, 0, 6.5, -10.05, 0, 6.833, 12.557, 0, 7.2, -10.122, 0, 7.567, 6.626, 0, 7.967, -3.799, 0, 8.367, 1.939, 0, 8.767, -0.944, 0, 9.333, 2.031, 0, 9.633, -7.294, 0, 9.933, 12.565, 0, 10.333, -13.333, 0, 10.733, 11.046, 0, 11.133, -8.554, 1, 11.2, -8.554, 11.266, -4.215, 11.333, -0.271]}, {"Target": "Parameter", "Id": "Param5", "Segments": [0, -0.08, 0, 0.367, 2.709, 0, 0.633, -10.303, 0, 0.933, 16.472, 0, 1.367, -16.111, 0, 1.767, 14.483, 0, 2.133, -11.809, 0, 2.533, 7.864, 0, 2.933, -4.341, 0, 3.367, 2.506, 0, 3.8, -1.598, 0, 4.167, 1.47, 0, 4.5, -3.433, 0, 4.833, 5.962, 0, 5.2, -5.75, 0, 5.567, 2.796, 0, 5.867, -1.248, 0, 6.2, 4.817, 0, 6.533, -11.795, 0, 6.9, 15.969, 0, 7.3, -14.464, 0, 7.667, 10.804, 0, 8.033, -7.019, 0, 8.433, 3.869, 0, 8.833, -1.872, 0, 9.4, 2.012, 0, 9.7, -8.377, 0, 10, 15.292, 0, 10.4, -17.166, 0, 10.8, 14.898, 0, 11.2, -12.291, 1, 11.244, -12.291, 11.289, -9.862, 11.333, -6.623]}, {"Target": "Parameter", "Id": "ZUOHOUFABG1", "Segments": [0, -0.066, 1, 0.011, -0.065, 0.022, -0.065, 0.033, -0.065, 0, 0.167, -2.125, 0, 0.367, 3.475, 0, 0.5, -0.452, 0, 0.667, 1.984, 0, 1.033, -2.678, 0, 1.467, 1.21, 0, 1.867, -0.643, 0, 2.3, 1.033, 0, 2.767, -0.232, 0, 3.133, 0.238, 0, 3.933, -1.319, 0, 4.367, 2.599, 0, 4.867, -2.545, 0, 5.533, 2.414, 0, 6.1, -5.799, 0, 6.467, 5.19, 0, 6.9, -2.983, 0, 7.333, 1.805, 0, 7.733, -0.993, 0, 8.167, 0.69, 0, 8.733, -0.451, 0, 8.967, -0.012, 0, 9.2, -3.056, 0, 9.567, 6.287, 0, 10, -7.193, 0, 10.433, 5.996, 0, 10.867, -3.184, 0, 11.267, 1.698, 1, 11.289, 1.698, 11.311, 1.621, 11.333, 1.493]}, {"Target": "Parameter", "Id": "ZUOHOUFABG2", "Segments": [0, -0.226, 2, 0.033, -0.226, 0, 0.133, 0.701, 0, 0.333, -4.632, 0, 0.5, 4.933, 0, 0.667, 0.296, 0, 0.867, 3.109, 0, 1.2, -4.357, 0, 1.6, 1.777, 0, 1.967, -1.696, 0, 2.467, 1.536, 0, 2.867, 0.004, 0, 3.267, 0.924, 0, 3.733, -0.242, 0, 3.833, -0.124, 0, 4.167, -2.8, 0, 4.533, 4.327, 0, 5.033, -3.534, 0, 5.767, 3.684, 0, 6.267, -10.162, 0, 6.633, 8.37, 0, 7.033, -5.059, 0, 7.433, 3.003, 0, 7.867, -1.589, 0, 8.3, 1.23, 0, 8.933, -0.459, 0, 9.1, 0.791, 0, 9.4, -6.895, 0, 9.733, 11.028, 0, 10.133, -10.977, 0, 10.567, 8.552, 0, 11, -4.83, 1, 11.111, -4.83, 11.222, 0.731, 11.333, 2.585]}, {"Target": "Parameter", "Id": "ZUOHOUFABG3", "Segments": [0, 0.334, 1, 0.078, 0.796, 0.155, 1.566, 0.233, 1.566, 0, 0.433, -4.909, 0, 0.667, 3.442, 0, 0.867, -0.096, 0, 1.033, 1.762, 0, 1.333, -3.642, 0, 1.667, 3.078, 0, 2.033, -2.038, 0, 2.467, 1.004, 0, 2.933, -0.654, 0, 3.333, 0.53, 0, 3.733, -0.206, 0, 4, 0.728, 0, 4.3, -2.615, 0, 4.667, 3.67, 0, 5.067, -2.501, 0, 5.5, 0.766, 0, 5.667, 0.281, 0, 6.033, 2.301, 0, 6.4, -8.126, 0, 6.733, 8.675, 0, 7.1, -6.006, 0, 7.5, 3.627, 0, 7.9, -2.049, 0, 8.333, 1.165, 0, 8.7, -0.527, 0, 9.233, 2.359, 0, 9.533, -6.796, 0, 9.867, 10.185, 0, 10.233, -9.355, 0, 10.667, 7.568, 0, 11.067, -5.542, 1, 11.156, -5.542, 11.244, -1.402, 11.333, 1.359]}, {"Target": "Parameter", "Id": "hounaofa1yaobai", "Segments": [0, -0.32, 1, 0.056, -0.325, 0.111, -0.327, 0.167, -0.327, 0, 0.367, 0.426, 0, 0.533, 0.03, 0, 0.6, 0.062, 0, 0.833, -0.162, 0, 1.1, 0.008, 0, 1.333, -0.053, 0, 1.633, -0.024, 2, 1.667, -0.024, 0, 1.7, -0.023, 2, 1.733, -0.023, 0, 2.333, 0.029, 0, 2.533, 0.021, 0, 2.8, 0.025, 0, 3.867, -0.032, 0, 4.267, 0.032, 0, 4.933, -0.024, 0, 5.6, 0.092, 0, 6.1, -0.124, 0, 6.333, 0.085, 0, 6.567, -0.036, 0, 6.833, 0.004, 0, 7.1, -0.009, 0, 7.367, -0.004, 0, 7.567, -0.005, 0, 8.567, 0.006, 0, 8.767, -0.007, 0, 8.967, 0.002, 0, 9.167, -0.083, 0, 9.467, 0.109, 0, 9.833, -0.069, 0, 10.333, 0.032, 0, 10.6, -0.002, 0, 10.833, 0.009, 1, 11, 0.009, 11.166, -0.001, 11.333, -0.004]}, {"Target": "Parameter", "Id": "hounaifa1bianxing", "Segments": [0, 0, 2, 0.033, 0, 0, 0.133, 0.327, 0, 0.3, -0.788, 0, 0.5, 0.83, 0, 0.7, -0.246, 0, 0.833, 0.031, 0, 0.967, -0.136, 0, 1.2, 0.132, 0, 1.467, -0.077, 0, 1.7, 0.033, 0, 1.967, -0.021, 0, 2.2, 0.001, 0, 2.267, 0, 0, 2.433, 0.013, 0, 2.667, -0.011, 0, 2.967, 0.007, 0, 3.2, -0.001, 0, 3.4, 0.004, 2, 3.467, 0.004, 2, 3.5, 0.004, 0, 3.633, 0.003, 2, 3.667, 0.003, 2, 3.7, 0.003, 0, 3.8, 0.011, 0, 4.033, -0.031, 0, 4.367, 0.034, 0, 4.6, -0.013, 0, 4.833, 0.013, 0, 5.067, -0.018, 0, 5.3, 0.002, 0, 5.5, -0.051, 0, 5.733, 0.079, 0, 5.9, 0, 0, 6.033, 0.04, 0, 6.233, -0.203, 0, 6.467, 0.215, 0, 6.7, -0.134, 0, 6.933, 0.066, 0, 7.2, -0.03, 0, 7.433, 0.012, 0, 7.7, -0.006, 0, 7.967, 0.002, 0, 8.233, -0.001, 0, 8.467, 0, 2, 8.533, 0, 0, 8.7, 0.014, 0, 8.9, -0.015, 0, 9.1, 0.08, 0, 9.333, -0.156, 0, 9.567, 0.17, 0, 9.9, -0.083, 0, 10.167, 0.026, 0, 10.333, -0.01, 0, 10.5, 0.031, 0, 10.733, -0.027, 0, 10.967, 0.017, 0, 11.233, -0.006, 1, 11.266, -0.006, 11.3, -0.002, 11.333, 0.001]}, {"Target": "Parameter", "Id": "hounaofa2yaobai", "Segments": [0, -0.985, 1, 0.044, -0.995, 0.089, -1, 0.133, -1, 2, 0.2, -1, 0, 0.3, 1, 2, 0.433, 1, 0, 0.533, 0.122, 0, 0.6, 0.248, 0, 0.833, -0.646, 0, 1.1, 0.031, 0, 1.333, -0.214, 0, 1.633, -0.096, 2, 1.667, -0.096, 0, 1.7, -0.092, 0, 1.733, -0.093, 0, 2.333, 0.116, 0, 2.533, 0.083, 0, 2.8, 0.1, 0, 3.867, -0.129, 0, 4.267, 0.13, 0, 4.933, -0.097, 0, 5.6, 0.37, 0, 6.1, -0.498, 0, 6.333, 0.34, 0, 6.567, -0.144, 0, 6.833, 0.017, 0, 7.1, -0.038, 0, 7.367, -0.015, 0, 7.567, -0.019, 0, 8.567, 0.025, 0, 8.767, -0.029, 0, 8.967, 0.007, 0, 9.167, -0.334, 0, 9.467, 0.437, 0, 9.833, -0.278, 0, 10.333, 0.13, 0, 10.6, -0.01, 0, 10.833, 0.034, 1, 11, 0.034, 11.166, -0.006, 11.333, -0.015]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, -0.32, 1, 0.056, -0.325, 0.111, -0.327, 0.167, -0.327, 0, 0.367, 0.426, 0, 0.533, 0.03, 0, 0.6, 0.062, 0, 0.833, -0.162, 0, 1.1, 0.008, 0, 1.333, -0.053, 0, 1.633, -0.024, 2, 1.667, -0.024, 0, 1.7, -0.023, 2, 1.733, -0.023, 0, 2.333, 0.029, 0, 2.533, 0.021, 0, 2.8, 0.025, 0, 3.867, -0.032, 0, 4.267, 0.032, 0, 4.933, -0.024, 0, 5.6, 0.092, 0, 6.1, -0.124, 0, 6.333, 0.085, 0, 6.567, -0.036, 0, 6.833, 0.004, 0, 7.1, -0.009, 0, 7.367, -0.004, 0, 7.567, -0.005, 0, 8.567, 0.006, 0, 8.767, -0.007, 0, 8.967, 0.002, 0, 9.167, -0.083, 0, 9.467, 0.109, 0, 9.833, -0.069, 0, 10.333, 0.032, 0, 10.6, -0.002, 0, 10.833, 0.009, 1, 11, 0.009, 11.166, -0.001, 11.333, -0.004]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, -0.274, 1, 0.056, -0.278, 0.111, -0.28, 0.167, -0.28, 0, 0.4, 0.327, 0, 0.833, -0.192, 0, 1.167, 0.015, 0, 1.467, -0.059, 0, 2.367, 0.031, 0, 2.633, 0.021, 0, 2.833, 0.025, 0, 2.867, 0.024, 0, 2.9, 0.025, 0, 3.867, -0.025, 0, 4.3, 0.026, 0, 4.933, -0.018, 0, 5.633, 0.088, 0, 6.1, -0.104, 0, 6.367, 0.054, 0, 6.7, -0.028, 0, 7, 0, 0, 7.3, -0.01, 0, 7.633, -0.004, 2, 7.667, -0.004, 2, 7.733, -0.004, 2, 7.767, -0.004, 0, 8.567, 0.006, 0, 8.8, -0.005, 0, 8.967, -0.001, 0, 9.2, -0.055, 0, 9.5, 0.081, 0, 9.9, -0.063, 0, 10.333, 0.028, 0, 10.667, -0.003, 0, 10.933, 0.006, 1, 11.066, 0.006, 11.2, -0.002, 11.333, -0.004]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.033, 0, 0, 0.133, 0.145, 0, 0.333, -0.323, 0, 0.567, 0.236, 0, 0.967, -0.187, 0, 1.267, 0.118, 0, 1.6, -0.064, 0, 1.9, 0.026, 0, 2.2, -0.017, 0, 2.5, 0.015, 0, 2.8, -0.008, 0, 3.133, 0.006, 0, 3.367, 0, 0, 3.767, 0.004, 0, 4.1, -0.015, 0, 4.467, 0.018, 0, 4.733, -0.006, 0, 4.933, 0, 0, 5.167, -0.005, 0, 5.367, -0.002, 0, 5.533, -0.027, 0, 5.8, 0.047, 0, 6.267, -0.1, 0, 6.533, 0.088, 0, 6.833, -0.052, 0, 7.133, 0.026, 0, 7.433, -0.012, 0, 7.767, 0.005, 0, 8.033, -0.002, 0, 8.367, 0, 0, 8.567, -0.001, 0, 8.733, 0.006, 0, 8.967, -0.005, 0, 9.133, 0.027, 0, 9.367, -0.064, 0, 9.667, 0.076, 0, 10, -0.06, 0, 10.4, 0.025, 0, 10.767, -0.017, 0, 11.067, 0.01, 1, 11.156, 0.01, 11.244, 0.004, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamHairFront", "Segments": [0, -0.977, 1, 0.056, -0.992, 0.111, -1, 0.167, -1, 0, 0.367, 1, 2, 0.433, 1, 0, 0.533, 0.413, 0, 0.6, 0.529, 0, 0.867, -0.676, 0, 1.167, 0.035, 0, 1.467, -0.212, 0, 2.367, 0.133, 0, 2.633, 0.093, 0, 2.833, 0.104, 2, 2.867, 0.104, 2, 2.9, 0.104, 0, 3.9, -0.186, 0, 4.3, 0.212, 0, 4.933, -0.141, 0, 5.6, 0.425, 0, 6.1, -0.679, 0, 6.367, 0.42, 0, 6.667, -0.171, 0, 7, 0.034, 0, 7.3, -0.035, 0, 7.633, -0.002, 2, 7.667, -0.002, 2, 7.7, -0.002, 0, 7.8, -0.004, 0, 7.833, -0.003, 0, 7.867, -0.004, 0, 8.567, 0.031, 0, 8.8, -0.035, 0, 8.967, -0.003, 0, 9.2, -0.442, 0, 9.5, 0.635, 0, 9.9, -0.499, 0, 10.333, 0.222, 0, 10.667, -0.026, 0, 10.933, 0.05, 1, 11.066, 0.05, 11.2, -0.014, 11.333, -0.032]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0.341, 1, 0.056, 0.366, 0.111, 0.382, 0.167, 0.382, 0, 0.367, -0.559, 0, 0.533, 0.255, 0, 0.7, 0.038, 0, 0.8, 0.096, 0, 1.033, -0.222, 0, 1.333, 0.261, 0, 1.667, -0.094, 0, 1.967, 0.088, 0, 2.333, -0.06, 0, 2.6, -0.005, 0, 2.9, -0.045, 0, 3.3, -0.015, 0, 3.367, -0.016, 0, 3.867, 0.05, 0, 4.2, -0.055, 0, 4.467, 0.037, 0, 4.733, -0.033, 0, 5.033, 0.03, 0, 5.567, -0.109, 0, 5.867, 0.032, 2, 5.9, 0.032, 0, 6.067, 0.121, 0, 6.3, -0.249, 0, 6.567, 0.261, 0, 6.867, -0.174, 0, 7.167, 0.11, 0, 7.5, -0.05, 0, 7.833, 0.029, 0, 8.2, -0.015, 0, 8.467, -0.001, 0, 8.567, -0.003, 0, 8.733, 0.014, 0, 8.967, -0.016, 0, 9.133, 0.152, 0, 9.433, -0.231, 0, 9.733, 0.21, 0, 10.033, -0.167, 0, 10.4, 0.114, 0, 10.767, -0.107, 0, 11.1, 0.053, 1, 11.178, 0.053, 11.255, 0.02, 11.333, -0.004]}, {"Target": "Parameter", "Id": "Param6", "Segments": [0, -0.004, 2, 0.033, -0.004, 0, 0.167, -0.142, 0, 0.367, 0.232, 0, 0.5, -0.03, 0, 0.667, 0.132, 0, 1.033, -0.179, 0, 1.467, 0.081, 0, 1.867, -0.043, 0, 2.3, 0.069, 0, 2.767, -0.015, 0, 3.133, 0.016, 0, 3.933, -0.088, 0, 4.367, 0.173, 0, 4.867, -0.17, 0, 5.533, 0.161, 0, 6.1, -0.387, 0, 6.467, 0.346, 0, 6.9, -0.199, 0, 7.333, 0.12, 0, 7.733, -0.066, 0, 8.167, 0.046, 0, 8.733, -0.03, 0, 8.967, -0.001, 0, 9.2, -0.204, 0, 9.567, 0.419, 0, 10, -0.48, 0, 10.433, 0.4, 0, 10.867, -0.212, 0, 11.267, 0.113, 1, 11.289, 0.113, 11.311, 0.108, 11.333, 0.1]}, {"Target": "Parameter", "Id": "Param7", "Segments": [0, -0.015, 2, 0.033, -0.015, 0, 0.133, 0.047, 0, 0.333, -0.309, 0, 0.5, 0.329, 0, 0.667, 0.02, 0, 0.867, 0.207, 0, 1.2, -0.29, 0, 1.6, 0.118, 0, 1.967, -0.113, 0, 2.467, 0.102, 0, 2.867, 0, 0, 3.267, 0.062, 0, 3.733, -0.016, 0, 3.833, -0.008, 0, 4.167, -0.187, 0, 4.533, 0.288, 0, 5.033, -0.236, 0, 5.767, 0.246, 0, 6.267, -0.677, 0, 6.633, 0.558, 0, 7.033, -0.337, 0, 7.433, 0.2, 0, 7.867, -0.106, 0, 8.3, 0.082, 0, 8.933, -0.031, 0, 9.1, 0.053, 0, 9.4, -0.46, 0, 9.733, 0.735, 0, 10.133, -0.732, 0, 10.567, 0.57, 0, 11, -0.322, 1, 11.111, -0.322, 11.222, 0.049, 11.333, 0.172]}, {"Target": "Parameter", "Id": "Param8", "Segments": [0, 0.022, 1, 0.078, 0.053, 0.155, 0.104, 0.233, 0.104, 0, 0.433, -0.327, 0, 0.667, 0.229, 0, 0.867, -0.006, 0, 1.033, 0.117, 0, 1.333, -0.243, 0, 1.667, 0.205, 0, 2.033, -0.136, 0, 2.467, 0.067, 0, 2.933, -0.044, 0, 3.333, 0.035, 0, 3.733, -0.014, 0, 4, 0.049, 0, 4.3, -0.174, 0, 4.667, 0.245, 0, 5.067, -0.167, 0, 5.5, 0.051, 0, 5.667, 0.019, 0, 6.033, 0.153, 0, 6.4, -0.542, 0, 6.733, 0.578, 0, 7.1, -0.4, 0, 7.5, 0.242, 0, 7.9, -0.137, 0, 8.333, 0.078, 0, 8.7, -0.035, 0, 9.233, 0.157, 0, 9.533, -0.453, 0, 9.867, 0.679, 0, 10.233, -0.624, 0, 10.667, 0.505, 0, 11.067, -0.369, 1, 11.156, -0.369, 11.244, -0.093, 11.333, 0.091]}, {"Target": "Parameter", "Id": "Param9", "Segments": [0, 0.006, 1, 0.1, 0.027, 0.2, 0.085, 0.3, 0.085, 0, 0.533, -0.292, 0, 0.8, 0.298, 0, 1.033, -0.005, 0, 1.167, 0.027, 0, 1.433, -0.275, 0, 1.767, 0.321, 0, 2.133, -0.252, 0, 2.5, 0.14, 0, 2.967, -0.065, 0, 3.4, 0.053, 0, 3.8, -0.028, 0, 4.067, 0.058, 0, 4.4, -0.216, 0, 4.733, 0.345, 0, 5.133, -0.276, 0, 5.533, 0.106, 0, 5.8, 0.005, 0, 6.1, 0.179, 0, 6.467, -0.609, 0, 6.833, 0.766, 0, 7.2, -0.618, 0, 7.567, 0.415, 0, 7.967, -0.253, 0, 8.367, 0.14, 0, 8.767, -0.071, 0, 9.333, 0.159, 0, 9.6, -0.524, 0, 9.933, 0.832, 0, 10.3, -0.813, 0, 10.733, 0.669, 0, 11.133, -0.538, 1, 11.2, -0.538, 11.266, -0.302, 11.333, -0.066]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 1, 1, 2, 3.667, 1, 2, 8.533, 1, 2, 10.233, 1, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.5, 0, 2, 0.9, 0, 0, 0.933, -1, 0, 1.433, 0, 0, 2, -0.622, 2, 3.667, -0.622, 2, 8.533, -0.622, 0, 9.4, -1, 2, 10.233, -1, 0, 10.4, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.5, 0, 2, 0.9, 0, 0, 0.933, -1, 0, 2, 1, 2, 3.667, 1, 2, 8.533, 1, 1, 8.711, 1, 8.889, 1.049, 9.067, 0.8, 1, 9.4, 0.334, 9.734, -0.5, 10.067, -0.5, 2, 10.233, -0.5, 0, 10.4, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.5, 0, 1, 0.722, 0, 0.945, -0.133, 1.167, -0.4, 1, 1.278, -0.533, 1.389, -0.6, 1.5, -0.6, 0, 2, 0.4, 2, 3.667, 0.4, 2, 8.533, 0.4, 0, 9.267, 1, 2, 9.933, 1, 0, 10.233, -1, 0, 10.4, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 1, 0, 1.167, -1, 0, 1.5, 1, 0, 2, -1, 2, 3.667, -1, 2, 8.533, -1, 0, 11.333, 1]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 1.033, -1, 2, 3.667, -1, 2, 8.533, -1, 2, 10.133, -1, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "xingfengyaobai", "Segments": [0, 0, 0, 1.5, -1, 0, 2.333, 1, 0, 2.767, -1, 0, 3.1, 0.501, 0, 3.4, -0.3, 0, 3.567, 0, 2, 3.667, 0, 2, 8.533, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 1, 0.133, 0, 0.267, 0.378, 0.4, 0.5, 1, 0.544, 0.632, 0.689, 0.637, 0.833, 0.75, 1, 0.922, 0.82, 1.011, 1, 1.1, 1, 2, 1.933, 1, 0, 3.667, 1.5, 2, 8.533, 1.5, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 1, 0.133, 0, 0.267, 0.378, 0.4, 0.5, 1, 0.544, 0.632, 0.689, 0.652, 0.833, 0.75, 1, 0.922, 0.81, 1.011, 0.9, 1.1, 1, 1, 1.378, 1.312, 1.655, 1.5, 1.933, 1.5, 2, 3.667, 1.5, 2, 8.533, 1.5, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 1, 0.367, 0.152, 0.733, 0.174, 1.1, 0.174, 2, 1.933, 0.174, 2, 3.667, 0.174, 2, 8.533, 0.174, 0, 9.933, 0.197, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON>biji<PERSON><PERSON>", "Segments": [0, 0, 0, 0.4, 0.083, 1, 0.633, 0.083, 0.867, 0.037, 1.1, -0.062, 1, 1.211, -0.109, 1.322, -0.136, 1.433, -0.136, 1, 1.533, -0.136, 1.633, -0.142, 1.733, 0, 1, 1.8, 0.095, 1.866, 1, 1.933, 1, 2, 3.667, 1, 2, 8.533, 1, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 1, 0.133, 0, 0.267, 0.009, 0.4, -0.086, 1, 0.511, -0.166, 0.622, -0.68, 0.733, -0.68, 0, 1.1, -0.396, 0, 1.933, -1, 2, 3.667, -1, 2, 8.533, -1, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.4, 0, 1, 0.511, 0, 0.622, -0.326, 0.733, -0.385, 1, 0.855, -0.45, 0.978, -0.498, 1.1, -0.5, 1, 1.378, -0.506, 1.655, -0.506, 1.933, -0.506, 0, 2.5, -0.426, 2, 3.667, -0.426, 2, 8.533, -0.426, 0, 9.967, -0.7, 1, 10.089, -0.7, 10.211, -0.207, 10.333, -0.157, 1, 10.666, -0.019, 11, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "xiongshangxai", "Segments": [0, -0.87, 1, 0.056, -0.883, 0.111, -0.89, 0.167, -0.89, 0, 0.333, 1, 2, 0.367, 1, 0, 0.467, 0.18, 0, 0.567, 0.245, 0, 0.8, -0.437, 0, 1.033, 0.033, 0, 1.267, -0.135, 0, 1.5, -0.071, 0, 1.533, -0.073, 0, 1.567, -0.072, 0, 1.6, -0.074, 0, 1.633, -0.072, 2, 1.667, -0.072, 0, 2.333, 0.067, 0, 2.5, 0.046, 0, 2.733, 0.059, 0, 3.733, -0.034, 0, 3.933, 0.011, 0, 4.167, -0.004, 0, 4.4, 0.001, 0, 4.633, 0, 2, 4.833, 0, 2, 5.067, 0, 2, 5.3, 0, 2, 5.367, 0, 0, 5.6, 0.16, 0, 6.133, -0.097, 0, 6.367, 0.012, 0, 6.6, -0.028, 0, 6.8, -0.019, 0, 7.033, -0.024, 0, 7.1, -0.023, 2, 7.133, -0.023, 2, 7.167, -0.023, 2, 7.2, -0.023, 0, 7.267, -0.022, 2, 7.3, -0.022, 0, 7.367, -0.021, 0, 7.4, -0.022, 0, 7.467, -0.02, 0, 7.5, -0.021, 0, 7.533, -0.02, 2, 7.567, -0.02, 0, 8.6, 0.01, 0, 8.867, -0.12, 0, 9.167, 0.108, 0, 9.367, 0, 0, 9.567, 0.027, 0, 10.133, -0.116, 0, 10.4, 0.104, 0, 10.633, -0.02, 0, 10.867, 0.019, 1, 11.022, 0.019, 11.178, -0.003, 11.333, -0.009]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, -0.435, 1, 0.056, -0.441, 0.111, -0.445, 0.167, -0.445, 0, 0.367, 0.54, 0, 0.467, 0.09, 0, 0.567, 0.122, 0, 0.8, -0.218, 0, 1.033, 0.016, 0, 1.267, -0.067, 0, 1.5, -0.036, 0, 1.533, -0.037, 0, 1.567, -0.036, 0, 1.6, -0.037, 0, 1.633, -0.036, 2, 1.667, -0.036, 0, 2.333, 0.033, 0, 2.5, 0.023, 0, 2.733, 0.029, 0, 3.733, -0.017, 0, 3.933, 0.005, 0, 4.167, -0.002, 0, 4.4, 0.001, 0, 4.633, 0, 2, 4.833, 0, 2, 5.067, 0, 2, 5.3, 0, 2, 5.367, 0, 0, 5.6, 0.08, 0, 6.133, -0.048, 0, 6.367, 0.006, 0, 6.6, -0.014, 0, 6.8, -0.009, 0, 7.033, -0.012, 2, 7.1, -0.012, 2, 7.133, -0.012, 0, 7.167, -0.011, 2, 7.2, -0.011, 2, 7.267, -0.011, 2, 7.3, -0.011, 2, 7.367, -0.011, 2, 7.4, -0.011, 0, 7.467, -0.01, 2, 7.5, -0.01, 2, 7.533, -0.01, 2, 7.567, -0.01, 0, 8.6, 0.005, 0, 8.867, -0.06, 0, 9.167, 0.054, 0, 9.367, 0, 0, 9.567, 0.013, 0, 10.133, -0.058, 0, 10.4, 0.052, 0, 10.633, -0.01, 0, 10.867, 0.009, 1, 11.022, 0.009, 11.178, -0.002, 11.333, -0.004]}, {"Target": "Parameter", "Id": "op", "Segments": [0, 0, 0, 1.667, 0.8, 0, 2.467, 0.4, 2, 3.667, 0.4, 2, 8.533, 0.4, 2, 9.133, 0.4, 0, 10.167, 0.6, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 2, 0.167, 1, 0, 0.333, 0, 0, 0.667, 1, 2, 2.533, 1, 0, 2.7, 0, 0, 3.033, 1.09, 0, 3.667, 0.8, 0, 5.633, 1.162, 0, 5.833, 0, 0, 6.067, 0.731, 2, 7.1, 0.731, 0, 7.267, 0, 1, 7.334, 0, 7.4, 0.698, 7.467, 0.7, 1, 7.822, 0.712, 8.178, 0.71, 8.533, 0.731, 1, 8.966, 0.756, 9.4, 1, 9.833, 1, 0, 9.967, 0, 0, 10.2, 1, 2, 11.333, 1]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 2, 0.167, 1, 0, 0.333, 0, 0, 0.667, 1, 2, 2.533, 1, 0, 2.7, 0, 0, 3.033, 1.09, 0, 3.667, 0.8, 0, 5.633, 1.162, 0, 5.833, 0, 0, 6.067, 0.731, 2, 7.1, 0.731, 0, 7.267, 0, 0, 7.567, 0.785, 0, 8.533, 0.731, 0, 9.833, 1, 0, 9.967, 0, 0, 10.2, 1, 2, 11.333, 1]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 0, 0.867, -0.6, 0, 2, 1, 0, 2.5, 0, 0, 3.9, 1, 2, 4.067, 1, 2, 6.1, 1, 2, 9.233, 1, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 0, 0.867, -1, 0, 2, 0.654, 2, 2.633, 0.654, 0, 2.7, -1, 2, 3.033, -1, 2, 4.067, -1, 2, 5.6, -1, 0, 6.1, 0, 2, 8.533, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON>kongda<PERSON><PERSON>", "Segments": [0, 0, 2, 0.167, 0, 0, 0.333, 1, 0, 0.667, -1, 0, 0.833, 0.5, 0, 0.967, -0.2, 0, 1.1, 0, 2, 2.533, 0, 0, 2.7, 1, 0, 3.033, -1, 0, 3.2, 0.5, 0, 3.333, -0.2, 0, 3.467, 0, 2, 3.667, 0, 2, 8.533, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "gaoguangdaxia<PERSON>", "Segments": [0, 0, 2, 0.333, 0, 0, 0.567, 1, 0, 0.733, 0, 0, 0.967, 0.9, 0, 1.167, 0, 0, 1.3, 0.4, 0, 1.433, 0, 2, 2.7, 0, 0, 2.933, 1, 0, 3.1, 0, 0, 3.333, 0.9, 0, 3.533, 0, 2, 3.667, 0, 2, 8.533, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, -1, 0, 1, 0, 2, 5.533, 0, 0, 6.167, -1, 2, 7.1, -1, 1, 7.267, -1, 7.433, -0.302, 7.6, -0.2, 1, 7.911, -0.009, 8.222, 0, 8.533, 0, 0, 11.333, -1]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, -1, 0, 1, 0, 2, 5.533, 0, 0, 6.167, -1, 2, 7.1, -1, 1, 7.267, -1, 7.433, -0.302, 7.6, -0.2, 1, 7.911, -0.009, 8.222, 0, 8.533, 0, 0, 11.333, -1]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 0, 0, 1, -1, 2, 1.833, -1, 0, 2.467, 1, 2, 3.667, 1, 2, 8.533, 1, 2, 10.467, 1, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 0, 0.867, 35.709, 1, 1.134, 35.709, 1.4, 34.961, 1.667, 29.362, 1, 1.811, 26.33, 1.956, 16.715, 2.1, 0, 1, 2.311, -24.43, 2.522, -84, 2.733, -84, 0, 3.267, 0, 0, 3.867, -47.74, 0, 4.4, 0, 2, 5.467, 0, 0, 6, 54, 2, 7.467, 54, 0, 8.533, 0, 0, 9, 21, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 0, 0.933, -90, 0, 1.9, 90, 0, 2.5, -90, 2, 3.667, -90, 1, 5.289, -90, 6.911, -62.753, 8.533, 0, 1, 8.644, 4.298, 8.756, 33, 8.867, 33, 0, 9.2, -51, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, -30, 2, 3.667, -30, 0, 6, -7, 2, 7.467, -7, 0, 8.533, -30, 0, 9.567, 1, 0, 11.333, -30]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 1, 0.067, 0.721, 0.133, 20, 0.2, 20, 0, 0.733, -20, 0, 2.233, 20, 0, 3.667, 0, 2, 5.333, 0, 0, 6.067, -16.923, 0, 8.533, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 2, 0.3, 0, 3.667, 0, 2, 8.533, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "shangshenyaobai", "Segments": [0, 0, 2, 0.667, 0, 0, 1.433, 17, 1, 1.622, 17, 1.811, 11.4, 2, 9, 1, 2.556, 1.94, 3.111, 0, 3.667, 0, 2, 8.533, 0, 0, 9.367, 24, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.222, -1.426, 0.445, -27, 0.667, -27, 0, 2, 30, 1, 2.678, 30, 3.355, 19.406, 4.033, 0, 1, 4.389, -10.18, 4.744, -14.46, 5.1, -14.46, 0, 8.533, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 0.167, -0.289, 0, 0.367, 0.416, 0, 0.5, -0.152, 0, 0.667, 0.175, 0, 0.867, -0.176, 0, 1.133, 0.013, 0, 1.367, -0.052, 0, 1.7, -0.019, 2, 1.733, -0.019, 0, 2.333, 0.032, 0, 2.533, 0.024, 0, 2.733, 0.028, 0, 2.767, 0.027, 0, 2.8, 0.028, 1, 3.089, 0.028, 3.378, 0.031, 3.667, -0.012, 1, 3.856, -0.04, 4.044, -1, 4.233, -1, 0, 6.767, 1, 1, 7.356, 1, 7.944, 0.191, 8.533, -0.012, 1, 8.555, -0.02, 8.578, -0.013, 8.6, -0.013, 0, 8.867, 0.003, 0, 9.133, -0.359, 0, 9.367, 0.282, 0, 9.667, -0.048, 0, 9.867, -0.003, 0, 10.167, -0.221, 0, 10.433, 0.198, 0, 10.767, -0.043, 0, 11.033, 0.028, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 1, -0.2, 0, 3.667, 0, 2, 8.533, 0, 0, 9.633, -0.3, 0, 11.333, 0]}, {"Target": "Model", "Id": "Opacity", "Segments": [0, 1, 0, 11.333, 1]}, {"Target": "Parameter", "Id": "ParamHairSide", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamHairBack", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "shi<PERSON>ez<PERSON>biaozuoyou", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Param11", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "tiqiehuan", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ZHITIyy", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "drzbxz", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "drybxz", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "tisousuo", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "dryoutui", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "renzuoyou", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "rens<PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "rendaxiao", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "jingshen", "Segments": [0, 0.5, 0, 11.333, 0.5]}, {"Target": "Parameter", "Id": "ZZZ", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "jingjingtmd", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "jingjingdaixioa", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "<PERSON>ng<PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "jingjingshangxai", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "b<PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Param10", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "baiguang<PERSON><PERSON>u", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "lizi1daxoao", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "lizi2daxiao", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "liziyi<PERSON>", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "liziqumian", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "lizizong<PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "jing<PERSON>u", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "andu", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "lanmu", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "heidi", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "bai", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON>ia<PERSON><PERSON>uo<PERSON>", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "zu<PERSON><PERSON><PERSON>ng", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "xingfengtmd", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "fizitmd", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 1, 0, 11.333, 1]}, {"Target": "Parameter", "Id": "xiongtuceng", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 1, 0, 11.333, 1]}, {"Target": "Parameter", "Id": "youxiabifa", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "kous<PERSON>dongkaibi", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "leimu", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "leidonghua", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "youleidonghua", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "leimudonghua", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "jingyaa", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamCheek", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "wu", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "zao1", "Segments": [0, 0.1, 0, 11.333, 0.1]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "<PERSON>banzhenmian", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "dibantmd", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "beijingtmd", "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "PartOpacity", "Id": "Part", "Segments": [0, 1, 0, 11.333, 1]}, {"Target": "PartOpacity", "Id": "Part24", "Segments": [0, 1, 0, 11.333, 1]}, {"Target": "PartOpacity", "Id": "Part38", "Segments": [0, 1, 0, 11.333, 1]}, {"Target": "PartOpacity", "Id": "Part3", "Segments": [0, 1, 0, 11.333, 1]}, {"Target": "PartOpacity", "Id": "toushipiaodai1_Skinning", "Segments": [0, 1, 0, 11.333, 1]}, {"Target": "PartOpacity", "Id": "toushipiaodai2_Skinning", "Segments": [0, 1, 0, 11.333, 1]}, {"Target": "PartOpacity", "Id": "Part2", "Segments": [0, 1, 0, 11.333, 1]}, {"Target": "PartOpacity", "Id": "Part18", "Segments": [0, 1, 0, 11.333, 1]}, {"Target": "PartOpacity", "Id": "toudaimao_Skinning", "Segments": [0, 1, 0, 11.333, 1]}, {"Target": "PartOpacity", "Id": "Part4", "Segments": [0, 1, 0, 11.333, 1]}, {"Target": "PartOpacity", "Id": "liuhaisi2_Skinning", "Segments": [0, 1, 0, 11.333, 1]}, {"Target": "PartOpacity", "Id": "Part17", "Segments": [0, 1, 0, 11.333, 1]}, {"Target": "PartOpacity", "Id": "Part22", "Segments": [0, 1, 0, 11.333, 1]}, {"Target": "PartOpacity", "Id": "Part21", "Segments": [0, 1, 0, 11.333, 1]}, {"Target": "PartOpacity", "Id": "cefatiao_Skinning2", "Segments": [0, 1, 0, 11.333, 1]}, {"Target": "PartOpacity", "Id": "Part5", "Segments": [0, 1, 0, 11.333, 1]}, {"Target": "PartOpacity", "Id": "Part6", "Segments": [0, 1, 0, 11.333, 1]}, {"Target": "PartOpacity", "Id": "Part7", "Segments": [0, 1, 0, 11.333, 1]}, {"Target": "PartOpacity", "Id": "Part45", "Segments": [0, 1, 0, 11.333, 1]}, {"Target": "PartOpacity", "Id": "Part26", "Segments": [0, 1, 0, 11.333, 1]}, {"Target": "PartOpacity", "Id": "Part8", "Segments": [0, 1, 0, 11.333, 1]}, {"Target": "PartOpacity", "Id": "Part56", "Segments": [0, 1, 0, 11.333, 1]}, {"Target": "PartOpacity", "Id": "Part27", "Segments": [0, 1, 0, 11.333, 1]}, {"Target": "PartOpacity", "Id": "Part30", "Segments": [0, 1, 0, 11.333, 1]}, {"Target": "PartOpacity", "Id": "Part37", "Segments": [0, 1, 0, 11.333, 1]}, {"Target": "PartOpacity", "Id": "Part31", "Segments": [0, 1, 0, 11.333, 1]}, {"Target": "PartOpacity", "Id": "Part9", "Segments": [0, 1, 0, 11.333, 1]}, {"Target": "PartOpacity", "Id": "Part39", "Segments": [0, 1, 0, 11.333, 1]}, {"Target": "PartOpacity", "Id": "houfatiao_Skinning", "Segments": [0, 1, 0, 11.333, 1]}, {"Target": "PartOpacity", "Id": "Part40", "Segments": [0, 1, 0, 11.333, 1]}, {"Target": "PartOpacity", "Id": "Part16", "Segments": [0, 1, 0, 11.333, 1]}, {"Target": "PartOpacity", "Id": "Part11", "Segments": [0, 1, 0, 11.333, 1]}, {"Target": "PartOpacity", "Id": "Part50", "Segments": [0, 1, 0, 11.333, 1]}, {"Target": "PartOpacity", "Id": "Part49", "Segments": [0, 1, 0, 11.333, 1]}, {"Target": "PartOpacity", "Id": "Part48", "Segments": [0, 1, 0, 11.333, 1]}, {"Target": "PartOpacity", "Id": "Part36", "Segments": [0, 1, 0, 11.333, 1]}, {"Target": "PartOpacity", "Id": "Part44", "Segments": [0, 1, 0, 11.333, 1]}, {"Target": "PartOpacity", "Id": "Part43", "Segments": [0, 1, 0, 11.333, 1]}, {"Target": "PartOpacity", "Id": "Part42", "Segments": [0, 1, 0, 11.333, 1]}, {"Target": "PartOpacity", "Id": "Part41", "Segments": [0, 1, 0, 11.333, 1]}, {"Target": "PartOpacity", "Id": "Part47", "Segments": [0, 1, 0, 11.333, 1]}, {"Target": "PartOpacity", "Id": "Part28", "Segments": [0, 1, 0, 11.333, 1]}, {"Target": "PartOpacity", "Id": "Part12", "Segments": [0, 1, 0, 11.333, 1]}, {"Target": "PartOpacity", "Id": "Part20", "Segments": [0, 1, 0, 11.333, 1]}, {"Target": "PartOpacity", "Id": "Part19", "Segments": [0, 1, 0, 11.333, 1]}, {"Target": "PartOpacity", "Id": "Part51", "Segments": [0, 1, 0, 11.333, 1]}, {"Target": "PartOpacity", "Id": "Part53", "Segments": [0, 1, 0, 11.333, 1]}, {"Target": "PartOpacity", "Id": "Part52", "Segments": [0, 1, 0, 11.333, 1]}, {"Target": "PartOpacity", "Id": "Part10", "Segments": [0, 1, 0, 11.333, 1]}, {"Target": "PartOpacity", "Id": "Part15", "Segments": [0, 1, 0, 11.333, 1]}, {"Target": "PartOpacity", "Id": "Part25", "Segments": [0, 1, 0, 11.333, 1]}, {"Target": "PartOpacity", "Id": "Part23", "Segments": [0, 1, 0, 11.333, 1]}, {"Target": "PartOpacity", "Id": "Part13", "Segments": [0, 1, 0, 11.333, 1]}, {"Target": "PartOpacity", "Id": "Part14", "Segments": [0, 1, 0, 11.333, 1]}, {"Target": "PartOpacity", "Id": "Part29", "Segments": [0, 0, 0, 11.333, 0]}], "UserData": [{"Time": 0.0, "Value": ""}, {"Time": 10.833, "Value": ""}]}