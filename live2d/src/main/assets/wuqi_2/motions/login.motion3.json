{"Version": 3, "Meta": {"Duration": 9.87, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 149, "TotalSegmentCount": 1601, "TotalPointCount": 1767, "UserDataCount": 2, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "wuli", "Segments": [0, 0, 2, 0.167, 0, 2, 7.167, 0, 0, 7.867, -1, 0, 8.033, 1, 0, 9.167, -1, 0, 9.867, 0]}, {"Target": "Parameter", "Id": "Para", "Segments": [0, 0, 2, 0.167, 0, 0, 4.6, 1, 0, 4.9, -1, 0, 6.167, 1, 0, 7.167, 0, 0, 8.1, 1, 0, 8.4, -1, 0, 8.767, 1, 0, 9.433, -1, 0, 9.867, 0]}, {"Target": "Parameter", "Id": "zuocefati1", "Segments": [0, 0.108, 2, 0.167, 0.108, 0, 0.233, 0.109, 0, 0.633, -0.104, 0, 0.867, 0, 2, 7.2, 0, 0, 7.533, 1.341, 0, 8.033, -10.6, 0, 8.4, 8.704, 0, 8.833, -4.698, 0, 9.233, 1.666, 0, 9.6, -1.984, 1, 9.689, -1.984, 9.778, -1.608, 9.867, 0]}, {"Target": "Parameter", "Id": "zuocefati2", "Segments": [0, -0.007, 2, 0.167, -0.007, 0, 0.5, 0.234, 0, 0.833, -0.202, 0, 1.067, 0.116, 0, 1.333, -0.098, 0, 1.467, 0, 2, 7.2, 0, 0, 7.333, -0.316, 0, 7.7, 2.754, 0, 7.9, 0.945, 0, 8, 2.633, 0, 8.2, -16.971, 0, 8.567, 13.881, 0, 8.967, -6.549, 0, 9.367, 3.703, 0, 9.733, -4.249, 1, 9.778, -4.249, 9.822, -1.428, 9.867, 0]}, {"Target": "Parameter", "Id": "zuocefati3", "Segments": [0, -0.015, 2, 0.167, -0.015, 0, 0.267, -0.035, 0, 0.4, 0.023, 0, 0.433, 0.021, 0, 0.467, 0.036, 0, 0.5, 0.008, 0, 0.667, 0.11, 0, 0.933, -0.24, 0, 1.233, 0.267, 0, 1.467, -0.22, 0, 1.733, 0.141, 0, 2, -0.119, 0, 2.267, 0.1, 0, 2.533, -0.084, 0, 2.667, 0, 2, 7.267, 0, 0, 7.5, -0.914, 0, 7.867, 1.987, 0, 7.933, 1.82, 0, 8.1, 6.724, 0, 8.3, -14.259, 0, 8.667, 13.364, 0, 9.033, -8.389, 0, 9.433, 5.612, 0, 9.833, -4.212, 1, 9.844, -4.212, 9.856, -0.138, 9.867, 0]}, {"Target": "Parameter", "Id": "zuocefati4", "Segments": [0, -0.081, 2, 0.167, -0.081, 0, 0.567, 0.097, 0, 0.667, 0.086, 0, 0.733, 0.089, 0, 1, -0.224, 0, 1.367, 0.354, 0, 1.633, -0.382, 0, 1.9, 0.381, 0, 2.2, -0.291, 0, 2.467, 0.249, 0, 2.667, -0.143, 0, 2.833, 0, 2, 7.333, 0, 0, 7.6, -1.021, 0, 8.167, 4.227, 0, 8.4, -15.135, 0, 8.733, 17.704, 0, 9.133, -12.761, 0, 9.5, 9.279, 1, 9.622, 9.279, 9.745, 6.592, 9.867, 0]}, {"Target": "Parameter", "Id": "zuocefati5", "Segments": [0, -0.009, 2, 0.167, -0.009, 0, 0.3, -0.082, 0, 0.7, 0.184, 0, 1.1, -0.338, 0, 1.433, 0.326, 0, 1.767, -0.565, 0, 2.033, 0.427, 0, 2.333, -0.396, 0, 2.6, 0.323, 0, 2.833, -0.227, 0, 3.1, 0.147, 0, 3.367, -0.124, 0, 3.633, 0.104, 0, 3.9, -0.088, 0, 4.033, 0, 2, 7.4, 0, 0, 7.667, -1.159, 0, 8.167, 3.903, 0, 8.433, -15.68, 0, 8.8, 21.332, 0, 9.2, -17.189, 0, 9.6, 13.691, 1, 9.689, 13.691, 9.778, 10.587, 9.867, 0]}, {"Target": "Parameter", "Id": "fashipiaodai1", "Segments": [0, 0.082, 2, 0.167, 0.082, 2, 0.233, 0.082, 0, 0.633, -0.078, 0, 1.033, 0.079, 0, 1.433, -0.08, 0, 1.833, 0.081, 0, 2.233, -0.082, 0, 2.633, 0.083, 0, 3.033, -0.084, 0, 3.433, 0.085, 0, 3.833, -0.086, 0, 4.233, 0.087, 0, 4.633, -0.088, 0, 5.033, 0.089, 0, 5.433, -0.09, 0, 5.833, 0.091, 0, 6.233, -0.092, 0, 6.633, 0.093, 0, 7.033, -0.095, 0, 7.533, 2.341, 0, 8.033, -17.958, 0, 8.4, 13.688, 0, 8.833, -7.236, 0, 9.233, 2.241, 0, 9.6, -3.004, 1, 9.689, -3.004, 9.778, -2.448, 9.867, 0]}, {"Target": "Parameter", "Id": "fashipiaodai2", "Segments": [0, -0.005, 2, 0.167, -0.005, 0, 0.5, 0.176, 0, 0.867, -0.205, 0, 1.167, 0.158, 0, 1.2, 0.157, 0, 1.267, 0.187, 0, 1.667, -0.198, 0, 2.067, 0.197, 0, 2.467, -0.201, 0, 2.867, 0.202, 0, 3.267, -0.205, 0, 3.667, 0.207, 0, 4.067, -0.21, 0, 4.467, 0.212, 0, 4.867, -0.215, 0, 5.267, 0.217, 0, 5.667, -0.22, 0, 6.067, 0.222, 0, 6.467, -0.225, 0, 6.867, 0.228, 0, 7.333, -0.529, 0, 7.7, 4.778, 0, 7.9, 1.659, 0, 7.967, 3.926, 0, 8.167, -29.641, 0, 8.567, 20.68, 0, 8.967, -9.283, 0, 9.367, 5.283, 0, 9.767, -6.65, 1, 9.8, -6.65, 9.834, -1.672, 9.867, 0]}, {"Target": "Parameter", "Id": "fashipiaodai3", "Segments": [0, -0.011, 2, 0.167, -0.011, 0, 0.267, -0.026, 0, 0.4, 0.017, 0, 0.433, 0.016, 0, 0.467, 0.027, 0, 0.567, -0.057, 0, 0.733, 0.013, 0, 1, -0.213, 0, 1.333, 0.16, 0, 1.767, -0.169, 0, 2.133, 0.162, 0, 2.567, -0.167, 0, 2.967, 0.167, 0, 3.367, -0.17, 0, 3.767, 0.171, 0, 4.167, -0.173, 0, 4.567, 0.175, 0, 4.967, -0.177, 0, 5.367, 0.179, 0, 5.767, -0.182, 0, 6.167, 0.184, 0, 6.567, -0.186, 0, 6.967, 0.188, 0, 7.5, -1.496, 0, 7.867, 3.407, 0, 7.933, 3.105, 0, 8.1, 12.729, 0, 8.3, -22.539, 0, 8.667, 18.483, 0, 9.067, -11.1, 0, 9.467, 7.958, 0, 9.833, -6.48, 1, 9.844, -6.48, 9.856, -0.181, 9.867, 0]}, {"Target": "Parameter", "Id": "fashipiaodai4", "Segments": [0, 0.004, 2, 0.167, 0.004, 0, 0.367, -0.025, 0, 0.633, 0.276, 0, 1.1, -0.177, 0, 1.4, 0.309, 0, 1.733, -0.239, 0, 1.8, -0.23, 0, 1.833, -0.232, 0, 2.2, 0.274, 0, 2.6, -0.257, 0, 3, 0.27, 0, 3.4, -0.268, 0, 3.8, 0.274, 0, 4.2, -0.276, 0, 4.6, 0.28, 0, 5, -0.283, 0, 5.4, 0.286, 0, 5.8, -0.29, 0, 6.2, 0.293, 0, 6.6, -0.296, 0, 7, 0.3, 0, 7.567, -1.599, 0, 7.933, 4.447, 0, 7.967, 4.427, 0, 8.033, 4.623, 0, 8.067, 4.414, 0, 8.167, 6.35, 0, 8.367, -22.663, 0, 8.733, 23.613, 0, 9.133, -16.371, 0, 9.533, 12.575, 1, 9.644, 12.575, 9.756, 9.007, 9.867, 0]}, {"Target": "Parameter", "Id": "fashipiaodai5", "Segments": [0, -0.009, 2, 0.167, -0.009, 0, 0.267, -0.004, 0, 0.467, -0.02, 0, 0.5, -0.016, 0, 0.533, -0.043, 0, 0.567, -0.035, 0, 0.6, -0.057, 0, 0.8, 0.186, 0, 1.1, -0.261, 0, 1.5, 0.509, 0, 1.833, -0.43, 0, 2.3, 0.411, 0, 2.667, -0.411, 0, 3.067, 0.416, 0, 3.467, -0.421, 0, 3.867, 0.426, 0, 4.267, -0.431, 0, 4.667, 0.436, 0, 5.067, -0.441, 0, 5.467, 0.446, 0, 5.867, -0.452, 0, 6.267, 0.457, 0, 6.667, -0.462, 0, 7.067, 0.468, 0, 7.667, -1.736, 0, 8.033, 5.403, 0, 8.133, 5.114, 0, 8.167, 5.148, 0, 8.433, -22.289, 0, 8.8, 27.84, 0, 9.2, -21.333, 0, 9.6, 17.873, 1, 9.689, 17.873, 9.778, 13.796, 9.867, 0]}, {"Target": "Parameter", "Id": "fashipiaodai6", "Segments": [0, -0.035, 2, 0.167, -0.035, 0, 0.433, -0.001, 0, 0.567, -0.02, 0, 0.9, 0.197, 0, 1.2, -0.481, 0, 1.6, 0.63, 0, 1.933, -0.807, 0, 2.3, 0.614, 0, 2.733, -0.648, 0, 3.133, 0.629, 0, 3.533, -0.654, 0, 3.933, 0.651, 0, 4.333, -0.664, 0, 4.733, 0.669, 0, 5.133, -0.679, 0, 5.533, 0.686, 0, 5.933, -0.694, 0, 6.333, 0.702, 0, 6.733, -0.711, 0, 7.133, 0.719, 0, 7.7, -1.886, 0, 8.1, 7.114, 0, 8.467, -23.369, 0, 8.833, 30, 2, 8.867, 30, 0, 9.267, -25.829, 0, 9.667, 22.812, 1, 9.734, 22.812, 9.8, 14.554, 9.867, 0]}, {"Target": "Parameter", "Id": "daimao1", "Segments": [0, 0.136, 2, 0.167, 0.136, 2, 0.233, 0.136, 0, 0.633, -0.13, 0, 0.867, 0, 2, 7.2, 0, 0, 7.533, 1.677, 0, 8.033, -13.25, 0, 8.4, 10.88, 0, 8.833, -5.873, 0, 9.233, 2.083, 0, 9.6, -2.48, 1, 9.689, -2.48, 9.778, -2.005, 9.867, 0]}, {"Target": "Parameter", "Id": "daimao2", "Segments": [0, -0.008, 2, 0.167, -0.008, 0, 0.5, 0.292, 0, 0.833, -0.252, 0, 1.067, 0.145, 0, 1.333, -0.122, 0, 1.467, 0, 2, 7.2, 0, 0, 7.333, -0.395, 0, 7.7, 3.442, 0, 7.9, 1.181, 0, 8, 3.291, 0, 8.2, -21.214, 0, 8.567, 17.351, 0, 8.967, -8.186, 0, 9.367, 4.629, 0, 9.733, -5.311, 1, 9.778, -5.311, 9.822, -1.783, 9.867, 0]}, {"Target": "Parameter", "Id": "daimao3", "Segments": [0, -0.019, 2, 0.167, -0.019, 0, 0.267, -0.044, 0, 0.4, 0.028, 0, 0.433, 0.027, 0, 0.467, 0.045, 0, 0.5, 0.011, 0, 0.667, 0.138, 0, 0.933, -0.3, 0, 1.233, 0.333, 0, 1.467, -0.275, 0, 1.733, 0.177, 0, 2, -0.149, 0, 2.267, 0.125, 0, 2.533, -0.105, 0, 2.667, 0, 2, 7.267, 0, 0, 7.5, -1.142, 0, 7.867, 2.483, 0, 7.933, 2.274, 0, 8.1, 8.405, 0, 8.3, -17.824, 0, 8.667, 16.705, 0, 9.033, -10.486, 0, 9.433, 7.015, 0, 9.833, -5.264, 1, 9.844, -5.264, 9.856, -0.172, 9.867, 0]}, {"Target": "Parameter", "Id": "daimao4", "Segments": [0, -0.101, 2, 0.167, -0.101, 0, 0.567, 0.121, 0, 0.667, 0.107, 0, 0.733, 0.111, 0, 1, -0.279, 0, 1.367, 0.443, 0, 1.633, -0.478, 0, 1.9, 0.476, 0, 2.2, -0.363, 0, 2.467, 0.311, 0, 2.667, -0.179, 0, 2.833, 0, 2, 7.333, 0, 0, 7.6, -1.276, 0, 8.167, 5.284, 0, 8.4, -18.918, 0, 8.733, 22.13, 0, 9.133, -15.951, 0, 9.5, 11.599, 1, 9.622, 11.599, 9.745, 8.249, 9.867, 0]}, {"Target": "Parameter", "Id": "Param", "Segments": [0, 0.13, 2, 0.167, 0.13, 2, 0.233, 0.13, 0, 0.667, -0.045, 0, 1.067, 0.046, 0, 1.467, -0.046, 0, 1.867, 0.047, 0, 2.267, -0.047, 0, 2.667, 0.048, 0, 3.067, -0.048, 0, 3.467, 0.049, 0, 3.867, -0.049, 0, 4.267, 0.05, 0, 4.667, -0.051, 0, 5.067, 0.051, 0, 5.467, -0.052, 0, 5.867, 0.052, 0, 6.267, -0.053, 0, 6.667, 0.054, 0, 7.067, -0.054, 0, 7.533, 1.08, 0, 8.033, -8.598, 0, 8.4, 7.274, 0, 8.833, -3.964, 0, 9.233, 1.475, 0, 9.6, -1.687, 1, 9.689, -1.687, 9.778, -1.364, 9.867, 0]}, {"Target": "Parameter", "Id": "Param2", "Segments": [0, -0.034, 2, 0.167, -0.034, 0, 0.433, 0.168, 0, 0.667, -0.112, 0, 0.833, -0.055, 0, 0.867, -0.061, 0, 1.267, 0.088, 0, 1.567, -0.075, 0, 1.633, -0.064, 0, 1.667, -0.077, 0, 2, 0.071, 0, 2.033, 0.068, 0, 2.067, 0.083, 0, 2.367, -0.074, 0, 2.433, -0.067, 0, 2.467, -0.082, 0, 2.767, 0.074, 0, 2.833, 0.069, 0, 2.867, 0.084, 0, 3.167, -0.075, 0, 3.233, -0.069, 0, 3.267, -0.084, 0, 3.567, 0.076, 0, 3.633, 0.07, 0, 3.667, 0.086, 0, 3.967, -0.077, 0, 4.033, -0.071, 0, 4.067, -0.087, 0, 4.367, 0.078, 0, 4.433, 0.072, 0, 4.467, 0.088, 0, 4.767, -0.079, 0, 4.833, -0.073, 0, 4.867, -0.089, 0, 5.167, 0.08, 0, 5.233, 0.074, 0, 5.267, 0.09, 0, 5.567, -0.081, 0, 5.633, -0.074, 0, 5.667, -0.091, 0, 5.967, 0.082, 0, 6.033, 0.075, 0, 6.067, 0.092, 0, 6.367, -0.082, 0, 6.433, -0.076, 0, 6.467, -0.093, 0, 6.767, 0.083, 0, 6.833, 0.077, 0, 6.867, 0.094, 0, 7.167, -0.084, 0, 7.2, -0.083, 0, 7.333, -0.247, 0, 7.7, 2.212, 0, 7.9, 0.776, 0, 8, 2.304, 0, 8.2, -13.943, 0, 8.533, 12.059, 0, 8.967, -5.77, 0, 9.333, 3.268, 0, 9.733, -3.572, 1, 9.778, -3.572, 9.822, -1.195, 9.867, 0]}, {"Target": "Parameter", "Id": "Param3", "Segments": [0, -0.019, 2, 0.167, -0.019, 0, 0.267, -0.044, 0, 0.533, 0.217, 0, 0.833, -0.174, 0, 1.233, 0.068, 0, 1.267, 0.053, 0, 1.367, 0.183, 0, 1.633, -0.113, 0, 1.667, -0.111, 0, 1.733, -0.167, 0, 2.033, 0.108, 0, 2.067, 0.096, 0, 2.167, 0.17, 0, 2.433, -0.112, 0, 2.467, -0.104, 0, 2.533, -0.169, 0, 2.833, 0.112, 0, 2.867, 0.102, 0, 2.967, 0.17, 0, 3.233, -0.114, 0, 3.267, -0.105, 0, 3.333, -0.173, 0, 3.633, 0.115, 0, 3.667, 0.105, 0, 3.733, 0.174, 0, 4.033, -0.117, 0, 4.067, -0.107, 0, 4.133, -0.177, 0, 4.433, 0.118, 0, 4.467, 0.108, 0, 4.533, 0.179, 0, 4.833, -0.12, 0, 4.867, -0.109, 0, 4.933, -0.181, 0, 5.233, 0.121, 0, 5.267, 0.111, 0, 5.333, 0.183, 0, 5.633, -0.122, 0, 5.667, -0.112, 0, 5.733, -0.185, 0, 6.033, 0.124, 0, 6.067, 0.113, 0, 6.133, 0.187, 0, 6.433, -0.125, 0, 6.467, -0.115, 0, 6.533, -0.189, 0, 6.833, 0.127, 0, 6.867, 0.116, 0, 6.933, 0.192, 0, 7.5, -0.628, 0, 7.867, 1.575, 0, 7.933, 1.473, 0, 8.1, 5.227, 0, 8.333, -12.215, 0, 8.667, 12.117, 0, 9.033, -7.801, 0, 9.433, 4.968, 0, 9.833, -3.556, 1, 9.844, -3.556, 9.856, -0.125, 9.867, 0]}, {"Target": "Parameter", "Id": "Param4", "Segments": [0, -0.089, 2, 0.167, -0.089, 0, 0.633, 0.149, 0, 0.933, -0.227, 0, 1.267, 0.143, 0, 1.4, 0.05, 0, 1.5, 0.069, 0, 1.833, -0.185, 0, 2.233, 0.153, 0, 2.633, -0.17, 0, 3.033, 0.165, 0, 3.433, -0.17, 0, 3.833, 0.171, 0, 4.233, -0.174, 0, 4.633, 0.175, 0, 5.033, -0.178, 0, 5.433, 0.18, 0, 5.833, -0.182, 0, 6.233, 0.184, 0, 6.633, -0.186, 0, 7.033, 0.188, 0, 7.567, -0.849, 0, 8.167, 3.658, 0, 8.4, -13.386, 0, 8.733, 16.29, 0, 9.133, -12.146, 0, 9.5, 8.629, 0, 9.867, 0]}, {"Target": "Parameter", "Id": "Param5", "Segments": [0, -0.009, 2, 0.167, -0.009, 0, 0.3, -0.094, 0, 0.7, 0.304, 0, 1.033, -0.357, 0, 1.367, 0.273, 0, 1.933, -0.256, 0, 2.267, 0.285, 0, 2.7, -0.252, 0, 3.067, 0.267, 0, 3.467, -0.264, 0, 3.867, 0.27, 0, 4.267, -0.272, 0, 4.667, 0.276, 0, 5.067, -0.279, 0, 5.467, 0.282, 0, 5.867, -0.285, 0, 6.267, 0.289, 0, 6.667, -0.292, 0, 7.067, 0.295, 0, 7.667, -0.731, 0, 8.2, 3.428, 0, 8.467, -14.187, 0, 8.833, 19.743, 0, 9.2, -16.764, 0, 9.567, 13.015, 1, 9.667, 13.015, 9.767, 10.006, 9.867, 0]}, {"Target": "Parameter", "Id": "ZUOHOUFABG1", "Segments": [0, 0.097, 2, 0.167, 0.097, 0, 0.233, 0.098, 0, 0.667, -0.034, 0, 1.067, 0.034, 0, 1.467, -0.035, 0, 1.867, 0.035, 0, 2.267, -0.035, 0, 2.667, 0.036, 0, 3.067, -0.036, 0, 3.467, 0.037, 0, 3.867, -0.037, 0, 4.267, 0.038, 0, 4.667, -0.038, 0, 5.067, 0.038, 0, 5.467, -0.039, 0, 5.867, 0.039, 0, 6.267, -0.04, 0, 6.667, 0.04, 0, 7.067, -0.041, 0, 7.533, 0.81, 0, 8.033, -6.448, 0, 8.4, 5.455, 0, 8.833, -2.973, 0, 9.233, 1.106, 0, 9.6, -1.265, 1, 9.689, -1.265, 9.778, -1.026, 9.867, 0]}, {"Target": "Parameter", "Id": "ZUOHOUFABG2", "Segments": [0, -0.025, 2, 0.167, -0.025, 0, 0.433, 0.126, 0, 0.667, -0.084, 0, 0.833, -0.042, 0, 0.867, -0.045, 0, 1.267, 0.066, 0, 1.567, -0.056, 0, 1.633, -0.048, 0, 1.667, -0.058, 0, 2, 0.054, 0, 2.033, 0.051, 0, 2.067, 0.062, 0, 2.367, -0.056, 0, 2.433, -0.051, 0, 2.467, -0.061, 0, 2.767, 0.055, 0, 2.833, 0.052, 0, 2.867, 0.063, 0, 3.167, -0.056, 0, 3.233, -0.052, 0, 3.267, -0.063, 0, 3.567, 0.057, 0, 3.633, 0.053, 0, 3.667, 0.064, 0, 3.967, -0.058, 0, 4.033, -0.053, 0, 4.067, -0.065, 0, 4.367, 0.058, 0, 4.433, 0.054, 0, 4.467, 0.066, 0, 4.767, -0.059, 0, 4.833, -0.054, 0, 4.867, -0.066, 0, 5.167, 0.06, 0, 5.233, 0.055, 0, 5.267, 0.067, 0, 5.567, -0.06, 0, 5.633, -0.056, 0, 5.667, -0.068, 0, 5.967, 0.061, 0, 6.033, 0.056, 0, 6.067, 0.069, 0, 6.367, -0.062, 0, 6.433, -0.057, 0, 6.467, -0.07, 0, 6.767, 0.063, 0, 6.833, 0.058, 0, 6.867, 0.07, 0, 7.167, -0.063, 2, 7.2, -0.063, 0, 7.333, -0.185, 0, 7.7, 1.659, 0, 7.9, 0.582, 0, 8, 1.728, 0, 8.2, -10.457, 0, 8.533, 9.044, 0, 8.967, -4.328, 0, 9.333, 2.451, 0, 9.733, -2.679, 1, 9.778, -2.679, 9.822, -0.9, 9.867, 0]}, {"Target": "Parameter", "Id": "ZUOHOUFABG3", "Segments": [0, -0.014, 2, 0.167, -0.014, 0, 0.267, -0.033, 0, 0.533, 0.163, 0, 0.833, -0.131, 0, 1.233, 0.051, 0, 1.267, 0.04, 0, 1.367, 0.137, 0, 1.633, -0.085, 0, 1.667, -0.083, 0, 1.733, -0.125, 0, 2.033, 0.081, 0, 2.067, 0.072, 0, 2.167, 0.127, 0, 2.433, -0.084, 0, 2.467, -0.078, 0, 2.533, -0.127, 0, 2.833, 0.084, 0, 2.867, 0.077, 0, 2.967, 0.128, 0, 3.233, -0.086, 0, 3.267, -0.079, 0, 3.333, -0.129, 0, 3.633, 0.087, 0, 3.667, 0.079, 0, 3.733, 0.131, 0, 4.033, -0.088, 0, 4.067, -0.08, 0, 4.133, -0.132, 0, 4.433, 0.089, 0, 4.467, 0.081, 0, 4.533, 0.134, 0, 4.833, -0.09, 0, 4.867, -0.082, 0, 4.933, -0.136, 0, 5.233, 0.091, 0, 5.267, 0.083, 0, 5.333, 0.137, 0, 5.633, -0.092, 0, 5.667, -0.084, 0, 5.733, -0.139, 0, 6.033, 0.093, 0, 6.067, 0.085, 0, 6.133, 0.14, 0, 6.433, -0.094, 0, 6.467, -0.086, 0, 6.533, -0.142, 0, 6.833, 0.095, 0, 6.867, 0.087, 0, 6.933, 0.144, 0, 7.5, -0.471, 0, 7.867, 1.181, 0, 7.933, 1.105, 0, 8.1, 3.92, 0, 8.333, -9.161, 0, 8.667, 9.088, 0, 9.033, -5.851, 0, 9.433, 3.726, 0, 9.833, -2.667, 1, 9.844, -2.667, 9.856, -0.094, 9.867, 0]}, {"Target": "Parameter", "Id": "hounaofa1yaobai", "Segments": [0, 0, 2, 0.167, 0, 2, 0.2, 0, 2, 7.2, 0, 0, 7.4, 0.021, 0, 8.033, -0.186, 0, 8.2, 0.132, 0, 8.467, -0.029, 0, 8.733, 0.02, 0, 9.4, -0.016, 1, 9.556, -0.016, 9.711, -0.012, 9.867, 0]}, {"Target": "Parameter", "Id": "hounaifa1bianxing", "Segments": [0, 0.011, 2, 0.167, 0.011, 0, 0.2, 0.012, 0, 0.333, 0, 2, 7.2, 0, 0, 7.333, -0.026, 0, 7.533, 0.025, 0, 7.8, -0.01, 0, 8, 0.204, 0, 8.133, -0.373, 0, 8.367, 0.305, 0, 8.6, -0.18, 0, 8.833, 0.09, 0, 9.1, -0.036, 0, 9.333, 0.018, 0, 9.567, -0.014, 0, 9.8, 0.002, 1, 9.822, 0.002, 9.845, 0.002, 9.867, 0]}, {"Target": "Parameter", "Id": "hounaofa2yaobai", "Segments": [0, 0.002, 2, 0.167, 0.002, 0, 0.2, 0, 2, 7.2, 0, 0, 7.4, 0.082, 0, 8.033, -0.746, 0, 8.2, 0.527, 0, 8.467, -0.118, 0, 8.733, 0.08, 0, 9.4, -0.063, 1, 9.556, -0.063, 9.711, -0.047, 9.867, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.167, 0, 2, 0.2, 0, 2, 7.2, 0, 0, 7.4, 0.021, 0, 8.033, -0.186, 0, 8.2, 0.132, 0, 8.467, -0.029, 0, 8.733, 0.02, 0, 9.4, -0.016, 1, 9.556, -0.016, 9.711, -0.012, 9.867, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0.004, 2, 0.167, 0.004, 2, 0.233, 0.004, 0, 0.4, 0, 2, 7.2, 0, 0, 7.467, 0.014, 0, 8.033, -0.117, 0, 8.267, 0.075, 0, 8.567, -0.014, 0, 8.867, 0.012, 0, 9.367, -0.012, 1, 9.534, -0.012, 9.7, -0.009, 9.867, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, -0.003, 2, 0.167, -0.003, 0, 0.4, 0.004, 0, 0.6, -0.004, 0, 0.867, 0.003, 0, 1.133, -0.003, 0, 1.4, 0.002, 0, 1.533, 0, 2, 7.2, 0, 0, 7.367, -0.007, 0, 7.633, 0.009, 0, 7.9, -0.002, 0, 8, 0.064, 0, 8.167, -0.11, 0, 8.433, 0.091, 0, 8.733, -0.053, 0, 9.033, 0.028, 0, 9.333, -0.011, 0, 9.667, 0.002, 1, 9.734, 0.002, 9.8, 0.001, 9.867, 0]}, {"Target": "Parameter", "Id": "ParamHairFront", "Segments": [0, 0.015, 2, 0.167, 0.015, 2, 0.233, 0.015, 0, 0.4, 0, 2, 7.2, 0, 0, 7.467, 0.055, 0, 8.033, -0.468, 0, 8.267, 0.302, 0, 8.567, -0.058, 0, 8.867, 0.049, 0, 9.367, -0.047, 1, 9.534, -0.047, 9.7, -0.035, 9.867, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, -0.003, 2, 0.167, -0.003, 0, 0.2, -0.004, 0, 0.4, 0.004, 0, 0.6, -0.005, 0, 0.933, 0.003, 0, 1.233, -0.004, 0, 1.433, 0, 2, 7.2, 0, 0, 7.367, -0.018, 0, 7.667, 0.006, 0, 7.9, -0.003, 0, 8.033, 0.162, 0, 8.2, -0.175, 0, 8.467, 0.133, 0, 8.767, -0.108, 0, 9.1, 0.057, 0, 9.433, -0.021, 0, 9.733, 0.018, 1, 9.778, 0.018, 9.822, 0.003, 9.867, 0]}, {"Target": "Parameter", "Id": "Param6", "Segments": [0, 0.006, 2, 0.167, 0.006, 0, 0.233, 0.007, 0, 0.667, -0.002, 0, 1.067, 0.002, 0, 1.467, -0.002, 0, 1.867, 0.002, 0, 2.267, -0.002, 0, 2.667, 0.002, 0, 3.067, -0.002, 0, 3.467, 0.002, 0, 3.867, -0.002, 0, 4.267, 0.003, 0, 4.667, -0.003, 0, 5.067, 0.003, 0, 5.467, -0.003, 0, 5.867, 0.003, 0, 6.267, -0.003, 0, 6.667, 0.003, 0, 7.067, -0.003, 0, 7.533, 0.054, 0, 8.033, -0.43, 0, 8.4, 0.364, 0, 8.833, -0.198, 0, 9.233, 0.074, 0, 9.6, -0.084, 1, 9.689, -0.084, 9.778, -0.069, 9.867, 0]}, {"Target": "Parameter", "Id": "Param7", "Segments": [0, -0.002, 2, 0.167, -0.002, 0, 0.433, 0.008, 0, 0.667, -0.006, 0, 0.833, -0.003, 2, 0.867, -0.003, 0, 1.267, 0.004, 0, 1.567, -0.004, 0, 1.633, -0.003, 0, 1.667, -0.004, 0, 2, 0.004, 0, 2.033, 0.003, 0, 2.067, 0.004, 0, 2.367, -0.004, 0, 2.433, -0.003, 0, 2.467, -0.004, 0, 2.767, 0.004, 0, 2.833, 0.003, 0, 2.867, 0.004, 0, 3.167, -0.004, 0, 3.233, -0.003, 0, 3.267, -0.004, 0, 3.567, 0.004, 2, 3.633, 0.004, 2, 3.667, 0.004, 0, 3.967, -0.004, 2, 4.033, -0.004, 2, 4.067, -0.004, 0, 4.367, 0.004, 2, 4.433, 0.004, 2, 4.467, 0.004, 0, 4.767, -0.004, 2, 4.833, -0.004, 2, 4.867, -0.004, 0, 5.167, 0.004, 2, 5.233, 0.004, 2, 5.267, 0.004, 0, 5.567, -0.004, 2, 5.633, -0.004, 0, 5.667, -0.005, 0, 5.967, 0.004, 2, 6.033, 0.004, 0, 6.067, 0.005, 0, 6.367, -0.004, 2, 6.433, -0.004, 0, 6.467, -0.005, 0, 6.767, 0.004, 2, 6.833, 0.004, 0, 6.867, 0.005, 0, 7.167, -0.004, 2, 7.2, -0.004, 0, 7.333, -0.012, 0, 7.7, 0.111, 0, 7.9, 0.039, 0, 8, 0.115, 0, 8.2, -0.697, 0, 8.533, 0.603, 0, 8.967, -0.289, 0, 9.333, 0.163, 0, 9.733, -0.179, 1, 9.778, -0.179, 9.822, -0.061, 9.867, 0]}, {"Target": "Parameter", "Id": "Param8", "Segments": [0, -0.001, 2, 0.167, -0.001, 0, 0.267, -0.002, 0, 0.533, 0.011, 0, 0.833, -0.009, 0, 1.233, 0.003, 2, 1.267, 0.003, 0, 1.367, 0.009, 0, 1.633, -0.006, 2, 1.667, -0.006, 0, 1.733, -0.008, 0, 2.033, 0.005, 2, 2.067, 0.005, 0, 2.167, 0.008, 0, 2.433, -0.006, 0, 2.467, -0.005, 0, 2.533, -0.008, 0, 2.833, 0.006, 0, 2.867, 0.005, 0, 2.967, 0.009, 0, 3.233, -0.006, 0, 3.267, -0.005, 0, 3.333, -0.009, 0, 3.633, 0.006, 0, 3.667, 0.005, 0, 3.733, 0.009, 0, 4.033, -0.006, 0, 4.067, -0.005, 0, 4.133, -0.009, 0, 4.433, 0.006, 0, 4.467, 0.005, 0, 4.533, 0.009, 0, 4.833, -0.006, 0, 4.867, -0.005, 0, 4.933, -0.009, 0, 5.233, 0.006, 2, 5.267, 0.006, 0, 5.333, 0.009, 0, 5.633, -0.006, 2, 5.667, -0.006, 0, 5.733, -0.009, 0, 6.033, 0.006, 2, 6.067, 0.006, 0, 6.133, 0.009, 0, 6.433, -0.006, 2, 6.467, -0.006, 0, 6.533, -0.009, 0, 6.833, 0.006, 2, 6.867, 0.006, 0, 6.933, 0.01, 0, 7.5, -0.031, 0, 7.867, 0.079, 0, 7.933, 0.074, 0, 8.1, 0.261, 0, 8.333, -0.611, 0, 8.667, 0.606, 0, 9.033, -0.39, 0, 9.433, 0.248, 0, 9.833, -0.178, 1, 9.844, -0.178, 9.856, -0.006, 9.867, 0]}, {"Target": "Parameter", "Id": "Param9", "Segments": [0, -0.004, 2, 0.167, -0.004, 0, 0.633, 0.007, 0, 0.933, -0.011, 0, 1.267, 0.007, 0, 1.4, 0.003, 2, 1.5, 0.003, 0, 1.833, -0.009, 0, 2.233, 0.008, 0, 2.633, -0.009, 0, 3.033, 0.008, 0, 3.433, -0.009, 0, 3.833, 0.009, 0, 4.233, -0.009, 0, 4.633, 0.009, 0, 5.033, -0.009, 0, 5.433, 0.009, 0, 5.833, -0.009, 0, 6.233, 0.009, 0, 6.633, -0.009, 0, 7.033, 0.009, 0, 7.567, -0.042, 0, 8.167, 0.183, 0, 8.4, -0.669, 0, 8.733, 0.815, 0, 9.133, -0.607, 0, 9.5, 0.431, 0, 9.867, 0]}, {"Target": "Parameter", "Id": "Param11", "Segments": [0, -16, 2, 0.167, -16, 2, 4.4, -16, 2, 4.433, 0, 2, 9.867, 0]}, {"Target": "Parameter", "Id": "tiqiehuan", "Segments": [0, 1, 2, 0.167, 1, 2, 4.4, 1, 2, 4.433, 0, 2, 9.867, 0]}, {"Target": "Parameter", "Id": "ZHITIyy", "Segments": [0, 0, 2, 0.167, 0, 0, 2.167, 1, 2, 3.167, 1, 0, 4.167, 0, 2, 9.867, 0]}, {"Target": "Parameter", "Id": "drzbxz", "Segments": [0, 1, 2, 0.167, 1, 2, 2.2, 1, 2, 7.167, 1, 0, 9.867, 0]}, {"Target": "Parameter", "Id": "drybxz", "Segments": [0, -1, 2, 0.167, -1, 0, 2.2, 1, 2, 7.167, 1, 0, 9.867, 0]}, {"Target": "Parameter", "Id": "tisousuo", "Segments": [0, 0, 2, 0.167, 0, 1, 0.845, 0, 1.522, 0.249, 2.2, 0.7, 1, 2.522, 0.914, 2.845, 1, 3.167, 1, 0, 5.533, 0.7, 2, 7.167, 0.7, 0, 9.867, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0.55, 2, 0.167, 0.55, 0, 2.2, -0.776, 0, 3.167, -0.712, 2, 7.167, -0.712, 0, 9.867, 0]}, {"Target": "Parameter", "Id": "dryoutui", "Segments": [0, 0.436, 2, 0.167, 0.436, 0, 2.167, -1, 2, 2.2, -1, 2, 7.167, -1, 0, 9.867, 0]}, {"Target": "Parameter", "Id": "renzuoyou", "Segments": [0, -4.98, 2, 0.167, -4.98, 0, 2.167, -5.88, 2, 2.2, -4.86, 0, 4.4, -4.9, 0, 4.433, 0, 2, 7.167, 0, 2, 9.867, 0]}, {"Target": "Parameter", "Id": "rens<PERSON><PERSON><PERSON>", "Segments": [0, 4.74, 2, 0.167, 4.74, 1, 0.834, 4.74, 1.5, 3.752, 2.167, 1.68, 2, 2.2, -5.28, 1, 2.522, -6.58, 2.845, -9.023, 3.167, -9.06, 1, 3.578, -9.107, 3.989, -9.1, 4.4, -9.1, 0, 4.433, 0, 2, 7.167, 0, 2, 9.867, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON>", "Segments": [0, -0.6, 2, 0.167, -0.6, 0, 2.167, 0.24, 0, 5.167, 0, 2, 9.867, 0]}, {"Target": "Parameter", "Id": "rendaxiao", "Segments": [0, 21.12, 2, 0.167, 21.12, 0, 2.167, 16.44, 2, 2.2, 19.86, 1, 2.933, 19.887, 3.667, 19.9, 4.4, 19.9, 0, 4.433, 0, 2, 7.167, 0, 2, 9.867, 0]}, {"Target": "Parameter", "Id": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.167, 0, 2, 3.167, 0, 1, 3.222, 0, 3.278, 0.065, 3.333, 0.13, 1, 3.555, 0.392, 3.778, 0.5, 4, 0.5, 2, 4.167, 0.5, 3, 4.2, 10, 3, 4.233, 0, 2, 4.533, 0, 2, 7.167, 0, 2, 9.867, 0]}, {"Target": "Parameter", "Id": "b<PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.167, 0, 2, 3.167, 0, 2, 3.5, 0, 0, 3.733, 0.5, 2, 3.967, 0.5, 0, 4.167, 0, 1, 4.222, 0, 4.278, -0.043, 4.333, 1, 1, 4.378, 1.834, 4.422, 10, 4.467, 10, 0, 4.5, 0, 2, 4.833, 0, 2, 9.867, 0]}, {"Target": "Parameter", "Id": "Param10", "Segments": [0, 30, 2, 0.167, 30, 2, 3, 30, 1, 3.056, 30, 3.111, 12.234, 3.167, 10.618, 1, 3.445, 2.54, 3.722, 0, 4, 0, 2, 7.167, 0, 2, 9.867, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.167, 0, 2, 3.433, 0, 1, 3.678, 0, 3.922, 0, 4.167, 1, 2, 7.167, 1, 2, 7.2, 0, 2, 9.867, 0]}, {"Target": "Parameter", "Id": "baiguang<PERSON><PERSON>u", "Segments": [0, 0, 2, 0.167, 0, 2, 4.167, 0, 1, 4.389, 1, 4.611, 1, 4.833, 1, 2, 4.867, 10, 2, 5.167, 10, 2, 5.2, 0, 2, 9.867, 0]}, {"Target": "Parameter", "Id": "lizi1daxoao", "Segments": [0, 0, 2, 0.167, 0, 2, 4.367, 0, 0, 4.4, 1, 0, 5.467, 0, 2, 7.167, 0, 2, 9.867, 0]}, {"Target": "Parameter", "Id": "lizi2daxiao", "Segments": [0, 0, 2, 0.167, 0, 2, 4.367, 0, 0, 4.4, 1, 0, 6.067, 0, 2, 7.167, 0, 2, 9.867, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.167, 0, 2, 4.367, 0, 0, 4.4, 1, 0, 6.067, 0, 2, 7.167, 0, 2, 9.867, 0]}, {"Target": "Parameter", "Id": "liziyi<PERSON>", "Segments": [0, 0, 2, 0.167, 0, 2, 4.767, 0, 1, 5.189, 0, 5.611, 0.761, 6.033, 0.9, 1, 6.411, 1.024, 6.789, 1, 7.167, 1, 0, 7.667, 0, 2, 9.867, 0]}, {"Target": "Parameter", "Id": "liziqumian", "Segments": [0, 0, 2, 0.167, 0, 2, 4.4, 0, 0, 4.8, 1, 2, 7.167, 1, 0, 7.667, 0, 2, 9.867, 0]}, {"Target": "Parameter", "Id": "lizizong<PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.167, 0, 2, 4.433, 0, 1, 4.544, 0, 4.656, 0.721, 4.767, 0.8, 1, 5.022, 0.983, 5.278, 1, 5.533, 1, 2, 7.167, 1, 2, 7.2, 0, 2, 9.867, 0]}, {"Target": "Parameter", "Id": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.167, 0, 2, 4.467, 0, 0, 4.7, 1, 2, 5.467, 1, 0, 6.567, 2, 2, 7.167, 2, 2, 7.2, 0, 2, 9.867, 0]}, {"Target": "Parameter", "Id": "jing<PERSON>u", "Segments": [0, 1, 2, 0.167, 1, 2, 4.4, 1, 0, 4.567, 0, 2, 7.167, 0, 2, 9.867, 0]}, {"Target": "Parameter", "Id": "andu", "Segments": [0, 0, 2, 0.167, 0, 2, 0.833, 0, 0, 2.2, 0.5, 1, 2.522, 0.5, 2.845, 0.507, 3.167, 0.454, 1, 3.445, 0.408, 3.722, 0, 4, 0, 2, 7.167, 0, 2, 9.867, 0]}, {"Target": "Parameter", "Id": "lanmu", "Segments": [0, 1, 2, 0.167, 1, 1, 0.845, 1, 1.522, 0.98, 2.2, 0.9, 1, 2.522, 0.862, 2.845, 0.818, 3.167, 0.7, 1, 3.5, 0.578, 3.834, 0, 4.167, 0, 2, 4.233, 0, 2, 4.367, 0, 0, 4.567, 1, 0, 4.833, 0, 2, 7.167, 0, 2, 9.867, 0]}, {"Target": "Parameter", "Id": "heidi", "Segments": [0, 1, 2, 0.167, 1, 2, 4.167, 1, 2, 4.4, 1, 2, 5.467, 1, 0, 7.167, 0, 2, 9.867, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.167, 0, 1, 0.822, 0, 1.478, 0.773, 2.133, 0.881, 1, 2.933, 1.013, 3.733, 1, 4.533, 1, 2, 7.167, 1, 2, 8.367, 1, 0, 9.6, 0, 2, 9.867, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.167, 0, 0, 4.533, -0.6, 2, 7.167, -0.6, 0, 8.633, -1, 0, 8.767, 0, 2, 8.8, 0, 2, 9.3, 0, 2, 9.867, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.167, 0, 0, 4.533, -1, 2, 7.167, -1, 0, 8.767, 0, 2, 8.8, 0, 2, 9.3, 0, 2, 9.867, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON>ia<PERSON><PERSON>uo<PERSON>", "Segments": [0, 0, 2, 0.167, 0, 2, 7.167, 0, 0, 7.833, 1, 1, 8.011, 1, 8.189, 1.019, 8.367, 0.817, 1, 8.645, 0.501, 8.922, 0, 9.2, 0, 2, 9.867, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 1, 2, 0.167, 1, 2, 7.167, 1, 0, 7.333, 0.4, 0, 7.867, 1, 0, 8.3, -1, 0, 9.867, 1]}, {"Target": "Parameter", "Id": "zu<PERSON><PERSON><PERSON>ng", "Segments": [0, 0, 2, 0.167, 0, 2, 7.167, 0, 0, 8.167, 1, 0, 8.367, 0, 2, 9.867, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.167, 0, 2, 7.167, 0, 0, 7.2, 1, 2, 7.533, 1, 2, 8.4, 1, 0, 8.633, 0, 2, 9.867, 0]}, {"Target": "Parameter", "Id": "xingfengyaobai", "Segments": [0, 0, 2, 0.167, 0, 2, 7.167, 0, 0, 7.467, -0.3, 0, 7.7, 0, 2, 7.8, 0, 2, 9.867, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.167, 0, 2, 2.167, 0, 0, 2.2, 1.5, 0, 4.433, 1, 2, 4.8, 1, 2, 7.167, 1, 2, 7.933, 1, 2, 8.433, 1, 0, 9.6, 0, 2, 9.867, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.167, 0, 2, 4.4, 0, 0, 4.433, 1, 2, 4.467, 1, 2, 4.8, 1, 2, 7.167, 1, 2, 7.933, 1, 2, 8.433, 1, 0, 9.6, 0, 2, 9.867, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.167, 0, 0, 4.433, 0.076, 2, 7.167, 0.076, 0, 7.933, 0.107, 0, 9.6, 0, 2, 9.867, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON>biji<PERSON><PERSON>", "Segments": [0, 0, 2, 0.167, 0, 0, 4.333, -1, 0, 4.433, -0.8, 0, 5.5, -1, 0, 6.433, -0.978, 0, 7.167, -1, 1, 7.422, -1, 7.678, -0.513, 7.933, -0.158, 1, 8.1, 0.073, 8.266, 0.064, 8.433, 0.064, 0, 9.6, 0, 2, 9.867, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.167, 0, 2, 7.167, 0, 2, 7.567, 0, 1, 7.689, 0, 7.811, -0.491, 7.933, -0.636, 1, 8.2, -0.952, 8.466, -1, 8.733, -1, 1, 8.811, -1, 8.889, -0.422, 8.967, -0.321, 1, 9.178, -0.046, 9.389, 0, 9.6, 0, 2, 9.867, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 1, 2, 0.167, 1, 0, 4.533, 0, 2, 5.5, 0, 2, 7.167, 0, 2, 7.4, 0, 0, 7.933, 1, 2, 9.6, 1, 2, 9.867, 1]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.167, 0, 2, 7.167, 0, 0, 8.433, -0.387, 0, 9.6, 0, 2, 9.867, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.167, 0, 0, 4.533, -1, 2, 5.5, -1, 2, 7.167, -1, 2, 8.433, -1, 0, 9.6, 0, 2, 9.867, 0]}, {"Target": "Parameter", "Id": "xiongshangxai", "Segments": [0, 0.003, 2, 0.167, 0.003, 0, 0.467, -0.017, 0, 0.667, -0.001, 0, 0.9, -0.008, 0, 1.1, -0.007, 0, 1.367, -0.008, 2, 1.4, -0.008, 2, 1.433, -0.008, 2, 1.467, -0.008, 2, 1.533, -0.008, 2, 1.567, -0.008, 2, 1.6, -0.008, 2, 1.633, -0.008, 0, 1.7, -0.009, 2, 1.733, -0.009, 2, 1.767, -0.009, 2, 1.8, -0.009, 2, 1.867, -0.009, 2, 1.9, -0.009, 2, 1.933, -0.009, 2, 1.967, -0.009, 2, 2.033, -0.009, 2, 2.067, -0.009, 2, 2.1, -0.009, 2, 2.133, -0.009, 2, 2.2, -0.009, 2, 2.233, -0.009, 2, 2.267, -0.009, 2, 2.3, -0.009, 2, 2.367, -0.009, 2, 2.4, -0.009, 2, 2.433, -0.009, 2, 2.467, -0.009, 2, 2.533, -0.009, 2, 2.567, -0.009, 2, 2.6, -0.009, 0, 2.633, -0.008, 0, 2.7, -0.009, 0, 2.733, -0.008, 2, 2.767, -0.008, 2, 2.8, -0.008, 2, 2.867, -0.008, 2, 2.9, -0.008, 2, 2.933, -0.008, 2, 2.967, -0.008, 2, 3.033, -0.008, 0, 3.067, -0.007, 2, 3.1, -0.007, 2, 3.133, -0.007, 2, 3.167, -0.007, 0, 4.8, 0.817, 0, 5.033, -1, 0, 5.367, 0.5, 0, 5.6, -0.134, 0, 5.8, 0.072, 0, 6.033, 0.007, 0, 6.233, 0.027, 0, 6.567, 0.015, 2, 6.6, 0.015, 0, 7.433, -0.076, 0, 8.3, 0.815, 0, 8.533, -1, 2, 8.567, -1, 0, 8.9, 0.981, 0, 9.167, -0.808, 0, 9.4, 0.154, 0, 9.6, -0.113, 1, 9.689, -0.113, 9.778, -0.083, 9.867, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0.001, 2, 0.167, 0.001, 0, 0.467, -0.008, 0, 0.667, 0, 0, 0.9, -0.004, 0, 1.1, -0.003, 0, 1.367, -0.004, 2, 1.4, -0.004, 2, 1.433, -0.004, 2, 1.467, -0.004, 2, 1.533, -0.004, 2, 1.567, -0.004, 2, 1.6, -0.004, 2, 1.633, -0.004, 2, 1.7, -0.004, 2, 1.733, -0.004, 2, 1.767, -0.004, 2, 1.8, -0.004, 0, 1.867, -0.005, 0, 1.9, -0.004, 2, 1.933, -0.004, 2, 1.967, -0.004, 0, 2.033, -0.005, 0, 2.067, -0.004, 0, 2.1, -0.005, 0, 2.133, -0.004, 0, 2.2, -0.005, 0, 2.233, -0.004, 0, 2.267, -0.005, 0, 2.3, -0.004, 0, 2.367, -0.005, 0, 2.4, -0.004, 2, 2.433, -0.004, 2, 2.467, -0.004, 2, 2.533, -0.004, 2, 2.567, -0.004, 2, 2.6, -0.004, 2, 2.633, -0.004, 2, 2.7, -0.004, 2, 2.733, -0.004, 2, 2.767, -0.004, 2, 2.8, -0.004, 2, 2.867, -0.004, 2, 2.9, -0.004, 2, 2.933, -0.004, 2, 2.967, -0.004, 2, 3.033, -0.004, 2, 3.067, -0.004, 2, 3.1, -0.004, 2, 3.133, -0.004, 2, 3.167, -0.004, 0, 4.8, 0.408, 0, 5.033, -0.505, 0, 5.367, 0.25, 0, 5.6, -0.067, 0, 5.8, 0.036, 0, 6.033, 0.003, 0, 6.233, 0.014, 0, 6.567, 0.007, 2, 6.6, 0.007, 0, 7.433, -0.038, 0, 8.3, 0.408, 0, 8.533, -0.533, 0, 8.9, 0.49, 0, 9.167, -0.404, 0, 9.4, 0.077, 0, 9.6, -0.057, 1, 9.689, -0.057, 9.778, -0.041, 9.867, 0]}, {"Target": "Parameter", "Id": "youxiabifa", "Segments": [0, 0, 2, 0.167, 0, 2, 7.167, 0, 0, 8.333, 1, 0, 9.033, 0, 2, 9.867, 0]}, {"Target": "Parameter", "Id": "op", "Segments": [0, 0, 2, 0.167, 0, 0, 4.533, 1, 2, 7.167, 1, 0, 7.967, 0.606, 0, 8.5, 1, 0, 9.867, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 0.332, 2, 0.167, 0.332, 0, 2.2, 1.2, 0, 3, 0, 2, 4.167, 0, 0, 5.467, 0.593, 2, 6.667, 0.593, 0, 7.167, 1, 0, 8.033, 0, 2, 8.4, 0, 0, 9.067, 1, 2, 9.867, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 2, 0.167, 0, 0, 4.067, 0.2, 2, 7.167, 0.2, 0, 8.033, 0, 2, 9.867, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 0.332, 2, 0.167, 0.332, 0, 2.2, 1.2, 0, 3, 0, 2, 4.167, 0, 0, 5.467, 0.593, 2, 6.667, 0.593, 0, 7.167, 1, 0, 8.033, 0, 2, 8.4, 0, 0, 9.067, 1, 2, 9.867, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 2, 0.167, 0, 0, 4.067, 0.2, 2, 7.167, 0.2, 0, 8.033, 0, 2, 9.867, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 2, 0.167, 0, 2, 7.167, 0, 0, 7.3, -1, 0, 9.867, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 0, 2, 0.167, 0, 0, 2.333, 1, 0, 3, 0.3, 0, 3.267, 1, 2, 4.467, 1, 0, 5.867, 0, 0, 7.167, 1, 2, 7.8, 1, 0, 8, 0, 0, 9.167, 1, 0, 9.867, 0]}, {"Target": "Parameter", "Id": "zao1", "Segments": [0, 0, 2, 0.167, 0, 2, 7.167, 0, 0, 9.867, 0.1]}, {"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, -90, 2, 0.167, -90, 2, 2.667, -90, 1, 3.245, -90, 3.822, -60.982, 4.4, 0, 2, 4.433, 90, 1, 4.789, 90, 5.144, 18.083, 5.5, 11.16, 1, 6.056, 0.343, 6.611, 0, 7.167, 0, 0, 7.8, 21, 0, 9.867, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 90, 2, 0.167, 90, 0, 2.133, -90, 1, 3.066, -90, 4, -91.552, 4.933, -77, 1, 5.855, -62.621, 6.778, 33, 7.7, 33, 0, 8.033, -51, 0, 9.867, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, -30, 2, 0.167, -30, 2, 2.133, -30, 1, 2.889, -30, 3.644, -22.835, 4.4, -8, 2, 4.433, 1, 0, 5.5, -30, 1, 6.056, -30, 6.611, -29.359, 7.167, -21, 1, 7.578, -14.814, 7.989, 1, 8.4, 1, 0, 9.867, -30]}, {"Target": "Parameter", "Id": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.167, 0, 2, 4.433, 0, 2, 7.167, 0, 0, 7.633, -0.14, 0, 8.5, 0.4, 0, 9.867, 0]}, {"Target": "Parameter", "Id": "shangshenyaobai", "Segments": [0, 0, 2, 0.167, 0, 2, 4.4, 0, 2, 4.433, 12, 1, 4.789, 12, 5.144, 0.608, 5.5, 0.36, 1, 6.056, -0.028, 6.611, 0, 7.167, 0, 0, 8.133, 10.863, 0, 8.8, 0, 2, 9.867, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 2, 0.167, 0, 2, 4.4, 0, 1, 4.411, 0, 4.422, -30, 4.433, -30, 1, 4.789, -30, 5.144, -18.219, 5.5, -17.18, 1, 6.056, -15.557, 6.611, -15.638, 7.167, -14.04, 1, 7.5, -13.081, 7.834, 0, 8.167, 0, 2, 9.867, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.167, 0, 2, 0.2, 0, 2, 7.2, 0, 0, 7.433, 0.035, 0, 8.033, -0.366, 0, 8.2, 0.251, 0, 8.467, -0.054, 0, 8.733, 0.041, 0, 9.4, -0.031, 1, 9.556, -0.031, 9.711, -0.023, 9.867, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.167, 0, 2, 4.4, 0, 2, 4.433, -1, 2, 7.933, -1, 0, 9.567, 0, 2, 9.867, 0]}, {"Target": "Parameter", "Id": "<PERSON>banzhenmian", "Segments": [0, 1, 2, 0.167, 1, 2, 4.4, 1, 2, 4.433, 0, 2, 7.167, 0, 2, 9.867, 0]}, {"Target": "Parameter", "Id": "dibantmd", "Segments": [0, 0, 2, 0.167, 0, 2, 3.167, 0, 0, 4.167, 1, 0, 7.167, 0, 2, 9.867, 0]}, {"Target": "Parameter", "Id": "beijingtmd", "Segments": [0, 1, 2, 0.167, 1, 2, 5.467, 1, 0, 7.167, 0, 2, 9.867, 0]}, {"Target": "Model", "Id": "Opacity", "Segments": [0, 1, 0, 9.87, 1]}, {"Target": "Parameter", "Id": "ParamHairSide", "Segments": [0, 0, 0, 9.87, 0]}, {"Target": "Parameter", "Id": "ParamHairBack", "Segments": [0, 0, 0, 9.87, 0]}, {"Target": "Parameter", "Id": "shi<PERSON>ez<PERSON>biaozuoyou", "Segments": [0, 0, 0, 9.87, 0]}, {"Target": "Parameter", "Id": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 9.87, 0]}, {"Target": "Parameter", "Id": "jingshen", "Segments": [0, 0.5, 0, 9.87, 0.5]}, {"Target": "Parameter", "Id": "ZZZ", "Segments": [0, 0, 0, 9.87, 0]}, {"Target": "Parameter", "Id": "jingjingtmd", "Segments": [0, 0, 0, 9.87, 0]}, {"Target": "Parameter", "Id": "jingjingdaixioa", "Segments": [0, 0, 0, 9.87, 0]}, {"Target": "Parameter", "Id": "<PERSON>ng<PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 9.87, 0]}, {"Target": "Parameter", "Id": "jingjingshangxai", "Segments": [0, 0, 0, 9.87, 0]}, {"Target": "Parameter", "Id": "bai", "Segments": [0, 0, 0, 9.87, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 9.87, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 9.87, 0]}, {"Target": "Parameter", "Id": "xingfengtmd", "Segments": [0, 0, 0, 9.87, 0]}, {"Target": "Parameter", "Id": "fizitmd", "Segments": [0, 0, 0, 9.87, 0]}, {"Target": "Parameter", "Id": "xiongtuceng", "Segments": [0, 0, 0, 9.87, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 9.87, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 1, 0, 9.87, 1]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 0, 9.87, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 9.87, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON>kongda<PERSON><PERSON>", "Segments": [0, 0, 0, 9.87, 0]}, {"Target": "Parameter", "Id": "gaoguangdaxia<PERSON>", "Segments": [0, 0, 0, 9.87, 0]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 0, 9.87, 0]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 0, 9.87, 0]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 0, 9.87, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 0, 9.87, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 0, 9.87, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 0, 9.87, 0]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, -1, 0, 9.87, -1]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, -1, 0, 9.87, -1]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 0, 9.87, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 9.87, 0]}, {"Target": "Parameter", "Id": "kous<PERSON>dongkaibi", "Segments": [0, 0, 0, 9.87, 0]}, {"Target": "Parameter", "Id": "leimu", "Segments": [0, 0, 0, 9.87, 0]}, {"Target": "Parameter", "Id": "leidonghua", "Segments": [0, 0, 0, 9.87, 0]}, {"Target": "Parameter", "Id": "youleidonghua", "Segments": [0, 0, 0, 9.87, 0]}, {"Target": "Parameter", "Id": "leimudonghua", "Segments": [0, 0, 0, 9.87, 0]}, {"Target": "Parameter", "Id": "jingyaa", "Segments": [0, 0, 0, 9.87, 0]}, {"Target": "Parameter", "Id": "ParamCheek", "Segments": [0, 0, 0, 9.87, 0]}, {"Target": "Parameter", "Id": "wu", "Segments": [0, 0, 0, 9.87, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 9.87, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 9.87, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 0, 9.87, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 0, 9.87, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 0, 9.87, 0]}], "UserData": [{"Time": 0.0, "Value": ""}, {"Time": 9.37, "Value": ""}]}