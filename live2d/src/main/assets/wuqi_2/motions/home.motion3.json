{"Version": 3, "Meta": {"Duration": 23.233, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 149, "TotalSegmentCount": 3036, "TotalPointCount": 3244, "UserDataCount": 2, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "wuli", "Segments": [0, 0.305, 0, 1.433, -1, 0, 2, -0.254, 0, 3.5, -1, 0, 4.233, 1, 0, 5.1, -1, 0, 9.167, 1, 0, 9.733, -1, 0, 10.767, 1, 0, 11.767, -1, 0, 12.467, 1, 0, 13.7, -1, 0, 14.8, 1, 0, 15.833, -1, 0, 16.9, 1, 0, 17.9, -1, 0, 18.967, 1, 0, 19.7, -1, 0, 21.4, 1, 0, 21.767, -1, 0, 22.367, 1, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "Para", "Segments": [0, 0.138, 0, 1.733, -1, 0, 2, -0.72, 2, 20.6, -0.72, 0, 21.467, 1, 0, 22.2, -1, 0, 22.733, 1, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "zuocefati1", "Segments": [0, -0.147, 0, 0.033, -0.151, 0, 0.433, 0.581, 0, 0.9, -0.044, 0, 1.233, 0.095, 0, 1.733, -0.566, 0, 2.167, 0.719, 0, 2.767, -3.584, 0, 3.133, 3.616, 0, 3.567, -2.002, 0, 4.1, 0.88, 0, 5.167, -0.475, 0, 5.567, 0.247, 0, 6, -0.2, 0, 6.4, 0.067, 0, 6.833, -0.084, 0, 7.3, 0.015, 0, 7.667, -0.033, 0, 8.167, 0.011, 0, 8.367, 0.003, 0, 8.8, 0.49, 0, 9.2, 0.154, 0, 9.5, 1.585, 0, 9.933, -2.102, 0, 10.367, 0.933, 0, 10.767, -0.389, 0, 11.167, 0.567, 0, 12.033, -0.923, 0, 12.6, 1.342, 0, 13.033, -0.468, 0, 13.4, 0.246, 0, 13.9, -0.685, 0, 14.467, 0.145, 0, 14.6, 0.139, 0, 15.067, 0.561, 0, 16.067, -0.522, 0, 17.167, 0.565, 0, 18.1, -0.541, 0, 19.267, 0.944, 0, 19.8, -1.271, 0, 20.233, 0.521, 0, 20.633, -0.418, 0, 21.1, 0.283, 0, 21.433, 0.055, 0, 21.733, 1.091, 0, 22.2, -1.621, 0, 22.633, 0.775, 0, 23.033, -0.423, 1, 23.1, -0.423, 23.166, -0.136, 23.233, 0.094]}, {"Target": "Parameter", "Id": "zuocefati2", "Segments": [0, -0.072, 0, 0.167, -0.322, 0, 0.6, 1.116, 0, 1, 0.367, 0, 1.367, 0.694, 0, 1.933, -0.761, 0, 2.333, 1.362, 0, 2.567, 0.065, 0, 2.633, 0.31, 0, 2.933, -7.048, 0, 3.3, 5.886, 0, 3.667, -3.35, 0, 4.2, 0.575, 0, 4.333, 0.446, 0, 4.633, 0.772, 0, 5.367, -0.616, 0, 5.733, 0.417, 0, 6.133, -0.401, 0, 6.533, 0.043, 0, 6.967, -0.231, 0, 7.4, -0.076, 0, 7.767, -0.155, 0, 8.3, -0.073, 0, 8.533, -0.146, 0, 9, 0.907, 0, 9.333, 0.326, 0, 9.7, 3.837, 0, 10.1, -2.727, 0, 10.5, 1.796, 0, 10.867, -0.74, 0, 11.3, 1.207, 0, 12.267, -1.813, 0, 12.767, 1.814, 0, 13.167, -0.593, 0, 13.533, 0.797, 0, 14.1, -1.075, 0, 14.567, -0.304, 0, 14.733, -0.341, 0, 15.3, 0.919, 0, 16.3, -0.973, 0, 17.4, 0.915, 0, 18.367, -0.931, 0, 19.467, 1.772, 0, 19.967, -1.735, 0, 20.367, 0.747, 0, 20.767, -0.975, 0, 21.233, 0.206, 0, 21.567, -0.272, 0, 21.933, 2.308, 0, 22.367, -2.405, 0, 22.767, 1.203, 0, 23.167, -0.96, 1, 23.189, -0.96, 23.211, -0.863, 23.233, -0.734]}, {"Target": "Parameter", "Id": "zuocefati3", "Segments": [0, 0.034, 0, 0.3, -0.46, 0, 0.7, 0.533, 0, 1.067, -0.362, 0, 1.467, 0.279, 0, 2.067, -0.785, 0, 2.433, 1.167, 0, 2.633, 0.363, 0, 2.8, 1.511, 0, 3.067, -5.764, 0, 3.4, 6.643, 0, 3.767, -4.327, 0, 4.1, 1.704, 0, 4.467, -0.582, 0, 4.767, 0.418, 0, 5.067, -0.117, 0, 5.267, 0.083, 0, 5.5, -0.453, 0, 5.833, 0.558, 0, 6.2, -0.41, 0, 6.567, 0.233, 0, 7, -0.116, 0, 7.433, 0.062, 0, 7.867, -0.04, 0, 8.267, 0.02, 0, 8.7, -0.289, 0, 9.067, 0.329, 0, 9.5, -1.115, 0, 9.833, 2.663, 0, 10.2, -3.123, 0, 10.567, 2.306, 0, 10.933, -1.482, 0, 11.333, 0.841, 0, 11.767, -0.168, 0, 12.1, 0.348, 0, 12.433, -1.061, 0, 12.867, 1.377, 0, 13.233, -1.168, 0, 13.633, 0.862, 0, 14.067, -0.397, 0, 14.567, 0.188, 0, 14.967, -0.313, 0, 15.4, 0.429, 0, 15.767, -0.176, 0, 16.067, 0.214, 0, 16.433, -0.314, 0, 16.767, 0.102, 0, 17.1, -0.227, 0, 17.5, 0.394, 0, 17.867, -0.133, 0, 18.133, 0.161, 0, 18.467, -0.294, 0, 18.8, 0.103, 0, 19.233, -0.416, 0, 19.633, 1.065, 0, 20.067, -1.313, 0, 20.433, 1.22, 0, 20.833, -0.862, 0, 21.233, 0.437, 0, 21.7, -0.814, 0, 22.1, 1.732, 0, 22.467, -2.177, 0, 22.833, 1.776, 0, 23.233, -1.178]}, {"Target": "Parameter", "Id": "zuocefati4", "Segments": [0, 0.262, 0, 0.3, -0.586, 0, 0.767, 0.736, 0, 1.133, -0.625, 0, 1.533, 0.495, 0, 2.167, -0.946, 0, 2.533, 1.63, 0, 2.733, 0.441, 0, 2.9, 0.932, 0, 3.167, -6.486, 0, 3.5, 9.441, 0, 3.833, -7.579, 0, 4.2, 4.135, 0, 4.533, -1.932, 0, 4.867, 1.108, 0, 5.167, -0.483, 0, 5.4, 0.096, 0, 5.633, -0.477, 0, 5.933, 0.799, 0, 6.267, -0.731, 0, 6.633, 0.49, 0, 7, -0.254, 0, 7.433, 0.111, 0, 7.9, -0.062, 0, 8.333, 0.035, 0, 8.8, -0.336, 0, 9.133, 0.483, 0, 9.567, -1.351, 0, 9.933, 3.459, 0, 10.3, -4.578, 0, 10.633, 4.009, 0, 11, -2.845, 0, 11.367, 1.71, 0, 11.767, -0.571, 0, 12.167, 0.496, 0, 12.533, -1.404, 0, 12.933, 1.93, 0, 13.3, -1.869, 0, 13.7, 1.526, 0, 14.067, -0.802, 0, 14.6, 0.243, 0, 15.033, -0.418, 0, 15.467, 0.604, 0, 15.833, -0.365, 0, 16.167, 0.357, 0, 16.5, -0.485, 0, 16.833, 0.254, 0, 17.2, -0.315, 0, 17.567, 0.55, 0, 17.933, -0.305, 0, 18.233, 0.273, 0, 18.567, -0.44, 0, 18.9, 0.25, 0, 19.3, -0.527, 0, 19.7, 1.392, 0, 20.133, -1.798, 0, 20.533, 1.897, 0, 20.9, -1.531, 0, 21.267, 0.904, 0, 21.767, -0.957, 0, 22.167, 2.3, 0, 22.567, -3.124, 0, 22.933, 2.94, 1, 23.033, 2.94, 23.133, -0.495, 23.233, -1.745]}, {"Target": "Parameter", "Id": "zuocefati5", "Segments": [0, 0.714, 0, 0.3, -0.862, 0, 0.833, 0.942, 0, 1.233, -0.988, 0, 1.6, 0.866, 0, 2.233, -1.109, 0, 2.633, 2.176, 0, 2.9, 0.133, 0, 2.967, 0.205, 0, 3.267, -6.852, 0, 3.567, 12.112, 0, 3.933, -11.595, 0, 4.3, 7.695, 0, 4.633, -4.515, 0, 4.933, 2.724, 0, 5.267, -1.44, 0, 5.533, 0.441, 0, 5.767, -0.493, 0, 6.067, 1.06, 0, 6.367, -1.192, 0, 6.733, 0.95, 0, 7.067, -0.578, 0, 7.433, 0.27, 0, 7.867, -0.103, 0, 8.367, 0.052, 0, 8.867, -0.387, 0, 9.233, 0.677, 0, 9.633, -1.696, 0, 10, 4.42, 0, 10.367, -6.541, 0, 10.733, 6.416, 0, 11.1, -5.082, 0, 11.433, 3.383, 0, 11.8, -1.585, 0, 12.2, 0.923, 0, 12.6, -1.839, 0, 13, 2.684, 0, 13.4, -2.889, 0, 13.767, 2.6, 0, 14.133, -1.636, 0, 14.533, 0.54, 0, 15.133, -0.504, 0, 15.533, 0.831, 0, 15.9, -0.641, 0, 16.267, 0.599, 0, 16.6, -0.758, 0, 16.967, 0.569, 0, 17.267, -0.589, 0, 17.667, 0.8, 0, 18.033, -0.566, 0, 18.333, 0.484, 0, 18.667, -0.673, 0, 19, 0.491, 0, 19.367, -0.757, 0, 19.8, 1.789, 0, 20.2, -2.494, 0, 20.6, 2.816, 0, 20.967, -2.548, 0, 21.333, 1.755, 0, 21.767, -1.314, 0, 22.233, 2.87, 0, 22.633, -4.399, 0, 23, 4.63, 1, 23.078, 4.63, 23.155, 1.192, 23.233, -1.308]}, {"Target": "Parameter", "Id": "fashipiaodai1", "Segments": [0, -0.184, 0, 0.033, -0.189, 0, 0.433, 0.726, 0, 0.9, -0.055, 0, 1.233, 0.119, 0, 1.733, -0.707, 0, 2.167, 0.899, 0, 2.767, -4.48, 0, 3.133, 4.52, 0, 3.567, -2.503, 0, 4.1, 1.101, 0, 5.167, -0.594, 0, 5.567, 0.309, 0, 6, -0.25, 0, 6.4, 0.084, 0, 6.833, -0.105, 0, 7.3, 0.018, 0, 7.667, -0.041, 0, 8.167, 0.014, 0, 8.367, 0.004, 0, 8.8, 0.613, 0, 9.2, 0.192, 0, 9.5, 1.981, 0, 9.933, -2.628, 0, 10.367, 1.167, 0, 10.767, -0.487, 0, 11.167, 0.708, 0, 12.033, -1.153, 0, 12.6, 1.678, 0, 13.033, -0.586, 0, 13.4, 0.308, 0, 13.9, -0.856, 0, 14.467, 0.181, 0, 14.6, 0.173, 0, 15.067, 0.701, 0, 16.067, -0.653, 0, 17.167, 0.706, 0, 18.1, -0.676, 0, 19.267, 1.18, 0, 19.8, -1.589, 0, 20.233, 0.651, 0, 20.633, -0.522, 0, 21.1, 0.354, 0, 21.433, 0.069, 0, 21.733, 1.364, 0, 22.2, -2.027, 0, 22.633, 0.969, 0, 23.033, -0.528, 1, 23.1, -0.528, 23.166, -0.17, 23.233, 0.117]}, {"Target": "Parameter", "Id": "fashipiaodai2", "Segments": [0, -0.09, 0, 0.167, -0.403, 0, 0.6, 1.395, 0, 1, 0.459, 0, 1.367, 0.868, 0, 1.933, -0.951, 0, 2.333, 1.703, 0, 2.567, 0.081, 0, 2.633, 0.387, 0, 2.933, -8.81, 0, 3.3, 7.357, 0, 3.667, -4.187, 0, 4.2, 0.719, 0, 4.333, 0.557, 0, 4.633, 0.966, 0, 5.367, -0.77, 0, 5.733, 0.521, 0, 6.133, -0.501, 0, 6.533, 0.054, 0, 6.967, -0.289, 0, 7.4, -0.095, 0, 7.767, -0.194, 0, 8.3, -0.092, 0, 8.533, -0.182, 0, 9, 1.134, 0, 9.333, 0.408, 0, 9.7, 4.797, 0, 10.1, -3.409, 0, 10.5, 2.245, 0, 10.867, -0.925, 0, 11.3, 1.509, 0, 12.267, -2.267, 0, 12.767, 2.267, 0, 13.167, -0.742, 0, 13.533, 0.996, 0, 14.1, -1.344, 0, 14.567, -0.38, 0, 14.733, -0.426, 0, 15.3, 1.149, 0, 16.3, -1.217, 0, 17.4, 1.144, 0, 18.367, -1.164, 0, 19.467, 2.215, 0, 19.967, -2.168, 0, 20.367, 0.934, 0, 20.767, -1.219, 0, 21.233, 0.257, 0, 21.567, -0.34, 0, 21.933, 2.885, 0, 22.367, -3.006, 0, 22.767, 1.504, 0, 23.167, -1.2, 1, 23.189, -1.2, 23.211, -1.079, 23.233, -0.918]}, {"Target": "Parameter", "Id": "fashipiaodai3", "Segments": [0, 0.042, 0, 0.3, -0.575, 0, 0.7, 0.666, 0, 1.067, -0.453, 0, 1.467, 0.349, 0, 2.067, -0.982, 0, 2.433, 1.458, 0, 2.633, 0.453, 0, 2.8, 1.889, 0, 3.067, -7.205, 0, 3.4, 8.304, 0, 3.767, -5.409, 0, 4.1, 2.129, 0, 4.467, -0.728, 0, 4.767, 0.523, 0, 5.067, -0.147, 0, 5.267, 0.103, 0, 5.5, -0.566, 0, 5.833, 0.698, 0, 6.2, -0.513, 0, 6.567, 0.291, 0, 7, -0.145, 0, 7.433, 0.077, 0, 7.867, -0.051, 0, 8.267, 0.025, 0, 8.7, -0.361, 0, 9.067, 0.411, 0, 9.5, -1.394, 0, 9.833, 3.328, 0, 10.2, -3.903, 0, 10.567, 2.882, 0, 10.933, -1.852, 0, 11.333, 1.051, 0, 11.767, -0.21, 0, 12.1, 0.436, 0, 12.433, -1.326, 0, 12.867, 1.721, 0, 13.233, -1.46, 0, 13.633, 1.077, 0, 14.067, -0.497, 0, 14.567, 0.235, 0, 14.967, -0.391, 0, 15.4, 0.536, 0, 15.767, -0.22, 0, 16.067, 0.267, 0, 16.433, -0.392, 0, 16.767, 0.127, 0, 17.1, -0.283, 0, 17.5, 0.493, 0, 17.867, -0.166, 0, 18.133, 0.202, 0, 18.467, -0.368, 0, 18.8, 0.129, 0, 19.233, -0.52, 0, 19.633, 1.331, 0, 20.067, -1.641, 0, 20.433, 1.525, 0, 20.833, -1.077, 0, 21.233, 0.547, 0, 21.7, -1.018, 0, 22.1, 2.165, 0, 22.467, -2.721, 0, 22.833, 2.22, 0, 23.233, -1.473]}, {"Target": "Parameter", "Id": "fashipiaodai4", "Segments": [0, 0.328, 0, 0.3, -0.733, 0, 0.767, 0.92, 0, 1.133, -0.782, 0, 1.533, 0.619, 0, 2.167, -1.183, 0, 2.533, 2.038, 0, 2.733, 0.551, 0, 2.9, 1.165, 0, 3.167, -8.107, 0, 3.5, 11.801, 0, 3.833, -9.474, 0, 4.2, 5.169, 0, 4.533, -2.415, 0, 4.867, 1.385, 0, 5.167, -0.604, 0, 5.4, 0.12, 0, 5.633, -0.596, 0, 5.933, 0.999, 0, 6.267, -0.914, 0, 6.633, 0.613, 0, 7, -0.317, 0, 7.433, 0.139, 0, 7.9, -0.077, 0, 8.333, 0.044, 0, 8.8, -0.42, 0, 9.133, 0.603, 0, 9.567, -1.689, 0, 9.933, 4.324, 0, 10.3, -5.722, 0, 10.633, 5.011, 0, 11, -3.556, 0, 11.367, 2.138, 0, 11.767, -0.714, 0, 12.167, 0.62, 0, 12.533, -1.755, 0, 12.933, 2.412, 0, 13.3, -2.336, 0, 13.7, 1.908, 0, 14.067, -1.003, 0, 14.6, 0.304, 0, 15.033, -0.522, 0, 15.467, 0.755, 0, 15.833, -0.456, 0, 16.167, 0.447, 0, 16.5, -0.606, 0, 16.833, 0.318, 0, 17.2, -0.394, 0, 17.567, 0.688, 0, 17.933, -0.381, 0, 18.233, 0.341, 0, 18.567, -0.549, 0, 18.9, 0.312, 0, 19.3, -0.659, 0, 19.7, 1.74, 0, 20.133, -2.248, 0, 20.533, 2.371, 0, 20.9, -1.914, 0, 21.267, 1.13, 0, 21.767, -1.196, 0, 22.167, 2.874, 0, 22.567, -3.905, 0, 22.933, 3.675, 1, 23.033, 3.675, 23.133, -0.619, 23.233, -2.181]}, {"Target": "Parameter", "Id": "fashipiaodai5", "Segments": [0, 0.892, 0, 0.3, -1.077, 0, 0.833, 1.177, 0, 1.233, -1.235, 0, 1.6, 1.083, 0, 2.233, -1.386, 0, 2.633, 2.72, 0, 2.9, 0.166, 0, 2.967, 0.256, 0, 3.267, -8.565, 0, 3.567, 15.139, 0, 3.933, -14.494, 0, 4.3, 9.618, 0, 4.633, -5.643, 0, 4.933, 3.404, 0, 5.267, -1.8, 0, 5.533, 0.552, 0, 5.767, -0.616, 0, 6.067, 1.325, 0, 6.367, -1.49, 0, 6.733, 1.188, 0, 7.067, -0.723, 0, 7.433, 0.337, 0, 7.867, -0.128, 0, 8.367, 0.065, 0, 8.867, -0.483, 0, 9.233, 0.846, 0, 9.633, -2.12, 0, 10, 5.525, 0, 10.367, -8.176, 0, 10.733, 8.02, 0, 11.1, -6.352, 0, 11.433, 4.229, 0, 11.8, -1.981, 0, 12.2, 1.154, 0, 12.6, -2.299, 0, 13, 3.355, 0, 13.4, -3.611, 0, 13.767, 3.25, 0, 14.133, -2.046, 0, 14.533, 0.675, 0, 15.133, -0.63, 0, 15.533, 1.039, 0, 15.9, -0.801, 0, 16.267, 0.749, 0, 16.6, -0.948, 0, 16.967, 0.712, 0, 17.267, -0.737, 0, 17.667, 1, 0, 18.033, -0.708, 0, 18.333, 0.606, 0, 18.667, -0.841, 0, 19, 0.614, 0, 19.367, -0.946, 0, 19.8, 2.236, 0, 20.2, -3.117, 0, 20.6, 3.52, 0, 20.967, -3.186, 0, 21.333, 2.194, 0, 21.767, -1.643, 0, 22.233, 3.588, 0, 22.633, -5.499, 0, 23, 5.787, 1, 23.078, 5.787, 23.155, 1.49, 23.233, -1.636]}, {"Target": "Parameter", "Id": "fashipiaodai6", "Segments": [0, 1.928, 0, 0.333, -1.64, 0, 0.9, 1.423, 0, 1.3, -1.796, 0, 1.667, 1.784, 0, 2.067, -0.956, 0, 2.133, -0.861, 0, 2.333, -1.296, 0, 2.7, 3.536, 0, 3.333, -8.756, 0, 3.667, 18.416, 0, 4.033, -19.613, 0, 4.4, 15.023, 0, 4.733, -10.481, 0, 5.033, 7.066, 0, 5.367, -4.326, 0, 5.667, 1.932, 0, 5.933, -0.96, 0, 6.2, 1.655, 0, 6.5, -2.274, 0, 6.833, 2.108, 0, 7.167, -1.51, 0, 7.5, 0.846, 0, 7.867, -0.363, 0, 8.3, 0.092, 0, 8.967, -0.559, 0, 9.333, 1.133, 0, 9.7, -2.719, 0, 10.1, 6.922, 0, 10.433, -10.964, 0, 10.8, 11.931, 0, 11.167, -10.477, 0, 11.533, 7.797, 0, 11.9, -4.441, 0, 12.233, 2.622, 0, 12.667, -3.131, 0, 13.067, 4.561, 0, 13.467, -5.354, 0, 13.833, 5.232, 0, 14.2, -3.823, 0, 14.567, 1.842, 0, 14.967, -0.582, 0, 15.067, -0.553, 0, 15.2, -0.58, 0, 15.633, 1.358, 0, 16, -1.302, 0, 16.333, 1.263, 0, 16.667, -1.481, 0, 17.033, 1.054, 0, 17.367, -1.137, 0, 17.733, 1.519, 0, 18.1, -1.244, 0, 18.433, 1.069, 0, 18.767, -1.319, 0, 19.1, 1.108, 0, 19.433, -1.455, 0, 19.867, 2.925, 0, 20.267, -4.288, 0, 20.667, 5.075, 0, 21.067, -5.04, 0, 21.433, 3.943, 0, 21.8, -2.924, 0, 22.333, 4.342, 0, 22.7, -7.353, 0, 23.1, 8.54, 1, 23.144, 8.54, 23.189, 4.948, 23.233, 1.357]}, {"Target": "Parameter", "Id": "daimao1", "Segments": [0, -0.169, 0, 0.033, -0.174, 0, 0.433, 0.819, 0, 0.9, -0.09, 0, 1.2, 0.073, 0, 1.733, -1.033, 0, 2.167, 1.393, 0, 2.767, -3.996, 0, 3.133, 4.07, 0, 3.6, -2.357, 0, 4.333, 1.755, 0, 5.167, -0.88, 0, 5.567, 0.446, 0, 6, -0.364, 0, 6.4, 0.121, 0, 6.833, -0.154, 0, 7.3, 0.025, 0, 7.667, -0.06, 0, 8.133, 0.02, 0, 8.367, 0.006, 0, 8.8, 0.551, 0, 9.2, 0.192, 0, 9.5, 2.804, 0, 9.933, -3.976, 0, 10.367, 1.765, 0, 10.733, -0.583, 0, 11.133, 1.225, 0, 12.067, -1.686, 0, 12.6, 2.539, 0, 13.033, -0.87, 0, 13.4, 0.475, 0, 13.9, -1.271, 0, 14.467, 0.291, 0, 14.6, 0.277, 0, 15.067, 1.066, 0, 16.067, -0.966, 0, 17.167, 1.069, 0, 18.1, -1.008, 0, 19.267, 1.769, 0, 19.8, -2.384, 0, 20.233, 0.969, 0, 20.633, -0.791, 0, 21.1, 0.535, 0, 21.433, 0.102, 0, 21.733, 2.045, 0, 22.2, -3.036, 0, 22.633, 1.449, 0, 23.033, -0.789, 1, 23.1, -0.789, 23.166, -0.253, 23.233, 0.175]}, {"Target": "Parameter", "Id": "daimao2", "Segments": [0, -0.084, 0, 0.167, -0.394, 0, 0.6, 1.583, 0, 1, 0.498, 0, 1.333, 0.874, 0, 1.933, -1.684, 0, 2.333, 2.389, 0, 2.6, -0.015, 0, 2.633, 0.039, 0, 2.933, -7.664, 0, 3.3, 6.676, 0, 3.667, -3.73, 0, 4.567, 2.372, 0, 5.367, -0.989, 0, 5.733, 0.723, 0, 6.133, -0.734, 0, 6.533, 0.073, 0, 6.967, -0.428, 0, 7.4, -0.147, 0, 7.767, -0.289, 0, 8.3, -0.139, 0, 8.533, -0.221, 0, 9, 0.979, 0, 9.333, 0.143, 0, 9.7, 6.416, 0, 10.1, -5.599, 0, 10.5, 2.898, 0, 10.867, -1.509, 0, 11.3, 2.508, 0, 11.833, -0.555, 0, 11.9, -0.548, 0, 12.3, -3.367, 0, 12.767, 3.47, 0, 13.167, -1.056, 0, 13.533, 1.559, 0, 14.1, -1.944, 0, 14.567, -0.483, 0, 14.733, -0.552, 0, 15.3, 1.808, 0, 16.3, -1.733, 0, 17.4, 1.79, 0, 18.367, -1.681, 0, 19.467, 3.345, 0, 19.967, -3.219, 0, 20.367, 1.402, 0, 20.767, -1.832, 0, 21.233, 0.393, 0, 21.567, -0.514, 0, 21.933, 4.31, 0, 22.367, -4.483, 0, 22.767, 2.238, 0, 23.167, -1.792, 1, 23.189, -1.792, 23.211, -1.612, 23.233, -1.371]}, {"Target": "Parameter", "Id": "daimao3", "Segments": [0, 0.038, 0, 0.3, -0.606, 0, 0.7, 0.748, 0, 1.067, -0.499, 0, 1.467, 0.404, 0, 1.567, 0.366, 0, 1.7, 0.404, 0, 2.1, -1.563, 0, 2.433, 2.305, 0, 2.667, 0.167, 0, 2.8, 0.976, 0, 3.067, -6.067, 0, 3.4, 7.397, 0, 3.733, -4.668, 0, 4.067, 1.332, 0, 4.367, -0.72, 0, 4.7, 1.206, 0, 5.033, -0.665, 0, 5.3, 0.249, 0, 5.533, -0.698, 0, 5.833, 0.934, 0, 6.2, -0.733, 0, 6.567, 0.429, 0, 6.967, -0.216, 0, 7.433, 0.112, 0, 7.867, -0.073, 0, 8.267, 0.036, 0, 8.7, -0.329, 0, 9.067, 0.362, 0, 9.5, -1.924, 0, 9.833, 4.807, 0, 10.2, -5.692, 0, 10.567, 4.146, 0, 10.933, -2.751, 0, 11.333, 1.735, 0, 11.767, -0.547, 0, 12.1, 0.827, 0, 12.433, -2.073, 0, 12.867, 2.614, 0, 13.233, -2.191, 0, 13.633, 1.615, 0, 14.067, -0.749, 0, 14.567, 0.357, 0, 14.967, -0.587, 0, 15.4, 0.802, 0, 15.767, -0.328, 0, 16.067, 0.398, 0, 16.433, -0.585, 0, 16.767, 0.189, 0, 17.1, -0.423, 0, 17.5, 0.737, 0, 17.867, -0.246, 0, 18.133, 0.301, 0, 18.467, -0.549, 0, 18.8, 0.193, 0, 19.233, -0.774, 0, 19.633, 1.983, 0, 20.067, -2.444, 0, 20.433, 2.273, 0, 20.833, -1.609, 0, 21.233, 0.823, 0, 21.7, -1.527, 0, 22.1, 3.22, 0, 22.467, -4.036, 0, 22.833, 3.279, 0, 23.233, -2.187]}, {"Target": "Parameter", "Id": "daimao4", "Segments": [0, 0.303, 0, 0.333, -0.728, 0, 0.767, 1.028, 0, 1.133, -0.868, 0, 1.533, 0.705, 0, 2.167, -1.778, 0, 2.533, 3.146, 0, 2.8, -0.357, 0, 2.9, -0.175, 0, 3.167, -6.453, 0, 3.5, 10.342, 0, 3.833, -8.285, 0, 4.167, 3.994, 0, 4.467, -2.128, 0, 4.8, 2.142, 0, 5.133, -1.423, 0, 5.4, 0.608, 0, 5.667, -0.857, 0, 5.967, 1.365, 0, 6.3, -1.3, 0, 6.633, 0.89, 0, 7, -0.475, 0, 7.433, 0.211, 0, 7.9, -0.112, 0, 8.333, 0.062, 0, 8.8, -0.386, 0, 9.133, 0.534, 0, 9.567, -2.234, 0, 9.933, 6.127, 0, 10.267, -8.222, 0, 10.633, 7.07, 0, 11, -5.127, 0, 11.4, 3.292, 0, 11.767, -1.352, 0, 12.167, 1.242, 0, 12.533, -2.819, 0, 12.933, 3.685, 0, 13.3, -3.502, 0, 13.7, 2.851, 0, 14.067, -1.491, 0, 14.6, 0.468, 0, 15.033, -0.787, 0, 15.467, 1.132, 0, 15.833, -0.68, 0, 16.167, 0.666, 0, 16.5, -0.905, 0, 16.833, 0.474, 0, 17.2, -0.648, 0, 17.567, 1.041, 0, 17.933, -0.568, 0, 18.233, 0.51, 0, 18.567, -0.82, 0, 18.9, 0.466, 0, 19.3, -0.98, 0, 19.7, 2.589, 0, 20.133, -3.332, 0, 20.533, 3.519, 0, 20.9, -2.844, 0, 21.267, 1.68, 0, 21.767, -1.8, 0, 22.167, 4.268, 0, 22.567, -5.743, 0, 22.933, 5.38, 1, 23.033, 5.38, 23.133, -0.914, 23.233, -3.202]}, {"Target": "Parameter", "Id": "Param", "Segments": [0, -0.141, 0, 0.033, -0.145, 0, 0.433, 0.618, 0, 0.9, -0.058, 0, 1.2, 0.077, 0, 1.733, -0.696, 0, 2.167, 0.917, 0, 2.767, -3.391, 0, 3.133, 3.437, 0, 3.567, -1.943, 0, 4.3, 1.021, 0, 5.167, -0.59, 0, 5.567, 0.302, 0, 6, -0.246, 0, 6.4, 0.082, 0, 6.833, -0.104, 0, 7.3, 0.017, 0, 7.667, -0.04, 0, 8.133, 0.013, 2, 8.167, 0.013, 0, 8.367, 0.004, 0, 8.8, 0.466, 0, 9.2, 0.154, 0, 9.5, 1.914, 0, 9.933, -2.642, 0, 10.367, 1.173, 0, 10.733, -0.428, 0, 11.167, 0.773, 0, 12.067, -1.135, 0, 12.6, 1.687, 0, 13.033, -0.582, 0, 13.4, 0.313, 0, 13.9, -0.851, 0, 14.467, 0.189, 0, 14.6, 0.18, 0, 15.067, 0.707, 0, 16.067, -0.648, 0, 17.167, 0.71, 0, 18.1, -0.673, 0, 19.267, 1.18, 0, 19.8, -1.589, 0, 20.233, 0.648, 0, 20.633, -0.525, 0, 21.1, 0.356, 0, 21.433, 0.068, 0, 21.733, 1.364, 0, 22.2, -2.026, 0, 22.633, 0.968, 0, 23.033, -0.527, 1, 23.1, -0.527, 23.166, -0.169, 23.233, 0.117]}, {"Target": "Parameter", "Id": "Param2", "Segments": [0, -0.07, 0, 0.167, -0.319, 0, 0.6, 1.192, 0, 1, 0.383, 0, 1.367, 0.695, 0, 1.933, -1.068, 0, 2.333, 1.656, 0, 2.567, 0.044, 0, 2.633, 0.165, 0, 2.933, -6.589, 0, 3.3, 5.614, 0, 3.667, -3.168, 0, 4.6, 1.333, 0, 5.367, -0.703, 0, 5.733, 0.498, 0, 6.133, -0.494, 0, 6.533, 0.051, 0, 6.967, -0.287, 0, 7.4, -0.097, 0, 7.767, -0.193, 0, 8.3, -0.092, 0, 8.533, -0.161, 0, 9, 0.845, 0, 9.333, 0.22, 0, 9.7, 4.489, 0, 10.1, -3.611, 0, 10.5, 2.062, 0, 10.867, -0.975, 0, 11.3, 1.608, 0, 12.3, -2.252, 0, 12.767, 2.296, 0, 13.167, -0.72, 0, 13.533, 1.023, 0, 14.1, -1.316, 0, 14.567, -0.345, 0, 14.733, -0.391, 0, 15.3, 1.183, 0, 16.3, -1.18, 0, 17.4, 1.174, 0, 18.367, -1.138, 0, 19.467, 2.225, 0, 19.967, -2.156, 0, 20.367, 0.935, 0, 20.767, -1.221, 0, 21.233, 0.26, 0, 21.567, -0.342, 0, 21.933, 2.88, 0, 22.367, -2.998, 0, 22.767, 1.499, 0, 23.167, -1.198, 1, 23.189, -1.198, 23.211, -1.077, 23.233, -0.916]}, {"Target": "Parameter", "Id": "Param3", "Segments": [0, 0.032, 0, 0.3, -0.473, 0, 0.7, 0.566, 0, 1.067, -0.381, 0, 1.467, 0.301, 0, 1.6, 0.263, 0, 1.7, 0.272, 0, 2.067, -1.01, 0, 2.433, 1.457, 0, 2.633, 0.316, 0, 2.8, 1.144, 0, 3.067, -5.31, 0, 3.4, 6.283, 0, 3.733, -4.03, 0, 4.1, 1.353, 0, 4.4, -0.508, 0, 4.733, 0.661, 0, 5.067, -0.321, 0, 5.267, 0.134, 0, 5.533, -0.502, 0, 5.833, 0.653, 0, 6.2, -0.498, 0, 6.567, 0.288, 0, 7, -0.144, 0, 7.433, 0.076, 0, 7.867, -0.05, 0, 8.267, 0.024, 0, 8.7, -0.276, 0, 9.067, 0.309, 0, 9.5, -1.329, 0, 9.833, 3.26, 0, 10.2, -3.85, 0, 10.567, 2.823, 0, 10.933, -1.847, 0, 11.333, 1.116, 0, 11.767, -0.304, 0, 12.1, 0.506, 0, 12.433, -1.361, 0, 12.867, 1.736, 0, 13.233, -1.463, 0, 13.633, 1.078, 0, 14.067, -0.498, 0, 14.567, 0.237, 0, 14.967, -0.392, 0, 15.4, 0.536, 0, 15.767, -0.219, 0, 16.067, 0.266, 0, 16.433, -0.391, 0, 16.767, 0.127, 0, 17.1, -0.283, 0, 17.5, 0.492, 0, 17.867, -0.165, 0, 18.133, 0.201, 0, 18.467, -0.367, 0, 18.8, 0.129, 0, 19.233, -0.518, 0, 19.633, 1.327, 0, 20.067, -1.636, 0, 20.433, 1.521, 0, 20.833, -1.075, 0, 21.233, 0.548, 0, 21.7, -1.019, 0, 22.1, 2.157, 0, 22.467, -2.708, 0, 22.833, 2.204, 0, 23.233, -1.466]}, {"Target": "Parameter", "Id": "Param4", "Segments": [0, 0.253, 0, 0.333, -0.581, 0, 0.767, 0.779, 0, 1.133, -0.66, 0, 1.533, 0.533, 0, 2.167, -1.227, 0, 2.533, 2.054, 0, 2.767, 0.159, 0, 2.9, 0.405, 0, 3.167, -5.833, 0, 3.5, 8.866, 0, 3.833, -7.112, 0, 4.167, 3.63, 0, 4.5, -1.761, 0, 4.833, 1.356, 0, 5.133, -0.796, 0, 5.4, 0.293, 0, 5.667, -0.567, 0, 5.967, 0.939, 0, 6.3, -0.885, 0, 6.633, 0.602, 0, 7, -0.317, 0, 7.433, 0.14, 0, 7.9, -0.076, 0, 8.333, 0.042, 0, 8.8, -0.322, 0, 9.133, 0.455, 0, 9.567, -1.571, 0, 9.933, 4.195, 0, 10.267, -5.597, 0, 10.633, 4.864, 0, 11, -3.494, 0, 11.4, 2.173, 0, 11.767, -0.829, 0, 12.167, 0.746, 0, 12.533, -1.832, 0, 12.933, 2.444, 0, 13.3, -2.341, 0, 13.7, 1.907, 0, 14.067, -0.999, 0, 14.6, 0.309, 0, 15.033, -0.524, 0, 15.467, 0.755, 0, 15.833, -0.455, 0, 16.167, 0.445, 0, 16.5, -0.605, 0, 16.833, 0.263, 0, 17.2, -0.41, 0, 17.567, 0.689, 0, 17.933, -0.379, 0, 18.233, 0.34, 0, 18.567, -0.548, 0, 18.9, 0.311, 0, 19.3, -0.656, 0, 19.7, 1.734, 0, 20.133, -2.235, 0, 20.533, 2.36, 0, 20.9, -1.906, 0, 21.267, 1.126, 0, 21.767, -1.199, 0, 22.167, 2.862, 0, 22.567, -3.871, 0, 22.933, 3.635, 1, 23.033, 3.635, 23.133, -0.615, 23.233, -2.16]}, {"Target": "Parameter", "Id": "Param5", "Segments": [0, 0.681, 0, 0.333, -0.834, 0, 0.867, 1.001, 0, 1.233, -1.05, 0, 1.567, 0.903, 0, 2.267, -1.348, 0, 2.633, 2.747, 0, 3.267, -5.957, 0, 3.567, 11.322, 0, 3.933, -10.935, 0, 4.267, 7.015, 0, 4.6, -4.193, 0, 4.933, 2.939, 0, 5.233, -1.864, 0, 5.533, 0.849, 0, 5.8, -0.753, 0, 6.067, 1.303, 0, 6.4, -1.455, 0, 6.733, 1.167, 0, 7.067, -0.716, 0, 7.433, 0.342, 0, 7.867, -0.132, 0, 8.367, 0.063, 0, 8.867, -0.373, 0, 9.233, 0.64, 0, 9.633, -1.912, 0, 10, 5.301, 0, 10.367, -7.904, 0, 10.733, 7.717, 0, 11.1, -6.147, 0, 11.467, 4.192, 0, 11.833, -2.045, 0, 12.2, 1.318, 0, 12.6, -2.452, 0, 13, 3.424, 0, 13.4, -3.617, 0, 13.767, 3.236, 0, 14.133, -2.029, 0, 14.533, 0.672, 0, 15.133, -0.634, 0, 15.533, 1.04, 0, 15.9, -0.8, 0, 16.233, 0.747, 0, 16.6, -0.945, 0, 16.933, 0.718, 0, 17.267, -0.703, 0, 17.667, 0.99, 0, 18.033, -0.701, 0, 18.333, 0.603, 0, 18.667, -0.839, 0, 19, 0.612, 0, 19.367, -0.942, 0, 19.8, 2.221, 0, 20.2, -3.091, 0, 20.6, 3.49, 0, 20.967, -3.157, 0, 21.333, 2.174, 0, 21.767, -1.645, 0, 22.233, 3.574, 0, 22.633, -5.424, 0, 23, 5.672, 1, 23.078, 5.672, 23.155, 1.462, 23.233, -1.6]}, {"Target": "Parameter", "Id": "ZUOHOUFABG1", "Segments": [0, -0.092, 0, 0.033, -0.095, 0, 0.433, 0.546, 0, 0.9, -0.075, 0, 1.2, 0.016, 0, 1.733, -0.814, 0, 2.167, 1.132, 0, 2.767, -2.107, 0, 3.1, 2.186, 0, 3.633, -1.372, 0, 4.367, 1.652, 0, 4.967, -0.673, 0, 5.033, -0.661, 0, 5.167, -0.7, 0, 5.567, 0.35, 0, 6, -0.287, 0, 6.4, 0.094, 0, 6.833, -0.122, 0, 7.3, 0.019, 0, 7.667, -0.047, 0, 8.133, 0.015, 0, 8.367, 0.005, 0, 8.8, 0.294, 0, 9.2, 0.116, 0, 9.5, 2.175, 0, 9.933, -3.188, 0, 10.367, 1.411, 0, 10.733, -0.405, 0, 11.133, 1.054, 0, 11.733, -0.797, 0, 11.8, -0.791, 0, 12.067, -1.332, 0, 12.6, 2.038, 0, 13.033, -0.691, 0, 13.4, 0.384, 0, 13.9, -1.011, 0, 14.467, 0.24, 0, 14.6, 0.228, 0, 15.067, 0.858, 0, 16.067, -0.768, 0, 17.167, 0.859, 0, 18.1, -0.803, 0, 19.267, 1.414, 0, 19.8, -1.906, 0, 20.233, 0.77, 0, 20.633, -0.634, 0, 21.1, 0.429, 0, 21.433, 0.081, 0, 21.733, 1.635, 0, 22.2, -2.425, 0, 22.633, 1.155, 0, 23.033, -0.628, 1, 23.1, -0.628, 23.166, -0.201, 23.233, 0.14]}, {"Target": "Parameter", "Id": "ZUOHOUFABG2", "Segments": [0, -0.046, 0, 0.167, -0.23, 0, 0.6, 1.062, 0, 1.033, 0.319, 0, 1.333, 0.531, 0, 1.933, -1.43, 0, 2.333, 1.891, 0, 2.933, -3.902, 0, 3.267, 3.612, 0, 3.667, -1.956, 0, 3.9, -1.67, 0, 3.967, -1.694, 0, 4.567, 2.275, 0, 5.067, -0.172, 0, 5.133, -0.135, 0, 5.367, -0.727, 0, 5.733, 0.556, 0, 6.133, -0.58, 0, 6.533, 0.055, 0, 6.967, -0.34, 0, 7.4, -0.119, 0, 7.767, -0.231, 0, 8.3, -0.112, 0, 8.533, -0.156, 0, 9, 0.494, 0, 9.333, -0.073, 0, 9.7, 4.797, 0, 10.1, -4.62, 0, 10.5, 2.101, 0, 10.867, -1.243, 0, 11.3, 2.095, 0, 11.833, -0.437, 0, 11.9, -0.398, 0, 12.3, -2.675, 0, 12.767, 2.793, 0, 13.167, -0.815, 0, 13.533, 1.27, 0, 14.1, -1.523, 0, 14.567, -0.353, 0, 14.733, -0.407, 0, 15.3, 1.477, 0, 16.3, -1.348, 0, 17.4, 1.459, 0, 18.367, -1.316, 0, 19.467, 2.675, 0, 19.967, -2.555, 0, 20.367, 1.117, 0, 20.767, -1.464, 0, 21.233, 0.317, 0, 21.567, -0.412, 0, 21.933, 3.427, 0, 22.367, -3.557, 0, 22.767, 1.77, 0, 23.167, -1.425, 1, 23.189, -1.425, 23.211, -1.282, 23.233, -1.091]}, {"Target": "Parameter", "Id": "ZUOHOUFABG3", "Segments": [0, 0.02, 0, 0.333, -0.386, 0, 0.7, 0.498, 0, 1.067, -0.326, 0, 1.467, 0.275, 0, 1.567, 0.257, 0, 1.7, 0.319, 0, 2.1, -1.211, 0, 2.433, 1.708, 0, 2.7, -0.124, 0, 2.8, 0.029, 0, 3.067, -2.957, 0, 3.4, 3.863, 0, 3.733, -2.335, 0, 4.033, 0.496, 0, 4.3, -0.79, 0, 4.667, 1.225, 0, 5.033, -0.706, 0, 5.3, 0.258, 0, 5.533, -0.506, 0, 5.867, 0.715, 0, 6.2, -0.572, 0, 6.567, 0.339, 0, 6.967, -0.172, 0, 7.433, 0.088, 0, 7.867, -0.057, 0, 8.267, 0.028, 0, 8.7, -0.178, 0, 9.067, 0.187, 0, 9.5, -1.461, 0, 9.833, 3.729, 0, 10.2, -4.406, 0, 10.567, 3.166, 0, 10.933, -2.151, 0, 11.333, 1.438, 0, 11.733, -0.525, 0, 12.1, 0.724, 0, 12.433, -1.682, 0, 12.867, 2.088, 0, 13.233, -1.739, 0, 13.633, 1.284, 0, 14.067, -0.599, 0, 14.567, 0.286, 0, 14.967, -0.469, 0, 15.4, 0.639, 0, 15.767, -0.259, 0, 16.067, 0.316, 0, 16.433, -0.465, 0, 16.767, 0.149, 0, 17.1, -0.336, 0, 17.5, 0.586, 0, 17.867, -0.193, 0, 18.133, 0.239, 0, 18.467, -0.437, 0, 18.8, 0.153, 0, 19.233, -0.613, 0, 19.633, 1.57, 0, 20.067, -1.938, 0, 20.433, 1.801, 0, 20.833, -1.277, 0, 21.233, 0.656, 0, 21.7, -1.216, 0, 22.067, 2.551, 0, 22.467, -3.178, 0, 22.833, 2.569, 0, 23.233, -1.725]}, {"Target": "Parameter", "Id": "hounaofa1yaobai", "Segments": [0, 0, 2, 0.033, 0, 0, 0.333, 0.016, 0, 0.533, 0.014, 0, 0.767, 0.016, 2, 0.8, 0.016, 2, 0.833, 0.016, 0, 1.667, -0.009, 0, 2.067, 0.016, 0, 2.4, -0.001, 0, 2.467, 0, 0, 2.733, -0.16, 0, 2.967, 0.109, 0, 3.2, -0.033, 0, 3.467, 0.015, 0, 3.767, -0.028, 0, 4.367, 0.02, 0, 4.733, 0.006, 2, 4.767, 0.006, 0, 5.167, -0.012, 0, 5.367, 0.004, 0, 5.633, -0.003, 0, 5.867, -0.001, 0, 6.167, -0.003, 0, 6.367, -0.002, 0, 6.567, -0.003, 2, 6.6, -0.003, 2, 6.633, -0.003, 2, 6.767, -0.003, 2, 6.8, -0.003, 2, 6.833, -0.003, 2, 6.867, -0.003, 2, 7, -0.003, 2, 7.033, -0.003, 2, 7.067, -0.003, 2, 7.1, -0.003, 2, 7.233, -0.003, 2, 7.267, -0.003, 0, 7.3, -0.002, 0, 7.333, -0.003, 0, 7.467, -0.002, 2, 7.5, -0.002, 2, 7.533, -0.002, 2, 7.567, -0.002, 2, 7.7, -0.002, 2, 7.733, -0.002, 2, 7.767, -0.002, 2, 7.8, -0.002, 2, 7.933, -0.002, 2, 7.967, -0.002, 0, 8.667, 0.016, 2, 8.767, 0.016, 2, 8.8, 0.016, 2, 8.833, 0.016, 0, 9.433, 0.067, 0, 9.833, -0.015, 0, 10.067, 0.01, 0, 10.333, -0.001, 0, 10.933, 0.012, 0, 11.067, 0.008, 0, 11.267, 0.014, 0, 12, -0.033, 0, 12.567, 0.02, 0, 12.8, 0.005, 0, 13.033, 0.01, 0, 13.967, -0.017, 0, 15.033, 0.013, 0, 16.067, -0.017, 0, 17.133, 0.014, 0, 18.1, -0.017, 0, 19.233, 0.028, 0, 19.767, -0.019, 0, 20.033, -0.002, 0, 20.3, -0.009, 0, 20.567, -0.006, 2, 20.633, -0.006, 2, 20.667, -0.006, 2, 20.7, -0.006, 0, 21.667, 0.036, 0, 22.133, -0.022, 0, 22.4, 0, 0, 22.633, -0.007, 1, 22.833, -0.007, 23.033, 0.002, 23.233, 0.003]}, {"Target": "Parameter", "Id": "hounaifa1bianxing", "Segments": [0, 0, 2, 0.033, 0, 0, 0.2, -0.01, 0, 0.433, 0.009, 0, 0.667, -0.005, 0, 0.9, 0.003, 0, 1.1, 0, 2, 1.133, 0, 2, 1.2, 0, 0, 1.3, 0.001, 2, 1.333, 0.001, 0, 1.367, 0.002, 0, 1.433, 0.001, 0, 1.6, 0.009, 0, 1.833, -0.016, 0, 2.167, 0.017, 0, 2.433, -0.01, 0, 2.667, 0.141, 0, 2.867, -0.289, 0, 3.1, 0.276, 0, 3.333, -0.165, 0, 3.6, 0.089, 0, 3.867, -0.055, 0, 4.1, 0.02, 0, 4.333, -0.011, 0, 4.533, 0.013, 0, 4.767, -0.007, 0, 5, 0.007, 0, 5.267, -0.019, 0, 5.5, 0.016, 0, 5.733, -0.009, 0, 6, 0.004, 0, 6.267, -0.002, 0, 6.5, 0.001, 0, 6.767, 0, 2, 6.8, 0, 2, 6.833, 0, 2, 7.033, 0, 2, 7.067, 0, 2, 7.1, 0, 2, 7.233, 0, 2, 7.267, 0, 2, 7.3, 0, 2, 7.333, 0, 2, 7.433, 0, 2, 7.5, 0, 2, 7.533, 0, 2, 7.567, 0, 2, 7.7, 0, 2, 7.733, 0, 2, 7.767, 0, 2, 7.8, 0, 2, 7.933, 0, 2, 7.967, 0, 2, 8.1, 0, 2, 8.2, 0, 0, 8.533, -0.01, 0, 8.767, 0.008, 0, 9, -0.005, 0, 9.2, 0.002, 0, 9.333, -0.033, 0, 9.567, 0.052, 0, 9.9, -0.04, 0, 10.167, 0.029, 0, 10.433, -0.016, 0, 10.667, 0.005, 0, 10.9, -0.003, 0, 11.067, 0.005, 0, 11.233, -0.006, 0, 11.433, 0.008, 0, 11.7, -0.001, 0, 11.933, 0.013, 0, 12.167, -0.023, 0, 12.4, 0.004, 0, 12.533, -0.004, 0, 12.7, 0.017, 0, 12.933, -0.014, 0, 13.167, 0.009, 0, 13.433, -0.002, 0, 13.667, 0.003, 0, 14.033, -0.005, 0, 14.3, 0.001, 0, 14.6, -0.003, 0, 14.833, -0.002, 2, 14.9, -0.002, 0, 15.133, 0.005, 0, 15.4, -0.001, 0, 15.667, 0.004, 0, 16.133, -0.005, 0, 16.367, 0.001, 0, 16.633, -0.003, 0, 16.867, -0.002, 0, 17, -0.003, 0, 17.233, 0.006, 0, 17.5, -0.001, 0, 17.733, 0.004, 0, 18.167, -0.005, 0, 18.433, 0.002, 0, 18.7, -0.003, 0, 18.9, -0.002, 0, 19.133, -0.011, 0, 19.333, 0.016, 0, 19.6, -0.001, 0, 19.733, 0.004, 0, 19.9, -0.019, 0, 20.133, 0.016, 0, 20.4, -0.009, 0, 20.633, 0.005, 0, 20.9, -0.003, 0, 21.167, 0, 0, 21.567, -0.022, 0, 21.8, 0.032, 0, 22.033, -0.008, 0, 22.133, -0.003, 0, 22.267, -0.022, 0, 22.5, 0.019, 0, 22.767, -0.011, 0, 23, 0.004, 1, 23.078, 0.004, 23.155, -0.003, 23.233, -0.005]}, {"Target": "Parameter", "Id": "hounaofa2yaobai", "Segments": [0, 0, 2, 0.033, 0, 0, 0.333, 0.066, 0, 0.533, 0.057, 0, 0.767, 0.065, 0, 0.8, 0.063, 0, 0.833, 0.064, 0, 1.667, -0.034, 0, 2.067, 0.064, 0, 2.4, -0.003, 0, 2.467, -0.002, 0, 2.733, -0.641, 0, 2.967, 0.437, 0, 3.2, -0.131, 0, 3.467, 0.059, 0, 3.767, -0.112, 0, 4.367, 0.081, 0, 4.733, 0.024, 0, 4.767, 0.025, 0, 5.167, -0.047, 0, 5.367, 0.017, 0, 5.633, -0.014, 0, 5.867, -0.005, 0, 6.167, -0.01, 0, 6.367, -0.009, 0, 6.567, -0.01, 2, 6.6, -0.01, 0, 6.633, -0.011, 0, 6.767, -0.01, 0, 6.8, -0.011, 0, 6.833, -0.01, 0, 6.867, -0.011, 0, 7, -0.01, 0, 7.033, -0.011, 0, 7.067, -0.01, 0, 7.1, -0.011, 0, 7.233, -0.01, 2, 7.267, -0.01, 2, 7.3, -0.01, 2, 7.333, -0.01, 2, 7.467, -0.01, 2, 7.5, -0.01, 0, 7.533, -0.009, 0, 7.567, -0.01, 0, 7.7, -0.009, 2, 7.733, -0.009, 2, 7.767, -0.009, 2, 7.8, -0.009, 0, 7.933, -0.008, 2, 7.967, -0.008, 0, 8.667, 0.065, 0, 8.767, 0.063, 0, 8.8, 0.064, 2, 8.833, 0.064, 0, 9.433, 0.268, 0, 9.833, -0.062, 0, 10.067, 0.039, 0, 10.333, -0.004, 0, 10.933, 0.046, 0, 11.067, 0.033, 0, 11.267, 0.055, 0, 12, -0.133, 0, 12.567, 0.079, 0, 12.8, 0.019, 0, 13.033, 0.038, 0, 13.967, -0.067, 0, 15.033, 0.052, 0, 16.067, -0.07, 0, 17.133, 0.057, 0, 18.1, -0.067, 0, 19.233, 0.114, 0, 19.767, -0.078, 0, 20.033, -0.007, 0, 20.3, -0.036, 0, 20.567, -0.023, 2, 20.633, -0.023, 2, 20.667, -0.023, 0, 20.7, -0.024, 0, 21.667, 0.143, 0, 22.133, -0.09, 0, 22.4, 0, 0, 22.633, -0.029, 1, 22.833, -0.029, 23.033, 0.007, 23.233, 0.014]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.033, 0, 0, 0.333, 0.016, 0, 0.533, 0.014, 0, 0.767, 0.016, 2, 0.8, 0.016, 2, 0.833, 0.016, 0, 1.667, -0.009, 0, 2.067, 0.016, 0, 2.4, -0.001, 0, 2.467, 0, 0, 2.733, -0.16, 0, 2.967, 0.109, 0, 3.2, -0.033, 0, 3.467, 0.015, 0, 3.767, -0.028, 0, 4.367, 0.02, 0, 4.733, 0.006, 2, 4.767, 0.006, 0, 5.167, -0.012, 0, 5.367, 0.004, 0, 5.633, -0.003, 0, 5.867, -0.001, 0, 6.167, -0.003, 0, 6.367, -0.002, 0, 6.567, -0.003, 2, 6.6, -0.003, 2, 6.633, -0.003, 2, 6.767, -0.003, 2, 6.8, -0.003, 2, 6.833, -0.003, 2, 6.867, -0.003, 2, 7, -0.003, 2, 7.033, -0.003, 2, 7.067, -0.003, 2, 7.1, -0.003, 2, 7.233, -0.003, 2, 7.267, -0.003, 0, 7.3, -0.002, 0, 7.333, -0.003, 0, 7.467, -0.002, 2, 7.5, -0.002, 2, 7.533, -0.002, 2, 7.567, -0.002, 2, 7.7, -0.002, 2, 7.733, -0.002, 2, 7.767, -0.002, 2, 7.8, -0.002, 2, 7.933, -0.002, 2, 7.967, -0.002, 0, 8.667, 0.016, 2, 8.767, 0.016, 2, 8.8, 0.016, 2, 8.833, 0.016, 0, 9.433, 0.067, 0, 9.833, -0.015, 0, 10.067, 0.01, 0, 10.333, -0.001, 0, 10.933, 0.012, 0, 11.067, 0.008, 0, 11.267, 0.014, 0, 12, -0.033, 0, 12.567, 0.02, 0, 12.8, 0.005, 0, 13.033, 0.01, 0, 13.967, -0.017, 0, 15.033, 0.013, 0, 16.067, -0.017, 0, 17.133, 0.014, 0, 18.1, -0.017, 0, 19.233, 0.028, 0, 19.767, -0.019, 0, 20.033, -0.002, 0, 20.3, -0.009, 0, 20.567, -0.006, 2, 20.633, -0.006, 2, 20.667, -0.006, 2, 20.7, -0.006, 0, 21.667, 0.036, 0, 22.133, -0.022, 0, 22.4, 0, 0, 22.633, -0.007, 1, 22.833, -0.007, 23.033, 0.002, 23.233, 0.003]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.033, 0, 0, 0.4, 0.016, 0, 0.633, 0.014, 0, 0.933, 0.015, 0, 1.733, -0.004, 0, 2.1, 0.014, 0, 2.5, -0.003, 2, 2.533, -0.003, 0, 2.733, -0.154, 0, 3.033, 0.09, 0, 3.333, -0.028, 0, 3.567, 0.009, 0, 3.867, -0.017, 0, 4.333, 0.015, 0, 4.733, 0.002, 2, 4.767, 0.002, 0, 5.167, -0.008, 0, 5.433, 0.003, 0, 5.733, -0.003, 0, 6.033, -0.001, 0, 6.4, -0.002, 2, 6.533, -0.002, 2, 6.567, -0.002, 2, 6.6, -0.002, 2, 6.8, -0.002, 2, 6.833, -0.002, 2, 6.9, -0.002, 2, 7, -0.002, 2, 7.033, -0.002, 2, 7.067, -0.002, 2, 7.1, -0.002, 2, 7.233, -0.002, 2, 7.267, -0.002, 2, 7.3, -0.002, 2, 7.333, -0.002, 2, 7.467, -0.002, 2, 7.5, -0.002, 2, 7.533, -0.002, 2, 7.567, -0.002, 2, 7.7, -0.002, 2, 7.733, -0.002, 2, 7.767, -0.002, 2, 7.8, -0.002, 0, 8.8, 0.019, 0, 8.933, 0.018, 0, 9.467, 0.055, 0, 9.867, -0.009, 0, 10.133, 0.012, 0, 10.433, 0.002, 0, 10.9, 0.008, 0, 11.1, 0.004, 0, 11.333, 0.01, 0, 12.067, -0.025, 0, 12.567, 0.015, 0, 12.867, 0.003, 0, 13.133, 0.006, 0, 14, -0.014, 0, 15.1, 0.009, 0, 16.067, -0.014, 0, 17.167, 0.01, 0, 18.133, -0.014, 0, 19.233, 0.021, 0, 19.8, -0.015, 0, 20.1, -0.002, 0, 20.4, -0.007, 0, 21.7, 0.026, 0, 22.167, -0.018, 0, 22.467, 0.001, 0, 22.733, -0.005, 1, 22.9, -0.005, 23.066, 0.002, 23.233, 0.004]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.033, 0, 0, 0.233, -0.005, 0, 0.5, 0.004, 0, 0.767, -0.002, 0, 1.133, 0.002, 0, 1.433, 0, 0, 1.633, 0.004, 0, 1.9, -0.007, 0, 2.233, 0.01, 0, 2.533, -0.006, 0, 2.7, 0.07, 0, 2.9, -0.149, 0, 3.167, 0.126, 0, 3.467, -0.074, 0, 3.767, 0.041, 0, 4.067, -0.024, 0, 4.4, 0.011, 0, 4.767, -0.006, 0, 5.067, 0.004, 0, 5.333, -0.007, 0, 5.6, 0.006, 0, 5.867, -0.003, 0, 6.167, 0.002, 0, 6.5, -0.001, 0, 6.8, 0, 2, 7.067, 0, 2, 7.267, 0, 2, 7.3, 0, 2, 7.367, 0, 2, 7.467, 0, 2, 7.5, 0, 2, 7.533, 0, 2, 7.567, 0, 2, 7.7, 0, 2, 7.733, 0, 2, 7.767, 0, 2, 7.8, 0, 2, 7.9, 0, 2, 7.967, 0, 2, 8.167, 0, 2, 8.2, 0, 0, 8.567, -0.006, 0, 8.833, 0.005, 0, 9.133, -0.003, 0, 9.2, -0.002, 0, 9.367, -0.013, 0, 9.667, 0.023, 0, 9.967, -0.024, 0, 10.267, 0.015, 0, 10.6, -0.008, 0, 10.933, 0.003, 2, 10.967, 0.003, 2, 11, 0.003, 0, 11.233, -0.005, 0, 11.533, 0.005, 0, 11.8, -0.001, 0, 11.967, 0.004, 0, 12.233, -0.01, 0, 12.7, 0.009, 0, 13, -0.006, 0, 13.3, 0.004, 0, 13.6, -0.001, 0, 13.867, 0.002, 0, 14.167, -0.002, 0, 14.433, 0, 0, 14.8, -0.002, 0, 15.2, 0.003, 0, 15.533, 0, 0, 15.833, 0.002, 0, 16.2, -0.003, 0, 16.467, 0.001, 0, 16.833, -0.002, 0, 17.3, 0.003, 0, 17.6, 0, 0, 17.933, 0.002, 0, 18.233, -0.003, 0, 18.533, 0.001, 0, 18.867, -0.002, 2, 19, -0.002, 0, 19.133, -0.004, 0, 19.433, 0.009, 0, 19.933, -0.008, 0, 20.233, 0.007, 0, 20.533, -0.004, 0, 20.833, 0.002, 0, 21.133, -0.001, 0, 21.433, 0, 0, 21.6, -0.009, 0, 21.9, 0.014, 0, 22.3, -0.014, 0, 22.6, 0.01, 0, 22.9, -0.006, 0, 23.133, 0.002, 1, 23.166, 0.002, 23.2, 0.001, 23.233, 0]}, {"Target": "Parameter", "Id": "ParamHairFront", "Segments": [0, 0, 2, 0.033, 0, 0, 0.4, 0.085, 0, 0.633, 0.071, 2, 0.667, 0.071, 2, 0.7, 0.071, 0, 0.767, 0.073, 2, 0.8, 0.073, 0, 0.833, 0.074, 0, 1.733, -0.064, 0, 2.1, 0.094, 0, 2.433, -0.013, 0, 2.533, -0.007, 0, 2.733, -0.596, 0, 3, 0.364, 0, 3.333, -0.11, 0, 3.567, 0.025, 0, 3.833, -0.157, 0, 4.367, 0.132, 0, 5.167, -0.071, 0, 5.467, 0.018, 0, 5.767, -0.02, 0, 6.067, -0.01, 0, 6.4, -0.016, 0, 6.533, -0.015, 0, 6.567, -0.016, 0, 6.6, -0.015, 2, 6.633, -0.015, 2, 6.7, -0.015, 0, 6.8, -0.016, 0, 6.833, -0.015, 0, 6.933, -0.016, 2, 7, -0.016, 2, 7.033, -0.016, 2, 7.067, -0.016, 2, 7.1, -0.016, 0, 7.233, -0.015, 2, 7.267, -0.015, 2, 7.3, -0.015, 2, 7.333, -0.015, 2, 7.467, -0.015, 2, 7.5, -0.015, 0, 7.533, -0.014, 2, 7.567, -0.014, 0, 7.7, -0.013, 2, 7.733, -0.013, 2, 7.767, -0.013, 2, 7.8, -0.013, 0, 8.8, 0.073, 0, 8.9, 0.071, 0, 9.467, 0.354, 0, 9.867, -0.147, 0, 10.167, 0.033, 0, 10.433, -0.024, 0, 10.967, 0.079, 0, 11.1, 0.067, 0, 11.267, 0.076, 0, 12.033, -0.194, 0, 12.567, 0.125, 0, 12.867, 0.031, 0, 13.133, 0.056, 0, 14, -0.098, 0, 15.1, 0.086, 0, 16.067, -0.104, 0, 17.167, 0.094, 0, 18.133, -0.102, 0, 19.267, 0.172, 0, 19.8, -0.117, 0, 20.1, -0.011, 0, 20.4, -0.053, 0, 21.7, 0.211, 0, 22.167, -0.147, 0, 22.467, 0.006, 0, 22.733, -0.042, 1, 22.9, -0.042, 23.066, 0.008, 23.233, 0.019]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, -0.001, 0, 0.267, -0.022, 0, 0.5, -0.01, 0, 0.833, -0.031, 0, 1.233, -0.015, 2, 1.267, -0.015, 2, 1.3, -0.015, 0, 1.333, -0.016, 0, 1.367, -0.015, 2, 1.4, -0.015, 0, 1.667, 0.014, 0, 2, -0.025, 0, 2.267, 0.022, 0, 2.533, -0.026, 0, 2.7, 0.19, 0, 2.933, -0.208, 0, 3.2, 0.2, 0, 3.5, -0.14, 0, 3.8, 0.116, 0, 4.133, -0.045, 0, 4.467, 0.015, 0, 4.8, -0.041, 0, 5.167, 0.027, 0, 5.367, -0.02, 0, 5.667, 0.021, 0, 5.967, -0.008, 0, 6.3, 0.011, 0, 6.6, 0.002, 0, 6.933, 0.007, 0, 7.233, 0.004, 2, 7.267, 0.004, 2, 7.3, 0.004, 0, 7.5, 0.005, 2, 7.533, 0.005, 2, 7.567, 0.005, 0, 7.933, 0.004, 2, 7.967, 0.004, 2, 8.167, 0.004, 2, 8.2, 0.004, 0, 8.633, -0.017, 0, 8.833, -0.01, 0, 9.4, -0.108, 0, 9.733, 0.028, 0, 10, -0.087, 0, 10.3, 0.064, 0, 10.633, -0.044, 0, 11, 0.003, 0, 11.3, -0.036, 0, 12, 0.044, 0, 12.3, 0.004, 0, 12.4, 0.007, 0, 12.533, -0.002, 0, 12.7, 0.012, 0, 13.033, -0.042, 0, 13.367, 0.007, 0, 13.6, -0.007, 0, 13.967, 0.026, 0, 14.2, 0.019, 0, 14.433, 0.023, 0, 15, -0.015, 0, 15.167, -0.011, 0, 15.467, -0.019, 0, 15.967, 0.021, 0, 16.167, 0.016, 0, 16.467, 0.029, 0, 17.1, -0.015, 0, 17.267, -0.012, 0, 17.533, -0.02, 0, 18.033, 0.021, 0, 18.233, 0.015, 0, 18.533, 0.028, 0, 19.2, -0.04, 0, 19.5, -0.001, 0, 19.667, -0.007, 0, 19.767, 0.003, 0, 19.933, -0.013, 0, 20.233, 0.039, 0, 20.567, -0.005, 0, 20.867, 0.02, 0, 21.3, -0.003, 0, 21.433, -0.002, 0, 21.633, -0.057, 0, 21.967, 0.016, 0, 22.3, -0.037, 0, 22.6, 0.052, 0, 22.933, -0.021, 0, 23.233, 0.013]}, {"Target": "Parameter", "Id": "Param6", "Segments": [0, -0.006, 2, 0.033, -0.006, 0, 0.433, 0.036, 0, 0.9, -0.005, 0, 1.2, 0.001, 0, 1.733, -0.054, 0, 2.167, 0.075, 0, 2.767, -0.14, 0, 3.1, 0.146, 0, 3.633, -0.091, 0, 4.367, 0.11, 0, 4.967, -0.045, 0, 5.033, -0.044, 0, 5.167, -0.047, 0, 5.567, 0.023, 0, 6, -0.019, 0, 6.4, 0.006, 0, 6.833, -0.008, 0, 7.3, 0.001, 0, 7.667, -0.003, 0, 8.133, 0.001, 0, 8.367, 0, 0, 8.8, 0.02, 0, 9.2, 0.008, 0, 9.5, 0.145, 0, 9.933, -0.213, 0, 10.367, 0.094, 0, 10.733, -0.027, 0, 11.133, 0.07, 0, 11.733, -0.053, 2, 11.8, -0.053, 0, 12.067, -0.089, 0, 12.6, 0.136, 0, 13.033, -0.046, 0, 13.4, 0.026, 0, 13.9, -0.067, 0, 14.467, 0.016, 0, 14.6, 0.015, 0, 15.067, 0.057, 0, 16.067, -0.051, 0, 17.167, 0.057, 0, 18.1, -0.054, 0, 19.267, 0.094, 0, 19.8, -0.127, 0, 20.233, 0.051, 0, 20.633, -0.042, 0, 21.1, 0.029, 0, 21.433, 0.005, 0, 21.733, 0.109, 0, 22.2, -0.162, 0, 22.633, 0.077, 0, 23.033, -0.042, 1, 23.1, -0.042, 23.166, -0.013, 23.233, 0.009]}, {"Target": "Parameter", "Id": "Param7", "Segments": [0, -0.003, 0, 0.167, -0.015, 0, 0.6, 0.071, 0, 1.033, 0.021, 0, 1.333, 0.035, 0, 1.933, -0.095, 0, 2.333, 0.126, 0, 2.933, -0.26, 0, 3.267, 0.241, 0, 3.667, -0.13, 0, 3.9, -0.111, 0, 3.967, -0.113, 0, 4.567, 0.152, 0, 5.067, -0.011, 0, 5.133, -0.009, 0, 5.367, -0.048, 0, 5.733, 0.037, 0, 6.133, -0.039, 0, 6.533, 0.004, 0, 6.967, -0.023, 0, 7.4, -0.008, 0, 7.767, -0.015, 0, 8.3, -0.007, 0, 8.533, -0.01, 0, 9, 0.033, 0, 9.333, -0.005, 0, 9.7, 0.32, 0, 10.1, -0.308, 0, 10.5, 0.14, 0, 10.867, -0.083, 0, 11.3, 0.14, 0, 11.833, -0.029, 0, 11.9, -0.027, 0, 12.3, -0.178, 0, 12.767, 0.186, 0, 13.167, -0.054, 0, 13.533, 0.085, 0, 14.1, -0.102, 0, 14.567, -0.024, 0, 14.733, -0.027, 0, 15.3, 0.098, 0, 16.3, -0.09, 0, 17.4, 0.097, 0, 18.367, -0.088, 0, 19.467, 0.178, 0, 19.967, -0.17, 0, 20.367, 0.074, 0, 20.767, -0.098, 0, 21.233, 0.021, 0, 21.567, -0.027, 0, 21.933, 0.228, 0, 22.367, -0.237, 0, 22.767, 0.118, 0, 23.167, -0.095, 1, 23.189, -0.095, 23.211, -0.085, 23.233, -0.073]}, {"Target": "Parameter", "Id": "Param8", "Segments": [0, 0.001, 0, 0.333, -0.026, 0, 0.7, 0.033, 0, 1.067, -0.022, 0, 1.467, 0.018, 0, 1.567, 0.017, 0, 1.7, 0.021, 0, 2.1, -0.081, 0, 2.433, 0.114, 0, 2.7, -0.008, 0, 2.8, 0.002, 0, 3.067, -0.197, 0, 3.4, 0.258, 0, 3.733, -0.156, 0, 4.033, 0.033, 0, 4.3, -0.053, 0, 4.667, 0.082, 0, 5.033, -0.047, 0, 5.3, 0.017, 0, 5.533, -0.034, 0, 5.867, 0.048, 0, 6.2, -0.038, 0, 6.567, 0.023, 0, 6.967, -0.011, 0, 7.433, 0.006, 0, 7.867, -0.004, 0, 8.267, 0.002, 0, 8.7, -0.012, 0, 9.067, 0.012, 0, 9.5, -0.097, 0, 9.833, 0.249, 0, 10.2, -0.294, 0, 10.567, 0.211, 0, 10.933, -0.143, 0, 11.333, 0.096, 0, 11.733, -0.035, 0, 12.1, 0.048, 0, 12.433, -0.112, 0, 12.867, 0.139, 0, 13.233, -0.116, 0, 13.633, 0.086, 0, 14.067, -0.04, 0, 14.567, 0.019, 0, 14.967, -0.031, 0, 15.4, 0.043, 0, 15.767, -0.017, 0, 16.067, 0.021, 0, 16.433, -0.031, 0, 16.767, 0.01, 0, 17.1, -0.022, 0, 17.5, 0.039, 0, 17.867, -0.013, 0, 18.133, 0.016, 0, 18.467, -0.029, 0, 18.8, 0.01, 0, 19.233, -0.041, 0, 19.633, 0.105, 0, 20.067, -0.129, 0, 20.433, 0.12, 0, 20.833, -0.085, 0, 21.233, 0.044, 0, 21.7, -0.081, 0, 22.067, 0.17, 0, 22.467, -0.212, 0, 22.833, 0.171, 0, 23.233, -0.115]}, {"Target": "Parameter", "Id": "Param9", "Segments": [0, 0.011, 0, 0.367, -0.029, 0, 0.8, 0.046, 0, 1.133, -0.038, 0, 1.5, 0.031, 0, 2.167, -0.098, 0, 2.533, 0.161, 0, 2.867, -0.058, 0, 2.9, -0.057, 0, 3.167, -0.194, 0, 3.5, 0.352, 0, 3.833, -0.279, 0, 4.133, 0.12, 0, 4.433, -0.097, 0, 4.767, 0.129, 0, 5.1, -0.09, 0, 5.4, 0.043, 0, 5.667, -0.046, 0, 5.967, 0.07, 0, 6.3, -0.067, 0, 6.633, 0.047, 0, 7.033, -0.025, 0, 7.433, 0.011, 0, 7.9, -0.006, 0, 8.333, 0.003, 0, 8.8, -0.014, 0, 9.133, 0.019, 0, 9.567, -0.11, 0, 9.9, 0.311, 0, 10.267, -0.419, 0, 10.633, 0.352, 0, 11, -0.259, 0, 11.4, 0.175, 0, 11.8, -0.079, 0, 12.167, 0.074, 0, 12.533, -0.154, 0, 12.933, 0.196, 0, 13.3, -0.184, 0, 13.7, 0.15, 0, 14.067, -0.078, 0, 14.6, 0.025, 0, 15.033, -0.042, 0, 15.467, 0.06, 0, 15.833, -0.036, 0, 16.167, 0.035, 0, 16.5, -0.048, 0, 16.833, 0.025, 0, 17.2, -0.034, 0, 17.567, 0.055, 0, 17.933, -0.03, 0, 18.233, 0.027, 0, 18.567, -0.043, 0, 18.9, 0.025, 0, 19.3, -0.052, 0, 19.7, 0.136, 0, 20.133, -0.175, 0, 20.533, 0.185, 0, 20.9, -0.15, 0, 21.267, 0.088, 0, 21.767, -0.096, 0, 22.167, 0.224, 0, 22.567, -0.298, 0, 22.933, 0.278, 1, 23.033, 0.278, 23.133, -0.048, 23.233, -0.166]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 1, 2, 2, 1, 2, 3.667, 1, 0, 4.6, 0.33, 1, 6.433, 0.33, 8.267, 0.355, 10.1, 0.439, 1, 10.389, 0.452, 10.678, 1, 10.967, 1, 2, 22.233, 1, 0, 23, 0, 2, 23.233, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, -0.198, 0, 1, -0.6, 1, 1.333, -0.6, 1.667, -0.52, 2, -0.402, 1, 2.189, -0.335, 2.378, -0.318, 2.567, -0.318, 0, 3.633, -0.982, 1, 3.644, -0.982, 3.656, -1.003, 3.667, -0.976, 1, 3.789, -0.674, 3.911, 0, 4.033, 0, 2, 4.6, 0, 2, 10.1, 0, 2, 10.533, 0, 0, 10.967, -1, 2, 22, -1, 1, 22.156, -1, 22.311, -0.971, 22.467, -0.9, 1, 22.478, -0.895, 22.489, 0, 22.5, 0, 2, 22.667, 0, 2, 23.233, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0.838, 0, 1, 0.958, 1, 1.333, 0.958, 1.667, 0.951, 2, 0.923, 1, 2.189, 0.908, 2.378, 0.906, 2.567, 0.854, 1, 2.822, 0.784, 3.078, -0.5, 3.333, -0.5, 0, 3.433, 0, 2, 4.6, 0, 2, 10.1, 0, 2, 10.533, 0, 0, 10.967, -1, 0, 22.5, 0, 2, 22.667, 0, 2, 23.233, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON>ia<PERSON><PERSON>uo<PERSON>", "Segments": [0, 0, 2, 2, 0, 2, 2.567, 0, 0, 3.133, 1, 0, 22.567, 0, 2, 23.233, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 1, 2, 1, 1, 0, 2, 0.7, 0, 2.567, 1, 0, 3.433, -1, 0, 4.6, 0, 2, 10.1, 0, 2, 10.533, 0, 2, 22.467, 0, 2, 22.5, 0, 2, 22.667, 0, 2, 23.233, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 1, 0, 1, 0, 0, 1.967, 1, 0, 2.633, -0.5, 0, 3.667, 1, 2, 23.233, 1]}, {"Target": "Parameter", "Id": "zu<PERSON><PERSON><PERSON>ng", "Segments": [0, 1, 2, 2, 1, 2, 2.667, 1, 0, 2.733, 0, 2, 23.233, 0]}, {"Target": "Parameter", "Id": "xingfengyaobai", "Segments": [0, 0.92, 0, 0.1, 1.002, 1, 0.2, 1.002, 0.3, 1.002, 0.4, 1, 0, 0.5, 0.931, 0, 0.6, 1, 1, 0.7, 1.002, 0.8, 1.002, 0.9, 1.002, 1, 0.933, 1.002, 0.967, 1.076, 1, 0.92, 1, 1.178, 0.09, 1.355, -1, 1.533, -1, 0, 1.9, 0.501, 0, 2, 0.328, 2, 20.6, 0.328, 0, 20.833, -0.3, 0, 21.067, 0, 2, 21.167, 0, 2, 23.233, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 1, 2, 2, 1, 2, 20.6, 1, 2, 21.567, 1, 2, 22.067, 1, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 1.225, 0, 1.533, 1.5, 0, 2, 1.375, 2, 20.6, 1.375, 0, 21.567, 1, 2, 22.067, 1, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0.055, 0, 2, 0.031, 0, 2.5, 0.042, 1, 2.611, 0.042, 2.722, 0, 2.833, -0.008, 1, 3.211, -0.034, 3.589, -0.039, 3.967, -0.071, 1, 4.378, -0.106, 4.789, -0.175, 5.2, -0.175, 1, 6.244, -0.175, 7.289, -0.168, 8.333, -0.081, 1, 8.989, -0.027, 9.644, 0.126, 10.3, 0.126, 0, 11.8, 0, 0, 13.333, 0.106, 0, 15.367, 0, 0, 17.2, 0.089, 1, 17.678, 0.089, 18.155, 0.018, 18.633, 0.013, 1, 19.289, 0.006, 19.944, 0.007, 20.6, 0.007, 0, 21.567, 0.107, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON>biji<PERSON><PERSON>", "Segments": [0, -1, 2, 2, -1, 0, 4.367, -0.782, 0, 10.167, -1, 2, 18.733, -1, 2, 20.6, -1, 2, 20.767, -1, 1, 20.878, -1, 20.989, -0.64, 21.1, -0.489, 1, 21.422, -0.05, 21.745, 0.064, 22.067, 0.064, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 2, 0, 2, 10.633, 0, 0, 11.633, -0.1, 2, 20.933, -0.1, 1, 21.144, -0.1, 21.356, -0.395, 21.567, -0.636, 1, 21.834, -0.941, 22.1, -1, 22.367, -1, 1, 22.445, -1, 22.522, -0.422, 22.6, -0.321, 1, 22.811, -0.046, 23.022, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 2, 0, 2, 20.6, 0, 2, 21, 0, 0, 21.567, 1, 2, 23.233, 1]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, -0.257, 0, 0.333, 0.2, 0, 2, 0, 2, 20.6, 0, 0, 21.133, 0.6, 0, 22.067, -0.387, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, -1, 2, 2, -1, 2, 20.6, -1, 2, 22.067, -1, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "xiongshangxai", "Segments": [0, 0, 2, 0.033, 0, 0, 0.3, 0.028, 0, 0.433, 0.026, 0, 0.767, 0.032, 2, 0.8, 0.032, 2, 0.833, 0.032, 0, 0.867, 0.031, 0, 0.933, 0.032, 0, 0.967, 0.031, 0, 1, 0.032, 0, 1.033, 0.031, 2, 1.1, 0.031, 0, 1.367, 0.026, 2, 1.4, 0.026, 0, 1.9, -0.013, 0, 2.1, 0.033, 0, 2.367, -0.01, 0, 2.5, -0.007, 0, 2.7, -0.414, 0, 2.933, 0.28, 0, 3.167, -0.086, 0, 3.367, 0.037, 0, 3.567, -0.001, 0, 3.8, 0.02, 0, 4.567, -0.019, 0, 5.267, 0.012, 0, 5.5, -0.004, 0, 5.7, 0.001, 0, 5.933, 0, 2, 6.167, 0, 2, 6.4, 0, 2, 6.6, 0, 2, 6.833, 0, 2, 7.067, 0, 2, 7.267, 0, 2, 7.3, 0, 2, 7.4, 0, 2, 7.433, 0, 2, 7.467, 0, 2, 8.367, 0, 0, 8.633, 0.039, 0, 8.767, 0.037, 0, 9.133, 0.049, 0, 9.233, 0.048, 0, 9.3, 0.05, 0, 9.433, 0.049, 0, 9.467, 0.051, 0, 9.6, 0.048, 0, 9.633, 0.049, 0, 9.767, 0.045, 0, 9.8, 0.047, 0, 9.933, 0.043, 2, 9.967, 0.043, 0, 10.1, 0.037, 2, 10.133, 0.037, 0, 10.267, 0.031, 2, 10.3, 0.031, 0, 11.067, -0.019, 0, 11.267, 0.004, 0, 11.5, -0.004, 0, 11.7, -0.002, 0, 12.033, -0.003, 2, 12.1, -0.003, 0, 12.367, -0.004, 2, 12.4, -0.004, 2, 12.667, -0.004, 2, 12.7, -0.004, 0, 12.967, -0.005, 2, 13, -0.005, 2, 13.267, -0.005, 2, 13.3, -0.005, 2, 13.567, -0.005, 2, 13.6, -0.005, 0, 13.867, -0.006, 2, 13.9, -0.006, 2, 14.167, -0.006, 2, 14.2, -0.006, 2, 14.433, -0.006, 2, 14.467, -0.006, 2, 14.5, -0.006, 2, 14.733, -0.006, 2, 14.8, -0.006, 0, 15, -0.007, 0, 15.1, -0.006, 0, 15.3, -0.007, 0, 15.4, -0.006, 0, 15.6, -0.007, 0, 15.7, -0.006, 0, 15.867, -0.007, 0, 16, -0.006, 0, 16.167, -0.007, 0, 16.3, -0.006, 2, 16.467, -0.006, 2, 16.6, -0.006, 2, 16.767, -0.006, 2, 16.9, -0.006, 2, 17.033, -0.006, 2, 17.2, -0.006, 2, 17.333, -0.006, 0, 17.5, -0.005, 2, 17.633, -0.005, 2, 17.8, -0.005, 2, 17.9, -0.005, 2, 18.1, -0.005, 2, 18.2, -0.005, 0, 18.433, -0.004, 2, 18.467, -0.004, 0, 20.633, 0.001, 0, 20.867, -0.032, 0, 21.667, 0.043, 0, 22.4, -0.075, 0, 22.833, 0.06, 1, 22.966, 0.06, 23.1, -0.004, 23.233, -0.023]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.033, 0, 0, 0.3, 0.014, 0, 0.433, 0.013, 0, 0.767, 0.016, 2, 0.8, 0.016, 2, 0.833, 0.016, 2, 0.867, 0.016, 2, 0.933, 0.016, 2, 0.967, 0.016, 2, 1, 0.016, 0, 1.033, 0.015, 0, 1.1, 0.016, 0, 1.367, 0.013, 2, 1.4, 0.013, 0, 1.9, -0.007, 0, 2.1, 0.016, 0, 2.367, -0.005, 0, 2.5, -0.004, 0, 2.7, -0.207, 0, 2.933, 0.14, 0, 3.167, -0.043, 0, 3.367, 0.019, 0, 3.567, -0.001, 0, 3.8, 0.01, 0, 4.567, -0.009, 0, 5.267, 0.006, 0, 5.5, -0.002, 0, 5.7, 0.001, 0, 5.933, 0, 2, 6.167, 0, 2, 6.4, 0, 2, 6.6, 0, 2, 6.833, 0, 2, 7.067, 0, 2, 7.267, 0, 2, 7.3, 0, 2, 7.4, 0, 2, 7.433, 0, 2, 7.467, 0, 2, 8.367, 0, 0, 8.633, 0.02, 0, 8.767, 0.018, 0, 9.133, 0.024, 2, 9.233, 0.024, 0, 9.3, 0.025, 0, 9.433, 0.024, 0, 9.467, 0.025, 0, 9.6, 0.024, 0, 9.633, 0.025, 0, 9.767, 0.023, 2, 9.8, 0.023, 0, 9.933, 0.021, 0, 9.967, 0.022, 0, 10.1, 0.019, 2, 10.133, 0.019, 0, 10.267, 0.015, 2, 10.3, 0.015, 0, 11.067, -0.009, 0, 11.267, 0.002, 0, 11.5, -0.002, 0, 11.7, -0.001, 0, 12.033, -0.002, 2, 12.1, -0.002, 2, 12.367, -0.002, 2, 12.4, -0.002, 2, 12.667, -0.002, 2, 12.7, -0.002, 2, 12.967, -0.002, 2, 13, -0.002, 0, 13.267, -0.003, 0, 13.3, -0.002, 0, 13.567, -0.003, 2, 13.6, -0.003, 2, 13.867, -0.003, 2, 13.9, -0.003, 2, 14.167, -0.003, 2, 14.2, -0.003, 2, 14.433, -0.003, 2, 14.467, -0.003, 2, 14.5, -0.003, 2, 14.733, -0.003, 2, 14.8, -0.003, 2, 15, -0.003, 2, 15.1, -0.003, 2, 15.3, -0.003, 2, 15.4, -0.003, 2, 15.6, -0.003, 2, 15.7, -0.003, 2, 15.867, -0.003, 2, 16, -0.003, 2, 16.167, -0.003, 2, 16.3, -0.003, 2, 16.467, -0.003, 2, 16.6, -0.003, 2, 16.767, -0.003, 2, 16.9, -0.003, 2, 17.033, -0.003, 2, 17.2, -0.003, 2, 17.333, -0.003, 2, 17.5, -0.003, 2, 17.633, -0.003, 0, 17.8, -0.002, 0, 17.9, -0.003, 0, 18.1, -0.002, 2, 18.2, -0.002, 2, 18.433, -0.002, 2, 18.467, -0.002, 0, 20.633, 0, 0, 20.867, -0.016, 0, 21.667, 0.022, 0, 22.4, -0.038, 0, 22.833, 0.03, 1, 22.966, 0.03, 23.1, -0.002, 23.233, -0.011]}, {"Target": "Parameter", "Id": "youxiabifa", "Segments": [0, 1, 2, 2, 1, 2, 20.6, 1, 2, 21.7, 1, 0, 22.4, 0, 2, 23.233, 0]}, {"Target": "Parameter", "Id": "op", "Segments": [0, 0.7, 0, 2, 0.622, 2, 20.6, 0.622, 0, 21.333, 0.606, 0, 21.867, 1, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 0, 0, 0.6, 1, 2, 1.1, 1, 2, 1.533, 1, 0, 2, 0, 1, 2.289, 0, 2.578, 0.897, 2.867, 1, 1, 3.411, 1.193, 3.956, 1.2, 4.5, 1.2, 2, 5.833, 1.2, 0, 6.3, 0, 2, 7.4, 0, 2, 8.833, 0, 0, 9.733, 1.2, 1, 10.989, 1.2, 12.244, 1.171, 13.5, 1.1, 1, 13.589, 1.095, 13.678, 0, 13.767, 0, 0, 14.167, 1, 2, 17.5, 1, 2, 18.433, 1, 0, 19.267, 0, 2, 21.767, 0, 0, 22.433, 1, 2, 23.233, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 2, 0.6, 0, 0, 1.1, 0.2, 1, 1.4, 0.2, 1.7, 0.103, 2, 0.091, 1, 3.8, 0.022, 5.6, 0, 7.4, 0, 0, 12, 0.2, 2, 17.5, 0.2, 2, 18.433, 0.2, 0, 19.267, 1, 2, 19.733, 1, 2, 20.6, 1, 0, 21.4, 0, 2, 23.233, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 0, 0, 0.6, 1, 2, 1.1, 1, 2, 1.533, 1, 0, 2, 0, 1, 2.289, 0, 2.578, 0.897, 2.867, 1, 1, 3.411, 1.193, 3.956, 1.2, 4.5, 1.2, 2, 5.833, 1.2, 0, 6.3, 0, 2, 7.4, 0, 2, 8.833, 0, 0, 9.733, 1.2, 1, 10.989, 1.2, 12.244, 1.171, 13.5, 1.1, 1, 13.589, 1.095, 13.678, 0, 13.767, 0, 0, 14.167, 1, 2, 17.5, 1, 2, 18.433, 1, 0, 19.267, 0, 2, 21.767, 0, 0, 22.433, 1, 2, 23.233, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 2, 0.6, 0, 0, 1.1, 0.2, 1, 1.4, 0.2, 1.7, 0.103, 2, 0.091, 1, 3.8, 0.022, 5.6, 0, 7.4, 0, 0, 12, 0.2, 2, 17.5, 0.2, 2, 18.433, 0.2, 0, 19.267, 1, 2, 19.733, 1, 2, 20.6, 1, 0, 21.4, 0, 2, 23.233, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 1, 2, 1.167, 1, 0, 1.533, -1, 0, 2, 1, 0, 3.6, -1, 2, 9.6, -1, 0, 10.133, 1, 2, 18, 1, 0, 18.567, -1, 2, 20.6, -1, 2, 20.667, -1, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, -1, 0, 1.267, 0, 2, 2, 0, 2, 9.933, 0, 0, 10.333, -1, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON>kongda<PERSON><PERSON>", "Segments": [0, 0, 0, 1.833, 1, 2, 2, 1, 0, 2.167, -1, 0, 2.333, 0.5, 0, 2.467, -0.2, 1, 2.511, -0.2, 2.556, -0.013, 2.6, 0, 1, 4.911, 0.678, 7.222, 1, 9.533, 1, 2, 9.7, 1, 0, 9.867, -1, 0, 10.033, 0.5, 0, 10.167, -0.2, 1, 10.211, -0.2, 10.256, -0.028, 10.3, 0, 1, 11.4, 0.688, 12.5, 1, 13.6, 1, 2, 13.767, 1, 0, 13.933, -1, 0, 14.1, 0.5, 0, 14.233, -0.2, 0, 14.367, 0, 2, 20.6, 0, 2, 23.233, 0]}, {"Target": "Parameter", "Id": "gaoguangdaxia<PERSON>", "Segments": [0, 0, 2, 0.6, 0, 0, 0.933, 1, 0, 1.167, 0, 0, 1.533, 0.9, 0, 1.8, 0, 2, 2, 0, 2, 8.867, 0, 0, 9, 1, 0, 9.133, 0, 0, 9.267, 1, 0, 9.4, 0, 0, 9.533, 1, 0, 9.667, 0, 0, 9.8, 1, 0, 9.933, 0, 0, 10.067, 1, 0, 10.2, 0, 0, 10.333, 1, 0, 10.467, 0, 0, 10.6, 1, 0, 10.733, 0, 0, 10.867, 1, 0, 11, 0, 0, 11.133, 1, 0, 11.267, 0, 0, 11.4, 1, 0, 11.533, 0, 0, 11.667, 1, 0, 11.8, 0, 0, 11.933, 1, 0, 12.067, 0, 0, 12.2, 1, 0, 12.333, 0, 0, 12.467, 1, 0, 12.6, 0, 0, 12.733, 1, 0, 12.867, 0, 0, 13, 1, 0, 13.133, 0, 0, 13.267, 1, 0, 13.4, 0, 0, 13.533, 1, 0, 13.667, 0, 0, 13.8, 1, 0, 13.933, 0, 0, 14.067, 1, 0, 14.2, 0, 0, 14.333, 1, 0, 14.467, 0, 0, 14.6, 1, 0, 14.733, 0, 0, 14.867, 1, 0, 15, 0, 0, 15.133, 1, 0, 15.267, 0, 0, 15.4, 1, 0, 15.533, 0, 0, 15.667, 1, 0, 15.8, 0, 0, 15.933, 1, 0, 16.067, 0, 0, 16.2, 1, 0, 16.333, 0, 0, 16.467, 1, 0, 16.6, 0, 0, 16.733, 1, 0, 16.867, 0, 0, 17, 1, 0, 17.133, 0, 0, 17.267, 1, 0, 17.4, 0, 0, 17.533, 1, 0, 17.667, 0, 0, 17.8, 1, 0, 17.933, 0, 0, 18.067, 1, 0, 18.2, 0, 0, 18.333, 1, 0, 18.467, 0, 2, 20.6, 0, 2, 23.233, 0]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0, 2, 0.3, 0, 0, 1.167, -1, 2, 2, -1, 0, 3.333, 0, 2, 17.633, 0, 0, 19.233, -1, 2, 20.6, -1, 2, 23.233, -1]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0, 2, 0.3, 0, 0, 1.167, -1, 2, 2, -1, 0, 3.333, 0, 2, 17.633, 0, 0, 19.233, -1, 2, 20.6, -1, 2, 23.233, -1]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 0.212, 0, 0.3, 1, 0, 0.733, 0, 2, 2, 0, 0, 2.867, 1, 2, 4.067, 1, 0, 4.267, 0, 0, 4.533, 1, 0, 4.867, 0.3, 0, 5.267, 1, 0, 6.333, 0.3, 0, 7.233, 1, 0, 7.6, 0, 0, 8.333, 1, 2, 10.567, 1, 0, 11.567, 0, 0, 12.133, 1, 0, 12.433, 0, 0, 13.233, 0.6, 0, 13.6, 0.227, 1, 14.111, 0.227, 14.622, 0.233, 15.133, 0.3, 1, 15.433, 0.34, 15.733, 0.485, 16.033, 0.641, 1, 16.3, 0.78, 16.566, 1, 16.833, 1, 0, 17.167, 0, 2, 18.433, 0, 0, 19.2, 1, 2, 20.6, 1, 2, 22.533, 1, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 2, 0, 2, 18.167, 0, 2, 18.2, 1, 2, 20.167, 1, 2, 20.2, 0, 2, 23.233, 0]}, {"Target": "Parameter", "Id": "ParamCheek", "Segments": [0, 0, 2, 2, 0, 0, 12, 1, 0, 20.6, 0, 2, 23.233, 0]}, {"Target": "Parameter", "Id": "zao1", "Segments": [0, 0, 0, 2, 0.04, 0, 9.733, 0, 0, 12, 0.3, 2, 17.433, 0.3, 0, 23.233, 0.1]}, {"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 0, 1, -75, 0, 2.2, -39, 1, 2.767, -39, 3.333, -39.237, 3.9, -41, 1, 4.1, -41.622, 4.3, -74.716, 4.5, -77.723, 1, 5.156, -87.579, 5.811, -90, 6.467, -90, 2, 9.567, -90, 1, 9.834, -90, 10.1, -52.61, 10.367, -16.35, 1, 10.489, 0.269, 10.611, 0, 10.733, 0, 1, 12.978, 0, 15.222, -3.966, 17.467, -14.587, 1, 17.7, -15.691, 17.934, -69.092, 18.167, -73, 1, 18.978, -86.585, 19.789, -90, 20.6, -90, 0, 21.167, 21, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 0, 2, -62, 0, 3.467, 29.535, 0, 4.233, -51.899, 1, 9.689, -51.899, 15.144, -35.457, 20.6, 0, 1, 20.756, 1.011, 20.911, 33, 21.067, 33, 0, 21.4, -51, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, -30, 2, 2.833, -30, 0, 5.267, 3, 1, 5.611, 3, 5.956, -13.039, 6.3, -14, 1, 10.411, -25.465, 14.522, -30, 18.633, -30, 1, 18.911, -30, 19.189, -8.179, 19.467, -6, 1, 20.234, 0.013, 21, 1, 21.767, 1, 0, 23.233, -30]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 20, 0, 2.5, 0, 1, 2.611, 0, 2.722, 13.122, 2.833, 15.24, 1, 3.055, 19.477, 3.278, 20, 3.5, 20, 0, 4.367, 17.313, 0, 5.2, 20, 2, 8.333, 20, 0, 10.3, -14.299, 0, 11.8, 13.738, 0, 13.333, -14.299, 0, 15.367, 10.254, 0, 17.2, -10.401, 1, 17.678, -10.401, 18.155, 3.496, 18.633, 5.17, 1, 19.366, 7.737, 20.1, 7.679, 20.833, 7.679, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0.291, 0, 2, 0, 0, 4, 1, 1, 4.467, 1, 4.933, 0.39, 5.4, 0.335, 1, 6.033, 0.261, 6.667, 0.221, 7.3, 0.2, 1, 11.733, 0.056, 16.167, 0, 20.6, 0, 2, 23.233, 0]}, {"Target": "Parameter", "Id": "shangshenyaobai", "Segments": [0, 6.949, 0, 2, 0, 0, 5.167, 3.6, 0, 8.733, 0, 2, 20.6, 0, 0, 21.8, 24, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 20, 1, 0.767, 20, 1.533, 16.91, 2.3, 0, 1, 2.767, -10.293, 3.233, -30, 3.7, -30, 0, 8.167, 0, 2, 9.9, 0, 0, 11.7, -19.639, 0, 13.433, 0, 0, 15.033, -13.091, 0, 16.633, 0, 1, 17.533, 0, 18.433, -21.941, 19.333, -27.937, 1, 19.8, -31.045, 20.266, -30, 20.733, -30, 0, 21.733, 0, 2, 23.233, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 9.767, -1, 0, 11.033, 1, 0, 12.633, -1, 0, 14.167, 1, 0, 15.6, -1, 0, 17.9, 0.141, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, -1, 2, 2, -1, 2, 20.6, -1, 2, 21.5, -1, 1, 21.867, -1, 22.233, -0.41, 22.6, 0, 1, 22.711, 0.124, 22.822, 0.082, 22.933, 0.082, 0, 23.233, 0]}, {"Target": "Model", "Id": "Opacity", "Segments": [0, 1, 0, 23.233, 1]}, {"Target": "Parameter", "Id": "ParamHairSide", "Segments": [0, 0, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "ParamHairBack", "Segments": [0, 0, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "shi<PERSON>ez<PERSON>biaozuoyou", "Segments": [0, 0, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "Param11", "Segments": [0, 0, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "tiqiehuan", "Segments": [0, 0, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "ZHITIyy", "Segments": [0, 0, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "drzbxz", "Segments": [0, 0, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "drybxz", "Segments": [0, 0, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "tisousuo", "Segments": [0, 0, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "dryoutui", "Segments": [0, 0, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "renzuoyou", "Segments": [0, 0, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "rens<PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "rendaxiao", "Segments": [0, 0, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "jingshen", "Segments": [0, 0.5, 0, 23.233, 0.5]}, {"Target": "Parameter", "Id": "ZZZ", "Segments": [0, 0, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "jingjingtmd", "Segments": [0, 0, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "jingjingdaixioa", "Segments": [0, 0, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "<PERSON>ng<PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "jingjingshangxai", "Segments": [0, 0, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "b<PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "Param10", "Segments": [0, 0, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "baiguang<PERSON><PERSON>u", "Segments": [0, 0, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "lizi1daxoao", "Segments": [0, 0, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "lizi2daxiao", "Segments": [0, 0, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "liziyi<PERSON>", "Segments": [0, 0, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "liziqumian", "Segments": [0, 0, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "lizizong<PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "jing<PERSON>u", "Segments": [0, 0, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "andu", "Segments": [0, 0, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "lanmu", "Segments": [0, 0, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "heidi", "Segments": [0, 0, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "bai", "Segments": [0, 0, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "xingfengtmd", "Segments": [0, 0, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "fizitmd", "Segments": [0, 0, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "xiongtuceng", "Segments": [0, 0, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 1, 0, 23.233, 1]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "kous<PERSON>dongkaibi", "Segments": [0, 0, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "leimu", "Segments": [0, 0, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "leidonghua", "Segments": [0, 0, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "youleidonghua", "Segments": [0, 0, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "leimudonghua", "Segments": [0, 0, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "jingyaa", "Segments": [0, 0, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "wu", "Segments": [0, 0, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "<PERSON>banzhenmian", "Segments": [0, 0, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "dibantmd", "Segments": [0, 0, 0, 23.233, 0]}, {"Target": "Parameter", "Id": "beijingtmd", "Segments": [0, 0, 0, 23.233, 0]}], "UserData": [{"Time": 0.0, "Value": ""}, {"Time": 22.733, "Value": ""}]}