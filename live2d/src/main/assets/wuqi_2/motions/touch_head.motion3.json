{"Version": 3, "Meta": {"Duration": 7.8, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 203, "TotalSegmentCount": 1501, "TotalPointCount": 1769, "UserDataCount": 2, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "wuli", "Segments": [0, 0, 0, 0.333, -1, 0, 0.5, 1, 2, 6.2, 1, 0, 6.733, -1, 0, 7.133, 1, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "Para", "Segments": [0, 0, 0, 0.3, -1, 0, 0.533, 1, 0, 0.9, -0.99, 0, 1.1, 1, 0, 1.333, -1, 0, 4.233, -0.99, 0, 5.333, -1, 0, 6.033, 1, 0, 6.833, -1, 0, 6.967, 1, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "zuocefati1", "Segments": [0, 0, 1, 0.078, 0.931, 0.155, 1.711, 0.233, 1.711, 0, 0.5, -11.221, 0, 0.867, 11.557, 0, 1.267, -11.232, 0, 1.667, 8.298, 0, 2.1, -4.814, 0, 2.567, 2.339, 0, 2.967, -1.579, 0, 3.4, 0.838, 0, 3.8, -0.48, 0, 4.267, 0.442, 0, 4.667, -0.26, 0, 5.1, 0.156, 0, 5.533, -0.092, 0, 5.967, 0.055, 0, 6.233, -0.013, 0, 6.533, 3.692, 0, 6.967, -9.041, 0, 7.367, 9.563, 1, 7.511, 9.563, 7.656, 6.727, 7.8, 0]}, {"Target": "Parameter", "Id": "zuocefati2", "Segments": [0, 0, 0, 0.167, -0.879, 0, 0.433, 6.084, 0, 0.667, -19.38, 0, 1.033, 19.717, 0, 1.433, -17.982, 0, 1.8, 12.731, 0, 2.233, -6.763, 0, 2.667, 3.651, 0, 3.1, -2.744, 0, 3.533, 1.136, 0, 3.933, -1.052, 0, 4.4, 0.603, 0, 4.833, -0.438, 0, 5.233, 0.253, 0, 5.667, -0.16, 0, 6.067, 0.086, 0, 6.367, -0.856, 0, 6.733, 7.722, 0, 7.133, -15.125, 0, 7.533, 14.206, 1, 7.622, 14.206, 7.711, 9.33, 7.8, 0]}, {"Target": "Parameter", "Id": "zuocefati3", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0.002, 0.033, 0, 1, 0.122, -0.017, 0.211, -1.376, 0.3, -1.376, 0, 0.567, 11.256, 0, 0.767, -16.887, 0, 1.133, 18.312, 0, 1.5, -16.17, 0, 1.9, 11.936, 0, 2.3, -7.39, 0, 2.733, 4.244, 0, 3.167, -2.627, 0, 3.567, 1.645, 0, 4, -1.025, 0, 4.433, 0.597, 0, 4.9, -0.435, 0, 5.3, 0.307, 0, 5.7, -0.187, 0, 6.1, 0.105, 0, 6.533, -2.547, 0, 6.867, 6.955, 0, 7.233, -12.377, 0, 7.633, 12.528, 1, 7.689, 12.528, 7.744, 4.969, 7.8, 0]}, {"Target": "Parameter", "Id": "zuocefati4", "Segments": [0, 0, 1, 0.022, 0, 0.045, 0.022, 0.067, -0.01, 1, 0.167, -0.151, 0.267, -1.373, 0.367, -1.373, 0, 0.633, 10.101, 0, 0.867, -18.3, 0, 1.2, 21.676, 0, 1.567, -19.815, 0, 1.967, 15.844, 0, 2.4, -11.142, 0, 2.8, 7.103, 0, 3.2, -4.371, 0, 3.633, 2.753, 0, 4.033, -1.758, 0, 4.467, 1.02, 0, 4.933, -0.631, 0, 5.367, 0.49, 0, 5.767, -0.332, 0, 6.167, 0.194, 0, 6.6, -2.782, 0, 6.933, 8.392, 0, 7.3, -14.682, 0, 7.7, 16.028, 1, 7.733, 16.028, 7.767, 3.188, 7.8, 0]}, {"Target": "Parameter", "Id": "zuocefati5", "Segments": [0, 0, 1, 0.044, -0.018, 0.089, 0.001, 0.133, -0.027, 1, 0.233, -0.09, 0.333, -1.244, 0.433, -1.244, 0, 0.7, 9.696, 0, 0.9, -19.886, 0, 1.233, 25.802, 0, 1.633, -23.676, 0, 2.033, 19.535, 0, 2.467, -15.252, 0, 2.867, 10.934, 0, 3.267, -7.234, 0, 3.667, 4.621, 0, 4.1, -2.985, 0, 4.5, 1.8, 0, 4.933, -0.962, 0, 5.4, 0.72, 0, 5.8, -0.558, 0, 6.2, 0.363, 0, 6.7, -3.053, 0, 7, 9.728, 0, 7.367, -16.735, 0, 7.767, 19.01, 1, 7.778, 19.01, 7.789, 1.007, 7.8, 0]}, {"Target": "Parameter", "Id": "fashipiaodai1", "Segments": [0, 0, 0, 0.033, -0.066, 0, 0.267, 4.291, 0, 0.533, -18.99, 0, 0.9, 13.826, 0, 1.3, -10.968, 0, 1.7, 7.655, 0, 2.133, -4.564, 0, 2.567, 2.376, 0, 3, -1.538, 0, 3.4, 0.839, 0, 3.833, -0.486, 0, 4.267, 0.41, 0, 4.7, -0.243, 0, 5.1, 0.144, 0, 5.533, -0.085, 0, 5.967, 0.051, 0, 6.233, -0.01, 0, 6.533, 6.446, 0, 6.967, -15.445, 0, 7.367, 15.744, 1, 7.511, 15.744, 7.656, 11.095, 7.8, 0]}, {"Target": "Parameter", "Id": "fashipiaodai2", "Segments": [0, 0, 0, 0.167, -1.531, 0, 0.433, 12.173, 0, 0.633, -30, 2, 0.667, -30, 0, 1.033, 23.247, 0, 1.433, -18.482, 0, 1.833, 12.515, 0, 2.267, -6.751, 0, 2.7, 3.75, 0, 3.133, -2.648, 0, 3.533, 1.207, 0, 3.967, -0.992, 0, 4.4, 0.585, 0, 4.833, -0.408, 0, 5.233, 0.235, 0, 5.667, -0.148, 0, 6.1, 0.081, 0, 6.367, -1.457, 0, 6.7, 13.143, 0, 7.133, -24.351, 0, 7.533, 21.726, 1, 7.622, 21.726, 7.711, 14.294, 7.8, 0]}, {"Target": "Parameter", "Id": "fashipiaodai3", "Segments": [0, 0, 2, 0.033, 0, 1, 0.122, -0.002, 0.211, -3.261, 0.3, -3.261, 0, 0.567, 19.065, 0, 0.767, -25.836, 0, 1.133, 22.202, 0, 1.533, -17.916, 0, 1.933, 12.726, 0, 2.333, -7.737, 0, 2.733, 4.376, 0, 3.167, -2.656, 0, 3.6, 1.638, 0, 4, -1.011, 0, 4.433, 0.597, 0, 4.9, -0.418, 0, 5.3, 0.284, 0, 5.7, -0.171, 0, 6.133, 0.098, 0, 6.533, -4.17, 0, 6.833, 11.225, 0, 7.233, -19.138, 0, 7.633, 17.825, 1, 7.689, 17.825, 7.744, 6.161, 7.8, 0]}, {"Target": "Parameter", "Id": "fashipiaodai4", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0.044, 0.033, -0.01, 1, 0.144, -0.543, 0.256, -3.292, 0.367, -3.292, 0, 0.6, 16.935, 0, 0.867, -27.21, 0, 1.2, 26.978, 0, 1.6, -23.008, 0, 2, 18.106, 0, 2.4, -12.466, 0, 2.8, 7.664, 0, 3.2, -4.48, 0, 3.633, 2.75, 0, 4.067, -1.717, 0, 4.467, 1.014, 0, 4.933, -0.632, 0, 5.367, 0.461, 0, 5.767, -0.304, 0, 6.167, 0.177, 0, 6.6, -4.563, 0, 6.9, 12.982, 0, 7.267, -21.008, 0, 7.7, 21.592, 1, 7.733, 21.592, 7.767, 3.766, 7.8, 0]}, {"Target": "Parameter", "Id": "fashipiaodai5", "Segments": [0, 0, 1, 0.033, 0, 0.067, 0.043, 0.1, -0.027, 1, 0.211, -0.261, 0.322, -2.948, 0.433, -2.948, 0, 0.667, 17.365, 0, 0.9, -30, 0, 1.267, 30, 0, 1.667, -27.959, 0, 2.067, 23.127, 0, 2.467, -17.593, 0, 2.867, 12.318, 0, 3.267, -7.784, 0, 3.667, 4.68, 0, 4.1, -2.929, 0, 4.5, 1.75, 0, 4.967, -0.99, 0, 5.4, 0.701, 0, 5.833, -0.512, 0, 6.2, 0.325, 0, 6.667, -4.933, 0, 6.967, 14.371, 0, 7.333, -23.613, 0, 7.733, 24.785, 1, 7.755, 24.785, 7.778, 2.447, 7.8, 0]}, {"Target": "Parameter", "Id": "fashipiaodai6", "Segments": [0, 0, 1, 0.022, -0.024, 0.045, -0.092, 0.067, -0.092, 0, 0.167, -0.066, 0, 0.467, -2.561, 0, 0.733, 16.774, 0, 0.967, -30, 0, 1.3, 30, 2, 1.367, 30, 0, 1.733, -30, 0, 2.133, 27.878, 0, 2.567, -22.477, 0, 2.967, 17.479, 0, 3.333, -12.346, 0, 3.733, 8.059, 0, 4.133, -5.031, 0, 4.567, 3.066, 0, 4.967, -1.688, 0, 5.467, 1.024, 0, 5.867, -0.81, 0, 6.267, 0.579, 0, 6.733, -5.337, 0, 7, 15.815, 0, 7.367, -26.57, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "daimao1", "Segments": [0, 0, 1, 0.078, 1.16, 0.155, 2.138, 0.233, 2.138, 0, 0.5, -14.026, 0, 0.867, 14.446, 0, 1.267, -14.04, 0, 1.667, 10.373, 0, 2.1, -6.018, 0, 2.567, 2.924, 0, 2.967, -1.974, 0, 3.4, 1.048, 0, 3.8, -0.6, 0, 4.267, 0.552, 0, 4.667, -0.326, 0, 5.1, 0.195, 0, 5.533, -0.115, 0, 5.967, 0.069, 0, 6.233, -0.016, 0, 6.533, 4.615, 0, 6.967, -11.301, 0, 7.367, 11.954, 1, 7.511, 11.954, 7.656, 8.414, 7.8, 0]}, {"Target": "Parameter", "Id": "daimao2", "Segments": [0, 0, 0, 0.167, -1.099, 0, 0.433, 7.605, 0, 0.667, -24.225, 0, 1.033, 24.646, 0, 1.433, -22.477, 0, 1.8, 15.913, 0, 2.233, -8.454, 0, 2.667, 4.563, 0, 3.1, -3.429, 0, 3.533, 1.42, 0, 3.933, -1.316, 0, 4.4, 0.754, 0, 4.833, -0.548, 0, 5.233, 0.316, 0, 5.667, -0.201, 0, 6.067, 0.108, 0, 6.367, -1.069, 0, 6.733, 9.652, 0, 7.133, -18.906, 0, 7.533, 17.758, 1, 7.622, 17.758, 7.711, 11.663, 7.8, 0]}, {"Target": "Parameter", "Id": "daimao3", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0.002, 0.033, 0, 1, 0.122, -0.021, 0.211, -1.72, 0.3, -1.72, 0, 0.567, 14.071, 0, 0.767, -21.109, 0, 1.133, 22.89, 0, 1.5, -20.213, 0, 1.9, 14.92, 0, 2.3, -9.237, 0, 2.733, 5.305, 0, 3.167, -3.284, 0, 3.567, 2.057, 0, 4, -1.282, 0, 4.433, 0.747, 0, 4.9, -0.543, 0, 5.3, 0.383, 0, 5.7, -0.234, 0, 6.1, 0.132, 0, 6.533, -3.184, 0, 6.867, 8.694, 0, 7.233, -15.471, 0, 7.633, 15.661, 1, 7.689, 15.661, 7.744, 6.223, 7.8, 0]}, {"Target": "Parameter", "Id": "daimao4", "Segments": [0, 0, 1, 0.022, 0, 0.045, 0.027, 0.067, -0.012, 1, 0.167, -0.189, 0.267, -1.716, 0.367, -1.716, 0, 0.633, 12.627, 0, 0.867, -22.875, 0, 1.2, 27.095, 0, 1.567, -24.769, 0, 1.967, 19.805, 0, 2.4, -13.928, 0, 2.8, 8.878, 0, 3.2, -5.464, 0, 3.633, 3.441, 0, 4.033, -2.198, 0, 4.467, 1.275, 0, 4.933, -0.789, 0, 5.367, 0.613, 0, 5.767, -0.415, 0, 6.167, 0.242, 0, 6.6, -3.477, 0, 6.933, 10.49, 0, 7.3, -18.352, 0, 7.7, 20.035, 1, 7.733, 20.035, 7.767, 3.996, 7.8, 0]}, {"Target": "Parameter", "Id": "Param", "Segments": [0, 0, 1, 0.078, 0.584, 0.155, 0.899, 0.233, 0.899, 0, 0.5, -9.088, 0, 0.833, 11.61, 0, 1.267, -12.313, 0, 1.667, 9.295, 0, 2.1, -5.343, 0, 2.533, 2.534, 0, 2.967, -1.737, 0, 3.4, 0.909, 0, 3.8, -0.524, 0, 4.267, 0.494, 0, 4.667, -0.292, 0, 5.1, 0.175, 0, 5.533, -0.103, 0, 5.933, 0.062, 0, 6.233, -0.015, 0, 6.533, 2.954, 0, 6.967, -7.299, 0, 7.367, 7.843, 1, 7.511, 7.843, 7.656, 5.515, 7.8, 0]}, {"Target": "Parameter", "Id": "Param2", "Segments": [0, 0, 0, 0.167, -0.701, 0, 0.433, 3.942, 0, 0.667, -16.147, 0, 1, 19.889, 0, 1.4, -19.469, 0, 1.8, 13.947, 0, 2.233, -7.317, 0, 2.667, 3.969, 0, 3.1, -3.049, 0, 3.533, 1.202, 0, 3.933, -1.172, 0, 4.4, 0.664, 0, 4.833, -0.49, 0, 5.233, 0.283, 0, 5.667, -0.18, 0, 6.067, 0.096, 0, 6.367, -0.697, 0, 6.733, 6.26, 0, 7.167, -12.626, 0, 7.533, 12.124, 1, 7.622, 12.124, 7.711, 7.951, 7.8, 0]}, {"Target": "Parameter", "Id": "Param3", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0.004, 0.033, -0.001, 1, 0.111, -0.033, 0.189, -0.762, 0.267, -0.762, 0, 0.567, 8.333, 0, 0.767, -14.665, 0, 1.1, 18.176, 0, 1.5, -17.065, 0, 1.9, 12.588, 0, 2.3, -7.784, 0, 2.733, 4.49, 0, 3.167, -2.89, 0, 3.567, 1.821, 0, 4, -1.12, 0, 4.433, 0.647, 0, 4.9, -0.48, 0, 5.3, 0.344, 0, 5.7, -0.21, 0, 6.1, 0.119, 0, 6.533, -2.1, 0, 6.867, 5.805, 0, 7.267, -10.673, 0, 7.633, 11.162, 1, 7.689, 11.162, 7.744, 4.477, 7.8, 0]}, {"Target": "Parameter", "Id": "Param4", "Segments": [0, 0, 1, 0.022, 0, 0.045, 0.02, 0.067, -0.011, 1, 0.167, -0.154, 0.267, -0.717, 0.367, -0.717, 0, 0.633, 7.404, 0, 0.867, -15.825, 0, 1.167, 21.683, 0, 1.567, -20.415, 0, 1.967, 16.471, 0, 2.367, -11.469, 0, 2.8, 7.389, 0, 3.2, -4.707, 0, 3.633, 3.054, 0, 4.033, -1.953, 0, 4.467, 1.106, 0, 4.933, -0.683, 0, 5.367, 0.547, 0, 5.767, -0.374, 0, 6.167, 0.218, 0, 6.6, -2.296, 0, 6.933, 7.116, 0, 7.333, -12.819, 0, 7.7, 14.734, 1, 7.733, 14.734, 7.767, 2.997, 7.8, 0]}, {"Target": "Parameter", "Id": "Param5", "Segments": [0, 0, 1, 0.056, -0.018, 0.111, -0.005, 0.167, -0.024, 1, 0.245, -0.049, 0.322, -0.647, 0.4, -0.647, 0, 0.7, 7.093, 0, 0.9, -17.01, 0, 1.233, 24.804, 0, 1.633, -23.251, 0, 2.033, 20.087, 0, 2.467, -15.408, 0, 2.867, 11.192, 0, 3.267, -7.586, 0, 3.667, 5.056, 0, 4.1, -3.338, 0, 4.5, 1.991, 0, 4.933, -1.027, 0, 5.433, 0.793, 0, 5.833, -0.628, 0, 6.2, 0.412, 0, 6.7, -2.553, 0, 7, 8.429, 0, 7.367, -15.348, 0, 7.767, 18.05, 1, 7.778, 18.05, 7.789, 0.975, 7.8, 0]}, {"Target": "Parameter", "Id": "ZUOHOUFABG1", "Segments": [0, 0, 1, 0.078, 0.438, 0.155, 0.674, 0.233, 0.674, 0, 0.5, -6.816, 0, 0.833, 8.707, 0, 1.267, -9.235, 0, 1.667, 6.971, 0, 2.1, -4.007, 0, 2.533, 1.9, 0, 2.967, -1.303, 0, 3.4, 0.682, 0, 3.8, -0.393, 0, 4.267, 0.371, 0, 4.667, -0.219, 0, 5.1, 0.131, 0, 5.533, -0.077, 0, 5.933, 0.046, 0, 6.233, -0.011, 0, 6.533, 2.215, 0, 6.967, -5.475, 0, 7.367, 5.882, 1, 7.511, 5.882, 7.656, 4.136, 7.8, 0]}, {"Target": "Parameter", "Id": "ZUOHOUFABG2", "Segments": [0, 0, 0, 0.167, -0.526, 0, 0.433, 2.957, 0, 0.667, -12.11, 0, 1, 14.917, 0, 1.4, -14.602, 0, 1.8, 10.46, 0, 2.233, -5.487, 0, 2.667, 2.977, 0, 3.1, -2.287, 0, 3.533, 0.902, 0, 3.933, -0.879, 0, 4.4, 0.498, 0, 4.833, -0.368, 0, 5.233, 0.212, 0, 5.667, -0.135, 0, 6.067, 0.072, 0, 6.367, -0.523, 0, 6.733, 4.695, 0, 7.167, -9.47, 0, 7.533, 9.093, 1, 7.622, 9.093, 7.711, 5.964, 7.8, 0]}, {"Target": "Parameter", "Id": "ZUOHOUFABG3", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0.003, 0.033, -0.001, 1, 0.111, -0.025, 0.189, -0.572, 0.267, -0.572, 0, 0.567, 6.25, 0, 0.767, -10.999, 0, 1.1, 13.632, 0, 1.5, -12.798, 0, 1.9, 9.441, 0, 2.3, -5.838, 0, 2.733, 3.368, 0, 3.167, -2.167, 0, 3.567, 1.366, 0, 4, -0.84, 0, 4.433, 0.485, 0, 4.9, -0.36, 0, 5.3, 0.258, 0, 5.7, -0.158, 0, 6.1, 0.089, 0, 6.533, -1.575, 0, 6.867, 4.353, 0, 7.267, -8.005, 0, 7.633, 8.372, 1, 7.689, 8.372, 7.744, 3.352, 7.8, 0]}, {"Target": "Parameter", "Id": "hounaofa1yaobai", "Segments": [0, 0, 0, 0.467, -0.198, 0, 0.733, 0.396, 0, 1.133, -0.307, 0, 1.467, 0.209, 0, 1.733, -0.011, 0, 1.933, 0.042, 0, 2.3, -0.039, 0, 2.533, 0, 0, 2.8, -0.017, 0, 3.067, -0.012, 0, 3.2, -0.013, 0, 3.767, -0.007, 2, 3.8, -0.007, 0, 4.3, 0.005, 0, 4.533, -0.003, 0, 4.8, 0, 0, 5.067, -0.001, 0, 5.333, 0, 2, 5.533, 0, 0, 6.467, 0.051, 0, 6.933, -0.079, 0, 7.233, 0.071, 0, 7.5, -0.008, 0, 7.7, 0.005, 1, 7.733, 0.005, 7.767, 0.005, 7.8, 0]}, {"Target": "Parameter", "Id": "hounaifa1bianxing", "Segments": [0, 0, 2, 0.033, 0, 1, 0.089, 0.003, 0.144, 0.033, 0.2, 0.033, 0, 0.367, -0.019, 0, 0.467, 0.125, 0, 0.6, -0.462, 0, 0.867, 0.549, 0, 1.233, -0.316, 0, 1.567, 0.341, 0, 1.8, -0.217, 0, 2.067, 0.124, 0, 2.367, -0.069, 0, 2.633, 0.047, 0, 2.9, -0.024, 0, 3.167, 0.011, 0, 3.4, -0.005, 0, 3.7, 0.001, 0, 3.767, 0, 0, 3.833, 0.003, 0, 4.033, -0.004, 0, 4.433, 0.007, 0, 4.667, -0.006, 0, 4.9, 0.004, 0, 5.167, -0.002, 0, 5.4, 0.001, 0, 5.667, 0, 2, 5.9, 0, 2, 6.2, 0, 2, 6.233, 0, 0, 6.367, -0.039, 0, 6.6, 0.061, 0, 6.8, -0.003, 0, 6.867, 0, 0, 7.1, -0.082, 0, 7.367, 0.12, 0, 7.6, -0.076, 1, 7.667, -0.076, 7.733, -0.056, 7.8, 0]}, {"Target": "Parameter", "Id": "hounaofa2yaobai", "Segments": [0, 0, 0, 0.467, -0.793, 0, 0.633, 1, 2, 0.8, 1, 0, 1.067, -1, 2, 1.167, -1, 0, 1.467, 0.837, 0, 1.733, -0.043, 0, 1.933, 0.169, 0, 2.3, -0.158, 0, 2.533, 0, 0, 2.8, -0.066, 0, 3.067, -0.048, 0, 3.2, -0.053, 0, 3.767, -0.028, 2, 3.8, -0.028, 0, 4.3, 0.021, 0, 4.533, -0.01, 0, 4.8, 0.001, 0, 5.067, -0.002, 0, 5.333, -0.001, 2, 5.533, -0.001, 0, 6.467, 0.203, 0, 6.933, -0.316, 0, 7.233, 0.283, 0, 7.5, -0.032, 0, 7.7, 0.022, 1, 7.733, 0.022, 7.767, 0.019, 7.8, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 0.467, -0.198, 0, 0.733, 0.396, 0, 1.133, -0.307, 0, 1.467, 0.209, 0, 1.733, -0.011, 0, 1.933, 0.042, 0, 2.3, -0.039, 0, 2.533, 0, 0, 2.8, -0.017, 0, 3.067, -0.012, 0, 3.2, -0.013, 0, 3.767, -0.007, 2, 3.8, -0.007, 0, 4.3, 0.005, 0, 4.533, -0.003, 0, 4.8, 0, 0, 5.067, -0.001, 0, 5.333, 0, 2, 5.533, 0, 0, 6.467, 0.051, 0, 6.933, -0.079, 0, 7.233, 0.071, 0, 7.5, -0.008, 0, 7.7, 0.005, 1, 7.733, 0.005, 7.767, 0.005, 7.8, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 0.467, -0.129, 0, 0.767, 0.278, 0, 1.167, -0.253, 0, 1.5, 0.167, 0, 1.833, -0.016, 0, 2.067, 0.021, 0, 2.367, -0.029, 0, 2.667, -0.001, 0, 2.967, -0.013, 0, 3.333, -0.008, 0, 3.367, -0.009, 0, 3.4, -0.008, 0, 3.433, -0.009, 0, 4.3, 0.004, 0, 4.6, -0.002, 0, 4.933, 0, 2, 5.233, 0, 2, 5.567, 0, 2, 5.8, 0, 0, 6.5, 0.036, 0, 6.933, -0.062, 0, 7.267, 0.053, 0, 7.6, -0.009, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.033, 0, 1, 0.089, 0.003, 0.144, 0.015, 0.2, 0.015, 0, 0.367, 0, 0, 0.467, 0.04, 0, 0.633, -0.17, 0, 0.933, 0.235, 0, 1.3, -0.226, 0, 1.633, 0.194, 0, 1.933, -0.107, 0, 2.267, 0.061, 0, 2.533, -0.039, 0, 2.833, 0.021, 0, 3.1, -0.01, 0, 3.433, 0.004, 0, 3.733, -0.002, 0, 4.033, 0, 0, 4.267, -0.001, 0, 4.467, 0.003, 0, 4.767, -0.003, 0, 5.067, 0.001, 0, 5.367, -0.001, 0, 5.667, 0, 2, 5.967, 0, 2, 6.233, 0, 0, 6.4, -0.016, 0, 6.7, 0.028, 0, 7.133, -0.049, 0, 7.433, 0.056, 0, 7.733, -0.032, 1, 7.755, -0.032, 7.778, -0.003, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamHairFront", "Segments": [0, 0, 0, 0.467, -0.518, 0, 0.733, 1, 2, 0.8, 1, 0, 1.167, -1, 0, 1.5, 0.67, 0, 1.833, -0.066, 0, 2.067, 0.083, 0, 2.367, -0.116, 0, 2.667, -0.004, 0, 2.967, -0.052, 0, 3.333, -0.034, 2, 3.367, -0.034, 2, 3.4, -0.034, 2, 3.433, -0.034, 0, 4.3, 0.016, 0, 4.6, -0.008, 0, 4.933, 0.001, 0, 5.233, -0.002, 0, 5.567, 0, 0, 5.8, -0.001, 0, 6.5, 0.145, 0, 6.933, -0.249, 0, 7.267, 0.211, 0, 7.6, -0.036, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 0.267, 0.06, 0, 0.367, 0.054, 0, 0.467, 0.163, 0, 0.7, -0.356, 0, 1, 0.321, 0, 1.333, -0.35, 0, 1.667, 0.355, 0, 1.967, -0.286, 0, 2.3, 0.166, 0, 2.6, -0.077, 0, 2.933, 0.055, 0, 3.233, -0.008, 0, 3.567, 0.021, 0, 3.967, 0.001, 0, 4.133, 0.003, 0, 4.3, 0, 0, 4.5, 0.004, 0, 4.767, -0.004, 0, 5.067, 0.004, 0, 5.4, -0.002, 0, 5.733, 0.001, 0, 6.067, 0, 2, 6.233, 0, 0, 6.433, -0.045, 0, 6.867, 0.045, 0, 7.2, -0.073, 0, 7.433, 0.085, 0, 7.733, -0.072, 1, 7.755, -0.072, 7.778, -0.006, 7.8, 0]}, {"Target": "Parameter", "Id": "Param6", "Segments": [0, 0, 1, 0.078, 0.029, 0.155, 0.045, 0.233, 0.045, 0, 0.5, -0.454, 0, 0.833, 0.58, 0, 1.267, -0.616, 0, 1.667, 0.465, 0, 2.1, -0.267, 0, 2.533, 0.127, 0, 2.967, -0.087, 0, 3.4, 0.045, 0, 3.8, -0.026, 0, 4.267, 0.025, 0, 4.667, -0.015, 0, 5.1, 0.009, 0, 5.533, -0.005, 0, 5.933, 0.003, 0, 6.233, -0.001, 0, 6.533, 0.148, 0, 6.967, -0.365, 0, 7.367, 0.392, 1, 7.511, 0.392, 7.656, 0.279, 7.8, 0]}, {"Target": "Parameter", "Id": "Param7", "Segments": [0, 0, 0, 0.167, -0.035, 0, 0.433, 0.197, 0, 0.667, -0.807, 0, 1, 0.994, 0, 1.4, -0.973, 0, 1.8, 0.697, 0, 2.233, -0.366, 0, 2.667, 0.198, 0, 3.1, -0.152, 0, 3.533, 0.06, 0, 3.933, -0.059, 0, 4.4, 0.033, 0, 4.833, -0.025, 0, 5.233, 0.014, 0, 5.667, -0.009, 0, 6.067, 0.005, 0, 6.367, -0.035, 0, 6.733, 0.313, 0, 7.167, -0.631, 0, 7.533, 0.606, 1, 7.622, 0.606, 7.711, 0.398, 7.8, 0]}, {"Target": "Parameter", "Id": "Param8", "Segments": [0, 0, 2, 0.033, 0, 1, 0.111, -0.002, 0.189, -0.038, 0.267, -0.038, 0, 0.567, 0.417, 0, 0.767, -0.733, 0, 1.1, 0.909, 0, 1.5, -0.853, 0, 1.9, 0.629, 0, 2.3, -0.389, 0, 2.733, 0.225, 0, 3.167, -0.144, 0, 3.567, 0.091, 0, 4, -0.056, 0, 4.433, 0.032, 0, 4.9, -0.024, 0, 5.3, 0.017, 0, 5.7, -0.011, 0, 6.1, 0.006, 0, 6.533, -0.105, 0, 6.867, 0.29, 0, 7.267, -0.534, 0, 7.633, 0.558, 1, 7.689, 0.558, 7.744, 0.227, 7.8, 0]}, {"Target": "Parameter", "Id": "Param9", "Segments": [0, 0, 1, 0.022, 0, 0.045, 0.001, 0.067, -0.001, 1, 0.167, -0.008, 0.267, -0.036, 0.367, -0.036, 0, 0.633, 0.37, 0, 0.867, -0.791, 0, 1.167, 1, 2, 1.2, 1, 0, 1.567, -1, 0, 1.967, 0.824, 0, 2.367, -0.573, 0, 2.8, 0.369, 0, 3.2, -0.235, 0, 3.633, 0.153, 0, 4.033, -0.098, 0, 4.467, 0.055, 0, 4.933, -0.034, 0, 5.367, 0.027, 0, 5.767, -0.019, 0, 6.167, 0.011, 0, 6.6, -0.115, 0, 6.933, 0.356, 0, 7.333, -0.641, 0, 7.7, 0.737, 1, 7.733, 0.737, 7.767, 0.153, 7.8, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.767, 0, 0, 1.667, 1, 2, 4.233, 1, 2, 6.9, 1, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 1.567, 0, 1, 1.589, 0, 1.611, -0.593, 1.633, -0.6, 1, 2.5, -0.873, 3.366, -1, 4.233, -1, 2, 6.9, -1, 0, 7.033, 0, 2, 7.8, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 1.567, 0, 0, 1.633, -1, 2, 4.233, -1, 2, 5.6, -1, 2, 6.9, -1, 0, 7.033, 0, 2, 7.8, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON>ia<PERSON><PERSON>uo<PERSON>", "Segments": [0, 0, 1, 0.6, 0, 1.2, 0.158, 1.8, 0.4, 1, 2.178, 0.553, 2.555, 0.6, 2.933, 0.6, 2, 4.233, 0.6, 2, 6.6, 0.6, 0, 6.933, 0, 2, 7.8, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.5, 0, 1, 0.722, 0, 0.945, -0.133, 1.167, -0.4, 1, 1.278, -0.533, 1.389, -0.6, 1.5, -0.6, 0, 2, 1, 2, 4.233, 1, 2, 6.1, 1, 2, 6.633, 1, 0, 6.9, -1, 0, 7.033, 0, 2, 7.8, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 1, 0, 1.4, -1, 0, 1.933, 1, 2, 4.233, 1, 2, 7.8, 1]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 4.233, 0, 2, 6.067, 0, 0, 6.1, -1, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "xingfengyaobai", "Segments": [0, 0, 0, 1.5, -1, 0, 2.333, 1, 0, 2.933, -1, 0, 3.4, 0.501, 0, 3.833, -0.3, 0, 4.1, 0, 2, 4.233, 0, 2, 7.8, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 1, 0.133, 0, 0.267, 0.378, 0.4, 0.5, 1, 0.544, 0.632, 0.689, 0.637, 0.833, 0.75, 1, 0.922, 0.82, 1.011, 1, 1.1, 1, 2, 1.667, 1, 2, 5.833, 1, 2, 6.333, 1, 0, 7.5, 0, 2, 7.8, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 1, 0.133, 0, 0.267, 0.378, 0.4, 0.5, 1, 0.544, 0.632, 0.689, 0.637, 0.833, 0.75, 1, 0.922, 0.82, 1.011, 1, 1.1, 1, 2, 1.667, 1, 2, 5.833, 1, 2, 6.333, 1, 0, 7.5, 0, 2, 7.8, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 1, 0.367, 0.104, 0.733, 0.129, 1.1, 0.174, 1, 1.478, 0.221, 1.855, 0.232, 2.233, 0.232, 0, 4.233, 0.031, 0, 5.833, 0.107, 0, 7.5, 0, 2, 7.8, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON>biji<PERSON><PERSON>", "Segments": [0, 0, 0, 0.4, 0.083, 1, 0.633, 0.083, 0.867, 0.118, 1.1, -0.062, 1, 1.511, -0.38, 1.922, -1, 2.333, -1, 0, 4.233, -0.924, 0, 5.167, -1, 1, 5.389, -1, 5.611, -0.471, 5.833, -0.158, 1, 6, 0.077, 6.166, 0.064, 6.333, 0.064, 0, 7.5, 0, 2, 7.8, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 1, 0.133, 0, 0.267, 0.009, 0.4, -0.086, 1, 0.511, -0.166, 0.622, -0.68, 0.733, -0.68, 0, 1.1, -0.396, 1, 1.456, -0.395, 1.811, -0.396, 2.167, -0.393, 1, 2.856, -0.387, 3.544, 0, 4.233, 0, 1, 4.655, 0, 5.078, -0.105, 5.5, -0.364, 1, 5.611, -0.432, 5.722, -0.538, 5.833, -0.636, 1, 6.1, -0.871, 6.366, -1, 6.633, -1, 1, 6.711, -1, 6.789, -0.422, 6.867, -0.321, 1, 7.078, -0.046, 7.289, 0, 7.5, 0, 2, 7.8, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 1, 2, 1.167, 1, 0, 1.2, 0, 2, 5.467, 0, 0, 5.5, 1, 2, 5.833, 1, 2, 7.5, 1, 2, 7.8, 1]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.4, 0, 1, 0.511, 0, 0.622, -0.326, 0.733, -0.385, 1, 0.855, -0.45, 0.978, -0.436, 1.1, -0.5, 1, 1.278, -0.592, 1.455, -0.753, 1.633, -0.753, 0, 2.567, 0.2, 0, 4.267, 0, 2, 5.833, 0, 0, 6.333, -0.387, 0, 7.5, 0, 2, 7.8, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 1.367, 0, 0, 1.4, 1, 2, 4.133, 1, 0, 4.233, 0, 2, 7.5, 0, 2, 7.8, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 1.533, 1, 0, 2.1, -1, 2, 4.233, -1, 2, 6.333, -1, 0, 7.5, 0, 2, 7.8, 0]}, {"Target": "Parameter", "Id": "xiongshangxai", "Segments": [0, 0, 0, 0.033, -0.019, 0, 0.167, 0.267, 0, 0.433, -1, 2, 0.5, -1, 0, 0.633, 1, 2, 0.767, 1, 0, 1, -1, 2, 1.1, -1, 0, 1.2, 1, 2, 1.3, 1, 0, 1.467, -0.756, 0, 1.667, 0.32, 0, 1.933, -0.042, 0, 2.1, 0.025, 0, 2.3, -0.059, 0, 2.533, -0.001, 0, 2.767, -0.024, 0, 3, -0.019, 0, 3.133, -0.02, 2, 3.167, -0.02, 0, 3.2, -0.021, 0, 4.3, 0.008, 0, 4.5, -0.003, 0, 4.733, 0.001, 0, 4.967, -0.001, 0, 5.133, 0, 0, 5.567, -0.257, 0, 6.167, 0.199, 0, 6.933, -1, 2, 7, -1, 0, 7.133, 0.967, 0, 7.367, -0.213, 0, 7.567, 0.116, 1, 7.645, 0.116, 7.722, 0.086, 7.8, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 0.033, -0.009, 0, 0.167, 0.133, 0, 0.467, -0.619, 0, 0.7, 0.778, 0, 1.067, -0.814, 0, 1.267, 0.776, 0, 1.467, -0.378, 0, 1.667, 0.16, 0, 1.933, -0.021, 0, 2.1, 0.013, 0, 2.3, -0.029, 0, 2.533, 0, 0, 2.767, -0.012, 0, 3, -0.009, 0, 3.133, -0.01, 2, 3.167, -0.01, 2, 3.2, -0.01, 0, 4.3, 0.004, 0, 4.5, -0.002, 0, 4.733, 0, 2, 4.967, 0, 2, 5.133, 0, 0, 5.567, -0.128, 0, 6.167, 0.1, 0, 6.967, -0.83, 0, 7.133, 0.483, 0, 7.367, -0.107, 0, 7.567, 0.058, 1, 7.645, 0.058, 7.722, 0.043, 7.8, 0]}, {"Target": "Parameter", "Id": "youxiabifa", "Segments": [0, 0, 0, 2.167, 1, 2, 6.233, 1, 0, 6.767, 0, 2, 7.8, 0]}, {"Target": "Parameter", "Id": "op", "Segments": [0, 0, 0, 1.167, 0.6, 2, 1.833, 0.6, 0, 3.7, 1, 2, 5.7, 1, 0, 6, 0.6, 2, 6.667, 0.6, 1, 6.745, 0.6, 6.822, 0.361, 6.9, 0.3, 1, 7.2, 0.064, 7.5, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 0, 0.167, 1.2, 0, 0.667, 1, 2, 1.833, 1, 0, 1.9, 1.2, 0, 2, 1, 0, 2.067, 1.17, 0, 2.167, 1, 0, 2.233, 1.14, 0, 2.333, 1, 0, 2.4, 1.128, 0, 2.5, 1, 0, 2.567, 1.113, 0, 2.667, 1, 0, 2.733, 1.098, 0, 2.833, 1, 0, 2.9, 1.091, 0, 3, 1, 0, 3.067, 1.079, 0, 3.167, 1, 0, 3.233, 1.068, 0, 3.333, 1, 0, 3.4, 1.045, 0, 3.5, 1, 0, 3.567, 1.034, 0, 3.667, 1, 0, 3.733, 1.011, 0, 3.833, 1, 2, 4.233, 1, 0, 4.667, 0, 2, 5.333, 0, 0, 5.9, 1, 2, 7.8, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 1, 0.611, 0, 1.222, 0.062, 1.833, 0.2, 1, 1.855, 0.205, 1.878, 0.375, 1.9, 0.375, 0, 2, 0.2, 0, 2.067, 0.372, 0, 2.167, 0.2, 0, 2.233, 0.357, 0, 2.333, 0.2, 0, 2.4, 0.347, 0, 2.5, 0.2, 0, 2.567, 0.336, 0, 2.667, 0.2, 0, 2.733, 0.325, 0, 2.833, 0.2, 0, 2.9, 0.317, 0, 3, 0.2, 0, 3.067, 0.304, 0, 3.167, 0.2, 0, 3.233, 0.293, 0, 3.333, 0.2, 0, 3.4, 0.285, 0, 3.5, 0.2, 0, 3.567, 0.27, 0, 3.667, 0.2, 0, 3.733, 0.257, 0, 3.833, 0.2, 2, 4.233, 0.2, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 0, 0.167, 1.2, 0, 0.667, 1, 2, 1.833, 1, 0, 1.9, 1.2, 0, 2, 1, 0, 2.067, 1.17, 0, 2.167, 1, 0, 2.233, 1.14, 0, 2.333, 1, 0, 2.4, 1.128, 0, 2.5, 1, 0, 2.567, 1.113, 0, 2.667, 1, 0, 2.733, 1.098, 0, 2.833, 1, 0, 2.9, 1.091, 0, 3, 1, 0, 3.067, 1.079, 0, 3.167, 1, 0, 3.233, 1.068, 0, 3.333, 1, 0, 3.4, 1.045, 0, 3.5, 1, 0, 3.567, 1.034, 0, 3.667, 1, 0, 3.733, 1.011, 0, 3.833, 1, 2, 4.233, 1, 0, 4.667, 0, 2, 5.333, 0, 0, 5.9, 1, 2, 7.8, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 1, 0.611, 0, 1.222, 0.062, 1.833, 0.2, 1, 1.855, 0.205, 1.878, 0.375, 1.9, 0.375, 0, 2, 0.2, 0, 2.067, 0.372, 0, 2.167, 0.2, 0, 2.233, 0.357, 0, 2.333, 0.2, 0, 2.4, 0.347, 0, 2.5, 0.2, 0, 2.567, 0.336, 0, 2.667, 0.2, 0, 2.733, 0.325, 0, 2.833, 0.2, 0, 2.9, 0.317, 0, 3, 0.2, 0, 3.067, 0.304, 0, 3.167, 0.2, 0, 3.233, 0.293, 0, 3.333, 0.2, 0, 3.4, 0.284, 0, 3.5, 0.2, 0, 3.567, 0.27, 0, 3.667, 0.2, 0, 3.733, 0.257, 0, 3.833, 0.2, 2, 4.233, 0.2, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 0.167, -0.6, 0, 0.667, 0, 0, 1.033, -0.3, 0, 2.5, 1, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON>kongda<PERSON><PERSON>", "Segments": [0, 0, 2, 0.167, 0, 0, 0.333, 1, 0, 0.667, -1, 0, 0.833, 0.5, 0, 0.967, -0.2, 0, 1.1, 0, 2, 2.6, 0, 0, 2.833, 1, 0, 3.333, -1, 0, 3.567, 0.5, 0, 3.767, -0.2, 0, 3.967, 0, 2, 4.233, 0, 2, 7.8, 0]}, {"Target": "Parameter", "Id": "gaoguangdaxia<PERSON>", "Segments": [0, 0, 2, 0.333, 0, 0, 0.567, 1, 0, 0.733, 0, 0, 0.867, 0.9, 1, 0.911, 0.9, 0.956, 0.208, 1, 0, 1, 1.167, -0.781, 1.333, -1, 1.5, -1, 2, 4.233, -1, 0, 4.5, 0, 2, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, -1, 2, 1.833, -1, 0, 2, -0.847, 0, 2.167, -1, 0, 2.333, -0.847, 0, 2.5, -1, 0, 2.667, -0.847, 0, 2.833, -1, 0, 3, -0.847, 0, 3.167, -1, 0, 3.333, -0.847, 0, 3.5, -1, 0, 3.667, -0.912, 0, 3.833, -1, 0, 4, -0.955, 0, 4.167, -1, 0, 4.333, -0.989, 0, 4.5, -1, 2, 7.8, -1]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, -1, 2, 1.833, -1, 0, 2, -0.847, 0, 2.167, -1, 0, 2.333, -0.847, 0, 2.5, -1, 0, 2.667, -0.847, 0, 2.833, -1, 0, 3, -0.847, 0, 3.167, -1, 0, 3.333, -0.847, 0, 3.5, -1, 0, 3.667, -0.912, 0, 3.833, -1, 0, 4, -0.955, 0, 4.167, -1, 0, 4.333, -0.989, 0, 4.5, -1, 2, 7.8, -1]}, {"Target": "Parameter", "Id": "k<PERSON>ax<PERSON>o", "Segments": [0, 0, 0, 0.233, 30, 0, 0.667, -30, 2, 1.4, -30, 1, 1.656, -30, 1.911, 17.072, 2.167, 22.115, 1, 2.556, 29.791, 2.944, 30, 3.333, 30, 0, 4, 0, 2, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 0, 0, 0.2, 1, 1, 0.6, 1, 1, 0.505, 1.4, 0, 1, 2.011, -0.772, 2.622, -1, 3.233, -1, 2, 3.333, -1, 2, 3.467, -1, 2, 3.7, -1, 0, 6.633, 0.055, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 0, 0.2, 1, 2, 0.5, 1, 2, 0.667, 1, 2, 1.833, 1, 0, 1.933, 0.807, 0, 2.067, 1, 0, 2.167, 0.807, 0, 2.3, 1, 0, 2.4, 0.856, 0, 2.533, 1, 0, 2.633, 0.868, 0, 2.767, 1, 0, 2.867, 0.896, 0, 3, 1, 0, 3.1, 0.896, 0, 3.233, 1, 0, 3.333, 0.922, 0, 3.467, 1, 0, 3.567, 0.942, 0, 3.7, 1, 1, 4.122, 1, 4.545, 0.498, 4.967, 0.42, 1, 5.522, 0.316, 6.078, 0.317, 6.633, 0.226, 1, 7.022, 0.163, 7.411, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.5, 0, 0, 0.533, 1, 2, 5.667, 1, 0, 5.7, 0, 2, 7.8, 0]}, {"Target": "Parameter", "Id": "kous<PERSON>dongkaibi", "Segments": [0, 0, 0, 0.2, 1, 2, 0.667, 1, 2, 1.833, 1, 0, 1.933, 0.8, 0, 2.067, 1, 0, 2.167, 0.8, 0, 2.3, 1, 0, 2.4, 0.9, 0, 2.533, 1, 0, 2.9, 0.9, 0, 3.267, 1, 0, 3.9, 0, 0, 6.633, 0.11, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "leimu", "Segments": [0, 0, 2, 1, 0, 0, 1.5, 1, 2, 4.233, 1, 0, 4.5, 0, 2, 7.8, 0]}, {"Target": "Parameter", "Id": "leidonghua", "Segments": [0, 0, 2, 1.833, 0, 0, 2.667, 0.25, 2, 4.233, 0.25, 1, 4.489, 0.5, 4.744, 0.75, 5, 1, 1, 5.144, 1.333, 5.289, 1.667, 5.433, 2, 2, 5.467, 0, 2, 7.8, 0]}, {"Target": "Parameter", "Id": "youleidonghua", "Segments": [0, 0, 2, 1.833, 0, 0, 2.667, 0.25, 2, 4.233, 0.25, 1, 4.489, 0.5, 4.744, 0.75, 5, 1, 1, 5.144, 1.333, 5.289, 1.667, 5.433, 2, 2, 5.467, 0, 2, 7.8, 0]}, {"Target": "Parameter", "Id": "leimudonghua", "Segments": [0, 0, 2, 1, 0, 0, 1.133, 1, 0, 1.333, 0, 0, 1.467, 1, 0, 1.667, 0, 0, 1.8, 1, 0, 2, 0, 0, 2.133, 1, 0, 2.333, 0, 0, 2.467, 1, 0, 2.667, 0, 0, 2.8, 1, 0, 3, 0, 0, 3.133, 1, 0, 3.333, 0, 0, 3.467, 1, 0, 3.667, 0, 0, 3.8, 1, 0, 4, 0, 0, 4.133, 1, 0, 4.333, 0, 0, 4.467, 1, 0, 4.667, 0, 0, 4.8, 1, 0, 5, 0, 0, 5.133, 1, 0, 5.333, 0, 0, 5.467, 1, 0, 5.667, 0, 0, 5.8, 1, 0, 6, 0, 0, 6.133, 1, 0, 6.333, 0, 2, 7.8, 0]}, {"Target": "Parameter", "Id": "jingyaa", "Segments": [0, 0, 2, 0.167, 0, 0, 0.267, 1, 0, 0.3, 0, 2, 4.233, 0, 2, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamCheek", "Segments": [0, 0, 2, 2.5, 0, 0, 3, 1, 2, 4.233, 1, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "zao1", "Segments": [0, 0.1, 0, 3.167, 0.3, 1, 3.934, 0.3, 4.7, 0.266, 5.467, 0.2, 1, 6.245, 0.133, 7.022, 0.1, 7.8, 0.1]}, {"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 0, 0.167, -16, 0, 0.3, 15, 0, 4.233, 0, 2, 5.2, 0, 0, 6.333, 21, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 0, 0.1, -12, 0, 0.233, 30, 1, 0.3, 30, 0.366, -55.211, 0.433, -60, 1, 0.755, -83.147, 1.078, -90, 1.4, -90, 0, 4.233, 0, 2, 5.2, 0, 0, 5.933, -90, 1, 6.122, -90, 6.311, -67.922, 6.5, -51, 1, 6.933, -12.18, 7.367, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, -30, 2, 0.5, -30, 0, 0.8, -8, 0, 1.3, -30, 0, 2, -4, 1, 2.222, -4, 2.445, -21.188, 2.667, -23.58, 1, 3.189, -29.203, 3.711, -30, 4.233, -30, 1, 4.578, -30, 4.922, -30.472, 5.267, -25.68, 1, 5.622, -20.734, 5.978, 1, 6.333, 1, 0, 7.8, -30]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 1, 0.178, 2.131, 0.355, 20, 0.533, 20, 0, 1, -20, 0, 1.367, 0, 0, 2.233, -20, 1, 2.9, -20, 3.566, -1.573, 4.233, -1, 1, 5.422, 0.022, 6.611, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.333, 0, 0, 0.6, 0.4, 0, 1, -0.2, 1, 1.122, -0.2, 1.245, -0.122, 1.367, 0, 1, 1.578, 0.21, 1.789, 0.3, 2, 0.3, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "shangshenyaobai", "Segments": [0, 0, 0, 0.133, -1, 0, 0.6, 9, 0, 1.367, 0, 0, 2, 9, 0, 4.233, 0, 0, 6.633, 12.182, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 2, 0.667, 0, 0, 1.367, -30, 0, 2.8, 0, 0, 5.4, -15, 0, 7.267, 0, 2, 7.8, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 1, 1, 0, 1.1, -1, 0, 1.3, 0.66, 0, 1.567, -1, 0, 2.6, 1, 0, 3.367, -1, 0, 4.867, 0.3, 0, 5.467, 0, 2, 7.8, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.333, 0, 1, 0.444, 0, 0.556, -0.011, 0.667, -0.28, 1, 0.778, -0.549, 0.889, -1, 1, -1, 0, 1.1, -0.982, 0, 1.233, -1, 2, 4.233, -1, 2, 6.033, -1, 0, 7.5, 0, 2, 7.8, 0]}, {"Target": "Model", "Id": "Opacity", "Segments": [0, 1, 0, 7.8, 1]}, {"Target": "Parameter", "Id": "ParamHairSide", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamHairBack", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "shi<PERSON>ez<PERSON>biaozuoyou", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "Param11", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "tiqiehuan", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "drzbxz", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "drybxz", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "tisousuo", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "dryoutui", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "renzuoyou", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "rens<PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "rendaxiao", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "jingshen", "Segments": [0, 0.5, 0, 7.8, 0.5]}, {"Target": "Parameter", "Id": "ZZZ", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "jingjingtmd", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "jingjingdaixioa", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "<PERSON>ng<PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "jingjingshangxai", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "Param10", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "baiguang<PERSON><PERSON>u", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "lizi1daxoao", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "lizi2daxiao", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "liziyi<PERSON>", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "liziqumian", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "lizizong<PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "jing<PERSON>u", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "andu", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "lanmu", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "heidi", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "bai", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "zu<PERSON><PERSON><PERSON>ng", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "xingfengtmd", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "fizitmd", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "xiongtuceng", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 1, 0, 7.8, 1]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "wu", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "<PERSON>banzhenmian", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "dibantmd", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "Parameter", "Id": "beijingtmd", "Segments": [0, 0, 0, 7.8, 0]}, {"Target": "PartOpacity", "Id": "Part", "Segments": [0, 1, 0, 7.8, 1]}, {"Target": "PartOpacity", "Id": "Part24", "Segments": [0, 1, 0, 7.8, 1]}, {"Target": "PartOpacity", "Id": "Part38", "Segments": [0, 1, 0, 7.8, 1]}, {"Target": "PartOpacity", "Id": "Part3", "Segments": [0, 1, 0, 7.8, 1]}, {"Target": "PartOpacity", "Id": "toushipiaodai1_Skinning", "Segments": [0, 1, 0, 7.8, 1]}, {"Target": "PartOpacity", "Id": "toushipiaodai2_Skinning", "Segments": [0, 1, 0, 7.8, 1]}, {"Target": "PartOpacity", "Id": "Part2", "Segments": [0, 1, 0, 7.8, 1]}, {"Target": "PartOpacity", "Id": "Part18", "Segments": [0, 1, 0, 7.8, 1]}, {"Target": "PartOpacity", "Id": "toudaimao_Skinning", "Segments": [0, 1, 0, 7.8, 1]}, {"Target": "PartOpacity", "Id": "Part4", "Segments": [0, 1, 0, 7.8, 1]}, {"Target": "PartOpacity", "Id": "liuhaisi2_Skinning", "Segments": [0, 1, 0, 7.8, 1]}, {"Target": "PartOpacity", "Id": "Part17", "Segments": [0, 1, 0, 7.8, 1]}, {"Target": "PartOpacity", "Id": "Part22", "Segments": [0, 1, 0, 7.8, 1]}, {"Target": "PartOpacity", "Id": "Part21", "Segments": [0, 1, 0, 7.8, 1]}, {"Target": "PartOpacity", "Id": "cefatiao_Skinning2", "Segments": [0, 1, 0, 7.8, 1]}, {"Target": "PartOpacity", "Id": "Part5", "Segments": [0, 1, 0, 7.8, 1]}, {"Target": "PartOpacity", "Id": "Part6", "Segments": [0, 1, 0, 7.8, 1]}, {"Target": "PartOpacity", "Id": "Part7", "Segments": [0, 1, 0, 7.8, 1]}, {"Target": "PartOpacity", "Id": "Part45", "Segments": [0, 1, 0, 7.8, 1]}, {"Target": "PartOpacity", "Id": "Part26", "Segments": [0, 1, 0, 7.8, 1]}, {"Target": "PartOpacity", "Id": "Part8", "Segments": [0, 1, 0, 7.8, 1]}, {"Target": "PartOpacity", "Id": "Part56", "Segments": [0, 1, 0, 7.8, 1]}, {"Target": "PartOpacity", "Id": "Part27", "Segments": [0, 1, 0, 7.8, 1]}, {"Target": "PartOpacity", "Id": "Part30", "Segments": [0, 1, 0, 7.8, 1]}, {"Target": "PartOpacity", "Id": "Part37", "Segments": [0, 1, 0, 7.8, 1]}, {"Target": "PartOpacity", "Id": "Part31", "Segments": [0, 1, 0, 7.8, 1]}, {"Target": "PartOpacity", "Id": "Part9", "Segments": [0, 1, 0, 7.8, 1]}, {"Target": "PartOpacity", "Id": "Part39", "Segments": [0, 1, 0, 7.8, 1]}, {"Target": "PartOpacity", "Id": "houfatiao_Skinning", "Segments": [0, 1, 0, 7.8, 1]}, {"Target": "PartOpacity", "Id": "Part40", "Segments": [0, 1, 0, 7.8, 1]}, {"Target": "PartOpacity", "Id": "Part16", "Segments": [0, 1, 0, 7.8, 1]}, {"Target": "PartOpacity", "Id": "Part11", "Segments": [0, 1, 0, 7.8, 1]}, {"Target": "PartOpacity", "Id": "Part50", "Segments": [0, 1, 0, 7.8, 1]}, {"Target": "PartOpacity", "Id": "Part49", "Segments": [0, 1, 0, 7.8, 1]}, {"Target": "PartOpacity", "Id": "Part48", "Segments": [0, 1, 0, 7.8, 1]}, {"Target": "PartOpacity", "Id": "Part36", "Segments": [0, 1, 0, 7.8, 1]}, {"Target": "PartOpacity", "Id": "Part44", "Segments": [0, 1, 0, 7.8, 1]}, {"Target": "PartOpacity", "Id": "Part43", "Segments": [0, 1, 0, 7.8, 1]}, {"Target": "PartOpacity", "Id": "Part42", "Segments": [0, 1, 0, 7.8, 1]}, {"Target": "PartOpacity", "Id": "Part41", "Segments": [0, 1, 0, 7.8, 1]}, {"Target": "PartOpacity", "Id": "Part47", "Segments": [0, 1, 0, 7.8, 1]}, {"Target": "PartOpacity", "Id": "Part28", "Segments": [0, 1, 0, 7.8, 1]}, {"Target": "PartOpacity", "Id": "Part12", "Segments": [0, 1, 0, 7.8, 1]}, {"Target": "PartOpacity", "Id": "Part20", "Segments": [0, 1, 0, 7.8, 1]}, {"Target": "PartOpacity", "Id": "Part19", "Segments": [0, 1, 0, 7.8, 1]}, {"Target": "PartOpacity", "Id": "Part51", "Segments": [0, 1, 0, 7.8, 1]}, {"Target": "PartOpacity", "Id": "Part53", "Segments": [0, 1, 0, 7.8, 1]}, {"Target": "PartOpacity", "Id": "Part52", "Segments": [0, 1, 0, 7.8, 1]}, {"Target": "PartOpacity", "Id": "Part10", "Segments": [0, 1, 0, 7.8, 1]}, {"Target": "PartOpacity", "Id": "Part15", "Segments": [0, 1, 0, 7.8, 1]}, {"Target": "PartOpacity", "Id": "Part25", "Segments": [0, 1, 0, 7.8, 1]}, {"Target": "PartOpacity", "Id": "Part23", "Segments": [0, 1, 0, 7.8, 1]}, {"Target": "PartOpacity", "Id": "Part13", "Segments": [0, 1, 0, 7.8, 1]}, {"Target": "PartOpacity", "Id": "Part14", "Segments": [0, 1, 0, 7.8, 1]}, {"Target": "PartOpacity", "Id": "Part29", "Segments": [0, 0, 0, 7.8, 0]}], "UserData": [{"Time": 0.0, "Value": ""}, {"Time": 7.3, "Value": ""}]}