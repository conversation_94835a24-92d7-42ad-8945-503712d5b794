{"Version": 3, "Meta": {"Duration": 6.0, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 200, "TotalSegmentCount": 1105, "TotalPointCount": 1251, "UserDataCount": 2, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "wuli", "Segments": [0, 0, 0, 0.333, -1, 0, 0.5, 1, 0, 4, -1, 0, 4.167, 1, 0, 4.833, -1, 0, 5.133, 1, 0, 6, 0]}, {"Target": "Parameter", "Id": "Para", "Segments": [0, 0, 0, 0.333, -1, 0, 0.433, 1, 0, 3.533, -1, 0, 4.233, 1, 0, 4.967, -1, 0, 5.5, 1, 0, 6, 0]}, {"Target": "Parameter", "Id": "zuocefati1", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0.073, 0.033, -0.069, 1, 0.066, -0.497, 0.1, -3.264, 0.133, -3.264, 0, 0.367, 7.573, 0, 0.533, -4.631, 0, 1, 1.959, 0, 1.433, -1.379, 0, 1.867, 1, 0, 2.267, 0.047, 0, 2.633, 0.502, 0, 3.167, -0.105, 0, 3.367, -0.085, 0, 3.767, -0.392, 0, 4.033, -0.039, 0, 4.167, -8.935, 0, 4.5, 9.365, 0, 5.033, -11.556, 0, 5.4, 10.681, 0, 5.833, -5.967, 0, 6, 0]}, {"Target": "Parameter", "Id": "zuocefati2", "Segments": [0, 0, 0, 0.033, -0.247, 0, 0.133, 1.398, 0, 0.267, -6.307, 0, 0.5, 13.963, 0, 0.767, -9.036, 0, 1.133, 4.26, 0, 1.533, -2.926, 0, 2, 1.176, 0, 2.333, -0.091, 0, 2.767, 1.276, 0, 3.267, 0.512, 0, 3.433, 0.536, 0, 3.967, -0.413, 0, 4.133, 3.618, 0, 4.333, -14.855, 0, 4.667, 15.47, 0, 5.2, -18.306, 0, 5.567, 15.569, 1, 5.711, 15.569, 5.856, 11.268, 6, 0]}, {"Target": "Parameter", "Id": "zuocefati3", "Segments": [0, 0, 1, 0.067, 0.056, 0.133, 2.112, 0.2, 2.112, 0, 0.433, -8.712, 0, 0.633, 13.085, 0, 0.933, -11.28, 0, 1.233, 7.48, 0, 1.6, -4.162, 0, 1.967, 1.728, 0, 2.4, -0.84, 0, 2.833, 0.538, 0, 3.233, -0.225, 0, 3.667, 0.228, 0, 4.033, -0.357, 0, 4.233, 7.828, 0, 4.467, -13.481, 0, 4.8, 14.411, 0, 5.267, -12.547, 0, 5.667, 13.571, 1, 5.778, 13.571, 5.889, 10.11, 6, 0]}, {"Target": "Parameter", "Id": "zuocefati4", "Segments": [0, 0, 1, 0.089, 0.21, 0.178, 1.418, 0.267, 1.418, 0, 0.5, -7.387, 0, 0.733, 14.053, 0, 1.033, -15.641, 0, 1.367, 12.373, 0, 1.7, -8.245, 0, 2.033, 4.316, 0, 2.433, -1.931, 0, 2.833, 0.91, 0, 3.267, -0.439, 0, 3.7, 0.342, 0, 4.067, -0.357, 0, 4.3, 5.87, 0, 4.533, -14.707, 0, 4.867, 18.705, 0, 5.333, -13.546, 0, 5.733, 16.574, 1, 5.822, 16.574, 5.911, 12.429, 6, 0]}, {"Target": "Parameter", "Id": "zuocefati5", "Segments": [0, 0, 0, 0.333, 1.213, 0, 0.567, -7.398, 0, 0.833, 15.244, 0, 1.133, -19.408, 0, 1.467, 17.362, 0, 1.8, -13.307, 0, 2.133, 8.419, 0, 2.5, -4.687, 0, 2.867, 2.177, 0, 3.267, -0.842, 0, 3.733, 0.523, 0, 4.1, -0.372, 0, 4.4, 5.406, 0, 4.6, -15.394, 0, 4.933, 21.941, 0, 5.367, -15.796, 0, 5.8, 18.8, 1, 5.867, 18.8, 5.933, 11.726, 6, 0]}, {"Target": "Parameter", "Id": "fashipiaodai1", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0.044, 0.033, -0.052, 1, 0.066, -0.341, 0.1, -1.205, 0.133, -1.205, 0, 0.333, 7.512, 0, 0.533, -14.326, 0, 0.933, 7.821, 0, 1.333, -4.597, 0, 1.767, 2.936, 0, 2.2, -1.21, 0, 2.6, 1.23, 0, 3.033, -0.514, 0, 3.433, 0.179, 0, 3.8, -0.516, 0, 4.033, -0.197, 0, 4.167, -15.362, 0, 4.533, 15.171, 0, 5.033, -19.125, 0, 5.4, 16.508, 0, 5.867, -9.135, 0, 6, 0]}, {"Target": "Parameter", "Id": "fashipiaodai2", "Segments": [0, 0, 0, 0.033, -0.185, 0, 0.1, 0.557, 0, 0.233, -3.899, 0, 0.467, 16.671, 0, 0.7, -20.953, 0, 1.067, 13.532, 0, 1.467, -7.982, 0, 1.9, 4.719, 0, 2.3, -1.899, 0, 2.733, 2.636, 0, 3.167, -0.074, 0, 3.533, 1.031, 0, 4, -0.573, 0, 4.133, 5.39, 0, 4.3, -25.289, 0, 4.667, 23.256, 0, 5.2, -29.872, 0, 5.567, 22.876, 1, 5.711, 22.876, 5.856, 16.62, 6, 0]}, {"Target": "Parameter", "Id": "fashipiaodai3", "Segments": [0, 0, 1, 0.056, 0.014, 0.111, 0.619, 0.167, 0.619, 0, 0.4, -6.751, 0, 0.567, 20.473, 0, 0.833, -21.275, 0, 1.167, 15.8, 0, 1.567, -9.895, 0, 1.933, 5.548, 0, 2.367, -3.165, 0, 2.767, 1.92, 0, 3.2, -1.065, 0, 3.633, 0.717, 0, 4.033, -0.575, 0, 4.233, 14.967, 0, 4.433, -20.565, 0, 4.8, 20.086, 0, 5.267, -19.918, 0, 5.667, 19.073, 1, 5.778, 19.073, 5.889, 14.205, 6, 0]}, {"Target": "Parameter", "Id": "fashipiaodai4", "Segments": [0, 0, 1, 0.067, 0.079, 0.133, 0.336, 0.2, 0.336, 0, 0.433, -5.817, 0, 0.667, 18.752, 0, 0.933, -25.802, 0, 1.267, 22.396, 0, 1.633, -15.943, 0, 2.033, 9.979, 0, 2.4, -5.903, 0, 2.8, 3.367, 0, 3.233, -1.892, 0, 3.667, 1.187, 0, 4.067, -0.772, 0, 4.3, 10.348, 0, 4.5, -21.237, 0, 4.867, 24.827, 0, 5.333, -19.866, 0, 5.733, 22.65, 1, 5.822, 22.65, 5.911, 16.805, 6, 0]}, {"Target": "Parameter", "Id": "fashipiaodai5", "Segments": [0, 0, 0, 0.267, 0.282, 0, 0.5, -4.484, 0, 0.733, 17.589, 0, 1, -29.638, 0, 1.367, 27.624, 0, 1.733, -22.162, 0, 2.1, 15.75, 0, 2.5, -10.364, 0, 2.867, 6.303, 0, 3.267, -3.484, 0, 3.7, 2.033, 0, 4.067, -1.089, 0, 4.367, 9.309, 0, 4.567, -22.222, 0, 4.9, 29.787, 0, 5.367, -21.285, 0, 5.8, 24.943, 1, 5.867, 24.943, 5.933, 14.615, 6, 0]}, {"Target": "Parameter", "Id": "fashipiaodai6", "Segments": [0, 0, 1, 0.022, -0.026, 0.045, -0.085, 0.067, -0.085, 0, 0.133, -0.068, 0, 0.167, -0.11, 0, 0.333, 0.141, 0, 0.6, -5.291, 0, 0.8, 17.237, 0, 1.067, -30, 0, 1.4, 30, 2, 1.433, 30, 0, 1.8, -27.625, 0, 2.2, 21.596, 0, 2.567, -16.081, 0, 2.967, 10.812, 0, 3.333, -6.577, 0, 3.733, 3.768, 0, 4.1, -1.765, 0, 4.433, 9.119, 0, 4.633, -22.006, 0, 4.933, 30, 2, 4.967, 30, 0, 5.4, -23.455, 0, 5.833, 28.07, 0, 6, 0]}, {"Target": "Parameter", "Id": "daimao1", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0.088, 0.033, -0.087, 1, 0.066, -0.61, 0.1, -4.08, 0.133, -4.08, 0, 0.367, 9.466, 0, 0.533, -5.789, 0, 1, 2.449, 0, 1.433, -1.724, 0, 1.867, 1.25, 0, 2.267, 0.058, 0, 2.633, 0.627, 0, 3.167, -0.132, 0, 3.367, -0.106, 0, 3.767, -0.489, 0, 4.033, -0.049, 0, 4.167, -11.169, 0, 4.5, 11.706, 0, 5.033, -14.445, 0, 5.4, 13.351, 0, 5.833, -7.459, 0, 6, 0]}, {"Target": "Parameter", "Id": "daimao2", "Segments": [0, 0, 0, 0.033, -0.309, 0, 0.133, 1.747, 0, 0.267, -7.883, 0, 0.5, 17.454, 0, 0.767, -11.295, 0, 1.133, 5.325, 0, 1.533, -3.657, 0, 2, 1.47, 0, 2.333, -0.114, 0, 2.767, 1.595, 0, 3.267, 0.64, 0, 3.433, 0.671, 0, 3.967, -0.517, 0, 4.133, 4.523, 0, 4.333, -18.569, 0, 4.667, 19.337, 0, 5.2, -22.882, 0, 5.567, 19.462, 1, 5.711, 19.462, 5.856, 14.1, 6, 0]}, {"Target": "Parameter", "Id": "daimao3", "Segments": [0, 0, 1, 0.067, 0.07, 0.133, 2.64, 0.2, 2.64, 0, 0.433, -10.89, 0, 0.633, 16.357, 0, 0.933, -14.1, 0, 1.233, 9.349, 0, 1.6, -5.202, 0, 1.967, 2.161, 0, 2.4, -1.05, 0, 2.833, 0.672, 0, 3.233, -0.281, 0, 3.667, 0.285, 0, 4.033, -0.447, 0, 4.233, 9.785, 0, 4.467, -16.852, 0, 4.8, 18.014, 0, 5.267, -15.683, 0, 5.667, 16.964, 1, 5.778, 16.964, 5.889, 12.657, 6, 0]}, {"Target": "Parameter", "Id": "daimao4", "Segments": [0, 0, 1, 0.089, 0.262, 0.178, 1.772, 0.267, 1.772, 0, 0.5, -9.234, 0, 0.733, 17.566, 0, 1.033, -19.552, 0, 1.367, 15.466, 0, 1.7, -10.306, 0, 2.033, 5.395, 0, 2.433, -2.413, 0, 2.833, 1.138, 0, 3.267, -0.549, 0, 3.7, 0.427, 0, 4.067, -0.446, 0, 4.3, 7.337, 0, 4.533, -18.383, 0, 4.867, 23.382, 0, 5.333, -16.933, 0, 5.733, 20.718, 1, 5.822, 20.718, 5.911, 15.554, 6, 0]}, {"Target": "Parameter", "Id": "Param", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0.086, 0.033, -0.083, 1, 0.066, -0.592, 0.1, -4.399, 0.133, -4.399, 0, 0.367, 8.42, 0, 0.733, -4.112, 0, 1.167, 1.869, 0, 1.567, -1.28, 0, 2.033, 1.103, 0, 2.5, 0.133, 0, 2.833, 0.363, 0, 3.733, -0.376, 0, 4.033, 0.014, 0, 4.167, -7.18, 0, 4.5, 7.75, 0, 5.033, -9.464, 0, 5.4, 9.012, 0, 5.833, -5.105, 0, 6, 0]}, {"Target": "Parameter", "Id": "Param2", "Segments": [0, 0, 0, 0.033, -0.297, 0, 0.133, 1.927, 0, 0.267, -7.947, 0, 0.5, 14.583, 0, 0.867, -7.909, 0, 1.267, 2.951, 0, 1.7, -2.73, 0, 2.167, 1.26, 0, 2.5, 0.308, 0, 2.967, 1.149, 0, 3.967, -0.369, 0, 4.133, 3.096, 0, 4.333, -12.174, 0, 4.667, 13.286, 0, 5.2, -15.281, 0, 5.567, 13.6, 0, 5.967, -7.499, 0, 6, 0]}, {"Target": "Parameter", "Id": "Param3", "Segments": [0, 0, 1, 0.067, 0.037, 0.133, 3.155, 0.2, 3.155, 0, 0.433, -10.635, 0, 0.667, 13.361, 0, 0.967, -9.896, 0, 1.333, 5.719, 0, 1.733, -2.855, 0, 2.167, 1.255, 0, 2.567, -0.634, 0, 3, 0.308, 0, 3.533, -0.048, 0, 3.8, 0.129, 0, 4.033, -0.271, 0, 4.233, 5.993, 0, 4.467, -11.649, 0, 4.8, 12.932, 0, 5.3, -10.763, 0, 5.667, 12.242, 1, 5.778, 12.242, 5.889, 9.123, 6, 0]}, {"Target": "Parameter", "Id": "Param4", "Segments": [0, 0, 1, 0.089, 0.273, 0.178, 2.176, 0.267, 2.176, 0, 0.5, -9.793, 0, 0.767, 15.89, 0, 1.1, -14.722, 0, 1.433, 10.268, 0, 1.8, -6.038, 0, 2.167, 2.746, 0, 2.6, -1.211, 0, 3, 0.574, 0, 3.4, -0.127, 0, 3.867, 0.142, 0, 4.067, -0.156, 0, 4.3, 4.634, 0, 4.567, -12.736, 0, 4.867, 17.013, 0, 5.333, -12.304, 0, 5.733, 15.274, 1, 5.822, 15.274, 5.911, 11.511, 6, 0]}, {"Target": "Parameter", "Id": "Param5", "Segments": [0, 0, 0, 0.333, 1.862, 0, 0.6, -9.367, 0, 0.833, 18.032, 0, 1.167, -19.428, 0, 1.533, 15.343, 0, 1.9, -10.534, 0, 2.233, 5.861, 0, 2.633, -2.807, 0, 3, 1.211, 0, 3.433, -0.36, 0, 3.9, 0.163, 0, 4.133, -0.063, 0, 4.4, 4.393, 0, 4.633, -13.591, 0, 4.933, 20.356, 0, 5.367, -15.375, 0, 5.8, 17.773, 1, 5.867, 17.773, 5.933, 11.385, 6, 0]}, {"Target": "Parameter", "Id": "ZUOHOUFABG1", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0.068, 0.033, -0.062, 1, 0.066, -0.454, 0.1, -3.299, 0.133, -3.299, 0, 0.367, 6.315, 0, 0.733, -3.084, 0, 1.167, 1.402, 0, 1.567, -0.96, 0, 2.033, 0.827, 0, 2.5, 0.1, 0, 2.833, 0.272, 0, 3.733, -0.282, 0, 4.033, 0.011, 0, 4.167, -5.385, 0, 4.5, 5.812, 0, 5.033, -7.098, 0, 5.4, 6.759, 0, 5.833, -3.828, 0, 6, 0]}, {"Target": "Parameter", "Id": "ZUOHOUFABG2", "Segments": [0, 0, 0, 0.033, -0.222, 0, 0.133, 1.445, 0, 0.267, -5.96, 0, 0.5, 10.937, 0, 0.867, -5.932, 0, 1.267, 2.213, 0, 1.7, -2.047, 0, 2.167, 0.945, 0, 2.5, 0.231, 0, 2.967, 0.861, 0, 3.967, -0.277, 0, 4.133, 2.322, 0, 4.333, -9.13, 0, 4.667, 9.965, 0, 5.2, -11.461, 0, 5.567, 10.2, 0, 5.967, -5.625, 0, 6, 0]}, {"Target": "Parameter", "Id": "ZUOHOUFABG3", "Segments": [0, 0, 1, 0.067, 0.028, 0.133, 2.366, 0.2, 2.366, 0, 0.433, -7.976, 0, 0.667, 10.021, 0, 0.967, -7.422, 0, 1.333, 4.289, 0, 1.733, -2.142, 0, 2.167, 0.941, 0, 2.567, -0.476, 0, 3, 0.231, 0, 3.533, -0.036, 0, 3.8, 0.097, 0, 4.033, -0.203, 0, 4.233, 4.494, 0, 4.467, -8.737, 0, 4.8, 9.699, 0, 5.3, -8.072, 0, 5.667, 9.181, 1, 5.778, 9.181, 5.889, 6.83, 6, 0]}, {"Target": "Parameter", "Id": "hounaofa1yaobai", "Segments": [0, 0, 0, 0.133, -0.462, 0, 0.333, 0.572, 0, 0.633, -0.236, 0, 0.867, 0.042, 0, 1.133, -0.059, 0, 1.4, -0.022, 0, 1.6, -0.027, 0, 2.367, 0.026, 0, 2.533, 0.022, 2, 2.567, 0.022, 2, 2.6, 0.022, 0, 2.733, 0.025, 0, 2.767, 0.024, 0, 2.833, 0.025, 0, 3.733, -0.013, 0, 3.967, 0.004, 0, 4.167, -0.183, 0, 4.367, 0.147, 0, 4.633, -0.026, 0, 4.8, -0.001, 0, 5.033, -0.111, 0, 5.267, 0.1, 0, 5.533, -0.022, 0, 5.767, 0.014, 1, 5.845, 0.014, 5.922, 0.011, 6, 0]}, {"Target": "Parameter", "Id": "hounaifa1bianxing", "Segments": [0, 0, 2, 0.033, 0, 1, 0.066, 0.001, 0.1, 0.471, 0.133, 0.471, 0, 0.267, -0.99, 0, 0.433, 1, 2, 0.5, 1, 0, 0.7, -0.662, 0, 0.967, 0.368, 0, 1.233, -0.176, 0, 1.5, 0.073, 0, 1.733, -0.035, 0, 2, 0.011, 0, 2.267, -0.01, 0, 2.467, 0.012, 0, 2.7, -0.007, 0, 2.967, 0.006, 0, 3.2, 0, 0, 3.4, 0.003, 2, 3.467, 0.003, 0, 3.5, 0.004, 0, 3.633, 0.003, 2, 3.667, 0.003, 0, 3.867, -0.017, 0, 4.133, 0.219, 0, 4.267, -0.379, 0, 4.5, 0.316, 0, 4.733, -0.174, 0, 4.967, 0.159, 0, 5.2, -0.246, 0, 5.4, 0.224, 0, 5.633, -0.133, 0, 5.9, 0.068, 1, 5.933, 0.068, 5.967, 0.026, 6, 0]}, {"Target": "Parameter", "Id": "hounaofa2yaobai", "Segments": [0, 0, 0, 0.1, -1, 2, 0.167, -1, 0, 0.267, 1, 2, 0.4, 1, 0, 0.633, -0.945, 0, 0.867, 0.17, 0, 1.133, -0.234, 0, 1.4, -0.086, 0, 1.6, -0.109, 0, 2.367, 0.103, 0, 2.533, 0.089, 0, 2.567, 0.09, 0, 2.6, 0.089, 0, 2.733, 0.098, 2, 2.767, 0.098, 0, 2.833, 0.099, 0, 3.733, -0.052, 0, 3.967, 0.015, 0, 4.167, -0.732, 0, 4.367, 0.589, 0, 4.633, -0.106, 0, 4.8, -0.004, 0, 5.033, -0.446, 0, 5.267, 0.401, 0, 5.533, -0.089, 0, 5.767, 0.056, 1, 5.845, 0.056, 5.922, 0.043, 6, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 0.133, -0.462, 0, 0.333, 0.572, 0, 0.633, -0.236, 0, 0.867, 0.042, 0, 1.133, -0.059, 0, 1.4, -0.022, 0, 1.6, -0.027, 0, 2.367, 0.026, 0, 2.533, 0.022, 2, 2.567, 0.022, 2, 2.6, 0.022, 0, 2.733, 0.025, 0, 2.767, 0.024, 0, 2.833, 0.025, 0, 3.733, -0.013, 0, 3.967, 0.004, 0, 4.167, -0.183, 0, 4.367, 0.147, 0, 4.633, -0.026, 0, 4.8, -0.001, 0, 5.033, -0.111, 0, 5.267, 0.1, 0, 5.533, -0.022, 0, 5.767, 0.014, 1, 5.845, 0.014, 5.922, 0.011, 6, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 0.133, -0.266, 0, 0.367, 0.341, 0, 0.7, -0.173, 0, 1, 0.03, 0, 1.3, -0.044, 0, 1.667, -0.011, 0, 1.767, -0.012, 0, 2.4, 0.02, 0, 2.6, 0.017, 0, 2.833, 0.018, 2, 2.867, 0.018, 2, 2.9, 0.018, 0, 3.733, -0.009, 0, 4.033, 0.003, 0, 4.167, -0.111, 0, 4.4, 0.086, 0, 4.767, -0.022, 0, 4.867, -0.019, 0, 5.033, -0.068, 0, 5.3, 0.06, 0, 5.633, -0.013, 0, 5.9, 0.006, 1, 5.933, 0.006, 5.967, 0.003, 6, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.033, 0, 1, 0.066, 0.001, 0.1, 0.161, 0.133, 0.161, 0, 0.267, -0.29, 0, 0.533, 0.344, 0, 0.833, -0.234, 0, 1.133, 0.131, 0, 1.433, -0.066, 0, 1.733, 0.028, 0, 2.067, -0.015, 0, 2.433, 0.007, 0, 2.733, -0.003, 0, 3.067, 0.003, 0, 3.367, 0, 0, 3.7, 0.002, 0, 3.9, -0.007, 0, 4.133, 0.074, 0, 4.333, -0.112, 0, 4.6, 0.093, 0, 4.867, -0.048, 0, 5.033, 0.017, 0, 5.233, -0.059, 0, 5.467, 0.063, 0, 5.767, -0.039, 1, 5.845, -0.039, 5.922, -0.029, 6, 0]}, {"Target": "Parameter", "Id": "ParamHairFront", "Segments": [0, 0, 0, 0.133, -1, 0, 0.333, 1, 2, 0.433, 1, 0, 0.7, -0.694, 0, 1, 0.119, 0, 1.3, -0.176, 0, 1.667, -0.045, 0, 1.767, -0.048, 0, 2.4, 0.079, 0, 2.6, 0.068, 0, 2.833, 0.073, 0, 2.867, 0.072, 2, 2.9, 0.072, 0, 3.733, -0.037, 0, 4.033, 0.011, 0, 4.167, -0.444, 0, 4.4, 0.344, 0, 4.767, -0.088, 0, 4.867, -0.076, 0, 5.033, -0.271, 0, 5.3, 0.241, 0, 5.633, -0.053, 0, 5.9, 0.025, 1, 5.933, 0.025, 5.967, 0.011, 6, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 0.133, 0.42, 0, 0.3, -0.552, 0, 0.567, 0.457, 0, 0.867, -0.397, 0, 1.167, 0.306, 0, 1.5, -0.129, 0, 1.833, 0.1, 0, 2.167, -0.043, 0, 2.5, 0.008, 0, 2.833, -0.037, 0, 3.233, -0.01, 0, 3.367, -0.013, 0, 3.733, 0.006, 0, 3.933, -0.008, 0, 4.167, 0.179, 0, 4.333, -0.184, 0, 4.633, 0.127, 0, 4.867, -0.077, 0, 5.067, 0.053, 0, 5.233, -0.072, 0, 5.5, 0.087, 0, 5.8, -0.08, 1, 5.867, -0.08, 5.933, -0.059, 6, 0]}, {"Target": "Parameter", "Id": "Param6", "Segments": [0, 0, 1, 0.011, 0, 0.022, 0.005, 0.033, -0.004, 1, 0.066, -0.032, 0.1, -0.22, 0.133, -0.22, 0, 0.367, 0.421, 0, 0.733, -0.206, 0, 1.167, 0.093, 0, 1.567, -0.064, 0, 2.033, 0.055, 0, 2.5, 0.007, 0, 2.833, 0.018, 0, 3.733, -0.019, 0, 4.033, 0.001, 0, 4.167, -0.359, 0, 4.5, 0.387, 0, 5.033, -0.473, 0, 5.4, 0.451, 0, 5.833, -0.255, 0, 6, 0]}, {"Target": "Parameter", "Id": "Param7", "Segments": [0, 0, 0, 0.033, -0.015, 0, 0.133, 0.096, 0, 0.267, -0.397, 0, 0.5, 0.729, 0, 0.867, -0.395, 0, 1.267, 0.148, 0, 1.7, -0.136, 0, 2.167, 0.063, 0, 2.5, 0.015, 0, 2.967, 0.057, 0, 3.967, -0.018, 0, 4.133, 0.155, 0, 4.333, -0.609, 0, 4.667, 0.664, 0, 5.2, -0.764, 0, 5.567, 0.68, 0, 5.967, -0.375, 0, 6, 0]}, {"Target": "Parameter", "Id": "Param8", "Segments": [0, 0, 1, 0.067, 0.002, 0.133, 0.158, 0.2, 0.158, 0, 0.433, -0.532, 0, 0.667, 0.668, 0, 0.967, -0.495, 0, 1.333, 0.286, 0, 1.733, -0.143, 0, 2.167, 0.063, 0, 2.567, -0.032, 0, 3, 0.015, 0, 3.533, -0.002, 0, 3.8, 0.006, 0, 4.033, -0.014, 0, 4.233, 0.3, 0, 4.467, -0.582, 0, 4.8, 0.647, 0, 5.3, -0.538, 0, 5.667, 0.612, 1, 5.778, 0.612, 5.889, 0.462, 6, 0]}, {"Target": "Parameter", "Id": "Param9", "Segments": [0, 0, 1, 0.089, 0.014, 0.178, 0.109, 0.267, 0.109, 0, 0.5, -0.49, 0, 0.767, 0.794, 0, 1.1, -0.736, 0, 1.433, 0.513, 0, 1.8, -0.302, 0, 2.167, 0.137, 0, 2.6, -0.061, 0, 3, 0.029, 0, 3.4, -0.006, 0, 3.867, 0.007, 0, 4.067, -0.008, 0, 4.3, 0.232, 0, 4.567, -0.637, 0, 4.867, 0.851, 0, 5.333, -0.615, 0, 5.733, 0.764, 1, 5.822, 0.764, 5.911, 0.579, 6, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 0.8, 1, 2, 3.667, 1, 2, 5.1, 1, 0, 6, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.7, 0, 0, 0.733, -1, 0, 1.433, 0, 0, 2, -0.4, 2, 3.667, -0.4, 0, 4.4, -1, 2, 5.1, -1, 0, 5.233, 0, 2, 6, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.4, 0, 2, 0.7, 0, 0, 0.733, -1, 0, 2, 0.5, 2, 3.667, 0.5, 0, 4.133, 0.8, 0, 4.967, -0.5, 2, 5.1, -0.5, 0, 5.233, 0, 2, 6, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.4, 0, 2, 0.533, 0, 0, 1.167, -0.5, 0, 2, 0.4, 0, 2.733, -0.1, 0, 3.667, 0, 0, 3.967, -0.8, 0, 4.3, 1, 2, 4.833, 1, 0, 5.1, -1, 0, 5.233, 0, 2, 6, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 1, 0, 1.167, -1, 2, 2, -1, 2, 3.667, -1, 0, 6, 1]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 1.033, -1, 2, 5, -1, 0, 6, 0]}, {"Target": "Parameter", "Id": "xingfengtmd", "Segments": [0, 0, 2, 0.533, 0, 0, 0.8, 1, 2, 4.767, 1, 0, 4.9, 0, 2, 6, 0]}, {"Target": "Parameter", "Id": "xingfengyaobai", "Segments": [0, 0, 0, 1.5, -1, 0, 2.333, 1, 0, 2.767, -1, 0, 3.1, 0.501, 0, 3.4, -0.3, 0, 3.567, 0, 2, 3.667, 0, 2, 6, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 1, 0.111, 0, 0.222, -0.15, 0.333, -0.2, 1, 0.489, -0.27, 0.644, -0.273, 0.8, -0.273, 0, 1.333, 0.5, 1, 1.555, 0.5, 1.778, 0.518, 2, 0.448, 1, 2.556, 0.272, 3.111, 0, 3.667, 0, 2, 6, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON>biji<PERSON><PERSON>", "Segments": [0, 0, 1, 0.267, -0.258, 0.533, -0.416, 0.8, -0.416, 0, 3, 0.458, 0, 3.667, 0, 2, 6, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 1, 0.267, 0, 0.533, -0.19, 0.8, -0.228, 1, 0.967, -0.252, 1.133, -0.244, 1.3, -0.244, 0, 2, 0, 2, 3.667, 0, 0, 4.6, -0.24, 0, 6, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 1, 0.267, 0, 0.533, 0.029, 0.8, 0.136, 1, 0.967, 0.203, 1.133, 0.275, 1.3, 0.275, 0, 2, 0, 2, 3.667, 0, 0, 4.6, -0.102, 0, 6, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 1, 0.267, -0.585, 0.533, -1, 0.8, -1, 2, 1.3, -1, 0, 1.733, 1, 2, 3, 1, 0, 3.667, 0, 2, 6, 0]}, {"Target": "Parameter", "Id": "xiongshangxai", "Segments": [0, 0, 0, 0.133, -0.616, 0, 0.267, 1, 2, 0.3, 1, 0, 0.433, -1, 2, 0.467, -1, 0, 0.6, 0.621, 0, 0.833, -0.229, 0, 1.033, 0.034, 0, 1.267, -0.052, 0, 1.533, -0.013, 2, 1.567, -0.013, 2, 1.6, -0.013, 2, 1.633, -0.013, 0, 2.333, 0.056, 0, 2.533, 0.048, 0, 2.567, 0.049, 0, 2.6, 0.048, 0, 2.667, 0.05, 2, 2.7, 0.05, 2, 2.733, 0.05, 0, 3.767, -0.27, 0, 4.4, 0.224, 0, 5.167, -0.372, 0, 5.6, 0.299, 1, 5.733, 0.299, 5.867, 0.219, 6, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 0.133, -0.308, 0, 0.3, 0.538, 0, 0.467, -0.814, 0, 0.6, 0.31, 0, 0.833, -0.114, 0, 1.033, 0.017, 0, 1.267, -0.026, 0, 1.533, -0.007, 2, 1.567, -0.007, 0, 1.6, -0.006, 2, 1.633, -0.006, 0, 2.333, 0.028, 0, 2.533, 0.024, 2, 2.567, 0.024, 2, 2.6, 0.024, 0, 2.667, 0.025, 2, 2.7, 0.025, 2, 2.733, 0.025, 0, 3.767, -0.135, 0, 4.4, 0.112, 0, 5.167, -0.186, 0, 5.6, 0.15, 1, 5.733, 0.15, 5.867, 0.11, 6, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 2, 0.133, 1, 0, 0.267, 0, 0, 0.533, 1, 2, 1.667, 1, 0, 2.167, 0, 2, 2.7, 0, 0, 3.667, 1, 2, 6, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 2, 1.667, 0, 0, 2.167, 1, 2, 2.7, 1, 0, 3.667, 0, 2, 6, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 2, 0.133, 1, 0, 0.267, 0, 0, 0.533, 1, 2, 1.667, 1, 0, 2.167, 0, 2, 2.7, 0, 0, 3.667, 1, 2, 6, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 2, 1.667, 0, 0, 2.167, 1, 2, 2.7, 1, 0, 3.667, 0, 2, 6, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON>kongda<PERSON><PERSON>", "Segments": [0, 0, 2, 0.133, 0, 0, 0.267, 1, 0, 0.533, -1, 0, 0.667, 0.5, 0, 0.767, -0.2, 0, 1.1, 0, 2, 2.533, 0, 0, 2.7, 1, 0, 3.033, -1, 0, 3.2, 0.5, 0, 3.333, -0.2, 0, 3.467, 0, 2, 3.667, 0, 2, 6, 0]}, {"Target": "Parameter", "Id": "gaoguangdaxia<PERSON>", "Segments": [0, 0, 2, 0.267, 0, 0, 0.433, 1, 0, 0.567, 0, 0, 0.767, 0.9, 0, 1.167, 0, 0, 1.3, 0.4, 0, 1.433, 0, 2, 2.7, 0, 0, 2.933, 1, 0, 3.1, 0, 0, 3.333, 0.9, 0, 3.533, 0, 2, 3.667, 0, 2, 6, 0]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, -1, 0, 2.167, 0, 2, 3.667, 0, 0, 6, -1]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, -1, 0, 2.167, 0, 2, 3.667, 0, 0, 6, -1]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 0, 0, 0.367, 1, 0, 0.5, 0, 0, 0.667, 1, 0, 0.833, 0, 0, 1.4, 1, 0, 1.533, 0, 0, 1.933, 0.709, 0, 2.467, 0, 0, 2.667, 1, 0, 3.667, 0, 2, 6, 0]}, {"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 0, 0.3, -33, 0, 1.033, 24, 0, 1.933, -48, 1, 2.511, -48, 3.089, -34.87, 3.667, 0, 1, 3.8, 8.047, 3.934, 21, 4.067, 21, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 0, 0.7, -90, 0, 1.933, 0, 0, 2.967, -90, 1, 3.2, -90, 3.434, -57.086, 3.667, 0, 1, 3.767, 24.466, 3.867, 33, 3.967, 33, 0, 4.233, -51, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, -30, 2, 3.667, -30, 0, 4.533, 1, 0, 6, -30]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 1, 0.044, 0.469, 0.089, 20, 0.133, 20, 0, 0.567, -20, 0, 2.233, 20, 0, 3.667, 0, 2, 6, 0]}, {"Target": "Parameter", "Id": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 2, 0.3, 0, 3.667, 0, 2, 6, 0]}, {"Target": "Parameter", "Id": "shangshenyaobai", "Segments": [0, 0, 1, 0.267, 0.842, 0.533, 1, 0.8, 1, 0, 3, -2, 1, 3.222, -2, 3.445, -2.795, 3.667, 0, 1, 4.056, 4.892, 4.444, 24, 4.833, 24, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.178, -1.094, 0.355, -27, 0.533, -27, 0, 2, 30, 0, 3.667, 0, 2, 6, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 0.133, -0.436, 0, 0.333, 0.581, 0, 0.533, -0.318, 0, 0.8, 0.037, 0, 1.067, -0.053, 0, 1.333, -0.023, 0, 1.5, -0.027, 0, 2.367, 0.029, 0, 2.533, 0.025, 2, 2.567, 0.025, 2, 2.6, 0.025, 0, 2.733, 0.027, 2, 2.767, 0.027, 2, 2.8, 0.027, 0, 3.733, -0.013, 0, 3.967, 0.003, 0, 4.167, -0.359, 0, 4.367, 0.282, 0, 4.633, -0.048, 0, 4.8, -0.003, 0, 5.033, -0.221, 0, 5.267, 0.198, 0, 5.533, -0.043, 0, 5.767, 0.028, 1, 5.845, 0.028, 5.922, 0.021, 6, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 3.667, 0, 0, 4.6, -0.3, 0, 6, 0]}, {"Target": "Model", "Id": "Opacity", "Segments": [0, 1, 0, 6, 1]}, {"Target": "Parameter", "Id": "ParamHairSide", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamHairBack", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "shi<PERSON>ez<PERSON>biaozuoyou", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Param11", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "tiqiehuan", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "drzbxz", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "drybxz", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "tisousuo", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "dryoutui", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "renzuoyou", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "rens<PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "rendaxiao", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "jingshen", "Segments": [0, 0.5, 0, 6, 0.5]}, {"Target": "Parameter", "Id": "ZZZ", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "jingjingtmd", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "jingjingdaixioa", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "<PERSON>ng<PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "jingjingshangxai", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Param10", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "baiguang<PERSON><PERSON>u", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "lizi1daxoao", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "lizi2daxiao", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "liziyi<PERSON>", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "liziqumian", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "lizizong<PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "jing<PERSON>u", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "andu", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "lanmu", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "heidi", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "bai", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON>ia<PERSON><PERSON>uo<PERSON>", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "zu<PERSON><PERSON><PERSON>ng", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "fizitmd", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 1, 0, 6, 1]}, {"Target": "Parameter", "Id": "xiongtuceng", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 1, 0, 6, 1]}, {"Target": "Parameter", "Id": "youxiabifa", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "op", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "leimu", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "leidonghua", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "youleidonghua", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "leimudonghua", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "jingyaa", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamCheek", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "wu", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "zao1", "Segments": [0, 0.1, 0, 6, 0.1]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "<PERSON>banzhenmian", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "dibantmd", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "beijingtmd", "Segments": [0, 0, 0, 6, 0]}, {"Target": "PartOpacity", "Id": "Part", "Segments": [0, 1, 0, 6, 1]}, {"Target": "PartOpacity", "Id": "Part24", "Segments": [0, 1, 0, 6, 1]}, {"Target": "PartOpacity", "Id": "Part38", "Segments": [0, 1, 0, 6, 1]}, {"Target": "PartOpacity", "Id": "Part3", "Segments": [0, 1, 0, 6, 1]}, {"Target": "PartOpacity", "Id": "toushipiaodai1_Skinning", "Segments": [0, 1, 0, 6, 1]}, {"Target": "PartOpacity", "Id": "toushipiaodai2_Skinning", "Segments": [0, 1, 0, 6, 1]}, {"Target": "PartOpacity", "Id": "Part2", "Segments": [0, 1, 0, 6, 1]}, {"Target": "PartOpacity", "Id": "Part18", "Segments": [0, 1, 0, 6, 1]}, {"Target": "PartOpacity", "Id": "toudaimao_Skinning", "Segments": [0, 1, 0, 6, 1]}, {"Target": "PartOpacity", "Id": "Part4", "Segments": [0, 1, 0, 6, 1]}, {"Target": "PartOpacity", "Id": "liuhaisi2_Skinning", "Segments": [0, 1, 0, 6, 1]}, {"Target": "PartOpacity", "Id": "Part17", "Segments": [0, 1, 0, 6, 1]}, {"Target": "PartOpacity", "Id": "Part22", "Segments": [0, 1, 0, 6, 1]}, {"Target": "PartOpacity", "Id": "Part21", "Segments": [0, 1, 0, 6, 1]}, {"Target": "PartOpacity", "Id": "cefatiao_Skinning2", "Segments": [0, 1, 0, 6, 1]}, {"Target": "PartOpacity", "Id": "Part5", "Segments": [0, 1, 0, 6, 1]}, {"Target": "PartOpacity", "Id": "Part6", "Segments": [0, 1, 0, 6, 1]}, {"Target": "PartOpacity", "Id": "Part7", "Segments": [0, 1, 0, 6, 1]}, {"Target": "PartOpacity", "Id": "Part45", "Segments": [0, 1, 0, 6, 1]}, {"Target": "PartOpacity", "Id": "Part26", "Segments": [0, 1, 0, 6, 1]}, {"Target": "PartOpacity", "Id": "Part8", "Segments": [0, 1, 0, 6, 1]}, {"Target": "PartOpacity", "Id": "Part56", "Segments": [0, 1, 0, 6, 1]}, {"Target": "PartOpacity", "Id": "Part27", "Segments": [0, 1, 0, 6, 1]}, {"Target": "PartOpacity", "Id": "Part30", "Segments": [0, 1, 0, 6, 1]}, {"Target": "PartOpacity", "Id": "Part37", "Segments": [0, 1, 0, 6, 1]}, {"Target": "PartOpacity", "Id": "Part31", "Segments": [0, 1, 0, 6, 1]}, {"Target": "PartOpacity", "Id": "Part9", "Segments": [0, 1, 0, 6, 1]}, {"Target": "PartOpacity", "Id": "Part39", "Segments": [0, 1, 0, 6, 1]}, {"Target": "PartOpacity", "Id": "houfatiao_Skinning", "Segments": [0, 1, 0, 6, 1]}, {"Target": "PartOpacity", "Id": "Part40", "Segments": [0, 1, 0, 6, 1]}, {"Target": "PartOpacity", "Id": "Part16", "Segments": [0, 1, 0, 6, 1]}, {"Target": "PartOpacity", "Id": "Part11", "Segments": [0, 1, 0, 6, 1]}, {"Target": "PartOpacity", "Id": "Part50", "Segments": [0, 1, 0, 6, 1]}, {"Target": "PartOpacity", "Id": "Part49", "Segments": [0, 1, 0, 6, 1]}, {"Target": "PartOpacity", "Id": "Part48", "Segments": [0, 1, 0, 6, 1]}, {"Target": "PartOpacity", "Id": "Part36", "Segments": [0, 1, 0, 6, 1]}, {"Target": "PartOpacity", "Id": "Part44", "Segments": [0, 1, 0, 6, 1]}, {"Target": "PartOpacity", "Id": "Part43", "Segments": [0, 1, 0, 6, 1]}, {"Target": "PartOpacity", "Id": "Part42", "Segments": [0, 1, 0, 6, 1]}, {"Target": "PartOpacity", "Id": "Part41", "Segments": [0, 1, 0, 6, 1]}, {"Target": "PartOpacity", "Id": "Part47", "Segments": [0, 1, 0, 6, 1]}, {"Target": "PartOpacity", "Id": "Part28", "Segments": [0, 1, 0, 6, 1]}, {"Target": "PartOpacity", "Id": "Part12", "Segments": [0, 1, 0, 6, 1]}, {"Target": "PartOpacity", "Id": "Part20", "Segments": [0, 1, 0, 6, 1]}, {"Target": "PartOpacity", "Id": "Part19", "Segments": [0, 1, 0, 6, 1]}, {"Target": "PartOpacity", "Id": "Part51", "Segments": [0, 1, 0, 6, 1]}, {"Target": "PartOpacity", "Id": "Part53", "Segments": [0, 1, 0, 6, 1]}, {"Target": "PartOpacity", "Id": "Part52", "Segments": [0, 1, 0, 6, 1]}, {"Target": "PartOpacity", "Id": "Part10", "Segments": [0, 1, 0, 6, 1]}, {"Target": "PartOpacity", "Id": "Part15", "Segments": [0, 1, 0, 6, 1]}, {"Target": "PartOpacity", "Id": "Part25", "Segments": [0, 1, 0, 6, 1]}, {"Target": "PartOpacity", "Id": "Part23", "Segments": [0, 1, 0, 6, 1]}, {"Target": "PartOpacity", "Id": "Part13", "Segments": [0, 1, 0, 6, 1]}, {"Target": "PartOpacity", "Id": "Part14", "Segments": [0, 1, 0, 6, 1]}, {"Target": "PartOpacity", "Id": "Part29", "Segments": [0, 0, 0, 6, 0]}], "UserData": [{"Time": 0.0, "Value": ""}, {"Time": 5.5, "Value": ""}]}