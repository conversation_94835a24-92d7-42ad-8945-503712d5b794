{"Version": 3, "Meta": {"Duration": 19.333, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 215, "TotalSegmentCount": 3334, "TotalPointCount": 3726, "UserDataCount": 2, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "wuli", "Segments": [0, 0, 0, 0.333, -1, 0, 0.6, 1, 0, 0.933, -1, 2, 4.333, -1, 0, 4.7, 1, 0, 5, -1, 0, 6.1, 1, 0, 9.733, -1, 0, 10.133, 1, 0, 10.733, -1, 2, 13, -1, 0, 13.667, 1, 0, 17, -0.851, 0, 17.833, 1, 0, 18.167, -1, 0, 18.733, 1, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "Para", "Segments": [0, 0, 0, 0.3, -1, 0, 0.533, 1, 0, 0.9, -0.99, 0, 1.1, 1, 0, 1.233, -1, 0, 1.433, 1, 0, 4.233, -1, 2, 9.833, -1, 2, 17, -1, 2, 18.3, -1, 0, 18.567, 1, 0, 18.867, -1, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "zuocefati1", "Segments": [0, -0.12, 1, 0.011, -0.119, 0.022, -0.119, 0.033, -0.119, 0, 0.2, -4.576, 0, 0.5, 5.231, 0, 0.667, 4.441, 0, 0.7, 4.451, 0, 1.2, -9.849, 0, 1.6, 9.755, 0, 2.033, -5.278, 0, 2.5, 2.115, 0, 2.933, -1.565, 0, 3.367, 0.752, 0, 3.767, -0.437, 0, 4.233, 0.506, 0, 4.3, 0.482, 0, 4.367, 0.515, 0, 4.6, -1.515, 0, 4.933, 5.201, 0, 5.333, -5.022, 0, 5.767, 2.434, 0, 6.2, -1.272, 0, 6.6, 0.617, 0, 7.033, -0.401, 0, 7.467, 0.333, 0, 7.833, 0.048, 0, 8.1, 0.187, 0, 8.533, -0.078, 0, 8.933, 0.081, 0, 9.367, -0.01, 0, 9.6, 0.025, 0, 10, -1.677, 0, 10.367, 4.129, 0, 10.833, -3.521, 0, 11.267, 1.009, 0, 11.667, -1.502, 0, 12.133, 0.511, 0, 12.467, -0.055, 0, 12.867, 0.873, 0, 13.333, -1.556, 0, 13.767, 1.877, 0, 14.2, -0.993, 0, 14.633, 0.711, 0, 15.067, -0.308, 0, 15.467, 0.281, 0, 15.9, -0.092, 0, 16.3, 0.103, 0, 16.767, -0.047, 0, 17.033, -0.002, 0, 17.367, -0.779, 0, 18.067, 2.682, 0, 18.433, -4.324, 0, 18.9, 3.852, 0, 19.333, -2.424]}, {"Target": "Parameter", "Id": "zuocefati2", "Segments": [0, -0.408, 1, 0.011, -0.409, 0.022, -0.409, 0.033, -0.409, 0, 0.133, 1.235, 0, 0.367, -9.279, 0, 0.667, 7.749, 0, 0.833, 6.321, 0, 0.967, 7.137, 0, 1.367, -14.982, 0, 1.767, 14.13, 0, 2.2, -6.955, 0, 2.633, 3.253, 0, 3.067, -2.764, 0, 3.467, 0.838, 0, 3.9, -1.136, 0, 4.333, 0.511, 0, 4.367, 0.489, 0, 4.5, 1.076, 0, 4.8, -3.751, 0, 5.1, 10.424, 0, 5.5, -8.064, 0, 5.9, 3.593, 0, 6.3, -2.656, 0, 6.733, 0.627, 0, 7.167, -1.029, 0, 7.6, 0.25, 0, 7.933, -0.097, 0, 8.3, 0.421, 0, 8.667, -0.042, 0, 9.067, 0.249, 0, 9.5, 0.09, 0, 9.733, 0.282, 0, 9.767, 0.281, 0, 9.9, 0.677, 0, 10.2, -3.734, 0, 10.567, 7.544, 0, 11, -4.453, 0, 11.367, 2.09, 0, 11.8, -3.116, 0, 12.233, -0.226, 0, 12.567, -1.135, 0, 13.067, 1.225, 0, 13.5, -2.892, 0, 13.933, 2.802, 0, 14.333, -1.61, 0, 14.767, 1.372, 0, 15.167, -0.305, 0, 15.6, 0.705, 0, 16.033, 0.099, 0, 16.433, 0.414, 0, 16.9, 0.137, 0, 17.167, 0.324, 0, 17.567, -1.452, 0, 17.9, 0.318, 0, 17.967, 0.277, 0, 18.267, 5.44, 0, 18.6, -7.431, 0, 19.033, 5.967, 0, 19.333, -1.516]}, {"Target": "Parameter", "Id": "zuocefati3", "Segments": [0, 0.623, 1, 0.089, 1.63, 0.178, 3.475, 0.267, 3.475, 0, 0.5, -8.917, 0, 0.767, 6.385, 0, 0.967, 1.538, 0, 1.133, 3.332, 0, 1.467, -11.483, 0, 1.867, 12.042, 0, 2.267, -7.747, 0, 2.667, 4.212, 0, 3.1, -2.339, 0, 3.533, 1.487, 0, 3.933, -0.976, 0, 4.333, 0.485, 0, 4.433, 0.399, 0, 4.633, 1.233, 0, 4.933, -5.263, 0, 5.233, 9.749, 0, 5.6, -8.591, 0, 5.967, 5.5, 0, 6.333, -3.076, 0, 6.733, 1.462, 0, 7.167, -0.72, 0, 7.633, 0.398, 0, 8.033, -0.3, 0, 8.4, 0.32, 0, 8.767, -0.252, 0, 9.1, 0.151, 0, 9.5, -0.076, 0, 9.6, -0.062, 0, 9.667, -0.111, 0, 10.033, 1.307, 0, 10.333, -4.443, 0, 10.667, 6.025, 0, 11.067, -4.74, 0, 11.467, 3.58, 0, 11.833, -2.248, 0, 12.267, 1.124, 0, 12.7, -0.802, 0, 13.2, 1.263, 0, 13.633, -2.164, 0, 14.033, 2.519, 0, 14.433, -2.168, 0, 14.8, 1.407, 0, 15.2, -0.778, 0, 15.633, 0.426, 0, 16.067, -0.244, 0, 16.5, 0.155, 0, 16.9, -0.083, 0, 17.333, 0.548, 0, 17.7, -0.985, 0, 17.967, -0.028, 0, 18.133, -0.839, 0, 18.4, 4.963, 0, 18.733, -7.032, 0, 19.133, 5.792, 0, 19.333, 0.032]}, {"Target": "Parameter", "Id": "zuocefati4", "Segments": [0, 0.197, 1, 0.111, 0.924, 0.222, 3.105, 0.333, 3.105, 0, 0.6, -9.602, 0, 0.9, 9.853, 0, 1.133, 0.693, 0, 1.267, 1.518, 0, 1.533, -12.248, 0, 1.933, 15.325, 0, 2.333, -11.487, 0, 2.733, 7.314, 0, 3.133, -4.042, 0, 3.567, 2.377, 0, 4, -1.617, 0, 4.4, 0.911, 0, 4.567, 0.62, 0, 4.7, 0.964, 0, 5.033, -5.973, 0, 5.333, 12.171, 0, 5.667, -12.809, 0, 6.033, 9.348, 0, 6.4, -5.866, 0, 6.8, 3.148, 0, 7.2, -1.434, 0, 7.667, 0.608, 0, 8.1, -0.458, 0, 8.5, 0.487, 0, 8.833, -0.445, 0, 9.2, 0.31, 0, 9.567, -0.172, 0, 9.667, -0.11, 0, 9.733, -0.168, 0, 10.1, 1.438, 0, 10.433, -5.28, 0, 10.767, 8.465, 0, 11.133, -7.447, 0, 11.533, 5.925, 0, 11.9, -4.078, 0, 12.3, 2.264, 0, 12.7, -1.351, 0, 13.267, 1.528, 0, 13.7, -2.848, 0, 14.133, 3.559, 0, 14.5, -3.481, 0, 14.867, 2.574, 0, 15.267, -1.535, 0, 15.667, 0.796, 0, 16.1, -0.408, 0, 16.533, 0.252, 0, 16.967, -0.145, 0, 17.433, 0.621, 0, 17.8, -1.349, 0, 18.067, 0.157, 0, 18.2, -0.398, 0, 18.5, 5.423, 0, 18.833, -9.499, 0, 19.2, 9.028, 0, 19.333, 3.972]}, {"Target": "Parameter", "Id": "zuocefati5", "Segments": [0, -0.088, 0, 0.4, 2.994, 0, 0.667, -10.458, 0, 1, 13.322, 0, 1.267, -1.446, 0, 1.4, -0.427, 0, 1.6, -11.878, 0, 2, 17.897, 0, 2.4, -15.132, 0, 2.8, 11.161, 0, 3.2, -7.032, 0, 3.6, 4.014, 0, 4.033, -2.591, 0, 4.467, 1.603, 0, 4.733, 0.503, 0, 4.767, 0.53, 0, 5.1, -6.522, 0, 5.4, 14.481, 0, 5.767, -16.859, 0, 6.133, 13.901, 0, 6.5, -9.94, 0, 6.867, 6.219, 0, 7.233, -3.262, 0, 7.633, 1.262, 0, 8.133, -0.608, 0, 8.567, 0.708, 0, 8.933, -0.721, 0, 9.267, 0.581, 0, 9.633, -0.441, 0, 10.167, 1.527, 0, 10.5, -6.296, 0, 10.833, 11.183, 0, 11.233, -10.902, 0, 11.6, 9.221, 0, 12, -6.899, 0, 12.367, 4.352, 0, 12.733, -2.575, 0, 13.3, 1.76, 0, 13.8, -3.586, 0, 14.2, 4.97, 0, 14.567, -5.264, 0, 14.933, 4.367, 0, 15.333, -2.96, 0, 15.7, 1.658, 0, 16.1, -0.775, 0, 16.567, 0.394, 0, 17, -0.245, 0, 17.5, 0.707, 0, 17.9, -1.767, 0, 18.167, 0.565, 0, 18.3, 0.185, 0, 18.567, 5.708, 0, 18.9, -12.047, 0, 19.267, 12.784, 0, 19.333, 10.938]}, {"Target": "Parameter", "Id": "fashipiaodai1", "Segments": [0, -0.15, 1, 0.011, -0.149, 0.022, -0.149, 0.033, -0.149, 0, 0.2, -5.72, 0, 0.5, 6.538, 0, 0.667, 5.552, 0, 0.7, 5.564, 0, 1.2, -12.312, 0, 1.6, 12.194, 0, 2.033, -6.597, 0, 2.5, 2.644, 0, 2.933, -1.956, 0, 3.367, 0.94, 0, 3.767, -0.546, 0, 4.233, 0.633, 0, 4.3, 0.603, 0, 4.367, 0.643, 0, 4.6, -1.894, 0, 4.933, 6.501, 0, 5.333, -6.278, 0, 5.767, 3.042, 0, 6.2, -1.59, 0, 6.6, 0.771, 0, 7.033, -0.501, 0, 7.467, 0.416, 0, 7.833, 0.06, 0, 8.1, 0.234, 0, 8.533, -0.098, 0, 8.933, 0.101, 0, 9.367, -0.013, 0, 9.6, 0.031, 0, 10, -2.096, 0, 10.367, 5.162, 0, 10.833, -4.402, 0, 11.267, 1.262, 0, 11.667, -1.877, 0, 12.133, 0.638, 0, 12.467, -0.068, 0, 12.867, 1.091, 0, 13.333, -1.944, 0, 13.767, 2.346, 0, 14.2, -1.241, 0, 14.633, 0.888, 0, 15.067, -0.385, 0, 15.467, 0.351, 0, 15.9, -0.115, 0, 16.3, 0.128, 0, 16.767, -0.059, 0, 17.033, -0.003, 0, 17.367, -0.974, 0, 18.067, 3.352, 0, 18.433, -5.406, 0, 18.9, 4.815, 0, 19.333, -3.03]}, {"Target": "Parameter", "Id": "fashipiaodai2", "Segments": [0, -0.51, 1, 0.011, -0.511, 0.022, -0.512, 0.033, -0.512, 0, 0.133, 1.543, 0, 0.367, -11.599, 0, 0.667, 9.686, 0, 0.833, 7.901, 0, 0.967, 8.921, 0, 1.367, -18.728, 0, 1.767, 17.663, 0, 2.2, -8.694, 0, 2.633, 4.066, 0, 3.067, -3.455, 0, 3.467, 1.047, 0, 3.9, -1.42, 0, 4.333, 0.639, 0, 4.367, 0.611, 0, 4.5, 1.345, 0, 4.8, -4.689, 0, 5.1, 13.031, 0, 5.5, -10.08, 0, 5.9, 4.491, 0, 6.3, -3.32, 0, 6.733, 0.783, 0, 7.167, -1.286, 0, 7.6, 0.313, 0, 7.933, -0.121, 0, 8.3, 0.527, 0, 8.667, -0.052, 0, 9.067, 0.311, 0, 9.5, 0.113, 0, 9.733, 0.352, 0, 9.767, 0.351, 0, 9.9, 0.847, 0, 10.2, -4.667, 0, 10.567, 9.43, 0, 11, -5.566, 0, 11.367, 2.613, 0, 11.8, -3.895, 0, 12.233, -0.283, 0, 12.567, -1.418, 0, 13.067, 1.531, 0, 13.5, -3.615, 0, 13.933, 3.503, 0, 14.333, -2.013, 0, 14.767, 1.715, 0, 15.167, -0.381, 0, 15.6, 0.881, 0, 16.033, 0.124, 0, 16.433, 0.518, 0, 16.9, 0.171, 0, 17.167, 0.405, 0, 17.567, -1.816, 0, 17.9, 0.397, 0, 17.967, 0.346, 0, 18.267, 6.8, 0, 18.6, -9.289, 0, 19.033, 7.459, 0, 19.333, -1.895]}, {"Target": "Parameter", "Id": "fashipiaodai3", "Segments": [0, 0.779, 1, 0.089, 2.037, 0.178, 4.344, 0.267, 4.344, 0, 0.5, -11.146, 0, 0.767, 7.981, 0, 0.967, 1.923, 0, 1.133, 4.165, 0, 1.467, -14.354, 0, 1.867, 15.053, 0, 2.267, -9.683, 0, 2.667, 5.265, 0, 3.1, -2.924, 0, 3.533, 1.859, 0, 3.933, -1.22, 0, 4.333, 0.606, 0, 4.433, 0.498, 0, 4.633, 1.541, 0, 4.933, -6.579, 0, 5.233, 12.186, 0, 5.6, -10.739, 0, 5.967, 6.875, 0, 6.333, -3.844, 0, 6.733, 1.827, 0, 7.167, -0.899, 0, 7.633, 0.497, 0, 8.033, -0.376, 0, 8.4, 0.401, 0, 8.767, -0.315, 0, 9.1, 0.189, 0, 9.5, -0.095, 0, 9.6, -0.077, 0, 9.667, -0.138, 0, 10.033, 1.634, 0, 10.333, -5.554, 0, 10.667, 7.531, 0, 11.067, -5.925, 0, 11.467, 4.474, 0, 11.833, -2.81, 0, 12.267, 1.405, 0, 12.7, -1.003, 0, 13.2, 1.579, 0, 13.633, -2.705, 0, 14.033, 3.149, 0, 14.433, -2.71, 0, 14.8, 1.758, 0, 15.2, -0.973, 0, 15.633, 0.533, 0, 16.067, -0.306, 0, 16.5, 0.194, 0, 16.9, -0.103, 0, 17.333, 0.685, 0, 17.7, -1.231, 0, 17.967, -0.034, 0, 18.133, -1.049, 0, 18.4, 6.204, 0, 18.733, -8.79, 0, 19.133, 7.24, 0, 19.333, 0.04]}, {"Target": "Parameter", "Id": "fashipiaodai4", "Segments": [0, 0.246, 1, 0.111, 1.155, 0.222, 3.881, 0.333, 3.881, 0, 0.6, -12.002, 0, 0.9, 12.317, 0, 1.133, 0.866, 0, 1.267, 1.898, 0, 1.533, -15.31, 0, 1.933, 19.156, 0, 2.333, -14.358, 0, 2.733, 9.143, 0, 3.133, -5.052, 0, 3.567, 2.971, 0, 4, -2.021, 0, 4.4, 1.138, 0, 4.567, 0.774, 0, 4.7, 1.205, 0, 5.033, -7.466, 0, 5.333, 15.213, 0, 5.667, -16.011, 0, 6.033, 11.685, 0, 6.4, -7.332, 0, 6.8, 3.935, 0, 7.2, -1.792, 0, 7.667, 0.76, 0, 8.1, -0.572, 0, 8.5, 0.609, 0, 8.833, -0.556, 0, 9.2, 0.387, 0, 9.567, -0.214, 0, 9.667, -0.137, 0, 9.733, -0.21, 0, 10.1, 1.797, 0, 10.433, -6.6, 0, 10.767, 10.582, 0, 11.133, -9.308, 0, 11.533, 7.406, 0, 11.9, -5.097, 0, 12.3, 2.83, 0, 12.7, -1.689, 0, 13.267, 1.91, 0, 13.7, -3.56, 0, 14.133, 4.449, 0, 14.5, -4.352, 0, 14.867, 3.217, 0, 15.267, -1.918, 0, 15.667, 0.995, 0, 16.1, -0.51, 0, 16.533, 0.316, 0, 16.967, -0.181, 0, 17.433, 0.776, 0, 17.8, -1.686, 0, 18.067, 0.196, 0, 18.2, -0.498, 0, 18.5, 6.778, 0, 18.833, -11.874, 0, 19.2, 11.286, 0, 19.333, 4.965]}, {"Target": "Parameter", "Id": "fashipiaodai5", "Segments": [0, -0.11, 0, 0.4, 3.743, 0, 0.667, -13.073, 0, 1, 16.652, 0, 1.267, -1.808, 0, 1.4, -0.534, 0, 1.6, -14.847, 0, 2, 22.372, 0, 2.4, -18.915, 0, 2.8, 13.951, 0, 3.2, -8.791, 0, 3.6, 5.017, 0, 4.033, -3.238, 0, 4.467, 2.004, 0, 4.733, 0.629, 0, 4.767, 0.662, 0, 5.1, -8.152, 0, 5.4, 18.101, 0, 5.767, -21.073, 0, 6.133, 17.376, 0, 6.5, -12.425, 0, 6.867, 7.773, 0, 7.233, -4.077, 0, 7.633, 1.578, 0, 8.133, -0.76, 0, 8.567, 0.885, 0, 8.933, -0.901, 0, 9.267, 0.726, 0, 9.633, -0.551, 0, 10.167, 1.909, 0, 10.5, -7.87, 0, 10.833, 13.979, 0, 11.233, -13.627, 0, 11.6, 11.526, 0, 12, -8.623, 0, 12.367, 5.44, 0, 12.733, -3.219, 0, 13.3, 2.2, 0, 13.8, -4.482, 0, 14.2, 6.213, 0, 14.567, -6.58, 0, 14.933, 5.458, 0, 15.333, -3.7, 0, 15.7, 2.073, 0, 16.1, -0.969, 0, 16.567, 0.492, 0, 17, -0.306, 0, 17.5, 0.884, 0, 17.9, -2.208, 0, 18.167, 0.706, 0, 18.3, 0.232, 0, 18.567, 7.134, 0, 18.9, -15.059, 0, 19.267, 15.979, 0, 19.333, 13.672]}, {"Target": "Parameter", "Id": "fashipiaodai6", "Segments": [0, -0.178, 1, 0.022, -0.217, 0.045, -0.242, 0.067, -0.242, 0, 0.467, 3.845, 0, 0.733, -14.041, 0, 1.067, 20.766, 0, 1.367, -4.675, 0, 1.5, -3.259, 0, 1.633, -13.06, 0, 2.033, 24.96, 0, 2.467, -22.894, 0, 2.9, 18.706, 0, 3.267, -13.539, 0, 3.667, 8.634, 0, 4.1, -5.392, 0, 4.5, 3.383, 0, 5.167, -8.449, 0, 5.467, 20.436, 0, 5.833, -25.699, 0, 6.2, 22.74, 0, 6.6, -18.1, 0, 6.967, 12.966, 0, 7.3, -8.062, 0, 7.667, 3.989, 0, 8.067, -1.404, 0, 8.633, 1.119, 0, 9, -1.364, 0, 9.367, 1.255, 0, 9.733, -0.848, 0, 10.267, 2.057, 0, 10.567, -9.059, 0, 10.9, 17.294, 0, 11.3, -18.448, 0, 11.7, 16.313, 0, 12.067, -13.439, 0, 12.433, 9.443, 0, 12.8, -6.166, 0, 13.233, 3.359, 0, 13.867, -5.402, 0, 14.267, 8.384, 0, 14.667, -9.522, 0, 15.033, 8.779, 0, 15.4, -6.655, 0, 15.767, 4.204, 0, 16.133, -2.159, 0, 16.533, 0.902, 0, 17.033, -0.441, 0, 17.567, 1.017, 0, 17.967, -2.849, 0, 18.3, 1.59, 0, 18.4, 1.2, 0, 18.667, 7.063, 0, 18.967, -17.69, 0, 19.333, 20.517]}, {"Target": "Parameter", "Id": "daimao1", "Segments": [0, -0.131, 1, 0.011, -0.13, 0.022, -0.13, 0.033, -0.13, 0, 0.2, -4.245, 0, 0.433, 4.461, 0, 0.6, 1.893, 0, 0.833, 7.082, 0, 1.2, -13.257, 0, 1.633, 11.733, 0, 2.067, -6.537, 0, 2.5, 2.787, 0, 2.933, -2.002, 0, 3.367, 1, 0, 3.767, -0.575, 0, 4.233, 0.619, 0, 4.3, 0.59, 0, 4.367, 0.609, 0, 4.6, -3.416, 0, 4.933, 9.611, 0, 5.333, -8.685, 0, 5.767, 4.38, 0, 6.2, -2.049, 0, 6.6, 1.119, 0, 7.033, -0.653, 0, 7.467, 0.534, 0, 7.867, -0.013, 0, 8.133, 0.229, 0, 8.567, -0.084, 0, 9, 0.087, 0, 9.433, -0.031, 0, 9.767, 0.018, 0, 10, -3.393, 0, 10.4, 7.214, 0, 10.833, -6.194, 0, 11.267, 2.479, 0, 11.667, -2.436, 0, 12.133, 1.037, 0, 12.5, -0.315, 0, 12.867, 1.077, 0, 13.333, -2.664, 0, 13.8, 3.348, 0, 14.2, -1.795, 0, 14.633, 1.252, 0, 15.067, -0.576, 0, 15.467, 0.48, 0, 15.9, -0.188, 0, 16.3, 0.16, 0, 16.8, -0.062, 0, 17.033, -0.047, 0, 17.367, -1.426, 0, 18.067, 5, 0, 18.433, -7.987, 0, 18.9, 7.107, 0, 19.333, -4.448]}, {"Target": "Parameter", "Id": "daimao2", "Segments": [0, -0.446, 1, 0.011, -0.447, 0.022, -0.447, 0.033, -0.447, 0, 0.133, 1.124, 0, 0.367, -8.737, 0, 0.6, 6.504, 0, 0.767, 1.43, 0, 1, 14.451, 0, 1.4, -20.954, 0, 1.767, 17.447, 0, 2.2, -8.806, 0, 2.633, 4.33, 0, 3.067, -3.511, 0, 3.5, 1.21, 0, 3.9, -1.408, 0, 4.333, 0.661, 0, 4.367, 0.643, 0, 4.5, 1.718, 0, 4.8, -8.077, 0, 5.1, 18.433, 0, 5.467, -13.725, 0, 5.9, 6.294, 0, 6.3, -4.191, 0, 6.733, 1.434, 0, 7.167, -1.42, 0, 7.6, 0.625, 0, 7.967, -0.123, 0, 8.3, 0.62, 0, 8.7, 0.041, 0, 9.1, 0.427, 0, 9.5, 0.107, 0, 9.9, 1.156, 0, 10.2, -7.535, 0, 10.567, 12.592, 0, 11, -8.246, 0, 11.4, 4.504, 0, 11.8, -4.74, 0, 12.233, 0.545, 0, 12.6, -1.642, 0, 13.1, 1.667, 0, 13.533, -5.078, 0, 13.967, 4.995, 0, 14.333, -2.959, 0, 14.767, 2.367, 0, 15.167, -0.647, 0, 15.6, 1.146, 0, 16.033, 0.053, 0, 16.433, 0.613, 0, 16.767, 0.162, 0, 17, 0.217, 0, 17.033, 0.214, 0, 17.167, 0.422, 0, 17.567, -2.768, 0, 17.9, 0.545, 0, 17.967, 0.422, 0, 18.267, 9.93, 0, 18.6, -13.326, 0, 19.033, 10.605, 0, 19.333, -2.517]}, {"Target": "Parameter", "Id": "daimao3", "Segments": [0, 0.558, 1, 0.089, 1.463, 0.178, 3.121, 0.267, 3.121, 0, 0.467, -8.14, 0, 0.733, 5.113, 0, 0.933, -3.57, 0, 1.133, 11.153, 0, 1.467, -18.073, 0, 1.867, 15.514, 0, 2.267, -9.728, 0, 2.667, 5.341, 0, 3.133, -3.084, 0, 3.533, 1.965, 0, 3.967, -1.269, 0, 4.367, 0.633, 0, 4.4, 0.61, 0, 4.633, 2.628, 0, 4.933, -9.575, 0, 5.233, 16.918, 0, 5.567, -13.911, 0, 5.967, 8.784, 0, 6.333, -5.044, 0, 6.733, 2.5, 0, 7.2, -1.231, 0, 7.633, 0.696, 0, 8.033, -0.488, 0, 8.4, 0.436, 0, 8.767, -0.304, 0, 9, 0.045, 0, 9.067, 0.014, 0, 9.267, 0.147, 0, 9.6, -0.122, 0, 10.033, 2.577, 0, 10.333, -8.049, 0, 10.667, 10.436, 0, 11.067, -8.2, 0, 11.467, 6.345, 0, 11.867, -4.064, 0, 12.267, 2.18, 0, 12.7, -1.424, 0, 13.233, 1.953, 0, 13.633, -3.736, 0, 14.067, 4.571, 0, 14.433, -3.916, 0, 14.8, 2.511, 0, 15.2, -1.386, 0, 15.633, 0.763, 0, 16.067, -0.437, 0, 16.5, 0.28, 0, 16.533, 0.269, 0, 16.567, 0.28, 0, 16.867, -0.22, 0, 17.333, 0.913, 0, 17.7, -1.782, 0, 17.933, -0.003, 0, 18.133, -1.641, 0, 18.4, 8.791, 0, 18.733, -12.196, 0, 19.133, 9.73, 0, 19.333, 0.381]}, {"Target": "Parameter", "Id": "daimao4", "Segments": [0, 0.171, 1, 0.111, 0.826, 0.222, 2.792, 0.333, 2.792, 0, 0.567, -8.635, 0, 0.833, 6.988, 0, 1.033, -2.081, 0, 1.233, 10.556, 0, 1.533, -21.389, 0, 1.933, 20.22, 0, 2.333, -14.387, 0, 2.733, 9.161, 0, 3.167, -5.23, 0, 3.6, 3.191, 0, 4, -2.125, 0, 4.433, 1.189, 0, 4.5, 1.098, 0, 4.7, 2.325, 0, 5, -10.994, 0, 5.3, 20.613, 0, 5.667, -19.784, 0, 6.033, 13.924, 0, 6.433, -9.125, 0, 6.8, 5.174, 0, 7.2, -2.432, 0, 7.667, 1.074, 0, 8.1, -0.758, 0, 8.5, 0.7, 0, 8.833, -0.555, 0, 9.167, 0.307, 0, 9.667, -0.178, 0, 10.1, 2.66, 0, 10.4, -9.389, 0, 10.767, 14.288, 0, 11.167, -12.189, 0, 11.533, 9.924, 0, 11.933, -7.073, 0, 12.333, 4.135, 0, 12.733, -2.473, 0, 13.267, 2.307, 0, 13.733, -4.847, 0, 14.133, 6.439, 0, 14.5, -6.233, 0, 14.867, 4.566, 0, 15.267, -2.749, 0, 15.667, 1.429, 0, 16.1, -0.733, 0, 16.533, 0.453, 0, 16.933, -0.358, 0, 17.4, 1.041, 0, 17.8, -2.389, 0, 18.067, 0.316, 0, 18.2, -0.904, 0, 18.467, 9.482, 0, 18.8, -16.109, 0, 19.2, 14.289, 0, 19.333, 6.334]}, {"Target": "Parameter", "Id": "Param", "Segments": [0, -0.112, 0, 0.033, -0.111, 0, 0.2, -3.988, 0, 0.467, 4.218, 0, 0.6, 3.038, 0, 0.767, 4.665, 0, 1.2, -10.253, 0, 1.6, 9.539, 0, 2.067, -5.248, 0, 2.5, 2.171, 0, 2.933, -1.583, 0, 3.367, 0.776, 0, 3.767, -0.448, 0, 4.233, 0.501, 0, 4.3, 0.477, 0, 4.367, 0.501, 0, 4.6, -2.125, 0, 4.933, 6.456, 0, 5.333, -6.01, 0, 5.767, 2.986, 0, 6.167, -1.467, 0, 6.6, 0.763, 0, 7.033, -0.466, 0, 7.467, 0.382, 0, 7.867, 0.017, 0, 8.133, 0.185, 0, 8.567, -0.071, 0, 8.967, 0.075, 0, 9.233, 0.009, 0, 9.5, 0.024, 0, 10, -2.204, 0, 10.4, 4.948, 0, 10.833, -4.247, 0, 11.267, 1.503, 0, 11.667, -1.73, 0, 12.133, 0.672, 0, 12.467, -0.148, 0, 12.867, 0.868, 0, 13.333, -1.844, 0, 13.8, 2.274, 0, 14.2, -1.215, 0, 14.633, 0.857, 0, 15.067, -0.385, 0, 15.467, 0.332, 0, 15.9, -0.121, 0, 16.3, 0.116, 0, 16.767, -0.067, 0, 17.033, -0.012, 0, 17.367, -0.956, 0, 18.067, 3.343, 0, 18.433, -5.366, 0, 18.9, 4.78, 0, 19.333, -3]}, {"Target": "Parameter", "Id": "Param2", "Segments": [0, -0.383, 0, 0.033, -0.384, 0, 0.133, 1.068, 0, 0.367, -8.142, 0, 0.633, 6.177, 0, 0.767, 3.884, 0, 1, 9.104, 0, 1.367, -15.931, 0, 1.767, 14.05, 0, 2.2, -6.996, 0, 2.633, 3.356, 0, 3.067, -2.786, 0, 3.5, 0.9, 0, 3.9, -1.131, 0, 4.333, 0.52, 0, 4.367, 0.501, 0, 4.5, 1.226, 0, 4.8, -5.124, 0, 5.1, 12.63, 0, 5.467, -9.607, 0, 5.9, 4.358, 0, 6.3, -3.026, 0, 6.733, 0.897, 0, 7.167, -1.088, 0, 7.6, 0.378, 0, 7.967, -0.099, 0, 8.3, 0.46, 0, 8.667, -0.002, 0, 9.067, 0.261, 0, 9.4, 0.047, 0, 9.7, 0.192, 0, 9.767, 0.18, 0, 9.9, 0.781, 0, 10.2, -4.889, 0, 10.567, 8.847, 0, 11, -5.553, 0, 11.4, 2.841, 0, 11.8, -3.463, 0, 12.233, 0.109, 0, 12.6, -1.225, 0, 13.067, 1.27, 0, 13.5, -3.467, 0, 13.967, 3.399, 0, 14.333, -1.992, 0, 14.767, 1.634, 0, 15.167, -0.412, 0, 15.6, 0.811, 0, 16.033, 0.071, 0, 16.433, 0.452, 0, 16.9, 0.113, 0, 17.167, 0.341, 0, 17.567, -1.848, 0, 17.9, 0.379, 0, 17.967, 0.307, 0, 18.267, 6.714, 0, 18.6, -9.098, 0, 19.033, 7.275, 0, 19.333, -1.79]}, {"Target": "Parameter", "Id": "Param3", "Segments": [0, 0.535, 1, 0.089, 1.401, 0.178, 2.989, 0.267, 2.989, 0, 0.5, -7.755, 0, 0.733, 4.931, 0, 0.933, -0.335, 0, 1.133, 6.042, 0, 1.467, -13.12, 0, 1.867, 12.254, 0, 2.267, -7.761, 0, 2.667, 4.239, 0, 3.1, -2.401, 0, 3.533, 1.529, 0, 3.967, -0.994, 0, 4.333, 0.492, 0, 4.433, 0.452, 0, 4.633, 1.673, 0, 4.933, -6.543, 0, 5.233, 11.78, 0, 5.567, -10.002, 0, 5.967, 6.356, 0, 6.333, -3.604, 0, 6.733, 1.749, 0, 7.2, -0.859, 0, 7.633, 0.482, 0, 8.033, -0.348, 0, 8.4, 0.337, 0, 8.767, -0.249, 0, 9.133, 0.145, 0, 9.233, 0.101, 0, 9.267, 0.102, 0, 9.533, -0.14, 0, 10.033, 1.639, 0, 10.333, -5.446, 0, 10.667, 7.233, 0, 11.067, -5.709, 0, 11.467, 4.368, 0, 11.867, -2.761, 0, 12.267, 1.441, 0, 12.7, -0.973, 0, 13.233, 1.408, 0, 13.633, -2.579, 0, 14.067, 3.082, 0, 14.433, -2.657, 0, 14.8, 1.712, 0, 15.2, -0.945, 0, 15.633, 0.519, 0, 16.067, -0.297, 0, 16.5, 0.19, 0, 16.9, -0.101, 0, 17.333, 0.667, 0, 17.7, -1.236, 0, 17.967, -0.004, 0, 18.133, -1.08, 0, 18.4, 6.051, 0, 18.733, -8.475, 0, 19.133, 6.872, 0, 19.333, 0.156]}, {"Target": "Parameter", "Id": "Param4", "Segments": [0, 0.167, 1, 0.111, 0.793, 0.222, 2.672, 0.333, 2.672, 0, 0.6, -8.324, 0, 0.867, 7.577, 0, 1.067, 0.598, 0, 1.233, 4.756, 0, 1.533, -14.878, 0, 1.933, 15.832, 0, 2.333, -11.507, 0, 2.733, 7.318, 0, 3.133, -4.09, 0, 3.567, 2.453, 0, 4, -1.658, 0, 4.4, 0.931, 0, 4.533, 0.768, 0, 4.7, 1.416, 0, 5, -7.425, 0, 5.3, 14.362, 0, 5.667, -14.561, 0, 6.033, 10.412, 0, 6.433, -6.652, 0, 6.8, 3.694, 0, 7.2, -1.706, 0, 7.667, 0.745, 0, 8.1, -0.537, 0, 8.5, 0.526, 0, 8.833, -0.446, 0, 9.2, 0.3, 0, 9.6, -0.222, 0, 10.1, 1.675, 0, 10.433, -6.37, 0, 10.767, 10.038, 0, 11.133, -8.707, 0, 11.533, 7.027, 0, 11.933, -4.896, 0, 12.3, 2.805, 0, 12.733, -1.662, 0, 13.267, 1.687, 0, 13.733, -3.343, 0, 14.133, 4.368, 0, 14.5, -4.252, 0, 14.867, 3.127, 0, 15.267, -1.872, 0, 15.667, 0.97, 0, 16.1, -0.497, 0, 16.533, 0.307, 0, 16.967, -0.177, 0, 17.433, 0.775, 0, 17.8, -1.687, 0, 18.067, 0.238, 0, 18.2, -0.545, 0, 18.467, 6.502, 0, 18.8, -11.282, 0, 19.2, 10.391, 0, 19.333, 4.6]}, {"Target": "Parameter", "Id": "Param5", "Segments": [0, -0.082, 0, 0.4, 2.589, 0, 0.667, -9.152, 0, 0.967, 10.641, 0, 1.2, 0.326, 0, 1.367, 3.093, 0, 1.6, -15.677, 0, 2, 18.907, 0, 2.4, -15.156, 0, 2.8, 11.086, 0, 3.2, -7.05, 0, 3.633, 4.149, 0, 4.067, -2.679, 0, 4.467, 1.655, 0, 4.667, 0.836, 0, 4.8, 0.987, 0, 5.1, -8.07, 0, 5.4, 16.697, 0, 5.733, -18.586, 0, 6.133, 15.055, 0, 6.5, -10.875, 0, 6.867, 6.979, 0, 7.233, -3.78, 0, 7.633, 1.54, 0, 8.133, -0.737, 0, 8.567, 0.775, 0, 8.933, -0.742, 0, 9.267, 0.57, 0, 9.667, -0.399, 0, 10.167, 1.75, 0, 10.5, -7.441, 0, 10.833, 13.028, 0, 11.233, -12.436, 0, 11.6, 10.475, 0, 12, -8.091, 0, 12.367, 5.204, 0, 12.767, -3.171, 0, 13.267, 2.04, 0, 13.8, -4.176, 0, 14.2, 6.007, 0, 14.6, -6.355, 0, 14.967, 5.311, 0, 15.333, -3.597, 0, 15.7, 2.014, 0, 16.1, -0.944, 0, 16.567, 0.481, 0, 17, -0.291, 0, 17.5, 0.893, 0, 17.867, -2.215, 0, 18.167, 0.765, 0, 18.3, 0.15, 0, 18.567, 6.779, 0, 18.9, -13.864, 0, 19.267, 14.277, 0, 19.333, 11.896]}, {"Target": "Parameter", "Id": "ZUOHOUFABG1", "Segments": [0, -0.067, 2, 0.033, -0.067, 0, 0.2, -1.655, 0, 0.4, 2.144, 0, 0.6, -1.174, 0, 0.867, 6.198, 0, 1.233, -8.483, 0, 1.633, 6.826, 0, 2.067, -3.841, 0, 2.533, 1.758, 0, 2.933, -1.212, 0, 3.367, 0.627, 0, 3.8, -0.357, 0, 4.233, 0.36, 0, 4.333, 0.341, 0, 4.367, 0.344, 0, 4.6, -2.956, 0, 4.967, 7.559, 0, 5.333, -6.487, 0, 5.767, 3.318, 0, 6.2, -1.44, 0, 6.6, 0.836, 0, 7.033, -0.458, 0, 7.467, 0.377, 0, 7.867, -0.043, 0, 8.2, 0.139, 0, 8.633, -0.048, 0, 9, 0.044, 0, 9.467, -0.033, 0, 9.767, -0.014, 0, 10, -2.815, 0, 10.4, 5.552, 0, 10.833, -4.751, 0, 11.267, 2.182, 0, 11.667, -1.772, 0, 12.133, 0.848, 0, 12.5, -0.338, 0, 12.9, 0.644, 0, 13.333, -2.026, 0, 13.8, 2.612, 0, 14.2, -1.405, 0, 14.633, 0.966, 0, 15.067, -0.458, 0, 15.467, 0.363, 0, 15.9, -0.156, 0, 16.333, 0.115, 0, 16.767, -0.093, 0, 17.033, -0.032, 0, 17.367, -1.128, 0, 18.067, 3.993, 0, 18.433, -6.29, 0, 18.9, 5.571, 0, 19.333, -3.464]}, {"Target": "Parameter", "Id": "ZUOHOUFABG2", "Segments": [0, -0.23, 2, 0.033, -0.23, 0, 0.133, 0.416, 0, 0.333, -3.678, 0, 0.567, 3.342, 0, 0.733, -3.233, 0, 1, 12.243, 0, 1.4, -13.675, 0, 1.8, 10.23, 0, 2.2, -5.28, 0, 2.633, 2.712, 0, 3.067, -2.113, 0, 3.5, 0.811, 0, 3.9, -0.828, 0, 4.333, 0.404, 0, 4.367, 0.4, 0, 4.5, 1.248, 0, 4.8, -6.753, 0, 5.1, 14.046, 0, 5.5, -9.921, 0, 5.9, 4.6, 0, 6.3, -2.901, 0, 6.733, 1.182, 0, 7.167, -0.894, 0, 7.6, 0.54, 0, 7.967, -0.059, 0, 8.333, 0.429, 0, 8.733, 0.075, 0, 9.133, 0.226, 0, 9.667, 0.067, 0, 9.9, 0.854, 0, 10.2, -6.184, 0, 10.567, 9.242, 0, 11, -6.405, 0, 11.4, 3.769, 0, 11.8, -3.298, 0, 12.233, 0.8, 0, 12.633, -1.139, 0, 13.133, 1.103, 0, 13.533, -3.93, 0, 13.967, 3.874, 0, 14.333, -2.324, 0, 14.767, 1.803, 0, 15.167, -0.544, 0, 15.6, 0.844, 0, 16.033, -0.009, 0, 16.433, 0.424, 0, 16.933, 0.031, 0, 17.167, 0.294, 0, 17.567, -2.242, 0, 17.9, 0.404, 0, 17.967, 0.31, 0, 18.233, 7.813, 0, 18.6, -10.098, 0, 19.033, 7.967, 0, 19.333, -1.739]}, {"Target": "Parameter", "Id": "ZUOHOUFABG3", "Segments": [0, 0.25, 1, 0.078, 0.602, 0.155, 1.187, 0.233, 1.187, 0, 0.467, -3.356, 0, 0.667, 3.42, 0, 0.9, -5.979, 0, 1.133, 10.792, 0, 1.5, -12.37, 0, 1.867, 9.29, 0, 2.267, -5.786, 0, 2.7, 3.257, 0, 3.133, -1.958, 0, 3.567, 1.223, 0, 3.967, -0.783, 0, 4.633, 2.19, 0, 4.9, -7.198, 0, 5.2, 12.249, 0, 5.567, -9.52, 0, 5.967, 5.954, 0, 6.367, -3.558, 0, 6.767, 1.794, 0, 7.2, -0.895, 0, 7.633, 0.51, 0, 8.067, -0.344, 0, 8.433, 0.277, 0, 8.8, -0.171, 0, 9.167, 0.095, 0, 9.6, -0.038, 0, 10.033, 2.059, 0, 10.333, -6.145, 0, 10.667, 7.698, 0, 11.1, -6.041, 0, 11.467, 4.71, 0, 11.867, -3.09, 0, 12.267, 1.73, 0, 12.7, -1.094, 0, 13.267, 1.417, 0, 13.667, -2.889, 0, 14.067, 3.574, 0, 14.433, -3.03, 0, 14.8, 1.931, 0, 15.233, -1.073, 0, 15.633, 0.594, 0, 16.067, -0.34, 0, 16.5, 0.218, 0, 16.933, -0.115, 0, 17.333, 0.774, 0, 17.7, -1.448, 0, 17.933, -0.008, 0, 18.133, -1.269, 0, 18.367, 6.625, 0, 18.733, -8.831, 0, 19.133, 6.892, 0, 19.333, 0.474]}, {"Target": "Parameter", "Id": "hounaofa1yaobai", "Segments": [0, -0.26, 1, 0.056, -0.264, 0.111, -0.266, 0.167, -0.266, 0, 0.367, 0.298, 0, 0.567, -0.023, 0, 0.767, 0.208, 0, 1.133, -0.243, 0, 1.467, 0.201, 0, 1.733, -0.009, 0, 1.933, 0.04, 0, 2.3, -0.04, 0, 2.533, 0, 0, 2.8, -0.017, 0, 3.067, -0.012, 0, 3.2, -0.013, 0, 4.367, 0.019, 0, 4.567, -0.049, 0, 4.867, 0.173, 0, 5.133, -0.108, 0, 5.367, 0.008, 0, 5.6, -0.029, 0, 5.933, -0.011, 2, 5.967, -0.011, 0, 6.167, -0.006, 0, 6.433, -0.015, 0, 6.733, -0.01, 0, 6.8, -0.011, 2, 6.833, -0.011, 2, 6.867, -0.011, 0, 8.033, 0.008, 0, 8.267, 0.001, 0, 8.533, 0.004, 0, 8.8, 0.003, 2, 9.1, 0.003, 2, 9.167, 0.003, 0, 9.5, 0.004, 2, 9.533, 0.004, 2, 9.733, 0.004, 0, 9.933, -0.06, 0, 10.267, 0.112, 0, 10.833, -0.016, 0, 11, -0.004, 0, 11.333, -0.034, 0, 11.533, -0.033, 0, 11.7, -0.035, 2, 11.8, -0.035, 2, 11.833, -0.035, 0, 12.833, 0.019, 0, 13.267, -0.034, 0, 13.733, 0.025, 0, 14, -0.002, 0, 14.267, 0.008, 0, 14.467, 0.005, 0, 14.767, 0.007, 2, 14.8, 0.007, 2, 14.833, 0.007, 2, 14.933, 0.007, 2, 14.967, 0.007, 2, 15, 0.007, 2, 15.167, 0.007, 2, 15.2, 0.007, 2, 15.233, 0.007, 2, 15.267, 0.007, 2, 15.3, 0.007, 2, 15.333, 0.007, 2, 15.367, 0.007, 2, 15.4, 0.007, 2, 15.433, 0.007, 2, 15.467, 0.007, 2, 15.5, 0.007, 2, 15.533, 0.007, 2, 15.567, 0.007, 2, 15.6, 0.007, 2, 15.633, 0.007, 2, 15.667, 0.007, 2, 15.7, 0.007, 2, 15.733, 0.007, 2, 15.767, 0.007, 2, 15.8, 0.007, 2, 15.833, 0.007, 2, 15.867, 0.007, 2, 15.933, 0.007, 2, 16, 0.007, 2, 16.033, 0.007, 0, 17.3, -0.021, 0, 18.033, 0.097, 0, 18.3, -0.105, 0, 18.867, 0.028, 0, 19.333, -0.007]}, {"Target": "Parameter", "Id": "hounaifa1bianxing", "Segments": [0, 0, 2, 0.033, 0, 0, 0.133, 0.259, 0, 0.3, -0.635, 0, 0.5, 0.619, 0, 0.733, -0.454, 0, 0.967, 0.421, 0, 1.233, -0.298, 0, 1.567, 0.335, 0, 1.833, -0.213, 0, 2.067, 0.118, 0, 2.4, -0.07, 0, 2.633, 0.047, 0, 2.9, -0.024, 0, 3.167, 0.011, 0, 3.4, -0.005, 0, 3.7, 0.001, 0, 3.833, -0.002, 2, 3.867, -0.002, 2, 3.9, -0.002, 2, 3.933, -0.002, 2, 3.967, -0.002, 0, 4.133, -0.001, 0, 4.367, -0.014, 0, 4.5, 0.068, 0, 4.733, -0.143, 0, 5.033, 0.273, 0, 5.233, -0.267, 0, 5.467, 0.153, 0, 5.733, -0.072, 0, 5.967, 0.029, 0, 6.2, -0.01, 0, 6.367, 0.006, 0, 6.567, -0.006, 0, 6.8, 0.003, 0, 6.967, -0.001, 2, 7, -0.001, 2, 7.033, -0.001, 2, 7.067, -0.001, 2, 7.1, -0.001, 2, 7.133, -0.001, 2, 7.167, -0.001, 0, 7.2, 0, 0, 7.233, -0.001, 0, 7.267, 0, 2, 7.3, 0, 2, 7.333, 0, 0, 7.367, -0.001, 0, 7.4, 0, 0, 7.433, -0.001, 2, 7.467, -0.001, 2, 7.5, -0.001, 2, 7.533, -0.001, 2, 7.567, -0.001, 2, 7.6, -0.001, 2, 7.633, -0.001, 2, 7.667, -0.001, 2, 7.7, -0.001, 2, 7.8, -0.001, 2, 7.833, -0.001, 2, 7.867, -0.001, 2, 7.9, -0.001, 2, 7.933, -0.001, 2, 7.967, -0.001, 0, 8.167, 0.007, 0, 8.4, -0.006, 0, 8.633, 0.003, 0, 8.9, -0.002, 0, 9.167, 0.001, 0, 9.4, 0, 2, 9.467, 0, 2, 9.5, 0, 2, 9.533, 0, 2, 9.567, 0, 2, 9.667, 0, 2, 9.733, 0, 0, 9.9, 0.059, 0, 10.1, -0.123, 0, 10.367, 0.135, 0, 10.6, -0.062, 0, 10.8, 0.031, 0, 11, -0.02, 0, 11.2, 0.024, 0, 11.433, -0.016, 0, 11.667, 0.009, 0, 11.933, -0.006, 0, 12.1, 0.001, 0, 12.233, -0.002, 2, 12.267, -0.002, 0, 12.433, -0.004, 0, 12.533, -0.003, 0, 12.667, -0.004, 0, 12.7, -0.003, 0, 12.8, -0.004, 0, 12.967, 0.023, 0, 13.1, 0.008, 0, 13.133, 0.009, 0, 13.367, -0.027, 0, 13.633, 0.006, 0, 13.733, 0.001, 0, 13.867, 0.025, 0, 14.1, -0.023, 0, 14.367, 0.012, 0, 14.6, -0.006, 0, 14.867, 0.003, 0, 15.1, -0.001, 0, 15.333, 0.001, 0, 15.367, 0, 0, 15.4, 0.001, 0, 15.567, 0, 2, 15.6, 0, 2, 15.633, 0, 2, 15.667, 0, 2, 15.7, 0, 2, 15.733, 0, 2, 15.767, 0, 2, 15.8, 0, 2, 15.833, 0, 2, 15.867, 0, 2, 15.933, 0, 2, 16, 0, 2, 16.067, 0, 2, 16.133, 0, 2, 16.2, 0, 2, 16.267, 0, 2, 16.333, 0, 2, 16.4, 0, 2, 16.467, 0, 2, 16.533, 0, 2, 16.6, 0, 2, 16.667, 0, 2, 16.733, 0, 2, 16.8, 0, 2, 16.867, 0, 2, 16.933, 0, 2, 17, 0, 0, 17.167, 0.017, 0, 17.4, -0.019, 0, 17.667, 0.007, 0, 17.967, -0.069, 0, 18.2, 0.18, 0, 18.433, -0.187, 0, 18.667, 0.093, 0, 18.9, -0.025, 0, 19.1, 0.012, 0, 19.267, 0, 0, 19.333, 0.007]}, {"Target": "Parameter", "Id": "hounaofa2yaobai", "Segments": [0, -0.978, 1, 0.056, -0.992, 0.111, -1, 0.167, -1, 2, 0.2, -1, 0, 0.367, 1, 2, 0.4, 1, 0, 0.567, -0.093, 0, 0.767, 0.831, 0, 1.133, -0.972, 0, 1.467, 0.803, 0, 1.733, -0.035, 0, 1.933, 0.161, 0, 2.3, -0.158, 0, 2.533, -0.002, 0, 2.8, -0.067, 0, 3.067, -0.049, 0, 3.2, -0.053, 0, 4.367, 0.075, 0, 4.567, -0.198, 0, 4.867, 0.692, 0, 5.133, -0.432, 0, 5.367, 0.034, 0, 5.6, -0.117, 0, 5.933, -0.045, 2, 5.967, -0.045, 0, 6.167, -0.023, 0, 6.433, -0.061, 0, 6.733, -0.042, 0, 6.8, -0.044, 0, 6.833, -0.042, 0, 6.867, -0.043, 0, 8.033, 0.033, 0, 8.267, 0.004, 0, 8.533, 0.014, 0, 8.8, 0.011, 0, 9.1, 0.013, 2, 9.167, 0.013, 0, 9.5, 0.015, 0, 9.533, 0.014, 0, 9.733, 0.016, 0, 9.933, -0.24, 0, 10.267, 0.447, 0, 10.833, -0.065, 0, 11, -0.015, 0, 11.333, -0.136, 0, 11.533, -0.131, 0, 11.7, -0.142, 0, 11.8, -0.139, 0, 11.833, -0.14, 0, 12.833, 0.075, 0, 13.267, -0.136, 0, 13.733, 0.098, 0, 14, -0.01, 0, 14.267, 0.032, 0, 14.467, 0.022, 0, 14.767, 0.029, 0, 14.8, 0.028, 0, 14.833, 0.029, 0, 14.933, 0.028, 2, 14.967, 0.028, 2, 15, 0.028, 0, 15.167, 0.029, 2, 15.2, 0.029, 0, 15.233, 0.03, 0, 15.267, 0.029, 0, 15.3, 0.03, 0, 15.333, 0.029, 0, 15.367, 0.03, 0, 15.4, 0.029, 0, 15.433, 0.03, 0, 15.467, 0.029, 2, 15.5, 0.029, 2, 15.533, 0.029, 2, 15.567, 0.029, 2, 15.6, 0.029, 2, 15.633, 0.029, 2, 15.667, 0.029, 2, 15.7, 0.029, 0, 15.733, 0.028, 0, 15.767, 0.029, 0, 15.8, 0.028, 2, 15.833, 0.028, 2, 15.867, 0.028, 2, 15.933, 0.028, 0, 16, 0.027, 2, 16.033, 0.027, 0, 17.3, -0.086, 0, 18.033, 0.387, 0, 18.3, -0.421, 0, 18.867, 0.111, 0, 19.333, -0.029]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, -0.26, 1, 0.056, -0.264, 0.111, -0.266, 0.167, -0.266, 0, 0.367, 0.298, 0, 0.567, -0.023, 0, 0.767, 0.208, 0, 1.133, -0.243, 0, 1.467, 0.201, 0, 1.733, -0.009, 0, 1.933, 0.04, 0, 2.3, -0.04, 0, 2.533, 0, 0, 2.8, -0.017, 0, 3.067, -0.012, 0, 3.2, -0.013, 0, 4.367, 0.019, 0, 4.567, -0.049, 0, 4.867, 0.173, 0, 5.133, -0.108, 0, 5.367, 0.008, 0, 5.6, -0.029, 0, 5.933, -0.011, 2, 5.967, -0.011, 0, 6.167, -0.006, 0, 6.433, -0.015, 0, 6.733, -0.01, 0, 6.8, -0.011, 2, 6.833, -0.011, 2, 6.867, -0.011, 0, 8.033, 0.008, 0, 8.267, 0.001, 0, 8.533, 0.004, 0, 8.8, 0.003, 2, 9.1, 0.003, 2, 9.167, 0.003, 0, 9.5, 0.004, 2, 9.533, 0.004, 2, 9.733, 0.004, 0, 9.933, -0.06, 0, 10.267, 0.112, 0, 10.833, -0.016, 0, 11, -0.004, 0, 11.333, -0.034, 0, 11.533, -0.033, 0, 11.7, -0.035, 2, 11.8, -0.035, 2, 11.833, -0.035, 0, 12.833, 0.019, 0, 13.267, -0.034, 0, 13.733, 0.025, 0, 14, -0.002, 0, 14.267, 0.008, 0, 14.467, 0.005, 0, 14.767, 0.007, 2, 14.8, 0.007, 2, 14.833, 0.007, 2, 14.933, 0.007, 2, 14.967, 0.007, 2, 15, 0.007, 2, 15.167, 0.007, 2, 15.2, 0.007, 2, 15.233, 0.007, 2, 15.267, 0.007, 2, 15.3, 0.007, 2, 15.333, 0.007, 2, 15.367, 0.007, 2, 15.4, 0.007, 2, 15.433, 0.007, 2, 15.467, 0.007, 2, 15.5, 0.007, 2, 15.533, 0.007, 2, 15.567, 0.007, 2, 15.6, 0.007, 2, 15.633, 0.007, 2, 15.667, 0.007, 2, 15.7, 0.007, 2, 15.733, 0.007, 2, 15.767, 0.007, 2, 15.8, 0.007, 2, 15.833, 0.007, 2, 15.867, 0.007, 2, 15.933, 0.007, 2, 16, 0.007, 2, 16.033, 0.007, 0, 17.3, -0.021, 0, 18.033, 0.097, 0, 18.3, -0.105, 0, 18.867, 0.028, 0, 19.333, -0.007]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, -0.241, 1, 0.067, -0.246, 0.133, -0.248, 0.2, -0.248, 0, 0.433, 0.238, 0, 0.633, 0.081, 0, 0.8, 0.11, 0, 1.2, -0.242, 0, 1.533, 0.199, 0, 1.833, -0.013, 0, 2.067, 0.024, 0, 2.367, -0.039, 0, 2.667, -0.002, 0, 2.967, -0.018, 0, 3.333, -0.012, 2, 3.367, -0.012, 2, 3.4, -0.012, 2, 3.433, -0.012, 0, 4.4, 0.018, 0, 4.567, -0.02, 0, 4.9, 0.129, 0, 5.2, -0.07, 0, 5.5, -0.007, 0, 5.8, -0.02, 0, 6.167, -0.007, 0, 6.5, -0.016, 0, 6.833, -0.011, 2, 6.867, -0.011, 2, 6.9, -0.011, 2, 6.933, -0.011, 0, 8.033, 0.008, 0, 8.333, 0, 0, 8.667, 0.003, 2, 8.933, 0.003, 0, 9.767, 0.004, 0, 9.967, -0.036, 0, 10.3, 0.095, 0, 10.833, -0.01, 0, 11, -0.005, 0, 11.467, -0.037, 0, 11.6, -0.036, 2, 11.667, -0.036, 2, 11.8, -0.036, 2, 11.867, -0.036, 0, 12.833, 0.019, 0, 13.267, -0.027, 0, 13.733, 0.02, 0, 14.067, -0.001, 0, 14.367, 0.008, 0, 14.667, 0.005, 0, 15.033, 0.007, 2, 15.067, 0.007, 2, 15.1, 0.007, 2, 15.133, 0.007, 2, 15.167, 0.007, 2, 15.2, 0.007, 2, 15.233, 0.007, 2, 15.267, 0.007, 2, 15.3, 0.007, 2, 15.333, 0.007, 2, 15.367, 0.007, 2, 15.4, 0.007, 2, 15.433, 0.007, 2, 15.467, 0.007, 2, 15.5, 0.007, 2, 15.533, 0.007, 2, 15.567, 0.007, 2, 15.6, 0.007, 2, 15.633, 0.007, 2, 15.667, 0.007, 2, 15.7, 0.007, 2, 15.733, 0.007, 2, 15.767, 0.007, 2, 15.8, 0.007, 2, 15.833, 0.007, 2, 15.867, 0.007, 2, 15.933, 0.007, 2, 16, 0.007, 2, 16.067, 0.007, 0, 17.333, -0.015, 0, 18.067, 0.066, 0, 18.367, -0.073, 0, 18.8, 0.028, 0, 19.333, -0.005]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.033, 0, 0, 0.167, 0.123, 0, 0.333, -0.281, 0, 0.6, 0.217, 0, 0.833, -0.081, 0, 1.067, 0.093, 0, 1.4, -0.207, 0, 1.667, 0.208, 0, 1.967, -0.115, 0, 2.267, 0.069, 0, 2.533, -0.046, 0, 2.833, 0.025, 0, 3.1, -0.013, 0, 3.433, 0.005, 0, 3.733, -0.003, 0, 4.033, 0, 0, 4.367, -0.007, 0, 4.533, 0.023, 0, 4.8, -0.065, 0, 5.067, 0.125, 0, 5.333, -0.095, 0, 5.633, 0.045, 0, 5.933, -0.021, 0, 6.3, 0.011, 0, 6.6, -0.007, 0, 6.933, 0.003, 0, 7.233, -0.002, 0, 7.467, 0, 2, 7.5, 0, 2, 7.533, 0, 0, 7.833, -0.001, 2, 7.867, -0.001, 2, 7.9, -0.001, 0, 8.2, 0.004, 0, 8.5, -0.003, 0, 8.8, 0.002, 0, 9.1, -0.001, 0, 9.4, 0, 2, 9.733, 0, 0, 9.9, 0.022, 0, 10.167, -0.055, 0, 10.467, 0.059, 0, 10.733, -0.023, 0, 11.167, 0.015, 0, 11.5, -0.01, 0, 11.8, 0.004, 0, 12.067, -0.003, 2, 12.1, -0.003, 2, 12.133, -0.003, 0, 12.333, -0.001, 0, 12.433, -0.002, 2, 12.467, -0.002, 0, 12.8, -0.003, 0, 13, 0.014, 0, 13.433, -0.016, 0, 13.9, 0.013, 0, 14.167, -0.011, 0, 14.5, 0.006, 0, 14.8, -0.003, 0, 15.1, 0.001, 0, 15.433, -0.001, 0, 15.667, 0, 2, 15.7, 0, 2, 15.733, 0, 2, 15.833, 0, 2, 15.867, 0, 2, 15.933, 0, 2, 16, 0, 2, 16.067, 0, 2, 16.133, 0, 2, 16.2, 0, 2, 16.267, 0, 2, 16.333, 0, 2, 16.4, 0, 2, 16.467, 0, 2, 16.533, 0, 2, 16.6, 0, 2, 16.667, 0, 2, 16.733, 0, 2, 16.8, 0, 2, 16.867, 0, 2, 16.933, 0, 2, 17, 0, 0, 17.2, 0.007, 0, 17.5, -0.009, 0, 17.767, 0.002, 0, 18, -0.027, 0, 18.233, 0.072, 0, 18.533, -0.071, 0, 18.833, 0.034, 0, 19.2, -0.013, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "ParamHairFront", "Segments": [0, -0.859, 1, 0.067, -0.878, 0.133, -0.888, 0.2, -0.888, 0, 0.4, 0.859, 0, 0.6, 0.082, 0, 0.8, 0.906, 0, 1.133, -1, 2, 1.2, -1, 0, 1.5, 0.834, 0, 1.833, -0.06, 0, 2.067, 0.102, 0, 2.367, -0.158, 0, 2.667, -0.009, 0, 2.967, -0.073, 0, 3.333, -0.047, 0, 3.367, -0.048, 2, 3.4, -0.048, 2, 3.433, -0.048, 0, 4.367, 0.067, 0, 4.567, -0.305, 0, 4.9, 0.923, 0, 5.2, -0.548, 0, 5.5, 0.019, 0, 5.8, -0.126, 0, 6.167, 0.006, 0, 6.467, -0.07, 0, 6.833, -0.035, 2, 6.867, -0.035, 2, 6.9, -0.035, 0, 6.933, -0.036, 0, 6.967, -0.035, 0, 7, -0.036, 0, 8.033, 0.04, 0, 8.333, 0.008, 0, 8.667, 0.019, 0, 8.967, 0.015, 2, 9, 0.015, 2, 9.033, 0.015, 0, 9.233, 0.016, 2, 9.3, 0.016, 2, 9.333, 0.016, 0, 9.467, 0.015, 2, 9.5, 0.015, 2, 9.533, 0.015, 2, 9.567, 0.015, 2, 9.667, 0.015, 2, 9.733, 0.015, 0, 9.967, -0.345, 0, 10.3, 0.61, 0, 10.833, -0.117, 0, 11.067, -0.006, 0, 11.433, -0.159, 0, 11.733, -0.14, 0, 11.867, -0.144, 0, 12.833, 0.077, 0, 13.3, -0.214, 0, 13.733, 0.152, 0, 14.067, -0.024, 0, 14.367, 0.046, 0, 14.667, 0.027, 0, 15, 0.037, 0, 15.267, 0.036, 2, 15.3, 0.036, 2, 15.333, 0.036, 2, 15.367, 0.036, 2, 15.4, 0.036, 2, 15.433, 0.036, 2, 15.467, 0.036, 2, 15.5, 0.036, 2, 15.533, 0.036, 2, 15.567, 0.036, 2, 15.6, 0.036, 2, 15.633, 0.036, 0, 16.867, 0.018, 0, 16.967, 0.023, 0, 17.333, -0.135, 0, 18.067, 0.523, 0, 18.367, -0.574, 0, 18.8, 0.223, 0, 19.333, -0.036]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0.283, 1, 0.056, 0.304, 0.111, 0.317, 0.167, 0.317, 0, 0.367, -0.452, 0, 0.567, 0.294, 0, 0.8, -0.379, 0, 1.067, 0.305, 0, 1.4, -0.348, 0, 1.667, 0.358, 0, 2, -0.312, 0, 2.3, 0.185, 0, 2.6, -0.083, 0, 2.933, 0.066, 0, 3.233, -0.006, 0, 3.533, 0.027, 0, 3.967, 0.002, 0, 4.1, 0.003, 0, 4.367, -0.02, 0, 4.533, 0.115, 0, 4.833, -0.312, 0, 5.1, 0.362, 0, 5.367, -0.297, 0, 5.667, 0.226, 0, 6, -0.091, 0, 6.333, 0.083, 0, 6.667, -0.023, 0, 7, 0.034, 0, 7.367, -0.003, 0, 7.6, 0.006, 0, 8.033, -0.011, 0, 8.233, 0, 0, 8.533, -0.011, 0, 8.833, -0.002, 0, 9.133, -0.008, 0, 9.467, -0.004, 2, 9.5, -0.004, 2, 9.533, -0.004, 0, 9.733, -0.006, 0, 9.933, 0.117, 0, 10.2, -0.212, 0, 10.5, 0.1, 0, 10.767, -0.118, 0, 11.2, 0.095, 0, 11.533, -0.011, 0, 11.867, 0.074, 0, 12.233, 0.022, 0, 12.433, 0.027, 0, 12.833, -0.015, 0, 13.2, 0.052, 0, 13.5, -0.019, 0, 13.9, 0.04, 0, 14.2, -0.055, 0, 14.533, 0.021, 0, 14.867, -0.028, 0, 15.2, -0.003, 0, 15.5, -0.016, 0, 15.867, -0.009, 0, 16.167, -0.012, 0, 16.867, -0.007, 0, 16.967, -0.013, 0, 17.267, 0.04, 0, 17.533, -0.007, 0, 17.767, 0.015, 0, 18.033, -0.164, 0, 18.267, 0.233, 0, 18.567, -0.177, 0, 18.867, 0.119, 0, 19.2, -0.096, 0, 19.333, -0.046]}, {"Target": "Parameter", "Id": "Param6", "Segments": [0, -0.004, 2, 0.033, -0.004, 0, 0.2, -0.11, 0, 0.4, 0.143, 0, 0.6, -0.078, 0, 0.867, 0.413, 0, 1.233, -0.566, 0, 1.633, 0.455, 0, 2.067, -0.256, 0, 2.533, 0.117, 0, 2.933, -0.081, 0, 3.367, 0.042, 0, 3.8, -0.024, 0, 4.233, 0.024, 0, 4.333, 0.023, 2, 4.367, 0.023, 0, 4.6, -0.197, 0, 4.967, 0.504, 0, 5.333, -0.432, 0, 5.767, 0.221, 0, 6.2, -0.096, 0, 6.6, 0.056, 0, 7.033, -0.031, 0, 7.467, 0.025, 0, 7.867, -0.003, 0, 8.2, 0.009, 0, 8.633, -0.003, 0, 9, 0.003, 0, 9.467, -0.002, 0, 9.767, -0.001, 0, 10, -0.188, 0, 10.4, 0.37, 0, 10.833, -0.317, 0, 11.267, 0.145, 0, 11.667, -0.118, 0, 12.133, 0.057, 0, 12.5, -0.023, 0, 12.9, 0.043, 0, 13.333, -0.135, 0, 13.8, 0.174, 0, 14.2, -0.094, 0, 14.633, 0.064, 0, 15.067, -0.031, 0, 15.467, 0.024, 0, 15.9, -0.01, 0, 16.333, 0.008, 0, 16.767, -0.006, 0, 17.033, -0.002, 0, 17.367, -0.075, 0, 18.067, 0.266, 0, 18.433, -0.419, 0, 18.9, 0.371, 0, 19.333, -0.231]}, {"Target": "Parameter", "Id": "Param7", "Segments": [0, -0.015, 2, 0.033, -0.015, 0, 0.133, 0.028, 0, 0.333, -0.245, 0, 0.567, 0.223, 0, 0.733, -0.216, 0, 1, 0.816, 0, 1.4, -0.912, 0, 1.8, 0.682, 0, 2.2, -0.352, 0, 2.633, 0.181, 0, 3.067, -0.141, 0, 3.5, 0.054, 0, 3.9, -0.055, 0, 4.333, 0.027, 2, 4.367, 0.027, 0, 4.5, 0.083, 0, 4.8, -0.45, 0, 5.1, 0.936, 0, 5.5, -0.661, 0, 5.9, 0.307, 0, 6.3, -0.193, 0, 6.733, 0.079, 0, 7.167, -0.06, 0, 7.6, 0.036, 0, 7.967, -0.004, 0, 8.333, 0.029, 0, 8.733, 0.005, 0, 9.133, 0.015, 0, 9.667, 0.004, 0, 9.9, 0.057, 0, 10.2, -0.412, 0, 10.567, 0.616, 0, 11, -0.427, 0, 11.4, 0.251, 0, 11.8, -0.22, 0, 12.233, 0.053, 0, 12.633, -0.076, 0, 13.133, 0.074, 0, 13.533, -0.262, 0, 13.967, 0.258, 0, 14.333, -0.155, 0, 14.767, 0.12, 0, 15.167, -0.036, 0, 15.6, 0.056, 0, 16.033, -0.001, 0, 16.433, 0.028, 0, 16.933, 0.002, 0, 17.167, 0.02, 0, 17.567, -0.149, 0, 17.9, 0.027, 0, 17.967, 0.021, 0, 18.233, 0.521, 0, 18.6, -0.673, 0, 19.033, 0.531, 0, 19.333, -0.116]}, {"Target": "Parameter", "Id": "Param8", "Segments": [0, 0.017, 1, 0.078, 0.04, 0.155, 0.079, 0.233, 0.079, 0, 0.467, -0.224, 0, 0.667, 0.228, 0, 0.9, -0.399, 0, 1.133, 0.719, 0, 1.5, -0.825, 0, 1.867, 0.619, 0, 2.267, -0.386, 0, 2.7, 0.217, 0, 3.133, -0.131, 0, 3.567, 0.082, 0, 3.967, -0.052, 0, 4.633, 0.146, 0, 4.9, -0.48, 0, 5.2, 0.817, 0, 5.567, -0.635, 0, 5.967, 0.397, 0, 6.367, -0.237, 0, 6.767, 0.12, 0, 7.2, -0.06, 0, 7.633, 0.034, 0, 8.067, -0.023, 0, 8.433, 0.018, 0, 8.8, -0.011, 0, 9.167, 0.006, 0, 9.6, -0.003, 0, 10.033, 0.137, 0, 10.333, -0.41, 0, 10.667, 0.513, 0, 11.1, -0.403, 0, 11.467, 0.314, 0, 11.867, -0.206, 0, 12.267, 0.115, 0, 12.7, -0.073, 0, 13.267, 0.094, 0, 13.667, -0.193, 0, 14.067, 0.238, 0, 14.433, -0.202, 0, 14.8, 0.129, 0, 15.233, -0.072, 0, 15.633, 0.04, 0, 16.067, -0.023, 0, 16.5, 0.015, 0, 16.933, -0.008, 0, 17.333, 0.052, 0, 17.7, -0.097, 0, 17.933, -0.001, 0, 18.133, -0.085, 0, 18.367, 0.442, 0, 18.733, -0.589, 0, 19.133, 0.459, 0, 19.333, 0.032]}, {"Target": "Parameter", "Id": "Param9", "Segments": [0, 0.005, 1, 0.1, 0.022, 0.2, 0.068, 0.3, 0.068, 0, 0.567, -0.215, 0, 0.767, 0.197, 0, 1, -0.401, 0, 1.2, 0.77, 0, 1.567, -1, 0, 1.967, 0.827, 0, 2.367, -0.58, 0, 2.767, 0.368, 0, 3.167, -0.218, 0, 3.6, 0.136, 0, 4, -0.088, 0, 4.7, 0.136, 0, 5, -0.535, 0, 5.3, 0.964, 0, 5.667, -0.868, 0, 6.067, 0.606, 0, 6.433, -0.408, 0, 6.8, 0.238, 0, 7.2, -0.118, 0, 7.667, 0.054, 0, 8.1, -0.036, 0, 8.5, 0.03, 0, 8.867, -0.022, 0, 9.233, 0.013, 0, 9.6, -0.006, 0, 10.133, 0.142, 0, 10.4, -0.476, 0, 10.733, 0.686, 0, 11.167, -0.57, 0, 11.567, 0.475, 0, 11.933, -0.344, 0, 12.333, 0.214, 0, 12.733, -0.129, 0, 13.267, 0.108, 0, 13.733, -0.246, 0, 14.133, 0.331, 0, 14.5, -0.317, 0, 14.9, 0.232, 0, 15.267, -0.141, 0, 15.667, 0.074, 0, 16.1, -0.038, 0, 16.533, 0.024, 0, 16.967, -0.014, 0, 17.433, 0.06, 0, 17.8, -0.131, 0, 18.067, 0.018, 0, 18.2, -0.046, 0, 18.467, 0.458, 0, 18.8, -0.757, 0, 19.2, 0.64, 0, 19.333, 0.283]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.767, 0, 0, 1.667, 1, 2, 4.233, 1, 2, 9.833, 1, 2, 17, 1, 2, 18.433, 1, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 1.567, 0, 1, 1.589, 0, 1.611, -0.593, 1.633, -0.6, 1, 2.5, -0.873, 3.366, -1, 4.233, -1, 2, 6, -1, 1, 6.089, -1, 6.178, -1.017, 6.267, -0.956, 1, 6.4, -0.864, 6.534, -0.393, 6.667, 0, 1, 6.856, 0.557, 7.044, 1, 7.233, 1, 2, 9.833, 1, 0, 11.3, -0.788, 2, 17, -0.788, 0, 17.733, -1, 2, 18.433, -1, 0, 18.567, 0, 2, 19.333, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 1.567, 0, 0, 1.633, -1, 1, 2.5, -1, 3.366, -0.998, 4.233, -0.99, 1, 4.911, -0.984, 5.589, -0.976, 6.267, -0.963, 1, 6.589, -0.956, 6.911, 0.5, 7.233, 0.5, 2, 9.833, 0.5, 0, 10.467, 1, 1, 10.745, 1, 11.022, 0.901, 11.3, 0.9, 1, 13.2, 0.89, 15.1, 0.883, 17, 0.874, 1, 17.156, 0.873, 17.311, 0.863, 17.467, 0.8, 1, 17.745, 0.688, 18.022, -0.5, 18.3, -0.5, 2, 18.433, -0.5, 0, 18.567, 0, 2, 19.333, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON>ia<PERSON><PERSON>uo<PERSON>", "Segments": [0, 0, 1, 0.6, 0, 1.2, 0.158, 1.8, 0.4, 1, 2.178, 0.553, 2.555, 0.6, 2.933, 0.6, 2, 4.233, 0.6, 2, 6, 0.6, 0, 6.267, 0, 2, 9.833, 0, 0, 11.3, 0.6, 1, 13.2, 0.6, 15.1, 0.393, 17, 0.122, 1, 17.778, 0.011, 18.555, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.5, 0, 1, 0.722, 0, 0.945, -0.133, 1.167, -0.4, 1, 1.278, -0.533, 1.389, -0.6, 1.5, -0.6, 0, 2, 1, 2, 4.233, 1, 2, 6, 1, 1, 6.089, 1, 6.178, -0.022, 6.267, -0.256, 1, 6.311, -0.373, 6.356, -0.348, 6.4, -0.4, 1, 6.511, -0.529, 6.622, -0.6, 6.733, -0.6, 0, 7.233, 1, 2, 9.833, 1, 2, 11.3, 1, 2, 17, 1, 2, 18.167, 1, 0, 18.433, -1, 0, 18.567, 0, 2, 19.333, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 1, 0, 1.4, -1, 0, 1.933, 1, 2, 4.233, 1, 2, 6, 1, 1, 6.089, 1, 6.178, -0.678, 6.267, -0.928, 1, 6.311, -1.053, 6.356, -1, 6.4, -1, 0, 6.733, 1, 0, 7.233, -1, 2, 9.833, -1, 2, 17, -1, 0, 19.333, 1]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 4.233, 0, 2, 6, 0, 0, 6.267, -1, 2, 9.833, -1, 2, 17, -1, 2, 18.333, -1, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "xingfengyaobai", "Segments": [0, 0, 0, 1.5, -1, 0, 2.333, 1, 0, 2.933, -1, 0, 3.4, 0.501, 0, 3.833, -0.3, 0, 4.1, 0, 2, 4.233, 0, 2, 6, 0, 1, 6.089, 0, 6.178, -0.645, 6.267, -0.77, 1, 6.422, -0.989, 6.578, -1, 6.733, -1, 0, 7.667, 1, 0, 8.2, -1, 0, 8.633, 0.501, 0, 9.033, -0.3, 0, 9.267, 0, 2, 9.833, 0, 2, 17, 0, 2, 19.333, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0.393, 1, 0.75, 1, 1.222, 0.988, 1.445, 1, 1.667, 1, 2, 4.233, 1, 2, 6, 1, 1, 6.089, 1, 6.178, 1.235, 6.267, 1.305, 1, 6.478, 1.471, 6.689, 1.5, 6.9, 1.5, 2, 9.833, 1.5, 0, 10.667, 1, 0, 11.333, 1.5, 2, 17, 1.5, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0.393, 1, 0.75, 1, 1.222, 0.988, 1.445, 1, 1.667, 1, 2, 4.233, 1, 2, 6, 1, 1, 6.089, 1, 6.178, 1.235, 6.267, 1.305, 1, 6.478, 1.471, 6.689, 1.5, 6.9, 1.5, 2, 9.833, 1.5, 2, 11.333, 1.5, 2, 17, 1.5, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 1, 0.333, 0.148, 0.667, 0.182, 1, 0.182, 0, 1.533, 0.089, 0, 2.233, 0.232, 0, 4.233, 0.031, 0, 5, 0.056, 0, 5.9, -0.349, 1, 6.256, -0.349, 6.611, -0.339, 6.967, -0.256, 1, 7.122, -0.22, 7.278, 0, 7.433, 0, 0, 8.233, -0.436, 1, 8.4, -0.436, 8.566, -0.352, 8.733, -0.225, 1, 8.822, -0.157, 8.911, -0.083, 9, -0.053, 1, 9.111, -0.016, 9.222, -0.013, 9.333, -0.013, 0, 9.833, -0.223, 1, 9.933, -0.223, 10.033, -0.055, 10.133, 0, 1, 10.344, 0.115, 10.556, 0.133, 10.767, 0.133, 0, 11.333, 0.064, 0, 17, 0.109, 0, 17.333, -0.074, 0, 18.167, 0.131, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON>biji<PERSON><PERSON>", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0.022, 0.5, 0.304, 1, 0.667, 0.586, 0.833, 1, 1, 1, 1, 1.067, 1, 1.133, 0.134, 1.2, 0, 1, 1.578, -0.76, 1.955, -1, 2.333, -1, 1, 2.966, -1, 3.6, -0.99, 4.233, -0.924, 1, 4.489, -0.898, 4.744, -0.731, 5, -0.608, 1, 5.189, -0.517, 5.378, -0.299, 5.567, -0.254, 1, 5.945, -0.164, 6.322, -0.106, 6.7, -0.047, 1, 6.944, -0.009, 7.189, 0, 7.433, 0, 0, 8.233, -0.096, 0, 9.833, -0.038, 0, 10.567, -0.202, 1, 10.756, -0.202, 10.944, -0.164, 11.133, 0, 1, 11.178, 0.039, 11.222, 1, 11.267, 1, 1, 13.178, 1, 15.089, 0.644, 17, -0.038, 1, 17.111, -0.078, 17.222, -0.095, 17.333, -0.133, 1, 17.4, -0.156, 17.466, -0.182, 17.533, -0.182, 1, 17.611, -0.182, 17.689, -0.189, 17.767, -0.164, 1, 17.9, -0.122, 18.034, 0, 18.167, 0, 2, 19.333, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.5, 0, 1, 0.567, 0, 0.633, -0.002, 0.7, -0.023, 1, 0.733, -0.033, 0.767, -0.07, 0.8, -0.14, 1, 0.833, -0.21, 0.867, -0.63, 0.9, -0.63, 0, 4.233, 0, 2, 5.267, 0, 1, 5.367, 0, 5.467, -0.384, 5.567, -0.419, 1, 5.867, -0.523, 6.167, -0.54, 6.467, -0.54, 1, 6.589, -0.54, 6.711, -0.471, 6.833, -0.452, 1, 7, -0.426, 7.166, -0.418, 7.333, -0.4, 1, 7.489, -0.383, 7.644, -0.373, 7.8, -0.373, 0, 8.533, -0.422, 0, 9.233, -0.385, 1, 9.433, -0.385, 9.633, -0.36, 9.833, -0.43, 1, 10.333, -0.606, 10.833, -1, 11.333, -1, 2, 17, -1, 1, 17.111, -1, 17.222, -0.496, 17.333, -0.408, 1, 17.478, -0.293, 17.622, -0.3, 17.767, -0.3, 0, 18.267, -0.7, 1, 18.322, -0.7, 18.378, -0.35, 18.433, -0.306, 1, 18.733, -0.071, 19.033, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 1, 2, 1.1, 1, 0, 1.167, 0, 2, 4.233, 0, 2, 5.033, 0, 0, 5.1, 1, 2, 6.267, 1, 2, 9.833, 1, 2, 17, 1, 2, 19.333, 1]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 1, 0.3, 0, 0.6, -0.005, 0.9, -0.023, 1, 0.956, -0.026, 1.011, -0.184, 1.067, -0.275, 1, 1.256, -0.585, 1.444, -0.753, 1.633, -0.753, 1, 1.944, -0.753, 2.256, 0.066, 2.567, 0.2, 1, 3.122, 0.439, 3.678, 0.623, 4.233, 0.698, 1, 4.3, 0.707, 4.366, 0.7, 4.433, 0.7, 0, 5, 0, 0, 5.567, 0.423, 1, 5.7, 0.423, 5.834, 0.003, 5.967, -0.032, 1, 6.334, -0.129, 6.7, -0.139, 7.067, -0.253, 1, 7.456, -0.374, 7.844, -0.634, 8.233, -0.634, 1, 8.422, -0.634, 8.611, -0.621, 8.8, -0.582, 1, 8.867, -0.568, 8.933, -0.551, 9, -0.551, 0, 9.233, -0.606, 0, 9.5, -0.492, 1, 9.611, -0.492, 9.722, -0.489, 9.833, -0.53, 1, 9.944, -0.571, 10.056, -0.7, 10.167, -0.7, 1, 10.3, -0.7, 10.434, -0.036, 10.567, 0.3, 1, 10.689, 0.608, 10.811, 0.6, 10.933, 0.6, 1, 11.066, 0.6, 11.2, 0.013, 11.333, 0, 1, 13.222, -0.189, 15.111, -0.344, 17, -0.53, 1, 17.111, -0.541, 17.222, -0.706, 17.333, -0.706, 1, 17.478, -0.706, 17.622, -0.463, 17.767, 0.1, 1, 17.845, 0.403, 17.922, 0.6, 18, 0.6, 0, 18.267, -0.2, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "xiongtuceng", "Segments": [0, 0, 2, 5.667, 0, 0, 5.8, 1, 2, 10.6, 1, 0, 10.633, 0, 2, 19.333, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 1.367, 0, 0, 1.4, 1, 2, 4.133, 1, 0, 4.233, 0, 2, 6.133, 0, 2, 9.833, 0, 2, 17, 0, 2, 19.333, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 1.533, 1, 0, 2.1, -1, 2, 4.233, -1, 2, 6.033, -1, 0, 6.267, 1, 0, 7.067, -1, 0, 8.233, 1, 0, 9.333, -1, 1, 9.5, -1, 9.666, 0.098, 9.833, 0.295, 1, 10.333, 0.886, 10.833, 1, 11.333, 1, 0, 17, 0.295, 0, 17.333, 1, 0, 17.533, -1, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "xiongshangxai", "Segments": [0, -0.715, 1, 0.056, -0.726, 0.111, -0.731, 0.167, -0.731, 0, 0.367, 0.769, 0, 0.533, 0.029, 0, 0.7, 0.402, 0, 1.1, -0.592, 0, 1.233, 0.162, 0, 1.367, -0.28, 0, 1.533, 0.446, 0, 1.767, -0.008, 0, 1.967, 0.088, 0, 2.3, -0.086, 0, 2.5, 0.004, 0, 2.733, -0.033, 0, 3, -0.025, 0, 3.133, -0.028, 2, 3.167, -0.028, 2, 3.2, -0.028, 0, 3.333, -0.024, 0, 3.367, -0.025, 0, 4.5, 0.083, 0, 4.733, 0.061, 2, 4.767, 0.061, 0, 5.433, -0.049, 0, 5.633, -0.012, 0, 5.9, -0.033, 0, 5.933, -0.032, 2, 5.967, -0.032, 0, 6, -0.031, 2, 6.033, -0.031, 0, 6.067, -0.03, 0, 6.1, -0.031, 2, 6.133, -0.031, 0, 6.233, -0.034, 2, 6.267, -0.034, 0, 6.3, -0.035, 0, 6.333, -0.034, 0, 6.367, -0.035, 0, 6.4, -0.034, 0, 6.433, -0.035, 0, 6.467, -0.034, 2, 6.5, -0.034, 2, 6.533, -0.034, 2, 6.567, -0.034, 0, 6.6, -0.033, 0, 6.667, -0.034, 0, 6.733, -0.032, 2, 6.8, -0.032, 0, 6.833, -0.031, 2, 6.867, -0.031, 0, 6.9, -0.03, 2, 6.933, -0.03, 0, 6.967, -0.028, 2, 7, -0.028, 0, 8.033, 0.013, 0, 8.233, -0.004, 0, 8.5, 0.002, 2, 8.667, 0.002, 0, 10.1, 0.088, 0, 10.367, 0.064, 0, 10.4, 0.065, 0, 11.667, -0.087, 0, 11.8, -0.082, 2, 11.867, -0.082, 0, 12.833, 0.045, 0, 13.033, -0.011, 0, 13.267, 0.009, 0, 13.5, 0.004, 0, 13.733, 0.007, 2, 13.767, 0.007, 2, 13.833, 0.007, 2, 13.9, 0.007, 0, 14.167, 0.008, 2, 14.2, 0.008, 2, 14.233, 0.008, 2, 14.267, 0.008, 2, 14.3, 0.008, 2, 14.333, 0.008, 2, 14.367, 0.008, 2, 14.4, 0.008, 0, 14.433, 0.009, 2, 14.467, 0.009, 2, 14.5, 0.009, 2, 14.533, 0.009, 2, 14.567, 0.009, 2, 14.6, 0.009, 2, 14.633, 0.009, 2, 14.667, 0.009, 2, 14.7, 0.009, 2, 14.733, 0.009, 0, 14.767, 0.01, 0, 14.8, 0.009, 0, 14.833, 0.01, 2, 14.867, 0.01, 2, 14.9, 0.01, 2, 14.933, 0.01, 2, 14.967, 0.01, 2, 15, 0.01, 2, 15.033, 0.01, 2, 15.067, 0.01, 2, 15.1, 0.01, 2, 15.133, 0.01, 2, 15.167, 0.01, 2, 15.2, 0.01, 0, 15.233, 0.011, 0, 15.267, 0.01, 0, 15.3, 0.011, 0, 15.333, 0.01, 0, 15.367, 0.011, 2, 15.4, 0.011, 2, 15.433, 0.011, 2, 15.467, 0.011, 2, 15.5, 0.011, 2, 15.533, 0.011, 2, 15.567, 0.011, 2, 15.6, 0.011, 2, 15.633, 0.011, 2, 15.667, 0.011, 2, 15.7, 0.011, 2, 15.733, 0.011, 2, 15.767, 0.011, 2, 15.8, 0.011, 2, 15.833, 0.011, 2, 15.867, 0.011, 2, 15.933, 0.011, 2, 16, 0.011, 2, 16.067, 0.011, 2, 16.133, 0.011, 2, 16.2, 0.011, 2, 16.267, 0.011, 2, 16.333, 0.011, 2, 16.4, 0.011, 2, 16.467, 0.011, 2, 16.533, 0.011, 2, 16.6, 0.011, 2, 16.667, 0.011, 2, 16.733, 0.011, 2, 16.8, 0.011, 2, 16.867, 0.011, 2, 16.933, 0.011, 2, 17, 0.011, 0, 17.3, 0.005, 0, 17.4, 0.009, 0, 17.7, 0.003, 2, 17.8, 0.003, 2, 17.833, 0.003, 2, 17.867, 0.003, 0, 18.5, -0.191, 0, 18.7, 0.292, 0, 18.967, -0.211, 0, 19.2, 0.038, 0, 19.333, 0.018]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, -0.358, 1, 0.056, -0.363, 0.111, -0.366, 0.167, -0.366, 0, 0.367, 0.385, 0, 0.533, 0.014, 0, 0.7, 0.201, 0, 1.1, -0.296, 0, 1.233, 0.081, 0, 1.367, -0.14, 0, 1.533, 0.223, 0, 1.767, -0.004, 0, 1.967, 0.044, 0, 2.3, -0.043, 0, 2.5, 0.002, 0, 2.733, -0.016, 0, 3, -0.013, 0, 3.133, -0.014, 2, 3.167, -0.014, 2, 3.2, -0.014, 0, 3.333, -0.012, 2, 3.367, -0.012, 0, 4.5, 0.042, 0, 4.733, 0.03, 2, 4.767, 0.03, 0, 5.433, -0.024, 0, 5.633, -0.006, 0, 5.9, -0.016, 2, 5.933, -0.016, 2, 5.967, -0.016, 0, 6, -0.015, 0, 6.033, -0.016, 0, 6.067, -0.015, 0, 6.1, -0.016, 2, 6.133, -0.016, 0, 6.233, -0.017, 2, 6.267, -0.017, 2, 6.3, -0.017, 2, 6.333, -0.017, 2, 6.367, -0.017, 2, 6.4, -0.017, 2, 6.433, -0.017, 2, 6.467, -0.017, 2, 6.5, -0.017, 2, 6.533, -0.017, 2, 6.567, -0.017, 2, 6.6, -0.017, 2, 6.667, -0.017, 0, 6.733, -0.016, 2, 6.8, -0.016, 0, 6.833, -0.015, 0, 6.867, -0.016, 0, 6.9, -0.015, 2, 6.933, -0.015, 0, 6.967, -0.014, 2, 7, -0.014, 0, 8.033, 0.007, 0, 8.233, -0.002, 0, 8.5, 0.001, 2, 8.667, 0.001, 0, 10.1, 0.044, 0, 10.367, 0.032, 0, 10.4, 0.033, 0, 11.667, -0.043, 0, 11.8, -0.041, 2, 11.867, -0.041, 0, 12.833, 0.023, 0, 13.033, -0.006, 0, 13.267, 0.004, 0, 13.5, 0.002, 0, 13.733, 0.003, 2, 13.767, 0.003, 2, 13.833, 0.003, 2, 13.9, 0.003, 0, 14.167, 0.004, 2, 14.2, 0.004, 2, 14.233, 0.004, 2, 14.267, 0.004, 2, 14.3, 0.004, 2, 14.333, 0.004, 2, 14.367, 0.004, 2, 14.4, 0.004, 2, 14.433, 0.004, 2, 14.467, 0.004, 2, 14.5, 0.004, 2, 14.533, 0.004, 0, 14.567, 0.005, 0, 14.6, 0.004, 0, 14.633, 0.005, 2, 14.667, 0.005, 2, 14.7, 0.005, 2, 14.733, 0.005, 2, 14.767, 0.005, 2, 14.8, 0.005, 2, 14.833, 0.005, 2, 14.867, 0.005, 2, 14.9, 0.005, 2, 14.933, 0.005, 2, 14.967, 0.005, 2, 15, 0.005, 2, 15.033, 0.005, 2, 15.067, 0.005, 2, 15.1, 0.005, 2, 15.133, 0.005, 2, 15.167, 0.005, 2, 15.2, 0.005, 2, 15.233, 0.005, 2, 15.267, 0.005, 2, 15.3, 0.005, 2, 15.333, 0.005, 2, 15.367, 0.005, 2, 15.4, 0.005, 2, 15.433, 0.005, 2, 15.467, 0.005, 2, 15.5, 0.005, 2, 15.533, 0.005, 2, 15.567, 0.005, 2, 15.6, 0.005, 0, 15.633, 0.006, 0, 15.667, 0.005, 0, 15.7, 0.006, 0, 15.733, 0.005, 0, 15.767, 0.006, 0, 15.8, 0.005, 0, 15.833, 0.006, 2, 15.867, 0.006, 2, 15.933, 0.006, 0, 16, 0.005, 0, 16.067, 0.006, 2, 16.133, 0.006, 2, 16.2, 0.006, 2, 16.267, 0.006, 2, 16.333, 0.006, 2, 16.4, 0.006, 2, 16.467, 0.006, 0, 16.533, 0.005, 0, 16.6, 0.006, 0, 16.667, 0.005, 0, 16.733, 0.006, 0, 16.8, 0.005, 0, 16.867, 0.006, 0, 16.933, 0.005, 0, 17, 0.006, 0, 17.3, 0.003, 0, 17.4, 0.004, 0, 17.7, 0.001, 0, 17.8, 0.002, 2, 17.833, 0.002, 2, 17.867, 0.002, 0, 18.5, -0.096, 0, 18.7, 0.146, 0, 18.967, -0.106, 0, 19.2, 0.019, 0, 19.333, 0.009]}, {"Target": "Parameter", "Id": "youxiabifa", "Segments": [0, 0, 0, 2.167, 1, 2, 4.233, 1, 2, 4.8, 1, 0, 8, 0, 2, 9.833, 0, 2, 17, 0, 2, 19.333, 0]}, {"Target": "Parameter", "Id": "op", "Segments": [0, 0, 1, 0.389, 0, 0.778, 0.142, 1.167, 0.6, 1, 1.289, 0.744, 1.411, 1, 1.533, 1, 0, 1.767, 0.682, 0, 1.933, 1, 2, 3.7, 1, 2, 4.233, 1, 2, 6.4, 1, 1, 6.6, 1, 6.8, 0.347, 7, 0.119, 1, 7.133, -0.033, 7.267, 0, 7.4, 0, 2, 7.733, 0, 0, 8.233, 0.6, 1, 8.478, 0.6, 8.722, 0.412, 8.967, 0.125, 1, 9.056, 0.02, 9.144, 0, 9.233, 0, 1, 9.344, 0, 9.456, 0.027, 9.567, 0.101, 1, 9.656, 0.16, 9.744, 0.18, 9.833, 0.304, 1, 9.944, 0.459, 10.056, 1, 10.167, 1, 0, 10.333, 0.766, 0, 10.5, 1, 2, 10.8, 1, 1, 11.044, 1, 11.289, 0.507, 11.533, 0.3, 1, 11.866, 0.018, 12.2, 0, 12.533, 0, 1, 14.022, 0, 15.511, 0.132, 17, 0.413, 1, 17.111, 0.434, 17.222, 0.447, 17.333, 0.5, 1, 17.444, 0.553, 17.556, 0.9, 17.667, 0.9, 1, 17.745, 0.9, 17.822, 0.225, 17.9, 0.2, 1, 18.378, 0.046, 18.855, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 0, 0.167, 1.2, 0, 0.667, 1, 2, 4.233, 1, 0, 4.667, 0, 0, 5.5, 1, 2, 6, 1, 2, 6.267, 1, 2, 7.9, 1, 0, 8.133, 0, 0, 8.567, 1, 2, 9.833, 1, 2, 17, 1, 2, 19.333, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 1, 0.611, 0, 1.222, 0.063, 1.833, 0.2, 1, 1.855, 0.205, 1.878, 0.252, 1.9, 0.252, 0, 2, 0.2, 0, 2.067, 0.252, 0, 2.167, 0.2, 0, 2.233, 0.252, 0, 2.333, 0.2, 0, 2.4, 0.252, 0, 2.5, 0.2, 0, 2.567, 0.252, 0, 2.667, 0.2, 0, 2.733, 0.252, 0, 2.833, 0.2, 0, 2.9, 0.252, 0, 3, 0.2, 0, 3.067, 0.252, 0, 3.167, 0.2, 0, 3.233, 0.252, 0, 3.333, 0.2, 0, 3.4, 0.252, 0, 3.5, 0.2, 0, 3.567, 0.252, 0, 3.667, 0.2, 0, 3.733, 0.252, 0, 3.833, 0.2, 2, 4.233, 0.2, 1, 4.822, 0.2, 5.411, 0.175, 6, 0.101, 1, 6.089, 0.09, 6.178, 0, 6.267, 0, 2, 6.733, 0, 0, 7.1, 0.2, 0, 9.833, 0, 2, 17, 0, 2, 17.667, 0, 0, 17.867, 1, 2, 18.667, 1, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 0, 0.167, 1.2, 0, 0.667, 1, 2, 4.233, 1, 0, 4.667, 0, 0, 5.5, 1, 2, 6, 1, 2, 6.267, 1, 2, 7.9, 1, 0, 8.133, 0, 0, 8.567, 1, 2, 9.833, 1, 2, 17, 1, 2, 19.333, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 1, 0.611, 0, 1.222, 0.063, 1.833, 0.2, 1, 1.855, 0.205, 1.878, 0.252, 1.9, 0.252, 0, 2, 0.2, 0, 2.067, 0.252, 0, 2.167, 0.2, 0, 2.233, 0.252, 0, 2.333, 0.2, 0, 2.4, 0.252, 0, 2.5, 0.2, 0, 2.567, 0.252, 0, 2.667, 0.2, 0, 2.733, 0.252, 0, 2.833, 0.2, 0, 2.9, 0.252, 0, 3, 0.2, 0, 3.067, 0.252, 0, 3.167, 0.2, 0, 3.233, 0.252, 0, 3.333, 0.2, 0, 3.4, 0.252, 0, 3.5, 0.2, 0, 3.567, 0.252, 0, 3.667, 0.2, 0, 3.733, 0.252, 0, 3.833, 0.2, 2, 4.233, 0.2, 1, 4.822, 0.2, 5.411, 0.175, 6, 0.101, 1, 6.089, 0.09, 6.178, 0, 6.267, 0, 2, 6.733, 0, 0, 7.1, 0.2, 0, 9.833, 0, 2, 17, 0, 2, 17.667, 0, 0, 17.867, 1, 2, 18.667, 1, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 0, 2.5, 0.9, 1, 3.078, 0.9, 3.655, 0.758, 4.233, 0.587, 1, 4.822, 0.413, 5.411, 0.309, 6, 0.145, 1, 6.089, 0.12, 6.178, 0, 6.267, 0, 2, 7.1, 0, 2, 9.833, 0, 2, 17, 0, 2, 19.333, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 2, 2.5, 0, 2, 4.233, 0, 2, 6, 0, 2, 6.267, 0, 2, 6.733, 0, 0, 7.1, -0.4, 0, 9.833, 0, 2, 17, 0, 2, 19.333, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.667, 0, 0, 1.033, -0.3, 0, 2.5, 1, 1, 3.078, 1, 3.655, 0.971, 4.233, 0.831, 1, 4.822, 0.688, 5.411, 0.479, 6, 0.268, 1, 6.089, 0.236, 6.178, 0, 6.267, 0, 2, 9.833, 0, 2, 17, 0, 2, 19.333, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON>kongda<PERSON><PERSON>", "Segments": [0, 0, 2, 0.167, 0, 0, 0.333, 1, 0, 0.667, -1, 0, 0.833, 0.5, 0, 0.967, -0.2, 0, 1.1, 0, 2, 2.6, 0, 0, 2.833, 1, 0, 3.333, -1, 0, 3.567, 0.5, 0, 3.767, -0.2, 0, 3.967, 0, 2, 4.233, 0, 2, 6, 0, 0, 6.267, -0.1, 0, 6.333, 0, 2, 7.9, 0, 0, 8.133, 1, 0, 8.567, -1, 0, 8.767, 0.5, 0, 8.967, -0.2, 0, 9.133, 0, 2, 9.833, 0, 2, 17, 0, 2, 19.333, 0]}, {"Target": "Parameter", "Id": "gaoguangdaxia<PERSON>", "Segments": [0, 0, 2, 0.333, 0, 0, 0.567, 1, 0, 0.733, 0, 0, 0.867, 0.9, 1, 0.911, 0.9, 0.956, 0.208, 1, 0, 1, 1.167, -0.781, 1.333, -1, 1.5, -1, 2, 4.233, -1, 0, 4.5, 0, 2, 6, 0, 0, 6.267, 0.668, 0, 6.4, 0, 0, 6.533, 0.4, 0, 6.667, 0, 2, 8.133, 0, 0, 8.433, 1, 0, 8.633, 0, 0, 8.967, 0.9, 0, 9.2, 0, 0, 9.4, 0.4, 0, 9.567, 0, 2, 9.833, 0, 2, 17, 0, 2, 19.333, 0]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, -1, 2, 4.233, -1, 2, 6, -1, 1, 6.089, -1, 6.178, -0.916, 6.267, -0.871, 1, 7.456, -0.27, 8.644, 0, 9.833, 0, 2, 17, 0, 0, 19.333, -1]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, -1, 2, 4.233, -1, 2, 6, -1, 1, 6.089, -1, 6.178, -0.916, 6.267, -0.871, 1, 7.456, -0.27, 8.644, 0, 9.833, 0, 2, 17, 0, 0, 19.333, -1]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 0, 0, 0.333, 1, 0, 0.9, 0, 0, 1.767, 1, 0, 2.733, 0, 2, 3.2, 0, 0, 5.267, 1, 0, 10.433, -1, 0, 11.233, 1, 2, 12.4, 1, 0, 12.533, -0.5, 0, 13.3, 1, 0, 15.067, 0, 0, 16.233, 1, 0, 16.433, 0, 0, 16.7, 1, 0, 17.2, 0.4, 0, 17.5, 1, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 10.2, 0, 0, 10.233, 1, 2, 11.5, 1, 0, 11.533, 0, 2, 19.333, 0]}, {"Target": "Parameter", "Id": "ParamCheek", "Segments": [0, 0, 2, 2.5, 0, 0, 3, 1, 2, 4.233, 1, 1, 4.822, 1, 5.411, 0.877, 6, 0.507, 1, 6.089, 0.451, 6.178, 0, 6.267, 0, 2, 6.733, 0, 0, 7.233, 1, 2, 9.833, 1, 2, 17, 1, 0, 18.1, 0, 2, 19.333, 0]}, {"Target": "Parameter", "Id": "zao1", "Segments": [0, 0.1, 0, 3.167, 0.3, 1, 3.522, 0.3, 3.878, 0.289, 4.233, 0.28, 1, 5.233, 0.253, 6.233, 0.243, 7.233, 0.243, 2, 9.833, 0.243, 2, 17, 0.243, 0, 18.1, 0, 0, 19.333, 0.1]}, {"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 0, 1.2, 15, 0, 4.233, 0, 0, 7.267, 90, 1, 8.134, 90, 9, 90.214, 9.867, 51, 1, 11.011, -0.783, 12.156, -90, 13.3, -90, 2, 16.233, -90, 1, 16.489, -90, 16.744, -41.48, 17, 0, 1, 17.133, 21.642, 17.267, 21, 17.4, 21, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 0, 0.367, 90, 0, 1.4, -90, 0, 3.167, 0, 0, 3.767, -90, 2, 4.233, -90, 2, 5.933, -90, 0, 7.133, 5, 1, 8.033, 5, 8.933, 4.275, 9.833, 0, 1, 10.166, -1.583, 10.5, -13.95, 10.833, -22, 1, 11.266, -32.465, 11.7, -39, 12.133, -39, 0, 12.767, 63.102, 0, 13.267, -90, 1, 14.289, -90, 15.311, -86.707, 16.333, -75, 1, 16.555, -72.455, 16.778, -29.238, 17, 0, 1, 17.1, 13.157, 17.2, 33, 17.3, 33, 0, 17.567, -51, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, -30, 2, 1.3, -30, 0, 2, -20, 1, 2.744, -20, 3.489, -20.544, 4.233, -22.098, 1, 4.589, -22.84, 4.944, -23.727, 5.3, -25, 1, 6.1, -27.864, 6.9, -30, 7.7, -30, 2, 9.833, -30, 0, 10.667, -2, 0, 12.233, -20, 1, 12.411, -20, 12.589, -20.176, 12.767, -15.919, 1, 12.956, -11.396, 13.144, 2, 13.333, 2, 1, 13.9, 2, 14.466, 1.854, 15.033, -6, 1, 15.689, -15.087, 16.344, -30, 17, -30, 0, 17.867, 1, 0, 19.333, -30]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 1, 0.078, 0.851, 0.155, 20, 0.233, 20, 0, 1, -20, 0, 1.367, 0, 0, 2.233, -20, 0, 4.233, -1, 0, 5.367, -20, 0, 7.967, 6.795, 1, 8.589, 6.795, 9.211, 6.899, 9.833, 3.961, 1, 10.244, 2.02, 10.656, -20, 11.067, -20, 0, 12.767, 20, 1, 14.178, 20, 15.589, 11.387, 17, 3.961, 1, 17.778, -0.132, 18.555, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 2, 0.3, 1, 2.744, 0.3, 3.489, 0.221, 4.233, 0.117, 1, 4.544, 0.074, 4.856, 0.069, 5.167, 0.069, 1, 5.534, 0.069, 5.9, 0.082, 6.267, 0.157, 1, 6.589, 0.223, 6.911, 0.3, 7.233, 0.3, 1, 8.1, 0.3, 8.966, 0.19, 9.833, 0.118, 1, 11.055, 0.017, 12.278, 0, 13.5, 0, 2, 17, 0, 2, 19.333, 0]}, {"Target": "Parameter", "Id": "shangshenyaobai", "Segments": [0, 0, 0, 0.133, -1, 0, 0.767, 9, 0, 1.367, 5.5, 0, 2, 9, 0, 4.233, 0, 0, 6, 10.084, 1, 6.411, 10.084, 6.822, 10.46, 7.233, 9, 1, 8.1, 5.922, 8.966, 0, 9.833, 0, 2, 11.733, 0, 0, 14.3, 15.42, 0, 17, 0, 0, 18.167, 24, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 0, 0.667, 30, 1, 0.989, 30, 1.311, -20.704, 1.633, -28.74, 1, 1.733, -31.234, 1.833, -30, 1.933, -30, 2, 2.533, -30, 0, 4.233, -16, 0, 6.433, -30, 0, 8.133, -15.112, 0, 9.867, -30, 0, 11.4, 15.897, 0, 13.8, -30, 1, 14.867, -30, 15.933, -18.263, 17, -8, 1, 17.778, -0.517, 18.555, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 0.3, -1, 2, 1.5, -1, 0, 1.667, 0.321, 0, 1.767, -0.153, 0, 1.867, -0.003, 0, 3.033, -1, 0, 5.3, 1, 0, 7.2, -1, 0, 8.833, 1, 0, 10.6, -1, 0, 12.933, 1, 0, 15.567, -1, 0, 17, -0.001, 1, 17.778, 0, 18.555, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 2, 0.5, 0, 1, 0.667, 0, 0.833, -0.011, 1, -0.28, 1, 1.167, -0.549, 1.333, -1, 1.5, -1, 0, 1.6, -0.982, 0, 1.733, -1, 2, 2.533, -1, 2, 4.233, -1, 2, 9.767, -1, 0, 9.833, -0.996, 2, 17, -0.996, 0, 18.833, 0, 2, 19.333, 0]}, {"Target": "PartOpacity", "Id": "Part61", "Segments": [0, 1, 2, 17, 1, 2, 19.33, 1]}, {"Target": "PartOpacity", "Id": "Part60", "Segments": [0, 1, 2, 17, 1, 2, 19.33, 1]}, {"Target": "PartOpacity", "Id": "Part57", "Segments": [0, 1, 2, 17, 1, 2, 19.33, 1]}, {"Target": "PartOpacity", "Id": "Part", "Segments": [0, 1, 2, 17, 1, 2, 19.33, 1]}, {"Target": "PartOpacity", "Id": "Part58", "Segments": [0, 1, 2, 17, 1, 2, 19.33, 1]}, {"Target": "PartOpacity", "Id": "Part24", "Segments": [0, 1, 2, 17, 1, 2, 19.33, 1]}, {"Target": "PartOpacity", "Id": "Part38", "Segments": [0, 1, 2, 17, 1, 2, 19.33, 1]}, {"Target": "PartOpacity", "Id": "Part3", "Segments": [0, 1, 2, 17, 1, 2, 19.33, 1]}, {"Target": "PartOpacity", "Id": "toushipiaodai1_Skinning", "Segments": [0, 1, 2, 17, 1, 2, 19.33, 1]}, {"Target": "PartOpacity", "Id": "toushipiaodai2_Skinning", "Segments": [0, 1, 2, 17, 1, 2, 19.33, 1]}, {"Target": "PartOpacity", "Id": "Part2", "Segments": [0, 1, 2, 17, 1, 2, 19.33, 1]}, {"Target": "PartOpacity", "Id": "Part18", "Segments": [0, 1, 2, 17, 1, 2, 19.33, 1]}, {"Target": "PartOpacity", "Id": "toudaimao_Skinning", "Segments": [0, 1, 2, 17, 1, 2, 19.33, 1]}, {"Target": "PartOpacity", "Id": "Part4", "Segments": [0, 1, 2, 17, 1, 2, 19.33, 1]}, {"Target": "PartOpacity", "Id": "liuhaisi2_Skinning", "Segments": [0, 1, 2, 17, 1, 2, 19.33, 1]}, {"Target": "PartOpacity", "Id": "Part17", "Segments": [0, 1, 2, 17, 1, 2, 19.33, 1]}, {"Target": "PartOpacity", "Id": "Part22", "Segments": [0, 1, 2, 17, 1, 2, 19.33, 1]}, {"Target": "PartOpacity", "Id": "Part21", "Segments": [0, 1, 2, 17, 1, 2, 19.33, 1]}, {"Target": "PartOpacity", "Id": "cefatiao_Skinning2", "Segments": [0, 1, 2, 17, 1, 2, 19.33, 1]}, {"Target": "PartOpacity", "Id": "Part5", "Segments": [0, 1, 2, 17, 1, 2, 19.33, 1]}, {"Target": "PartOpacity", "Id": "Part6", "Segments": [0, 1, 2, 17, 1, 2, 19.33, 1]}, {"Target": "PartOpacity", "Id": "Part7", "Segments": [0, 1, 2, 17, 1, 2, 19.33, 1]}, {"Target": "PartOpacity", "Id": "Part45", "Segments": [0, 1, 2, 17, 1, 2, 19.33, 1]}, {"Target": "PartOpacity", "Id": "Part26", "Segments": [0, 1, 2, 17, 1, 2, 19.33, 1]}, {"Target": "PartOpacity", "Id": "Part8", "Segments": [0, 1, 2, 17, 1, 2, 19.33, 1]}, {"Target": "PartOpacity", "Id": "Part56", "Segments": [0, 1, 2, 17, 1, 2, 19.33, 1]}, {"Target": "PartOpacity", "Id": "Part27", "Segments": [0, 1, 2, 17, 1, 2, 19.33, 1]}, {"Target": "PartOpacity", "Id": "Part30", "Segments": [0, 1, 2, 17, 1, 2, 19.33, 1]}, {"Target": "PartOpacity", "Id": "Part37", "Segments": [0, 1, 2, 17, 1, 2, 19.33, 1]}, {"Target": "PartOpacity", "Id": "Part31", "Segments": [0, 1, 2, 17, 1, 2, 19.33, 1]}, {"Target": "PartOpacity", "Id": "Part9", "Segments": [0, 1, 2, 17, 1, 2, 19.33, 1]}, {"Target": "PartOpacity", "Id": "Part39", "Segments": [0, 1, 2, 17, 1, 2, 19.33, 1]}, {"Target": "PartOpacity", "Id": "houfatiao_Skinning", "Segments": [0, 1, 2, 17, 1, 2, 19.33, 1]}, {"Target": "PartOpacity", "Id": "Part40", "Segments": [0, 1, 2, 17, 1, 2, 19.33, 1]}, {"Target": "PartOpacity", "Id": "Part16", "Segments": [0, 1, 2, 17, 1, 2, 19.33, 1]}, {"Target": "PartOpacity", "Id": "Part59", "Segments": [0, 1, 2, 17, 1, 2, 19.33, 1]}, {"Target": "PartOpacity", "Id": "Part11", "Segments": [0, 1, 2, 17, 1, 2, 19.33, 1]}, {"Target": "PartOpacity", "Id": "Part50", "Segments": [0, 1, 2, 17, 1, 2, 19.33, 1]}, {"Target": "PartOpacity", "Id": "Part49", "Segments": [0, 1, 2, 17, 1, 2, 19.33, 1]}, {"Target": "PartOpacity", "Id": "Part48", "Segments": [0, 1, 2, 17, 1, 2, 19.33, 1]}, {"Target": "PartOpacity", "Id": "Part36", "Segments": [0, 1, 2, 17, 1, 2, 19.33, 1]}, {"Target": "PartOpacity", "Id": "Part44", "Segments": [0, 1, 2, 17, 1, 2, 19.33, 1]}, {"Target": "PartOpacity", "Id": "Part43", "Segments": [0, 1, 2, 17, 1, 2, 19.33, 1]}, {"Target": "PartOpacity", "Id": "Part42", "Segments": [0, 1, 2, 17, 1, 2, 19.33, 1]}, {"Target": "PartOpacity", "Id": "Part41", "Segments": [0, 1, 2, 17, 1, 2, 19.33, 1]}, {"Target": "PartOpacity", "Id": "Part47", "Segments": [0, 1, 2, 17, 1, 2, 19.33, 1]}, {"Target": "PartOpacity", "Id": "Part28", "Segments": [0, 1, 2, 17, 1, 2, 19.33, 1]}, {"Target": "PartOpacity", "Id": "Part12", "Segments": [0, 1, 2, 17, 1, 2, 19.33, 1]}, {"Target": "PartOpacity", "Id": "Part20", "Segments": [0, 1, 2, 17, 1, 2, 19.33, 1]}, {"Target": "PartOpacity", "Id": "Part19", "Segments": [0, 1, 2, 17, 1, 2, 19.33, 1]}, {"Target": "PartOpacity", "Id": "Part51", "Segments": [0, 1, 2, 17, 1, 2, 19.33, 1]}, {"Target": "PartOpacity", "Id": "Part53", "Segments": [0, 1, 2, 17, 1, 2, 19.33, 1]}, {"Target": "PartOpacity", "Id": "Part52", "Segments": [0, 1, 2, 17, 1, 2, 19.33, 1]}, {"Target": "PartOpacity", "Id": "Part10", "Segments": [0, 1, 2, 17, 1, 2, 19.33, 1]}, {"Target": "PartOpacity", "Id": "Part15", "Segments": [0, 1, 2, 17, 1, 2, 19.33, 1]}, {"Target": "PartOpacity", "Id": "Part25", "Segments": [0, 1, 2, 17, 1, 2, 19.33, 1]}, {"Target": "PartOpacity", "Id": "Part23", "Segments": [0, 1, 2, 17, 1, 2, 19.33, 1]}, {"Target": "PartOpacity", "Id": "Part13", "Segments": [0, 1, 2, 17, 1, 2, 19.33, 1]}, {"Target": "PartOpacity", "Id": "Part14", "Segments": [0, 1, 2, 17, 1, 2, 19.33, 1]}, {"Target": "PartOpacity", "Id": "Part29", "Segments": [0, 0, 2, 17, 0, 2, 19.33, 0]}, {"Target": "PartOpacity", "Id": "Part32", "Segments": [0, 1, 2, 17, 1, 2, 19.33, 1]}, {"Target": "PartOpacity", "Id": "Part33", "Segments": [0, 1, 2, 17, 1, 2, 19.33, 1]}, {"Target": "PartOpacity", "Id": "Part34", "Segments": [0, 1, 2, 17, 1, 2, 19.33, 1]}, {"Target": "PartOpacity", "Id": "Part35", "Segments": [0, 1, 2, 17, 1, 2, 19.33, 1]}, {"Target": "PartOpacity", "Id": "Part54", "Segments": [0, 1, 2, 17, 1, 2, 19.33, 1]}, {"Target": "Model", "Id": "Opacity", "Segments": [0, 1, 0, 19.333, 1]}, {"Target": "Parameter", "Id": "ParamHairSide", "Segments": [0, 0, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "ParamHairBack", "Segments": [0, 0, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "shi<PERSON>ez<PERSON>biaozuoyou", "Segments": [0, 0, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "Param11", "Segments": [0, 0, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "tiqiehuan", "Segments": [0, 0, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "ZHITIyy", "Segments": [0, 0, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "drzbxz", "Segments": [0, 0, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "drybxz", "Segments": [0, 0, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "tisousuo", "Segments": [0, 0, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "dryoutui", "Segments": [0, 0, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "renzuoyou", "Segments": [0, 0, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "rens<PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "rendaxiao", "Segments": [0, 0, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "jingshen", "Segments": [0, 0.5, 0, 19.333, 0.5]}, {"Target": "Parameter", "Id": "ZZZ", "Segments": [0, 0, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "jingjingtmd", "Segments": [0, 0, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "jingjingdaixioa", "Segments": [0, 0, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "<PERSON>ng<PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "jingjingshangxai", "Segments": [0, 0, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "b<PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "Param10", "Segments": [0, 0, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "baiguang<PERSON><PERSON>u", "Segments": [0, 0, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "lizi1daxoao", "Segments": [0, 0, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "lizi2daxiao", "Segments": [0, 0, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "liziyi<PERSON>", "Segments": [0, 0, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "liziqumian", "Segments": [0, 0, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "lizizong<PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "j<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "jing<PERSON>u", "Segments": [0, 0, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "andu", "Segments": [0, 0, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "lanmu", "Segments": [0, 0, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "heidi", "Segments": [0, 0, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "bai", "Segments": [0, 0, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "zu<PERSON><PERSON><PERSON>ng", "Segments": [0, 0, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "xingfengtmd", "Segments": [0, 0, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "fizitmd", "Segments": [0, 0, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 1, 0, 19.333, 1]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "leimu", "Segments": [0, 0, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "leidonghua", "Segments": [0, 0, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "youleidonghua", "Segments": [0, 0, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "leimudonghua", "Segments": [0, 0, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "jingyaa", "Segments": [0, 0, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "wu", "Segments": [0, 0, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "<PERSON>banzhenmian", "Segments": [0, 0, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "dibantmd", "Segments": [0, 0, 0, 19.333, 0]}, {"Target": "Parameter", "Id": "beijingtmd", "Segments": [0, 0, 0, 19.333, 0]}, {"Target": "PartOpacity", "Id": "Part55", "Segments": [0, 1, 0, 19.333, 1]}, {"Target": "PartOpacity", "Id": "Part46", "Segments": [0, 1, 0, 19.333, 1]}], "UserData": [{"Time": 0.0, "Value": ""}, {"Time": 18.833, "Value": ""}]}