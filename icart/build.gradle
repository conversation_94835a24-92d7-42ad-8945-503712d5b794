plugins {
    id 'com.android.library'
    id 'kotlin-android'
}

apply from: "$rootProject.projectDir/common.gradle"
apply from: "$rootProject.projectDir/channel.gradle"
apply from: "$rootProject.projectDir/build_type.gradle"

android {
    namespace 'com.deeptalkie.base.icart'
}

//基础依赖
dependencies {
    implementation libs.androidx.core.ktx
    implementation libs.app.compat

    // Koin Core features
    implementation libs.koin.core
    api libs.koin.android
    // Koin Test features
    testImplementation libs.koin.test

    api libs.billing
    api libs.billing.ktx

    testImplementation libs.junit
    androidTestImplementation libs.androidx.junit
    androidTestImplementation libs.androidx.espresso.core
}