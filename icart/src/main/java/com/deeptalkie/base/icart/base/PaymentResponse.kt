package com.deeptalkie.base.icart.base

/**
 * 支付结果
 * code : 支付结果code @see [CODE_UNKNOWN]...
 * message :
 */
data class PaymentResponse(
    val code: Int,
    val message: String? = null,
    val e: ICartException? = null
)


const val CODE_ICART_UNKNOWN = 500
const val CODE_ICART_SUCCEED = 200
const val CODE_ICART_TIMEOUT = 501 //请求超时
const val CODE_ICART_INVALID_PARAM = 502 //错误参数
const val CODE_ICART_UNAVAILABLE = 503 //功能不支持
const val CODE_ICART_DISCONNECTED = 504 //断开连接
const val CODE_ICART_CANCELED = 505 //用户取消操作
const val CODE_ICART_DEVELOPER_ERROR = 506 //开发配置错误
const val CODE_ICART_ALREADY_OWNED = 507 //已经存在购买了
const val CODE_ICART_SERVICE_BUSY = 510 //服务繁忙,一般是流程正在进行,请稍后
