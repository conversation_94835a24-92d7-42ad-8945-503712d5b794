package com.deeptalkie.base.icart.google

import android.app.Activity
import android.content.Context
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.widget.Toast
import com.android.billingclient.api.*
import com.deeptalkie.base.icart.base.*
import com.deeptalkie.base.icart.base.ICartException.Companion.CODE_ALREADY_OWNED
import com.deeptalkie.base.icart.base.ICartException.Companion.CODE_CANCELED
import com.deeptalkie.base.icart.base.ICartException.Companion.CODE_DEVELOPER_ERROR
import com.deeptalkie.base.icart.base.ICartException.Companion.CODE_DISCONNECTED
import com.deeptalkie.base.icart.base.ICartException.Companion.CODE_INVALID_PARAM
import com.deeptalkie.base.icart.base.ICartException.Companion.CODE_SUCCEED
import com.deeptalkie.base.icart.base.ICartException.Companion.CODE_TIMEOUT
import com.deeptalkie.base.icart.base.ICartException.Companion.CODE_UNAVAILABLE
import com.deeptalkie.base.icart.base.ICartException.Companion.CODE_UNKNOWN
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.json.JSONObject
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicReference

/**
 * GoogleBillingClient google 支付客户端
 * 使用步骤:
 * 1.连接google service [connection]
 * 2.查询获取订单详情 [loadProduct]
 *
 */
internal class GoogleBillingClient(
    private val context: Context, onUpdate: (Purchase?, ICartException?) -> Unit
) : IPlay<ProductDetails, Purchase> {

    /**
     * 购买更新监听器
     */
    private val purchasesUpdatedListener = PurchasesUpdatedListener { billingResult, purchases ->
        if (getCodeByGoogle(billingResult.responseCode) == CODE_SUCCEED && purchases != null) {
            for (purchase in purchases) {
                //用户购买成功了
                onUpdate.invoke(purchase, null)
            }
        } else {
            //购买后出现错误
            onUpdate.invoke(null, ICartException(getCodeByGoogle(billingResult.responseCode)))
        }
    }

    /**
     * billingClient
     */
    private var billingClient =
        BillingClient.newBuilder(context).setListener(purchasesUpdatedListener)
            .enablePendingPurchases().build()

    private val needConnection = AtomicBoolean(false)
    private val callback = AtomicReference<((ConnectionState) -> Unit)?>()

    /**
     * 连接google service
     * @param state 状态
     */
    override suspend fun connection(state: ((ConnectionState) -> Unit)?) {
        this.callback.set(state)
        Log.e("GoogleBillingClient", "connection - 1，needConnection:${needConnection.get()}")
        if (needConnection.compareAndSet(false, true)) {
            billingClient.startConnection(object : BillingClientStateListener {
                override fun onBillingSetupFinished(billingResult: BillingResult) {
                    Log.e(
                        "GoogleBillingClient",
                        "connection - 2，needConnection:${needConnection.get()}"
                    )
                    if (billingResult.responseCode == BillingClient.BillingResponseCode.OK) {
                        //连接成功
                        callback.get()?.invoke(ConnectionState.CONNECTED)
                        Log.e(
                            "GoogleBillingClient",
                            "onBillingSetupFinished: -5，needConnection:${needConnection.get()}"
                        )
                    }
                }

                override fun onBillingServiceDisconnected() {
                    //关闭连接
                    callback.get()?.invoke(ConnectionState.CLOSED)
                    Log.e(
                        "GoogleBillingClient",
                        "connection - 3，needConnection:${needConnection.get()}"
                    )
                }

            })
            callback.get()?.invoke(ConnectionState.CONNECTING)
            Log.e("GoogleBillingClient", "connection:-4，needConnection:${needConnection.get()} ")
        }
    }


    /**
     * 获取产品列表
     * tip:如果发起请求这个时候出现连接已经中断时候不会返回
     */
    override suspend fun loadSkuPrepaid(skuList: List<SkuInfo>): List<com.deeptalkie.base.icart.base.SkuDetails<ProductDetails>> {
        Log.e("loadSkuPrepaid", "loadSkuPrepaid  skuid=${skuList.get(0).skuId},type=${skuList[0].type}")
        val productList = mutableListOf<QueryProductDetailsParams.Product>().apply {
            skuList.forEach {
                add(
                    QueryProductDetailsParams.Product.newBuilder().setProductId(it.skuId)
                        .setProductType(getProductType(it.type)).build()
                )
            }
        }
        val params = QueryProductDetailsParams.newBuilder().setProductList(productList)
        val productDetailsResult = withContext(Dispatchers.IO) {
            billingClient.queryProductDetails(params.build())
        }
        val itemList = mutableListOf<com.deeptalkie.base.icart.base.SkuDetails<ProductDetails>>()
        productDetailsResult.productDetailsList?.forEach {
            itemList.add(com.deeptalkie.base.icart.base.SkuDetails(it.productId, it))
        }
        "loadSkuPrepaid responseCode:${productDetailsResult.billingResult.responseCode}".log()
        "loadSkuPrepaid debugMessage:${productDetailsResult.billingResult.debugMessage}".log()
        "loadSkuPrepaid:${itemList}".log()
        mainHandler.sendEmptyMessage(productDetailsResult.billingResult.responseCode)


        return itemList
    }

    private val mainHandler = Handler(Looper.getMainLooper()) {
        if (BillingClient.BillingResponseCode.FEATURE_NOT_SUPPORTED == it.what) {
            Toast.makeText(context, "google play版本不支持,请升级!!", Toast.LENGTH_LONG).show()
        }
        false
    }

    /**
     * 获取类型
     * @param type 类型 @see [SkuInfo.type]
     */
    private fun getProductType(type: Int): String {
        return when (type) {
            SkuInfo.TYPE_GIFT -> BillingClient.ProductType.INAPP
            else -> BillingClient.ProductType.SUBS
        }
    }

    /**
     * 将google的code转换成我们内部使用的code
     * @param responseCode google请求返回的code
     */
    private fun getCodeByGoogle(responseCode: Int): Int {
        return when (responseCode) {
            BillingClient.BillingResponseCode.SERVICE_TIMEOUT -> CODE_TIMEOUT
            BillingClient.BillingResponseCode.FEATURE_NOT_SUPPORTED,
            BillingClient.BillingResponseCode.SERVICE_UNAVAILABLE,
            BillingClient.BillingResponseCode.BILLING_UNAVAILABLE,
            BillingClient.BillingResponseCode.ITEM_UNAVAILABLE,
                -> CODE_UNAVAILABLE

            BillingClient.BillingResponseCode.SERVICE_DISCONNECTED -> CODE_DISCONNECTED
            BillingClient.BillingResponseCode.OK -> CODE_SUCCEED
            BillingClient.BillingResponseCode.USER_CANCELED -> CODE_CANCELED
            BillingClient.BillingResponseCode.DEVELOPER_ERROR -> CODE_DEVELOPER_ERROR
            BillingClient.BillingResponseCode.ITEM_ALREADY_OWNED -> CODE_ALREADY_OWNED
            else -> CODE_UNKNOWN
        }
    }


    /**
     * 获取优惠代码
     */
    override suspend fun getTagsBySkuDetails(details: com.deeptalkie.base.icart.base.SkuDetails<ProductDetails>): List<ProductDetails.SubscriptionOfferDetails> {
        return details.details.subscriptionOfferDetails ?: mutableListOf()
    }

    /**
     * 发起购买
     * @param activity 上下文
     * @param skuDetails 确认购买的商品详情
     * @param tag 优惠内容 @see [ProductDetails.subscriptionOfferDetails()]
     * @return code 支付流程发起是否正确
     */
    override suspend fun requestPayment(
        activity: Activity,
        skuDetails: com.deeptalkie.base.icart.base.SkuDetails<ProductDetails>,
        tag: String?,
        orderNo: String
    ): Int {
        Log.e("GoogleBillingClient", "orderNo:$orderNo")
        val orderJson = "{\"order_no\":\"$orderNo\"}"
        val flowParamsBuilder = BillingFlowParams.ProductDetailsParams.newBuilder()
            .setProductDetails(skuDetails.details)
        tag?.let { flowParamsBuilder.setOfferToken(it) }
        val productDetailsParamsList = listOf(flowParamsBuilder.build())
        val billingFlowParams =
            BillingFlowParams.newBuilder()
                .setObfuscatedAccountId(orderJson)
                .setObfuscatedAccountId(orderJson)
                .setProductDetailsParamsList(productDetailsParamsList)
                .build()
        val billingResult = billingClient.launchBillingFlow(activity, billingFlowParams)
        return getCodeByGoogle(billingResult.responseCode)
    }

    /**
     * 确认并且消耗订单，让订单可以重复购买
     */
    override suspend fun expendInvoice(invoice: Purchase?): Int {
        invoice ?: return CODE_INVALID_PARAM
        if (invoice.purchaseState == Purchase.PurchaseState.PURCHASED) {
            val jsonObject = JSONObject(invoice.originalJson)
            return if (!invoice.isAcknowledged) {//未确认的订单才会进行确认,其他已经确认的全部回复成功
                val responseCode = if (jsonObject.has("autoRenewing")) {
                    //订阅订单
                    val acknowledgePurchaseParams = AcknowledgePurchaseParams.newBuilder()
                        .setPurchaseToken(invoice.purchaseToken)
                    val ackPurchaseResult = withContext(Dispatchers.IO) {
                        billingClient.acknowledgePurchase(acknowledgePurchaseParams.build())
                    }
                    ackPurchaseResult.responseCode
                } else {
                    //消耗品订单
                    val consumeResult = billingClient.consumePurchase(
                        ConsumeParams.newBuilder().setPurchaseToken(invoice.purchaseToken).build()
                    )
                    consumeResult.billingResult.responseCode
                }
                getCodeByGoogle(responseCode)
            } else CODE_SUCCEED
        }
        return CODE_INVALID_PARAM
    }

    /**
     * 查询已经购买的订单
     * @param type 订单类型 @see [SkuInfo.type]
     * @param expend 是否已经过期|消耗   sub:过期  gift:已经消耗
     */
    override suspend fun requestInvoice(type: Int, expend: Boolean): List<JSONObject> {
        val invoiceList = mutableListOf<JSONObject>()
        if (expend) {
            val purchasesResult = billingClient.queryPurchaseHistory(
                QueryPurchaseHistoryParams.newBuilder().setProductType(getProductType(type)).build()
            )
            "requestInvoice :${purchasesResult.purchaseHistoryRecordList}".log()
            purchasesResult.purchaseHistoryRecordList?.forEach {
                invoiceList.add(JSONObject(it.originalJson))
            }
        } else {
            val purchasesResult = billingClient.queryPurchasesAsync(
                QueryPurchasesParams.newBuilder().setProductType(getProductType(type)).build()
            )
            "requestInvoice :${purchasesResult.purchasesList}".log()
            purchasesResult.purchasesList.forEach {
                invoiceList.add(JSONObject(it.originalJson))
            }
        }
        "requestInvoice responseCode:${invoiceList}".log()
        return invoiceList
    }


}
