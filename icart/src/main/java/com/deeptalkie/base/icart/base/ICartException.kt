package com.deeptalkie.base.icart.base

//实体类聚合

data class ICartException(val code: Int, override val message: String = "") : Exception() {
    companion object {
        const val CODE_UNKNOWN = 500
        const val CODE_SUCCEED = 200
        const val CODE_TIMEOUT = 501 //请求超时
        const val CODE_INVALID_PARAM = 502 //错误参数
        const val CODE_UNAVAILABLE = 503 //功能不支持
        const val CODE_DISCONNECTED = 504 //断开连接
        const val CODE_CANCELED = 505 //用户取消操作
        const val CODE_DEVELOPER_ERROR = 506 //开发配置错误
        const val CODE_ALREADY_OWNED = 507 //已经存在购买了
        const val CODE_SERVICE_BUSY = 510 //服务繁忙,一般是流程正在进行,请稍后
    }
}






