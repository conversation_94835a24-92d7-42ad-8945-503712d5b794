package com.deeptalkie.base.icart.base

import android.app.Activity
import org.json.JSONObject
import java.security.InvalidKeyException

/**
 * 第三方支付接口
 * 使用步骤:
 * 1.连接支付系统 [connection]
 * 2.获取支付凭据 [loadSkuPrepaid]
 * 3.发起支付 [requestPayment]
 * 4.消耗商品 [expendInvoice]
 * Prepaid : 预付单据,类似付款菜单
 * Invoice : 单据,购买成功后的单据
 */
interface IPlay<Prepaid, Invoice> {

    /**
     * 连接支付系统
     * @param state 连接状态回调 @see [ConnectionState]
     */
    suspend fun connection(state: ((ConnectionState) -> Unit)? = null)


    /**
     * 获取支付枚举凭证
     * @param skuList 需要获取的sku列表
     * @return  返回 支付凭据  Prepaid : 各个支付系统的凭据
     */
    suspend fun loadSkuPrepaid(skuList: List<SkuInfo>): List<SkuDetails<Prepaid>>

    /**
     * 从购买枚举中获取标识
     * @param details 支付枚举凭证 @see [loadSkuPrepaid]
     * @return
     */
    suspend fun getTagsBySkuDetails(details: SkuDetails<Prepaid>): List<Any>


    /**
     * 发起支付请求
     * @param activity 支付界面
     * @param skuDetails 选中的支付凭据,内容 @see [loadSkuPrepaid]
     * @param tag 附加参数(优惠参数)
     */
    suspend fun requestPayment(
        activity: Activity, skuDetails: SkuDetails<Prepaid>, tag: String?,orderNo:String
    ): Int

    /**
     * 消费商品
     */
    suspend fun expendInvoice(invoice: Invoice?): Int

    /**
     * 获取当前账号的历史购买凭据
     *  @param type 类型 @see [SkuInfo.TYPE_SUB] [SkuInfo.TYPE_GIFT]
     *  @param expend 是否已经过期
     */
    suspend fun requestInvoice(type: Int, expend: Boolean): List<JSONObject>


}
