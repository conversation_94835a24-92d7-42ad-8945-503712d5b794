//package com.deeptalkie.base.icart
//
//import android.app.Activity
//import android.content.Context
//import com.android.billingclient.api.ProductDetails
//import com.android.billingclient.api.Purchase
//import com.deeptalkie.base.icart.base.*
//import com.deeptalkie.base.icart.base.ICartException.Companion.CODE_SUCCEED
//import com.deeptalkie.base.icart.google.GoogleBillingClient
//import kotlinx.coroutines.*
//import kotlinx.coroutines.Dispatchers.IO
//import org.json.JSONObject
//import java.util.concurrent.atomic.AtomicInteger
//import kotlin.coroutines.CoroutineContext
//import kotlin.coroutines.resume
//
//@Deprecated("test")
//class ICartClient2(
//    ctx: Context, coroutineContext: CoroutineContext = SupervisorJob() + Dispatchers.Main
//) {
//    companion object {
//        private const val STATE_IDLE = 0//闲置
//        private const val STATE_IN_PAYMENT = 2//正在支付
//    }
//
//    /**
//     * 支付流程发起状态,防止抖动
//     */
//    private var paymentFlowState = AtomicInteger(STATE_IDLE)
//    private val scope by lazy { CoroutineScope(coroutineContext) }
//
//    //支付断言流
//    private var paymentContinuation: CancellableContinuation<PaymentResponse>? = null
//
//
//    /**
//     * 代理执行的客户端
//     */
//    private val proxy: com.deeptalkie.base.icart.base.IPlay<*, *> by lazy {
//        GoogleBillingClient(ctx) { purchase, e ->
//            updateState(newState = STATE_IDLE)
//            if (paymentContinuation?.isActive == true) {
//                if (purchase != null) {
//                    paymentContinuation?.resume(PaymentResponse(CODE_SUCCEED, purchase.originalJson))
//                } else if (e != null) {
//                    paymentContinuation?.resume(PaymentResponse(e.code, purchase?.originalJson, e))
//                }
//            }
//
//        }
//    }
//
//    //当前连接状态
//    @Volatile
//    var connectionState: ConnectionState = ConnectionState.CLOSED
//        private set
//
//    private fun updateState(oldState: Int = this.paymentFlowState.get(), newState: Int): Boolean {
//        return this.paymentFlowState.compareAndSet(oldState, newState)
//    }
//
//    /**
//     * 应用商店连接
//     * @param state 状态回调
//     */
//    suspend fun connection(state: ((ConnectionState) -> Unit)? = null) {
//        if (connectionState != ConnectionState.CLOSED) {
//            //如果正在连接状态,直接返回
//            state?.invoke(connectionState)
//            return
//        }
//        proxy.connection {
//            connectionState = it
//            when (it) {
//                ConnectionState.CONNECTING -> {
//                    state?.invoke(ConnectionState.CONNECTING)
//                }
//                ConnectionState.CONNECTED -> {
//                    state?.invoke(ConnectionState.CONNECTED)
//                }
//                ConnectionState.CLOSED -> {
//                    state?.invoke(ConnectionState.CLOSED)
//                }
//            }
//        }
//    }
//
//    /**
//     * 检测当前连接状态并且连接,知道连接才会返回
//     * @param timeout 超时时间 ,超过这个时间 ,就会返回false
//     * @return true 当连接成功
//     */
//    suspend fun checkConnectionState(timeout: Long = 5000L): Boolean {
//        return suspendCancellableCoroutine { cancellableContinuation ->
//            scope.launch(IO) {
//                connection {
//                    if (it == ConnectionState.CONNECTED && cancellableContinuation.isActive) {
//                        cancellableContinuation.resume(true)
//                    }
//                }
//                cancellableContinuation.countdown(false, timeout)
//            }
//        }
//    }
//
//
//    /**
//     * 断言倒计时
//     * @param result 倒计时结束进行的操作
//     * @param timeout 超时时间  ms
//     */
//    private suspend fun <T> CancellableContinuation<T>.countdown(result: T, timeout: Long = 5000L) {
//        var time: Long = 0
//        while (isActive && time < timeout) {
//            time += 1000
//            delay(1000)
//        }
//        if (isActive) {
//            resume(result)
//        }
//    }
//
//
//    /**
//     * 查询已有订单列表
//     * @param skuType 订单类型 @see [SkuInfo.type]
//     * @param expend 是否过期或是消耗
//     */
//    suspend fun requestInvoice(skuType: Int, expend: Boolean): List<JSONObject> {
//        return if (checkConnectionState()) {
//            proxy.requestInvoice(skuType, expend)
//        } else mutableListOf()
//    }
//
//    /**
//     * 消耗订单
//     * @param  invoice 购买的凭证 @see [requestInvoice]
//     */
//    suspend fun expendInvoice(invoice: JSONObject): Int {
//        return if (checkConnectionState()) {
//            (proxy as GoogleBillingClient).expendInvoice(
//                Purchase(invoice.toString(), invoice.toString())
//            )
//        } else ICartException.CODE_DISCONNECTED
//    }
//
//    /**
//     *  发起支付流程
//     *  @param activity 界面上下文
//     *  @param sku 选中的sku信息 (skuId和type) @see [SkuInfo]
//     *  @param tag 支付过程添加的tag,一般用于使用优惠卷之类的  在google pay中,需要进入管理平台设置优惠选项,然后设置唯一的标签,通过标签确认使用优惠券
//     */
//    suspend fun requestPayment(
//        activity: Activity,
//        sku: SkuInfo,
//        tag: String? = null
//    ): PaymentResponse {
//        val resultCode = if (checkConnectionState()) {
//            if (updateState(STATE_IDLE, STATE_IN_PAYMENT)) {
//                executePayment(activity, sku, tag)
//            } else {
//                //服务繁忙
//                ICartException.CODE_SERVICE_BUSY
//            }
//        } else ICartException.CODE_DISCONNECTED
//        "requestPayment resultCode:$resultCode".log()
//        //代表发起支付流程完成,但是没有实现付款操作
//        return suspendCancellableCoroutine { paymentContinuation = it }
//    }
//
//    /**
//     * 发起执行购买动作
//     */
//    private suspend fun executePayment(activity: Activity, sku: SkuInfo, tag: String? = null): Int {
//        val resultCode = when (proxy) {
//            is GoogleBillingClient -> {
//                (proxy as GoogleBillingClient).executePayment(activity, sku, tag)
//            }
//            else -> {
//                ICartException.CODE_UNAVAILABLE
//            }
//        }
//        return resultCode
//    }
//
//    private suspend fun GoogleBillingClient.executePayment(
//        activity: Activity, sku: SkuInfo, tag: String? = null
//    ): Int {
//        var skuDetails: SkuDetails<ProductDetails>? = null
//        //请求购买清单
//        val skuPrepaidList = loadSkuPrepaid(mutableListOf(sku))
//        "loadSkuPrepaid:${skuPrepaidList}".log()
//        if (skuPrepaidList.isNotEmpty()) {
//            skuDetails = skuPrepaidList[0]
//        }
//        //请求预付单失败
//        if (skuDetails == null) {
//            return ICartException.CODE_INVALID_PARAM
//        }
//        val token = getMatchOfferDetailsByGoogle(skuDetails.details, tag)
//        //发起请求支付流程
//        return requestPayment(activity, skuDetails, token)
//    }
//
//
//    /**
//     *  获取匹配SKU信息上面的Offer(优惠服务)
//     */
//    private fun getMatchOfferDetailsByGoogle(skuDetails: ProductDetails, tag: String?): String {
//        val offerDetails = skuDetails.subscriptionOfferDetails
//        var token = ""
//        if (offerDetails?.isNotEmpty() == true) {
//            token = offerDetails[0].offerToken
//            offerDetails.forEach {
//                if (isMatchTag(it.offerTags, tag)) {
//                    return it.offerToken
//                }
//            }
//        }
//        return token
//
//    }
//
//    /**
//     * 是否匹配上tag
//     */
//    private fun isMatchTag(offerTags: List<String>, tag: String?): Boolean {
//        return offerTags.last() == tag
//    }
//
//}