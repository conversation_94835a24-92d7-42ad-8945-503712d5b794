plugins {
    id 'com.android.library'
    id 'kotlin-android'
}

apply from: "$rootProject.projectDir/common.gradle"
apply from: "$rootProject.projectDir/build_type.gradle"

android {
    namespace "com.clevguard.utils"

    buildFeatures {
        buildConfig true
    }
}

apply from: "$rootProject.projectDir/channel.gradle"

dependencies {
    api fileTree(include: ['*.aar', '*.jar'], dir: 'libs')
    implementation libs.androidx.core.ktx
    implementation libs.app.compat
    implementation libs.fastjson
    api libs.gson
    api libs.mmkv
    api libs.timber

    //应用内评分
    implementation libs.play.review
    implementation libs.play.review.ktx

    testImplementation libs.junit
    androidTestImplementation libs.androidx.junit
    androidTestImplementation libs.androidx.espresso.core
}