package com.clevguard.utils.ext

import com.clevguard.utils.BaseApplication
import com.clevguard.utils.utils.TimeUtils.YMD_HMS3
import com.clevguard.utils.utils.newTimeFMTStr
import timber.log.Timber

val isDebug by lazy {
    BaseApplication.showLog
}

@JvmOverloads
fun logv(
    msg: String,
    tag: String = "xaluoqone",
    showLogFrom: Boolean = false,
    showCurrentThreadName: Boolean = false
) {
    if (!isDebug) return
    log(tag, getPackagedMsg(msg, showLogFrom, showCurrentThreadName), "v")
}

@JvmOverloads
fun logd(
    msg: String,
    tag: String = "xaluoqone",
    showLogFrom: Boolean = true,
    showCurrentThreadName: Boolean = false
) {
    if (!isDebug) return
    log(tag, getPackagedMsg(msg, showLogFrom, showCurrentThreadName), "d")
}

@JvmOverloads
fun logi(
    msg: String,
    tag: String = "xaluoqone",
    showLogFrom: Boolean = false,
    showCurrentThreadName: Boolean = false
) {
    if (!isDebug) return
    log(tag, getPackagedMsg(msg, showLogFrom, showCurrentThreadName), "i")
}

@JvmOverloads
fun logw(
    msg: String,
    tag: String = "xaluoqone",
    showLogFrom: Boolean = true,
    showCurrentThreadName: Boolean = false
) {
    if (!isDebug) return
    log(tag, getPackagedMsg(msg, showLogFrom, showCurrentThreadName), "w")
}

@JvmOverloads
fun loge(
    msg: String,
    tag: String = "xaluoqone",
    showLogFrom: Boolean = true,
    showCurrentThreadName: Boolean = false
) {
    if (!isDebug) return
    log(tag, getPackagedMsg(msg, showLogFrom, showCurrentThreadName), "e")
}

private fun getPackagedMsg(
    msg: String,
    showLogFrom: Boolean,
    showCurrentThreadName: Boolean
) = when {
    showLogFrom -> getStackTraceMsg(msg)
    showCurrentThreadName -> getStackTraceMsg("${Thread.currentThread().name}(id：${Thread.currentThread().id})：$msg")
    else -> msg
}

fun log(tag: String, msg: String, method: String) {
    if (!isDebug) return
    when (method) {
        "v" -> Timber.tag(tag).v(msg)
        "d" -> Timber.tag(tag).d(msg)
        "i" -> Timber.tag(tag).i(msg)
        "w" -> Timber.tag(tag).w(msg)
        "e" -> Timber.tag(tag).e(msg)
    }
    BaseApplication.logToFile("${newTimeFMTStr(format = YMD_HMS3)} $tag ${method}：$msg\n")
}

fun getStackTraceMsg(msg: String): String {
    val elements = Thread.currentThread().stackTrace
    val logFileLastIndex = elements.indexOfLast { it.className.contains("LogKtx") }
    val index = when {
        logFileLastIndex == -1 && elements.size > 6 -> 6
        elements.size > logFileLastIndex + 1 -> logFileLastIndex + 1
        else -> -1
    }
    if (index == -1) {
        return msg
    }
    val element = elements[index]
    return "$element：\n$msg"
}