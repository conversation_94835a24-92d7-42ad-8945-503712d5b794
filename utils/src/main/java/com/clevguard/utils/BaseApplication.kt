package com.clevguard.utils

import android.app.ActivityManager
import android.app.Application
import android.content.Context
import android.os.Process
import com.clevguard.utils.ext.loge
import com.clevguard.utils.utils.TimeUtils.YMD_H
import com.clevguard.utils.utils.newTimeFMTStr
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.launch
import java.io.File

abstract class BaseApplication : Application() {
    companion object {
        private lateinit var mInstance: BaseApplication

        fun getInstance(): BaseApplication {
            return mInstance
        }

        val scope = CoroutineScope(
            SupervisorJob() + Dispatchers.Main + CoroutineExceptionHandler { _, exception ->
                loge("协程发生崩溃：${exception.stackTraceToString()}", "clash_logging")
            }
        )

        val showLog = BuildConfig.BUILD_TYPE != "release"

        private val logFlow = MutableSharedFlow<String>()

        fun collectLog() {
            scope.launch {
                logFlow.collect { log ->
                    val logFile =
                        File(
                            getInstance().getExternalFilesDir("logs"),
                            "/${newTimeFMTStr(format = YMD_H)}.txt"
                        )
                    if (!logFile.exists()) {
                        logFile.parentFile?.mkdirs()
                        logFile.createNewFile()
                    }
                    logFile.appendText(log)
                }
            }
        }

        // 记录日志到本地文件("files/logs"目录下，按照小时创建文件)
        fun logToFile(msg: String) {
            scope.launch {
                logFlow.emit(msg)
            }
        }
    }

    override fun onCreate() {
        super.onCreate()

        if (showLog) {
            collectLog()

            val defaultUncaughtExceptionHandler = Thread.getDefaultUncaughtExceptionHandler()

            Thread.setDefaultUncaughtExceptionHandler { t, e ->
                loge("线程 ${t.name}(${t.id}) 发生崩溃：${e.stackTraceToString()}", "clash_logging")
                defaultUncaughtExceptionHandler?.uncaughtException(t, e)
            }
        }

        mInstance = this
    }

    fun isMainProcess(context: Context): Boolean {
        return context.packageName == getProcessName(context)
    }

    //取得进程名
    private fun getProcessName(context: Context): String {
        var currentProcessName = ""
        val pid = Process.myPid()
        val manager = context.getSystemService(ACTIVITY_SERVICE) as ActivityManager
        for (processInfo in manager.runningAppProcesses) {
            if (processInfo.pid == pid) {
                currentProcessName = processInfo.processName
                break
            }
        }
        return currentProcessName
    }

    abstract fun dealLogout()

    abstract fun getToken(): String

    abstract fun deviceId(): String

    abstract fun language(): String
}