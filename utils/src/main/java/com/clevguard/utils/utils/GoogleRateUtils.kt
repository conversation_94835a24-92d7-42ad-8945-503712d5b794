package com.clevguard.utils.utils

import android.app.Activity
import com.clevguard.utils.ext.loge
import com.clevguard.utils.ext.logv
import com.google.android.play.core.review.ReviewManager
import com.google.android.play.core.review.ReviewManagerFactory

/**
 *creater:l<PERSON><PERSON><PERSON> on 2023/3/28 15:44
 */
object GoogleRateUtils {
    /**
     * google应用内评分
     */
    fun openGoogleRating(activity: Activity) {
        val reviewManager: ReviewManager = ReviewManagerFactory.create(activity)
        reviewManager.requestReviewFlow().addOnCompleteListener { task ->
            if (task.isSuccessful) {
                //reviewInfo仅在有限时间内有效，确认要发起评价流程前请求。
                val reviewInfo = task.result
                reviewManager.launchReviewFlow(activity, reviewInfo!!)
                    .addOnCompleteListener { reviewTask ->
                        //不论是否成功都应该继续用户原本的行为，当配额达到限制后，对话框不会弹出。
                        if (task.isSuccessful) {
                            logv("评分成功")
                        } else {
                            loge("评分失败${reviewTask.exception?.stackTraceToString()}")
                        }
                    }
            } else {
                loge("评分失败${task.exception?.stackTraceToString()}")
            }
        }
    }
}